# Snapshot report for `test/integration/globenewswire/index.js`

The actual snapshot is saved in `index.js.snap`.

Generated by [AVA](https://avajs.dev).

## globenewswire

> Snapshot 1

    {
      audio: null,
      author: 'Segment',
      date: '2016-04-06T12:00:00.000Z',
      description: 'SAN FRANCISCO, CA--(Marketwired - April 06, 2016) - Segment, the customer data platform, today released Sources, giving companies access to new types…',
      image: 'https://www.globenewswire.com/news-release/logo/901814/0/901814.png?lastModified=09%2F16%2F2020%2010%3A00%3A19&v=1300478',
      lang: 'en',
      logo: 'https://www.globenewswire.com/Content/logo/favicon.ico',
      publisher: 'Segment',
      title: 'Segment Launches Sources to Unify Siloed Customer Data in Minutes',
      url: 'https://www.globenewswire.com/news-release/2016/04/06/1300478/0/en/Segment-Launches-Sources-to-Unify-Siloed-Customer-Data-in-Minutes.html',
      video: null,
    }
