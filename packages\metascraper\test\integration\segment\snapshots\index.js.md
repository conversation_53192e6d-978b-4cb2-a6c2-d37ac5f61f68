# Snapshot report for `test/integration/segment/index.js`

The actual snapshot is saved in `index.js.snap`.

Generated by [AVA](https://avajs.dev).

## segment

> Snapshot 1

    {
      audio: null,
      author: '<PERSON>',
      date: '2016-05-16T00:00:00.000Z',
      description: 'Segment is the analytics API you’ve always wanted. It’s the easiest way to install all of your favorite analytics tools at once!',
      image: 'https://c19f7be2e84987e7904e-bf41efcb49679c193a4ec0f3210da86f.ssl.cf1.rackcdn.com/photos/40528-1-1.jpg',
      lang: null,
      publisher: 'Segment Blog',
      title: 'Scaling NSQ to 750 Billion Messages',
      url: 'https://segment.com/blog/scaling-nsq',
      video: null,
    }
