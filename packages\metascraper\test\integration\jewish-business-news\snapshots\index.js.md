# Snapshot report for `test/integration/jewish-business-news/index.js`

The actual snapshot is saved in `index.js.snap`.

Generated by [AVA](https://avajs.dev).

## jewish-business-news

> Snapshot 1

    {
      audio: null,
      author: 'Jewish Business News Correspondent',
      date: '2016-01-20T21:50:42.000Z',
      description: 'The Netanya base company is transforming how developers and DevOps team manage binary artifacts: JFrog’s total capital raised to date is $62 million.',
      image: 'https://jewishbusinessnews.com/wp-content/uploads/2016/01/Shl<PERSON>-<PERSON>-<PERSON><PERSON>-<PERSON><PERSON><PERSON>-Co-Founder-and-CEO-Source-<PERSON><PERSON><PERSON>-e1453326404147.jpg',
      lang: 'en',
      logo: 'https://i0.wp.com/jewishbusinessnews.com/wp-content/uploads/2021/08/cropped-favicon.jpg?fit=192%2C192&ssl=1',
      publisher: 'Jewish Business News',
      title: 'Israeli startup <PERSON>Frog raises $50 million in C round',
      url: 'https://jewishbusinessnews.com/2016/01/20/israeli-startup-jfrog-raises-50-million-in-c-round/',
      video: null,
    }
