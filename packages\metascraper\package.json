{"name": "metascraper", "description": "A library to easily scrape metadata from an article on the web using Open Graph, JSON+LD, regular HTML metadata, and series of fallbacks.", "homepage": "https://metascraper.js.org", "version": "5.49.1", "types": "src/index.d.ts", "main": "src/index.js", "repository": {"type": "git", "url": "git+https://github.com/microlinkhq/metascraper.git"}, "bugs": {"url": "https://github.com/microlinkhq/metascraper/issues"}, "keywords": ["RDF", "article", "browser", "cheerio", "content", "expand", "extract", "facebook", "fallback", "fetch", "get", "graph", "html", "json-ld", "j<PERSON>ld", "linked data", "meta", "metadata", "micro format", "microformat", "og", "open", "open graph", "opengraph", "page", "parse", "parser", "scrape", "scraper", "semantic", "semantic web", "server", "site", "summarize", "summary", "tag", "tags", "twitter", "unfluff", "unfurl", "url", "web", "website"], "dependencies": {"@metascraper/helpers": "workspace:*", "cheerio": "~1.1.0", "debug-logfmt": "~1.2.3", "whoops": "~5.0.1"}, "devDependencies": {"ava": "5", "tsd": "latest"}, "engines": {"node": ">= 16"}, "files": ["src"], "scripts": {"lint": "tsd", "pretest": "npm run lint", "test": "NODE_PATH=.. TZ=UTC ava"}, "license": "MIT", "ava": {"timeout": "30s", "failFast": true}, "tsd": {"directory": "test/types"}}