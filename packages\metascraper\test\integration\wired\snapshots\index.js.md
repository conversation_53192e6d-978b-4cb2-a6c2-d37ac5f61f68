# Snapshot report for `test/integration/wired/index.js`

The actual snapshot is saved in `index.js.snap`.

Generated by [AVA](https://avajs.dev).

## wired

> Snapshot 1

    {
      audio: null,
      author: 'WIRED Staff',
      date: '2017-07-13T00:35:06.995Z',
      description: 'Even if climate change didn’t send Larsen C packing, the air and oceans on Earth are incontrovertibly warmer than they used to be. Could an event like this move a policy needle?',
      image: 'https://media.wired.com/photos/5966648eb07d437d51477a87/191:100/pass/LarsenC-FeatureArt.jpg',
      lang: 'en',
      logo: 'https://www.wired.com/images/logos/article-icon.jpg',
      publisher: 'WIRED',
      title: 'Giant Antarctic Icebergs and Crushing Existential Dread',
      url: 'https://www.wired.com/story/giant-antarctic-icebergs-and-crushing-existential-dread/',
      video: null,
    }
