# Snapshot report for `test/integration/the-guardian/index.js`

The actual snapshot is saved in `index.js.snap`.

Generated by [AVA](https://avajs.dev).

## the-guardian

> Snapshot 1

    {
      audio: 'https://www.theguardian.com/preference/edition/au',
      author: '<PERSON>',
      date: '2017-07-04T08:37:34.000Z',
      description: 'Judge dismisses lawsuit accusing Facebook of tracking users’ activity, saying responsibility was on plaintiffs to keep browsing history private',
      image: 'https://i.guim.co.uk/img/media/7a94153cfbe65bca1a3e7f6993b233d3c0983c36/0_533_3003_1801/master/3003.jpg?w=1200&h=630&q=55&auto=format&usm=12&fit=crop&crop=faces%2Centropy&bm=normal&ba=bottom%2Cleft&blend64=aHR0cHM6Ly91cGxvYWRzLmd1aW0uY28udWsvMjAxNi8wNS8yNS9vdmVybGF5LWxvZ28tMTIwMC05MF9vcHQucG5n&s=eaa757e2ef45d638beabf99b31e756ac',
      lang: null,
      logo: 'https://assets.guim.co.uk/images/favicons/451963ac2e23633472bf48e2856d3f04/152x152.png',
      publisher: 'The Guardian',
      title: 'Facebook can track your browsing even after you’ve logged out, judge says',
      url: 'http://www.theguardian.com/technology/2017/jul/03/facebook-track-browsing-history-california-lawsuit',
      video: null,
    }
