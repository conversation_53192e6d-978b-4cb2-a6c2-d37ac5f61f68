# Snapshot report for `test/integration/astier/index.js`

The actual snapshot is saved in `index.js.snap`.

Generated by [AVA](https://avajs.dev).

## astier

> Snapshot 1

    {
      audio: null,
      author: '<PERSON><PERSON>',
      date: '2017-07-05T12:00:00.000Z',
      description: 'In which I babble about some projects I do and I rant about stuff I like. I’m working as a Linux Kernel engineer as a day job, and I probably play too much video games on my free time.',
      image: 'https://anisse.astier.eu/images/anisse.jpg',
      lang: 'en',
      logo: 'https://anisse.astier.eu/theme/img/favicon.png',
      publisher: 'awk driven IoT',
      title: 'Linux Engineer’s random thoughts - awk driven IoT',
      url: 'https://anisse.astier.eu/awk-driven-iot.html',
      video: null,
    }
