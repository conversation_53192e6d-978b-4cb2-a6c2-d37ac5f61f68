<!DOCTYPE html>
<html lang="en-US" class="view-grid dark">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>More than a decade later, how do original YouTube stars feel about the site? - Ars Technica</title>
    <link rel="preconnect" href="https://c.arstechnica.com">
    <!-- The SEO Framework by Sybre Waaijer -->
    <meta name="robots" content="max-snippet:-1,max-image-preview:large,max-video-preview:-1">
    <link rel="canonical" href="https://arstechnica.com/features/2017/06/youtube-changed-my-life-a-pair-of-original-videostars-ponder-a-life-lived-online/">
    <meta name="description" content="For original YouTubers, their online haven became a media behemoth—but they keep vlogging.">
    <meta property="og:type" content="article">
    <meta property="og:locale" content="en_US">
    <meta property="og:site_name" content="Ars Technica">
    <meta property="og:title" content="More than a decade later, how do original YouTube stars feel about the site?">
    <meta property="og:description" content="For original YouTubers, their online haven became a media behemoth—but they keep vlogging.">
    <meta property="og:url" content="https://arstechnica.com/features/2017/06/youtube-changed-my-life-a-pair-of-original-videostars-ponder-a-life-lived-online/">
    <meta property="og:image" content="https://cdn.arstechnica.net/wp-content/uploads/2017/06/Screen-Shot-2017-06-08-at-1.32.44-PM.png">
    <meta property="og:image:width" content="1440">
    <meta property="og:image:height" content="900">
    <meta property="article:published_time" content="2017-06-11T14:00:33+00:00">
    <meta property="article:modified_time" content="2017-06-11T14:11:02+00:00">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="More than a decade later, how do original YouTube stars feel about the site?">
    <meta name="twitter:description" content="For original YouTubers, their online haven became a media behemoth—but they keep vlogging.">
    <meta name="twitter:image" content="https://cdn.arstechnica.net/wp-content/uploads/2017/06/Screen-Shot-2017-06-08-at-1.32.44-PM.png">
    <script type="text/javascript" async="" src="https://analytics.tiktok.com/i18n/pixel/static/identify_7bf75739.js"></script><script type="text/javascript" async="" src="https://analytics.tiktok.com/i18n/pixel/static/main.MWZkMThhNTg2MQ.js" data-id="C1IQID9FKFK1PHD4UBH0"></script><script src="https://rules.quantcount.com/rules-p-Jjy-Cyr1NZGRz.js" async=""></script><script async="" src="https://wave.outbrain.com/mtWavesBundler/handler/00c1076881eb5352ee07e7589585aa30bb" type="text/javascript"></script><script charset="UTF-8" type="text/javascript" async="async" src="https://cdn.taboola.com/libtrc/google-topics-api.20241001-19-RELEASE.es6.js"></script><script type="text/javascript" async="" src="https://www.googletagmanager.com/gtag/js?id=G-LRHY7JG2PW&amp;l=dataLayer&amp;cx=c"></script><script charset="UTF-8" type="text/javascript" src="https://cdn.taboola.com/libtrc/impl.20241001-19-RELEASE.js"></script><script async="" src="https://sc-static.net/scevent.min.js"></script><script async="" src="https://connect.facebook.net/en_US/fbevents.js"></script><script async="" src="//amplify.outbrain.com/cp/obtp.js" type="text/javascript"></script><script type="text/javascript" async="" src="https://analytics.tiktok.com/i18n/pixel/events.js?sdkid=C1IQID9FKFK1PHD4UBH0&amp;lib=ttq"></script><script async="" src="//cdn.taboola.com/libtrc/unip/1187698/tfa.js" id="tb_tfa_script"></script><script type="text/javascript" async="" src="https://snap.licdn.com/li.lms-analytics/insight.min.js"></script><script async="" src="https://sb.scorecardresearch.com/cs/6035094/beacon.js"></script><script type="text/javascript" async="" src="https://static.ads-twitter.com/uwt.js"></script><script type="text/javascript" async="" src="https://secure.quantserve.com/quant.js"></script><script async="" defer="" src="https://launchpad.privacymanager.io/latest/launchpad.bundle.js"></script><script async="" src="https://globalservices.conde.digital/p77xzrbz9z.js"></script><script type="text/javascript" src="//gum.criteo.com/sync?c=72&amp;r=2&amp;j=TRC.getRTUS&amp;us_privacy=1---&amp;gdpr=0&amp;gdpr_consent=&amp;gdpr_pd="></script><script type="text/javascript" src="//apv-launcher.minute.ly/api/launcher/MIN-901870.js" async="async" id="videoreel_loader_script"></script><script type="text/javascript" src="https://cdn.taboola.com/scripts/cds-pips.js" crossorigin="anonymous" async="async"></script><script async="" src="https://shiverscissors.com/v2fumwIJOo-LsCB0dlG18VSTW43CpWhUEPJuKeRTzrEQdSPPlMr5GymU"></script><script async="" src="//cdn.taboola.com/libtrc/condenast1-network/loader.js" id="tb_loader_script"></script><script async="" src="https://www.googletagmanager.com/gtm.js?id=GTM-NLXNPCQ"></script><script src="https://geolocation.onetrust.com/cookieconsentpub/v1/geo/location/dnsfeed" async="" type="text/javascript"></script><script type="application/ld+json">{"@context":"https://schema.org","@graph":[{"@type":"WebSite","@id":"https://arstechnica.com/#/schema/WebSite","url":"https://arstechnica.com/","name":"Ars Technica","description":"Serving the Technologist for more than a decade. IT news, reviews, and analysis.","inLanguage":"en-US","potentialAction":{"@type":"SearchAction","target":{"@type":"EntryPoint","urlTemplate":"https://arstechnica.com/search/{search_term_string}/"},"query-input":"required name=search_term_string"},"publisher":{"@type":"Organization","@id":"https://arstechnica.com/#/schema/Organization","name":"Ars Technica","url":"https://arstechnica.com/","logo":{"@type":"ImageObject","url":"https://cdn.arstechnica.net/wp-content/uploads/2016/10/cropped-ars-logo-512_480.png","contentUrl":"https://cdn.arstechnica.net/wp-content/uploads/2016/10/cropped-ars-logo-512_480.png","width":512,"height":512,"contentSize":"34417"}}},{"@type":"WebPage","@id":"https://arstechnica.com/features/2017/06/youtube-changed-my-life-a-pair-of-original-videostars-ponder-a-life-lived-online/","url":"https://arstechnica.com/features/2017/06/youtube-changed-my-life-a-pair-of-original-videostars-ponder-a-life-lived-online/","name":"More than a decade later, how do original YouTube stars feel about the site? &#x2d; Ars Technica","description":"For original YouTubers, their online haven became a media behemoth&mdash;but they keep vlogging.","inLanguage":"en-US","isPartOf":{"@id":"https://arstechnica.com/#/schema/WebSite"},"breadcrumb":{"@type":"BreadcrumbList","@id":"https://arstechnica.com/#/schema/BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"item":"https://arstechnica.com/","name":"Ars Technica"},{"@type":"ListItem","position":2,"item":"https://arstechnica.com/features/","name":"Category: Features"},{"@type":"ListItem","position":3,"name":"More than a decade later, how do original YouTube stars feel about the site?"}]},"potentialAction":{"@type":"ReadAction","target":"https://arstechnica.com/features/2017/06/youtube-changed-my-life-a-pair-of-original-videostars-ponder-a-life-lived-online/"},"datePublished":"2017-06-11T14:00:33+00:00","dateModified":"2017-06-11T14:11:02+00:00","author":{"@type":"Person","@id":"https://arstechnica.com/#/schema/Person/814d720a1c2173278de90472e643174f","name":"Ars Staff"}}]}</script>
    <script type="application/ld+json">{"@context":"https://schema.org","@type":"NewsArticle","mainEntityOfPage":{"@type":"WebPage","@id":"https://arstechnica.com/features/2017/06/youtube-changed-my-life-a-pair-of-original-videostars-ponder-a-life-lived-online/"},"headline":"More than a decade later, how do original YouTube stars feel about the site?","image":{"@type":"ImageObject","url":"https://cdn.arstechnica.net/wp-content/uploads/2017/06/Screen-Shot-2017-06-08-at-1.32.44-PM.png","width":1440,"height":900},"datePublished":"2017-06-11T14:00:33+00:00","dateModified":"2017-06-11T14:11:02+00:00","author":{"@type":"Person","name":"Ars Staff","url":"https://arstechnica.com/author/ars-staff/"},"publisher":{"@type":"Organization","name":"Ars Technica","logo":{"@type":"ImageObject","url":"https://cdn.arstechnica.net/wp-content/uploads/2024/10/ars-logo-186x60.png","width":186,"height":60}},"description":"For original YouTubers, their online haven became a media behemoth\u0026mdash;but they keep vlogging."}</script>
    <!-- / The SEO Framework by Sybre Waaijer | 11.29ms meta | 0.22ms boot -->
    <link rel="preconnect" href="https://cdn.cookielaw.org">
    <link rel="preconnect" href="https://geolocation.onetrust.com">
    <!-- OneTrust Cookies Consent Notice start -->
    <script src="https://cdn.cookielaw.org/scripttemplates/otSDKStub.js" data-domain-script="b10882a1-8446-4e7d-bfb2-ce2c770ad910"></script>
    <script id="oneTrustScripts">
      window.OptanonWrapper = function() {
        var CCPAButton = document.getElementById('ot-sdk-btn');
        CCPAButton && CCPAButton.classList.add('ot-sdk-btn--visible');
        window.dataLayer && window.dataLayer.push({
          event: 'OneTrustGroupsUpdated'
        });
        window.cnBus && window.cnBus.emit('onetrust.OneTrustGroupsUpdated');
      };
    </script>
    <script src="https://cdn.cookielaw.org/opt-out/otCCPAiab.js" ccpa-opt-out-ids="C0002,C0003,C0004,C0005" ccpa-opt-out-geo="ca" ccpa-opt-out-lspa="true"></script>
    <!-- OneTrust Cookies Consent Notice end -->
    <!-- Google Tag Manager DataLayer -->
    <script>
      window.dataLayer = window.dataLayer || [];
      window.dataLayer.push({"event":"data-layer-loaded","user":{"ars_userId":undefined,"amg_userId":undefined,"uID":undefined,"sID":undefined,"loginStatus":false,"subscriberStatus":"none","infinityId":undefined,"registrationSource":undefined,"mdw_cnd_id":undefined,"monthlyVisits":undefined,"accessPaywall":undefined,"view":"grid","theme":"system","show_comments":undefined},"content":{"pageTemplate":"single","pageType":"article|report","contentCategory":"homepage","section":"homepage","subsection":undefined,"contributor":"Ars Staff","contentID":1113493,"contentLength":3399,"display":"More than a decade later, how do original YouTube stars feel about the site?","contentSource":"web","pageAssets":undefined,"uniqueContentCount":undefined,"monthlyContentCount":undefined,"publishDate":"2017-06-11T14:00:33-04:00","modifiedDate":"2017-06-11T14:11:02-04:00","keywords":"","dataSource":undefined},"marketing":{"campaignName":undefined,"circCampaignId":undefined,"internalCampaignId":undefined,"brand":"Ars Technica","certified_mrc_data":undefined,"condeNastId":undefined},"page":{"pID":undefined,"syndicatorUrl":undefined,"pageURL":"https:\/\/arstechnica.com\/?p=1113493","canonical":"https:\/\/arstechnica.com\/features\/2017\/06\/youtube-changed-my-life-a-pair-of-original-videostars-ponder-a-life-lived-online\/","canonicalPathName":"\/features\/2017\/06\/youtube-changed-my-life-a-pair-of-original-videostars-ponder-a-life-lived-online\/"},"search":{"facets":undefined,"searchTerms":undefined},"site":{"appVersion":"1.0.0"}});
    </script>
    <!-- End Google Tag Manager DataLayer -->
    <!-- Google Tag Manager -->
    <script>
      (function(w, d, s, l, i) {
        w[l] = w[l] || [];
        w[l].push({
          'gtm.start': new Date().getTime(),
          event: 'gtm.js'
        });
        var f = d.getElementsByTagName(s)[0],
          j = d.createElement(s),
          dl = l != 'dataLayer' ? '&l=' + l : '';
        j.async = true;
        j.src =
          'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
        f.parentNode.insertBefore(j, f);
      })(window, document, 'script', 'dataLayer', 'GTM-NLXNPCQ');
    </script>
    <!-- End Google Tag Manager -->
    <style id="elasticpress-related-posts-style-inline-css">
      .editor-styles-wrapper .wp-block-elasticpress-related-posts ul,.wp-block-elasticpress-related-posts ul{list-style-type:none;padding:0}.editor-styles-wrapper .wp-block-elasticpress-related-posts ul li a>div{display:inline}
    </style>
    <link rel="stylesheet" id="elasticpress-facets-css" href="https://cdn.arstechnica.net/wp-content/plugins/_composer_elasticpress/dist/css/facets-styles.css?ver=7d568203f3965dc85d8a" media="all">
    <link rel="stylesheet" id="searchterm-highlighting-css" href="https://cdn.arstechnica.net/wp-content/plugins/_composer_elasticpress/dist/css/highlighting-styles.css?ver=252562c4ed9241547293" media="all">
    <link rel="stylesheet" id="app/0-css" href="https://cdn.arstechnica.net/wp-content/themes/ars-v9/public/css/app.422b47.css" media="all">
    <link rel="stylesheet" id="ads/0-css" href="https://cdn.arstechnica.net/wp-content/themes/ars-v9/public/css/ads.23dea4.css" media="all">
    <script src="https://cdn.arstechnica.net/wp-content/themes/ars-v9/resources/scripts/jquery-3.7.1.min.js?ver=3.7.1" id="jquery-js"></script>
    <meta property="article:published_time" content="2017-06-11T14:00:33+00:00">
    <meta property="article:modified_time" content="2017-06-11T14:11:02+00:00">
    <script>window.ars = {"subscriber":false,"hasAdFree":false,"hasTrackerFree":false}</script>
    <script>
      const theme = "system";
      let darkMode = false;
      if (theme === "dark" || (theme === "system" && (window.matchMedia("(prefers-color-scheme: dark)").matches))) {
          darkMode = true;
          document.documentElement.classList.add("dark");
      }
      window.darkMode = darkMode;
    </script>
    <meta name="twitter:site" content="@arstechnica">
    <meta name="twitter:domain" content="arstechnica.com">
    <style>[x-cloak] { display: none !important; }</style>
    <link rel="preconnect" href="https://globalservices.conde.digital">
    <link rel="preconnect" href="https://player.cnevids.com">
    <script>
      window.permutiveCohorts = {"cached_until":{"date":"2024-10-05 10:07:10.236809","timezone_type":3,"timezone":"UTC"},"cohorts":["bjfa"],"gam":["bjfa"],"xandr":[]};
      window.permutiveContextInfo = {"pageProperties":{"client":{"url":"https:\/\/arstechnica.com\/features\/2017\/06\/youtube-changed-my-life-a-pair-of-original-videostars-ponder-a-life-lived-online\/","referrer":"","type":"web","user_agent":"Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","domain":"arstechnica.com","title":"More than a decade later, how do original YouTube stars feel about the site? &#x2d; Ars Technica"},"type":"article","article":{"id":"1113493","category":"misc","subcategory":"","title":"More than a decade later, how do original YouTube stars feel about the site?","tags":[]}},"url":"https:\/\/arstechnica.com\/features\/2017\/06\/youtube-changed-my-life-a-pair-of-original-videostars-ponder-a-life-lived-online\/"};
    </script>
    <script src="https://www.googletagservices.com/tag/js/gpt.js" id="gpt-script" async=""></script>
    <script>
      window.googletag = window.googletag || {};
      window.googletag.cmd = window.googletag.cmd || [];
      window.cns = window.cns || {};
      window.cns.queue = [];
      window.cns.async = function(s, c) {
        cns.queue.push({
          service: s,
          callback: c
        })
      };
      window.cns.pageContext = {"contentType":"article","templateType":"mt_article_two_column","channel":"misc","subChannel":"","slug":"youtube-changed-my-life-a-pair-of-original-videostars-ponder-a-life-lived-online","server":"production","keywords":{"tags":[],"cm":[],"platform":["wordpress"],"copilotid":""}};
    </script>
    <script src="https://ads-static.conde.digital/production/cns/builds/ars-technica/ars-technica.min.js" async=""></script>
    <script type="text/javascript">
      window._taboola = window._taboola || [];
      _taboola.push({
        article: 'auto'
      });
      ! function(e, f, u, i) {
        if (!document.getElementById(i)) {
          e.async = 1;
          e.src = u;
          e.id = i;
          f.parentNode.insertBefore(e, f);
        }
      }(document.createElement('script'),
        document.getElementsByTagName('script')[0],
        '//cdn.taboola.com/libtrc/condenast1-network/loader.js',
        'tb_loader_script');
      if (window.performance && typeof window.performance.mark == 'function') {
        window.performance.mark('tbl_ic');
      }
    </script>
    <script type="text/javascript">!(function(o,_name){function n(){(n.q=n.q||[]).push(arguments)}n.v=1,o[_name]=o[_name]||n;!(function(o,t,n,c){function e(n){(function(){try{return(localStorage.getItem("v4ac1eiZr0")||"").split(",")[4]>0}catch(o){}return!1})()&&(n=o[t].pubads())&&n.setTargeting("admiral-engaged","true")}(c=o[t]=o[t]||{}).cmd=c.cmd||[],typeof c.pubads===n?e():typeof c.cmd.unshift===n?c.cmd.unshift(e):c.cmd.push(e)})(window,"googletag","function");})(window,String.fromCharCode(97,100,109,105,114,97,108));!(function(t,c,i){i=t.createElement(c),t=t.getElementsByTagName(c)[0],i.async=1,i.src="https://shiverscissors.com/v2fumwIJOo-LsCB0dlG18VSTW43CpWhUEPJuKeRTzrEQdSPPlMr5GymU",t.parentNode.insertBefore(i,t)})(document,"script");</script>
    <meta name="twitter:partner" content="tfwp">
    <meta name="parsely-page" content="{&quot;title&quot;:&quot;More than a decade later, how do original YouTube stars feel about the site?&quot;,&quot;link&quot;:&quot;https:\/\/arstechnica.com\/features\/2017\/06\/youtube-changed-my-life-a-pair-of-original-videostars-ponder-a-life-lived-online\/&quot;,&quot;type&quot;:&quot;post&quot;,&quot;author&quot;:&quot;Ars Staff&quot;,&quot;post_id&quot;:1113493,&quot;pub_date&quot;:&quot;2017-06-11T10:00:33-04:00&quot;,&quot;section&quot;:&quot;Features&quot;,&quot;tags&quot;:[],&quot;image_url&quot;:&quot;https:\/\/cdn.arstechnica.net\/wp-content\/uploads\/2017\/06\/Screen-Shot-2017-06-08-at-1.32.44-PM-500x500.png&quot;}">
    <meta name="parsely-metadata" content="{&quot;type&quot;:&quot;post&quot;,&quot;title&quot;:&quot;More than a decade later, how do original YouTube stars feel about the site?&quot;,&quot;post_id&quot;:1113493,&quot;lower_deck&quot;:&quot;For original YouTubers, their online haven became a media behemoth\u2014but they keep vlogging.&quot;,&quot;image_url&quot;:&quot;https:\/\/cdn.arstechnica.net\/wp-content\/uploads\/2017\/06\/Screen-Shot-2017-06-08-at-1.32.44-PM-500x500.png&quot;,&quot;listing_image_url&quot;:&quot;https:\/\/cdn.arstechnica.net\/wp-content\/uploads\/2017\/06\/Screen-Shot-2017-06-08-at-1.32.44-PM-768x432.png&quot;}">
    <!-- Start Headline A/B -->
    <script type="text/javascript">
      class ABTest {
        constructor(post_id, init_method) {
          this.post_id = post_id;
          this.ajaxurl = '/services/ars-ajax-handler.php';
          this.expireDays = 1 / 48; // 30 min
          this.group = this.getGroup();
          this.uid = this.getUid();
          this.init_method = init_method;
          this.setTitle();
      
          if (this.init_method === 'click') {
            this.click();
          } else {
            this.impression();
          }
        }
      
        setCookie(name, value, days) {
          var expires = "";
          if (days) {
            var date = new Date();
            date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
            expires = "; expires=" + date.toUTCString();
          }
          document.cookie = name + "=" + (value || "") + expires + "; path=/";
        }
      
        getCookie(name) {
          var nameEQ = name + "=";
          var ca = document.cookie.split(';');
          for (var i = 0; i < ca.length; i++) {
            var c = ca[i];
            while (c.charAt(0) == ' ') c = c.substring(1, c.length);
            if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length, c.length);
          }
          return null;
        }
      
        // Retrieves a unique id for determining whether the event should be recorded
        getUid() {
          var uid = this.getCookie('ars_ab_' + this.post_id + '_uid');
          if (!uid) {
            uid = (Math.random() + 1).toString(36).substring(2, 7);
            this.setCookie('ars_ab_' + this.post_id + '_uid', uid, this.expireDays);
          }
          return uid;
        };
      
        // Places the user in either A or B for this post id
        getGroup() {
          var group = this.getCookie('ars_ab_' + this.post_id + '_group');
          if (!group) {
            group = String.fromCharCode(Math.floor(Math.random() * 2) + 65).toLowerCase();
            this.setCookie('ars_ab_' + this.post_id + '_group', group, this.expireDays);
          }
          return group;
        };
      
        // Records a headline impression (from homepage or other listing)
        impression() {
          // Send fake ajax
          var params = {
            nonce: '1c903c1d86',
            action: 'ars_ab_impression',
            id: this.post_id,
            group: this.group,
            uid: this.uid,
            ts: (new Date()).getTime()
          };
          var url = this.ajaxurl + '?' + this.encodeParams(params);
          document.write('\x3Cscript type="text/javascript" src="' + url + '">\x3C/script>');
        };
      
        // Records a headline click from the actual post page
        click() {
          // Send fake ajax
          var params = {
            nonce: '4f7dcc15e5',
            action: 'ars_ab_click',
            id: this.post_id,
            group: this.group,
            uid: this.uid,
            ts: (new Date()).getTime()
          };
          var url = this.ajaxurl + '?' + this.encodeParams(params);
          document.write('\x3Cscript type="text/javascript" src="' + url + '">\x3C/script>');
        };
      
        // If user is in B group, dynamically set title
        setTitle() {
          if (this.group == 'b') {
            var span = document.getElementById('ars_ab_' + this.post_id);
            var title = span.parentNode;
            title.innerHTML = span.getAttribute('data-title-b');
          }
        };
      
        encodeParams(data) {
          var ret = [];
          for (var d in data)
            ret.push(encodeURIComponent(d) + "=" + encodeURIComponent(data[d]));
          return ret.join("&");
        };
      
      };
    </script>
    <!-- End Headline A/B -->
    <link rel="icon" href="https://cdn.arstechnica.net/wp-content/uploads/2016/10/cropped-ars-logo-512_480-60x60.png" sizes="32x32">
    <link rel="icon" href="https://cdn.arstechnica.net/wp-content/uploads/2016/10/cropped-ars-logo-512_480-300x300.png" sizes="192x192">
    <link rel="apple-touch-icon" href="https://cdn.arstechnica.net/wp-content/uploads/2016/10/cropped-ars-logo-512_480-300x300.png">
    <meta name="msapplication-TileImage" content="https://cdn.arstechnica.net/wp-content/uploads/2016/10/cropped-ars-logo-512_480-300x300.png">
    <!--
      generated 17 seconds ago
      generated in 0.349 seconds
      served from batcache in 0.006 seconds
      expires in 283 seconds
      view: grid 
      xf_style_id: 3 
      -->
    <script src="//static.adsafeprotected.com/iasPET.1.js" async=""></script><script src="https://cdn.permutive.app/1dfc40bb-d155-4f15-970e-99450dbfa0e2-web.js" async=""></script><script src="https://c.amazon-adsystem.com/aax2/apstag.js" async=""></script><script src="https://ads-static.conde.digital/production/cns/builds/ars-technica/prebid.min.js" async=""></script><script src="https://cdn.cookielaw.org/scripttemplates/202409.1.0/otBannerSdk.js" async="" type="text/javascript"></script>
    <style class="cns-ads-iframe-styles" type="text/css">.cns-ads-stage {margin: 0 auto;padding: 0;position: relative;width: 100%;z-index: 1;}[data-slot-type="_out_of_page"] {position: absolute;z-index: -1;}.cns-ads-flex-sizer {display: none;}.cns-ads-stage.cns-ads-flex {display: block;position: relative;}.cns-ads-flex .cns-ads-flex-sizer {display: block;width: 100%;}.cns-ads-flex .cns-ads-container,.cns-ads-flex iframe[id^="google_ads_iframe"],.cns-ads-flex div[id*="google_ads_iframe"] {position: absolute;left: 0;top: 0;right: 0;bottom: 0;height: 100% !important;width: 100% !important;}.full-screen .cns-ads-container,.full-screen iframe[id^="google_ads_iframe"],.full-screen div[id*="google_ads_iframe"] {height: 100vh !important;width: 100vw !important;}iframe[id^="google_ads_iframe"],div[id*="google_ads_iframe"] {margin: 0 auto;padding: 0;}</style>
    <meta http-equiv="origin-trial" content="AlK2UR5SkAlj8jjdEc9p3F3xuFYlF6LYjAML3EOqw1g26eCwWPjdmecULvBH5MVPoqKYrOfPhYVL71xAXI1IBQoAAAB8eyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVlfQ==">
    <meta http-equiv="origin-trial" content="Amm8/NmvvQfhwCib6I7ZsmUxiSCfOxWxHayJwyU1r3gRIItzr7bNQid6O8ZYaE1GSQTa69WwhPC9flq/oYkRBwsAAACCeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVlfQ==">
    <meta http-equiv="origin-trial" content="A9wSqI5i0iwGdf6L1CERNdmsTPgVu44ewj8QxTBYgsv1LCPUVF7YmWOvTappqB1139jAymxUW/RO8zmMqo4zlAAAAACNeyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiRmxlZGdlQmlkZGluZ0FuZEF1Y3Rpb25TZXJ2ZXIiLCJleHBpcnkiOjE3MzY4MTI4MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9">
    <meta http-equiv="origin-trial" content="A+d7vJfYtay4OUbdtRPZA3y7bKQLsxaMEPmxgfhBGqKXNrdkCQeJlUwqa6EBbSfjwFtJWTrWIioXeMW+y8bWAgQAAACTeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiRmxlZGdlQmlkZGluZ0FuZEF1Y3Rpb25TZXJ2ZXIiLCJleHBpcnkiOjE3MzY4MTI4MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9">
    <script src="https://securepubads.g.doubleclick.net/pagead/managed/js/gpt/m202410020101/pubads_impl.js?cb=31087775" async=""></script><script type="text/javascript" async="" src="https://cdn.cookielaw.org/scripttemplates/202409.1.0/otTCF.js"></script>
    <meta http-equiv="origin-trial" content="A+uXoByCfR5HCRAl94AWMQ8Y7DHd3670DSOPWj55vVOoaN/cUpF/r0yk6KbxjLIyaxJ2H3/YX4ZxkiI3srY5oQwAAABreyJvcmlnaW4iOiJodHRwczovL2Nkbi50YWJvb2xhLmNvbTo0NDMiLCJmZWF0dXJlIjoiU2NoZWR1bGVyWWllbGQiLCJleHBpcnkiOjE3MDk2ODMxOTksImlzVGhpcmRQYXJ0eSI6dHJ1ZX0=">
    <script src="https://ads-static.conde.digital/production/cns/builds/condenast/pixelpropagate.min.js" async=""></script><script src="https://config.aps.amazon-adsystem.com/configs/3035" type="text/javascript" async="async"></script><script async="async" defer="defer" src="https://launchpad-wrapper.privacymanager.io/************************************/launchpad-liveramp.js"></script>
    <style id="onetrust-style">#onetrust-banner-sdk .onetrust-vendors-list-handler{cursor:pointer;color:#1f96db;font-size:inherit;font-weight:bold;text-decoration:none;margin-left:5px}#onetrust-banner-sdk .onetrust-vendors-list-handler:hover{color:#1f96db}#onetrust-banner-sdk:focus{outline:2px solid #000;outline-offset:-2px}#onetrust-banner-sdk a:focus{outline:2px solid #000}#onetrust-banner-sdk #onetrust-accept-btn-handler,#onetrust-banner-sdk #onetrust-reject-all-handler,#onetrust-banner-sdk #onetrust-pc-btn-handler{outline-offset:1px}#onetrust-banner-sdk.ot-bnr-w-logo .ot-bnr-logo{height:64px;width:64px}#onetrust-banner-sdk .ot-tcf2-vendor-count.ot-text-bold{font-weight:bold}#onetrust-banner-sdk .ot-close-icon,#onetrust-pc-sdk .ot-close-icon,#ot-sync-ntfy .ot-close-icon{background-size:contain;background-repeat:no-repeat;background-position:center;height:12px;width:12px}#onetrust-banner-sdk .powered-by-logo,#onetrust-banner-sdk .ot-pc-footer-logo a,#onetrust-pc-sdk .powered-by-logo,#onetrust-pc-sdk .ot-pc-footer-logo a,#ot-sync-ntfy .powered-by-logo,#ot-sync-ntfy .ot-pc-footer-logo a{background-size:contain;background-repeat:no-repeat;background-position:center;height:25px;width:152px;display:block;text-decoration:none;font-size:.75em}#onetrust-banner-sdk .powered-by-logo:hover,#onetrust-banner-sdk .ot-pc-footer-logo a:hover,#onetrust-pc-sdk .powered-by-logo:hover,#onetrust-pc-sdk .ot-pc-footer-logo a:hover,#ot-sync-ntfy .powered-by-logo:hover,#ot-sync-ntfy .ot-pc-footer-logo a:hover{color:#565656}#onetrust-banner-sdk h3 *,#onetrust-banner-sdk h4 *,#onetrust-banner-sdk h6 *,#onetrust-banner-sdk button *,#onetrust-banner-sdk a[data-parent-id] *,#onetrust-pc-sdk h3 *,#onetrust-pc-sdk h4 *,#onetrust-pc-sdk h6 *,#onetrust-pc-sdk button *,#onetrust-pc-sdk a[data-parent-id] *,#ot-sync-ntfy h3 *,#ot-sync-ntfy h4 *,#ot-sync-ntfy h6 *,#ot-sync-ntfy button *,#ot-sync-ntfy a[data-parent-id] *{font-size:inherit;font-weight:inherit;color:inherit}#onetrust-banner-sdk .ot-hide,#onetrust-pc-sdk .ot-hide,#ot-sync-ntfy .ot-hide{display:none !important}#onetrust-banner-sdk button.ot-link-btn:hover,#onetrust-pc-sdk button.ot-link-btn:hover,#ot-sync-ntfy button.ot-link-btn:hover{text-decoration:underline;opacity:1}#onetrust-pc-sdk .ot-sdk-row .ot-sdk-column{padding:0}#onetrust-pc-sdk .ot-sdk-container{padding-right:0}#onetrust-pc-sdk .ot-sdk-row{flex-direction:initial;width:100%}#onetrust-pc-sdk [type=checkbox]:checked,#onetrust-pc-sdk [type=checkbox]:not(:checked){pointer-events:initial}#onetrust-pc-sdk [type=checkbox]:disabled+label::before,#onetrust-pc-sdk [type=checkbox]:disabled+label:after,#onetrust-pc-sdk [type=checkbox]:disabled+label{pointer-events:none;opacity:.7}#onetrust-pc-sdk #vendor-list-content{transform:translate3d(0, 0, 0)}#onetrust-pc-sdk li input[type=checkbox]{z-index:1}#onetrust-pc-sdk li .ot-checkbox label{z-index:2}#onetrust-pc-sdk li .ot-checkbox input[type=checkbox]{height:auto;width:auto}#onetrust-pc-sdk li .host-title a,#onetrust-pc-sdk li .ot-host-name a,#onetrust-pc-sdk li .accordion-text,#onetrust-pc-sdk li .ot-acc-txt{z-index:2;position:relative}#onetrust-pc-sdk input{margin:3px .1ex}#onetrust-pc-sdk .pc-logo,#onetrust-pc-sdk .ot-pc-logo{height:60px;width:180px;background-position:center;background-size:contain;background-repeat:no-repeat;display:inline-flex;justify-content:center;align-items:center}#onetrust-pc-sdk .pc-logo img,#onetrust-pc-sdk .ot-pc-logo img{max-height:100%;max-width:100%}#onetrust-pc-sdk .screen-reader-only,#onetrust-pc-sdk .ot-scrn-rdr,.ot-sdk-cookie-policy .screen-reader-only,.ot-sdk-cookie-policy .ot-scrn-rdr{border:0;clip:rect(0 0 0 0);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px}#onetrust-pc-sdk.ot-fade-in,.onetrust-pc-dark-filter.ot-fade-in,#onetrust-banner-sdk.ot-fade-in{animation-name:onetrust-fade-in;animation-duration:400ms;animation-timing-function:ease-in-out}#onetrust-pc-sdk.ot-hide{display:none !important}.onetrust-pc-dark-filter.ot-hide{display:none !important}#ot-sdk-btn.ot-sdk-show-settings,#ot-sdk-btn.optanon-show-settings{color:#68b631;border:1px solid #68b631;height:auto;white-space:normal;word-wrap:break-word;padding:.8em 2em;font-size:.8em;line-height:1.2;cursor:pointer;-moz-transition:.1s ease;-o-transition:.1s ease;-webkit-transition:1s ease;transition:.1s ease}#ot-sdk-btn.ot-sdk-show-settings:hover,#ot-sdk-btn.optanon-show-settings:hover{color:#fff;background-color:#68b631}.onetrust-pc-dark-filter{background:rgba(0,0,0,.5);z-index:2147483646;width:100%;height:100%;overflow:hidden;position:fixed;top:0;bottom:0;left:0}@keyframes onetrust-fade-in{0%{opacity:0}100%{opacity:1}}.ot-cookie-label{text-decoration:underline}@media only screen and (min-width: 426px)and (max-width: 896px)and (orientation: landscape){#onetrust-pc-sdk p{font-size:.75em}}#onetrust-banner-sdk .banner-option-input:focus+label{outline:1px solid #000;outline-style:auto}.category-vendors-list-handler+a:focus,.category-vendors-list-handler+a:focus-visible{outline:2px solid #000}#onetrust-pc-sdk .ot-userid-title{margin-top:10px}#onetrust-pc-sdk .ot-userid-title>span,#onetrust-pc-sdk .ot-userid-timestamp>span{font-weight:700}#onetrust-pc-sdk .ot-userid-desc{font-style:italic}#onetrust-pc-sdk .ot-host-desc a{pointer-events:initial}#onetrust-pc-sdk .ot-ven-hdr>p a{position:relative;z-index:2;pointer-events:initial}#onetrust-pc-sdk .ot-vnd-serv .ot-vnd-item .ot-vnd-info a,#onetrust-pc-sdk .ot-vs-list .ot-vnd-item .ot-vnd-info a{margin-right:auto}#onetrust-pc-sdk .ot-pc-footer-logo img{width:136px;height:16px}#onetrust-pc-sdk .ot-pur-vdr-count{font-weight:400;font-size:.7rem;padding-top:3px;display:block}#onetrust-banner-sdk .ot-optout-signal,#onetrust-pc-sdk .ot-optout-signal{border:1px solid #32ae88;border-radius:3px;padding:5px;margin-bottom:10px;background-color:#f9fffa;font-size:.85rem;line-height:2}#onetrust-banner-sdk .ot-optout-signal .ot-optout-icon,#onetrust-pc-sdk .ot-optout-signal .ot-optout-icon{display:inline;margin-right:5px}#onetrust-banner-sdk .ot-optout-signal svg,#onetrust-pc-sdk .ot-optout-signal svg{height:20px;width:30px;transform:scale(0.5)}#onetrust-banner-sdk .ot-optout-signal svg path,#onetrust-pc-sdk .ot-optout-signal svg path{fill:#32ae88}#onetrust-consent-sdk .ot-general-modal{overflow:hidden;position:fixed;margin:0 auto;top:50%;left:50%;width:40%;padding:1.5rem;max-width:575px;min-width:575px;z-index:**********;border-radius:2.5px;transform:translate(-50%, -50%)}#onetrust-consent-sdk .ot-signature-health-group{margin-top:1rem;padding-left:1.25rem;padding-right:1.25rem;margin-bottom:.625rem;width:calc(100% - 2.5rem)}#onetrust-consent-sdk .ot-signature-health-group .ot-signature-health-form{gap:.5rem}#onetrust-consent-sdk .ot-signature-health .ot-signature-health-form{width:70%;gap:.35rem}#onetrust-consent-sdk .ot-signature-health .ot-signature-input{height:38px;padding:6px 10px;background-color:#fff;border:1px solid #d1d1d1;border-radius:4px;box-shadow:none;box-sizing:border-box}#onetrust-consent-sdk .ot-signature-health .ot-signature-subtitle{font-size:1.125rem}#onetrust-consent-sdk .ot-signature-health .ot-signature-group-title{font-size:1.25rem;font-weight:bold}#onetrust-consent-sdk .ot-signature-health,#onetrust-consent-sdk .ot-signature-health-group{display:flex;flex-direction:column;gap:1rem}#onetrust-consent-sdk .ot-signature-health .ot-signature-cont,#onetrust-consent-sdk .ot-signature-health-group .ot-signature-cont{display:flex;flex-direction:column;gap:.25rem}#onetrust-consent-sdk .ot-signature-health .ot-signature-paragraph,#onetrust-consent-sdk .ot-signature-health-group .ot-signature-paragraph{margin:0;line-height:20px;font-size:max(14px,.875rem)}#onetrust-consent-sdk .ot-signature-health .ot-health-signature-error,#onetrust-consent-sdk .ot-signature-health-group .ot-health-signature-error{color:#4d4d4d;font-size:min(12px,.75rem)}#onetrust-consent-sdk .ot-signature-health .ot-signature-buttons-cont,#onetrust-consent-sdk .ot-signature-health-group .ot-signature-buttons-cont{margin-top:max(.75rem,2%);gap:1rem;display:flex;justify-content:flex-end}#onetrust-consent-sdk .ot-signature-health .ot-signature-button,#onetrust-consent-sdk .ot-signature-health-group .ot-signature-button{flex:1;height:auto;color:#fff;cursor:pointer;line-height:1.2;min-width:125px;font-weight:600;font-size:.813em;border-radius:2px;padding:12px 10px;white-space:normal;word-wrap:break-word;word-break:break-word;background-color:#68b631;border:2px solid #68b631}#onetrust-consent-sdk .ot-signature-health .ot-signature-button.reject,#onetrust-consent-sdk .ot-signature-health-group .ot-signature-button.reject{background-color:#fff}#onetrust-consent-sdk .ot-input-field-cont{display:flex;flex-direction:column;gap:.5rem}#onetrust-consent-sdk .ot-input-field-cont .ot-signature-input{width:65%}#onetrust-consent-sdk .ot-signature-health-form{display:flex;flex-direction:column}#onetrust-consent-sdk .ot-signature-health-form .ot-signature-label{margin-bottom:0;line-height:20px;font-size:max(14px,.875rem)}@media only screen and (max-width: 600px){#onetrust-consent-sdk .ot-general-modal{min-width:100%}#onetrust-consent-sdk .ot-signature-health .ot-signature-health-form{width:100%}#onetrust-consent-sdk .ot-input-field-cont .ot-signature-input{width:100%}}#onetrust-banner-sdk,#onetrust-pc-sdk,#ot-sdk-cookie-policy,#ot-sync-ntfy{font-size:16px}#onetrust-banner-sdk *,#onetrust-banner-sdk ::after,#onetrust-banner-sdk ::before,#onetrust-pc-sdk *,#onetrust-pc-sdk ::after,#onetrust-pc-sdk ::before,#ot-sdk-cookie-policy *,#ot-sdk-cookie-policy ::after,#ot-sdk-cookie-policy ::before,#ot-sync-ntfy *,#ot-sync-ntfy ::after,#ot-sync-ntfy ::before{-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box}#onetrust-banner-sdk div,#onetrust-banner-sdk span,#onetrust-banner-sdk h1,#onetrust-banner-sdk h2,#onetrust-banner-sdk h3,#onetrust-banner-sdk h4,#onetrust-banner-sdk h5,#onetrust-banner-sdk h6,#onetrust-banner-sdk p,#onetrust-banner-sdk img,#onetrust-banner-sdk svg,#onetrust-banner-sdk button,#onetrust-banner-sdk section,#onetrust-banner-sdk a,#onetrust-banner-sdk label,#onetrust-banner-sdk input,#onetrust-banner-sdk ul,#onetrust-banner-sdk li,#onetrust-banner-sdk nav,#onetrust-banner-sdk table,#onetrust-banner-sdk thead,#onetrust-banner-sdk tr,#onetrust-banner-sdk td,#onetrust-banner-sdk tbody,#onetrust-banner-sdk .ot-main-content,#onetrust-banner-sdk .ot-toggle,#onetrust-banner-sdk #ot-content,#onetrust-banner-sdk #ot-pc-content,#onetrust-banner-sdk .checkbox,#onetrust-pc-sdk div,#onetrust-pc-sdk span,#onetrust-pc-sdk h1,#onetrust-pc-sdk h2,#onetrust-pc-sdk h3,#onetrust-pc-sdk h4,#onetrust-pc-sdk h5,#onetrust-pc-sdk h6,#onetrust-pc-sdk p,#onetrust-pc-sdk img,#onetrust-pc-sdk svg,#onetrust-pc-sdk button,#onetrust-pc-sdk section,#onetrust-pc-sdk a,#onetrust-pc-sdk label,#onetrust-pc-sdk input,#onetrust-pc-sdk ul,#onetrust-pc-sdk li,#onetrust-pc-sdk nav,#onetrust-pc-sdk table,#onetrust-pc-sdk thead,#onetrust-pc-sdk tr,#onetrust-pc-sdk td,#onetrust-pc-sdk tbody,#onetrust-pc-sdk .ot-main-content,#onetrust-pc-sdk .ot-toggle,#onetrust-pc-sdk #ot-content,#onetrust-pc-sdk #ot-pc-content,#onetrust-pc-sdk .checkbox,#ot-sdk-cookie-policy div,#ot-sdk-cookie-policy span,#ot-sdk-cookie-policy h1,#ot-sdk-cookie-policy h2,#ot-sdk-cookie-policy h3,#ot-sdk-cookie-policy h4,#ot-sdk-cookie-policy h5,#ot-sdk-cookie-policy h6,#ot-sdk-cookie-policy p,#ot-sdk-cookie-policy img,#ot-sdk-cookie-policy svg,#ot-sdk-cookie-policy button,#ot-sdk-cookie-policy section,#ot-sdk-cookie-policy a,#ot-sdk-cookie-policy label,#ot-sdk-cookie-policy input,#ot-sdk-cookie-policy ul,#ot-sdk-cookie-policy li,#ot-sdk-cookie-policy nav,#ot-sdk-cookie-policy table,#ot-sdk-cookie-policy thead,#ot-sdk-cookie-policy tr,#ot-sdk-cookie-policy td,#ot-sdk-cookie-policy tbody,#ot-sdk-cookie-policy .ot-main-content,#ot-sdk-cookie-policy .ot-toggle,#ot-sdk-cookie-policy #ot-content,#ot-sdk-cookie-policy #ot-pc-content,#ot-sdk-cookie-policy .checkbox,#ot-sync-ntfy div,#ot-sync-ntfy span,#ot-sync-ntfy h1,#ot-sync-ntfy h2,#ot-sync-ntfy h3,#ot-sync-ntfy h4,#ot-sync-ntfy h5,#ot-sync-ntfy h6,#ot-sync-ntfy p,#ot-sync-ntfy img,#ot-sync-ntfy svg,#ot-sync-ntfy button,#ot-sync-ntfy section,#ot-sync-ntfy a,#ot-sync-ntfy label,#ot-sync-ntfy input,#ot-sync-ntfy ul,#ot-sync-ntfy li,#ot-sync-ntfy nav,#ot-sync-ntfy table,#ot-sync-ntfy thead,#ot-sync-ntfy tr,#ot-sync-ntfy td,#ot-sync-ntfy tbody,#ot-sync-ntfy .ot-main-content,#ot-sync-ntfy .ot-toggle,#ot-sync-ntfy #ot-content,#ot-sync-ntfy #ot-pc-content,#ot-sync-ntfy .checkbox{font-family:inherit;font-weight:normal;-webkit-font-smoothing:auto;letter-spacing:normal;line-height:normal;padding:0;margin:0;height:auto;min-height:0;max-height:none;width:auto;min-width:0;max-width:none;border-radius:0;border:none;clear:none;float:none;position:static;bottom:auto;left:auto;right:auto;top:auto;text-align:left;text-decoration:none;text-indent:0;text-shadow:none;text-transform:none;white-space:normal;background:none;overflow:visible;vertical-align:baseline;visibility:visible;z-index:auto;box-shadow:none}#onetrust-banner-sdk label:before,#onetrust-banner-sdk label:after,#onetrust-banner-sdk .checkbox:after,#onetrust-banner-sdk .checkbox:before,#onetrust-pc-sdk label:before,#onetrust-pc-sdk label:after,#onetrust-pc-sdk .checkbox:after,#onetrust-pc-sdk .checkbox:before,#ot-sdk-cookie-policy label:before,#ot-sdk-cookie-policy label:after,#ot-sdk-cookie-policy .checkbox:after,#ot-sdk-cookie-policy .checkbox:before,#ot-sync-ntfy label:before,#ot-sync-ntfy label:after,#ot-sync-ntfy .checkbox:after,#ot-sync-ntfy .checkbox:before{content:"";content:none}#onetrust-banner-sdk .ot-sdk-container,#onetrust-pc-sdk .ot-sdk-container,#ot-sdk-cookie-policy .ot-sdk-container{position:relative;width:100%;max-width:100%;margin:0 auto;padding:0 20px;box-sizing:border-box}#onetrust-banner-sdk .ot-sdk-column,#onetrust-banner-sdk .ot-sdk-columns,#onetrust-pc-sdk .ot-sdk-column,#onetrust-pc-sdk .ot-sdk-columns,#ot-sdk-cookie-policy .ot-sdk-column,#ot-sdk-cookie-policy .ot-sdk-columns{width:100%;float:left;box-sizing:border-box;padding:0;display:initial}@media(min-width: 400px){#onetrust-banner-sdk .ot-sdk-container,#onetrust-pc-sdk .ot-sdk-container,#ot-sdk-cookie-policy .ot-sdk-container{width:90%;padding:0}}@media(min-width: 550px){#onetrust-banner-sdk .ot-sdk-container,#onetrust-pc-sdk .ot-sdk-container,#ot-sdk-cookie-policy .ot-sdk-container{width:100%}#onetrust-banner-sdk .ot-sdk-column,#onetrust-banner-sdk .ot-sdk-columns,#onetrust-pc-sdk .ot-sdk-column,#onetrust-pc-sdk .ot-sdk-columns,#ot-sdk-cookie-policy .ot-sdk-column,#ot-sdk-cookie-policy .ot-sdk-columns{margin-left:4%}#onetrust-banner-sdk .ot-sdk-column:first-child,#onetrust-banner-sdk .ot-sdk-columns:first-child,#onetrust-pc-sdk .ot-sdk-column:first-child,#onetrust-pc-sdk .ot-sdk-columns:first-child,#ot-sdk-cookie-policy .ot-sdk-column:first-child,#ot-sdk-cookie-policy .ot-sdk-columns:first-child{margin-left:0}#onetrust-banner-sdk .ot-sdk-two.ot-sdk-columns,#onetrust-pc-sdk .ot-sdk-two.ot-sdk-columns,#ot-sdk-cookie-policy .ot-sdk-two.ot-sdk-columns{width:13.3333333333%}#onetrust-banner-sdk .ot-sdk-three.ot-sdk-columns,#onetrust-pc-sdk .ot-sdk-three.ot-sdk-columns,#ot-sdk-cookie-policy .ot-sdk-three.ot-sdk-columns{width:22%}#onetrust-banner-sdk .ot-sdk-four.ot-sdk-columns,#onetrust-pc-sdk .ot-sdk-four.ot-sdk-columns,#ot-sdk-cookie-policy .ot-sdk-four.ot-sdk-columns{width:30.6666666667%}#onetrust-banner-sdk .ot-sdk-eight.ot-sdk-columns,#onetrust-pc-sdk .ot-sdk-eight.ot-sdk-columns,#ot-sdk-cookie-policy .ot-sdk-eight.ot-sdk-columns{width:65.3333333333%}#onetrust-banner-sdk .ot-sdk-nine.ot-sdk-columns,#onetrust-pc-sdk .ot-sdk-nine.ot-sdk-columns,#ot-sdk-cookie-policy .ot-sdk-nine.ot-sdk-columns{width:74%}#onetrust-banner-sdk .ot-sdk-ten.ot-sdk-columns,#onetrust-pc-sdk .ot-sdk-ten.ot-sdk-columns,#ot-sdk-cookie-policy .ot-sdk-ten.ot-sdk-columns{width:82.6666666667%}#onetrust-banner-sdk .ot-sdk-eleven.ot-sdk-columns,#onetrust-pc-sdk .ot-sdk-eleven.ot-sdk-columns,#ot-sdk-cookie-policy .ot-sdk-eleven.ot-sdk-columns{width:91.3333333333%}#onetrust-banner-sdk .ot-sdk-twelve.ot-sdk-columns,#onetrust-pc-sdk .ot-sdk-twelve.ot-sdk-columns,#ot-sdk-cookie-policy .ot-sdk-twelve.ot-sdk-columns{width:100%;margin-left:0}}#onetrust-banner-sdk h1,#onetrust-banner-sdk h2,#onetrust-banner-sdk h3,#onetrust-banner-sdk h4,#onetrust-banner-sdk h5,#onetrust-banner-sdk h6,#onetrust-pc-sdk h1,#onetrust-pc-sdk h2,#onetrust-pc-sdk h3,#onetrust-pc-sdk h4,#onetrust-pc-sdk h5,#onetrust-pc-sdk h6,#ot-sdk-cookie-policy h1,#ot-sdk-cookie-policy h2,#ot-sdk-cookie-policy h3,#ot-sdk-cookie-policy h4,#ot-sdk-cookie-policy h5,#ot-sdk-cookie-policy h6{margin-top:0;font-weight:600;font-family:inherit}#onetrust-banner-sdk h1,#onetrust-pc-sdk h1,#ot-sdk-cookie-policy h1{font-size:1.5rem;line-height:1.2}#onetrust-banner-sdk h2,#onetrust-pc-sdk h2,#ot-sdk-cookie-policy h2{font-size:1.5rem;line-height:1.25}#onetrust-banner-sdk h3,#onetrust-pc-sdk h3,#ot-sdk-cookie-policy h3{font-size:1.5rem;line-height:1.3}#onetrust-banner-sdk h4,#onetrust-pc-sdk h4,#ot-sdk-cookie-policy h4{font-size:1.5rem;line-height:1.35}#onetrust-banner-sdk h5,#onetrust-pc-sdk h5,#ot-sdk-cookie-policy h5{font-size:1.5rem;line-height:1.5}#onetrust-banner-sdk h6,#onetrust-pc-sdk h6,#ot-sdk-cookie-policy h6{font-size:1.5rem;line-height:1.6}@media(min-width: 550px){#onetrust-banner-sdk h1,#onetrust-pc-sdk h1,#ot-sdk-cookie-policy h1{font-size:1.5rem}#onetrust-banner-sdk h2,#onetrust-pc-sdk h2,#ot-sdk-cookie-policy h2{font-size:1.5rem}#onetrust-banner-sdk h3,#onetrust-pc-sdk h3,#ot-sdk-cookie-policy h3{font-size:1.5rem}#onetrust-banner-sdk h4,#onetrust-pc-sdk h4,#ot-sdk-cookie-policy h4{font-size:1.5rem}#onetrust-banner-sdk h5,#onetrust-pc-sdk h5,#ot-sdk-cookie-policy h5{font-size:1.5rem}#onetrust-banner-sdk h6,#onetrust-pc-sdk h6,#ot-sdk-cookie-policy h6{font-size:1.5rem}}#onetrust-banner-sdk p,#onetrust-pc-sdk p,#ot-sdk-cookie-policy p{margin:0 0 1em 0;font-family:inherit;line-height:normal}#onetrust-banner-sdk a,#onetrust-pc-sdk a,#ot-sdk-cookie-policy a{color:#565656;text-decoration:underline}#onetrust-banner-sdk a:hover,#onetrust-pc-sdk a:hover,#ot-sdk-cookie-policy a:hover{color:#565656;text-decoration:none}#onetrust-banner-sdk .ot-sdk-button,#onetrust-banner-sdk button,#onetrust-pc-sdk .ot-sdk-button,#onetrust-pc-sdk button,#ot-sdk-cookie-policy .ot-sdk-button,#ot-sdk-cookie-policy button{margin-bottom:1rem;font-family:inherit}#onetrust-banner-sdk .ot-sdk-button,#onetrust-banner-sdk button,#onetrust-pc-sdk .ot-sdk-button,#onetrust-pc-sdk button,#ot-sdk-cookie-policy .ot-sdk-button,#ot-sdk-cookie-policy button{display:inline-block;height:38px;padding:0 30px;color:#555;text-align:center;font-size:.9em;font-weight:400;line-height:38px;letter-spacing:.01em;text-decoration:none;white-space:nowrap;background-color:rgba(0,0,0,0);border-radius:2px;border:1px solid #bbb;cursor:pointer;box-sizing:border-box}#onetrust-banner-sdk .ot-sdk-button:hover,#onetrust-banner-sdk :not(.ot-leg-btn-container)>button:not(.ot-link-btn):hover,#onetrust-banner-sdk :not(.ot-leg-btn-container)>button:not(.ot-link-btn):focus,#onetrust-pc-sdk .ot-sdk-button:hover,#onetrust-pc-sdk :not(.ot-leg-btn-container)>button:not(.ot-link-btn):hover,#onetrust-pc-sdk :not(.ot-leg-btn-container)>button:not(.ot-link-btn):focus,#ot-sdk-cookie-policy .ot-sdk-button:hover,#ot-sdk-cookie-policy :not(.ot-leg-btn-container)>button:not(.ot-link-btn):hover,#ot-sdk-cookie-policy :not(.ot-leg-btn-container)>button:not(.ot-link-btn):focus{color:#333;border-color:#888;opacity:.7}#onetrust-banner-sdk .ot-sdk-button:focus,#onetrust-banner-sdk :not(.ot-leg-btn-container)>button:focus,#onetrust-pc-sdk .ot-sdk-button:focus,#onetrust-pc-sdk :not(.ot-leg-btn-container)>button:focus,#ot-sdk-cookie-policy .ot-sdk-button:focus,#ot-sdk-cookie-policy :not(.ot-leg-btn-container)>button:focus{outline:2px solid #000}#onetrust-banner-sdk .ot-sdk-button.ot-sdk-button-primary,#onetrust-banner-sdk button.ot-sdk-button-primary,#onetrust-banner-sdk input[type=submit].ot-sdk-button-primary,#onetrust-banner-sdk input[type=reset].ot-sdk-button-primary,#onetrust-banner-sdk input[type=button].ot-sdk-button-primary,#onetrust-pc-sdk .ot-sdk-button.ot-sdk-button-primary,#onetrust-pc-sdk button.ot-sdk-button-primary,#onetrust-pc-sdk input[type=submit].ot-sdk-button-primary,#onetrust-pc-sdk input[type=reset].ot-sdk-button-primary,#onetrust-pc-sdk input[type=button].ot-sdk-button-primary,#ot-sdk-cookie-policy .ot-sdk-button.ot-sdk-button-primary,#ot-sdk-cookie-policy button.ot-sdk-button-primary,#ot-sdk-cookie-policy input[type=submit].ot-sdk-button-primary,#ot-sdk-cookie-policy input[type=reset].ot-sdk-button-primary,#ot-sdk-cookie-policy input[type=button].ot-sdk-button-primary{color:#fff;background-color:#33c3f0;border-color:#33c3f0}#onetrust-banner-sdk .ot-sdk-button.ot-sdk-button-primary:hover,#onetrust-banner-sdk button.ot-sdk-button-primary:hover,#onetrust-banner-sdk input[type=submit].ot-sdk-button-primary:hover,#onetrust-banner-sdk input[type=reset].ot-sdk-button-primary:hover,#onetrust-banner-sdk input[type=button].ot-sdk-button-primary:hover,#onetrust-banner-sdk .ot-sdk-button.ot-sdk-button-primary:focus,#onetrust-banner-sdk button.ot-sdk-button-primary:focus,#onetrust-banner-sdk input[type=submit].ot-sdk-button-primary:focus,#onetrust-banner-sdk input[type=reset].ot-sdk-button-primary:focus,#onetrust-banner-sdk input[type=button].ot-sdk-button-primary:focus,#onetrust-pc-sdk .ot-sdk-button.ot-sdk-button-primary:hover,#onetrust-pc-sdk button.ot-sdk-button-primary:hover,#onetrust-pc-sdk input[type=submit].ot-sdk-button-primary:hover,#onetrust-pc-sdk input[type=reset].ot-sdk-button-primary:hover,#onetrust-pc-sdk input[type=button].ot-sdk-button-primary:hover,#onetrust-pc-sdk .ot-sdk-button.ot-sdk-button-primary:focus,#onetrust-pc-sdk button.ot-sdk-button-primary:focus,#onetrust-pc-sdk input[type=submit].ot-sdk-button-primary:focus,#onetrust-pc-sdk input[type=reset].ot-sdk-button-primary:focus,#onetrust-pc-sdk input[type=button].ot-sdk-button-primary:focus,#ot-sdk-cookie-policy .ot-sdk-button.ot-sdk-button-primary:hover,#ot-sdk-cookie-policy button.ot-sdk-button-primary:hover,#ot-sdk-cookie-policy input[type=submit].ot-sdk-button-primary:hover,#ot-sdk-cookie-policy input[type=reset].ot-sdk-button-primary:hover,#ot-sdk-cookie-policy input[type=button].ot-sdk-button-primary:hover,#ot-sdk-cookie-policy .ot-sdk-button.ot-sdk-button-primary:focus,#ot-sdk-cookie-policy button.ot-sdk-button-primary:focus,#ot-sdk-cookie-policy input[type=submit].ot-sdk-button-primary:focus,#ot-sdk-cookie-policy input[type=reset].ot-sdk-button-primary:focus,#ot-sdk-cookie-policy input[type=button].ot-sdk-button-primary:focus{color:#fff;background-color:#1eaedb;border-color:#1eaedb}#onetrust-banner-sdk input[type=text],#onetrust-pc-sdk input[type=text],#ot-sdk-cookie-policy input[type=text]{height:38px;padding:6px 10px;background-color:#fff;border:1px solid #d1d1d1;border-radius:4px;box-shadow:none;box-sizing:border-box}#onetrust-banner-sdk input[type=text],#onetrust-pc-sdk input[type=text],#ot-sdk-cookie-policy input[type=text]{-webkit-appearance:none;-moz-appearance:none;appearance:none}#onetrust-banner-sdk input[type=text]:focus,#onetrust-pc-sdk input[type=text]:focus,#ot-sdk-cookie-policy input[type=text]:focus{border:1px solid #000;outline:0}#onetrust-banner-sdk label,#onetrust-pc-sdk label,#ot-sdk-cookie-policy label{display:block;margin-bottom:.5rem;font-weight:600}#onetrust-banner-sdk input[type=checkbox],#onetrust-pc-sdk input[type=checkbox],#ot-sdk-cookie-policy input[type=checkbox]{display:inline}#onetrust-banner-sdk ul,#onetrust-pc-sdk ul,#ot-sdk-cookie-policy ul{list-style:circle inside}#onetrust-banner-sdk ul,#onetrust-pc-sdk ul,#ot-sdk-cookie-policy ul{padding-left:0;margin-top:0}#onetrust-banner-sdk ul ul,#onetrust-pc-sdk ul ul,#ot-sdk-cookie-policy ul ul{margin:1.5rem 0 1.5rem 3rem;font-size:90%}#onetrust-banner-sdk li,#onetrust-pc-sdk li,#ot-sdk-cookie-policy li{margin-bottom:1rem}#onetrust-banner-sdk th,#onetrust-banner-sdk td,#onetrust-pc-sdk th,#onetrust-pc-sdk td,#ot-sdk-cookie-policy th,#ot-sdk-cookie-policy td{padding:12px 15px;text-align:left;border-bottom:1px solid #e1e1e1}#onetrust-banner-sdk button,#onetrust-pc-sdk button,#ot-sdk-cookie-policy button{margin-bottom:1rem;font-family:inherit}#onetrust-banner-sdk .ot-sdk-container:after,#onetrust-banner-sdk .ot-sdk-row:after,#onetrust-pc-sdk .ot-sdk-container:after,#onetrust-pc-sdk .ot-sdk-row:after,#ot-sdk-cookie-policy .ot-sdk-container:after,#ot-sdk-cookie-policy .ot-sdk-row:after{content:"";display:table;clear:both}#onetrust-banner-sdk .ot-sdk-row,#onetrust-pc-sdk .ot-sdk-row,#ot-sdk-cookie-policy .ot-sdk-row{margin:0;max-width:none;display:block}#onetrust-banner-sdk{box-shadow:0 0 18px rgba(0,0,0,.2)}#onetrust-banner-sdk.otCenterRounded{z-index:2147483645;top:10%;position:fixed;right:0;background-color:#fff;width:60%;max-width:650px;border-radius:2.5px;left:1em;margin:0 auto;font-size:14px;max-height:90%;overflow-x:hidden;overflow-y:auto}#onetrust-banner-sdk.otRelFont{font-size:.875rem}#onetrust-banner-sdk::-webkit-scrollbar{width:11px}#onetrust-banner-sdk::-webkit-scrollbar-thumb{border-radius:10px;background:#c1c1c1}#onetrust-banner-sdk{scrollbar-arrow-color:#c1c1c1;scrollbar-darkshadow-color:#c1c1c1;scrollbar-face-color:#c1c1c1;scrollbar-shadow-color:#c1c1c1}#onetrust-banner-sdk h3,#onetrust-banner-sdk p{color:dimgray}#onetrust-banner-sdk #onetrust-policy{margin-top:40px}#onetrust-banner-sdk #onetrust-policy-title{float:left;text-align:left;font-size:1em;line-height:1.4;margin-bottom:0;padding:0 0 10px 30px;width:calc(100% - 90px)}#onetrust-banner-sdk #onetrust-policy-text,#onetrust-banner-sdk .ot-b-addl-desc,#onetrust-banner-sdk .ot-gv-list-handler{clear:both;float:left;margin:0 30px 10px 30px;font-size:.813em;line-height:1.5}#onetrust-banner-sdk #onetrust-policy-text *,#onetrust-banner-sdk .ot-b-addl-desc *,#onetrust-banner-sdk .ot-gv-list-handler *{line-height:inherit;font-size:inherit;margin:0}#onetrust-banner-sdk .ot-optout-signal{margin:0 1.875rem .625rem 1.875rem}#onetrust-banner-sdk .ot-gv-list-handler{padding:0;border:0;height:auto;width:auto}#onetrust-banner-sdk .ot-b-addl-desc{display:block}#onetrust-banner-sdk #onetrust-button-group-parent{padding:15px 30px;text-align:center}#onetrust-banner-sdk #onetrust-button-group-parent:not(.has-reject-all-button) #onetrust-button-group{text-align:right}#onetrust-banner-sdk #onetrust-button-group{text-align:center;display:inline-block;width:100%}#onetrust-banner-sdk #onetrust-reject-all-handler,#onetrust-banner-sdk #onetrust-pc-btn-handler{margin-right:1em}#onetrust-banner-sdk #onetrust-pc-btn-handler{border:1px solid #6cc04a;max-width:45%}#onetrust-banner-sdk .banner-actions-container{float:right;width:50%}#onetrust-banner-sdk #onetrust-pc-btn-handler.cookie-setting-link{background-color:#fff;border:none;color:#6cc04a;text-decoration:underline;padding-left:0;padding-right:0}#onetrust-banner-sdk #onetrust-accept-btn-handler,#onetrust-banner-sdk #onetrust-reject-all-handler,#onetrust-banner-sdk #onetrust-pc-btn-handler{background-color:#6cc04a;color:#fff;border-color:#6cc04a;min-width:135px;padding:12px 10px;letter-spacing:.05em;line-height:1.4;font-size:.813em;font-weight:600;height:auto;white-space:normal;word-break:break-word;word-wrap:break-word}#onetrust-banner-sdk .has-reject-all-button #onetrust-pc-btn-handler{float:left;max-width:calc(40% - 18px)}#onetrust-banner-sdk .has-reject-all-button #onetrust-pc-btn-handler.cookie-setting-link{text-align:left;margin-right:0}#onetrust-banner-sdk .has-reject-all-button .banner-actions-container{max-width:60%;width:auto}#onetrust-banner-sdk .ot-close-icon{width:44px;height:44px;background-size:12px;margin:-18px -18px 0 0;border:none;display:inline-block;padding:0}#onetrust-banner-sdk #onetrust-close-btn-container{position:absolute;right:24px;top:20px}#onetrust-banner-sdk .banner_logo{display:none}#onetrust-banner-sdk.ot-bnr-w-logo #onetrust-policy{margin-top:10px}#onetrust-banner-sdk.ot-bnr-w-logo .ot-bnr-logo{margin:4px 25px}#onetrust-banner-sdk #banner-options{float:left;padding:0 30px;width:calc(100% - 90px)}#onetrust-banner-sdk .banner-option{margin-bottom:10px}#onetrust-banner-sdk .banner-option-input{cursor:pointer;width:auto;height:auto;border:none;padding:0;padding-right:3px;margin:0 0 6px;font-size:.82em;line-height:1.4}#onetrust-banner-sdk .banner-option-input *{pointer-events:none;font-size:inherit;line-height:inherit}#onetrust-banner-sdk .banner-option-input[aria-expanded=true] .ot-arrow-container{transform:rotate(90deg)}#onetrust-banner-sdk .banner-option-input[aria-expanded=true]~.banner-option-details{height:auto;display:block}#onetrust-banner-sdk .banner-option-header{cursor:pointer;display:inline-block}#onetrust-banner-sdk .banner-option-header :first-child{color:dimgray;font-weight:bold;float:left}#onetrust-banner-sdk .ot-arrow-container,#onetrust-banner-sdk .banner-option-details{transition:all 300ms ease-in 0s;-webkit-transition:all 300ms ease-in 0s;-moz-transition:all 300ms ease-in 0s;-o-transition:all 300ms ease-in 0s}#onetrust-banner-sdk .ot-arrow-container{display:inline-block;border-top:6px solid rgba(0,0,0,0);border-bottom:6px solid rgba(0,0,0,0);border-left:6px solid dimgray;margin-left:10px;vertical-align:middle}#onetrust-banner-sdk .banner-option-details{display:none;font-size:.83em;line-height:1.5;height:0px;padding:10px 10px 5px 10px}#onetrust-banner-sdk .banner-option-details *{font-size:inherit;line-height:inherit;color:dimgray}#onetrust-banner-sdk .ot-dpd-container{float:left;margin:0 30px 10px 30px}#onetrust-banner-sdk .ot-dpd-title{font-weight:bold;padding-bottom:10px}#onetrust-banner-sdk .ot-dpd-title{font-size:1em;line-height:1.4}#onetrust-banner-sdk .ot-dpd-desc{font-size:.813em;line-height:1.5;margin-bottom:0}#onetrust-banner-sdk .ot-dpd-desc *{margin:0}#onetrust-banner-sdk .onetrust-vendors-list-handler{display:block;margin-left:0px;margin-top:5px;padding:0;margin-bottom:0;border:0;line-height:normal;height:auto;width:auto}#onetrust-banner-sdk :not(.ot-dpd-desc)>.ot-b-addl-desc{float:left;margin:0 30px 10px 30px}#onetrust-banner-sdk .ot-dpd-desc>.ot-b-addl-desc{margin-top:10px;margin-bottom:10px;font-size:1em;line-height:1.5;float:none}#onetrust-banner-sdk #onetrust-policy-text a{font-weight:bold;margin-left:5px}#onetrust-banner-sdk.ot-close-btn-link #onetrust-close-btn-container{top:15px;transform:none;right:15px}#onetrust-banner-sdk.ot-close-btn-link #onetrust-close-btn-container button{padding:0;white-space:pre-wrap;border:none;height:auto;line-height:1.5;text-decoration:underline;font-size:.75em}#onetrust-banner-sdk.ot-close-btn-link.ot-wo-title #onetrust-group-container{margin-top:20px}@media only screen and (max-width: 425px){#onetrust-banner-sdk #onetrust-accept-btn-handler,#onetrust-banner-sdk #onetrust-reject-all-handler,#onetrust-banner-sdk #onetrust-pc-btn-handler{width:100%;margin-bottom:10px}#onetrust-banner-sdk #onetrust-pc-btn-handler,#onetrust-banner-sdk #onetrust-reject-all-handler{margin-right:0}#onetrust-banner-sdk .has-reject-all-button #onetrust-pc-btn-handler.cookie-setting-link{text-align:center}#onetrust-banner-sdk .banner-actions-container,#onetrust-banner-sdk #onetrust-pc-btn-handler{width:100%;max-width:none}#onetrust-banner-sdk.otCenterRounded{left:0;width:95%;top:50%;transform:translateY(-50%);-webkit-transform:translateY(-50%)}}@media only screen and (max-width: 600px){#onetrust-banner-sdk .ot-sdk-container{width:auto;padding:0}#onetrust-banner-sdk #onetrust-policy-title{padding:0 22px 10px 22px}#onetrust-banner-sdk #onetrust-policy-text,#onetrust-banner-sdk :not(.ot-dpd-desc)>.ot-b-addl-desc,#onetrust-banner-sdk .ot-dpd-container{margin:0 22px 10px 22px;width:calc(100% - 44px)}#onetrust-banner-sdk #onetrust-button-group-parent{padding:15px 22px}#onetrust-banner-sdk #banner-options{padding:0 22px;width:calc(100% - 44px)}#onetrust-banner-sdk .banner-option{margin-bottom:6px}#onetrust-banner-sdk .has-reject-all-button #onetrust-pc-btn-handler{float:none;max-width:100%}#onetrust-banner-sdk .has-reject-all-button .banner-actions-container{width:100%;text-align:center;max-width:100%}#onetrust-banner-sdk.ot-close-btn-link #onetrust-group-container{margin-top:20px}}@media only screen and (min-width: 426px)and (max-width: 896px){#onetrust-banner-sdk.otCenterRounded{left:0;top:15%;transform:translateY(-13%);-webkit-transform:translateY(-13%);max-width:600px;width:95%}}
      #onetrust-consent-sdk #onetrust-banner-sdk {background-color: #FFFFFF;}
      #onetrust-consent-sdk #onetrust-policy-title,
      #onetrust-consent-sdk #onetrust-policy-text,
      #onetrust-consent-sdk .ot-b-addl-desc,
      #onetrust-consent-sdk .ot-dpd-desc,
      #onetrust-consent-sdk .ot-dpd-title,
      #onetrust-consent-sdk #onetrust-policy-text *:not(.onetrust-vendors-list-handler),
      #onetrust-consent-sdk .ot-dpd-desc *:not(.onetrust-vendors-list-handler),
      #onetrust-consent-sdk #onetrust-banner-sdk #banner-options *,
      #onetrust-banner-sdk .ot-cat-header,
      #onetrust-banner-sdk .ot-optout-signal
      {
      color: #696969;
      }
      #onetrust-consent-sdk #onetrust-banner-sdk .banner-option-details {
      background-color: #E9E9E9;}
      #onetrust-consent-sdk #onetrust-banner-sdk a[href],
      #onetrust-consent-sdk #onetrust-banner-sdk a[href] font,
      #onetrust-consent-sdk #onetrust-banner-sdk .ot-link-btn
      {
      color: #121212;
      }#onetrust-consent-sdk #onetrust-accept-btn-handler,
      #onetrust-banner-sdk #onetrust-reject-all-handler {
      background-color: #696969;border-color: #696969;
      color: #FFFFFF;
      }
      #onetrust-consent-sdk #onetrust-banner-sdk *:focus,
      #onetrust-consent-sdk #onetrust-banner-sdk:focus {
      outline-color: #000000;
      outline-width: 1px;
      }
      #onetrust-consent-sdk #onetrust-pc-btn-handler,
      #onetrust-consent-sdk #onetrust-pc-btn-handler.cookie-setting-link {
      color: #FFFFFF; border-color: #FFFFFF;
      background-color:
      #696969;
      }#onetrust-pc-sdk #accept-recommended-btn-handler{
      float: left;
      display: none !important;
      }
      #onetrust-pc-sdk #accept-recommended-btn-handler {
      display: none !important;
      }
      #onetrust-pc-sdk .ot-always-active {
      color: #121212;
      }
      /* force hide the floating button */
      #ot-sdk-btn-floating.ot-floating-button {
      display: none !important;
      }
      div#ot-sdk-btn-floating {
      display: none !important;
      }
      .ot-floating-button__front {
      display: none !important;
      }.ot-sdk-cookie-policy{font-family:inherit;font-size:16px}.ot-sdk-cookie-policy.otRelFont{font-size:1rem}.ot-sdk-cookie-policy h3,.ot-sdk-cookie-policy h4,.ot-sdk-cookie-policy h6,.ot-sdk-cookie-policy p,.ot-sdk-cookie-policy li,.ot-sdk-cookie-policy a,.ot-sdk-cookie-policy th,.ot-sdk-cookie-policy #cookie-policy-description,.ot-sdk-cookie-policy .ot-sdk-cookie-policy-group,.ot-sdk-cookie-policy #cookie-policy-title{color:dimgray}.ot-sdk-cookie-policy #cookie-policy-description{margin-bottom:1em}.ot-sdk-cookie-policy h4{font-size:1.2em}.ot-sdk-cookie-policy h6{font-size:1em;margin-top:2em}.ot-sdk-cookie-policy th{min-width:75px}.ot-sdk-cookie-policy a,.ot-sdk-cookie-policy a:hover{background:#fff}.ot-sdk-cookie-policy thead{background-color:#f6f6f4;font-weight:bold}.ot-sdk-cookie-policy .ot-mobile-border{display:none}.ot-sdk-cookie-policy section{margin-bottom:2em}.ot-sdk-cookie-policy table{border-collapse:inherit}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy{font-family:inherit;font-size:1rem}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy h3,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy h4,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy h6,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy p,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy li,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy a,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy th,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy #cookie-policy-description,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy .ot-sdk-cookie-policy-group,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy #cookie-policy-title{color:dimgray}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy #cookie-policy-description{margin-bottom:1em}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy .ot-sdk-subgroup{margin-left:1.5em}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy #cookie-policy-description,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy .ot-sdk-cookie-policy-group-desc,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy .ot-table-header,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy a,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy span,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy td{font-size:.9em}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy td span,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy td a{font-size:inherit}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy .ot-sdk-cookie-policy-group{font-size:1em;margin-bottom:.6em}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy .ot-sdk-cookie-policy-title{margin-bottom:1.2em}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy>section{margin-bottom:1em}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy th{min-width:75px}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy a,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy a:hover{background:#fff}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy thead{background-color:#f6f6f4;font-weight:bold}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy .ot-mobile-border{display:none}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy section{margin-bottom:2em}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy .ot-sdk-subgroup ul li{list-style:disc;margin-left:1.5em}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy .ot-sdk-subgroup ul li h4{display:inline-block}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table{border-collapse:inherit;margin:auto;border:1px solid #d7d7d7;border-radius:5px;border-spacing:initial;width:100%;overflow:hidden}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table th,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table td{border-bottom:1px solid #d7d7d7;border-right:1px solid #d7d7d7}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table tr:last-child td{border-bottom:0px}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table tr th:last-child,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table tr td:last-child{border-right:0px}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table .ot-host,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table .ot-cookies-type{width:25%}.ot-sdk-cookie-policy[dir=rtl]{text-align:left}#ot-sdk-cookie-policy h3{font-size:1.5em}@media only screen and (max-width: 530px){.ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) table,.ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) thead,.ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) tbody,.ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) th,.ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) td,.ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) tr{display:block}.ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) thead tr{position:absolute;top:-9999px;left:-9999px}.ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) tr{margin:0 0 1em 0}.ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) tr:nth-child(odd),.ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) tr:nth-child(odd) a{background:#f6f6f4}.ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) td{border:none;border-bottom:1px solid #eee;position:relative;padding-left:50%}.ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) td:before{position:absolute;height:100%;left:6px;width:40%;padding-right:10px}.ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) .ot-mobile-border{display:inline-block;background-color:#e4e4e4;position:absolute;height:100%;top:0;left:45%;width:2px}.ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) td:before{content:attr(data-label);font-weight:bold}.ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) li{word-break:break-word;word-wrap:break-word}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table{overflow:hidden}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table td{border:none;border-bottom:1px solid #d7d7d7}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy thead,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy tbody,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy th,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy td,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy tr{display:block}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table .ot-host,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table .ot-cookies-type{width:auto}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy tr{margin:0 0 1em 0}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy td:before{height:100%;width:40%;padding-right:10px}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy td:before{content:attr(data-label);font-weight:bold}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy li{word-break:break-word;word-wrap:break-word}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy thead tr{position:absolute;top:-9999px;left:-9999px;z-index:-9999}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table tr:last-child td{border-bottom:1px solid #d7d7d7;border-right:0px}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table tr:last-child td:last-child{border-bottom:0px}}
      #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy h5,
      #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy h6,
      #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy li,
      #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy p,
      #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy a,
      #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy span,
      #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy td,
      #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy #cookie-policy-description {
      color: #696969;
      }
      #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy th {
      color: #696969;
      }
      #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy .ot-sdk-cookie-policy-group {
      color: #696969;
      }
      #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy #cookie-policy-title {
      color: #696969;
      }
      #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table th {
      background-color: #F8F8F8;
      }
      .ot-floating-button__front{background-image:url('https://cdn.cookielaw.org/logos/static/ot_persistent_cookie_icon.png')}
    </style>
    <script src="https://globalservices.conde.digital/cwv-attribution.3.4.0.js" async="1"></script><script async="" type="text/javascript" src="//imasdk.googleapis.com/js/sdkloader/gpt_proxy.js"></script><script id="funnel-relay-installer" data-customer-id="condenast_eujdmc753_arstechnica" data-property-id="PROPERTY_ID" data-autorun="true" async="true" src="https://cdn-magiclinks.trackonomics.net/client/static/v2/condenast_eujdmc753_arstechnica.js"></script><script async="" src="https://static.hotjar.com/c/hotjar-1632543.js?sv=6"></script>
    <style id="tRecs-taboola-choice"> .trc_user_exclude_btn{background:url("//cdn.taboola.com/libtrc/static/thumbnails/f539211219b796ffbb49949997c764f0.png") no-repeat scroll 0 0 rgba(0,0,0,0);width:12px;height:12px;position:absolute;right:2px;top:2px;z-index:9000;cursor:pointer;visibility:hidden}.trc_exclude_undo_btn{font-family:Arial,Helvetica,sans-serif;font-size:11px;line-height:14px;font-weight:normal;color:#36c;text-decoration:underline;cursor:pointer;position:absolute;right:2px;top:2px;padding:0 1px;z-index:11000;visibility:hidden}.videoCube:hover .trc_user_exclude_btn,.videoCube_hover .trc_user_exclude_btn,.trc_user_excluded.videoCube:hover .trc_exclude_undo_btn,.trc_user_excluded.videoCube_hover .trc_exclude_undo_btn,.trc_exclude_undo_btn.trc_exclude_undo_btn_visible{visibility:visible}.videoCube.trc_user_excluded .trc_user_exclude_btn{visibility:hidden}.trc_fade{opacity:0;filter:alpha(opacity=0);visibility:hidden;transition:opacity 500ms 0s,visibility 0s 500ms;-webkit-transition:opacity 500ms 0s,visibility 0s 500ms}.trc_fade.trc_in,.trc_user_excluded .trc_exclude_overlay{visibility:visible;opacity:1;filter:alpha(opacity=100);transition-delay:0s,0s;-webkit-transition-delay:0s,0s}.trc_excludable .trc_exclude_overlay{position:absolute;z-index:10000;top:0;left:0;width:100%;height:100%;cursor:default;background-color:#fff}.videoCube.trc_user_excluded .trc_exclude_overlay{visibility:visible;opacity:.8;filter:alpha(opacity=80)}.videoCube.trc_user_excluded .thumbBlock{filter:grayscale(100%);-webkit-filter:grayscale(100%)}.videoCube.trc_user_excluded:hover a .video-label-box .video-title,.videoCube_hover.trc_user_excluded a .video-label-box .video-title{text-decoration:none}.videoCube.trc_user_excluded a .video-label-box *,.videoCube.trc_user_excluded:hover a .video-label-box *,.videoCube_hover.trc_user_excluded a .video-label-box *{color:#000;overflow:hidden;transition:color 500ms 0s;-webkit-transition:color 500ms 0s}</style>
    <style id="tRecs-taboola-choice-popover-container"> .trc_popover{position:absolute;font-family:Arial,Helvetica,sans-serif;font-size:12px;line-height:16px;color:#000;cursor:default;top:0;right:0;z-index:**********;width:180px;padding:1px;text-align:left;white-space:normal;background-color:#fff;border:1px solid rgba(0,0,0,.2);border-radius:6px;box-shadow:0 5px 10px rgba(0,0,0,.2);background-clip:padding-box;-webkit-background-clip:padding;box-sizing:content-box}.trc_popover.trc_bottom{margin-top:10px}.trc_popover.trc_bottom .trc_popover_arrow{top:-11px;right:11px;margin-left:-11px;border-bottom-color:#999;border-bottom-color:rgba(0,0,0,.25);border-top-width:0}.trc_popover.trc_bottom .trc_popover_arrow:after{top:1px;margin-left:-10px;border-bottom-color:#fff;border-top-width:0}.trc_popover iframe{width:100%}.trc_popover .trc_popover_arrow,.trc_popover .trc_popover_arrow:after{position:absolute;display:block;width:0;height:0;border:solid rgba(0,0,0,0)}.trc_popover .trc_popover_arrow{border-width:11px}.trc_popover .trc_popover_arrow:after{border-width:10px;content:""}.trc_popover_fade{visibility:hidden;opacity:0;filter:alpha(opacity=0);transition:opacity 500ms 0s,visibility 0s 500ms;-webkit-transition:opacity 500ms 0s,visibility 0s 500ms}.trc_popover_fade.trc_popover_show{visibility:visible;opacity:1;filter:alpha(opacity=100);transition-delay:0s,0s;-webkit-transition-delay:0s,0s}</style>
    <style id="tRecs-taboola-choice-popover-content"> .popupContentWrapper{font-family:Arial,Helvetica,sans-serif;font-weight:normal;font-size:12px;color:#000}.popupContentWrapper .trc_popover_title_wrapper{padding:8px 14px;margin:0;font-weight:bold;background-color:#f7f7f7;border-bottom:1px solid #ebebeb;border-radius:5px 5px 0 0}.popupContentWrapper .trc_popover_title{width:100%;display:inline-block;vertical-align:middle}.popupContentWrapper .trc_popover_content_wrapper{display:inline-block;float:left;padding:9px 14px}.popupContentWrapper .trc_popover_content_wrapper .trc_questionnaire_container label{float:left;clear:left;width:100%;cursor:pointer;line-height:20px;text-align:left}.popupContentWrapper .trc_popover_content{width:100%}.popupContentWrapper .trc_questionnaire_container .trc_questionnaire_title{margin:0 0 3px}.popupContentWrapper .trc_questionnaire_container input[type=radio]{float:left;cursor:pointer;margin:3px 4px 0 5px}.popupContentWrapper .trc_questionnaire_container_ie.trc_questionnaire_container input[type=radio]{margin:0 2px 0 0}</style>
    <script src="//tag.bounceexchange.com/2806/i.js" async=""></script><script async="" src="https://script.hotjar.com/modules.ca70bc16369dcd35d4ef.js" charset="utf-8"></script>
    <style id="tblStyle_0"> .trc_rbox_container { direction: ltr; text-align: left; } /*override bootstrap default css */ .trc_rbox_container [class*=span] { float: none; margin-left: 0; } /*------------- Multi-widget -------------*/ .trc_multi_widget_container { display: -ms-flexbox; display: flex; -ms-flex-pack: justify; justify-content: space-between; } .trc_multi_widget_container .trc_rbox_div { margin: 0; } /*----------------------------------------*/ .trc_rbox_header { border: 0 solid; overflow: hidden; vertical-align: middle; } .trc_rbox_container .trc_img { display: inline-block !important; } .trc_rbox_header_icon_div { display: table-cell; vertical-align: baseline; } .trc_rbox_header .trc_rbox_header_icon_div .trc_rbox_header_icon_img { vertical-align: middle; width: auto; } .trc_rbox_header_icon_span { display: inline-table; } .in_trc_header { position: relative !important; float: right; margin: 0; } #trc_rbox_css_loaded { overflow: hidden; width: 0; height: 0; } .trc_rbox { margin-top: 0; } .trc_rbox_div { margin: 0 0 3px; direction: ltr; padding: 0; box-sizing: border-box; -moz-box-sizing: border-box; -ms-box-sizing: border-box; -webkit-box-sizing: border-box; overflow: auto; position: relative; width: auto; border: solid #CCC 1px; } .loading-animation span { display: block; } .videoCube { zoom: 1; cursor: pointer; float: none; overflow: hidden; box-sizing: border-box; -moz-box-sizing: border-box; -ms-box-sizing: border-box; -webkit-box-sizing: border-box; } div.videoCube:hover, .videoCube_hover { cursor: pointer; } .videoCube span.video-title:hover, .videoCube_hover span.video-title { text-decoration: underline; } .videoCube a { text-decoration: none; border: 0; color: black; cursor: pointer; } .videoCube a:hover, .videoCube_hover a, .videoCube a:link, .videoCube a { text-decoration: none !important; outline: none; } .videoCube a .thumbBlock { float: left; display: block; overflow: hidden !important; } .videoCube a img, .videoCube img { border: 0; display: block; margin: 0; height: auto; width: auto; } .videoCube .video-label { display: block; overflow: hidden; } .videoCube .video-label { width: auto !important; white-space: pre-wrap; /* css-3 */ white-space: -moz-pre-wrap; /* Mozilla, since 1999 */ white-space: -o-pre-wrap; /* Opera 7 */ word-wrap: break-word; /* Internet Explorer 5.5+ */ } .videoCube .video-label-box.label-box-with-title-icon { display: table; } .video-icon-container { float: left; display: table-cell; vertical-align: baseline; } .video-icon-img { vertical-align: middle; } .videoCube .video-duration { height: 0; float: left; position: relative; color: white; font-size: 11px; } .videoCube .video-duration dt { border-radius: 4px; -moz-border-radius: 4px; -webkit-border-radius: 4px; background-color: black; opacity: 0.6; filter: alpha(opacity=60); } /* browser native line-clamp */ .videoCube span.video-label.trc_ellipsis { position: relative; overflow: hidden; display: -webkit-box; -webkit-box-orient: vertical; } .videoCube span.video-label.trc-smart-ellipsis { position: relative; overflow: hidden; } .videoCube span.video-label.trc-smart-ellipsis ins { display: inline-block; text-decoration: inherit; } .videoCube span.video-label.trc-smart-ellipsis.tbl-ltr-label { direction: ltr; } .videoCube span.video-label.trc-smart-ellipsis.tbl-ltr-label ins { float: left; margin-right: 5px; direction: ltr; } .videoCube span.video-label.trc_ellipsis.tbl-rtl-label{ direction: rtl; text-align: right; width: auto !important; } .videoCube span.video-label.trc-smart-ellipsis.tbl-rtl-label { float: right; direction: rtl; width: auto !important; } .videoCube span.video-label.trc-smart-ellipsis.tbl-rtl-label ins { float: right; margin-left: 5px; direction: rtl; } .videoCube span.video-label.trc-smart-ellipsis ins.lastLineEllipsis { display: block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; word-wrap: normal; width: 100%; } .video-duration.video-duration-detail div { color: white; } .trc_rbox .sponsored { position: relative; display: block; overflow: visible; height: auto; width: auto; padding-right: 0; text-align: right; font-size: 9px; } /* Configuration defaults */ .trc_rbox_div { height: 410px; } .videoCube { direction: ltr; font-size: 11px; margin: 0; color: black; border-width: 0; } .videoCube.vertical:first-child { border-top: 0; margin-top: 0; } .videoCube.horizontal:first-child { border-left: 0; margin-left: 0; } div.videoCube:hover, .videoCube_hover { background-color: #EBF0FF; color: black; } .videoCube .thumbBlock { margin: 0; border-style: solid; } .videoCube a img, .videoCube img { border-color: #ececec; } .videoCube .video-label-box { margin-left: 81px; } .videoCube .video-label dt { font-weight: bold; } .videoCube .video-title { height: auto; margin-bottom: 3px; white-space: normal; } .videoCube .trc_inline_detail_spacer { display: inline-block; white-space: pre; } .loading-animation { font-family: sans; font-size: 1.5em; text-align: center; color: gray; height: 100%; } .trc_rbox_header { font-family: Arial, Helvetica, sans-serif; font-size: 12px; font-weight: bold; text-decoration: none; color: black; } .trc_header_right_part { position: absolute; left: 50%; top: 0; } .branding_div { overflow: visible; float: right; } .branding_div img { height: 20px; } .videoCube .branding .logoDiv { font-size: inherit; line-height: inherit; background: none; margin: 0; padding: 0; } .videoCube .branding .logoDiv a { vertical-align: inherit; color: inherit; line-height: inherit; } .videoCube .branding .logoDiv a span { vertical-align: inherit; } .trc_related_container .videoCube .branding .attribution-disclosure-link-sponsored { display: inline-block; float: none; } .trc_related_container .videoCube .branding .attribution-disclosure-link-sponsored.align-disclosure-right { float: right; margin-left: auto; padding-left: 2px; } .videoCube .video-label-box .branding.composite-branding { display: -webkit-box; display: -ms-flexbox; display: flex; } .branding.composite-branding > * { display: inline-block; vertical-align: bottom; } .branding .branding-separator { margin: 0 2px; font-weight: normal; } .branding .branding-inner { text-overflow: ellipsis; overflow: hidden; white-space: nowrap; } .video-label-box span.branding.inline-branding { display: inline-block; } /* Support for Horizontal mode */ .trc_related_container div.horizontal { float: left; box-sizing: border-box; -moz-box-sizing: border-box; -ms-box-sizing: border-box; -webkit-box-sizing: border-box; } /* Support for thumbnail position */ .trc_related_container DIV.videoCube.thumbnail_top .thumbBlock, .trc_related_container DIV.videoCube.thumbnail_bottom .thumbBlock { float: none; } /* SEO blocks should be hidden once R-Box loads */ .vidiscovery-note { display: none; } .videoCube .thumbBlock .trc_sponsored_overlay_base { display: block; width: auto; margin-left: 0; position: absolute; color: white !important; } .videoCube .thumbBlock .trc_sponsored_overlay { filter: alpha(opacity=60); opacity: 0.6; display: block; position: absolute; } .videoCube .thumbBlock .trc_sponsored_overlay_base .sponsored { position: relative; display: block; overflow: visible; width: auto; text-align: center; padding: 0 5px; margin-top: 0; } .videoCube .thumbBlock .trc_sponsored_overlay_base.round .trc_sponsored_overlay { border-radius: 4px; -moz-border-radius: 4px; -webkit-border-radius: 4px; } .videoCube .thumbBlock .trc_sponsored_overlay_base.round { margin-left: 4px; } .thumbnail-emblem, .videoCube .thumbnail-overlay, .videoCube:hover .thumbnail-overlay, .videoCube_hover .thumbnail-overlay { position: absolute; background: transparent no-repeat; background-size: contain; z-index: 50; } .thumbnail_bottom { padding-bottom: 8px; } .trc_related_container .logoDiv { font-family: Arial, Helvetica, sans-serif; white-space: nowrap; font-size: 9px; } .trc_related_container .logoDiv a { font-size: 9px; text-decoration: none !important; color: black; margin-right: 1px; /* don't allow focus line to cause overflow */ vertical-align: text-bottom; } .logoDiv a span:hover { text-decoration: underline; } .trc_rbox_header .logoDiv { font-size: 1em; } /* text-link widgets*/ .trc_tl .trc_rbox_header .logoDiv { position: relative; z-index: 1; } .trc_tl .trc_rbox_header_span .trc_header_right_column { position: absolute; width: 48%; left: 52%; top: 0; } .trc_tl .trc_rbox_div .videoCube.horizontal { clear: left; } .trc_tl .trc_rbox_div .videoCube.trc_tl_right_col { float: none; clear: right; margin-left: auto; } .trc_tl .videoCube .video-title .branding { line-height: 1.3em; } .trc_tl .videoCube:hover span.branding, .trc_tl .videoCube_hover span.branding { text-decoration: none; } .trc_tl .trc_rbox_div .videoCube.thumbnail_none a{ vertical-align: top; overflow: visible; } .trc_tl .videoCube .video-label-box { display: inline-block; vertical-align: top; width: 100%; } /* text-link widgets - end*/ .trc_rbox_container.trc_expandable { overflow: hidden; max-height: 0; transition-property: max-height; -webkit-transition-property: max-height; -moz-transition-property: max-height; -o-transition-property: max-height; -webkit-transform: translateZ(0); -moz-transform: translateZ(0); -ms-transform: translateZ(0); -o-transform: translateZ(0); transform: translateZ(0); } .trc_related_container .videoCube .thumbBlock .branding { position: absolute; bottom: 0; z-index: 1; width: 100%; margin: 0; padding: 5px 0; text-align: center; } .syndicatedItem .branding { margin: 0; } .trc-inplayer-rbox { background: #333; background: rgba(30, 30, 30, 0.9); bottom: 0; position: absolute; height: 300px; text-align: center; } .trc-inplayer-rbox .trc_rbox_container { margin: 50px auto 0; width: 640px } .trc_rbox.trc-auto-size { width: 100%; height: 100%; } .videoCube.thumbnail_under .video-title { min-height: 2.58em; } .videoCube.thumbnail_under .tbl-text-over-container { width: 100%; position: absolute; z-index: 1; left: 0; bottom: 0; min-height: 66%; max-height: 66%; padding-top: 2px; padding-bottom: 2px; line-height: 1.25em; } .videoCube.thumbnail_under .tbl-text-over-container .tbl-text-over { height: 100%; width: 100%; position: absolute; background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.8) 100%); filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#00000000, endColorstr=#CC000000, GradientType=0); } .videoCube.thumbnail_under .tbl-text-over-container span.video-title, .videoCube.thumbnail_under .tbl-text-over-container span.video-description, .videoCube.thumbnail_under .tbl-text-over-container span.branding { position: relative; z-index: 1; padding: 0 8px; margin: 0; } .videoCube.thumbnail_under .tbl-text-over-container span.video-title { margin-bottom: 6px; min-height: auto; } .videoCube.thumbnail_under .tbl-text-over-container .video-label-box { position: absolute; bottom: 0; left: 0; width: 100%; padding: 0 8px 6px 8px; min-height: auto; box-sizing: border-box; } .trc-auto-size .trc_rbox_outer .trc_rbox_div { height: auto; width: auto; } .trc-auto-size .trc_rbox_div .videoCube { height: auto; } .trc-auto-size .trc_rbox_div .videoCube.trc-first-recommendation { margin-top: 0; } .trc_rbox .trc_rbox_outer .trc_rbox_div .videoCube.trc-first-in-row { margin-left: 0; } .trc_elastic .trc_rbox { width: auto; } .trc_elastic .videoCube { overflow: hidden; } .trc_elastic .videoCube .thumbBlock { background: transparent no-repeat center center; background-size: cover; position: absolute; display: inline-block; top: 0; right: 0; bottom: 0; left: 0; margin-left: 0; margin-right: 0; } .trc_elastic .thumbBlock_holder { position: relative; width: 100%; } .trc_elastic .thumbnail_start .thumbBlock_holder { float: left; margin-right: 10px; } .trc_elastic .thumbnail_start.item-has-pre-label .thumbBlock_holder { margin-right: 0; } .trc_elastic .videoCube_aspect { width: 1px; } .trc_elastic .trc_rbox .trc_rbox_div { height: auto; } .trc_elastic .thumbnail_start .trc-pre-label { float: left; padding-right: 10px; } .trc_elastic .thumbnail_start.trc-split-label .trc-main-label { float: left; padding-left: 10px; } .trc_elastic .video-label-box { display: block; } .trc_elastic .thumbnail_start .video-label-box { box-sizing: border-box; } /** USER Ad-Choice **/ .trc_user_adChoice_btn { background: url("//cdn.taboola.com/static/c5/c5ef96bc-30ab-456a-b3d5-a84f367c6a46.svg") no-repeat scroll 0 0 rgba(255, 255, 255, 1); border-radius: 0 0 0 5px; width: 16px; height: 16px; position: absolute; right: 0; top: 0; z-index: 9000; cursor: pointer; border-width: 2px 0 2px 4px; border-style: solid; border-color: #fff; opacity: 0.7; background-size: contain; visibility: hidden; } .videoCube:hover .trc_user_adChoice_btn, .videoCube_hover .trc_user_adChoice_btn { visibility: visible; } .videoCube .trc_user_adChoice_btn_static { visibility: visible; } .p-video-overlay-container { position: absolute; width: 100%; height: 100%; top: 0; left: 0; background-color: transparent; } .p-video-overlay.p-video-overlay-show { display: flex; } .p-video-overlay { display: none; background-color: #000; opacity: 0.7; width: 100%; height: 100%; flex-direction: column; } .p-video-overlay-action { color: #fff; width: 100%; direction: ltr; text-align: center; display: flex; justify-content: center; flex-direction: column; } .p-video-overlay-action.p-video-back-action { height: 34%; } .p-video-back-action-label { font-family: Helvetica Neue, serif; font-size: 14px; font-weight: 200; letter-spacing: 1px } .p-video-overlay-action.p-video-goto-action { height: 66%; } .p-video-goto-action-url { font-family: Helvetica Neue, serif; font-size: 24px; font-weight: 400; text-decoration: underline; margin-top: 5px; } .p-video-goto-action-label { font-family: Helvetica Neue, serif; font-size: 14px; font-weight: 100; letter-spacing: 1px; } .trc_related_container .trc_clearer { clear: both; height: 0; overflow: hidden; font-size: 0; line-height: 0; visibility: hidden; } /* Ad Choices */ .link-adc { float: right !important; } .trc-widget-footer .logoDiv { line-height: normal; padding-bottom: 5px; } .trc-widget-footer .link-adc a .trc_adc_wrapper, .trc_header_ext .link-adc a .trc_adc_wrapper { height: 12px; width: 18px; display: inline-block; padding-left: 1px; margin-bottom: 2px; } .trc-widget-footer .link-adc a .trc_adc_s_logo, .trc_header_ext .link-adc a .trc_adc_s_logo, .trc-widget-footer .link-adc a .trc_adc_b_logo, .trc_header_ext .link-adc a .trc_adc_b_logo { vertical-align: middle; height: 15px; display: inline-block; margin-top: -1px; /**fix v align of adc logo - compensate for link underline */ } .trc-widget-footer .link-adc a .trc_adc_s_logo, .trc_header_ext .link-adc a .trc_adc_s_logo { width: 12px; height: 14px; background: url("//cdn.taboola.com/static/c5/c5ef96bc-30ab-456a-b3d5-a84f367c6a46.svg") no-repeat; background-size: contain; vertical-align: middle; } .trc-widget-footer .link-adc a .trc_adc_b_logo, .trc_header_ext .link-adc a .trc_adc_b_logo { width: 77px; background: #ffffff url("//cdn.taboola.com/libtrc/static/thumbnails/0781f9c5a8637d1e162874f157460048.png") no-repeat !important; right: -1px; display: none; position: absolute; } /* Attribution och Disclosure */ .logoDiv .trc_mobile_disclosure_link, .logoDiv .trc_mobile_attribution_link, .logoDiv .trc_mobile_adc_link { display: none; } .logoDiv .trc_desktop_disclosure_link, .logoDiv .trc_desktop_attribution_link, .logoDiv .trc_desktop_adc_link { display: inline; } @media screen and (max-width: 767px) { .logoDiv .trc_mobile_disclosure_link { display: inline; } .logoDiv .trc_mobile_attribution_link { display: inline; } .logoDiv .trc_mobile_adc_link { display: inline; } .logoDiv .trc_desktop_disclosure_link { display: none; } .logoDiv .trc_desktop_attribution_link { display: none; } .logoDiv .trc_desktop_adc_link { display: none; } } .trc_in_iframe .logoDiv .trc_mobile_attribution_link, .trc_in_iframe .logoDiv .trc_mobile_disclosure_link { display: inline; } .trc_in_iframe .logoDiv .trc_desktop_attribution_link, .trc_in_iframe .logoDiv .trc_desktop_disclosure_link { display: none; } .trc_related_container .logoDiv, .trc_related_container .trc_header_ext .logoDiv { float: right; } .trc_related_container .logoDiv + .logoDiv { margin-right: 2px; } .trc_related_container .attribution-disclosure-link-sponsored, .trc_related_container .attribution-disclosure-link-hybrid { display: none; } .trc_related_container .trc-content-sponsored .attribution-disclosure-link-sponsored, .trc-w2f.trc-content-sponsored .attribution-disclosure-link-sponsored { display: block; } .trc_related_container .trc-content-hybrid .attribution-disclosure-link-hybrid, .trc-w2f.trc-content-hybrid .attribution-disclosure-link-hybrid { display: block; } .trc_related_container .trc-widget-footer:hover a span, .trc_related_container .trc_header_ext:hover a span { text-decoration: underline !important; } /* this span makes sure that all logos (attribution + adc + disclosure) are vertically aligned - especially when the attribution font-size is smaller than the adc logo height (15px) */ .logoDiv a span.trc_logos_v_align { display: inline-block !important; font-size: 15px !important; line-height: 1em !important; width: 0 !important; } .trc_related_container .trc_header_ext:hover a span.trc_logos_v_align, .trc_related_container .trc_header_ext:hover a span.trc_adc_wrapper, .trc_related_container .trc-widget-footer:hover a span.trc_logos_v_align, .trc_related_container .trc-widget-footer:hover a span.trc_adc_wrapper { text-decoration: none !important; } .trc_related_container .trc_rbox_header_span .trc_header_right_column { display: none; } .trc_related_container img { max-width: none; } .trc_related_container * { pointer-events: auto; } .trc_related_container { clear: both; } .tbl-loading-spinner { width: 100%; height: 40px; background: url(//cdn.taboola.com/static/91/91a25024-792d-4b52-84e6-ad1478c3f552.gif) center center no-repeat; background-size: 40px; } .tbl-hidden { display: none !important; } .tbl-invisible { opacity: 0; pointer-events: none; } .tbl-batch-anchor { width: 100%; height: 1px; } .videoCube .video-logo + .branding.composite-branding { display: inline-block; vertical-align: middle; } .videoCube .video-logo { margin-right: 4px; } .videoCube .video-logo .branding { margin: auto auto auto 25px; } .videoCube .video-logo img { padding: 0; max-height: 14px; width: auto; max-width: 100px; display: inline-block; } /* Support for integrated widget frame */ .iw_video_frame .trc_rbox_div { overflow: hidden; } .trc-w2f .trc_rbox .trc_rbox_header, .trc-w2f .trc_rbox .trc-widget-footer { display: none !important; } .videoCube .tbl-organic-video-wrapper { display: -ms-flexbox; display: flex; align-items: flex-start; height: 100%; width: 100%; background-color: transparent; background-size: auto; text-align: center; overflow: hidden; position: absolute; top: 0; z-index: 3; opacity: 0; } .videoCube .tbl-o-video-loaded .tbl-organic-video-wrapper { opacity: 1; } .videoCube .tbl-o-video-loaded .thumbBlock { display: none; } .videoCube .tbl-organic-video-wrapper .tbl-o-vertical-video { height: 100%; } .videoCube .tbl-organic-video { width: 100%; height: auto; object-fit: contain; object-position: 50% 50%; -o-object-fit: contain; -o-object-position: 50% 50%; background-color: black; pointer-events: none; } .videoCube .tbl-accessibility-title { font: -apple-system-headline !important; } .videoCube .tbl-accessibility-description { font: -apple-system-subheadline !important; } .videoCube .tbl-accessibility-branding { font: -apple-system-caption1 !important; } .thumbnails-a-6x1 {width:300px;_width:300px;border-width:0px 0px 0px 0px;border-style:solid solid solid solid;border-color:#DFDFDF;padding:0px 0px 0px 0px;border-radius:0;-moz-border-radius:0;-webkit-border-radius:0;box-shadow:none;}.thumbnails-a-6x1 vignette{xButtonColor:#fff;backgroundColor:#fff;backgroundOpacity:0.8;xButtonBGColor:#000;}.thumbnails-a-6x1 .playerCube .video-external-data{font-size:10px;font-weight:normal;text-decoration:none;color:black;}.thumbnails-a-6x1 .tbl-reco-reel-slider{z-index:99999;margin:initial;top:50%;}.thumbnails-a-6x1 .trc_lightbox_overlay{background-color:#000000;opacity:0.70;filter:alpha(opacity=70);}.thumbnails-a-6x1 .tbl-recommendation-reel .tbl-text-under-branding-background{background-color:#EBEBEB;}.thumbnails-a-6x1 div.syndicatedItem:hover,.thumbnails-a-6x1  div.syndicatedItem.videoCube_hover{background-color:transparent;}.thumbnails-a-6x1 .playerCube div.videoCube:hover,.thumbnails-a-6x1  div.videoCube_hover{background-color:transparent;}.thumbnails-a-6x1 .trc_pager_prev:hover,.thumbnails-a-6x1  .trc_pager_next:hover{color:#6497ED;}.thumbnails-a-6x1 .trc_rbox_border_elm{border-color:darkgray;}.thumbnails-a-6x1 .syndicatedItem .video-views{color:black;font-size:10px;font-weight:normal;text-decoration:none;}.thumbnails-a-6x1 .syndicatedItem .video-category{color:black;font-size:10px;font-weight:normal;text-decoration:none;}.thumbnails-a-6x1 .tbl-vignette-close-btn-wrp{height:15;background:#000;}.thumbnails-a-6x1 .syndicatedItem .sponsored{color:#9C9A9C;font-size:9px;font-weight:normal;text-decoration:none;}.thumbnails-a-6x1 .pager_disabled{color:#7d898f;}.thumbnails-a-6x1 .playerCube .video-category{font-size:10px;font-weight:normal;text-decoration:none;color:black;}.thumbnails-a-6x1 .syndicatedItem .video-uploader{color:black;font-size:10px;font-weight:normal;text-decoration:none;}.thumbnails-a-6x1 .videoCube.thumbnail_start .thumbBlock_holder{width:40%;_width:40%;}.thumbnails-a-6x1 .playerCube .video-uploader{font-size:10px;font-weight:normal;text-decoration:none;color:black;}.thumbnails-a-6x1 .video-uploader{font-size:10px;font-weight:normal;text-decoration:none;color:black;}.thumbnails-a-6x1 .trc_sponsored_overlay{background-color:black;}.thumbnails-a-6x1 .syndicatedItem .video-external-data{color:black;font-size:10px;font-weight:normal;text-decoration:none;}.thumbnails-a-6x1 .trc_rbox_header{font-family:inherit;font-size:8.0px;font-weight:bold;text-decoration:none;color:#212121;border-width:0;background:transparent;border-style:none;border-color:#D6D5D3;padding:0px 0px 6px 0px;line-height:1.2em;display:block;margin:0px 0px 10px 0px;position:relative;background-color:transparent;box-sizing:initial;height:auto;width:auto;_width:auto;}.thumbnails-a-6x1 .syndicatedItem .video-rating{color:black;font-size:10px;font-weight:normal;text-decoration:none;}.thumbnails-a-6x1 .videoCube.vertical{border-style:solid none none none;}.thumbnails-a-6x1 .trc_pager_unselected{color:#7d898f;}.thumbnails-a-6x1 .video-rating{font-size:10px;font-weight:normal;text-decoration:none;color:black;}.thumbnails-a-6x1 .video-published-date{font-size:10px;font-weight:normal;text-decoration:none;color:black;display:inherit;}.thumbnails-a-6x1 .syndicatedItem{background-color:transparent;}.thumbnails-a-6x1 .syndicatedItem .video-duration-detail{color:black;font-size:10px;font-weight:normal;text-decoration:none;}.thumbnails-a-6x1 .playerCube .videoCube.horizontal{border-style:none none none none;}.thumbnails-a-6x1 .videoCube.syndicatedItem .thumbnail-overlay{background-image:null;background-position:5% 5%;}.thumbnails-a-6x1 .videoCube.syndicatedItem.vertical{border-style:solid none none none;}.thumbnails-a-6x1 .sponsored{font-size:9px;font-weight:normal;text-decoration:none;color:#9C9A9C;}.thumbnails-a-6x1 .videoCube.syndicatedItem .thumbBlock{border-color:darkgray;border-width:0px;}.thumbnails-a-6x1 .videoCube.syndicatedItem .thumbBlock .static-text{text-align:left;background-color:black;display:none;color:white;font-size:10px;font-weight:normal;text-decoration:none;font-family:Arial, Helvetica, sans-serif;}.thumbnails-a-6x1 .videoCube.thumbnail_start.trc-split-label .trc-pre-label{width:30%;_width:30%;}.thumbnails-a-6x1 .video-category{font-size:10px;font-weight:normal;text-decoration:none;color:black;}.thumbnails-a-6x1 .thumbnail-emblem{background-position:5% 5%;width:35;_width:35;height:35;}.thumbnails-a-6x1 .tbl-vignette-background-screen{background-color:#fff;opacity:0.8;filter:alpha(opacity=80);}.thumbnails-a-6x1 .syndicatedItem .video-description{max-height:2.2em;*height:2.2em;color:black;font-family:Arial, Helvetica, sans-serif;font-size:14px;font-weight:normal;line-height:19.0px;text-decoration:none;}.thumbnails-a-6x1 .tbl-cta-style .cta-button:hover{color:inherit;border-color:#999990;}.thumbnails-a-6x1 .playerCube .video-published-date{font-size:10px;font-weight:normal;text-decoration:none;color:black;}.thumbnails-a-6x1 .videoCube:hover .thumbnail-overlay,.thumbnails-a-6x1  .videoCube_hover .thumbnail-overlay{background-image:null;}.thumbnails-a-6x1 .video-label-box.trc-pre-label{height:0px;}.thumbnails-a-6x1 .videoCube.thumbnail_start .trc-pre-label{width:60%;_width:60%;}.thumbnails-a-6x1 .syndicatedItem .video-title{max-height:72.0px;*height:72.0px;color:#212121;font-family:inherit;font-size:18.0px;line-height:24.0px;font-weight:bold;text-decoration:none;padding:0;}.thumbnails-a-6x1 .playerCube:hover .thumbnail-overlay,.thumbnails-a-6x1  .playerCube_hover .thumbnail-overlay{background-image:null;}.thumbnails-a-6x1 .videoCube.thumbnail_start.trc-split-label .trc-main-label{width:30%;_width:30%;}.thumbnails-a-6x1 .videoCube{width:auto;_width:auto;background-color:transparent;border-width:0px 0px 0px 0px;border-color:#E4E4E4;padding:0px 0px 0px 0px;height:auto;margin-left:0px;margin-top:0px;border-radius:0px;-moz-border-radius:0px;-webkit-border-radius:0px;border-style:SOLID;}.thumbnails-a-6x1 .sponsored-default .video-description{max-height:2.2em;*height:2.2em;}.thumbnails-a-6x1 .tbl-vignette-attribution{color:#6B6666;font-size:15px;}.thumbnails-a-6x1 .playerCube .video-description{font-family:Arial, Helvetica, sans-serif;font-size:10px;line-height:11px;font-weight:normal;text-decoration:none;max-height:2.2em;*height:2.2em;color:black;}.thumbnails-a-6x1 .playerCube .videoCube .video-label-box{margin-left:81px;margin-right:0px;}.thumbnails-a-6x1 .videoCube.syndicatedItem .thumbBlock .branding{text-align:left;background-color:transparent;display:none;left:0px;color:black;font-size:10px;font-weight:normal;text-decoration:none;font-family:Arial, Helvetica, sans-serif;background-image:null;}.thumbnails-a-6x1 div.videoCube:hover,.thumbnails-a-6x1  div.videoCube_hover{background-color:transparent;}.thumbnails-a-6x1 .videoCube .story-widget.story-widget-text-under .tbl-ui-line{background-color:#333333;}.thumbnails-a-6x1 .videoCube .sponsored{margin-top:-7px;}.thumbnails-a-6x1 .trc_pager_pages div{font-size:12px;font-weight:normal;text-decoration:none;}.thumbnails-a-6x1 .sponsored-url{font-size:9px;font-weight:bold;text-decoration:underline;color:green;}.thumbnails-a-6x1 .playerCube .video-title{font-family:Arial, Helvetica, sans-serif;text-decoration:none;font-size:14px;line-height:17.5px;font-weight:bold;max-height:2.58em;*height:2.58em;color:black;}.thumbnails-a-6x1 .trc_rbox_header_icon_img{margin:0px;height:18px;}.thumbnails-a-6x1 .tbl-recommendation-reel .tbl-text-under-title-background{background-color:#EBEBEB;}.thumbnails-a-6x1 .tbl-recommendation-reel .tbl-ui-line{background-color:#333333;}.thumbnails-a-6x1 .videoCube.syndicatedItem.horizontal{border-style:none;}.thumbnails-a-6x1 .videoCube .thumbBlock .static-text{font-weight:normal;font-family:Arial, Helvetica, sans-serif;text-decoration:none;font-size:11px;background-color:#a30202;display:block;color:#ffffff;text-align:left;}.thumbnails-a-6x1 .video-title{font-family:Arial, Helvetica, sans-serif;font-size:18.0px;line-height:24.0px;font-weight:bold;max-height:72.0px;*height:72.0px;color:#212121;text-decoration:none;margin:0 0 0 0;}.thumbnails-a-6x1 .video-label,.thumbnails-a-6x1 .sponsored,.thumbnails-a-6x1 .sponsored-url{font-family:inherit;}.thumbnails-a-6x1 .playerCube .video-rating{font-size:10px;font-weight:normal;text-decoration:none;color:black;}.thumbnails-a-6x1 .syndicatedItem .branding{color:#999999;font-size:11.0px;font-weight:bold;text-decoration:none;font-family:inherit;background-image:null;text-align:left;line-height:24.0px;}.thumbnails-a-6x1 .trc_pager_selected{color:#0056b3;}.thumbnails-a-6x1 .videoCube.syndicatedItem{background-color:transparent;border-color:#E4E4E4;border-radius:0px;-moz-border-radius:0px;-webkit-border-radius:0px;border-width:0px 0px 0px 0px;border-style:SOLID;}.thumbnails-a-6x1 .branding div.logoDiv{font-family:inherit;}.thumbnails-a-6x1 .trc_rbox_div{width:auto;_width:99%;height:410px;border-width:0;padding:0;}.thumbnails-a-6x1 .playerCube .video-views{font-size:10px;font-weight:normal;text-decoration:none;color:black;}.thumbnails-a-6x1 .trc_pager div{font-family:serif;}.thumbnails-a-6x1 .syndicatedItem .video-label-box.trc-pre-label{height:0px;}.thumbnails-a-6x1 recommendationReel{min-adx-line-color:#2abfd5;min-adx-progress-color:#FFF;}.thumbnails-a-6x1 .videoCube.horizontal{border-style:none;}.thumbnails-a-6x1 div.trc_pager_pages div:hover{color:#6497ED;}.thumbnails-a-6x1 .pager_enabled{color:#0056b3;}.thumbnails-a-6x1 .playerCube .thumbnail-overlay{background-image:null;background-position:5% 5%;}.thumbnails-a-6x1 .videoCube .thumbnail-overlay{background-image:null;background-position:5% 5%;}.thumbnails-a-6x1 .playerCube .videoCube .video-duration{display:block;left:36px;}.thumbnails-a-6x1 .syndicatedItem .video-published-date{color:black;font-size:10px;font-weight:normal;text-decoration:none;display:inherit;}.thumbnails-a-6x1 .syndicatedItem .sponsored-url{color:green;font-size:9px;font-weight:bold;text-decoration:underline;}.thumbnails-a-6x1 .playerCube .videoCube .thumbBlock{border-width:0px;border-color:darkgray;}.thumbnails-a-6x1 .playerCube .video-label-box{text-align:left;}.thumbnails-a-6x1 div.sponsored-default:hover,.thumbnails-a-6x1  div.sponsored-default.videoCube_hover{background-color:inherit;}.thumbnails-a-6x1 .videoCube .story-widget.story-widget-text-under .tbl-text-under-title-background{background-color:#EBEBEB;}.thumbnails-a-6x1 .video-external-data{font-size:10px;font-weight:normal;text-decoration:none;color:black;}.thumbnails-a-6x1 .trc_pager_prev,.thumbnails-a-6x1 .trc_pager_next{font-size:12px;font-weight:normal;text-decoration:none;}.thumbnails-a-6x1 .videoCube .thumbBlock{border-width:0px;border-color:darkgray;}.thumbnails-a-6x1 .videoCube.syndicatedItem .video-duration{display:none;left:36px;}.thumbnails-a-6x1 .sponsored-default .video-title{max-height:2.58em;*height:2.58em;}.thumbnails-a-6x1 .branding{color:#999999;font-size:11.0px;font-weight:bold;text-decoration:none;font-family:inherit;background-image:null;text-align:left;line-height:24.0px;}.thumbnails-a-6x1 .sponsored-default{background-color:#F7F6C6;}.thumbnails-a-6x1 .playerCube .videoCube{background-color:transparent;border-color:#D6D5D3;border-width:1px;border-radius:0px;-moz-border-radius:0px;-webkit-border-radius:0px;margin-left:0px;margin-top:0px;padding:3px;}.thumbnails-a-6x1 .branding .logoDiv a span{color:inherit;font-size:inherit;}.thumbnails-a-6x1 .video-label-box{text-align:left;height:96px;margin:5px 0px 0px 0px;}.thumbnails-a-6x1 .video-description{font-family:Arial, Helvetica, sans-serif;font-size:14px;line-height:19.0px;font-weight:normal;max-height:2.2em;*height:2.2em;color:black;text-decoration:none;}.thumbnails-a-6x1 .videoCube .video-duration{left:36px;display:none;}.thumbnails-a-6x1 div.syndicatedItem:hover .thumbBlock{border-color:inherit;}.thumbnails-a-6x1 .trc_pager_counter{color:#000000;}.thumbnails-a-6x1 .whatsThisSyndicated{font-family:Arial, Verdana, sans-serif;font-size:9px;font-weight:normal;color:black;text-decoration:none;padding:0;}.thumbnails-a-6x1 .playerCube .video-duration-detail{font-size:10px;font-weight:normal;text-decoration:none;color:black;}.thumbnails-a-6x1 .video-duration-detail{font-size:10px;font-weight:normal;text-decoration:none;color:black;}.thumbnails-a-6x1 div.videoCube:hover .thumbBlock{border-color:inherit;}.thumbnails-a-6x1 .video-icon-img{margin:0px;height:18px;}.thumbnails-a-6x1 .video-views{font-size:10px;font-weight:normal;text-decoration:none;color:black;}.thumbnails-a-6x1 .tbl-cta-style .cta-button{font-family:Helvetica, Arial, sans-serif;background-color:transparent;border-color:#999990;color:#333333;}.thumbnails-a-6x1 .videoCube .video-label-box{margin-left:0;margin-right:0px;}.thumbnails-a-6x1 .videoCube .video-label-box.trc-pre-label{margin:0px 0px 5px 0px;}.thumbnails-a-6x1 .syndicatedItem .video-label-box{height:96px;margin:5px 0px 0px 0px;}.thumbnails-a-6x1 .logoDiv a span{font-size:18.0px;color:#212121;display:inline;font-weight:normal;}.thumbnails-a-6x1 .videoCube .video-label-box .video-title{text-decoration:none;}.thumbnails-a-6x1 .videoCube:hover .video-label-box .video-title{text-decoration:underline;}.thumbnails-a-6x1 .videoCube:hover .video-label-box .video-description{text-decoration:underline;}.thumbnails-a-6x1 .video-label-box .branding{display:block;}.thumbnails-a-6x1 .trc_header_left_column{width:48%;_width:48%;display:inline-block;height:auto;background-color:transparent;}.thumbnails-a-6x1 .trc_rbox_header .trc_header_ext{position:relative;top:auto;right:auto;}.thumbnails-a-6x1 .logoDiv a{font-size:100%;}.thumbnails-a-6x1 .videoCube a{padding:0;}.thumbnails-a-6x1 .trc_rbox_header .logoDiv{line-height:normal;}.thumbnails-a-6x1 .trc_header_right_part{margin:0px 0px 0px 0px;}/* s-split-thumbnails-a-6x1 */.thumbnails-a-6x1 .trc_header_right_column {	background: transparent;	height: auto;}body.dark .thumbnails-a-6x1 .logoDiv a span {    color: #BEBEBE;}body.dark .thumbnails-a-6x1 .syndicatedItem .video-title {    color: #E7E6EC;}/* e-split-thumbnails-a-6x1 */@media screen and (min-width:0px) and (max-width:480px) {            .trc_elastic_thumbnails-a-6x1 .trc_rbox_outer .videoCube .video-label-box {height:auto;}.trc_elastic_thumbnails-a-6x1 .trc_rbox_outer .videoCube {margin-bottom:10px;}.trc_elastic_thumbnails-a-6x1 .trc_rbox_outer{margin-left:-2%;}.trc_elastic_thumbnails-a-6x1 .videoCube_aspect{padding-bottom:83.33333333333334%; width: 100%;}.trc_elastic_thumbnails-a-6x1 .videoCube{width: 97.99%; position: relative; float: left; margin: 0 0 2% 0; margin-left: 2%;}.trc_elastic_thumbnails-a-6x1 div.videoCube:nth-of-type(-n+6){display:block;visibility:visible;}.trc_elastic_thumbnails-a-6x1 div.videoCube:nth-of-type(n+7){display:none;visibility:hidden;}         }@media screen and (min-width:481px) {            .trc_elastic_thumbnails-a-6x1 .trc_rbox_outer .videoCube .trc-main-label {height:auto;}.trc_elastic_thumbnails-a-6x1 .trc_rbox_outer .videoCube {margin-bottom:10px;}.trc_elastic_thumbnails-a-6x1 .trc_rbox_outer{margin-left:-2%;}.trc_elastic_thumbnails-a-6x1 .videoCube_aspect{padding-bottom:83.33333333333334%; width: 100%;}.trc_elastic_thumbnails-a-6x1 .videoCube{width: 14.665%; position: relative; float: left; margin: 0 0 2% 0; margin-left: 2%;}.trc_elastic_thumbnails-a-6x1 div.videoCube:nth-of-type(-n+6){display:block;visibility:visible;}.trc_elastic_thumbnails-a-6x1 div.videoCube:nth-of-type(n+7){display:none;visibility:hidden;}         }</style>
    <style id="tblCallToAction"> .videoCube a.tbl-text-over-container span.video-label-box.video-label-box-cta{position:inherit}.videoCube a.video-cta-style{width:100%}.videoCube a.video-cta-style .video-label-box.video-label-box-cta{display:flex;flex-wrap:wrap;position:relative;align-content:flex-start;align-items:center}.videoCube a.video-cta-style .video-label-box.video-label-box-cta.video-label-box-cta-non-ie{justify-content:space-between}.videoCube a.video-cta-style .video-label-flex-cta-item{flex-basis:100%}.videoCube a.video-cta-style .video-branding-flex-cta-item{margin-top:1px;flex-grow:1;flex-basis:1px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.videoCube .video-cta-href{display:inline-block;margin-top:1px;margin-inline-start:2px;padding:0;max-width:100%;min-width:72px;min-width:min(100%,72px);bottom:0;vertical-align:top;position:relative;box-sizing:content-box;flex-shrink:0;font-size:10px;line-height:13.2px}.videoCube .video-cta-href button.video-cta-button{margin:0;height:24px;font-size:10px;line-height:13.2px;border-radius:4px;border:1px solid #000;background-color:inherit;text-transform:none;letter-spacing:normal;box-sizing:border-box;cursor:pointer;outline:none;font-family:Helvetica;padding:1px 4px;width:100%;min-width:1px;opacity:1;box-shadow:none;user-select:none;transition:none;text-decoration:none;transform:none;font-weight:bold}.videoCube .video-cta-href button.video-cta-button:hover{color:inherit;border-color:inherit;background-color:inherit;letter-spacing:normal}</style>
    <script src="https://a.teads.tv/static/master/media/format/v3/teads-format.min.js" async=""></script><script src="//assets.bounceexchange.com/assets/smart-tag/versioned/onsite-v2_abbdf7a49be9b52b097917b7b527b262.br.js" async="" type="text/javascript"></script>
    <meta http-equiv="origin-trial" content="A/ZN3JeVl863wk4gji5LwmyqD8tQETuBB/T7ruSp8OvPp/kIaJGhw4I8mpB3u4vvQoSH2zniTHlhvlBBOA1ZbAkAAAB+eyJvcmlnaW4iOiJodHRwczovL3RlYWRzLnR2OjQ0MyIsImZlYXR1cmUiOiJQcml2YWN5U2FuZGJveEFkc0FQSXMiLCJleHBpcnkiOjE2OTUxNjc5OTksImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9">
    <script src="https://api.bounceexchange.com/bounce/init1.js?wklzs=906&amp;wklz=C4ewVgigvAZgrgOwMbAJYgQMhQZygRgHYAmADn3wE4AWQw-AZn02AC8QoBafYgBkwDuAUwBGOVMCEB9VABMoZXgDZMAJyE4QAGzhoMBSnwAeDXpTVCYQ1etVRsAQy1bUCAOZS4qrVAAWwYAAHHABSBgBBEOIAMSjoh1UcSSRfBFQkBwA6JBAAWzirB2AvDTi+IjjlOIBPEF04ESFOFId3IVlOXOrOFytOB05Ah1RVThAYMdVUN1cnTgA3OSEQJIScQYxZa36e1D6XefaxhBcEITjMRfFgKRyQAGtUISgQwgAhKOItQM+wyOJiP4gqFiABWCJRUExSExNbJVLpLI5fJgmKFYrqEFQ6LlQgw6JVVHRWr1RrNXytNxHLq7PoDIYjMYTEBTGYIOaLLYrYBrDYILajAa9JoHI4YU7nIm-YgAYU+ql+EIB0rlAKERkV4SIZB4PEISlIpF4rwAItgQA8ni93p8cAJ5pqYE4cJLVcRZHJNZ9KIQGPrDEolNQDTQGKClMQVZ9OZrteQqLR6MR6PhSFE3Q7lRCbWrDqopFoQG4qbIZAhHc7zoQTabLmsANqc5ZSK6oERaIQAXVglbriXrQypUmA1UCXZ7WhdfZw9dyIFkThbqHE7fHTsnQmn9YSaCQHak7Nya978wbO-S+4ykjcLOq3fXU9P-bcTg7qmqB4cR-vJ4bL+c1gfleQg3u+P4bluhbFu0ZbgY+DZCHmBZFiWsETvB-ayDAgRSKo7YgEg9zdggIA3OoDiyNUW5IAgUhCLkwxaFIOANDgSBTI0qhwZuT4zixIi5BIkilvRjFSCyIgSNxW5CBkOEwCMMGDohTwCNJdyPNIoAgAWCRUuhm4iIEUD8EIQRQPWnaYIEwB4OauSBC4rRINIMBaA4bj2PMFJQEAA" type="text/javascript" async="async"></script><script src="https://api.bounceexchange.com/bounce/reloadCampaigns.js?wklzs=1225&amp;wklz=C4ewVgigvAZgrgOwMbAJYgQMhQZygRgHYAmADn3wE4AWQy-YyzYALxCgFoGAGTAdwCmAIxypgAgPqoAJlDLcAbJgBOAnCAA2cNBgKVi3AB4Bmbk1UwBy1cqjYAhho2oEAcwlxlGqAAtgwAAccAFJjAEFg4gAxSKj7ZRxxJB8EVCR7ADokEABbWMt7YE81WIMiWMVYgE8QbTghAQ5k+zcBaQ4cqo5nSw57DgD7VGUOEBhR5VRXF0cOADcZARBE+JwBjGkrPu7UXuc5ttGEZwQBWMwF0WAJbJAAa1QBKGDCACFI4g0Aj9CI4mI-IEQsQAKzhSIg6IQ6KrJIpNKZbJ5UHRApFVTAyFRMqEaFRSooqI1OoNJo+FquQ6dHa9fqDYajcYgSbTBCzBabZbAVbrBCbEb9HqNfaHDAnM6En7EADCH2UP3B-ylsv+AkMCrCRDIDAYhBIClxMo+qBwElcIAkODUogwGpgjitkRVxBNZoClut6AQdodZ0IABFsCB7o9nm8Pjg+HMfRpHUb-tIZBqPpRCMZCAp9AoFNQFKQaMYQQpiMqPhyNVryFRaGmyqRqE6y8nw6qDsoJBoQK5KdIpN6leF7bG-f7G-8YHNgBWSFWaHqx8R7CANdwF19k-9AUEIYqsXjYQJkql0llcni0cVMdEcXiCVjiUVSc1Wu1qULtvSRmMJlMZhp5osXI8gEGxbIKuzCqgBztGKLgSnuSrxouwIluEq5IXMATRgOmoLtIG7EPhiHOvKOFSiWSFIJO07atW85IaIRGoWEFHOq4SCkcxCggguyLMehzoaNROGVhQc64gGFyrAA2hySwSJcqBCBoAgALqwL6UkJNJgyUhIwBVAEakacOWk4NJOQgNIjgKSaSkqepQ5WmZ0nxGgSAqRIbI5MZTkCC5blpJ56TiOayhVI5mlzDJriOCp4VefYPmRaZ0XabFThWFUNyFAIYURSZzlpeZnbdm0fYpUVMkCG2HZdj2FWFf5xXSdIMDusoykgEgdzqQgIDXKo9jSFULlIAgEgCDkQwaJa9Q4BxSlWJVzUyTg9Q5GI4i9lNM0SMyQhiCtLmHvY7owMM5W6TVjx8MdtwPJIoAWho8SUk1mBCAEUC8AIgRQNJqmYAEwB4EGOQBM4LRIJIMCva4dhtQEgNQAo1b4KQhAgpQTBIyjkLEMW3AGKYFzkj9wMcgQKgaAZRlQEjEidZ2PU095Txpag9jKQIQA" type="text/javascript" async="async"></script><script src="https://api.bounceexchange.com/bounce/reloadCampaigns.js?wklzs=1220&amp;wklz=C4ewVgigvAZgrgOwMbAJYgQMhQZygRgHYAmADn3wE4AWUgBn2Ms2AC8QoBaRuzAdwCmAIxypgAgPqoAJlDJ0AbJgBOAnCAA2cNBgKVidAB4BmOs1UwBy1cqjYAhho2oEAcwlxlGqAAtgwAAccAFJjAEFg4gAxSKj7ZRxxJB8EVCR7ADokEABbWMt7YE81WIMiWMVYgE8QbTghAU5k+zcBaU4cqs5nS057TgD7VGVOEBhR5VRXF0dOADcZARBE+JwBjGkrPu7UXuc5ttGEZwQBWMwF0WAJbJAAa1QBKGDCACFI4g0Aj9CI4mI-IEQsQAKzhSIg6IQ6KrJIpNKZbJ5UHRApFVTAyFRMqEaFRSooqI1OoNJo+FquQ6dHa9fqDYajcYgSbTBCzBabZbAVbrBCbEb9HqNfaHDAnM6En7EADCH2UP3B-ylsv+AkMCrCRDIjEYhBIClxMo+qBwElcIAkODUogwGpgjitkRVxBNZoClut6AQdodZ0IABFsCB7o9nm8Pjg+HMfRpHUb-tIZBqPpRCMZCAp9AoFNQFKQaMYQQpiMqPhyNVryFRqHrjGVSNQnWXk+HVQdlBINCBXJTpFJvUrwvbY37-U3-jA5sAKyQqzQ9ePiPYQBq6Iuvsn-oCghDFVi8bCBMlUuksrk8Wjipjoji8QSscSiqTmq12tShdt6SMxhMpjMNPMixcjyAQbFsgq7MKqAHO0YouBK+5KvGS7AiW4RrshcwBNGg6aou0ibsQBFIc68q4VKJbIUgU4ztq1YLshojEWhYTGIurhIGRLEKCCi7IixGHOhoNG4ZWFDzriAYXKsADaHJLBIlyoEIGgCAAurAvrSQkMmDJSEjAFUATqZpI7aTgMk5CA0iOIpJrKapGnDla5kyfEaBIKpEhsjkJnOQIrnuWkXnpOI5rKFUTlaXMsmuI4qkRd59i+VFZkxTpcVOFYVQ3IUAjhZFpkuelFldj2bT9qlxWyQI7adt2vaVUVAUlTJ0gwO6ygqSASB3Bp8UgHwrlIAgEgCDkQwaJa9Q4JxylWFVLWyTg9Q5GI4h9uNk0SMyQhiItrlHvY7owMMFV6bVjx8AdtwPJIoAWho8SUs1mBCAEUC8AIgRQDJamYAEwB4EGOQBM4LRIJIMBPa4djtQEf1QAo1b4KQhAgpQzDw4jkLEMWdAGKYFzkp9AMcgQKgaIZxlQPDEhdV2vWUz5Tzpag9gqQIQA" type="text/javascript" async="async"></script>
  </head>
  <body class="post-template-default single single-post postid-1113493 single-format-standard wp-embed-responsive youtube-changed-my-life-a-pair-of-original-videostars-ponder-a-life-lived-online bg-gray-100 text-gray-700 dark:text-gray-250 dark:bg-gray-50 singular">
    <!-- Google Tag Manager (noscript) -->
    <div id="cns-ads-slot-type--out-of-page-0" class="cns-ads-stage cns-ads-slot-type--out-of-page cns-ads-slot-type--out-of-page-0 cns-ads-slot-type-out-of-page cns-ads-slot-state-filled cns-ads-slot-size-1x1" data-name="_out_of_page_0" data-slot-type="_out_of_page" style="font-size: 0px; line-height: 0; overflow: hidden;">
      <div class="cns-ads-flex-sizer"></div>
      <div id="_out_of_page_0" class="cns-ads-container" style="margin: 0px auto; box-sizing: content-box;" data-google-query-id="CMyQ7tvy9ogDFbIkBgAdupgisg">
        <div id="google_ads_iframe_3379/conde.ars/interstitial/misc/article/1_0__container__" style="border: 0pt none; margin: auto; text-align: center;"><iframe id="google_ads_iframe_3379/conde.ars/interstitial/misc/article/1_0" name="google_ads_iframe_3379/conde.ars/interstitial/misc/article/1_0" title="3rd party ad content" width="1" height="1" scrolling="no" marginwidth="0" marginheight="0" frameborder="0" aria-label="Advertisement" tabindex="0" allow="private-state-token-redemption;attribution-reporting" data-load-complete="true" data-google-container-id="1" style="border: 0px; vertical-align: bottom;"></iframe></div>
      </div>
    </div>
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-NLXNPCQ" height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
    <!-- End Google Tag Manager (noscript) -->
    <div id="app">
      <a class="sr-only focus:not-sr-only" href="#main">
      Skip to content
      </a>
      <div class="ad-wrapper is-fullwidth is-hero">
        <div class="ad-wrapper-inner">
          <div class="ad ad--hero">
            <div id="cns-ads-slot-type-hero-0" class="cns-ads-stage cns-ads-slot-type-hero cns-ads-slot-type-hero-0 cns-ads-slot-state-filled cns-ads-slot-size-728x90" data-name="hero_0" data-slot-type="hero" style="font-size: 0px; line-height: 0; overflow: hidden;">
              <div class="cns-ads-flex-sizer"></div>
              <div id="hero_0" class="cns-ads-container" style="margin: 0px auto; box-sizing: content-box;" data-google-query-id="CO7Jy9zy9ogDFRViQQIdvCcawg">
                <div id="google_ads_iframe_3379/conde.ars/hero/misc/article/1_0__container__" style="border: 0pt none; margin: auto; text-align: center;"><iframe id="google_ads_iframe_3379/conde.ars/hero/misc/article/1_0" name="google_ads_iframe_3379/conde.ars/hero/misc/article/1_0" title="3rd party ad content" width="728" height="90" scrolling="no" marginwidth="0" marginheight="0" frameborder="0" aria-label="Advertisement" tabindex="0" allow="private-state-token-redemption;attribution-reporting" data-load-complete="true" style="border: 0px; vertical-align: bottom; width: 728px; height: 90px;" data-google-container-id="2"></iframe></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <header class="banner font-impact xxl:max-w-xxl relative top-0 z-30 mx-auto flex h-14 max-w-6xl flex-row flex-nowrap items-center justify-between rounded-sm bg-gray-700 px-4 font-semibold uppercase transition-[top] duration-500 dark:bg-black sm:sticky md:my-5 md:h-10 md:px-8 lg:my-10" id="site-header">
        <a id="header-logo" href="https://arstechnica.com/" aria-label="Ars Technica home">
          <span class="sr-only">Ars Technica home</span>
          <svg class="h-[36px] w-[109px] md:h-[65px] md:w-[197px]" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 436 144.1">
            <defs>
              <clipPath id="ars-full_svg__a">
                <path fill="none" d="M0 0h436v144.1H0z"></path>
              </clipPath>
              <clipPath id="ars-full_svg__b">
                <path fill="none" d="M0 0h436v144.1H0z"></path>
              </clipPath>
            </defs>
            <g clip-path="url(#ars-full_svg__a)">
              <g fill="none" clip-path="url(#ars-full_svg__b)">
                <path fill="#ff4e00" d="M72 0c39.8 0 72.1 32.3 72.1 72.1s-32.3 72.1-72.1 72.1S0 111.8 0 72.1 32.3 0 72 0"></path>
                <path fill="#fff" d="m46.5 94-.9-5.9c-4 4.4-9.6 6.8-15.6 6.8-8 0-13-4.8-13-12.3 0-11 9.4-15.4 27.8-17.3v-1.9c0-5.6-3.3-7.5-8.4-7.5s-10.5 1.7-15.3 3.8L20 52.6c5.3-2.1 10.3-3.7 17.1-3.7 10.7 0 15.9 4.3 15.9 14.2v30.8h-6.7Zm-1.6-22.4c-16.3 1.6-19.7 6-19.7 10.6s2.4 5.9 6.6 5.9 9.4-2.4 13.1-6.2zm27.3-3.7v26H64v-44h6.6l1.4 9c3.1-5 8.2-9.5 15.5-9.9l1.3 7.9c-7.4.3-13.6 5.2-16.6 11m37.2 26.9c-5.6-.1-11.1-1.6-16.1-4.2l1.2-7.8c4.6 3.2 10 5 15.6 5.1 5.6 0 9-2.1 9-5.8s-2.5-5.6-10.5-7.5C98.2 72 94.1 68.9 94.1 61s5.9-12.2 15.6-12.2c5 0 9.9 1 14.5 3l-1.3 7.8c-4.1-2.4-8.7-3.7-13.4-3.8-5 0-7.6 1.9-7.6 5.1s2.2 4.6 9.2 6.4c10.9 2.8 15.8 5.9 15.8 14.3s-6.1 13.2-17.5 13.2m109.4-11.1c-4.4 3.7-8.4 5-12.8 5-7.7 0-12.7-5.3-13.5-14h24.8l.9-5.5h-25.7c.8-8.7 5.7-14.1 12.9-14.1s8.8 1.7 12.9 5.1l1-5.9c-4-2.9-8.8-4.4-13.7-4.3-10.7 0-19.2 7.8-19.2 21.9s8.3 21.9 18.9 21.9c5.2.1 10.2-1.6 14.3-4.8zm-48.7-27.5v36.9h-5.8V56.2h-13.4v-5.3H183l.9 5.3H170Zm74.5 37.6c-11.9 0-19.5-8.8-19.5-21.8s7.8-22 19.6-22c4.3-.1 8.5 1.1 12 3.5l-.9 5.9c-3.2-2.6-7.1-4-11.2-4.1-8.6 0-13.6 6.5-13.6 16.6s5.1 16.6 13.6 16.6c4.3 0 8.5-1.6 11.9-4.2l.9 5.4c-3.7 2.6-8.2 4.1-12.8 4.1M292 93V73.5h-21.4V93h-5.8V50.9h5.8v17.5H292V50.9h5.8V93zm42.9 0-23.2-32.8V93h-5.3V50.9h5.1l22.4 31.5V50.9h5.3V93zm13.4-42.1h5.8V93h-5.8zm32.6 42.9c-11.9 0-19.5-8.8-19.5-21.8s7.8-22 19.6-22c4.3-.1 8.5 1.1 12 3.5l-.9 5.9c-3.2-2.6-7.1-4-11.2-4.1-8.6 0-13.6 6.5-13.6 16.6s5.1 16.6 13.6 16.6c4.3 0 8.5-1.6 11.9-4.2l.9 5.4c-3.7 2.6-8.2 4.1-12.8 4.1m32.9-43.1h5.8l16.3 41.5-5.6 1.2-5-13.1h-17.4L403.1 93h-5.8zm-4 24.6h13.5l-6.8-17.9z"></path>
              </g>
            </g>
          </svg>
        </a>
        <div class="flex flex-row flex-nowrap items-center gap-3 md:gap-5 xl:gap-4">
          <div class="inline-flex xxl:hidden" 0="items-center">
            <div x-data="{
              open: false,
              toggle() {
              if (this.open) {
              return this.close()
              }
              // If we're inside main header, add a data attribute to the header
              if (this.$el.closest('#site-header')) {
              this.$el.closest('#site-header').dataset.dropdownOpen = 'true';
              }
              this.open = true
              },
              close() {
              if (!this.open) {
              return;
              }
              // If we're inside main header, add a data attribute to the header
              if (this.$el.closest('#site-header')) {
              this.$el.closest('#site-header').dataset.dropdownOpen = 'false';
              }
              this.open = false
              }
              }" @keydown.escape.prevent.stop="close($refs.button)" @focusin.window="! $refs.panel.contains($event.target) &amp;&amp; close()" x-id="['dropdown-button']">
              <!-- Button -->
              <button type="button" x-ref="button" x-on:click="
                toggle();
                $dispatch('dropdown-opened', { panel: $refs.panel });
                " :aria-expanded="open" :aria-controls="$id('dropdown-button')" :class="{ selected: open }" class="group flex items-center focus:outline-none" arial-label="" aria-label="Open Sections menu dropdown" aria-expanded="false" aria-controls="dropdown-button-1">
                <svg class="group-with-selected:text-gray-200 h-5 w-5 text-gray-300 hover:text-gray-100 group-focus:text-gray-100 sm:hidden" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40">
                  <path fill="currentColor" d="M0 0h40v8H0zm0 16h40v8H0zm24 24H0v-8h16z"></path>
                  <path fill="#04cc74" d="M23 32h17l-8 8h-.3z"></path>
                </svg>
                <span class="group-with-selected:text-gray-100 hidden flex-row flex-nowrap items-center gap-1 uppercase text-gray-300 hover:text-gray-100 group-focus:text-gray-100 sm:flex">
                  Sections
                  <svg class="h-1 text-gray-300" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 19.3">
                    <defs>
                      <clipPath id="arrow-down_svg__a">
                        <path fill="none" d="M0 0h40v19.3H0z"></path>
                      </clipPath>
                      <clipPath id="arrow-down_svg__b">
                        <path fill="none" d="M0 0h40v19.3H0z"></path>
                      </clipPath>
                    </defs>
                    <g clip-path="url(#arrow-down_svg__a)">
                      <g fill="none" clip-path="url(#arrow-down_svg__b)">
                        <path fill="currentColor" d="m0 0 18.9 18.9c.6.6 1.6.6 2.2 0L40 0z"></path>
                      </g>
                    </g>
                  </svg>
                </span>
              </button>
              <!-- Panel -->
              <div x-ref="panel" x-show="open" x-transition.origin.top.center="" x-on:click.outside="close()" :id="$id('dropdown-button')" class="absolute overflow-hidden z-50 bg-gray-550 xxs:max-w-[400px] absolute right-0 top-14 mt-[1px] w-full rounded-sm sm:right-auto sm:max-w-[200px] md:top-10" id="dropdown-button-1" style="display: none;">
                <nav class="topnav-sections">
                  <div class="flex flex-row flex-nowrap items-center justify-between bg-gray-700 px-10 py-2 sm:hidden sm:flex-col sm:items-start">
                    <a class="text-green-400 hover:text-green-500 focus:text-green-500" href="/civis/">
                    Forum
                    </a>
                    <div class="h-5 w-[1px] bg-gray-400"></div>
                    <a class="text-orange-400 hover:text-orange-500 focus:text-orange-500" href="/store/product/subscriptions/">
                    Subscribe
                    </a>
                    <div class="h-5 w-[1px] bg-gray-400"></div>
                    <a class="flex flex-row flex-nowrap items-center gap-2 text-gray-300 hover:text-gray-100 focus:text-gray-100" href="/search/">
                      <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40">
                        <defs>
                          <clipPath id="magnify_svg__a">
                            <path fill="none" d="M0 0h40v40H0z"></path>
                          </clipPath>
                          <clipPath id="magnify_svg__b">
                            <path fill="none" d="M0 0h40v40H0z"></path>
                          </clipPath>
                        </defs>
                        <g clip-path="url(#magnify_svg__a)">
                          <g fill="none" clip-path="url(#magnify_svg__b)">
                            <path fill="currentColor" d="M39.2 35.4 29 25.2c4.4-6.2 3.9-15-1.7-20.6C24.2 1.6 20.1 0 16 0S7.8 1.6 4.7 4.7c-6.2 6.2-6.2 16.4 0 22.6C7.8 30.4 11.9 32 16 32s6.5-1 9.3-3l10.2 10.2c.5.5 1.2.8 1.9.8s1.4-.3 1.9-.8c1-1 1-2.7 0-3.8M8.5 23.5c-2-2-3.1-4.7-3.1-7.5s1.1-5.5 3.1-7.5 4.7-3.1 7.5-3.1 5.5 1.1 7.5 3.1c4.2 4.2 4.2 10.9 0 15.1-2 2-4.7 3.1-7.5 3.1s-5.5-1.1-7.5-3.1"></path>
                          </g>
                        </g>
                      </svg>
                    </a>
                  </div>
                  <ul class="my-3 grid grid-cols-2 sm:grid-cols-1">
                    <li>
                      <a class="group flex flex-row items-center px-5 py-2 text-gray-300 hover:bg-gray-700 hover:text-green-400 focus:bg-gray-700 focus:text-green-400" href="https://arstechnica.com/ai/">
                        <svg class="mr-2 inline-block h-5 w-5 text-gray-100 group-hover:text-green-400 group-focus:text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40">
                          <defs>
                            <clipPath id="section-ai_svg__a">
                              <path fill="none" d="M0 0h40v40H0z"></path>
                            </clipPath>
                            <clipPath id="section-ai_svg__b">
                              <path fill="none" d="M0 0h40v40H0z"></path>
                            </clipPath>
                          </defs>
                          <g clip-path="url(#section-ai_svg__a)">
                            <g fill="currentColor" clip-path="url(#section-ai_svg__b)">
                              <path d="M20 2.4c9.7 0 17.6 7.9 17.6 17.6S29.7 37.6 20 37.6 2.4 29.7 2.4 20 10.3 2.4 20 2.4M20 0C9 0 0 9 0 20s9 20 20 20 20-9 20-20S31 0 20 0"></path>
                              <path d="M20 13q2.85 0 5.4.9c.7.2 1.4-.1 1.6-.9l1.4-5.5C26 5.9 23.1 4.9 20 4.9s-6 .9-8.4 2.6L13 13c.2.7.9 1.1 1.6.9Q17 13 20 13M8.9 18.3c.4-.8 1-1.5 1.7-2.1l-2.2-5.7C7 12.2 6 14.1 5.5 16.3l1.3 2.1c.5.8 1.7.8 2.2 0m24.3 0 1.3-2.1c-.5-2.2-1.5-4.1-2.9-5.8l-2.2 5.7c.7.6 1.3 1.3 1.7 ******* 1.6.9 2.2 0M23.2 20c0 1.8-1.5 3.2-3.2 3.2s-3.2-1.4-3.2-3.2 1.5-3.2 3.2-3.2 3.2 1.4 3.2 3.2"></path>
                            </g>
                          </g>
                        </svg>
                        AI
                      </a>
                    </li>
                    <li>
                      <a class="group flex flex-row items-center px-5 py-2 text-gray-300 hover:bg-gray-700 hover:text-green-400 focus:bg-gray-700 focus:text-green-400" href="https://arstechnica.com/information-technology/">
                        <svg class="mr-2 inline-block h-5 w-5 text-gray-100 group-hover:text-green-400 group-focus:text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40">
                          <defs>
                            <clipPath id="section-information-technology_svg__a">
                              <path fill="none" d="M0 0h40v40H0z"></path>
                            </clipPath>
                            <clipPath id="section-information-technology_svg__b">
                              <path fill="none" d="M0 0h40v40H0z"></path>
                            </clipPath>
                          </defs>
                          <g clip-path="url(#section-information-technology_svg__a)">
                            <g fill="currentColor" clip-path="url(#section-information-technology_svg__b)">
                              <path d="M35 0H5C2.2 0 0 2.2 0 5s2.2 5 5 5h30c2.8 0 5-2.2 5-5s-2.2-5-5-5m-6.9 7c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2m6 0c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2m.9 8H5c-2.8 0-5 2.2-5 5s2.2 5 5 5h30c2.8 0 5-2.2 5-5s-2.2-5-5-5m-6.9 7.2c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2m6 0c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2M35 30H5c-2.8 0-5 2.2-5 5s2.2 5 5 5h30c2.8 0 5-2.2 5-5s-2.2-5-5-5m-6.9 7.4c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2m6 0c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2"></path>
                            </g>
                          </g>
                        </svg>
                        Biz &amp; IT
                      </a>
                    </li>
                    <li>
                      <a class="group flex flex-row items-center px-5 py-2 text-gray-300 hover:bg-gray-700 hover:text-green-400 focus:bg-gray-700 focus:text-green-400" href="https://arstechnica.com/cars/">
                        <svg class="mr-2 inline-block h-5 w-5 text-gray-100 group-hover:text-green-400 group-focus:text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40">
                          <defs>
                            <clipPath id="section-cars_svg__a">
                              <path fill="none" d="M0 0h40v40H0z"></path>
                            </clipPath>
                            <clipPath id="section-cars_svg__b">
                              <path fill="none" d="M0 0h40v40H0z"></path>
                            </clipPath>
                          </defs>
                          <g clip-path="url(#section-cars_svg__a)">
                            <g fill="none" clip-path="url(#section-cars_svg__b)">
                              <path fill="currentColor" d="M39.7 23.5c.2-1.2.3-2.3.3-3.5s-.1-2.4-.3-3.5l-1.3-.4c-.1-.6-.3-1.2-.5-1.8l.9-1c-.8-2.3-2-4.3-3.5-6.1l-1.3.3c-.4-.4-.8-.9-1.3-1.3l.3-1.3a20.6 20.6 0 0 0-6.1-3.5l-1 .9c-.6-.2-1.2-.3-1.8-.5L23.7.5C22.4.1 21.2 0 20 0s-2.4.1-3.5.3l-.4 1.3c-.6.1-1.2.3-1.8.5l-1-.9C11 2 9 3.2 7.2 4.7L7.5 6c-.4.4-.9.8-1.3 1.3L4.9 7a20.6 20.6 0 0 0-3.5 6.1l.9 1c-.2.6-.3 1.2-.5 1.8l-1.3.4C.1 17.6 0 18.8 0 20s.1 2.4.3 3.5l1.3.4c.1.6.3 1.2.5 1.8l-.9 1c.8 2.3 2 4.3 3.5 6.1l1.3-.3c.******* 1.3 1.3L7 35.1c1.8 1.5 3.9 2.7 6.1 3.5l1-.9c.6.2 1.2.3 1.8.5l.4 1.3c1.1.2 2.3.3 3.5.3s2.4-.1 3.5-.3l.4-1.3c.6-.1 1.2-.3 1.8-.5l1 .9c2.3-.8 4.3-2 6.1-3.5l-.3-1.3c.4-.4.9-.8 1.3-1.3l1.3.3c1.5-1.8 2.7-3.9 3.5-6.1l-.9-1c.2-.6.3-1.2.5-1.8l1.3-.4ZM25.9 8.2c1.3.6 2.4 1.5 3.4 2.5l-3.1 6.2-2.6.9c-.6-.9-1.5-1.6-2.6-1.9v-2.8zM22 19.9c0 1.1-.9 2-2 2s-2-.9-2-2 .9-2 2-2 2 .9 2 2M20 6.8q2.1 0 3.9.6L20 11.3l-3.9-3.9q1.8-.6 3.9-.6m-5.9 1.4 4.9 4.9v2.8c-1.1.3-2 .9-2.6 1.9l-2.6-.9-3.1-6.2c1-1 2.2-1.9 3.4-2.5m-4.8 4.2 2.5 4.9-4.9 2.5c0-2.7.9-5.3 2.4-7.4m.2 15.4 5.4-.9.9 5.4c-2.5-.9-4.7-2.5-6.3-4.5m5.7-2.9L8.4 26c-.6-1.2-1.1-2.6-1.3-4.1l6.2-3.1 2.6.9v.3c0 1 .4 2 1 2.7l-1.6 2.2Zm7 8c-.7.1-1.4.2-2.1.2s-1.4 0-2.1-.2l-1.1-6.8 1.6-2.2c.5.2 1 .3 1.6.3s1.1-.1 1.6-.3l1.6 2.2zm2.1-.5.9-5.4 5.4.9c-1.6 2.1-3.7 3.7-6.3 4.5m7.4-6.4-6.8-1.1-1.6-2.2c.6-.7 1-1.7 1-2.7v-.3l2.6-.9 6.2 3.1c-.2 1.4-.7 2.8-1.3 4.1m-3.4-8.7 2.5-4.9c1.5 2.1 2.4 4.6 2.4 7.4z"></path>
                            </g>
                          </g>
                        </svg>
                        Cars
                      </a>
                    </li>
                    <li>
                      <a class="group flex flex-row items-center px-5 py-2 text-gray-300 hover:bg-gray-700 hover:text-green-400 focus:bg-gray-700 focus:text-green-400" href="https://arstechnica.com/culture/">
                        <svg class="mr-2 inline-block h-5 w-5 text-gray-100 group-hover:text-green-400 group-focus:text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40">
                          <defs>
                            <clipPath id="section-culture_svg__a">
                              <path fill="none" d="M0 0h40v40H0z"></path>
                            </clipPath>
                            <clipPath id="section-culture_svg__b">
                              <path fill="none" d="M0 0h40v40H0z"></path>
                            </clipPath>
                          </defs>
                          <g clip-path="url(#section-culture_svg__a)">
                            <g fill="currentColor" clip-path="url(#section-culture_svg__b)">
                              <path d="M19 32v7.1c0 .5.4 1 1 1s1-.4 1-1V32zm2-24V1c0-.6-.5-1-1-1s-1 .4-1 1v7.1h2m-8.3 22.6L9.6 36c-.3.5-.1 1 .3 ******* 1 .1 1.3-.3l3.3-5.7c-.5-.1-1-.3-1.5-.4-.1 0-.3 0-.4-.1M27.3 9.3 30.4 4c.3-.5.1-1-.3-1.3-.5-.3-1-.1-1.3.3l-3.3 5.7c.5.1 1 .2 ******* 0 .3 0 .4.1m-21.8 18L3 28.7c-.5.3-.6.8-.3 1.3s.8.6 1.3.3l3.5-2-.9-.6-.9-.6m28.7-14.3 2.6-1.5c.5-.3.6-.8.3-1.3s-.8-.6-1.3-.3l-3.5 2c.******* 1 .5zm-9 18.5 3.3 5.7c.******* 1.3.3s.6-.8.3-1.3l-3.1-5.3c-.1 0-.3 0-.4.1-.5.2-1 .3-1.5.4M14.6 8.7 11.3 3c-.3-.5-.8-.6-1.3-.3s-.6.8-.3 1.3l3.1 5.3c.1 0 .3 0 .4-.1.5-.2 1-.3 1.5-.4m17.9 19.6 3.5 2c.5.3 1 .1 1.3-.3.3-.5.1-1-.3-1.3l-2.6-1.5-.9.6-.9.6M7.4 11.6l-3.5-2c-.5-.3-1-.1-1.3.3-.3.5-.1 1 .3 1.3l2.6 1.5.9-.6.9-.6m25.2 2.4c-.6-.4-1.3-.7-1.9-1.1-1.3-.7-2.7-1.3-4.3-1.8-.6-.2-1.3-.4-1.9-.5-1.1-.3-2.3-.4-3.4-.5h-2c-1.2 0-2.3.2-3.4.5-.6.1-1.3.3-1.9.5-1.5.5-2.9 1.1-4.3 1.8-.7.3-1.3.7-1.9 1.1C2.9 16.7 0 20 0 20s2.9 3.3 7.5 6.1c.6.4 1.3.7 1.9 1.1 1.3.7 2.7 1.3 4.3 1.8.6.2 1.3.4 1.9.5 1.1.3 2.3.4 3.4.5h2c1.2 0 2.3-.2 3.4-.5.6-.1 1.3-.3 1.9-.5 1.5-.5 2.9-1.1 4.3-1.8.7-.3 1.3-.7 1.9-1.1C37.1 23.3 40 20 40 20s-2.9-3.3-7.5-6.1M20 28c-4.4 0-8-3.6-8-8s3.6-8 8-8 8 3.6 8 8-3.6 8-8 8"></path>
                              <path d="M25 20c0 2.8-2.2 5-5 5s-5-2.2-5-5 2.2-5 5-5 5 2.2 5 5"></path>
                            </g>
                          </g>
                        </svg>
                        Culture
                      </a>
                    </li>
                    <li>
                      <a class="group flex flex-row items-center px-5 py-2 text-gray-300 hover:bg-gray-700 hover:text-green-400 focus:bg-gray-700 focus:text-green-400" href="https://arstechnica.com/gaming/">
                        <svg class="mr-2 inline-block h-5 w-5 text-gray-100 group-hover:text-green-400 group-focus:text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40">
                          <defs>
                            <clipPath id="section-gaming_svg__a">
                              <path fill="none" d="M0 0h40v40H0z"></path>
                            </clipPath>
                            <clipPath id="section-gaming_svg__b">
                              <path fill="none" d="M0 0h40v40H0z"></path>
                            </clipPath>
                          </defs>
                          <g clip-path="url(#section-gaming_svg__a)">
                            <g fill="none" clip-path="url(#section-gaming_svg__b)">
                              <path fill="currentColor" d="M30.7 39.7c-.7-1.1-1.7-1.8-2.5-2.8-.9-1.2 0-2 .8-3 .6-.9 1-1.9.8-3-.6-2.7-3.4-3.3-5.8-3.6-.7-.1-1.8-.2-2.3-.7s-.5-1.4-.5-2.1v-.4l15.5-3.6c2.3-.5 3.7-2.8 3.2-5.1l-2.8-12C36.6 1.1 34.3-.3 32 .2L3.3 6.8C1 7.4-.4 9.7.1 12l2.8 12c.5 2.3 2.8 3.7 5.1 3.2l11.1-2.6c0 1 .2 2.1.7 2.9 1.7 2.7 6 .8 7.6 3.3.8 1.2-.5 2.3-1.1 3.3-.6.9-.9 2-.4 3 .4 1.1 1.4 1.8 2.2 2.6 0 .*******.3h2.4c0-.1-.1-.2-.2-.3m.7-28.7c1.3-.3 2.7.5 3 1.9.3 1.3-.5 2.7-1.9 3-1.3.3-2.7-.5-3-1.9-.3-1.3.5-2.7 1.9-3m-6-3.7c1.3-.3 2.7.5 3 1.9.3 1.3-.5 2.7-1.9 3-1.3.3-2.7-.5-3-1.9-.3-1.3.5-2.7 1.9-3m-9.9 13.2-2.7.6-1-4.1-4.1 1-.6-2.7 4.1-1-1-4.1 2.7-.6 1 4.1 4.1-1 .6 2.7-4.1 1z"></path>
                            </g>
                          </g>
                        </svg>
                        Gaming
                      </a>
                    </li>
                    <li>
                      <a class="group flex flex-row items-center px-5 py-2 text-gray-300 hover:bg-gray-700 hover:text-green-400 focus:bg-gray-700 focus:text-green-400" href="https://arstechnica.com/health/">
                        <svg class="mr-2 inline-block h-5 w-5 text-gray-100 group-hover:text-green-400 group-focus:text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40">
                          <defs>
                            <clipPath id="section-health_svg__a">
                              <path fill="none" d="M0 0h40v40H0z"></path>
                            </clipPath>
                            <clipPath id="section-health_svg__b">
                              <path fill="none" d="M0 0h40v40H0z"></path>
                            </clipPath>
                          </defs>
                          <g clip-path="url(#section-health_svg__a)">
                            <g fill="currentColor" clip-path="url(#section-health_svg__b)">
                              <path d="M10.4 21.6c-.4-.4-1-.4-1.4 0l-3.9 3.9c-.4.4-.4 1 0 1.4s1 .4 1.4 0l3.9-3.9c.4-.4.4-1 0-1.4"></path>
                              <path d="M40 10.6c0-2.7-1-5.4-3.1-7.5C33.8 0 29.2-.8 25.4.8c-1.3.5-2.5 1.3-3.5 2.3L3.1 21.9c-4.2 4.2-4.2 10.9 0 15C5.2 39 7.9 40 10.6 40s5.4-1 7.5-3.1l18.7-18.7c2.1-2.1 3.1-4.8 3.1-7.5m-6.6-4c-.4-.4-.4-1 0-1.4s1-.4 1.4 0c3 3 3 7.8 0 10.8L26 24.8c-.4.4-1 .4-1.4 0s-.4-1 0-1.4l8.7-8.7c2.2-2.2 2.2-5.8 0-8M10.6 38.1c-2.3 0-4.5-.9-6.1-2.5-3.4-3.4-3.4-8.8 0-12.2l7.6-7.6c.6 2.1 2.3 4.9 4.8 7.4s5.2 4.2 7.4 4.8l-7.6 7.6c-1.6 1.6-3.8 2.5-6.1 2.5"></path>
                            </g>
                          </g>
                        </svg>
                        Health
                      </a>
                    </li>
                    <li>
                      <a class="group flex flex-row items-center px-5 py-2 text-gray-300 hover:bg-gray-700 hover:text-green-400 focus:bg-gray-700 focus:text-green-400" href="https://arstechnica.com/tech-policy/">
                        <svg class="mr-2 inline-block h-5 w-5 text-gray-100 group-hover:text-green-400 group-focus:text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40">
                          <defs>
                            <clipPath id="section-tech-policy_svg__a">
                              <path fill="none" d="M0 0h40v40H0z"></path>
                            </clipPath>
                            <clipPath id="section-tech-policy_svg__b">
                              <path fill="none" d="M0 0h40v40H0z"></path>
                            </clipPath>
                          </defs>
                          <g clip-path="url(#section-tech-policy_svg__a)">
                            <path fill="currentColor" d="M12.8 0 6.4 6.4 0 12.8l4 1.4L14.2 4z"></path>
                            <g clip-path="url(#section-tech-policy_svg__b)">
                              <path fill="currentColor" d="M34.8 31.7c-4.4-10.4-6.1-23.6-6.1-23.6L15.4 5.4l-9.9 10 2.7 13.3s13.2 1.6 23.6 6.1c-.4 1.4 0 2.9 1.1 4 1.4 1.4 3.6 1.6 5.2.6L18.5 19.8c-1.6 1-3.8.8-5.2-.6-1.6-1.6-1.6-4.3 0-5.9s4.3-1.6 5.9 0c1.4 1.4 1.6 3.6.6 5.2L39.3 38c1-1.6.8-3.8-.6-5.2-1.1-1.1-2.6-1.4-4-1.1"></path>
                            </g>
                          </g>
                        </svg>
                        Policy
                      </a>
                    </li>
                    <li>
                      <a class="group flex flex-row items-center px-5 py-2 text-gray-300 hover:bg-gray-700 hover:text-green-400 focus:bg-gray-700 focus:text-green-400" href="https://arstechnica.com/science/">
                        <svg class="mr-2 inline-block h-5 w-5 text-gray-100 group-hover:text-green-400 group-focus:text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40">
                          <defs>
                            <clipPath id="section-science_svg__a">
                              <path fill="none" d="M0 0h40v40H0z"></path>
                            </clipPath>
                            <clipPath id="section-science_svg__b">
                              <path fill="none" d="M0 0h40v40H0z"></path>
                            </clipPath>
                          </defs>
                          <g clip-path="url(#section-science_svg__a)">
                            <g fill="none" clip-path="url(#section-science_svg__b)">
                              <path fill="currentColor" d="M39.6 34.5 28 14.6V4h1.1c.5 0 .9-.4.9-.9V.9c0-.5-.4-.9-.9-.9H10.9c-.5 0-.9.4-.9.9V3c0 .*******.9H12v10.6L.4 34.5C-.9 37 .8 40 3.6 40h32.8c2.7 0 4.5-3 3.2-5.5M21.9 13.2c1.7 0 3 1.3 3 3s-1.3 3-3 3-3-1.3-3-3 1.3-3 3-3m-5-6c1.1 0 2 .9 2 2s-.9 2-2 2-2-.9-2-2 .9-2 2-2M4.1 36l6-10.3c.2-.3.5-.5.8-.5H13c-.1-.3-.2-.6-.2-1 0-1.7 1.3-3 3-3s3 1.3 3 3 0 .7-.2 1h4.2c0-1.1.9-2 2-2s2 .9 2 2h2.1c.3 0 .6.2.8.5l6 10.3H4.2Z"></path>
                            </g>
                          </g>
                        </svg>
                        Science
                      </a>
                    </li>
                    <li>
                      <a class="group flex flex-row items-center px-5 py-2 text-gray-300 hover:bg-gray-700 hover:text-green-400 focus:bg-gray-700 focus:text-green-400" href="https://arstechnica.com/security/">
                        <svg class="mr-2 inline-block h-5 w-5 text-gray-100 group-hover:text-green-400 group-focus:text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40">
                          <defs>
                            <clipPath id="section-security_svg__a">
                              <path fill="none" d="M0 0h40v40H0z"></path>
                            </clipPath>
                            <clipPath id="section-security_svg__b">
                              <path fill="none" d="M0 0h40v40H0z"></path>
                            </clipPath>
                          </defs>
                          <g clip-path="url(#section-security_svg__a)">
                            <g fill="none" clip-path="url(#section-security_svg__b)">
                              <path fill="currentColor" d="M37.7 21.1C39.7 10.4 32.8 0 20.8 0h-1.6C7.2 0 .3 10.4 2.3 21.1c.5 2.6-2.3 3.5-2.3 6.6 0 3.2 3.5 4 5.9 4.1h2.8c1.3 0 1.8.5 1.8 1.6 0 1.5.2 4.1.3 5.6 0 .2.7.4 1.9.5v-3.4c0-.4.3-.8.7-.8s.8.3.8.8v3.5c.9 0 1.8.1 2.9.1v-3.6c0-.4.3-.8.8-.8s.8.3.8.8v3.7h2.9v-3.7c0-.4.3-.8.8-.8s.8.3.8.8v3.6c1 0 2 0 2.9-.1v-3.5c0-.4.3-.8.8-.8s.8.3.8.8v3.4c1.1-.1 1.8-.3 1.9-.5.1-1.5.3-4.1.3-5.6 0-1.1.5-1.7 1.8-1.6h2.8c2.4-.1 5.9-.9 5.9-4.1 0-3.1-2.8-4-2.3-6.7m-26.7 4.7c-4 0-6.6-4-4.9-7.2 1.1-2 3.1-3.2 5.2-3.7 4.1-.9 7.6 2.9 6.7 6.6-.7 2.7-3.5 3.9-7 4.2m8.6 2.1-1 3c-.2.5-.7.8-1.1.6s-.7-.8-.5-1.3l.9-3c.2-.5.7-.8 1.1-.6s.7.8.5 1.3m2.8 3.6c-.4.2-.9 0-1.1-.6l-1-3c-.2-.5 0-1.1.5-1.3.4-.2.9 0 1.1.6l.9 3c.2.5 0 1.1-.5 1.3m6.6-5.7c-3.5-.4-6.3-1.5-7-4.2-.9-3.7 2.6-7.6 6.7-6.6 2.1.5 4.1 1.7 5.2 3.7 1.8 3.2-.9 7.2-4.9 7.2"></path>
                            </g>
                          </g>
                        </svg>
                        Security
                      </a>
                    </li>
                    <li>
                      <a class="group flex flex-row items-center px-5 py-2 text-gray-300 hover:bg-gray-700 hover:text-green-400 focus:bg-gray-700 focus:text-green-400" href="https://arstechnica.com/space/">
                        <svg class="mr-2 inline-block h-5 w-5 text-gray-100 group-hover:text-green-400 group-focus:text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40">
                          <defs>
                            <clipPath id="section-space_svg__a">
                              <path fill="none" d="M0 0h40v40H0z"></path>
                            </clipPath>
                            <clipPath id="section-space_svg__b">
                              <path fill="none" d="M0 0h40v40H0z"></path>
                            </clipPath>
                          </defs>
                          <g clip-path="url(#section-space_svg__a)">
                            <g fill="currentColor" clip-path="url(#section-space_svg__b)">
                              <path d="M32.9 13.1c-2.5-4.7-7.5-7.8-13.2-7.8-8.3 0-15 6.7-15 15s3.1 10.6 7.7 13.1c3.1-2.5 6.9-5.8 11-10 3.9-3.9 7-7.4 9.4-10.3M14.4 34.3c1.6.6 3.4 1 5.2 1 8.3 0 15-6.7 15-15s-.3-3.5-.9-5.2c-2.5 3-5.5 6.4-8.9 9.7-3.6 3.6-7.2 6.9-10.4 9.5"></path>
                              <path d="M28.5 5.8c.6.4 1.2.8 1.7 1.2 3.5-2.7 6.1-4.2 7.6-4.8-.5 1.4-2.1 4.1-4.8 7.6-2.6 3.4-6.2 7.5-10.9 12.3s-9.6 8.9-13 11.5c-3.2 2.4-5.5 3.7-6.9 4.2.5-1.3 1.9-3.7 4.2-6.9-.4-.5-.8-1.1-1.2-1.7-4 5.4-6 9.4-4.9 10.5s5.1-.9 10.5-4.9c3.8-2.9 8.2-6.8 12.7-11.3s7.9-8.4 10.7-12c4.4-5.7 6.7-10 5.5-11.2s-5.5 1.1-11.2 5.5"></path>
                            </g>
                          </g>
                        </svg>
                        Space
                      </a>
                    </li>
                    <li>
                      <a class="group flex flex-row items-center px-5 py-2 text-gray-300 hover:bg-gray-700 hover:text-green-400 focus:bg-gray-700 focus:text-green-400" href="https://arstechnica.com/gadgets/">
                        <svg class="mr-2 inline-block h-5 w-5 text-gray-100 group-hover:text-green-400 group-focus:text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40">
                          <defs>
                            <clipPath id="section-gadgets_svg__a">
                              <path fill="none" d="M0 0h40v40H0z"></path>
                            </clipPath>
                            <clipPath id="section-gadgets_svg__b">
                              <path fill="none" d="M0 0h40v40H0z"></path>
                            </clipPath>
                          </defs>
                          <g clip-path="url(#section-gadgets_svg__a)">
                            <g fill="currentColor" clip-path="url(#section-gadgets_svg__b)">
                              <path d="M38 22c1.1 0 2-.9 2-2s-.9-2-2-2h-2v-6h2c1.1 0 2-.9 2-2s-.9-2-2-2h-2V4h-4V2c0-1.1-.9-2-2-2s-2 .9-2 2v2h-6V2c0-1.1-.9-2-2-2s-2 .9-2 2v2h-6V2c0-1.1-.9-2-2-2S8 .9 8 2v2H4v4H2c-1.1 0-2 .9-2 2s.9 2 2 2h2v6H2c-1.1 0-2 .9-2 2s.9 2 2 2h2v6H2c-1.1 0-2 .9-2 2s.9 2 2 2h2v4h4v2c0 1.1.9 2 2 2s2-.9 2-2v-2h6v2c0 1.1.9 2 2 2s2-.9 2-2v-2h6v2c0 1.1.9 2 2 2s2-.9 2-2v-2h4v-4h2c1.1 0 2-.9 2-2s-.9-2-2-2h-2v-6zm-6 10H8V8h24z"></path>
                              <path d="M24.7 17.3 20 12h-7.1c-.6 0-1 .4-1 1s.4 1 1 1h6.3l4.1 4.7L20 22h8v-8z"></path>
                              <path d="m15.2 22.7 4.7 5.3H27c.6 0 1-.4 1-1s-.4-1-1-1h-6.3l-4.1-4.7 3.3-3.3h-8v8z"></path>
                            </g>
                          </g>
                        </svg>
                        Tech
                      </a>
                    </li>
                  </ul>
                  <div class="mx-3 h-[1px] bg-gray-400"></div>
                  <ul class="my-3 grid grid-cols-2 sm:grid-cols-1">
                    <li>
                      <a class="group flex flex-row items-center px-5 py-2 text-gray-300 hover:bg-gray-700 hover:text-green-400 focus:bg-gray-700 focus:text-green-400" href="/features/">
                        <svg class="mr-2 inline-block h-5 w-5 text-gray-100 group-hover:text-green-400 group-focus:text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 37.8">
                          <defs>
                            <clipPath id="star_svg__a">
                              <path fill="none" d="M0 0h40v37.8H0z"></path>
                            </clipPath>
                          </defs>
                          <g fill="none" clip-path="url(#star_svg__a)">
                            <path fill="currentColor" d="m20 0-6.2 12.4-13.8 2L10 24 7.6 37.8 20 31.3l12.4 6.5L30 24l10-9.6-13.8-2z"></path>
                          </g>
                        </svg>
                        Feature
                      </a>
                    </li>
                    <li>
                      <a class="group flex flex-row items-center px-5 py-2 text-gray-300 hover:bg-gray-700 hover:text-green-400 focus:bg-gray-700 focus:text-green-400" href="/reviews/">
                        <svg class="mr-2 inline-block h-5 w-5 text-gray-100 group-hover:text-green-400 group-focus:text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40">
                          <defs>
                            <clipPath id="section-reviews_svg__a">
                              <path fill="none" d="M0 0h40v40H0z"></path>
                            </clipPath>
                            <clipPath id="section-reviews_svg__b">
                              <path fill="none" d="M0 0h40v40H0z"></path>
                            </clipPath>
                          </defs>
                          <g clip-path="url(#section-reviews_svg__a)">
                            <g fill="currentColor" clip-path="url(#section-reviews_svg__b)">
                              <path d="M19.3 9.4V16l4.7 4.7h6.6l4.7-4.7V9.4l-4.7-4.7H24zm10.8.5c1.6 1.6 1.6 4.1 0 5.7s-4.1 1.6-5.7 0-1.6-4.1 0-5.7 4.1-1.6 5.7 0"></path>
                              <path d="M31.4 22.7h-8.3l-5.9-5.9V8.5L25.9 0H12L6.9 5.1V19L0 25.9C0 33.7 6.3 40 14.1 40l6.9-6.9h13.9L40 28V14.1z"></path>
                            </g>
                          </g>
                        </svg>
                        Reviews
                      </a>
                    </li>
                    <li>
                      <a class="group flex flex-row items-center px-5 py-2 text-gray-300 hover:bg-gray-700 hover:text-green-400 focus:bg-gray-700 focus:text-green-400" href="/store/">
                        <svg class="mr-2 inline-block h-5 w-5 text-gray-100 group-hover:text-green-400 group-focus:text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40">
                          <defs>
                            <clipPath id="section-store_svg__a">
                              <path fill="none" d="M0 0h40v40H0z"></path>
                            </clipPath>
                            <clipPath id="section-store_svg__b">
                              <path fill="none" d="M0 0h40v40H0z"></path>
                            </clipPath>
                          </defs>
                          <g clip-path="url(#section-store_svg__a)">
                            <g fill="none" clip-path="url(#section-store_svg__b)">
                              <path fill="currentColor" d="M37.9 8.5h-9.4C28.5 3.8 24.7 0 20 0s-8.5 3.8-8.5 8.5H2.1L0 40h40zM20 2c3.6 0 6.5 2.9 6.5 6.5h-13C13.5 4.9 16.4 2 20 2m0 17c-4.7 0-8.5-3.8-8.5-8.5h2c0 3.6 2.9 6.5 6.5 6.5s6.5-2.9 6.5-6.5h2c0 4.7-3.8 8.5-8.5 8.5"></path>
                            </g>
                          </g>
                        </svg>
                        Store
                      </a>
                    </li>
                  </ul>
                </nav>
              </div>
            </div>
          </div>
          <ul class="xxl:flex hidden gap-4 text-sm">
            <li>
              <a class="text-gray-250 hover:text-green-400 focus:text-green-400" href="https://arstechnica.com/ai/">
              AI
              </a>
            </li>
            <li>
              <a class="text-gray-250 hover:text-green-400 focus:text-green-400" href="https://arstechnica.com/information-technology/">
              Biz &amp; IT
              </a>
            </li>
            <li>
              <a class="text-gray-250 hover:text-green-400 focus:text-green-400" href="https://arstechnica.com/cars/">
              Cars
              </a>
            </li>
            <li>
              <a class="text-gray-250 hover:text-green-400 focus:text-green-400" href="https://arstechnica.com/culture/">
              Culture
              </a>
            </li>
            <li>
              <a class="text-gray-250 hover:text-green-400 focus:text-green-400" href="https://arstechnica.com/gaming/">
              Gaming
              </a>
            </li>
            <li>
              <a class="text-gray-250 hover:text-green-400 focus:text-green-400" href="https://arstechnica.com/health/">
              Health
              </a>
            </li>
            <li>
              <a class="text-gray-250 hover:text-green-400 focus:text-green-400" href="https://arstechnica.com/tech-policy/">
              Policy
              </a>
            </li>
            <li>
              <a class="text-gray-250 hover:text-green-400 focus:text-green-400" href="https://arstechnica.com/science/">
              Science
              </a>
            </li>
            <li>
              <a class="text-gray-250 hover:text-green-400 focus:text-green-400" href="https://arstechnica.com/security/">
              Security
              </a>
            </li>
            <li>
              <a class="text-gray-250 hover:text-green-400 focus:text-green-400" href="https://arstechnica.com/space/">
              Space
              </a>
            </li>
            <li>
              <a class="text-gray-250 hover:text-green-400 focus:text-green-400" href="https://arstechnica.com/gadgets/">
              Tech
              </a>
            </li>
          </ul>
          <a class="hidden text-green-400 sm:block xl:text-sm" href="/civis/">
          Forum
          </a>
          <div class="h-5 w-[1px] bg-gray-400"></div>
          <a class="hidden text-orange-400 sm:block xl:text-sm" href="/store/product/subscriptions/">
          Subscribe
          </a>
          <div class="h-5 w-[1px] bg-gray-400"></div>
          <div class="inline-flex" 0="items-center">
            <div x-data="{
              open: false,
              toggle() {
              if (this.open) {
              return this.close()
              }
              // If we're inside main header, add a data attribute to the header
              if (this.$el.closest('#site-header')) {
              this.$el.closest('#site-header').dataset.dropdownOpen = 'true';
              }
              this.open = true
              },
              close() {
              if (!this.open) {
              return;
              }
              // If we're inside main header, add a data attribute to the header
              if (this.$el.closest('#site-header')) {
              this.$el.closest('#site-header').dataset.dropdownOpen = 'false';
              }
              this.open = false
              }
              }" @keydown.escape.prevent.stop="close($refs.button)" @focusin.window="! $refs.panel.contains($event.target) &amp;&amp; close()" x-id="['dropdown-button']">
              <!-- Button -->
              <button type="button" x-ref="button" x-on:click="
                toggle();
                $dispatch('dropdown-opened', { panel: $refs.panel });
                " :aria-expanded="open" :aria-controls="$id('dropdown-button')" :class="{ selected: open }" class="group flex items-center group" arial-label="" aria-label="Open Theme selection dropdown" aria-expanded="false" aria-controls="dropdown-button-2">
                <span class="sr-only">Theme</span>
                <span x-data="{ placeholder: true }">
                  <span class="inline-block h-5 w-5" x-show="placeholder" style="display: none;"></span>
                  <span x-show="darkMode" x-init="placeholder = false">
                    <svg class="h-5 w-5 text-yellow-100 group-hover:text-yellow-200" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 38.4 38.4">
                      <defs>
                        <clipPath id="theme-dark_svg__a">
                          <path fill="none" d="M0 0h38.4v38.4H0z"></path>
                        </clipPath>
                        <clipPath id="theme-dark_svg__b">
                          <path fill="none" d="M0 0h38.4v38.4H0z"></path>
                        </clipPath>
                      </defs>
                      <g clip-path="url(#theme-dark_svg__a)">
                        <g fill="currentColor" clip-path="url(#theme-dark_svg__b)">
                          <path d="M14.5 11.4c0-4.3 1.4-8.2 3.7-11.4C8.8 1.3 1.6 9.3 1.6 19.1s8.6 19.3 19.3 19.3 12.1-3.1 15.6-7.9c-.9.1-1.8.2-2.7.2-10.7 0-19.3-8.6-19.3-19.3m17.8-6.8v2.1c0 .8-.6 1.4-1.4 1.4s-1.4-.6-1.4-1.4V4.6c0-.8.6-1.4 1.4-1.4s1.4.6 1.4 1.4m0 6.8v2.1c0 .8-.6 1.4-1.4 1.4s-1.4-.6-1.4-1.4v-2.1c0-.8.6-1.4 1.4-1.4s1.4.6 1.4 1.4m-5.8-3.7h2.1c.8 0 1.4.6 1.4 1.4s-.6 1.4-1.4 1.4h-2.1c-.8 0-1.4-.6-1.4-1.4s.6-1.4 1.4-1.4m6.8 0h2.1c.8 0 1.4.6 1.4 1.4s-.6 1.4-1.4 1.4h-2.1c-.8 0-1.4-.6-1.4-1.4s.6-1.4 1.4-1.4"></path>
                        </g>
                      </g>
                    </svg>
                  </span>
                  <span x-show="!darkMode" x-init="placeholder = false" style="display: none;">
                    <svg class="h-5 w-5 text-yellow-400 group-hover:text-yellow-200" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40">
                      <defs>
                        <clipPath id="theme-light_svg__a">
                          <path fill="none" d="M0 0h40v40H0z"></path>
                        </clipPath>
                        <clipPath id="theme-light_svg__b">
                          <path fill="none" d="M0 0h40v40H0z"></path>
                        </clipPath>
                      </defs>
                      <g clip-path="url(#theme-light_svg__a)">
                        <g fill="currentColor" clip-path="url(#theme-light_svg__b)">
                          <path d="M30 20c0 5.5-4.5 10-10 10s-10-4.5-10-10 4.5-10 10-10 10 4.5 10 10m8.6 1.4h-2.2c-.8 0-1.4-.6-1.4-1.4s.6-1.4 1.4-1.4h2.2c.8 0 1.4.6 1.4 1.4s-.6 1.4-1.4 1.4M34.1 7.9l-1.5 1.5c-.6.6-1.5.6-2 0-.6-.6-.6-1.5 0-2l1.5-1.5c.6-.6 1.5-.6 2 0 .6.6.6 1.5 0 2M21.4 1.4v2.2c0 .8-.6 1.4-1.4 1.4s-1.4-.6-1.4-1.4V1.4c0-.8.6-1.4 1.4-1.4s1.4.6 1.4 1.4M7.9 5.9l1.5 1.5c.6.6.6 1.5 0 2-.6.6-1.5.6-2 0L5.9 7.9c-.6-.6-.6-1.5 0-2 .6-.6 1.5-.6 2 0M1.4 18.6h2.2c.8 0 1.4.6 1.4 1.4s-.6 1.4-1.4 1.4H1.4C.6 21.4 0 20.8 0 20s.6-1.4 1.4-1.4m4.5 13.5 1.5-1.5c.6-.6 1.4-.6 2 0s.6 1.5 0 2l-1.5 1.5c-.6.6-1.5.6-2 0-.6-.6-.6-1.5 0-2m12.7 6.5v-2.2c0-.8.6-1.4 1.4-1.4s1.4.6 1.4 1.4v2.2c0 .8-.6 1.4-1.4 1.4s-1.4-.6-1.4-1.4m13.5-4.5-1.5-1.5c-.6-.6-.6-1.4 0-2s1.5-.6 2 0l1.5 1.5c.6.6.6 1.5 0 2-.6.6-1.5.6-2 0"></path>
                        </g>
                      </g>
                    </svg>
                  </span>
                </span>
              </button>
              <!-- Panel -->
              <div x-ref="panel" x-show="open" x-transition.origin.top.center="" x-on:click.outside="close()" :id="$id('dropdown-button')" class="absolute overflow-hidden z-50 bg-gray-550 absolute right-0 top-14 mt-[1px] min-w-[200px] rounded-sm py-3 md:top-10" id="dropdown-button-2" style="display: none;">
                <form action="." method="post">
                  <nav>
                    <ul class="">
                      <li>
                        <button class=" group flex w-full flex-row items-center px-5 py-2 text-gray-300 hover:bg-gray-700 hover:text-green-400 focus:bg-gray-700 focus:text-green-400" name="theme" type="submit" value="light" aria-label="Set theme to Light">
                          <svg class="group-with-selected:text-green-400 mr-2 inline-block h-5 w-5 text-gray-100 group-hover:text-green-400 group-focus:text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40">
                            <defs>
                              <clipPath id="theme-light_svg__a">
                                <path fill="none" d="M0 0h40v40H0z"></path>
                              </clipPath>
                              <clipPath id="theme-light_svg__b">
                                <path fill="none" d="M0 0h40v40H0z"></path>
                              </clipPath>
                            </defs>
                            <g clip-path="url(#theme-light_svg__a)">
                              <g fill="currentColor" clip-path="url(#theme-light_svg__b)">
                                <path d="M30 20c0 5.5-4.5 10-10 10s-10-4.5-10-10 4.5-10 10-10 10 4.5 10 10m8.6 1.4h-2.2c-.8 0-1.4-.6-1.4-1.4s.6-1.4 1.4-1.4h2.2c.8 0 1.4.6 1.4 1.4s-.6 1.4-1.4 1.4M34.1 7.9l-1.5 1.5c-.6.6-1.5.6-2 0-.6-.6-.6-1.5 0-2l1.5-1.5c.6-.6 1.5-.6 2 0 .6.6.6 1.5 0 2M21.4 1.4v2.2c0 .8-.6 1.4-1.4 1.4s-1.4-.6-1.4-1.4V1.4c0-.8.6-1.4 1.4-1.4s1.4.6 1.4 1.4M7.9 5.9l1.5 1.5c.6.6.6 1.5 0 2-.6.6-1.5.6-2 0L5.9 7.9c-.6-.6-.6-1.5 0-2 .6-.6 1.5-.6 2 0M1.4 18.6h2.2c.8 0 1.4.6 1.4 1.4s-.6 1.4-1.4 1.4H1.4C.6 21.4 0 20.8 0 20s.6-1.4 1.4-1.4m4.5 13.5 1.5-1.5c.6-.6 1.4-.6 2 0s.6 1.5 0 2l-1.5 1.5c-.6.6-1.5.6-2 0-.6-.6-.6-1.5 0-2m12.7 6.5v-2.2c0-.8.6-1.4 1.4-1.4s1.4.6 1.4 1.4v2.2c0 .8-.6 1.4-1.4 1.4s-1.4-.6-1.4-1.4m13.5-4.5-1.5-1.5c-.6-.6-.6-1.4 0-2s1.5-.6 2 0l1.5 1.5c.6.6.6 1.5 0 2-.6.6-1.5.6-2 0"></path>
                              </g>
                            </g>
                          </svg>
                          Light
                        </button>
                      </li>
                      <li>
                        <button class=" group flex w-full flex-row items-center px-5 py-2 text-gray-300 hover:bg-gray-700 hover:text-green-400 focus:bg-gray-700 focus:text-green-400" name="theme" type="submit" value="dark" aria-label="Set theme to Dark">
                          <svg class="group-with-selected:text-green-400 mr-2 inline-block h-5 w-5 text-gray-100 group-hover:text-green-400 group-focus:text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 38.4 38.4">
                            <defs>
                              <clipPath id="theme-dark_svg__a">
                                <path fill="none" d="M0 0h38.4v38.4H0z"></path>
                              </clipPath>
                              <clipPath id="theme-dark_svg__b">
                                <path fill="none" d="M0 0h38.4v38.4H0z"></path>
                              </clipPath>
                            </defs>
                            <g clip-path="url(#theme-dark_svg__a)">
                              <g fill="currentColor" clip-path="url(#theme-dark_svg__b)">
                                <path d="M14.5 11.4c0-4.3 1.4-8.2 3.7-11.4C8.8 1.3 1.6 9.3 1.6 19.1s8.6 19.3 19.3 19.3 12.1-3.1 15.6-7.9c-.9.1-1.8.2-2.7.2-10.7 0-19.3-8.6-19.3-19.3m17.8-6.8v2.1c0 .8-.6 1.4-1.4 1.4s-1.4-.6-1.4-1.4V4.6c0-.8.6-1.4 1.4-1.4s1.4.6 1.4 1.4m0 6.8v2.1c0 .8-.6 1.4-1.4 1.4s-1.4-.6-1.4-1.4v-2.1c0-.8.6-1.4 1.4-1.4s1.4.6 1.4 1.4m-5.8-3.7h2.1c.8 0 1.4.6 1.4 1.4s-.6 1.4-1.4 1.4h-2.1c-.8 0-1.4-.6-1.4-1.4s.6-1.4 1.4-1.4m6.8 0h2.1c.8 0 1.4.6 1.4 1.4s-.6 1.4-1.4 1.4h-2.1c-.8 0-1.4-.6-1.4-1.4s.6-1.4 1.4-1.4"></path>
                              </g>
                            </g>
                          </svg>
                          Dark
                        </button>
                      </li>
                      <li>
                        <button class="selected bg-gray-700 text-green-400 group flex w-full flex-row items-center px-5 py-2 text-gray-300 hover:bg-gray-700 hover:text-green-400 focus:bg-gray-700 focus:text-green-400" name="theme" type="submit" value="system" aria-label="Set theme to System">
                          <svg class="group-with-selected:text-green-400 mr-2 inline-block h-5 w-5 text-gray-100 group-hover:text-green-400 group-focus:text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40">
                            <defs>
                              <clipPath id="theme-system_svg__a">
                                <path fill="none" d="M0 0h40v40H0z"></path>
                              </clipPath>
                              <clipPath id="theme-system_svg__b">
                                <path fill="none" d="M0 0h40v40H0z"></path>
                              </clipPath>
                            </defs>
                            <g clip-path="url(#theme-system_svg__a)">
                              <g fill="currentColor" clip-path="url(#theme-system_svg__b)">
                                <path d="M32 4c2.2 0 4 1.8 4 4v24c0 2.2-1.8 4-4 4H8c-2.2 0-4-1.8-4-4V8c0-2.2 1.8-4 4-4zm0-4H8C3.6 0 0 3.6 0 8v24c0 4.4 3.6 8 8 8h24c4.4 0 8-3.6 8-8V8c0-4.4-3.6-8-8-8"></path>
                                <path d="M8 8h8v8H8z"></path>
                              </g>
                            </g>
                          </svg>
                          System
                        </button>
                      </li>
                    </ul>
                  </nav>
                </form>
              </div>
            </div>
          </div>
          <div class="hidden md:flex md:justify-center" x-data="{
            open: false,
            show() {
            this.open = true;
            this.$dispatch('modal-opened', {
            panel: this.$refs.panel,
            });
            },
            hide() {
            this.open = false
            },
            }">
            <button type="button" aria-label="Search dialog..." class="flex flex-row items-center text-gray-300 hover:text-gray-100" x-on:click="show()">
              <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40">
                <defs>
                  <clipPath id="magnify_svg__a">
                    <path fill="none" d="M0 0h40v40H0z"></path>
                  </clipPath>
                  <clipPath id="magnify_svg__b">
                    <path fill="none" d="M0 0h40v40H0z"></path>
                  </clipPath>
                </defs>
                <g clip-path="url(#magnify_svg__a)">
                  <g fill="none" clip-path="url(#magnify_svg__b)">
                    <path fill="currentColor" d="M39.2 35.4 29 25.2c4.4-6.2 3.9-15-1.7-20.6C24.2 1.6 20.1 0 16 0S7.8 1.6 4.7 4.7c-6.2 6.2-6.2 16.4 0 22.6C7.8 30.4 11.9 32 16 32s6.5-1 9.3-3l10.2 10.2c.5.5 1.2.8 1.9.8s1.4-.3 1.9-.8c1-1 1-2.7 0-3.8M8.5 23.5c-2-2-3.1-4.7-3.1-7.5s1.1-5.5 3.1-7.5 4.7-3.1 7.5-3.1 5.5 1.1 7.5 3.1c4.2 4.2 4.2 10.9 0 15.1-2 2-4.7 3.1-7.5 3.1s-5.5-1.1-7.5-3.1"></path>
                  </g>
                </g>
              </svg>
            </button>
            <template x-teleport="body" data-teleport-template="true">
              <div class="fixed inset-0 z-50 overflow-y-auto" role="dialog" aria-modal="true" x-cloak="" x-show="open" x-on:keydown.escape.window.prevent.stop="open = false" x-id="['modal-title']" x-ref="panel" :aria-labelledby="$id('modal-title')">
                <div class="fixed inset-0 bg-slate-900/80 opacity-100 backdrop-blur" x-show="open" x-transition.duration.150ms="">
                </div>
                <div class="relative flex min-h-screen items-center justify-center" x-on:click="open = false" x-show="open" x-transition.duration.150ms="">
                  <div x-on:click.stop="" x-trap.noscroll.inert="open">
                    <span class="sr-only" :id="$id('modal-title')">
                    Search dialog...
                    </span>
                    <div class="relative min-w-[640px]">
                      <form class="search-form" role="search" method="get" action="https://arstechnica.com/">
                        <label>
                        <span class="sr-only">
                        Search for:
                        </span>
                        <input name="s" type="search" value="" placeholder="Search …">
                        </label>
                        <button aria-label="Search">Search</button>
                      </form>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </div>
          <div class="h-5 w-[1px] bg-gray-400"></div>
          <div class="flex md:justify-center" x-data="{
            open: false,
            show() {
            this.open = true;
            this.$dispatch('modal-opened', {
            panel: this.$refs.panel,
            });
            },
            hide() {
            this.open = false
            },
            }">
            <button type="button" aria-label="Sign in dialog..." class="whitespace-nowrap text-gray-300 hover:text-gray-100" x-on:click="show()">
            Sign In
            </button>
            <template x-teleport="body" data-teleport-template="true">
              <div class="fixed inset-0 z-50 overflow-y-auto" role="dialog" aria-modal="true" x-cloak="" x-show="open" x-on:keydown.escape.window.prevent.stop="open = false" x-id="['modal-title']" x-ref="panel" :aria-labelledby="$id('modal-title')">
                <div class="fixed inset-0 bg-slate-900/80 opacity-100 backdrop-blur" x-show="open" x-transition.duration.150ms="">
                </div>
                <div class="relative flex min-h-screen items-center justify-center" x-on:click="open = false" x-show="open" x-transition.duration.150ms="">
                  <div x-on:click.stop="" x-trap.noscroll.inert="open">
                    <span class="sr-only" :id="$id('modal-title')">
                    Sign in dialog...
                    </span>
                    <div class="sign-in-panel absolute left-1/2 top-1/2 w-3/4 min-w-[320px] max-w-xl -translate-x-1/2 -translate-y-1/2">
                      <header class="font-impact flex items-center justify-between bg-gray-600 px-7 py-4 font-semibold uppercase">
                        <div class="text-gray-350 flex items-center gap-3">
                          <svg class="h-3 w-3 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40">
                            <defs>
                              <clipPath id="arrow-blocks-right_svg__a">
                                <path fill="none" d="M0 0h40v40H0z"></path>
                              </clipPath>
                            </defs>
                            <g fill="currentColor" clip-path="url(#arrow-blocks-right_svg__a)">
                              <path d="M32 16h8v8h-8zm-8 8h8v8h-8zm-8 8h8v8h-8zm8-24h8v8h-8zm-8-8h8v8h-8zM0 16h16v8H0z"></path>
                            </g>
                          </svg>
                          Sign in
                        </div>
                        <button class="text-gray-300 hover:text-gray-100 focus:text-gray-100" x-on:click="open = false">
                          <svg class="h-3 w-3" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 34.7 40">
                            <defs>
                              <clipPath id="x_svg__a">
                                <path fill="none" d="M0 0h34.7v40H0z"></path>
                              </clipPath>
                            </defs>
                            <g fill="none" clip-path="url(#x_svg__a)">
                              <path fill="currentColor" d="m26.4 0-8.5 16.9h-1.1L8.3 0H.8l10.1 19.4L0 40h7.6l9.2-18.3h1.1L27.1 40h7.6L23.8 19.4 33.9 0z"></path>
                            </g>
                          </svg>
                        </button>
                      </header>
                      <div class="sign-in-panel-body bg-gray-700 px-7 py-4">
                        <div class="col-span-3 normal-case text-gray-300" x-data="{ html: '', form: '', triggered: false }" x-on:modal-opened.window="
                          panel = $el.parentElement.parentElement.parentElement.parentElement.parentElement;
                          if (triggered || panel !== event.detail.panel) {
                          return;
                          }
                          triggered = true;
                          html = await (await fetch('/civis/login')).text();
                          // Parse html for form with action=/civis/login/login
                          parser = new DOMParser();
                          doc = parser.parseFromString(html, 'text/html');
                          form = doc.querySelector('form[action=&quot;/civis/login/login&quot;]');
                          // Remove autofocus and set focus to username field
                          username = form.querySelector('input[name=&quot;login&quot;]');
                          username.removeAttribute('autofocus');
                          document.querySelector('.sign-in-form').appendChild(form);
                          username.focus();
                          ">
                          <div class="sign-in-form"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </div>
        </div>
      </header>
      <main class="main relative" id="main">
        <article class="h-entry post-1113493 post type-post status-publish format-standard has-post-thumbnail hentry category-features category-gaming" data-id="1113493">
          <header>
            <div class="my-4 bg-gray-700 md:my-10">
              <div class="mx-auto max-w-2xl p-4 md:px-8 md:py-10 lg:grid lg:max-w-6xl lg:grid-cols-2">
                <div>
                  <div class="mb-2">
                    <div class="upper-deck font-impact inline-flex flex-row flex-nowrap items-center gap-[0.6rem] text-left text-[0.85rem] font-semibold uppercase leading-tight text-green-400">
                      <span class="upper-deck__icon">
                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40">
                          <defs>
                            <clipPath id="section-misc_svg__a">
                              <path fill="none" d="M0 0h40v40H0z"></path>
                            </clipPath>
                            <clipPath id="section-misc_svg__b">
                              <path fill="none" d="M0 0h40v40H0z"></path>
                            </clipPath>
                          </defs>
                          <g fill="none" clip-path="url(#section-misc_svg__a)">
                            <path fill="currentColor" d="m8.67 25.657 16.971-16.97 5.657 5.656-16.97 16.97z"></path>
                            <path fill="currentColor" d="m8.67 14.343 5.657-5.657 16.971 16.97-5.657 5.658z"></path>
                            <g clip-path="url(#section-misc_svg__b)">
                              <path fill="currentColor" d="M36 0H4C1.8 0 0 1.8 0 4v32c0 2.2 1.8 4 4 4h32c2.2 0 4-1.8 4-4V4c0-2.2-1.8-4-4-4m0 34c0 1.1-.9 2-2 2H6c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2h28c1.1 0 2 .9 2 2z"></path>
                            </g>
                          </g>
                        </svg>
                      </span>
                      <span class="upper-deck__text">
                      Features
                      </span>
                    </div>
                  </div>
                  <h1 class="mb-3 font-serif text-4xl font-bold text-gray-100 md:text-6xl md:leading-[1.05]">
                    More than a decade later, how do original YouTube stars feel about the site?
                  </h1>
                  <p class="my-3 text-2xl leading-[1.1] text-gray-300 md:mt-7 md:leading-[1.2]">
                    For original YouTubers, their online haven became a media behemoth—but they keep vlogging.
                  </p>
                  <div class="my-3 md:my-5">
                    <div class="font-impact relative z-10 text-xs font-semibold uppercase text-gray-300">
                      <a class="text-orange-400 hover:text-orange-500" href="https://arstechnica.com/author/ars-staff/">
                      Chris Stokel-Walker
                      </a>
                      –
                      <time class="cursor-default" title="2017-06-11T10:00:33-04:00" datetime="2017-06-11T10:00:33-04:00" x-data="{
                        compact: false,
                        open: false,
                        date: new Date('2017-06-11T10:00:33-04:00'),
                        updatedTimestamp: false,
                        format: function() {
                        let dateFormat = {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric'
                        };
                        let timeFormat = {
                        hour: 'numeric',
                        minute: 'numeric'
                        };
                        let formatted =
                        this.date.toLocaleDateString(undefined, dateFormat) +
                        ' ' +
                        this.date.toLocaleTimeString(undefined, timeFormat);
                        if (this.compact) {
                        if (this.date.toDateString() === new Date().toDateString()) {
                        formatted = this.date.toLocaleTimeString(undefined, timeFormat);
                        if (this.updatedTimestamp) {
                        formatted = 'at ' + formatted;
                        }
                        } else {
                        formatted = this.date.toLocaleDateString(undefined, {
                        year: 'numeric',
                        month: 'numeric',
                        day: 'numeric'
                        });
                        }
                        }
                        if (this.updatedTimestamp) {
                        formatted = 'Updated ' + formatted;
                        }
                        return formatted;
                        }
                        }" x-text="format()">Jun 11, 2017 4:00 PM</time>
                    </div>
                  </div>
                </div>
                <div class="lg:pl-20">
                  <div class="relative aspect-square overflow-hidden">
                    <img width="1000" height="900" src="https://cdn.arstechnica.net/wp-content/uploads/2017/06/Screen-Shot-2017-06-08-at-1.32.44-PM-1000x900.png" class="intro-image absolute min-w-full min-h-full h-auto object-cover" alt="" loading="eager" decoding="async" fetchpriority="high">
                  </div>
                  <div class="caption mt-1 inline-flex flex-row items-stretch gap-1 text-lg leading-tight text-gray-300">
                    <div class="caption-icon bg-[left_top_7px] w-[10px] shrink-0"></div>
                    <div class="caption-content">
                      <span class="caption-credit mt-2 whitespace-nowrap text-xs">
                      Credit:
                      <a class="caption-credit-link text-gray-400 hover:text-gray-300" href="https://www.youtube.com/watch?v=HSb4IkhG6lQ">
                      Michael Buckley via YouTube
                      </a>
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </header>
          <div class="mx-auto my-5 max-w-md px-5 sm:max-w-3xl lg:grid lg:max-w-6xl lg:grid-cols-3 lg:gap-16 xl:px-0">
            <div class="relative lg:col-span-2">
              <div class="single-comment-bubble absolute left-0 top-0 hidden lg:block">
                <a class="view-comments font-impact text-gray-300 hover:text-gray-500 relative z-10 inline-flex h-[37px] w-[37px] flex-row flex-nowrap items-center justify-center font-medium uppercase" href="https://arstechnica.com/features/2017/06/youtube-changed-my-life-a-pair-of-original-videostars-ponder-a-life-lived-online/#comments" title="38 comments">
                  <svg class="-scale-y-100 absolute h-full w-full" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 49 49">
                    <defs>
                      <clipPath id="bubble_svg__a">
                        <path fill="none" d="M.011 10.382 38.648.029l10.353 38.637L10.364 49.02z"></path>
                      </clipPath>
                      <clipPath id="bubble_svg__b">
                        <path fill="none" d="M.011 10.382 38.648.029l10.353 38.637L10.364 49.02z"></path>
                      </clipPath>
                    </defs>
                    <g clip-path="url(#bubble_svg__a)">
                      <g fill="currentColor" clip-path="url(#bubble_svg__b)">
                        <path d="M29.7 43.8C19 46.7 8.1 40.3 5.2 29.7S8.7 8.1 19.3 5.2s21.6 3.5 24.5 14.1c2.9 10.7-3.5 21.6-14.1 24.5"></path>
                        <path d="M24.5 24.5 1.7 10.2c-.8-.4-1.7.3-1.5 1.1l3.3 12.2 1.7 6.2z"></path>
                      </g>
                    </g>
                  </svg>
                  <span class="text-xs sm:text-sm relative text-white">
                  38
                  </span>
                </a>
              </div>
              <div class="post-content post-content-double text-xl lg:pl-[72px]">
                <div class="page-anchor-wrapper"><a class="" name="page-1" data-page="1" data-url="https://arstechnica.com/features/2017/06/youtube-changed-my-life-a-pair-of-original-videostars-ponder-a-life-lived-online/"></a></div>
                <p>It can be hard to remember what life was like before YouTube. If you wanted to listen to a pop song, you couldn’t just queue it up online. You’d have to find it on the radio, download an MP3 file from iTunes (or a number of file sharing websites), or wait for MTV to play the video in its never-ending rotation. Celebrities were the only reality TV stars, and the most popular show was American Idol (won in 2006 by Taylor Hicks).</p>
                <p>In short, old media still dominated, but culture was at an odd liminal stage. Almost everything that mattered still happened on TV, but much of the talking started happening on the Internet. This is essentially the landscape YouTube entered.</p>
                <p>The site started relatively slowly in April 2005, with a video of YouTube co-founder Jawed Karim narrating a visit to San Diego Zoo’s elephant enclosure. In August the same year, the site boasted an impressive 58,000 visitors. By the end of the year, it had a traffic rocket strapped to its back: 20 million unique visitors logged on to YouTube each month by mid-2006. These days, more than 1.3 billion people use the site around the world, and five billion videos are watched daily.</p>
                <p>Contest-wise, we’ve come a long way from zoo visits today. Modern YouTube stars can bring in millions of dollars through sponsorship and companies they own and run (often built on the foundation of their videos). Polish and premise on-site can match most of what you’d find on TV or streaming services. Accordingly, someone like Jacob Sartorius is better-known to a generation of young men and women than many mainstream celebrities.</p>
                <div class="ars-interlude-container in-content-interlude my-5">
                  <div id="cne-interlude-1" data-cne-interlude="">
                    <div>
                      <style>#cne-interlude-1 .cne-interlude-container {
                        background: #e9e3d1;  
                        clear: both;
                        color: #586e75;  
                        margin: 0 auto;
                        padding: 18px 12px;
                        text-align: center;
                        }
                        body.dark #cne-interlude-1 .cne-interlude-container {
                        background: #000d11;
                        color: #839496;
                        }
                        #cne-interlude-1 .cne-interlude-header {
                        color: #2aa198;
                        font-family: bitter,NoticiaBold,"Times New Roman",serif;
                        font-size: 15px;
                        line-height: 1.2;
                        font-style: italic;
                        margin: 0 auto;
                        text-transform: uppercase;
                        }
                        #cne-interlude-1 .cne-interlude-header:after {
                        border-bottom: 1px solid;
                        content: '';
                        display: block;
                        height: 0;
                        margin: 8px auto;
                        width: 14px;
                        }
                        #cne-interlude-1 .cne-interlude-title {
                        color: #586e75;
                        font-family: bitter,NoticiaBold,"Times New Roman",serif;
                        font-size: 17px;
                        line-height: 1.25;
                        margin: 2px auto 16px;
                        }
                        body.dark #cne-interlude-1 .cne-interlude-title {
                        color: #839496;
                        }
                        #cne-interlude-1 .cne-player-placeholder:first-of-type:last-of-type {
                        padding-top: 56.25%;
                        width: 100%;
                        }
                      </style>
                      <figure class="cne-interlude-container">
                        <h3 class="cne-interlude-header">Ars Video</h3>
                        <a class="cne-interlude-title-link" target="_blank" href="https://www.arstechnica.com/video/watch/how-scientists-respond-to-science-deniers">
                          <h3 class="cne-interlude-title">How Scientists Respond to Science Deniers</h3>
                        </a>
                        <div class="cne-interlude-player-container">
                          <div class="cne-player-placeholder"></div>
                          <div style="box-sizing:content-box;display:inline-block;height:0px;padding-top:56.25%;position:relative;transition:height 300ms ease-in-out;vertical-align:top;width:100%;" class="cne-player-container" id="18c35a09-8c47-61a7-55e-17b93c5c92fc"><iframe style="height:1px !important;left:0px;min-height:100%;min-width:100%;position:absolute;top:0px;width:1px !important;" frameborder="0" scrolling="no" allowfullscreen="true" msallowfullscreen="true" allowtransparency="true" title="Video Player"></iframe></div>
                          <script async="" type="text/javascript" src="//player.cnevids.com/script/video/62cecf17828e58507c5082a1.js?autoplay=1&amp;hasCompanion=false&amp;hideHoverTitle=1&amp;hidePosterTitle=1&amp;muted=1&amp;interludeOverride=true&amp;onReady=setupInterlude1&amp;playerType=interlude&amp;recAlgorithm=sitewideInterludeVideoOverride&amp;recStrategy=sitewideInterludeVideoOverride&amp;showPlaylistBar=false" class="processed"></script>
                        </div>
                      </figure>
                    </div>
                  </div>
                </div>
                <p>But even in YouTube’s early going, there were people creating videos and growing some kind of community—it’s just their experiences differed quite a bit from the meteoric video star risings of today, plenty of which end in lucrative business partnerships or studio-based opportunities. What happened to those original viral video pioneers who laid the path to success for today’s YouTubers—the innovators who found the first flourish of fame before YouTube became a media juggernaut? Where are they now, and, perhaps more interesting, how do they feel about what their beloved platform has become?</p>
              </div>
            </div>
            <div class="hidden min-w-[300px] bg-gray-100 dark:bg-gray-50 lg:block">
              <div class="ad-wrapper is-sticky is-rail">
                <div class="ad-wrapper-inner">
                  <div class="ad ad--rail">
                    <div id="cns-ads-slot-type-rail-0" class="cns-ads-stage cns-ads-slot-type-rail cns-ads-slot-type-rail-0" data-name="rail_0" data-slot-type="rail" style="font-size: 0px; line-height: 0; overflow: hidden;">
                      <div class="cns-ads-flex-sizer"></div>
                      <div id="rail_0" class="cns-ads-container" style="margin: 0px auto; box-sizing: content-box;"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="ad-wrapper with-label is-fullwidth">
            <div class="ad-wrapper-inner">
              <div class="ad ad--mid-content">
                <div id="cns-ads-slot-type-mid-content-0" class="cns-ads-stage cns-ads-slot-type-mid-content cns-ads-slot-type-mid-content-0" data-name="mid_content_0" data-slot-type="mid_content" style="font-size: 0px; line-height: 0; overflow: hidden;">
                  <div class="cns-ads-flex-sizer"></div>
                  <div id="mid_content_0" class="cns-ads-container" style="margin: 0px auto; box-sizing: content-box;"></div>
                </div>
              </div>
            </div>
          </div>
          <div class="mx-auto my-5 max-w-md px-5 sm:max-w-3xl lg:grid lg:max-w-6xl lg:grid-cols-3 lg:gap-16 xl:px-0">
            <div class="relative lg:col-span-2">
              <div class="post-content post-content-double text-xl lg:pl-[72px]">
                <h2>A pair of personalities</h2>
                <p>Michael Buckley is slowly chugging through traffic at the end of a holiday weekend at his parents’ summer house on Cape Cod. “It’s a good time to talk,” he drawls, his languorous voice playfully rising and falling over his in-car cellphone’s speaker. “I have nothing else distracting me, so you have my full attention.”</p>
                <p>For those who weren’t around at the inception of YouTube, Buckley was kind of the Jimmy Fallon of his day: a public access TV talk show host who transferred online and attained a smidgen of fame by starting up a YouTube channel in the summer of 2006. His schtick was simple but effective: Buckley starred as the snarky host of his own program, <em>What The Buck</em>?. In short, snappy videos rarely more than three minutes long, he’d run through (and run down) the celebrity news of the day with a biting and acerbic wit. “My online persona 10 years ago was bitchy, rambling pop-culture-expert talking head,” Buckley says today. And viewers loved it. At one point, he had four of the top 10 most-viewed videos on YouTube.</p>
                <figure class="ars-video">
                  <div class="relative" style="aspect-ratio: 1.7777777777778 auto;"><iframe class="absolute left-0 top-0 block h-full w-full object-cover" allow="fullscreen" loading="lazy" src="https://www.youtube.com/embed/5-NyhfeqpyY?start=0&amp;wmode=transparent"></iframe></div>
                  <div class="caption mt-1 inline-flex flex-row items-stretch gap-1 text-lg leading-tight text-gray-300">
                    <div class="caption-icon bg-[left_top_5px] w-[10px] shrink-0"></div>
                    <div class="caption-content">
                      A vintage <em>What The Buck?</em> from 2007 skewers Dakota Fanning and <em>Harry Potter</em> (topics on other episodes in the era range from <a href="https://www.youtube.com/watch?v=UZJdaNLUuKg&amp;index=29&amp;list=PL1uzuPuZP70m9zCxv6A6zV1rgjSHk1tOm">Britney Spears</a> to <a href="https://www.youtube.com/watch?v=5-NyhfeqpyY&amp;list=PL1uzuPuZP70m9zCxv6A6zV1rgjSHk1tOm&amp;index=45">Donald Trump</a>).
                    </div>
                  </div>
                </figure>
                <p>Looking back at the<a href="https://www.youtube.com/watch?v=nnh81WB5wno&amp;list=PL1uzuPuZP70m9zCxv6A6zV1rgjSHk1tOm"> first of 10 seasons</a> of <em>What The Buck?</em> shows just how different YouTube was back in 2006. Buckley’s initial videos were simply a low-bitrate, slightly more risqué version of what you’d see on late night public access TV. Standing in front of a poorly keyed green screen, Buckley delivered a scripted monologue in a single take with no cuts. Pictures appeared over his shoulder as if he were a news anchor. Though it may share the same bones, it feels a world away from the fast-paced, well-lit jump cut monologues of modern-day YouTubers like Grace Helbig, the Vlog Brothers, or Zoella.</p>
                <p>Other early YouTubers, however, bridged the gap between these two seemingly disparate worlds.&nbsp;If Michael Buckley represented the old, nasty side of celebrity culture, another early site personality represented the cuddly, bubbly side. As bitchy and negative as Buckley’s persona was, Olga Kay came across as the exact opposite.</p>
              </div>
            </div>
            <div class="hidden min-w-[300px] bg-gray-100 dark:bg-gray-50 lg:block">
              <div class="ad-wrapper is-sticky is-rail">
                <div class="ad-wrapper-inner">
                  <div class="ad ad--rail">
                    <div id="cns-ads-slot-type-rail-1" class="cns-ads-stage cns-ads-slot-type-rail cns-ads-slot-type-rail-1" data-name="rail_1" data-slot-type="rail" style="font-size: 0px; line-height: 0; overflow: hidden;">
                      <div class="cns-ads-flex-sizer"></div>
                      <div id="rail_1" class="cns-ads-container" style="margin: 0px auto; box-sizing: content-box;"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="ad-wrapper with-label is-fullwidth">
            <div class="ad-wrapper-inner">
              <div class="ad ad--mid-content">
                <div id="cns-ads-slot-type-mid-content-1" class="cns-ads-stage cns-ads-slot-type-mid-content cns-ads-slot-type-mid-content-1" data-name="mid_content_1" data-slot-type="mid_content" style="font-size: 0px; line-height: 0; overflow: hidden;">
                  <div class="cns-ads-flex-sizer"></div>
                  <div id="mid_content_1" class="cns-ads-container" style="margin: 0px auto; box-sizing: content-box;"></div>
                </div>
              </div>
            </div>
          </div>
          <div class="mx-auto my-5 max-w-md px-5 sm:max-w-3xl lg:grid lg:max-w-6xl lg:grid-cols-3 lg:gap-16 xl:px-0">
            <div class="relative lg:col-span-2">
              <div class="post-content post-content-double text-xl lg:pl-[72px]">
                <p>At the time an accomplished juggler with the Ringling Brothers’ Circus, Kay (whose real name is Olga Karavajeva) was trying to break into the broader world of show business. Throughout much of 2006, she auditioned for TV shows, movies, and commercials, but she wasn’t breaking through.</p>
                <p>Some of Kay’s friends recommended she open an account on a new website called YouTube. Not only might it help her gain attention and an audience that could propel her into mainstream television, but it’d also help them out. As aspiring YouTubers uploading juggling videos themselves, they were keen to enlist their family and friends to rate their videos and put them up the rankings. “At first I thought they said U2.com and that it was associated with the band,” she recalls. “It took me a while to find out exactly what the site was.”</p>
                <p>When Kay did find the website, she created an account and looked beyond her friends’ juggling videos. What she found surprised her. “I remember coming across the original creators like LonelyGirl15 and LisaNova and being so mesmerized by these people looking at me through the screen and telling their stories,” Kay says. “It was the first time I’d experienced that break in the fourth wall, with someone talking to me through the screen.”</p>
                <p>Quickly, Kay wanted to be part of it. “I remember thinking, I can do it better—if only I knew how.”</p>
                <p>Kay didn’t have a camera or special editing software; she made her first forays onto YouTube by recording videos with her computer’s built-in webcam and iMovie. She taught herself the basics of video editing, observing the tips and tricks that other, more established YouTubers used to give the veneer of professionalism. And for two years between 2006 to 2008, Kay estimates she spent around 12 hours a day working out how to make videos. She’d watch other creators on YouTube, then futz around on her own content. She’d upload it, then comment on her favorite channels, inviting others to watch her videos, too, working hard to bring attention to them.<br></p>
                <figure class="ars-video">
                  <div class="relative" style="aspect-ratio: 1.7777777777778 auto;"><iframe class="absolute left-0 top-0 block h-full w-full object-cover" allow="fullscreen" loading="lazy" src="https://www.youtube.com/embed/K1XI3JWew_s?start=0&amp;wmode=transparent"></iframe></div>
                  <div class="caption mt-1 inline-flex flex-row items-stretch gap-1 text-lg leading-tight text-gray-300">
                    <div class="caption-icon bg-[left_top_5px] w-[10px] shrink-0"></div>
                    <div class="caption-content">
                      An early Olga Kay video explaining her setting, the Magic Castle.
                    </div>
                  </div>
                </figure>
                <p></p>
              </div>
            </div>
            <div class="hidden min-w-[300px] bg-gray-100 dark:bg-gray-50 lg:block">
              <div class="ad-wrapper is-sticky is-rail">
                <div class="ad-wrapper-inner">
                  <div class="ad ad--rail">
                    <div id="cns-ads-slot-type-rail-2" class="cns-ads-stage cns-ads-slot-type-rail cns-ads-slot-type-rail-2" data-name="rail_2" data-slot-type="rail" style="font-size: 0px; line-height: 0; overflow: hidden;">
                      <div class="cns-ads-flex-sizer"></div>
                      <div id="rail_2" class="cns-ads-container" style="margin: 0px auto; box-sizing: content-box;"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="ad-wrapper with-label is-fullwidth">
            <div class="ad-wrapper-inner">
              <div class="ad ad--mid-content">
                <div id="cns-ads-slot-type-mid-content-2" class="cns-ads-stage cns-ads-slot-type-mid-content cns-ads-slot-type-mid-content-2" data-name="mid_content_2" data-slot-type="mid_content" style="font-size: 0px; line-height: 0; overflow: hidden;">
                  <div class="cns-ads-flex-sizer"></div>
                  <div id="mid_content_2" class="cns-ads-container" style="margin: 0px auto; box-sizing: content-box;"></div>
                </div>
              </div>
            </div>
          </div>
          <div class="mx-auto my-5 max-w-md px-5 sm:max-w-3xl lg:grid lg:max-w-6xl lg:grid-cols-3 lg:gap-16 xl:px-0">
            <div class="relative lg:col-span-2">
              <div class="post-content post-content-double text-xl lg:pl-[72px]">
                <h2>All work, no pay</h2>
                <p>Both Buckley and Kay swiftly arrived at the paradox of early YouTube. To become popular on the site required <em>plenty </em>of work—but the payoff was in no way immediate or obvious. “I had 100 videos before anybody knew who I was, and I was working 50 hours a week on this,” Buckley explains. “I had a 40-hour-a-week job, and I was still spending every waking hour writing, editing, and promoting, really working on building my YouTube persona. It was a lot of work, but it was fun. I didn’t know where it was going to lead, but I just saw a great potential.”</p>
                <p>Kay’s 12-hour days weren’t earning her explosive growth, either, but through all the research she soon realized some YouTubers had advertising playing before their videos. While Kay couldn’t find any information on how to get that, monetizing her videos would soon become important. Around this time, the YouTube community had begun to branch out slowly into the real world: occasionally there’d be real-life meetups at events like VidCon (which today is a media mammoth). At these get togethers, a handful of creators would meet up in Central Park in New York City or Hollywood Boulevard in Los Angeles to swap tips, chat, and record videos of the day that they would later upload. When Kay went to one particular event, she was doing well on YouTube, boasting a devoted following of around 1,000 subscribers. But she was caught on some of the videos of more popular creators that day. Her 1,000 subscribers soon became 3,000. Rapidly, it became more.</p>
                <figure class="ars-video">
                  <div class="relative" style="aspect-ratio: 1.7777777777778 auto;"><iframe class="absolute left-0 top-0 block h-full w-full object-cover" allow="fullscreen" loading="lazy" src="https://www.youtube.com/embed/_xUrogu-WZU?start=0&amp;wmode=transparent"></iframe></div>
                  <div class="caption mt-1 inline-flex flex-row items-stretch gap-1 text-lg leading-tight text-gray-300">
                    <div class="caption-icon bg-[left_top_5px] w-[10px] shrink-0"></div>
                    <div class="caption-content">
                      A magical gathering of YouTube creators helped truly get Olga Kay off and running.
                    </div>
                  </div>
                </figure>
                <p>Kay describes the first check she received from YouTube as a momentous occasion—though the money at this stage, for someone early in a “video” career, was a pittance. Opening the envelope that arrived in the morning mail, she found an invoice for 54 cents. It didn’t matter that she wouldn’t actually get the money into her bank account until she made $100, Kay was excited. “I felt like I had a hobby that was not only fun, but it also made me money,” she says. “It became an interesting race: how can I get more people to watch?”</p>
              </div>
            </div>
            <div class="hidden min-w-[300px] bg-gray-100 dark:bg-gray-50 lg:block">
              <div class="ad-wrapper is-sticky is-rail">
                <div class="ad-wrapper-inner">
                  <div class="ad ad--rail">
                    <div id="cns-ads-slot-type-rail-3" class="cns-ads-stage cns-ads-slot-type-rail cns-ads-slot-type-rail-3" data-name="rail_3" data-slot-type="rail" style="font-size: 0px; line-height: 0; overflow: hidden;">
                      <div class="cns-ads-flex-sizer"></div>
                      <div id="rail_3" class="cns-ads-container" style="margin: 0px auto; box-sizing: content-box;"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="ad-wrapper with-label is-fullwidth">
            <div class="ad-wrapper-inner">
              <div class="ad ad--mid-content">
                <div id="cns-ads-slot-type-mid-content-3" class="cns-ads-stage cns-ads-slot-type-mid-content cns-ads-slot-type-mid-content-3" data-name="mid_content_3" data-slot-type="mid_content" style="font-size: 0px; line-height: 0; overflow: hidden;">
                  <div class="cns-ads-flex-sizer"></div>
                  <div id="mid_content_3" class="cns-ads-container" style="margin: 0px auto; box-sizing: content-box;"></div>
                </div>
              </div>
            </div>
          </div>
          <div class="mx-auto my-5 max-w-md px-5 sm:max-w-3xl lg:grid lg:max-w-6xl lg:grid-cols-3 lg:gap-16 xl:px-0">
            <div class="relative lg:col-span-2">
              <div class="post-content post-content-double text-xl lg:pl-[72px]">
                <p>As Kay and Buckley worked hard to bring new viewers to their videos, sometimes struggling and running up against brick walls, the whole ecosystem of online video slowly changed. Internet speeds got faster; celebrity as a mantle became more easily attainable. As YouTube became more popular and the concept of viral videos became more commonplace, mainstream-type money started flowing onto the platform.</p>
                <p>That cash trickled down to the creators. “You start making money, and you think you’ve tricked the world,” says Buckley. “How did this happen? I was doing this for fun, and for free, and now I have thousands of dollars in my Adsense account. This is crazy. It’s nutty.”</p>
                <p>In the years since Buckley and Kay made their first paycheck on YouTube, the site has become a much bigger goldmine for creators. Since 2007, YouTube has paid out more than $1.25 billion to rights holders for the ability to screen their content. And many creators—like Lisa “LisaNova” Donovan, one of the most successful YouTubers of this first generation—have swapped a life in front of the camera for one behind the scenes in the larger industry. (After her video starring days, Donovan co-founded Maker Studios, a YouTube multichannel network that was bought out by the Walt Disney Company for half a billion dollars.)</p>
                <p>Nowadays many young YouTubers can become flush with cash much more quickly and set up their own companies just to deal with the income from ad sales, marketing tie-ins, and personal appearances at live events. For example, Felix “PewDiePie” Kjellberg, one of the world’s best-known YouTubers, made $8 million in profit in 2015, according to company accounts filed in his native Sweden.</p>
                <p>“Some of these YouTube stars have quite an apparatus around them,” explains Alice E. Marwick, a researcher in online identity and celebrity at Fordham University. “There are these influencer marketing companies trying to broker these kids' fame and sponsorship deals.”</p>
              </div>
            </div>
            <div class="hidden min-w-[300px] bg-gray-100 dark:bg-gray-50 lg:block">
              <div class="ad-wrapper is-sticky is-rail">
                <div class="ad-wrapper-inner">
                  <div class="ad ad--rail">
                    <div id="cns-ads-slot-type-rail-4" class="cns-ads-stage cns-ads-slot-type-rail cns-ads-slot-type-rail-4" data-name="rail_4" data-slot-type="rail" style="font-size: 0px; line-height: 0; overflow: hidden;">
                      <div class="cns-ads-flex-sizer"></div>
                      <div id="rail_4" class="cns-ads-container" style="margin: 0px auto; box-sizing: content-box;"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="ad-wrapper with-label is-fullwidth">
            <div class="ad-wrapper-inner">
              <div class="ad ad--mid-content">
                <div id="cns-ads-slot-type-mid-content-4" class="cns-ads-stage cns-ads-slot-type-mid-content cns-ads-slot-type-mid-content-4" data-name="mid_content_4" data-slot-type="mid_content" style="font-size: 0px; line-height: 0; overflow: hidden;">
                  <div class="cns-ads-flex-sizer"></div>
                  <div id="mid_content_4" class="cns-ads-container" style="margin: 0px auto; box-sizing: content-box;"></div>
                </div>
              </div>
            </div>
          </div>
          <div class="mx-auto my-5 max-w-md px-5 sm:max-w-3xl lg:grid lg:max-w-6xl lg:grid-cols-3 lg:gap-16 xl:px-0">
            <div class="relative lg:col-span-2">
              <div class="post-content post-content-double text-xl lg:pl-[72px]">
                <div class="page-anchor-wrapper"><a class="record-pageview" name="page-2" data-page="2" data-url="https://arstechnica.com/features/2017/06/youtube-changed-my-life-a-pair-of-original-videostars-ponder-a-life-lived-online/"></a></div>
                <h2>Fame</h2>
                <p>As a great philosopher once said, “Mo’ money, mo’ problems.” Buckley feels grateful his modest ascension happened a decade ago, not today. “I’m very grateful I did go to college and worked normal paying jobs until I was 33 years old, because I think a lot of these kids have no idea how money works,” he says.</p>
                <p>“You meet some of these kids who are 14 or 15 when they start, and by the time they’re 19 or 20 they seem a little wanky because they let it go to their head,” Buckley adds. “Their whole life has been Internet comments. It’s not like they’re doing it because they love making videos. They’re doing it because they love being famous. They love the celebrity, and they love that their fans adore them and scream at them. I worry for them in the future.”</p>
                <p>Fame may be fickle nowadays as our modern society’s attention span narrows to even less than Andy Warhol’s famed 15 minutes, but the struggles of success aren’t new to modern YouTube. Early on as the site matured and transformed, things became more difficult for people like Kay, too.</p>
                <p>“The YouTube career has been one of the hardest careers I’ve built—and I’ve been in the circus, which was really tough,” says Kay. “A lot of people still don’t understand that being any kind of social media personality, you do the job of at least five people. You have to be creative; you have to be a writer, be a performer, an editor, a director, a marketer. You have to have some kind of a business sense to make it work <em>and</em> make it sustainable.”</p>
                <p>For a long time, despite her popularity, Kay couldn’t make it work. For <a href="https://www.nytimes.com/2014/02/02/business/chasing-their-star-on-youtube.html?_r=0">a period in 2013 and 2014</a>, Kay was posting 23 videos a week across a network of channels. She no longer did YouTube as part of her life, she did her life for YouTube. “I would film every single step of my life and everything I was going through. Did I cry all day? Did I laugh all day? That was all filmed and documented,” she explains. “It was a really unhealthy way of living life.”</p>
              </div>
            </div>
            <div class="hidden min-w-[300px] bg-gray-100 dark:bg-gray-50 lg:block">
              <div class="ad-wrapper is-sticky is-rail">
                <div class="ad-wrapper-inner">
                  <div class="ad ad--rail">
                    <div id="cns-ads-slot-type-rail-5" class="cns-ads-stage cns-ads-slot-type-rail cns-ads-slot-type-rail-5" data-name="rail_5" data-slot-type="rail" style="font-size: 0px; line-height: 0; overflow: hidden;">
                      <div class="cns-ads-flex-sizer"></div>
                      <div id="rail_5" class="cns-ads-container" style="margin: 0px auto; box-sizing: content-box;"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="ad-wrapper with-label is-fullwidth">
            <div class="ad-wrapper-inner">
              <div class="ad ad--mid-content">
                <div id="cns-ads-slot-type-mid-content-5" class="cns-ads-stage cns-ads-slot-type-mid-content cns-ads-slot-type-mid-content-5" data-name="mid_content_5" data-slot-type="mid_content" style="font-size: 0px; line-height: 0; overflow: hidden;">
                  <div class="cns-ads-flex-sizer"></div>
                  <div id="mid_content_5" class="cns-ads-container" style="margin: 0px auto; box-sizing: content-box;"></div>
                </div>
              </div>
            </div>
          </div>
          <div class="mx-auto my-5 max-w-md px-5 sm:max-w-3xl lg:grid lg:max-w-6xl lg:grid-cols-3 lg:gap-16 xl:px-0">
            <div class="relative lg:col-span-2">
              <div class="post-content post-content-double text-xl lg:pl-[72px]">
                <p>During this time, if Kay received an invitation to a party, her first question was a simple one: can I film there? “I was saying: ‘If somebody’s uncomfortable with me filming I don’t think I can go and have fun with you guys because I need content,’” she says. Kay was at the peak of her powers, but she was deeply unhappy. YouTube had gone from a passion to a problem.</p>
                <p>“Instead of seeing it as a fun chance to get money to do something more exciting, I saw it as a burden,” she admits.</p>
                <p>Kay had to take a step back: where YouTube had once been her whole life, it needed to become just one part. She set up a sock company and started dedicating more of her time to that than the video camera in the corner of her room. With the benefit of hindsight today, she remains eternally grateful to YouTube despite any dark times—“everything I’m doing today came from YouTube,” she sings.</p>
                <p>Like Olga Kay, Buckley has had both ups and downs from a life lived online. “I was so in love with it and it was my whole world, and then I kind of burned out for a couple of years, and now I feel much more peaceful,” he explains. “I used to see my whole world as YouTube. Now I just see YouTube as one piece of the puzzle.” When you’re on a site for 10 years of your life, he reckons, you’re bound to go through positive and negative emotions. “I burned out four or five times.”<br></p>
                <figure class="ars-video">
                  <div class="relative" style="aspect-ratio: 1.7777777777778 auto;"><iframe class="absolute left-0 top-0 block h-full w-full object-cover" allow="fullscreen" loading="lazy" src="https://www.youtube.com/embed/yxdR_u8pCEk?start=0&amp;wmode=transparent"></iframe></div>
                  <div class="caption mt-1 inline-flex flex-row items-stretch gap-1 text-lg leading-tight text-gray-300">
                    <div class="caption-icon bg-[left_top_5px] w-[10px] shrink-0"></div>
                    <div class="caption-content">
                      Olga Kay has returned to YouTube and embraced her classic skillset—juggling.
                    </div>
                  </div>
                </figure>
                <p></p>
                <h2>Old stars; new videos</h2>
                <p>These days, Kay says she’ll somewhat routinely run into old fans when she goes to Target—people who are 21 or 22. They’ll introduce themselves, saying they grew up watching her videos.</p>
              </div>
            </div>
            <div class="hidden min-w-[300px] bg-gray-100 dark:bg-gray-50 lg:block">
              <div class="ad-wrapper is-sticky is-rail">
                <div class="ad-wrapper-inner">
                  <div class="ad ad--rail">
                    <div id="cns-ads-slot-type-rail-6" class="cns-ads-stage cns-ads-slot-type-rail cns-ads-slot-type-rail-6" data-name="rail_6" data-slot-type="rail" style="font-size: 0px; line-height: 0; overflow: hidden;">
                      <div class="cns-ads-flex-sizer"></div>
                      <div id="rail_6" class="cns-ads-container" style="margin: 0px auto; box-sizing: content-box;"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="ad-wrapper with-label is-fullwidth">
            <div class="ad-wrapper-inner">
              <div class="ad ad--mid-content">
                <div id="cns-ads-slot-type-mid-content-6" class="cns-ads-stage cns-ads-slot-type-mid-content cns-ads-slot-type-mid-content-6" data-name="mid_content_6" data-slot-type="mid_content" style="font-size: 0px; line-height: 0; overflow: hidden;">
                  <div class="cns-ads-flex-sizer"></div>
                  <div id="mid_content_6" class="cns-ads-container" style="margin: 0px auto; box-sizing: content-box;"></div>
                </div>
              </div>
            </div>
          </div>
          <div class="mx-auto my-5 max-w-md px-5 sm:max-w-3xl lg:grid lg:max-w-6xl lg:grid-cols-3 lg:gap-16 xl:px-0">
            <div class="relative lg:col-span-2">
              <div class="post-content post-content-double text-xl lg:pl-[72px]">
                <p>Kay usually asks them a question: why don’t you watch them now?</p>
                <p>They say they’re sorry, but they don’t have time to watch content. They have to pay their bills.</p>
                <p>“The truth is, whoever brings you to the top of your YouTube career grows up and has to move on,” she says. “I’m happy doing YouTube as a hobby again.”</p>
                <figure class="ars-img-shortcode id-1113841 align-center">
                  <div>
                    <div class="ars-lightbox">
                      <div class="ars-lightbox-item">
                        <a data-pswp-width="1440" data-pswp-height="900" data-pswp-srcset="https://cdn.arstechnica.net/wp-content/uploads/2017/06/Screen-Shot-2017-06-08-at-1.33.40-PM-300x188.png 300w, https://cdn.arstechnica.net/wp-content/uploads/2017/06/Screen-Shot-2017-06-08-at-1.33.40-PM-640x400.png 640w, https://cdn.arstechnica.net/wp-content/uploads/2017/06/Screen-Shot-2017-06-08-at-1.33.40-PM-768x480.png 768w, https://cdn.arstechnica.net/wp-content/uploads/2017/06/Screen-Shot-2017-06-08-at-1.33.40-PM-980x613.png 980w, https://cdn.arstechnica.net/wp-content/uploads/2017/06/Screen-Shot-2017-06-08-at-1.33.40-PM.png 1440w" data-cropped="true" href="https://cdn.arstechnica.net/wp-content/uploads/2017/06/Screen-Shot-2017-06-08-at-1.33.40-PM.png" target="_blank" class="cursor-zoom-in">
                        <img decoding="async" width="1440" height="900" src="https://cdn.arstechnica.net/wp-content/uploads/2017/06/Screen-Shot-2017-06-08-at-1.33.40-PM.png" class="attachment-full size-full" alt="" srcset="https://cdn.arstechnica.net/wp-content/uploads/2017/06/Screen-Shot-2017-06-08-at-1.33.40-PM.png 1440w, https://cdn.arstechnica.net/wp-content/uploads/2017/06/Screen-Shot-2017-06-08-at-1.33.40-PM-300x188.png 300w, https://cdn.arstechnica.net/wp-content/uploads/2017/06/Screen-Shot-2017-06-08-at-1.33.40-PM-640x400.png 640w, https://cdn.arstechnica.net/wp-content/uploads/2017/06/Screen-Shot-2017-06-08-at-1.33.40-PM-768x480.png 768w, https://cdn.arstechnica.net/wp-content/uploads/2017/06/Screen-Shot-2017-06-08-at-1.33.40-PM-980x613.png 980w" sizes="(max-width: 1440px) 100vw, 1440px">
                        </a>
                        <div class="pswp-caption-content" id="caption-1113841">
                          One of those <a href="https://www.youtube.com/watch?v=HSb4IkhG6lQ">tattoos</a> look familiar?
                          <div class="ars-gallery-caption-credit">
                            Credit:
                            <a href="https://www.youtube.com/watch?v=HSb4IkhG6lQ" target="_blank">Michael Buckley on YouTube</a>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <figcaption>
                    <div class="caption mt-1 inline-flex flex-row items-stretch gap-1 text-lg leading-tight text-gray-300">
                      <div class="caption-icon bg-[left_top_5px] w-[10px] shrink-0"></div>
                      <div class="caption-content">
                        One of those <a href="https://www.youtube.com/watch?v=HSb4IkhG6lQ">tattoos</a> look familiar?
                        <span class="caption-credit mt-2 whitespace-nowrap text-xs">
                        Credit:
                        <a class="caption-credit-link text-gray-400 hover:text-gray-300" href="https://www.youtube.com/watch?v=HSb4IkhG6lQ">
                        Michael Buckley on YouTube
                        </a>
                        </span>
                      </div>
                    </div>
                  </figcaption>
                </figure>
                <p>“YouTube in 2006 was people just uploading videos of their pets or very weird comedic sketches they’d shot with themselves,” Buckley explains. “There was no high-quality content. There was high-quality interactions, though—and that’s the huge difference.”</p>
                <p>To Buckley, that’s the biggest change on his beloved platform within the last decade. The quality of content and community have swapped—“Now it’s high-quality content, but not as great interactions,” he says. Buckley is still on YouTube, but his videos have shifted accordingly. He’s no longer in front of a green screen, for starters. As the bedroom confessional look became the default medium for YouTube, he adopted it, too. <em>What The Buck? </em>as it previously existed has all but disappeared. “The format just doesn’t play well on YouTube in 2016,” Buckley admits. “We hear the word authentic all the time—it’s a buzzword YouTubers love to say at conferences—but being the <em>What The Buck?</em> character all the time, people don’t connect with that.”</p>
                <p>We’ve also become a nicer society, he reckons. When Buckley first found YouTube fame, acerbic comedies poking fun at people were among the most popular forms of entertainment. Today, many people cringe at the trolling culture of comment threads or social media. The old <em>What The Buck </em>character has been abandoned, and today Buckley is just plain old Michael Buckley, a gentler, older YouTuber—still on the site, but not beholden to it. “We worship celebrities now, whereas I found it all so ridiculous that famous people are so famous. A 41-year-old man in his guest room making fun of celebrities… there’s something about that that doesn’t connect with people on YouTube.”</p>
              </div>
            </div>
            <div class="hidden min-w-[300px] bg-gray-100 dark:bg-gray-50 lg:block">
              <div class="ad-wrapper is-sticky is-rail">
                <div class="ad-wrapper-inner">
                  <div class="ad ad--rail">
                    <div id="cns-ads-slot-type-rail-7" class="cns-ads-stage cns-ads-slot-type-rail cns-ads-slot-type-rail-7" data-name="rail_7" data-slot-type="rail" style="font-size: 0px; line-height: 0; overflow: hidden;">
                      <div class="cns-ads-flex-sizer"></div>
                      <div id="rail_7" class="cns-ads-container" style="margin: 0px auto; box-sizing: content-box;"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="ad-wrapper with-label is-fullwidth">
            <div class="ad-wrapper-inner">
              <div class="ad ad--mid-content">
                <div id="cns-ads-slot-type-mid-content-7" class="cns-ads-stage cns-ads-slot-type-mid-content cns-ads-slot-type-mid-content-7" data-name="mid_content_7" data-slot-type="mid_content" style="font-size: 0px; line-height: 0; overflow: hidden;">
                  <div class="cns-ads-flex-sizer"></div>
                  <div id="mid_content_7" class="cns-ads-container" style="margin: 0px auto; box-sizing: content-box;"></div>
                </div>
              </div>
            </div>
          </div>
          <div class="mx-auto my-5 max-w-md px-5 sm:max-w-3xl lg:grid lg:max-w-6xl lg:grid-cols-3 lg:gap-16 xl:px-0">
            <div class="relative lg:col-span-2">
              <div class="post-content post-content-double text-xl lg:pl-[72px]">
                <p>But good or bad, YouTube still changed his life. “I bought my home because of YouTube,” he says. “I’ve been able to travel the world and see places. I’ve been invited to go all around the world to make videos. I’ve met very inspiring celebrities and fellow content creators and people all over the world who have just inspired me to make content and to be a better person and to wish to change the world.</p>
                <p>“It doesn’t matter if 10 people are watching or 1,000: you can change the world with a single video… Someone could be going through something major in their life and happen to click on your video, and you’ve greatly improved their life.”</p>
                <p>As he rotates the wheel on his drive back from Cape Cod, his hands on the wheel, Buckley’s left wrist turns upward, showing a tattoo of a red play button on his skin. It’s a version of the YouTube icon, etched into Buckley’s skin as a reminder of his past and present life. “YouTube changed my life in every way,” he says. “Every time I feel stressed, I look down and think YouTube has made my entire life possible.”</p>
                <p><em><span class="s1"><a href="https://twitter.com/stokel?lang=en">Chris Stokel-Walker</a> is a freelance journalist, writing features for the </span></em><span class="s1">BBC</span><em><span class="s1"> and </span></em><span class="s1">The Sunday Times</span><em><span class="s1"> of London. He is based in the United Kingdom and has previously written about <a href="https://arstechnica.com/information-technology/2015/07/inside-an-online-content-mill-or-writing-4156-words-a-day-just-to-earn-lunch-money/">online content farms for Ars</a>.</span></em></p>
              </div>
              <section class="my-5 bg-gray-700" id="related-stories">
                <div class="related-stories-title font-impact flex flex-row items-center justify-center gap-2 bg-gray-600 px-5 py-2 text-3xl font-extrabold uppercase text-green-400">
                  <svg class="h-5 w-auto" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 60 40">
                    <defs>
                      <clipPath id="double-arrows_svg__a">
                        <path fill="none" d="M0 0h60v40H0z"></path>
                      </clipPath>
                      <clipPath id="double-arrows_svg__b">
                        <path fill="none" d="M0 0h60v40H0z"></path>
                      </clipPath>
                    </defs>
                    <g fill="none" clip-path="url(#double-arrows_svg__a)">
                      <path fill="#ff4e00" d="m0 20 23.5 20L20 25h7.8l-5.9-5 5.9-5H20l3.5-15z"></path>
                      <g clip-path="url(#double-arrows_svg__b)">
                        <path fill="#e9eaed" d="m45 15 3.5-15L25 20l23.5 20L45 25h10c2.8 0 5-2.2 5-5s-2.2-5-5-5z"></path>
                      </g>
                    </g>
                  </svg>
                  <span>Related Stories</span>
                </div>
                <div class="related-stories-list">
                  <span class="ars-story-sidebar footnote inline">
                  <span class="relative inline-block w-full">
                  <span class="footnote-content w-full overflow-hidden pt-[5px]">
                  <span class="footnote-inner relative flex gap-4 overflow-hidden rounded-sm bg-gray-700 p-5 text-gray-300">
                  <span class="relative aspect-square h-[72px] w-[72px] shrink-0 overflow-hidden rounded-full border-4 border-green-400">
                  <a href="https://arstechnica.com/gadgets/2015/04/cheaper-bandwidth-or-bust-how-google-saved-youtube/"><img width="150" height="150" src="https://cdn.arstechnica.net/wp-content/uploads/2015/04/2005-archive.org_-150x150.jpg" class="absolute left-0 top-0 min-w-full min-h-full object-cover wp-post-image" alt="" srcset="https://cdn.arstechnica.net/wp-content/uploads/2015/04/2005-archive.org_-150x150.jpg 150w, https://cdn.arstechnica.net/wp-content/uploads/2015/04/2005-archive.org_-500x500.jpg 500w" sizes="(max-width: 150px) 100vw, 150px"></a>
                  </span>
                  <span class="footnote-headline mt-[-6px]">
                  <a class="footnote-link" href="https://arstechnica.com/gadgets/2015/04/cheaper-bandwidth-or-bust-how-google-saved-youtube/">
                  Cheaper bandwidth or bust: How Google saved YouTube
                  </a><br>
                  <span class="footnote-text leading-tighter inline-block text-lg">
                  Leading the Web video revolution was an almost constant battle for survival.
                  </span>
                  </span>
                  </span>
                  </span>
                  </span>
                  </span>
                  <hr class="ml-5 w-28 border-gray-300">
                  <span class="ars-story-sidebar footnote inline">
                  <span class="relative inline-block w-full">
                  <span class="footnote-content w-full overflow-hidden pt-[5px]">
                  <span class="footnote-inner relative flex gap-4 overflow-hidden rounded-sm bg-gray-700 p-5 text-gray-300">
                  <span class="relative aspect-square h-[72px] w-[72px] shrink-0 overflow-hidden rounded-full border-4 border-green-400">
                  <a href="https://arstechnica.com/staff/2015/04/from-victor-borge-to-jon-stewart-ars-staffers-pick-their-youtube-favorites/"><img width="150" height="150" src="https://cdn.arstechnica.net/wp-content/uploads/2015/04/yo.dawg_.timezones-copy-2-150x150.jpg" class="absolute left-0 top-0 min-w-full min-h-full object-cover wp-post-image" alt="" srcset="https://cdn.arstechnica.net/wp-content/uploads/2015/04/yo.dawg_.timezones-copy-2-150x150.jpg 150w, https://cdn.arstechnica.net/wp-content/uploads/2015/04/yo.dawg_.timezones-copy-2-500x500.jpg 500w" sizes="(max-width: 150px) 100vw, 150px"></a>
                  </span>
                  <span class="footnote-headline mt-[-6px]">
                  <a class="footnote-link" href="https://arstechnica.com/staff/2015/04/from-victor-borge-to-jon-stewart-ars-staffers-pick-their-youtube-favorites/">
                  From Victor Borge to Jon Stewart: Ars staffers pick their YouTube favorites
                  </a><br>
                  <span class="footnote-text leading-tighter inline-block text-lg">
                  We can't possibly have hit all your must-sees, so holler at us in comments!
                  </span>
                  </span>
                  </span>
                  </span>
                  </span>
                  </span>
                  <hr class="ml-5 w-28 border-gray-300">
                  <span class="ars-story-sidebar footnote inline">
                  <span class="relative inline-block w-full">
                  <span class="footnote-content w-full overflow-hidden pt-[5px]">
                  <span class="footnote-inner relative flex gap-4 overflow-hidden rounded-sm bg-gray-700 p-5 text-gray-300">
                  <span class="relative aspect-square h-[72px] w-[72px] shrink-0 overflow-hidden rounded-full border-4 border-green-400">
                  <a href="https://arstechnica.com/information-technology/2015/04/ars-picks-the-top-youtube-video-of-all-time/"><img width="150" height="150" src="https://cdn.arstechnica.net/wp-content/uploads/2015/04/youtube-150x150.png" class="absolute left-0 top-0 min-w-full min-h-full object-cover wp-post-image" alt="" srcset="https://cdn.arstechnica.net/wp-content/uploads/2015/04/youtube-150x150.png 150w, https://cdn.arstechnica.net/wp-content/uploads/2015/04/youtube-500x500.png 500w" sizes="(max-width: 150px) 100vw, 150px"></a>
                  </span>
                  <span class="footnote-headline mt-[-6px]">
                  <a class="footnote-link" href="https://arstechnica.com/information-technology/2015/04/ars-picks-the-top-youtube-video-of-all-time/">
                  Ars picks the top YouTube video of <b>all time</b>
                  </a><br>
                  <span class="footnote-text leading-tighter inline-block text-lg">
                  We top off our look at the 10th anniversary of YouTube with the best video ever.
                  </span>
                  </span>
                  </span>
                  </span>
                  </span>
                  </span>
                </div>
              </section>
              <div class="author-mini-bio">
                <div class="flex flex-col items-start gap-5 border-y-4 py-5 dark:border-gray-700 sm:flex-row">
                  <div class="flex items-center gap-3">
                    <a class="relative block aspect-square h-24 w-24 shrink-0 overflow-hidden rounded-full border-4 border-green-400" href="https://arstechnica.com/author/ars-staff/"><img class="absolute left-0 top-0 min-h-full min-w-full object-cover" src="/wp-content/uploads/2024/05/ars-staff-author-image.jpg" alt="Photo of Ars Staff"></a>
                    <div class="font-impact mb-0 text-left text-base font-semibold uppercase sm:hidden">
                      <a href="https://arstechnica.com/author/ars-staff/">Chris Stokel-Walker</a>
                      <span class="block font-sans text-sm font-normal italic sm:inline-block"></span>
                    </div>
                  </div>
                  <div class="">
                    <div class="font-impact mb-0 hidden text-left text-base font-semibold uppercase sm:block">
                      <a href="https://arstechnica.com/author/ars-staff/">Chris Stokel-Walker</a>
                      <span class="block font-sans text-sm font-normal italic sm:ml-2 sm:inline-block"></span>
                    </div>
                    <div class="text-left text-base leading-5 text-gray-400" itemprop="description">
                    </div>
                  </div>
                </div>
              </div>
              <div class="story-tools flex flex-col items-center justify-between sm:flex-row">
                <div class="my-5 flex items-center gap-2">
                  <div class="single-comment-bubble">
                    <a class="view-comments font-impact text-gray-300 hover:text-gray-500 relative z-10 inline-flex h-[37px] w-[37px] flex-row flex-nowrap items-center justify-center font-medium uppercase" href="https://arstechnica.com/features/2017/06/youtube-changed-my-life-a-pair-of-original-videostars-ponder-a-life-lived-online/#comments" title="38 comments">
                      <svg class="-scale-y-100 absolute h-full w-full" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 49 49">
                        <defs>
                          <clipPath id="bubble_svg__a">
                            <path fill="none" d="M.011 10.382 38.648.029l10.353 38.637L10.364 49.02z"></path>
                          </clipPath>
                          <clipPath id="bubble_svg__b">
                            <path fill="none" d="M.011 10.382 38.648.029l10.353 38.637L10.364 49.02z"></path>
                          </clipPath>
                        </defs>
                        <g clip-path="url(#bubble_svg__a)">
                          <g fill="currentColor" clip-path="url(#bubble_svg__b)">
                            <path d="M29.7 43.8C19 46.7 8.1 40.3 5.2 29.7S8.7 8.1 19.3 5.2s21.6 3.5 24.5 14.1c2.9 10.7-3.5 21.6-14.1 24.5"></path>
                            <path d="M24.5 24.5 1.7 10.2c-.8-.4-1.7.3-1.5 1.1l3.3 12.2 1.7 6.2z"></path>
                          </g>
                        </g>
                      </svg>
                      <span class="text-xs sm:text-sm relative text-white">
                      38
                      </span>
                    </a>
                  </div>
                  <a class="view-comments font-impact whitespace-nowrap text-xl font-semibold uppercase" href="https://arstechnica.com/features/2017/06/youtube-changed-my-life-a-pair-of-original-videostars-ponder-a-life-lived-online/#comments" rel="nofollow ugc">View Comments</a>
                </div>
              </div>
              <section id="comments">
                <div class="comments-wrapper hidden">
                  <div class="wp-forum-connect-comments relative">
                    <div class="comments-title font-impact xs:justify-center flex flex-row items-center gap-2 bg-gray-600 px-5 py-2 text-3xl font-extrabold uppercase text-green-400">
                      <svg class="h-6 w-6 rotate-[-75deg] text-gray-100" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 49 49">
                        <defs>
                          <clipPath id="bubble_svg__a">
                            <path fill="none" d="M.011 10.382 38.648.029l10.353 38.637L10.364 49.02z"></path>
                          </clipPath>
                          <clipPath id="bubble_svg__b">
                            <path fill="none" d="M.011 10.382 38.648.029l10.353 38.637L10.364 49.02z"></path>
                          </clipPath>
                        </defs>
                        <g clip-path="url(#bubble_svg__a)">
                          <g fill="currentColor" clip-path="url(#bubble_svg__b)">
                            <path d="M29.7 43.8C19 46.7 8.1 40.3 5.2 29.7S8.7 8.1 19.3 5.2s21.6 3.5 24.5 14.1c2.9 10.7-3.5 21.6-14.1 24.5"></path>
                            <path d="M24.5 24.5 1.7 10.2c-.8-.4-1.7.3-1.5 1.1l3.3 12.2 1.7 6.2z"></path>
                          </g>
                        </g>
                      </svg>
                      Comments
                    </div>
                    <a class="font-impact absolute bottom-0 right-5 top-0 flex flex-row items-center gap-2 text-base font-semibold uppercase text-gray-300 hover:text-gray-200" href="https://arstechnica.com/civis/threads/%E2%80%9Cyoutube-changed-my-life%E2%80%9D%E2%80%94a-pair-of-original-video-stars-ponders-a-life-lived-online.1388163/" target="_blank">
                      <svg class="h-5 w-5 text-gray-200" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40">
                        <defs>
                          <clipPath id="forum-arrow_svg__a">
                            <path fill="none" d="M0 0h40v40H0z"></path>
                          </clipPath>
                          <clipPath id="forum-arrow_svg__b">
                            <path fill="none" d="M0 0h40v40H0z"></path>
                          </clipPath>
                        </defs>
                        <g clip-path="url(#forum-arrow_svg__a)">
                          <g fill="none" clip-path="url(#forum-arrow_svg__b)">
                            <path fill="#ff4e00" d="M23 0c-1.1 0-2 .9-2 2s.9 2 2 2h10.2L16.6 20.6c-.8.8-.8 2 0 *******.9.6 1.4.6s1-.2 1.4-.6L36 6.8V17c0 1.1.9 2 2 2s2-.9 2-2V0z"></path>
                            <path fill="currentColor" d="M30 24v12H4V10h12l4-4H4c-2.2 0-4 1.8-4 4v26c0 2.2 1.8 4 4 4h26c2.2 0 4-1.8 4-4V20z"></path>
                          </g>
                        </g>
                      </svg>
                      <span class="hidden sm:inline">Forum view</span>
                    </a>
                  </div>
                  <div class="xf_thread_iframe_wrapper relative min-h-screen">
                    <div class="xf_thread_iframe_loading flex items-center justify-center">
                      <div class="my-20">
                        <img class="h-10 w-10" src="https://arstechnica.com/wp-content/themes/ars-v9/public/images/firework-loader.75ab30.gif" alt="Loading">
                        Loading comments...
                      </div>
                    </div>
                    <div class="xf_thread_iframe_container" id="xf_thread_iframe_container" data-thread-id="1388163" data-url="https://arstechnica.com/civis/threads/%E2%80%9Cyoutube-changed-my-life%E2%80%9D%E2%80%94a-pair-of-original-video-stars-ponders-a-life-lived-online.1388163/unread?in_iframe=1&amp;theme=system&amp;wp_data=eyJ1cmwiOiJodHRwczpcL1wvYXJzdGVjaG5pY2EuY29tXC9mZWF0dXJlc1wvMjAxN1wvMDZcL3lvdXR1YmUtY2hhbmdlZC1teS1saWZlLWEtcGFpci1vZi1vcmlnaW5hbC12aWRlb3N0YXJzLXBvbmRlci1hLWxpZmUtbGl2ZWQtb25saW5lXC8iLCJvcGVuX2NvbW1lbnRzIjoiY29tbWVudHM9MSJ9&amp;" data-open="0" data-open-default="0"></div>
                  </div>
                </div>
              </section>
            </div>
            <div class="hidden min-w-[300px] bg-gray-100 dark:bg-gray-50 lg:block">
              <div class="ad-wrapper is-sticky is-rail">
                <div class="ad-wrapper-inner">
                  <div class="ad ad--rail">
                    <div id="cns-ads-slot-type-rail-8" class="cns-ads-stage cns-ads-slot-type-rail cns-ads-slot-type-rail-8" data-name="rail_8" data-slot-type="rail" style="font-size: 0px; line-height: 0; overflow: hidden;">
                      <div class="cns-ads-flex-sizer"></div>
                      <div id="rail_8" class="cns-ads-container" style="margin: 0px auto; box-sizing: content-box;"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </article>
        <div class="mx-auto my-5 max-w-md px-5 sm:max-w-3xl lg:grid lg:max-w-6xl lg:grid-cols-3 lg:gap-16 xl:px-0">
          <div class="single-most-read relative col-span-2 bg-gray-700">
            <div class="component-most-read font-impact flex h-full min-h-[300px] flex-col flex-nowrap gap-5 pb-5 uppercase text-white">
              <div>
                <header class="flex flex-row flex-nowrap items-center justify-center gap-2 bg-gray-600 px-5 py-2">
                  <svg class="h-[20px] w-[30px] text-gray-100" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 26">
                    <defs>
                      <clipPath id="most-read_svg__a">
                        <path fill="none" d="M0 0h40v26H0z"></path>
                      </clipPath>
                      <clipPath id="most-read_svg__b">
                        <path fill="none" d="M0 0h40v26H0z"></path>
                      </clipPath>
                    </defs>
                    <g clip-path="url(#most-read_svg__a)">
                      <g fill="none" clip-path="url(#most-read_svg__b)">
                        <path fill="currentColor" d="M20 2h.8q1.5 0 3 .6c.6.2 1.1.4 1.7.6 1.3.5 2.6 1.3 3.9 ******* 1.2.8 1.8 1.3 2.9 2.3 5.1 4.9 6.3 6.4-1.1 1.5-3.4 4-6.3 6.4-.6.5-1.2.9-1.8 1.3q-1.95 1.35-3.9 2.1c-.6.2-1.1.4-1.7.6q-1.5.45-3 .6h-1.6q-1.5 0-3-.6c-.6-.2-1.1-.4-1.7-.6-1.3-.5-2.6-1.3-3.9-2.1-.6-.4-1.2-.8-1.8-1.3-2.9-2.3-5.1-4.9-6.3-6.4 1.1-1.5 3.4-4 6.3-6.4.6-.5 1.2-.9 1.8-1.3q1.95-1.35 3.9-2.1c.6-.2 1.1-.4 1.7-.6q1.5-.45 3-.6zm0-2h-1c-1.2 0-2.3.3-3.4.6-.6.2-1.3.4-1.9.7-1.5.6-2.9 1.4-4.3 2.3-.7.5-1.3.9-1.9 1.4C2.9 8.7 0 13 0 13s2.9 4.3 7.5 7.9c.6.5 1.3 1 1.9 1.4 1.3.9 2.7 1.7 4.3 ******* 1.3.5 1.9.7 1.1.3 2.3.6 3.4.6h2c1.2 0 2.3-.3 3.4-.6.6-.2 1.3-.4 1.9-.7 1.5-.6 2.9-1.4 4.3-2.3.7-.5 1.3-.9 1.9-1.4C37.1 17.3 40 13 40 13s-2.9-4.3-7.5-7.9c-.6-.5-1.3-1-1.9-1.4-1.3-.9-2.8-1.7-4.3-2.3-.6-.3-1.3-.5-1.9-.7C23.3.4 22.1.1 21 .1h-1"></path>
                        <path fill="#ff4e00" d="M20 5c-4.4 0-8 3.6-8 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8m0 11c-1.7 0-3-1.3-3-3s1.3-3 3-3 3 1.3 3 3-1.3 3-3 3"></path>
                      </g>
                    </g>
                  </svg>
                  <div class="font-impact inline text-3xl font-extrabold uppercase text-green-400">
                    Most Read
                  </div>
                </header>
                <ol>
                  <li class="group relative">
                    <img class="h-auto w-full rounded-sm group-hover:saturate-150" src="https://cdn.arstechnica.net/wp-content/uploads/2024/10/GettyImages-1693802745-768x432.jpg" alt="Listing image for first story in Most Read: Meta smart glasses can be used to dox anyone in seconds, study finds" decoding="async" loading="lazy">
                    <div class="relative px-5 py-4">
                      <div class="most-read-divider absolute left-5 top-[-3px] h-[5px] w-1/4 bg-green-400">
                      </div>
                      <span class="flex flex-row flex-nowrap items-start gap-4 font-serif text-2xl font-bold normal-case leading-tight group-hover:text-orange-400">
                      <span class="shrink-0 text-green-400">1.</span>
                      <span class="most-read-title">Meta smart glasses can be used to dox anyone in seconds, study finds</span>
                      </span>
                    </div>
                    <a class="absolute left-0 top-0 h-full w-full" href="https://arstechnica.com/tech-policy/2024/10/harvard-students-make-auto-doxxing-smart-glasses-to-show-need-for-privacy-regs/?itm_source=parsely-api" aria-label="Read Meta smart glasses can be used to dox anyone in seconds, study finds">
                    </a>
                  </li>
                  <li class="group relative">
                    <div class="relative px-5 py-4">
                      <div class="most-read-divider absolute left-5 top-0 h-[1px] w-1/4 bg-gray-400">
                      </div>
                      <span class="flex flex-row flex-nowrap items-start gap-4 font-serif text-xl font-bold normal-case leading-tight group-hover:text-orange-400">
                      <span class="shrink-0 text-green-400">2.</span>
                      <span class="most-read-title">Thousands of Linux systems infected by stealthy malware since 2021</span>
                      </span>
                    </div>
                    <a class="absolute left-0 top-0 h-full w-full" href="https://arstechnica.com/security/2024/10/persistent-stealthy-linux-malware-has-infected-thousands-since-2021/?itm_source=parsely-api" aria-label="Read Thousands of Linux systems infected by stealthy malware since 2021">
                    </a>
                  </li>
                  <li class="group relative">
                    <div class="relative px-5 py-4">
                      <div class="most-read-divider absolute left-5 top-0 h-[1px] w-1/4 bg-gray-400">
                      </div>
                      <span class="flex flex-row flex-nowrap items-start gap-4 font-serif text-xl font-bold normal-case leading-tight group-hover:text-orange-400">
                      <span class="shrink-0 text-green-400">3.</span>
                      <span class="most-read-title">Popular gut probiotic completely craps out in randomized controlled trial</span>
                      </span>
                    </div>
                    <a class="absolute left-0 top-0 h-full w-full" href="https://arstechnica.com/health/2024/10/popular-gut-probiotic-completely-craps-out-in-randomized-controlled-trial/?itm_source=parsely-api" aria-label="Read Popular gut probiotic completely craps out in randomized controlled trial">
                    </a>
                  </li>
                  <li class="group relative">
                    <div class="relative px-5 py-4">
                      <div class="most-read-divider absolute left-5 top-0 h-[1px] w-1/4 bg-gray-400">
                      </div>
                      <span class="flex flex-row flex-nowrap items-start gap-4 font-serif text-xl font-bold normal-case leading-tight group-hover:text-orange-400">
                      <span class="shrink-0 text-green-400">4.</span>
                      <span class="most-read-title">X fails to avoid Australia child safety fine by arguing Twitter doesn’t exist</span>
                      </span>
                    </div>
                    <a class="absolute left-0 top-0 h-full w-full" href="https://arstechnica.com/tech-policy/2024/10/x-loses-appeal-of-400k-australia-child-safety-fine-now-faces-more-fines/?itm_source=parsely-api" aria-label="Read X fails to avoid Australia child safety fine by arguing Twitter doesn’t exist">
                    </a>
                  </li>
                  <li class="group relative">
                    <div class="relative px-5 py-4">
                      <div class="most-read-divider absolute left-5 top-0 h-[1px] w-1/4 bg-gray-400">
                      </div>
                      <span class="flex flex-row flex-nowrap items-start gap-4 font-serif text-xl font-bold normal-case leading-tight group-hover:text-orange-400">
                      <span class="shrink-0 text-green-400">5.</span>
                      <span class="most-read-title">ULA’s second Vulcan rocket lost part of its booster and kept going</span>
                      </span>
                    </div>
                    <a class="absolute left-0 top-0 h-full w-full" href="https://arstechnica.com/space/2024/10/ulas-second-vulcan-rocket-lost-part-of-its-booster-and-kept-going/?itm_source=parsely-api" aria-label="Read ULA’s second Vulcan rocket lost part of its booster and kept going">
                    </a>
                  </li>
                </ol>
              </div>
              <div class="most-read-customize text-center">
                <button class="btn-customize font-impact mt-5 inline-flex flex-row flex-nowrap items-center justify-center gap-2 font-semibold uppercase text-gray-300 hover:text-gray-100" aria-label="Customize view settings" x-data="" x-on:click="$dispatch('view-settings-bar-open');">
                  <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 39.8 40">
                    <defs>
                      <clipPath id="settings_svg__a">
                        <path fill="none" d="M0 0h39.8v40H0z"></path>
                      </clipPath>
                      <clipPath id="settings_svg__b">
                        <path fill="none" d="M0 0h39.8v40H0z"></path>
                      </clipPath>
                    </defs>
                    <g clip-path="url(#settings_svg__a)">
                      <g fill="currentColor" clip-path="url(#settings_svg__b)">
                        <path d="M17.4 3c-.8-1.8-2.5-3-4.5-3S9.1 1.2 8.3 3H0v4h8.3c.8 1.8 2.5 3 4.6 3s3.8-1.2 4.6-3h22.4V3H17.5Zm-4.6 4.5c-1.4 0-2.5-1.1-2.5-2.5s1.1-2.5 2.5-2.5 2.5 1.1 2.5 2.5-1.1 2.5-2.5 2.5M27 15c-2 0-3.8 1.2-4.6 3H0v4h22.4c.8 1.8 2.5 3 4.6 3s3.8-1.2 4.6-3h8.3v-4h-8.3c-.8-1.8-2.5-3-4.6-3m0 7.5c-1.4 0-2.5-1.1-2.5-2.5s1.1-2.5 2.5-2.5 2.5 1.1 2.5 2.5-1.1 2.5-2.5 2.5M12.9 30c-2 0-3.8 1.2-4.6 3H0v4h8.3c.8 1.8 2.5 3 4.6 3s3.8-1.2 4.6-3h22.4v-4H17.5c-.8-1.8-2.5-3-4.6-3m0 7.5c-1.4 0-2.5-1.1-2.5-2.5s1.1-2.5 2.5-2.5 2.5 1.1 2.5 2.5-1.1 2.5-2.5 2.5"></path>
                      </g>
                    </g>
                  </svg>
                  <span>Customize</span>
                </button>
              </div>
            </div>
          </div>
        </div>
        <div class="taboola-container border-t-gray-250 mx-auto my-5 mt-10 max-w-md border-t-4 px-5 pt-10 dark:border-t-gray-600 sm:max-w-3xl lg:max-w-6xl xl:px-0">
          <div id="taboola-below-article-thumbnails---at" class="trc_related_container trc_spotlight_widget tbl-trecs-container trc_elastic trc_elastic_thumbnails-a-6x1" data-placement-name="Below Article Thumbnails - AT">
            <div class="trc_rbox_container">
              <div>
                <div id="trc_wrapper_4224775392" class="trc_rbox thumbnails-a-6x1 trc-content-sponsored" style="overflow: hidden;">
                  <div id="trc_header_4224775392" class="trc_rbox_header trc_rbox_border_elm">
                    <div class="trc_header_ext">
                      <div class="logoDiv link-attribution"><a href="https://popup.taboola.com/en/?template=colorbox&amp;utm_source=condenast-arstechnica&amp;utm_medium=referral&amp;utm_content=thumbnails-a-6x1:Below Article Thumbnails - AT:" rel="nofollow sponsored noopener" target="_blank" class="trc_desktop_attribution_link trc_attribution_position_top"><span>by Taboola</span></a><a href="https://popup.taboola.com/en/?template=colorbox&amp;utm_source=condenast-arstechnica&amp;utm_medium=referral&amp;utm_content=thumbnails-a-6x1:Below Article Thumbnails - AT:" rel="nofollow sponsored noopener" target="_blank" class="trc_mobile_attribution_link trc_attribution_position_top"><span>by Taboola</span></a></div>
                      <div class="logoDiv link-disclosure attribution-disclosure-link-sponsored"><a href="https://popup.taboola.com/en/?template=colorbox&amp;utm_source=condenast-arstechnica&amp;utm_medium=referral&amp;utm_content=thumbnails-a-6x1:Below Article Thumbnails - AT:" rel="nofollow sponsored noopener" target="_blank" class="trc_desktop_disclosure_link trc_attribution_position_top"><span>Sponsored Links</span></a><a href="https://popup.taboola.com/en/?template=colorbox&amp;utm_source=condenast-arstechnica&amp;utm_medium=referral&amp;utm_content=thumbnails-a-6x1:Below Article Thumbnails - AT:" rel="nofollow sponsored noopener" target="_blank" class="trc_mobile_disclosure_link trc_attribution_position_top"><span>Sponsored Links</span></a></div>
                      <div class="logoDiv link-disclosure attribution-disclosure-link-hybrid"><a href="https://popup.taboola.com/en/?template=colorbox&amp;utm_source=condenast-arstechnica&amp;utm_medium=referral&amp;utm_content=thumbnails-a-6x1:Below Article Thumbnails - AT:" rel="nofollow sponsored noopener" target="_blank" class="trc_desktop_disclosure_link trc_attribution_position_top"><span>Promoted Links</span></a><a href="https://popup.taboola.com/en/?template=colorbox&amp;utm_source=condenast-arstechnica&amp;utm_medium=referral&amp;utm_content=thumbnails-a-6x1:Below Article Thumbnails - AT:" rel="nofollow sponsored noopener" target="_blank" class="trc_mobile_disclosure_link trc_attribution_position_top"><span>Promoted Links</span></a></div>
                    </div>
                    <span role="heading" aria-level="3" class="trc_rbox_header_span"></span>
                  </div>
                  <div id="outer_4224775392" class="trc_rbox_outer">
                    <div id="rbox-t2m" class="trc_rbox_div trc_rbox_border_elm">
                      <div id="internal_trc_4224775392">
                        <div data-item-id="~~V1~~201353287375225755~~TsQ4hNFTTgq2D1FbaNwnaD1uCJGtSSYPTeBRiHzYp-59_9IWyVTZYEw3zPc60dwzHu_TYvngZ3fIkOCpr71_kAqE0_yL1hRu0U20mv8_l14sJnhbc8pXpuxxdJaj5uIloN2RMrZZy4RF9y2z5PmSznzny3OjPVUmeSBo1Rdud4-Pty428XVfgjc-OnjEeXGBqpeC9Rh1PGpJm4tn_B-cVA" data-item-title="What happens if I pass away in Spain?" data-item-thumb="https://cdn.taboola.com/libtrc/static/thumbnails/STABLE_DIFFUSION/ESD/3861433f-65a3-47ff-a09e-7402afd681c5__wMH1MhQX.jpg" data-item-syndicated="true" class="videoCube trc_spotlight_item origin-default textItem thumbnail_top videoCube_1_child syndicatedItem trc-first-recommendation trc-spotlight-first-recommendation trc_excludable">
                          <a target="_blank" class="item-thumbnail-href" rel="nofollow noopener sponsored" href="https://comparefuneral.org/cf24-quote?utm_source=taboola&amp;utm_medium=referral&amp;tblci=GiBF3NT17H2p3q64K3Vn3R_XZFTvv8GLfX7r-7ueTIIMAiDEuWUozOzB94P3zIEbMNTtXA#tblciGiBF3NT17H2p3q64K3Vn3R_XZFTvv8GLfX7r-7ueTIIMAiDEuWUozOzB94P3zIEbMNTtXA" slot="thumbnail">
                            <div class="thumbBlock_holder">
                              <span role="img" aria-label="Image for Taboola Advertising Unit" class="thumbBlock"><span class="thumbnail-overlay"></span></span>
                              <div class="videoCube_aspect"></div>
                            </div>
                          </a>
                          <a title="What happens if I pass away in Spain?" target="_blank" class="item-label-href video-cta-style non-feed-cta-item" rel="nofollow noopener sponsored" href="https://comparefuneral.org/cf24-quote?utm_source=taboola&amp;utm_medium=referral&amp;tblci=GiBF3NT17H2p3q64K3Vn3R_XZFTvv8GLfX7r-7ueTIIMAiDEuWUozOzB94P3zIEbMNTtXA#tblciGiBF3NT17H2p3q64K3Vn3R_XZFTvv8GLfX7r-7ueTIIMAiDEuWUozOzB94P3zIEbMNTtXA">
                            <span class="video-label-box trc-main-label video-label-box-cta video-label-box-cta-non-ie">
                              <span class="video-label video-title video-label-flex-cta-item" slot="title" role="link" style="-webkit-line-clamp: 2; text-overflow: ellipsis; white-space: normal;">What happens if I pass away in Spain?</span><span class="branding video-branding-flex-cta-item" slot="branding" role="link" aria-label="Compare Funerals in Taboola advertising section">Compare Funerals</span>
                              <div class="video-cta-href"><button class="video-cta-button video-cta-style" style="font-family: &quot;Source Sans 3&quot;, &quot;Source Sans 3-fallback&quot;; color: rgb(189, 191, 199); border-color: rgb(203, 204, 208);">Learn More</button></div>
                            </span>
                          </a>
                          <div title="Remove this item" class="trc_user_exclude_btn"></div>
                          <div class="trc_exclude_overlay trc_fade"></div>
                          <div class="trc_exclude_undo_btn">Undo</div>
                        </div>
                        <div data-item-id="~~V1~~6795695080612361059~~t42s7_ach3bGEEygn4PNCtTwp6XuSsCSzvrNSB06DfvnoZueAsnM0UTkqRiz-o8uV9GNaet_FWxUAZ9PPPsFwaZe3kvCgAr7kPii_sMEM7oS5fznZoU0A69I_hhfkk85SL5u3ZnxqGrxCEHW8hyHH48XwLbI--WZuj80gUuMjcnclL-oGoLH5vtQ5o8NwjzrCyUaIPa8Ood5gvJ8oUMJEw" data-item-title="The CEO of OpenAI, Sam Altman, Shares the Nine Books He Thinks Will Change Your Life." data-item-thumb="https://cdn.taboola.com/libtrc/static/thumbnails/a22567ce8c3753329df77d5abd8ab467.jpg" data-item-syndicated="true" class="videoCube trc_spotlight_item origin-default textItem thumbnail_top videoCube_2_child syndicatedItem trc_excludable">
                          <a target="_blank" class="item-thumbnail-href" rel="nofollow noopener sponsored" href="https://www.blinkist.com/magazine/posts/nine-books-sam-altman?utm_source=taboola&amp;utm_medium=paid&amp;utm_campaign=20240826_TB_PRO_SamAltman-OnboardingTest_DW_Universal_MaxConversion-OnboardingMatrix&amp;utm_term=condenast-arstechnica&amp;utm_content=4030520339&amp;taboola_click_id=GiBF3NT17H2p3q64K3Vn3R_XZFTvv8GLfX7r-7ueTIIMAiCtlkEow7m1u_yN8Le4ATDU7Vw&amp;tblci=GiBF3NT17H2p3q64K3Vn3R_XZFTvv8GLfX7r-7ueTIIMAiCtlkEow7m1u_yN8Le4ATDU7Vw#tblciGiBF3NT17H2p3q64K3Vn3R_XZFTvv8GLfX7r-7ueTIIMAiCtlkEow7m1u_yN8Le4ATDU7Vw" slot="thumbnail">
                            <div class="thumbBlock_holder">
                              <span role="img" aria-label="Image for Taboola Advertising Unit" class="thumbBlock"><span class="thumbnail-overlay"></span></span>
                              <div class="videoCube_aspect"></div>
                            </div>
                          </a>
                          <a title="The CEO of OpenAI, Sam Altman, Shares the Nine Books He Thinks Will Change Your Life." target="_blank" class="item-label-href" rel="nofollow noopener sponsored" href="https://www.blinkist.com/magazine/posts/nine-books-sam-altman?utm_source=taboola&amp;utm_medium=paid&amp;utm_campaign=20240826_TB_PRO_SamAltman-OnboardingTest_DW_Universal_MaxConversion-OnboardingMatrix&amp;utm_term=condenast-arstechnica&amp;utm_content=4030520339&amp;taboola_click_id=GiBF3NT17H2p3q64K3Vn3R_XZFTvv8GLfX7r-7ueTIIMAiCtlkEow7m1u_yN8Le4ATDU7Vw&amp;tblci=GiBF3NT17H2p3q64K3Vn3R_XZFTvv8GLfX7r-7ueTIIMAiCtlkEow7m1u_yN8Le4ATDU7Vw#tblciGiBF3NT17H2p3q64K3Vn3R_XZFTvv8GLfX7r-7ueTIIMAiCtlkEow7m1u_yN8Le4ATDU7Vw"><span class="video-label-box trc-main-label"><span class="video-label video-title" slot="title" role="link" style="-webkit-line-clamp: 3; text-overflow: ellipsis; white-space: normal;">The CEO of OpenAI, Sam Altman, Shares the Nine Books He Thinks Will Change Your Life.</span><span class="branding" slot="branding" role="link" aria-label="Blinkist: Sam Altman's Reading List in Taboola advertising section">Blinkist: Sam Altman's Reading List</span></span></a>
                          <div title="Remove this item" class="trc_user_exclude_btn"></div>
                          <div class="trc_exclude_overlay trc_fade"></div>
                          <div class="trc_exclude_undo_btn">Undo</div>
                        </div>
                        <div data-item-id="~~V1~~6476049129035791149~~wi_AA14powG4RrHzmgt_fcaAIBZsmyqbLTjFcX6jzeLkc1MwgRREHD_eZSuV_YDligAEVXYvm0EmTWNJMhilwjowmD1dHFsZAkeJWqwe71105idO8DP_ZjuajOPbbe8Vktuh36TVo6v9KUVKk0pJ3b_hdQhkW60aiH8xyL168M66rXzQfT0owwZYoOtr28H3" data-item-title="Do you have a mouse and a PC?" data-item-thumb="https://cdn.taboola.com/libtrc/static/thumbnails/77adb047d288700dfd14b3763d5460ee.jpg" data-item-syndicated="true" class="videoCube trc_spotlight_item origin-default textItem thumbnail_top videoCube_3_child syndicatedItem trc_excludable">
                          <a target="_blank" class="item-thumbnail-href" rel="nofollow noopener sponsored" href="https://www.combatsiege.com?r=tabcs1wwp&amp;clickid=GiBF3NT17H2p3q64K3Vn3R_XZFTvv8GLfX7r-7ueTIIMAiD8gFso5f3j_d7VxrboATDU7Vw&amp;utm_source=taboola-condenast-arstechnica&amp;utm_medium=tabcs1wwp+gameplay&amp;utm_term=Do+you+have+a+mouse+and+a+PC%3F&amp;tblci=GiBF3NT17H2p3q64K3Vn3R_XZFTvv8GLfX7r-7ueTIIMAiD8gFso5f3j_d7VxrboATDU7Vw#tblciGiBF3NT17H2p3q64K3Vn3R_XZFTvv8GLfX7r-7ueTIIMAiD8gFso5f3j_d7VxrboATDU7Vw" slot="thumbnail">
                            <div class="thumbBlock_holder">
                              <span role="img" aria-label="Image for Taboola Advertising Unit" class="thumbBlock"><span class="thumbnail-overlay"></span></span>
                              <div class="videoCube_aspect"></div>
                            </div>
                          </a>
                          <a title="Do you have a mouse and a PC?" target="_blank" class="item-label-href video-cta-style non-feed-cta-item" rel="nofollow noopener sponsored" href="https://www.combatsiege.com?r=tabcs1wwp&amp;clickid=GiBF3NT17H2p3q64K3Vn3R_XZFTvv8GLfX7r-7ueTIIMAiD8gFso5f3j_d7VxrboATDU7Vw&amp;utm_source=taboola-condenast-arstechnica&amp;utm_medium=tabcs1wwp+gameplay&amp;utm_term=Do+you+have+a+mouse+and+a+PC%3F&amp;tblci=GiBF3NT17H2p3q64K3Vn3R_XZFTvv8GLfX7r-7ueTIIMAiD8gFso5f3j_d7VxrboATDU7Vw#tblciGiBF3NT17H2p3q64K3Vn3R_XZFTvv8GLfX7r-7ueTIIMAiD8gFso5f3j_d7VxrboATDU7Vw">
                            <span class="video-label-box trc-main-label video-label-box-cta video-label-box-cta-non-ie">
                              <span class="video-label video-title video-label-flex-cta-item" slot="title" role="link" style="-webkit-line-clamp: 2; text-overflow: ellipsis; white-space: normal;">Do you have a mouse and a PC?</span><span class="branding video-branding-flex-cta-item" slot="branding" role="link" aria-label="CombatSiege in Taboola advertising section">CombatSiege</span>
                              <div class="video-cta-href"><button class="video-cta-button video-cta-style" style="font-family: &quot;Source Sans 3&quot;, &quot;Source Sans 3-fallback&quot;; color: rgb(189, 191, 199); border-color: rgb(203, 204, 208);">Play Now</button></div>
                            </span>
                          </a>
                          <div title="Remove this item" class="trc_user_exclude_btn"></div>
                          <div class="trc_exclude_overlay trc_fade"></div>
                          <div class="trc_exclude_undo_btn">Undo</div>
                        </div>
                        <div data-item-id="~~V1~~-5785280730926516208~~JAySCMzCSdxDRlTwKpVvPnWstGhG4emAbv-op9HrvCBX0Y1p638VbFQBn088-wXBd1tV-adOHp6M2NLjFyeVBibyxfFngL2iImPTyoIxL2ba1YTWO_c0HQul5LYKIXfmjxfAtsj75Zm6PzSBS4yNydyUv6gagsfm-1Dmjw3CPOsLJRog9rw6h3mC8nyhQwkT" data-item-title="Why Are Villas In Dubai So Cheap?" data-item-thumb="https://cdn.taboola.com/libtrc/static/thumbnails/35bf8b93c1eff82cc509a6f306747c03.jpg" data-item-syndicated="true" class="videoCube trc_spotlight_item origin-default textItem thumbnail_top videoCube_4_child syndicatedItem trc_excludable">
                          <a target="_blank" class="item-thumbnail-href" rel="nofollow noopener sponsored" href="https://techtome.net/article/a-sneak-peek-what-to-expect-with-dubai-villa-prices-in-2024?utm_term=dubai%20villa%20holidays,%20villas%20for%20rent%20in%20dubai,%20Palm%20Jumeirah%20villas%20for%20rent,%20cheap%20villas%20for%20rent%20in%20dubai,%20dubai%20luxury%20villa%20rental,%20dubai%20vacation%20villas,%20dubai%20villa%20vacation%20rentals,%20holiday%20villas%20to%20rent%20on%20the%20Palm%20dubai,%20beach%20holiday%20dubai,%20lux%20holiday%20home%20dubai&amp;utm_content=Dubai%20villa%20prices%202024,%20Best%20Dubai%20villa%20deals,%20Top%20villa%20packages,%20Dubai%202024%20vacations,%20Leading%20villa%20offers,%20Dubai%20villa%20holidays&amp;camp_id=373436&amp;click_id=GiBF3NT17H2p3q64K3Vn3R_XZFTvv8GLfX7r-7ueTIIMAiDO1mkosoXzweH5rs_IATDU7Vw&amp;placement=1521364&amp;utm_source=taboola&amp;utm_medium=referral&amp;tblci=GiBF3NT17H2p3q64K3Vn3R_XZFTvv8GLfX7r-7ueTIIMAiDO1mkosoXzweH5rs_IATDU7Vw#tblciGiBF3NT17H2p3q64K3Vn3R_XZFTvv8GLfX7r-7ueTIIMAiDO1mkosoXzweH5rs_IATDU7Vw" slot="thumbnail">
                            <div class="thumbBlock_holder">
                              <span role="img" aria-label="Image for Taboola Advertising Unit" class="thumbBlock"><span class="thumbnail-overlay"></span></span>
                              <div class="videoCube_aspect"></div>
                            </div>
                          </a>
                          <a title="Why Are Villas In Dubai So Cheap?" target="_blank" class="item-label-href video-cta-style non-feed-cta-item" rel="nofollow noopener sponsored" href="https://techtome.net/article/a-sneak-peek-what-to-expect-with-dubai-villa-prices-in-2024?utm_term=dubai%20villa%20holidays,%20villas%20for%20rent%20in%20dubai,%20Palm%20Jumeirah%20villas%20for%20rent,%20cheap%20villas%20for%20rent%20in%20dubai,%20dubai%20luxury%20villa%20rental,%20dubai%20vacation%20villas,%20dubai%20villa%20vacation%20rentals,%20holiday%20villas%20to%20rent%20on%20the%20Palm%20dubai,%20beach%20holiday%20dubai,%20lux%20holiday%20home%20dubai&amp;utm_content=Dubai%20villa%20prices%202024,%20Best%20Dubai%20villa%20deals,%20Top%20villa%20packages,%20Dubai%202024%20vacations,%20Leading%20villa%20offers,%20Dubai%20villa%20holidays&amp;camp_id=373436&amp;click_id=GiBF3NT17H2p3q64K3Vn3R_XZFTvv8GLfX7r-7ueTIIMAiDO1mkosoXzweH5rs_IATDU7Vw&amp;placement=1521364&amp;utm_source=taboola&amp;utm_medium=referral&amp;tblci=GiBF3NT17H2p3q64K3Vn3R_XZFTvv8GLfX7r-7ueTIIMAiDO1mkosoXzweH5rs_IATDU7Vw#tblciGiBF3NT17H2p3q64K3Vn3R_XZFTvv8GLfX7r-7ueTIIMAiDO1mkosoXzweH5rs_IATDU7Vw">
                            <span class="video-label-box trc-main-label video-label-box-cta video-label-box-cta-non-ie">
                              <span class="video-label video-title video-label-flex-cta-item" slot="title" role="link" style="-webkit-line-clamp: 2; text-overflow: ellipsis; white-space: normal;">Why Are Villas In Dubai So Cheap?</span><span class="branding video-branding-flex-cta-item" slot="branding" role="link" aria-label="Dubai Villas | Search Ads in Taboola advertising section">Dubai Villas | Search Ads</span>
                              <div class="video-cta-href"><button class="video-cta-button video-cta-style" style="font-family: &quot;Source Sans 3&quot;, &quot;Source Sans 3-fallback&quot;; color: rgb(189, 191, 199); border-color: rgb(203, 204, 208);">Learn More</button></div>
                            </span>
                          </a>
                          <div title="Remove this item" class="trc_user_exclude_btn"></div>
                          <div class="trc_exclude_overlay trc_fade"></div>
                          <div class="trc_exclude_undo_btn">Undo</div>
                        </div>
                        <div data-item-id="~~V1~~6710945792367219864~~JJH3Rg2M8UI2SJ5fp_6EFg7G_3FZS16vFt69jyTiry7noZueAsnM0UTkqRiz-o8u-G79zK3W40OqQv6uruOC3IoA01xpp8L7RA_btY9496P9IRRe28NV-GyJAXddvyWS_goEz5txUAvQ_XkJL9la8Fb68Q1J9p-lhvn4m41lm9OxmSl3XELVqR2od0Ta50tlcaTnJ6GGmJyJeC3Y0WfugcXE8N28MsY5yUJNK1bqQ6bEnVKVD0UoXiHrlrNU7yaNMO7Tswj5WH4cylUjQLFLHQ" data-item-title="IQ Test: What is Your IQ? | Answer 30 Questions to Find Out!" data-item-thumb="https://cdn.taboola.com/libtrc/static/thumbnails/fbd458ff5fdf3b7771c440f3a1a4dd03.png" data-item-syndicated="true" class="videoCube trc_spotlight_item origin-default textItem thumbnail_top videoCube_5_child syndicatedItem trc_excludable">
                          <a target="_blank" class="item-thumbnail-href" rel="nofollow noopener sponsored" href="https://webeasyhit.com/cf/r/66e96e7d5736860012cda30d?click_id=GiBF3NT17H2p3q64K3Vn3R_XZFTvv8GLfX7r-7ueTIIMAiCDwV4ot8zxyo66hdtMMNTtXA&amp;site=condenast-arstechnica&amp;site_id=1521364&amp;title=IQ+Test%3A+What+is+Your+IQ%3F+%7C+Answer+30+Questions+to+Find+Out%21&amp;platform=Desktop&amp;campaign_id=39132732&amp;campaign_item_id=3946603052&amp;thumbnail=http%3A%2F%2Fcdn.taboola.com%2Flibtrc%2Fstatic%2Fthumbnails%2Ffbd458ff5fdf3b7771c440f3a1a4dd03.png&amp;camp_name=IQTestMC-EUR1-D-IQ-TB-IQ&amp;utm_source=taboola_39132732&amp;utm_term=condenast-arstechnica_1521364&amp;utm_content=3946603052&amp;utm_medium=GiBF3NT17H2p3q64K3Vn3R_XZFTvv8GLfX7r-7ueTIIMAiCDwV4ot8zxyo66hdtMMNTtXA&amp;utm_campaign=IQTestMC-EUR1-D-IQ-TB-IQ#tblciGiBF3NT17H2p3q64K3Vn3R_XZFTvv8GLfX7r-7ueTIIMAiCDwV4ot8zxyo66hdtMMNTtXA" slot="thumbnail">
                            <div class="thumbBlock_holder">
                              <span role="img" aria-label="Image for Taboola Advertising Unit" class="thumbBlock"><span class="thumbnail-overlay"></span></span>
                              <div class="videoCube_aspect"></div>
                            </div>
                          </a>
                          <a title="IQ Test: What is Your IQ? | Answer 30 Questions to Find Out!" target="_blank" class="item-label-href" rel="nofollow noopener sponsored" href="https://webeasyhit.com/cf/r/66e96e7d5736860012cda30d?click_id=GiBF3NT17H2p3q64K3Vn3R_XZFTvv8GLfX7r-7ueTIIMAiCDwV4ot8zxyo66hdtMMNTtXA&amp;site=condenast-arstechnica&amp;site_id=1521364&amp;title=IQ+Test%3A+What+is+Your+IQ%3F+%7C+Answer+30+Questions+to+Find+Out%21&amp;platform=Desktop&amp;campaign_id=39132732&amp;campaign_item_id=3946603052&amp;thumbnail=http%3A%2F%2Fcdn.taboola.com%2Flibtrc%2Fstatic%2Fthumbnails%2Ffbd458ff5fdf3b7771c440f3a1a4dd03.png&amp;camp_name=IQTestMC-EUR1-D-IQ-TB-IQ&amp;utm_source=taboola_39132732&amp;utm_term=condenast-arstechnica_1521364&amp;utm_content=3946603052&amp;utm_medium=GiBF3NT17H2p3q64K3Vn3R_XZFTvv8GLfX7r-7ueTIIMAiCDwV4ot8zxyo66hdtMMNTtXA&amp;utm_campaign=IQTestMC-EUR1-D-IQ-TB-IQ#tblciGiBF3NT17H2p3q64K3Vn3R_XZFTvv8GLfX7r-7ueTIIMAiCDwV4ot8zxyo66hdtMMNTtXA"><span class="video-label-box trc-main-label"><span class="video-label video-title" slot="title" role="link" style="-webkit-line-clamp: 3; text-overflow: ellipsis; white-space: normal;">IQ Test: What is Your IQ? | Answer 30 Questions to Find Out!</span><span class="branding" slot="branding" role="link" aria-label="IQ Test in Taboola advertising section">IQ Test</span></span></a>
                          <div title="Remove this item" class="trc_user_exclude_btn"></div>
                          <div class="trc_exclude_overlay trc_fade"></div>
                          <div class="trc_exclude_undo_btn">Undo</div>
                        </div>
                        <div data-item-id="~~V1~~3261907790371678394~~GvTcxqNfE1qbZIGiLztIySpRftvoYb4ozZJccBlgX-zpZyC7dGVdvKnSHsaHrNsFor4BFHsO6eE08LLg-CvdOB2tkbJ0H19gkwcUX8p5JFHbzsxfM0k_3_siGm-c3uad3Klku1m4DwJpqLkj_osiKIq14821I7fbRvWLMdivd3qALc70pFfLu2zvkZ_V3eB_o1g3OsJlMBOV0GYnbREpXO1ZDg8lbufPcttNu9_e3tfQOJ6esxe2_dHplaoNnXvbrkaE65XwdXesKUUIz_hTOA" data-item-title="Are You Smarter Than You Think? Take Our IQ Test Today!&nbsp;" data-item-thumb="https://cdn.taboola.com/libtrc/static/thumbnails/72b823ce2e0bc34a1a87d2208567650d.jpg" data-item-syndicated="true" class="videoCube trc_spotlight_item origin-default textItem thumbnail_top videoCube_6_child syndicatedItem trc_excludable">
                          <a target="_blank" class="item-thumbnail-href" rel="nofollow noopener sponsored" href="https://webeasyhit.com/cf/r/66ed5b37b1a8860012b8c3ad?click_id=GiBF3NT17H2p3q64K3Vn3R_XZFTvv8GLfX7r-7ueTIIMAiD1tGooid3z8teyo7xaMNTtXA&amp;site=condenast-arstechnica&amp;site_id=1521364&amp;title=Are+You+Smarter+Than+You+Think%3F+Take+Our+IQ+Test+Today%21%C2%A0&amp;platform=Desktop&amp;campaign_id=42348188&amp;campaign_item_id=4038817604&amp;thumbnail=http%3A%2F%2Fcdn.taboola.com%2Flibtrc%2Fstatic%2Fthumbnails%2F72b823ce2e0bc34a1a87d2208567650d.jpg&amp;camp_name=IQ+Int.+-+Taboola+-+EUR+-+D&amp;tblci=GiBF3NT17H2p3q64K3Vn3R_XZFTvv8GLfX7r-7ueTIIMAiD1tGooid3z8teyo7xaMNTtXA#tblciGiBF3NT17H2p3q64K3Vn3R_XZFTvv8GLfX7r-7ueTIIMAiD1tGooid3z8teyo7xaMNTtXA" slot="thumbnail">
                            <div class="thumbBlock_holder">
                              <span role="img" aria-label="Image for Taboola Advertising Unit" class="thumbBlock"><span class="thumbnail-overlay"></span></span>
                              <div class="videoCube_aspect"></div>
                            </div>
                          </a>
                          <a title="Are You Smarter Than You Think? Take Our IQ Test Today!&nbsp;" target="_blank" class="item-label-href video-cta-style non-feed-cta-item" rel="nofollow noopener sponsored" href="https://webeasyhit.com/cf/r/66ed5b37b1a8860012b8c3ad?click_id=GiBF3NT17H2p3q64K3Vn3R_XZFTvv8GLfX7r-7ueTIIMAiD1tGooid3z8teyo7xaMNTtXA&amp;site=condenast-arstechnica&amp;site_id=1521364&amp;title=Are+You+Smarter+Than+You+Think%3F+Take+Our+IQ+Test+Today%21%C2%A0&amp;platform=Desktop&amp;campaign_id=42348188&amp;campaign_item_id=4038817604&amp;thumbnail=http%3A%2F%2Fcdn.taboola.com%2Flibtrc%2Fstatic%2Fthumbnails%2F72b823ce2e0bc34a1a87d2208567650d.jpg&amp;camp_name=IQ+Int.+-+Taboola+-+EUR+-+D&amp;tblci=GiBF3NT17H2p3q64K3Vn3R_XZFTvv8GLfX7r-7ueTIIMAiD1tGooid3z8teyo7xaMNTtXA#tblciGiBF3NT17H2p3q64K3Vn3R_XZFTvv8GLfX7r-7ueTIIMAiD1tGooid3z8teyo7xaMNTtXA">
                            <span class="video-label-box trc-main-label video-label-box-cta video-label-box-cta-non-ie">
                              <span class="video-label video-title video-label-flex-cta-item" slot="title" role="link" style="-webkit-line-clamp: 3; text-overflow: ellipsis; white-space: normal;">Are You Smarter Than You Think? Take Our IQ Test Today!&nbsp;</span><span class="branding video-branding-flex-cta-item" slot="branding" role="link" aria-label="IQ International in Taboola advertising section">IQ International</span>
                              <div class="video-cta-href"><button class="video-cta-button video-cta-style" style="font-family: &quot;Source Sans 3&quot;, &quot;Source Sans 3-fallback&quot;; color: rgb(189, 191, 199); border-color: rgb(203, 204, 208);">Go To Quiz</button></div>
                            </span>
                          </a>
                          <div title="Remove this item" class="trc_user_exclude_btn"></div>
                          <div class="trc_exclude_overlay trc_fade"></div>
                          <div class="trc_exclude_undo_btn">Undo</div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="trc-widget-footer"></div>
                  <div class="trc_clearer"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <script type="text/javascript">
          window._taboola = window._taboola || [];
          _taboola.push({
            mode: 'thumbnails-a-6x1',
            container: 'taboola-below-article-thumbnails---at',
            placement: 'Below Article Thumbnails - AT',
            target_type: 'mix'
          });
        </script>
      </main>
      <div class="ad-wrapper is-fullwidth">
        <div class="ad-wrapper-inner">
          <div class="ad ad--footer">
            <div id="cns-ads-slot-type-footer-0" class="cns-ads-stage cns-ads-slot-type-footer cns-ads-slot-type-footer-0" data-name="footer_0" data-slot-type="footer" style="font-size: 0px; line-height: 0; overflow: hidden;">
              <div class="cns-ads-flex-sizer"></div>
              <div id="footer_0" class="cns-ads-container" style="margin: 0px auto; box-sizing: content-box;"></div>
            </div>
          </div>
        </div>
      </div>
      <footer class="site-footer bg-black">
        <div class="mx-auto max-w-6xl px-4 text-gray-300">
          <div class="justify-between gap-10 py-8 md:flex">
            <div class="site-footer-statement text-center md:w-3/5 md:text-left">
              <svg class="mb-6 inline h-10 md:mb-4 md:h-12 lg:h-14" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 436 144.1">
                <defs>
                  <clipPath id="ars-full-mono_svg__a">
                    <path fill="none" d="M0 0h436v144.1H0z"></path>
                  </clipPath>
                  <clipPath id="ars-full-mono_svg__b">
                    <path fill="none" d="M0 0h436v144.1H0z"></path>
                  </clipPath>
                </defs>
                <g clip-path="url(#ars-full-mono_svg__a)">
                  <g fill="currentColor" clip-path="url(#ars-full-mono_svg__b)">
                    <path d="M218.8 83.7c-4.4 3.7-8.4 5-12.8 5-7.7 0-12.7-5.3-13.5-14h24.8l.9-5.5h-25.7c.8-8.7 5.7-14.1 12.9-14.1s8.8 1.7 12.9 5.1l1-5.9c-4-2.9-8.8-4.4-13.7-4.3-10.7 0-19.2 7.8-19.2 21.9s8.3 21.9 18.9 21.9c5.2.1 10.2-1.6 14.3-4.8zm-48.7-27.5v36.9h-5.8V56.2h-13.4v-5.3H183l.9 5.3H170Zm74.5 37.6c-11.9 0-19.5-8.8-19.5-21.8s7.8-22 19.6-22c4.3-.1 8.5 1.1 12 3.5l-.9 5.9c-3.2-2.6-7.1-4-11.2-4.1-8.6 0-13.6 6.5-13.6 16.6s5.1 16.6 13.6 16.6c4.3 0 8.5-1.6 11.9-4.2l.9 5.4c-3.7 2.6-8.2 4.1-12.8 4.1M292 93V73.5h-21.4V93h-5.8V50.9h5.8v17.5H292V50.9h5.8V93zm42.9 0-23.2-32.8V93h-5.3V50.9h5.1l22.4 31.5V50.9h5.3V93zm13.4-42.1h5.8V93h-5.8zm32.6 42.9c-11.9 0-19.5-8.8-19.5-21.8s7.8-22 19.6-22c4.3-.1 8.5 1.1 12 3.5l-.9 5.9c-3.2-2.6-7.1-4-11.2-4.1-8.6 0-13.6 6.5-13.6 16.6s5.1 16.6 13.6 16.6c4.3 0 8.5-1.6 11.9-4.2l.9 5.4c-3.7 2.6-8.2 4.1-12.8 4.1m32.9-43.1h5.8l16.3 41.5-5.6 1.2-5-13.1h-17.4L403.1 93h-5.8zm-4 24.6h13.5l-6.8-17.9zM72 0C32.3 0 0 32.3 0 72.1s32.3 72.1 72 72.1 72.1-32.3 72.1-72.1S111.8 0 72 0M53 94h-6.6l-.9-5.9c-4 4.4-9.6 6.8-15.6 6.8-8 0-13-4.8-13-12.3 0-11 9.4-15.4 27.8-17.3v-1.9c0-5.6-3.3-7.5-8.4-7.5S25.8 57.6 21 59.7l-1.1-7.1c5.3-2.1 10.3-3.7 17.1-3.7 10.7 0 15.9 4.3 15.9 14.2v30.8Zm19.2-26v26H64V50h6.6l1.4 9c3.1-5 8.2-9.5 15.5-9.9l1.3 7.9c-7.4.3-13.6 5.2-16.6 11m37.2 26.9c-5.6-.1-11.1-1.6-16.1-4.2l1.2-7.8c4.6 3.2 10 5 15.6 5.1 5.6 0 9-2.1 9-5.8s-2.5-5.6-10.5-7.5C98.2 72.1 94.1 69 94.1 61.1s5.9-12.2 15.6-12.2c5 0 9.9 1 14.5 3l-1.3 7.8c-4.1-2.4-8.7-3.7-13.4-3.8-5 0-7.6 1.9-7.6 5.1s2.2 4.6 9.2 6.4c10.9 2.8 15.8 5.9 15.8 14.3s-6.1 13.2-17.5 13.2"></path>
                    <path d="M25.2 82.2c0 4.6 2.4 5.9 6.6 5.9s9.4-2.4 13.1-6.2V71.6c-16.3 1.6-19.7 6-19.7 10.6"></path>
                  </g>
                </g>
              </svg>
              <p>Ars Technica has been separating the signal from
                the noise for over 25 years. With our unique combination of
                technical savvy and wide-ranging interest in the technological arts
                and sciences, Ars is the trusted source in a sea of information. After
                all, you don’t need to know everything, only what’s important.
              </p>
              <p class="mt-4">
                <a href="https://twitter.com/arstechnica" aria-label="Follow Ars Technica on Twitter/X">
                  <svg class="inline h-12 w-12" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40">
                    <defs>
                      <clipPath id="twitter_svg__a">
                        <path fill="none" d="M0 0h40v40H0z"></path>
                      </clipPath>
                      <clipPath id="twitter_svg__b">
                        <path fill="none" d="M0 0h40v40H0z"></path>
                      </clipPath>
                    </defs>
                    <g clip-path="url(#twitter_svg__a)">
                      <g fill="none" clip-path="url(#twitter_svg__b)">
                        <path fill="currentColor" d="M16.3 28.1c7.5 0 11.7-6.3 11.7-11.7v-.5c.8-.6 1.5-1.3 2-2.1q-1.05.45-2.4.6c.9-.5 1.5-1.3 1.8-2.3-.8.5-1.7.8-2.6 1-.6-.7-1.4-1.1-2.3-1.2s-1.8 0-2.6.4-1.4 1.1-1.8 1.9-.5 1.7-.3 2.6c-1.6 0-3.2-.5-4.7-1.2s-2.7-1.8-3.8-3c-.5.9-.7 2-.5 3s.9 1.9 1.7 2.5c-.7 0-1.3-.2-1.9-.5q0 1.5.9 2.7c.6.7 1.4 1.2 2.4 1.4-.6.2-1.2.2-1.9 0 .3.8.8 1.5 1.5 2s1.5.8 2.4.8c-1.5 1.1-3.2 1.8-5.1 1.8h-1c1.9 1.2 4.1 1.8 6.3 1.8"></path>
                      </g>
                    </g>
                  </svg>
                </a>
                <a href="https://mastodon.social/arstechnica" aria-label="Follow Ars Technica on Mastodon">
                  <svg class="inline h-12 w-12" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40">
                    <defs>
                      <clipPath id="mastodon_svg__a">
                        <path fill="none" d="M0 0h40v40H0z"></path>
                      </clipPath>
                      <clipPath id="mastodon_svg__b">
                        <path fill="none" d="M0 0h40v40H0z"></path>
                      </clipPath>
                    </defs>
                    <g clip-path="url(#mastodon_svg__a)">
                      <g fill="none" clip-path="url(#mastodon_svg__b)">
                        <path fill="currentColor" d="M29.3 16.6c0-4.3-2.8-5.6-2.8-5.6-1.4-.7-3.9-.9-6.5-1-2.6 0-5 .3-6.4 1 0 0-2.8 1.3-2.8 5.6V20c.1 4.2.8 8.4 4.7 9.5 1.8.5 3.4.6 4.6.5 2.3-.1 3.5-.8 3.5-.8v-1.6s-1.7.5-3.5.4c-1.8 0-3.7-.2-4-2.4V25s1.8.4 4 .5c1.4 0 2.7 0 4-.2 2.5-.3 4.7-1.8 5-3.3.4-2.2.4-5.4.4-5.4Zm-3.4 5.6h-2.1v-5.1c0-1.1-.5-1.6-1.4-1.6s-1.5.6-1.5 1.9v2.8h-2.1v-2.8c0-1.3-.5-1.9-1.5-1.9s-1.4.5-1.4 1.6v5.1h-2.1v-5.3c0-1.1.3-1.9.8-2.6.6-.6 1.3-1 2.2-1s1.9.4 2.4 1.2l.5.9.5-.9q.75-1.2 2.4-1.2c1.65 0 1.7.3 2.2 1 .6.6.8 1.5.8 2.6v5.3Z"></path>
                      </g>
                    </g>
                  </svg>
                </a>
                <a href="https://www.facebook.com/arstechnica" aria-label="Follow Ars Technica on Facebook">
                  <svg class="inline h-12 w-12" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40">
                    <defs>
                      <clipPath id="facebook_svg__a">
                        <path fill="none" d="M0 0h40v40H0z"></path>
                      </clipPath>
                      <clipPath id="facebook_svg__b">
                        <path fill="none" d="M0 0h40v40H0z"></path>
                      </clipPath>
                    </defs>
                    <g clip-path="url(#facebook_svg__a)">
                      <g fill="none" clip-path="url(#facebook_svg__b)">
                        <path fill="currentColor" d="M17.3 13.9v2.8h-2v3.4h2v10h4.2v-10h2.8s.3-1.6.4-3.4h-3.2v-2.3c0-.3.5-.8.9-.8h2.3v-3.5h-3.1c-4.4 0-4.3 3.4-4.3 3.9"></path>
                      </g>
                    </g>
                  </svg>
                </a>
                <a href="https://www.youtube.com/@arstechnica" aria-label="Follow Ars Technica on YouTube">
                  <svg class="inline h-12 w-12" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40">
                    <defs>
                      <clipPath id="youtube_svg__a">
                        <path fill="none" d="M0 0h40v40H0z"></path>
                      </clipPath>
                      <clipPath id="youtube_svg__b">
                        <path fill="none" d="M0 0h40v40H0z"></path>
                      </clipPath>
                    </defs>
                    <g clip-path="url(#youtube_svg__a)">
                      <g fill="none" clip-path="url(#youtube_svg__b)">
                        <path fill="currentColor" d="M29.6 15.2c-.1-.4-.3-.8-.6-1.1s-.7-.5-1.1-.7c-1.6-.4-7.8-.4-7.8-.4s-6.3 0-7.8.4c-.4.1-.8.3-1.1.7-.3.3-.5.7-.6 1.1-.4 1.6-.4 4.8-.4 4.8s0 3.3.4 4.8c.*******.6 1.1s.7.5 1.1.7c1.6.4 7.8.4 7.8.4s6.3 0 7.8-.4c.4-.1.8-.3 1.1-.7s.5-.7.6-1.1c.4-1.6.4-4.8.4-4.8s0-3.3-.4-4.8M18 23v-5.9l5.2 3-5.2 3Z"></path>
                      </g>
                    </g>
                  </svg>
                </a>
                <a href="https://www.instagram.com/arstechnica/" aria-label="Follow Ars Technica on Instagram">
                  <svg class="inline h-12 w-12" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40">
                    <defs>
                      <clipPath id="instagram_svg__a">
                        <path fill="none" d="M0 0h40v40H0z"></path>
                      </clipPath>
                      <clipPath id="instagram_svg__b">
                        <path fill="none" d="M0 0h40v40H0z"></path>
                      </clipPath>
                    </defs>
                    <g clip-path="url(#instagram_svg__a)">
                      <g fill="none" clip-path="url(#instagram_svg__b)">
                        <path fill="currentColor" d="M20 10h4.1c1.1 0 1.8.2 *******.3 1.2.6 1.8 1.2s.9 1.1 1.2 1.8c.2.6.4 1.4.5 2.4v8.2c0 1.1-.2 1.8-.5 2.4-.3.7-.6 1.3-1.2 1.8-.6.6-1.1.9-1.8 1.2-.6.2-1.4.4-2.4.5h-8.2c-1.1 0-1.8-.2-2.4-.5-.7-.3-1.3-.6-1.8-1.2q-.75-.75-1.2-1.8c-.2-.6-.4-1.4-.5-2.4v-8.2c0-1.1.2-1.8.5-2.4.3-.7.6-1.2 1.2-1.8s1.1-.9 1.8-1.2c.6-.2 1.4-.4 2.4-.5zm0 2.5h-3.7c-.9 0-1.4.2-1.7.3-.4.1-.8.4-1.1.7s-.5.6-.7 1.1c-.1.3-.3.8-.3 1.7v7.4c0 .9.2 1.4.3 *******.4.7.7 *******.6.5 *******.1.8.3 1.7.3h7.4c.9 0 1.4-.2 1.7-.3.4-.2.7-.4 1.1-.7.3-.3.5-.6.7-1.1.1-.3.3-.8.3-1.7v-7.4c0-.9-.2-1.4-.3-1.7-.1-.4-.4-.8-.7-1.1s-.7-.5-1.1-.7c-.3-.1-.8-.3-1.7-.3zm0 2.2c.7 0 1.4.1 2 .4s1.2.7 1.7 1.1c.5.5.9 1.1 1.1 *******.4 1.3.4 2s-.1 1.4-.4 2-.7 1.2-1.1 1.7c-.5.5-1.1.9-1.7 1.1-.6.3-1.3.4-2 .4-1.4 0-2.7-.6-3.7-1.5-1-1-1.5-2.3-1.5-3.7s.6-2.7 1.5-3.7 2.3-1.5 3.7-1.5m0 8.3q1.2 0 2.1-.9T23 20c0-1.2-.3-1.5-.9-2.1q-.9-.9-2.1-.9c-1.2 0-1.5.3-2.1.9q-.9.9-.9 2.1c0 1.2.3 1.5.9 2.1q.9.9 2.1.9m6.6-8.1c0 .4-.2.7-.4 1s-.6.4-1 .4-.7-.2-1-.4c-.3-.3-.4-.6-.4-1s.2-.7.4-1c.3-.3.6-.4 1-.4s.7.2 1 .4c.3.3.4.6.4 1"></path>
                      </g>
                    </g>
                  </svg>
                </a>
              </p>
            </div>
            <div class="text-center md:w-1/5 md:text-left">
              <span class="font-impact mb-4 mt-6 block font-semibold uppercase">More
              from Ars
              </span>
              <ul id="menu-more_navigation" class="menu">
                <li id="menu-item-1971876" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1971876"><a href="https://arstechnica.com/about-us/">About Us</a></li>
                <li id="menu-item-1971877" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1971877"><a href="https://arstechnica.com/staff-directory/">Staff Directory</a></li>
                <li id="menu-item-1971878" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1971878"><a href="https://arstechnica.com/newsletters/">Newsletters</a></li>
                <li id="menu-item-1980432" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-1980432"><a href="https://video.arstechnica.com">Ars Videos</a></li>
                <li id="menu-item-1971879" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1971879"><a href="https://arstechnica.com/general-faq/">General FAQ</a></li>
                <li id="menu-item-1971880" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1971880"><a href="https://arstechnica.com/rss-feeds/">RSS Feeds</a></li>
              </ul>
            </div>
            <div class="text-center md:w-1/5 md:text-left">
              <span class="font-impact mb-4 mt-6 block font-semibold uppercase">Contact</span>
              <ul id="menu-contact_navigation" class="menu">
                <li id="menu-item-1971881" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1971881"><a href="https://arstechnica.com/contact-us/">Contact us</a></li>
                <li id="menu-item-1971884" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-1971884"><a target="_blank" rel="noopener" href="https://www.condenast.com/brands/ars-technica">Advertise with us</a></li>
                <li id="menu-item-1971882" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1971882"><a href="https://arstechnica.com/reprints/">Reprints</a></li>
              </ul>
            </div>
          </div>
          <div class="pb-10 pt-5" id="copyright-terms">
            <div class="mb-4 flex flex-row flex-nowrap items-center gap-2">
              <svg class="h-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 14">
                <path d="M7.4 12.8h6.8l3.1-11.6H7.4C4.2 1.2 1.6 3.8 1.6 7s2.6 5.8 5.8 5.8" style="fill-rule:evenodd;clip-rule:evenodd;fill:#fff"></path>
                <path d="M22.6 0H7.4c-3.9 0-7 3.1-7 7s3.1 7 7 7h15.2c3.9 0 7-3.1 7-7s-3.2-7-7-7m-21 7c0-3.2 2.6-5.8 5.8-5.8h9.9l-3.1 11.6H7.4c-3.2 0-5.8-2.6-5.8-5.8" style="fill-rule:evenodd;clip-rule:evenodd;fill:#06f"></path>
                <path d="M24.6 4c.******* 0 .8L22.5 7l2.2 2.2c.******* 0 .8s-.6.2-.8 0l-2.2-2.2-2.2 2.2c-.2.2-.6.2-.8 0s-.2-.6 0-.8L20.8 7l-2.2-2.2c-.2-.2-.2-.6 0-.8s.6-.2.8 0l2.2 2.2L23.8 4c.2-.2.6-.2.8 0" style="fill:#fff"></path>
                <path d="M12.7 4.1c.*******.1.8L8.6 9.8c-.1.1-.2.2-.3.2-.2.1-.5.1-.7-.1L5.4 7.7c-.2-.2-.2-.6 0-.8s.6-.2.8 0L8 8.6l3.8-4.5c.2-.2.6-.2.9 0" style="fill:#06f"></path>
              </svg>
              <a class="ot-sdk-show-settings ot-sdk-btn--visible" id="ot-sdk-btn">Manage Preferences</a>
            </div>
            © 2024 Condé Nast. All rights reserved. Use of and/or
            registration on any portion of this site constitutes acceptance of our <a href="https://www.condenast.com/user-agreement/">User Agreement</a> and
            <a href="https://www.condenast.com/privacy-policy/">Privacy Policy and
            Cookie Statement</a> and <a href="/amendment-to-conde-nast-user-agreement-privacy-policy/">Ars
            Technica Addendum</a> and <a href="https://www.condenast.com/privacy-policy/#california">Your
            California Privacy Rights</a>. Ars Technica may earn compensation on
            sales from links on this site. <a href="/affiliate-link-policy/">Read our
            affiliate link policy</a>. The material on this site may not be
            reproduced, distributed, transmitted, cached or otherwise used, except
            with the prior written permission of Condé Nast. <a href="https://www.aboutads.info/">Ad
            Choices</a>
          </div>
        </div>
      </footer>
    </div>
    <iframe style="display:none;height:0;opacity:0;visibility:hidden;border:0;width:0;height:0;" name="__uspapiLocator"></iframe>
    <script type="text/javascript" src="https://s.skimresources.com/js/100098X1555750.skimlinks.js"></script><iframe style="display: none;" name="__tcfapiLocator" title="CMP Locator"></iframe><iframe src="https://cdn.taboola.com/libtrc/static/topics/taboola-browsing-topics.html" id="tbl-browsing-topics-iframe-69.59777317212303" style="width: 0px; height: 0px; display: none;"></iframe><iframe id="skimlinks-pixels-iframe" width="0" height="0" style="display: none;"></iframe>
    <script>
      (function() {
        const div = document.querySelector('.ars-interlude-container');
        if (!div) {
          return;
        }
      
        // Exclude on sponsored posts
        if (document.querySelector('.single-ars_sponsored_post')) {
          return;
        }
      
        // If on an article page and the interlude container exists
        if (document.querySelector('body.single')) {
          const parent = div.parentElement;
      
          // Get all the top level elements in the parent that aren't the interlude container
          const elems = Array.from(parent.children).filter((elem) => elem !== div);
          // Loop over the elements in reverse order
          for (let i = elems.length - 1; i >= 0; i--) {
            const elem = elems[i];
            // If the next element isn't one of: h1, h2, h3, h4, h5, h6, or div, insert the interlude container before it
            const nextElem = elems[i - 1];
            if (nextElem && !['H1', 'H2', 'H3', 'H4', 'H5', 'H6', 'DIV'].includes(nextElem.tagName)) {
              // Add .my-5 to the interlude container
              div.classList.add('my-5');
              parent.insertBefore(div, elem);
              break;
            }
          }
        }
        const src =
          'https://player.cnevids.com/interlude/arstechnica.js';
        const s = document.createElement('script');
        s.setAttribute('async', true);
        s.setAttribute('src', src);
        document.body.appendChild(s);
      })();
    </script><script async="true" src="https://player.cnevids.com/interlude/arstechnica.js"></script>
    <!-- Parse.ly start -->
    <!-- Parse.ly end -->
    <script id="snowplow-js-before">
      window.snowplowQueue = window.snowplowQueue || []; window.snowplowContexts = {"site":{"orgId":"4gKgcFGUFUvCGFzHakTPfYp85Yi8","orgAppId":null,"appVersion":null,"env":"production"},"content":{"functionalTags":null,"hasBuyButtons":null,"noOfRevisions":null,"editorNames":null,"author_name":"Ars Staff","contentId":"1113493","contentLength":2,"contentTitle":"More than a decade later, how do original YouTube stars feel about the site?","contentSource":"web","authorIds":"10","publishDate":"2017-06-11T14:00:33Z","modifiedDate":"2017-06-11T14:11:02Z","tags":"","contentLang":"en-US","galleryName":null,"totalGalleryImages":null,"wordCount":3006,"contentType":null,"templateType":"article_standard_two_column","primaryTag":null,"contentFlag":"news","isCommerceContent":null,"pageTypeProperties":null,"section":"features","subsection":null,"subsection2":null,"dataSource":"web","content_type":"article"},"syndication":{"content":null,"originalSource":null,"originalContentLanguage":null},"page":{"canonical":"https:\/\/arstechnica.com\/features\/2017\/06\/youtube-changed-my-life-a-pair-of-original-videostars-ponder-a-life-lived-online\/","syndicatorUrl":null},"user":{"amguuid":null}}; window.snowplowConfig = {"SNOWPLOW_COLLECTOR":"c.arstechnica.com","SNOWPLOW_SCRIPT":"https:\/\/globalservices.conde.digital\/p77xzrbz9z.js","AVO_API_KEY":"FTJO6mVPBIzdGhjn2Ruy","APP_ID":"ars-technica","APP_NAME":"ars-technica","APP_ENV":"production","APP_VERSION":"1.0.0","COOKIE_DOMAIN":".arstechnica.com"};
    </script>
    <script src="https://cdn.arstechnica.net/wp-content/mu-plugins/ars-snowplow/ars-snowplow-js/dist/main-1-0-4.js?ver=1.0.4" id="snowplow-js"></script>
    <script src="https://cdn.arstechnica.net/wp-content/plugins/article-forum-connect/public/js/iframe-resizer.parent.js?ver=5.3.1" id="article_forum_connect_iframe_resizer-js"></script>
    <script src="https://cdn.arstechnica.net/wp-content/plugins/article-forum-connect/public/js/iframe.js?ver=1.2.4" id="article_forum_connect_iframe-js"></script>
    <script id="app/0-js-before">
      !function(){"use strict";var r,n={},t={};function e(r){var o=t[r];if(void 0!==o)return o.exports;var u=t[r]={exports:{}};return n[r](u,u.exports,e),u.exports}e.m=n,r=[],e.O=function(n,t,o,u){if(!t){var i=1/0;for(c=0;c<r.length;c++){t=r[c][0],o=r[c][1],u=r[c][2];for(var f=!0,s=0;s<t.length;s++)(!1&u||i>=u)&&Object.keys(e.O).every((function(r){return e.O[r](t[s])}))?t.splice(s--,1):(f=!1,u<i&&(i=u));if(f){r.splice(c--,1);var a=o();void 0!==a&&(n=a)}}return n}u=u||0;for(var c=r.length;c>0&&r[c-1][2]>u;c--)r[c]=r[c-1];r[c]=[t,o,u]},e.o=function(r,n){return Object.prototype.hasOwnProperty.call(r,n)},function(){var r={666:0};e.O.j=function(n){return 0===r[n]};var n=function(n,t){var o,u,i=t[0],f=t[1],s=t[2],a=0;if(i.some((function(n){return 0!==r[n]}))){for(o in f)e.o(f,o)&&(e.m[o]=f[o]);if(s)var c=s(e)}for(n&&n(t);a<i.length;a++)u=i[a],e.o(r,u)&&r[u]&&r[u][0](),r[u]=0;return e.O(c)},t=self.webpackChunk_roots_bud_sage=self.webpackChunk_roots_bud_sage||[];t.forEach(n.bind(null,0)),t.push=n.bind(null,t.push.bind(t))}()}();
    </script>
    <script src="https://cdn.arstechnica.net/wp-content/themes/ars-v9/public/js/app.c8d71a.js" id="app/0-js"></script>
    <div class="fixed inset-0 z-50 overflow-y-auto" role="dialog" aria-modal="true" x-show="open" x-on:keydown.escape.window.prevent.stop="open = false" x-id="['modal-title']" x-ref="panel" :aria-labelledby="$id('modal-title')" data-teleport-target="true" aria-labelledby="modal-title-1" style="display: none;">
      <div class="fixed inset-0 bg-slate-900/80 opacity-100 backdrop-blur" x-show="open" x-transition.duration.150ms="" style="display: none;">
      </div>
      <div class="relative flex min-h-screen items-center justify-center" x-on:click="open = false" x-show="open" x-transition.duration.150ms="" style="display: none;">
        <div x-on:click.stop="" x-trap.noscroll.inert="open">
          <span class="sr-only" :id="$id('modal-title')" id="modal-title-1">
          Search dialog...
          </span>
          <div class="relative min-w-[640px]">
            <form class="search-form" role="search" method="get" action="https://arstechnica.com/">
              <label>
              <span class="sr-only">
              Search for:
              </span>
              <input name="s" type="search" value="" placeholder="Search …">
              </label>
              <button aria-label="Search">Search</button>
            </form>
          </div>
        </div>
      </div>
    </div>
    <div class="fixed inset-0 z-50 overflow-y-auto" role="dialog" aria-modal="true" x-show="open" x-on:keydown.escape.window.prevent.stop="open = false" x-id="['modal-title']" x-ref="panel" :aria-labelledby="$id('modal-title')" data-teleport-target="true" aria-labelledby="modal-title-2" style="display: none;">
      <div class="fixed inset-0 bg-slate-900/80 opacity-100 backdrop-blur" x-show="open" x-transition.duration.150ms="" style="display: none;">
      </div>
      <div class="relative flex min-h-screen items-center justify-center" x-on:click="open = false" x-show="open" x-transition.duration.150ms="" style="display: none;">
        <div x-on:click.stop="" x-trap.noscroll.inert="open">
          <span class="sr-only" :id="$id('modal-title')" id="modal-title-2">
          Sign in dialog...
          </span>
          <div class="sign-in-panel absolute left-1/2 top-1/2 w-3/4 min-w-[320px] max-w-xl -translate-x-1/2 -translate-y-1/2">
            <header class="font-impact flex items-center justify-between bg-gray-600 px-7 py-4 font-semibold uppercase">
              <div class="text-gray-350 flex items-center gap-3">
                <svg class="h-3 w-3 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40">
                  <defs>
                    <clipPath id="arrow-blocks-right_svg__a">
                      <path fill="none" d="M0 0h40v40H0z"></path>
                    </clipPath>
                  </defs>
                  <g fill="currentColor" clip-path="url(#arrow-blocks-right_svg__a)">
                    <path d="M32 16h8v8h-8zm-8 8h8v8h-8zm-8 8h8v8h-8zm8-24h8v8h-8zm-8-8h8v8h-8zM0 16h16v8H0z"></path>
                  </g>
                </svg>
                Sign in
              </div>
              <button class="text-gray-300 hover:text-gray-100 focus:text-gray-100" x-on:click="open = false">
                <svg class="h-3 w-3" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 34.7 40">
                  <defs>
                    <clipPath id="x_svg__a">
                      <path fill="none" d="M0 0h34.7v40H0z"></path>
                    </clipPath>
                  </defs>
                  <g fill="none" clip-path="url(#x_svg__a)">
                    <path fill="currentColor" d="m26.4 0-8.5 16.9h-1.1L8.3 0H.8l10.1 19.4L0 40h7.6l9.2-18.3h1.1L27.1 40h7.6L23.8 19.4 33.9 0z"></path>
                  </g>
                </svg>
              </button>
            </header>
            <div class="sign-in-panel-body bg-gray-700 px-7 py-4">
              <div class="col-span-3 normal-case text-gray-300" x-data="{ html: '', form: '', triggered: false }" x-on:modal-opened.window="
                panel = $el.parentElement.parentElement.parentElement.parentElement.parentElement;
                if (triggered || panel !== event.detail.panel) {
                return;
                }
                triggered = true;
                html = await (await fetch('/civis/login')).text();
                // Parse html for form with action=/civis/login/login
                parser = new DOMParser();
                doc = parser.parseFromString(html, 'text/html');
                form = doc.querySelector('form[action=&quot;/civis/login/login&quot;]');
                // Remove autofocus and set focus to username field
                username = form.querySelector('input[name=&quot;login&quot;]');
                username.removeAttribute('autofocus');
                document.querySelector('.sign-in-form').appendChild(form);
                username.focus();
                ">
                <div class="sign-in-form"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <script src="https://cdn.arstechnica.net/wp-content/themes/ars-v9/public/js/ads.b27c16.js" id="ads/0-js"></script>
    <script src="https://cdn.arstechnica.net/wp-content/themes/ars-v9/public/js/stats.eaf6cb.js" id="stats/0-js"></script>
    <iframe marginwidth="0" marginheight="0" scrolling="no" frameborder="0" id="16a4308f88dbd7" width="0" height="0" src="about:blank" name="__pb_locator__" style="display: none; height: 0px; width: 0px; border: 0px;"></iframe>
    <div style="position: absolute; z-index: -1;">
      <div class="ad__slot ad__slot--out-of-page" "=""></div>
    </div>
    <script type="text/javascript" id="">var b=navigator.userAgent,h=3;
      if(window.webkitRequestFileSystem)webkitRequestFileSystem(TEMPORARY,1,function(){dataLayer.push({event:"privacy-mode-false"})},function(){dataLayer.push({event:"privacy-mode-true"})});else if(-1<b.indexOf("Firefox")&&window.indexedDB)b=indexedDB.open("test"),b.onsuccess=function(){dataLayer.push({event:"privacy-mode-false"})},b.onerror=function(){dataLayer.push({event:"privacy-mode-true"})};else if(-1<b.indexOf("Edge")||(h=/(?:MSIE|rv:)\s?([\d\.]+)/.exec(b))&&10<=parseInt(h[1],10))privacyMode=!window.indexedDB,
      dataLayer.push({event:"privacy-mode-"+privacyMode.toString()});else if(-1<b.indexOf("Safari")&&window.localStorage)try{privateMode=!openDatabase(null,null,null,null),localStorage.setItem("test",1),localStorage.removeItem("test"),dataLayer.push({event:"privacy-mode-false"})}catch(a){dataLayer.push({event:"privacy-mode-true"})};
    </script>
    <div id="amzn-assoc-ad-e6160dfa-32a7-4b0e-9675-d18902339f1e"></div>
    <script type="text/javascript" id="" src="https://z-na.associates-amazon.com/onetag/v2?MarketPlace=US&amp;instanceId=e6160dfa-32a7-4b0e-9675-d18902339f1e"></script>
    <div id="onetrust-consent-sdk" data-nosnippet="true">
      <div class="onetrust-pc-dark-filter ot-fade-in" style="display: none;transition: visibility 0s 400ms, opacity 400ms linear;
        opacity: 0;visibility: hidden;z-index:2147483645;"></div>
      <div id="onetrust-banner-sdk" class="otCenterRounded ot-iab-2 default vertical-align-content" tabindex="0" role="region" aria-label="Cookie banner" style="display: none;
        transition: visibility 0s 400ms, opacity 400ms linear;
        opacity: 0;visibility: hidden;">
        <div class="ot-sdk-container" role="dialog" aria-modal="true" aria-label="We Care About Your Privacy">
          <div class="ot-sdk-row">
            <div id="onetrust-group-container" class="ot-sdk-twelve ot-sdk-columns">
              <div id="onetrust-policy">
                <div class="banner-header">
                  <div class="banner_logo"></div>
                </div>
                <h2 id="onetrust-policy-title">We Care About Your Privacy</h2>
                <div id="onetrust-policy-text">We and our <span class="ot-tcf2-vendor-count ot-text-bold">192</span> partners store and/or access information on a device, such as unique IDs in cookies to process personal data. You may accept or manage your choices by clicking below, including your right to object where legitimate interest is used, or at any time in the privacy policy page. These choices will be signaled to our partners and will not affect browsing data.<a class="ot-cookie-policy-link" href="https://www.condenast.com/privacy-policy" aria-label="More information about your privacy, opens in a new tab" rel="noopener" target="_blank">More information about your privacy</a></div>
                <div class="ot-dpd-container">
                  <h3 class="ot-dpd-title">We and our partners process data to provide:</h3>
                  <div class="ot-dpd-content">
                    <p class="ot-dpd-desc">Use precise geolocation data. Actively scan device characteristics for identification. Store and/or access information on a device. Personalised advertising and content, advertising and content measurement, audience research and services development. <button class="ot-link-btn onetrust-vendors-list-handler">
                      List of Partners (vendors)
                      </button>
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div id="onetrust-button-group-parent" class="ot-sdk-twelve ot-sdk-columns">
              <div id="onetrust-button-group">
                <div class="banner-actions-container"> <button id="onetrust-accept-btn-handler">I Accept</button></div>
                <button id="onetrust-pc-btn-handler">Your Privacy Choices</button>
              </div>
            </div>
            <!-- Close Button -->
            <div id="onetrust-close-btn-container"></div>
            <!-- Close Button END-->
          </div>
        </div>
      </div>
    </div>
    <iframe src="https://cdn.permutive.app/topics.html" style="display: none;"></iframe><iframe name="__launchpadLocator" style="display: none;"></iframe><script type="text/javascript" class="optanon-category-C0002" id="parsely-cfg" src="//fpa-cdn.arstechnica.com/keys/arstechnica.com/p.js"></script>
    <script type="text/javascript" id="">var _comscore=_comscore||[];_comscore.push({c1:"2",c2:"6035094",options:{enableFirstPartyCookie:!0,bypassUserConsentRequirementFor1PCookie:!0}});(function(){var a=document.createElement("script"),b=document.getElementsByTagName("script")[0];a.async=!0;a.src="https://sb.scorecardresearch.com/cs/6035094/beacon.js";b.parentNode.insertBefore(a,b)})();</script>
    <script type="text/javascript" id="" src="https://a.ad.gt/api/v1/u/matches/57"></script><script type="text/javascript" id="">_linkedin_partner_id="434737";window._linkedin_data_partner_ids=window._linkedin_data_partner_ids||[];window._linkedin_data_partner_ids.push(_linkedin_partner_id);</script><script type="text/javascript" id="">(function(){var b=document.getElementsByTagName("script")[0],a=document.createElement("script");a.type="text/javascript";a.async=!0;a.src="https://snap.licdn.com/li.lms-analytics/insight.min.js";b.parentNode.insertBefore(a,b)})();</script>
    <noscript>
      <img height="1" width="1" style="display:none;" alt="" src="https://dc.ads.linkedin.com/collect/?pid=434737&amp;fmt=gif">
    </noscript>
    <script type="text/javascript" id="">window._tfa=window._tfa||[];window._tfa.push({notify:"event",name:"page_view",id:1187698});!function(a,b,d,c){document.getElementById(c)||(a.async=1,a.src=d,a.id=c,b.parentNode.insertBefore(a,b))}(document.createElement("script"),document.getElementsByTagName("script")[0],"//cdn.taboola.com/libtrc/unip/1187698/tfa.js","tb_tfa_script");</script>
    <noscript>
      <img src="//trc.taboola.com/1187698/log/3/unip?en=page_view" width="0" height="0" style="display:none">
    </noscript>
    <script type="text/javascript" id="">window.googletag=window.googletag||{};window.googletag.cmd=window.googletag.cmd||[];window.googletag.cmd.push(function(){window.googletag.pubads().addEventListener("slotRenderEnded",function(a){if(!a.isEmpty){var b=a.advertiserId||"programmatic",c=a.campaignId||"programmatic";a=a.lineItemId||"programmatic";var d=new Image;d.src="https://pixel.quantserve.com/pixel/p-Jjy-Cyr1NZGRz.gif?labels\x3d_campaign.media.Advertiser%20ID."+b+".Campaign%20ID."+c+".Line%20Item%20ID."+a}})});</script>
    <script type="text/javascript" id="">!function(d,g,e){d.TiktokAnalyticsObject=e;var a=d[e]=d[e]||[];a.methods="page track identify instances debug on off once ready alias group enableCookie disableCookie".split(" ");a.setAndDefer=function(b,c){b[c]=function(){b.push([c].concat(Array.prototype.slice.call(arguments,0)))}};for(d=0;d<a.methods.length;d++)a.setAndDefer(a,a.methods[d]);a.instance=function(b){b=a._i[b]||[];for(var c=0;c<a.methods.length;c++)a.setAndDefer(b,a.methods[c]);return b};a.load=function(b,c){var f="https://analytics.tiktok.com/i18n/pixel/events.js";
      a._i=a._i||{};a._i[b]=[];a._i[b]._u=f;a._t=a._t||{};a._t[b]=+new Date;a._o=a._o||{};a._o[b]=c||{};c=document.createElement("script");c.type="text/javascript";c.async=!0;c.src=f+"?sdkid\x3d"+b+"\x26lib\x3d"+e;b=document.getElementsByTagName("script")[0];b.parentNode.insertBefore(c,b)};a.load("C1IQID9FKFK1PHD4UBH0");a.page()}(window,document,"ttq");
    </script><script type="text/javascript" id="">var scrEm=document.createElement("script");scrEm.setAttribute("id","funnel-relay-installer");scrEm.setAttribute("data-customer-id","condenast_eujdmc753_arstechnica");scrEm.setAttribute("data-property-id","PROPERTY_ID");scrEm.setAttribute("data-autorun","true");scrEm.setAttribute("async","true");scrEm.setAttribute("src","https://cdn-magiclinks.trackonomics.net/client/static/v2/condenast_eujdmc753_arstechnica.js");document.head.appendChild(scrEm);</script><script type="text/javascript" id="">!function(a,b){var e="00c1076881eb5352ee07e7589585aa30bb";if(a.obApi)b=function(d){return"[object Array]"===Object.prototype.toString.call(d)?d:[d]},a.obApi.marketerId=b(a.obApi.marketerId).concat(b(e));else{var c=a.obApi=function(){c.dispatch?c.dispatch.apply(c,arguments):c.queue.push(arguments)};c.version="1.1";c.loaded=!0;c.marketerId=e;c.queue=[];a=b.createElement("script");a.async=!0;a.src="//amplify.outbrain.com/cp/obtp.js";a.type="text/javascript";b=b.getElementsByTagName("script")[0];b.parentNode.insertBefore(a,
      b)}}(window,document);obApi("track","PAGE_VIEW");
    </script><script type="text/javascript" id="">!function(b,e,f,g,a,c,d){b.fbq||(a=b.fbq=function(){a.callMethod?a.callMethod.apply(a,arguments):a.queue.push(arguments)},b._fbq||(b._fbq=a),a.push=a,a.loaded=!0,a.version="2.0",a.queue=[],c=e.createElement(f),c.async=!0,c.src=g,d=e.getElementsByTagName(f)[0],d.parentNode.insertBefore(c,d))}(window,document,"script","https://connect.facebook.net/en_US/fbevents.js");</script>
    <script type="text/javascript" id="">function getVisitNumCustom(e){function q(c){var b=new Date;b.setHours(0);b.setMinutes(0);b.setSeconds(0);if("m"==c){c=b.getMonth();var a=b.getFullYear();c=new Date(a,c+1,0);c=c.getDate();d=c-b.getDate()+1}else d="w"==c?7-b.getDay():1;b.setDate(b.getDate()+d);return b}function n(c,b){c=(b=document.cookie.match("(^|;)\\s*"+c+"\\s*\x3d\\s*([^;]+)"))?b.pop():"";return decodeURIComponent(c)}function g(c,b,a){a=a?"; expires\x3d"+a.toGMTString():"";document.cookie=c+"\x3d"+encodeURIComponent(b)+a+"; path\x3d/"}
      var a=new Date,r,l=a.getTime(),m="CN_visits_"+e,k="CN_in_visit_"+e;e=q(e);var p=e.getTime();a.setTime(p);if(e=n(m))var h=e.indexOf("\x26vn\x3d"),f=e.substring(h+4,e.length);if(r=n(k))return f?(a.setTime(l+18E5),g(k,"true",a),f):"unknown visit number";if(f)return f++,h=e.substring(0,h),a.setTime(h),g(m,h+"\x26vn\x3d"+f,a),a.setTime(l+18E5),g(k,"true",a),f;g(m,p+"\x26vn\x3d1",a);a.setTime(l+18E5);g(k,"true",a);return 1}window.dataLayer=window.dataLayer||[];window.dataLayer.push({user:{monthlyVisitCount:getVisitNumCustom("m")}});
    </script>
    <script type="text/javascript" id="" src="https://ak.sail-horizon.com/spm/spm.v1.min.js"></script>
    <script type="text/javascript" id="">(function(a,b,d){if(!a.snaptr){var c=a.snaptr=function(){c.handleRequest?c.handleRequest.apply(c,arguments):c.queue.push(arguments)};c.queue=[];a="script";r=b.createElement(a);r.async=!0;r.src=d;b=b.getElementsByTagName(a)[0];b.parentNode.insertBefore(r,b)}})(window,document,"https://sc-static.net/scevent.min.js");snaptr("init","da17f2f6-35e0-46e3-b2ec-3f325753384d",{user_email:"__INSERT_USER_EMAIL__"});snaptr("track","da17f2f6-35e0-46e3-b2ec-3f325753384d","VIEW_CONTENT");
      -1<google_tag_manager["rm"]["8819567"](317).toLowerCase().indexOf("snapchat")&&snaptr("track","da17f2f6-35e0-46e3-b2ec-3f325753384d","PAGE_VIEW");
    </script>
    <script type="text/javascript" id="">(function(a,c,e,f,d,b){a.hj=a.hj||function(){(a.hj.q=a.hj.q||[]).push(arguments)};a._hjSettings={hjid:1632543,hjsv:6};d=c.getElementsByTagName("head")[0];b=c.createElement("script");b.async=1;b.src=e+a._hjSettings.hjid+f+a._hjSettings.hjsv;d.appendChild(b)})(window,document,"https://static.hotjar.com/c/hotjar-",".js?sv\x3d");</script><script type="text/javascript" id="">function getVisitNumCustom(e){function q(c){var b=new Date;b.setHours(0);b.setMinutes(0);b.setSeconds(0);if("m"==c){c=b.getMonth();var a=b.getFullYear();c=new Date(a,c+1,0);c=c.getDate();d=c-b.getDate()+1}else d="w"==c?7-b.getDay():1;b.setDate(b.getDate()+d);return b}function n(c,b){c=(b=document.cookie.match("(^|;)\\s*"+c+"\\s*\x3d\\s*([^;]+)"))?b.pop():"";return decodeURIComponent(c)}function g(c,b,a){a=a?"; expires\x3d"+a.toGMTString():"";document.cookie=c+"\x3d"+encodeURIComponent(b)+a+"; path\x3d/"}
      var a=new Date,r,l=a.getTime(),m="CN_visits_"+e,k="CN_in_visit_"+e;e=q(e);var p=e.getTime();a.setTime(p);if(e=n(m))var h=e.indexOf("\x26vn\x3d"),f=e.substring(h+4,e.length);if(r=n(k))return f?(a.setTime(l+18E5),g(k,"true",a),f):"unknown visit number";if(f)return f++,h=e.substring(0,h),a.setTime(h),g(m,h+"\x26vn\x3d"+f,a),a.setTime(l+18E5),g(k,"true",a),f;g(m,p+"\x26vn\x3d1",a);a.setTime(l+18E5);g(k,"true",a);return 1}window.dataLayer=window.dataLayer||[];window.dataLayer.push({user:{monthlyVisitCount:getVisitNumCustom("m")}});
    </script>
    <script type="text/javascript" id="">(function(a){a=a.createElement("script");a.src="//tag.bounceexchange.com/2806/i.js";a.async=!0;window.top.document.head.appendChild(a)})(document);</script><iframe name="google_ads_top_frame" id="google_ads_top_frame" style="display: none; position: fixed; left: -999px; top: -999px; width: 0px; height: 0px;"></iframe><iframe id="tbl-browsing-topics-iframe-60.360155958722" src="https://cdn.taboola.com/libtrc/static/topics/taboola-browsing-topics.html" style="width: 0px; height: 0px; display: none;"></iframe><script src="//tr.outbrain.com/cachedClickId?marketerId=00c1076881eb5352ee07e7589585aa30bb"></script><img src="https://t.co/i/adsct?bci=3&amp;eci=2&amp;event_id=8621a8a3-a98d-4237-a8e1-d8681edec907&amp;events=%5B%5B%22pageview%22%2C%7B%7D%5D%5D&amp;integration=advertiser&amp;p_id=Twitter&amp;p_user_id=0&amp;pl_id=41ae6d79-6b33-48f7-8b43-4e07c47dd492&amp;tw_document_href=https%3A%2F%2Farstechnica.com%2Ffeatures%2F2017%2F06%2Fyoutube-changed-my-life-a-pair-of-original-videostars-ponder-a-life-lived-online%2F&amp;tw_iframe_status=0&amp;tw_order_quantity=0&amp;tw_sale_amount=0&amp;txn_id=o1o49&amp;type=javascript&amp;version=2.3.30" height="1" width="1" style="display: none;"><img src="https://analytics.twitter.com/i/adsct?bci=3&amp;eci=2&amp;event_id=8621a8a3-a98d-4237-a8e1-d8681edec907&amp;events=%5B%5B%22pageview%22%2C%7B%7D%5D%5D&amp;integration=advertiser&amp;p_id=Twitter&amp;p_user_id=0&amp;pl_id=41ae6d79-6b33-48f7-8b43-4e07c47dd492&amp;tw_document_href=https%3A%2F%2Farstechnica.com%2Ffeatures%2F2017%2F06%2Fyoutube-changed-my-life-a-pair-of-original-videostars-ponder-a-life-lived-online%2F&amp;tw_iframe_status=0&amp;tw_order_quantity=0&amp;tw_sale_amount=0&amp;txn_id=o1o49&amp;type=javascript&amp;version=2.3.30" height="1" width="1" style="display: none;">
    <script type="text/javascript" id="">Sailthru.init({customerId:"96cc6d73eeadca5c51a196378f9bf3d1"});</script><iframe id="snap2663387" src="https://tr.snapchat.com/cm/i?pid=da17f2f6-35e0-46e3-b2ec-3f325753384d&amp;u_scsid=fc0f457e-ea31-4ca6-84ce-7be037e9d304&amp;u_sclid=f3436d36-d7af-4c04-8722-182aa2e278fd" style="display: none !important; height: 1px !important; overflow: hidden !important; position: absolute !important; width: 1px !important;"></iframe><script src="//assets.bounceexchange.com/assets/smart-tag/versioned/runtime_c81e76ee00d795b1eebf8d27949f8dc5.br.js" async="async"></script>
    <div id="tbl-aug-67947">
      <div id="tbl-aug-45827">
        <div id="tbl-aug-18799">
          <div class="trc_popover trc_popover_fade trc_bottom">
            <div class="trc_popover_arrow"></div>
            <div class="popupContentWrapper">
              <div class="trc_popover_title_wrapper">
                <div class="trc_popover_title"></div>
              </div>
              <div class="trc_popover_content_wrapper">
                <div class="trc_popover_content"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <iframe src="https://www.google.com/recaptcha/api2/aframe" width="0" height="0" style="display: none;"></iframe><script src="//assets.bounceexchange.com/assets/smart-tag/versioned/main-v2_823eab310aa4453f7f370728bbc88aca.br.js" async="async"></script><iframe id="_hjSafeContext_30572960" title="_hjSafeContext" tabindex="-1" aria-hidden="true" src="about:blank" style="display: none !important; width: 1px !important; height: 1px !important; opacity: 0 !important; pointer-events: none !important;"></iframe><iframe allow="autoplay; attribution-reporting 'src' https://cm.teads.tv" scrolling="no" title="wigo-helper" frameborder="0" id="wigoiframe" src="https://sync.teads.tv/wigo-no-slot" style="margin: 0px !important; padding: 0px !important; width: 0px !important; height: 0px !important; border: 0px !important; overflow: hidden !important; float: none !important; display: none !important;"></iframe><iframe id="bcx_local_storage_frame" src="https://assets.bounceexchange.com/assets/bounce/local_storage_frame17.min.html#2806" title="empty" style="display: none;"></iframe><iframe src="https://aax-eu.amazon-adsystem.com/s/iu3?cm3ppd=1&amp;d=dtb-pub&amp;csif=t&amp;gdpr=1&amp;gdpr_consent=CQGA_XAQGA_XAAcABBENBKFsAP_gAELAAChQKfkF5CjeLWFiIXR1IKsAeIUfRlAAooQRBgBBEYABCBIQNIQE0kERBATAAAAKGAIAsAJBAAIkAABAAUAAQAAAAABAAACAIBBIIgBECAAAQAAAAAAAAQAAEAAAggAFAEAAmAggQAIgAEAAAAAAACABhCAFAAABAgCAAQAAQAAAAQAIgCgAAQIAyAAAAAAAABAAEAAAAAAAIAICAAAAIAAAAEgAAAQAAAAB3_gp5ALAB-AZ8BKoC8wGCANGAmqBPQCmQFNQKeAGBIEoACAAFgAVAA4AB4AEEAMgA1ACIAEwAKoAbwA9AB-AEJAIgAiQBHADKAHOAO4AewA_QCAAEUAJEAVcAuYBigDaAHEAQ6AkQBbAC5AG6gOCAhCBC8IAFAI4Av8BowD9wIVjgBgACAALgA_AEcAQAAhABHAF_gMEAaMA_cCFY6BMAAsACoAHAAQQAyADUAIgATAAqgBdADEAG8APQAfoBEAESAKMAZQA5wB3AD2AH7ARQBFgCRAFXALmAYoA2gBxADqAIdAReAkQBMgCmgFsALkAZYA3UB_ZAAKAAgAjgDBAGjAQrIQEgAFgA1ACqAGIAN4AegBHADnAHcARQAq4BcwDFAG0AOoApoBcgD-yUBUABAACwAOABEACYAFUAMUAiACJAEcAKMAq4BcwDFAHUAQ6AiYBF4CRAFNALYAhCSADgAXAHcAQAAjgC_wGWAP3KQIwAFgAVAA4ACCAGQAaABEACYAFIAKoAYgA_QCIAIkAUYAygBzgD9AIsASIAq4BcwDFAG0AOoAh0BEwCLwEiAKaAWwAuQBlgDdQHBAP7AhCUAEgAXAEcAO4AgADBAGjAP3AhWBC8tADABqAO4BTRYAEARwBowAA.f_wACFgAAAAA&amp;dl=n-index_n-onetag_pm-db5_rbd_ox-db5_smrt_an-db5_3lift" style="display: none;"></iframe><img height="1" width="1" alt="" src="https://trx-hub.com/i/m/i.png?q=N4IghgLhBOD6BmB7aB3M0AmBLAdgcxAC5gBfAGhAFsBTCMDSMI0iiLS3A48kAV2gA2AZ2Y9IMWGAAOU6jgyiKUsHmqwYYAMbUiAbVBDE-bURAALKFKGEA9DfRCI1TWZxZNYAHSbElG-GpIfmohGwAmAAYARgB2GwiANhsATyMIXgAjagBaFzB8agxsymTsgSwA7LBs5SxobMR4BugsPFwwAWyANywMakRHBxrEeWp66vLK8q7ChpxynGobEApoagDoNehTFfAoOCRUdGx8URIAXQpNXkdfWBo6RRBNcrkIdXYdQhBIsIAWbJRCLZCIAVgAKhEAJyEKJRWEADk8CLBAC1dspVB8IAIviAALLIagAAggZnyxLAxL6Hj6xIEkDGZGJZkQKGpiGJyFa7QExIAmkZwZkSYNoEJiQFqHywBk0qSzKKsE4APzE7LEgCC4uJ4OcrncTAoWF6phNGFiYQRcKhfxiCJRoM0eAIFGoMxw7wgyVkZsoUjWQiEWBGu2utxocFN3x8oxwYEcsGovAAVhhKJoYqCAMyScVOFxuDxh3pSKKmWN9eOJ5NpjNZ7MljBSMKmBwFg3FkhAA" style="border-style: none; display: none;">
  </body>
  <iframe name="goog_topics_frame" src="https://securepubads.g.doubleclick.net/static/topics/topics_frame.html" style="display: none;"></iframe><iframe sandbox="allow-scripts allow-same-origin" id="21db3d644f4a60e" frameborder="0" allowtransparency="true" marginheight="0" marginwidth="0" width="0" hspace="0" vspace="0" height="0" style="height:0px;width:0px;display:none;" scrolling="no" src="https://eus.rubiconproject.com/usync.html">
  </iframe><iframe sandbox="allow-scripts allow-same-origin" id="223da29283c9398" frameborder="0" allowtransparency="true" marginheight="0" marginwidth="0" width="0" hspace="0" vspace="0" height="0" style="height:0px;width:0px;display:none;" scrolling="no" src="https://ads.pubmatic.com/AdServer/js/user_sync.html?kdntuid=1&amp;p=164315">
  </iframe><iframe sandbox="allow-scripts allow-same-origin" id="23d7cb5862a9bbd" frameborder="0" allowtransparency="true" marginheight="0" marginwidth="0" width="0" hspace="0" vspace="0" height="0" style="height:0px;width:0px;display:none;" scrolling="no" src="https://eb2.3lift.com/sync?">
  </iframe><iframe sandbox="allow-scripts allow-same-origin" id="2400906dd5f3a46" frameborder="0" allowtransparency="true" marginheight="0" marginwidth="0" width="0" hspace="0" vspace="0" height="0" style="height:0px;width:0px;display:none;" scrolling="no" src="https://contextual.media.net/checksync.php?vsSync=1&amp;cs=8&amp;cv=31&amp;https=1&amp;cid=8CU65UN7R&amp;prvid=2033%2C2030%2C294%2C251%2C132%2C2027%2C159%2C2026%2C238%2C359%2C338%2C459%2C97%2C77%2C59%2C3012%2C3011%2C262%2C461%2C201%2C246%2C4%2C126%2C203%2C326%2C10000%2C108%2C9&amp;itype=PREBID&amp;purpose1=1&amp;gdprconsent=0&amp;gdpr=1&amp;coppa=0&amp;usp_status=0&amp;usp_consent=1">
  </iframe><iframe sandbox="allow-scripts allow-same-origin" id="25b0d29acf1389a" frameborder="0" allowtransparency="true" marginheight="0" marginwidth="0" width="0" hspace="0" vspace="0" height="0" style="height:0px;width:0px;display:none;" scrolling="no" src="https://js-sec.indexww.com/um/ixmatch.html">
  </iframe><iframe sandbox="allow-scripts allow-same-origin" id="26172c6f4edd80b" frameborder="0" allowtransparency="true" marginheight="0" marginwidth="0" width="0" hspace="0" vspace="0" height="0" style="height:0px;width:0px;display:none;" scrolling="no" src="https://gum.criteo.com/syncframe?origin=criteoPrebidAdapter&amp;topUrl=arstechnica.com&amp;gpp=#{%22cw%22:true,%22lsw%22:true,%22origin%22:%22criteoPrebidAdapter%22,%22requestId%22:%220.3887350880198841%22,%22tld%22:%22arstechnica.com%22,%22topUrl%22:%22arstechnica.com%22,%22version%22:%229_5_0%22}">
  </iframe><iframe sandbox="allow-scripts allow-same-origin" id="274233b1cb9a2f6" frameborder="0" allowtransparency="true" marginheight="0" marginwidth="0" width="0" hspace="0" vspace="0" height="0" style="height:0px;width:0px;display:none;" scrolling="no" src="https://acdn.adnxs.com/dmp/async_usersync.html">
  </iframe><iframe sandbox="allow-scripts allow-same-origin" id="28f65e5346834eb" frameborder="0" allowtransparency="true" marginheight="0" marginwidth="0" width="0" hspace="0" vspace="0" height="0" style="height:0px;width:0px;display:none;" scrolling="no" src="https://condenastus-d.openx.net/w/1.0/pd">
  </iframe>
</html>