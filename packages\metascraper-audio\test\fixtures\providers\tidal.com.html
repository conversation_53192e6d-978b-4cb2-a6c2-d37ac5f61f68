<!DOCTYPE html>
<html lang="en-US" data-n-head="%7B%22lang%22:%7B%22ssr%22:%22en-US%22%7D%7D">

<head>
  <title>DESPECHÁ by ROSALÍA on TIDAL</title>
  <meta data-n-head="ssr" charset="utf-8">
  <meta data-n-head="ssr" name="viewport" content="width=device-width, initial-scale=1.0, shrink-to-fit=no">
  <meta data-n-head="ssr" name="theme-color" content="#000000">
  <meta data-n-head="ssr" rel="preload" href="/browse/_nuxt/fonts/nationale-regular.woff2" as="font" crossorigin="true" nopush="true">
  <meta data-n-head="ssr" rel="preload" href="/browse/_nuxt/fonts/nationale-bold.woff2" as="font" crossorigin="true" nopush="true">
  <meta data-n-head="ssr" rel="preload" href="/browse/_nuxt/fonts/nationale-demibold.woff2" as="font" crossorigin="true" nopush="true">
  <meta data-n-head="ssr" rel="dns-prefetch" href="//embed.tidal.com">
  <meta data-n-head="ssr" rel="dns-prefetch" href="//resources.tidal.com">
  <meta data-n-head="ssr" rel="dns-prefetch" href="//googletagmanager.com">
  <meta data-n-head="ssr" property="fb:app_id" content="185717988231456">
  <meta data-n-head="ssr" property="og:site_name" content="Music on TIDAL" data-hid="og_site">
  <meta data-n-head="ssr" name="msapplication-config" content="/browse/browserconfig.xml">
  <meta data-n-head="ssr" data-hid="i18n-og" property="og:locale" content="en_US">
  <meta data-n-head="ssr" data-hid="i18n-og-alt-es-AR" property="og:locale:alternate" content="es_AR">
  <meta data-n-head="ssr" data-hid="i18n-og-alt-bg-BG" property="og:locale:alternate" content="bg_BG">
  <meta data-n-head="ssr" data-hid="i18n-og-alt-es-CL" property="og:locale:alternate" content="es_CL">
  <meta data-n-head="ssr" data-hid="i18n-og-alt-es-CO" property="og:locale:alternate" content="es_CO">
  <meta data-n-head="ssr" data-hid="i18n-og-alt-de-DE" property="og:locale:alternate" content="de_DE">
  <meta data-n-head="ssr" data-hid="i18n-og-alt-es-DO" property="og:locale:alternate" content="es_DO">
  <meta data-n-head="ssr" data-hid="i18n-og-alt-es-ES" property="og:locale:alternate" content="es_ES">
  <meta data-n-head="ssr" data-hid="i18n-og-alt-fr-FR" property="og:locale:alternate" content="fr_FR">
  <meta data-n-head="ssr" data-hid="i18n-og-alt-hr-HR" property="og:locale:alternate" content="hr_HR">
  <meta data-n-head="ssr" data-hid="i18n-og-alt-it-IT" property="og:locale:alternate" content="it_IT">
  <meta data-n-head="ssr" data-hid="i18n-og-alt-ja-JP" property="og:locale:alternate" content="ja_JP">
  <meta data-n-head="ssr" data-hid="i18n-og-alt-es-MX" property="og:locale:alternate" content="es_MX">
  <meta data-n-head="ssr" data-hid="i18n-og-alt-no-NO" property="og:locale:alternate" content="no_NO">
  <meta data-n-head="ssr" data-hid="i18n-og-alt-es-PE" property="og:locale:alternate" content="es_PE">
  <meta data-n-head="ssr" data-hid="i18n-og-alt-pl-PL" property="og:locale:alternate" content="pl_PL">
  <meta data-n-head="ssr" data-hid="i18n-og-alt-es-PR" property="og:locale:alternate" content="es_PR">
  <meta data-n-head="ssr" data-hid="i18n-og-alt-pt-PT" property="og:locale:alternate" content="pt_PT">
  <meta data-n-head="ssr" data-hid="i18n-og-alt-pt-BR" property="og:locale:alternate" content="pt_BR">
  <meta data-n-head="ssr" data-hid="i18n-og-alt-sl-SI" property="og:locale:alternate" content="sl_SI">
  <meta data-n-head="ssr" data-hid="i18n-og-alt-sr-RS" property="og:locale:alternate" content="sr_RS">
  <meta data-n-head="ssr" name="apple-itunes-app" content="app-id=913943275, app-argument=tidal://track/240175608" data-hid="smart-banner-apple">
  <meta data-n-head="ssr" name="description" content="Listen to DESPECHÁ, a song by ROSALÍA on TIDAL" data-hid="desc">
  <meta data-n-head="ssr" name="twitter:card" content="summary" data-hid="t_card">
  <meta data-n-head="ssr" name="twitter:site" content="@tidal" data-hid="t_site">
  <meta data-n-head="ssr" name="al:android:app_name" content="Tidal">
  <meta data-n-head="ssr" name="al:android:package" content="com.aspiro.tidal">
  <meta data-n-head="ssr" name="al:android:url" content="tidal://track/240175608">
  <meta data-n-head="ssr" name="al:ios:app_name" content="TIDAL">
  <meta data-n-head="ssr" name="al:ios:app_store_id" content="913943275">
  <meta data-n-head="ssr" name="al:ios:url" content="tidal://track/240175608">
  <meta data-n-head="ssr" property="og:title" content="ROSALÍA - DESPECHÁ">
  <meta data-n-head="ssr" property="og:description" content="Listen to DESPECHÁ on TIDAL">
  <meta data-n-head="ssr" property="og:type" content="music.song">
  <meta data-n-head="ssr" property="og:url" content="https://tidal.com/browse/track/240175608" data-hid="og_url">
  <meta data-n-head="ssr" property="og:image" content="https://resources.tidal.com/images/55b2bcae/8e9e/4ec2/9845/d1f83e2ff61c/640x640.jpg">
  <meta data-n-head="ssr" property="og:image:width" content="640">
  <meta data-n-head="ssr" property="og:image:height" content="640">
  <meta data-n-head="ssr" property="music:musician" content="https://tidal.com/browse/artist/4748331">
  <meta data-n-head="ssr" property="music:album" content="https://tidal.com/browse/album/240175607">
  <meta data-n-head="ssr" property="music:album:track" content="1">
  <meta data-n-head="ssr" property="music:duration" content="157">
  <base href="/browse/">
  <link data-n-head="ssr" rel="icon" sizes="any" href="/browse/favicon.ico">
  <link data-n-head="ssr" rel="icon" type="image/svg+xml" href="/browse/favicon.svg">
  <link data-n-head="ssr" rel="apple-touch-icon" href="/browse/apple-touch-icon.png">
  <link data-n-head="ssr" rel="manifest" href="/browse/manifest.json">
  <link data-n-head="ssr" rel="dns-prefetch" href="https://cdn.cookielaw.org/">
  <link data-n-head="ssr" data-hid="i18n-can" rel="canonical" href="https://tidal.com/browse/track/240175608">
  <link data-n-head="ssr" rel="canonical" href="https://tidal.com/browse/track/240175608" data-hid="canonical">
  <link data-n-head="ssr" rel="alternate" type="application/json+oembed" href="https://oembed.tidal.com/?url=https://tidal.com/browse/track/240175608">
  <style data-n-head="ssr">
    body {
      background-color: #101012;
    }
  </style>
  <script type="text/javascript" async="" src="https://analytics.tiktok.com/i18n/pixel/static/main.MTI1YjU3ZmM1MQ.js" data-id="C7S0A50FLK2NRAISTAQG"></script>
  <script async="true" src="https://tr.snapchat.com/config/com/4d24efb0-f252-4535-a1d2-d1056340748f.js" crossorigin="anonymous"></script>
  <script async="" src="//js-tag.zemanta.com/zcpt.js" type="text/javascript"></script>
  <script async="" src="https://sc-static.net/scevent.min.js"></script>
  <script src="https://www.redditstatic.com/ads/pixel.js" async=""></script>
  <script type="text/javascript" async="" src="https://analytics.tiktok.com/i18n/pixel/events.js?sdkid=C7S0A50FLK2NRAISTAQG&amp;lib=ttq"></script>
  <script type="text/javascript" async="" src="https://bat.bing.com/bat.js"></script>
  <script type="text/javascript" async="" src="https://www.google-analytics.com/analytics.js"></script>
  <script src="https://connect.facebook.net/signals/config/837463869618040?v=2.9.121&amp;r=stable" async=""></script>
  <script async="" src="https://connect.facebook.net/en_US/fbevents.js"></script>
  <script type="text/javascript" async="" src="https://www.googletagmanager.com/gtag/js?id=G-H0RBVC0JN6&amp;l=dataLayer&amp;cx=c"></script>
  <script async="" src="https://www.googletagmanager.com/gtm.js?id=GTM-5XWHVHQ"></script>
  <script async="" src="https://dd.tidal.com/tags.js"></script>
  <script data-n-head="ssr" data-hid="onetrust-consent-init">
    // Define dataLayer and the gtag function.
    window.dataLayer = window.dataLayer || [];
    function gtag() {
      dataLayer.push(arguments);
    }

    // Default ad_storage to 'denied'.
    gtag('consent', 'default', {
      'ad_storage': 'denied',
      'wait_for_update': 500
    })</script>
  <script data-n-head="ssr" data-hid="onetrust1" src="https://cdn.cookielaw.org/scripttemplates/otSDKStub.js" data-domain-script="0e85e00b-5092-4389-bafc-7a50cb1352ca" data-document-language="true"></script>
  <script data-n-head="ssr" data-hid="onetrust-wrapper">
    window.consentPluginHandler = null
    function OptanonWrapper() {
      if (window.consentPluginHandler) {
        window.consentPluginHandler(window.OptanonActiveGroups);
      }
    }</script>
  <script data-n-head="ssr" data-hid="datadome">
    !function (a, b, c, d, e, f) {
      a.ddjskey = e;
      a.ddoptions = f || null;
      var m = b.createElement(c), n = b.getElementsByTagName(c)[0];
      m.async = 1, m.src = d, n.parentNode.insertBefore(m, n);
    }(window, document, "script", "https://dd.tidal.com/tags.js", "1F633CDD8EF22541BD6D9B1B8EF13A",
      { endpoint: 'https://dd.tidal.com/js/', ajaxListenerPath: true });
  </script>
  <script data-n-head="ssr" data-hid="gtm-script">window['dataLayer'] = [{ "geo": "ES" }]; if (!window._gtm_init) { window._gtm_init = 1; (function (w, n, d, m, e, p) { w[d] = (w[d] == 1 || n[d] == 'yes' || n[d] == 1 || n[m] == 1 || (w[e] && w[e][p] && w[e][p]())) ? 1 : 0 })(window, navigator, 'doNotTrack', 'msDoNotTrack', 'external', 'msTrackingProtectionEnabled'); (function (w, d, s, l, x, y) { w[x] = {}; w._gtm_inject = function (i) { if (w[x][i]) return; w[x][i] = 1; w[l] = w[l] || []; w[l].push({ 'gtm.start': new Date().getTime(), event: 'gtm.js' }); var f = d.getElementsByTagName(s)[0], j = d.createElement(s); j.async = true; j.src = 'https://www.googletagmanager.com/gtm.js?id=' + i; f.parentNode.insertBefore(j, f); } })(window, document, 'script', 'dataLayer', '_gtm_ids', '_gtm_inject') }</script>
  <script data-n-head="ssr"
    type="application/ld+json">{"@context":"http://schema.org","@type":"MusicRecording","url":"https://tidal.com/browse/track/240175608","name":"DESPECHÁ","description":"Listen to DESPECHÁ by ROSALÍA","duration":"P2M37S","datePublished":"2022-07-28T00:00:00.000Z","byArtist":[{"@type":"MusicGroup","@id":"https://tidal.com/browse/artist/4748331","name":"ROSALÍA"}],"potentialAction":{"@type":"ListenAction","target":[{"@type":"EntryPoint","urlTemplate":"https://listen.tidal.com/track/240175608","actionPlatform":["http://schema.org/DesktopWebPlatform","http://schema.org/IOSPlatform","http://schema.org/AndroidPlatform"]},{"@type":"EntryPoint","urlTemplate":"https://tidal.com/browse/track/240175608","actionPlatform":"http://schema.org/IOSPlatform"},{"@type":"EntryPoint","urlTemplate":"https://tidal.com/browse/track/240175608","actionPlatform":"http://schema.org/AndroidPlatform"},{"@type":"EntryPoint","urlTemplate":"https://tidal.com/browse/track/240175608","actionPlatform":"http://schema.org/DesktopWebPlatform"}],"expectsAcceptanceOf":{"@type":"Offer","eligibleRegion":[{"@type":"Country","name":"US"},{"@type":"Country","name":"NO"}]}}}</script>
  <noscript data-n-head="ssr" data-hid="tidalScroll">
    <style>
      .tidal-lazy-image,
      .tidal-scroll-item {
        opacity: 1 !important
      }
    </style>
  </noscript>
  <link rel="preload" href="/browse/_nuxt/66ce7ec.modern.js" as="script">
  <link rel="preload" href="/browse/_nuxt/963f8f7.modern.js" as="script">
  <link rel="preload" href="/browse/_nuxt/d1ca131.modern.js" as="script">
  <link rel="preload" href="/browse/_nuxt/8a1cd62.modern.js" as="script">
  <link rel="preload" href="/browse/_nuxt/889e134.modern.js" as="script">
  <link rel="preload" href="/browse/_nuxt/ea4067f.modern.js" as="script">
  <link rel="preload" href="/browse/_nuxt/bbddaa9.modern.js" as="script">
  <link rel="preload" href="/browse/_nuxt/24901a5.modern.js" as="script">
  <style data-vue-ssr-id="697d1396:0 b8141ebc:0 affdd110:0 c1efb4c6:0 019a6669:0 ac9bf4c2:0 96467888:0 45549d82:0 1e733c81:0 27aacf32:0 335e85fa:0 8863fa9c:0 1b8277d7:0 b60ff53c:0 76452c1a:0 4c6f6a10:0 70d55ca8:0 6a35edc1:0">
    /*!
*  TIDAL Styleguide v12.3.0
*/
    @font-face {
      font-family: nationale;
      font-display: fallback;
      src: url(/browse/_nuxt/fonts/nationale-regular.eot);
      src: url(/browse/_nuxt/fonts/nationale-regular.eot?#iefix) format("embedded-opentype"), url(/browse/_nuxt/fonts/nationale-regular.woff2) format("woff2"), url(/browse/_nuxt/fonts/nationale-regular.woff) format("woff"), url(/browse/_nuxt/fonts/nationale-regular.otf) format("opentype")
    }

    @font-face {
      font-family: nationale;
      font-weight: 600;
      font-display: fallback;
      src: url(/browse/_nuxt/fonts/nationale-demibold.eot);
      src: url(/browse/_nuxt/fonts/nationale-demibold.eot?#iefix) format("embedded-opentype"), url(/browse/_nuxt/fonts/nationale-demibold.woff2) format("woff2"), url(/browse/_nuxt/fonts/nationale-demibold.woff) format("woff"), url(/browse/_nuxt/fonts/nationale-demibold.otf) format("opentype")
    }

    @font-face {
      font-family: nationale;
      font-weight: 700;
      font-display: fallback;
      src: url(/browse/_nuxt/fonts/nationale-bold.eot);
      src: url(/browse/_nuxt/fonts/nationale-bold.eot?#iefix) format("embedded-opentype"), url(/browse/_nuxt/fonts/nationale-bold.woff2) format("woff2"), url(/browse/_nuxt/fonts/nationale-bold.woff) format("woff"), url(/browse/_nuxt/fonts/nationale-bold.otf) format("opentype")
    }

    html {
      -webkit-text-size-adjust: 100%;
      box-sizing: border-box
    }

    *,
    :after,
    :before {
      box-sizing: inherit
    }

    body {
      color: #fff;
      background-color: #000;
      font-family: nationale, nationale-regular, helvetica, arial, sans-serif;
      font-size: 1rem;
      line-height: 1.5;
      overflow-x: hidden;
      margin: 0;
      padding: 0
    }

    ::-moz-selection {
      background-color: #0ff
    }

    ::selection {
      background-color: #0ff
    }

    blockquote,
    button,
    dd,
    dl,
    dt,
    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    input,
    li,
    ol,
    p,
    select,
    textarea,
    ul {
      margin: 0;
      padding: 0;
      border: 0;
      font-size: 100%;
      font: inherit;
      vertical-align: baseline
    }

    article,
    aside,
    figcaption,
    figure,
    footer,
    header,
    iframe,
    main,
    menu,
    nav,
    section {
      display: block
    }

    .baseline-flow,
    .form-select-wrapper,
    blockquote,
    dd,
    fieldset,
    figure,
    input,
    ol,
    p,
    small,
    table,
    textarea,
    ul {
      margin-bottom: 1.5rem
    }

    [class*=col-] .baseline-flow:last-child,
    [class*=col-] .form-select-wrapper:last-child,
    [class*=col-] blockquote:last-child,
    [class*=col-] dd:last-child,
    [class*=col-] fieldset:last-child,
    [class*=col-] figure:last-child,
    [class*=col-] input:last-child,
    [class*=col-] ol:last-child,
    [class*=col-] p:last-child,
    [class*=col-] small:last-child,
    [class*=col-] table:last-child,
    [class*=col-] textarea:last-child,
    [class*=col-] ul:last-child {
      margin-bottom: 0
    }

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      font-weight: 700
    }

    ol,
    ul {
      padding-left: 1.5rem
    }

    li {
      margin-bottom: .375rem
    }

    img {
      border-style: none;
      max-height: 100%;
      max-height: inherit;
      height: auto;
      max-width: 100%
    }

    img:not([width], .fullwidth, .img-cover) {
      width: auto
    }

    img[srcset] {
      width: 100%
    }

    .clearfix:after,
    .row:after,
    .wrapper:after {
      content: "";
      display: table;
      clear: both;
      flex-basis: 0;
      order: 1
    }

    html {
      font-size: 16px;
      font-size: 100%;
      text-rendering: optimizeLegibility;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      font-feature-settings: "liga" on
    }

    @media(min-width:1200px) {
      html {
        font-size: 18px;
        font-size: 112.5%
      }
    }

    .font-family-dharma {
      font-family: dharma, Impact, HelveticaNeue-CondensedBlack, sans-serif;
      font-size: 1.9rem;
      line-height: 1
    }

    html[lang=bg-BG] .font-family-dharma,
    html[lang=ja-JP] .font-family-dharma {
      font-size: 1.3rem;
      line-height: 1.15
    }

    @media(min-width:992px) {
      .font-family-dharma {
        font-size: 2.8rem
      }

      html[lang=bg-BG] .font-family-dharma,
      html[lang=ja-JP] .font-family-dharma {
        font-size: 1.944rem
      }
    }

    html[lang=bg-BG] .font-family-dharma {
      font-family: Impact, HelveticaNeue-CondensedBlack, sans-serif
    }

    html[lang=ja-JP] .font-family-dharma {
      font-family: inherit;
      font-weight: 700
    }

    .font-size-huge,
    blockquote {
      font-size: 3rem;
      line-height: 1.25
    }

    @media(min-width:992px) {

      .font-size-huge,
      blockquote {
        font-size: 3.5rem
      }
    }

    .font-size-huge.font-family-dharma,
    blockquote.font-family-dharma {
      font-size: 6.6rem;
      line-height: .9
    }

    html[lang=bg-BG] .font-size-huge.font-family-dharma,
    html[lang=bg-BG] blockquote.font-family-dharma,
    html[lang=ja-JP] .font-size-huge.font-family-dharma,
    html[lang=ja-JP] blockquote.font-family-dharma {
      font-size: 4.5rem;
      line-height: 1.05
    }

    @media(min-width:992px) {

      .font-size-huge.font-family-dharma,
      blockquote.font-family-dharma {
        font-size: 8.9rem
      }

      html[lang=bg-BG] .font-size-huge.font-family-dharma,
      html[lang=bg-BG] blockquote.font-family-dharma,
      html[lang=ja-JP] .font-size-huge.font-family-dharma,
      html[lang=ja-JP] blockquote.font-family-dharma {
        font-size: 6.15rem
      }
    }

    .font-size-big {
      font-size: 3rem;
      line-height: 1.25
    }

    .font-size-big.font-family-dharma {
      font-size: 4rem;
      line-height: .9
    }

    html[lang=bg-BG] .font-size-big.font-family-dharma,
    html[lang=ja-JP] .font-size-big.font-family-dharma {
      font-size: 3rem;
      line-height: 1.08
    }

    @media(min-width:992px) {
      .font-size-big.font-family-dharma {
        font-size: 6.7rem
      }

      html[lang=bg-BG] .font-size-big.font-family-dharma,
      html[lang=ja-JP] .font-size-big.font-family-dharma {
        font-size: 4.6112rem
      }
    }

    @media(min-width:576px) {
      .font-size-big-sm-min {
        font-size: 3rem;
        line-height: 1.25
      }

      .font-size-big-sm-min.font-family-dharma {
        font-size: 4rem;
        line-height: .9
      }

      html[lang=bg-BG] .font-size-big-sm-min.font-family-dharma,
      html[lang=ja-JP] .font-size-big-sm-min.font-family-dharma {
        font-size: 3rem;
        line-height: 1.08
      }
    }

    @media(min-width:576px)and (min-width:992px) {
      .font-size-big-sm-min.font-family-dharma {
        font-size: 6.7rem
      }

      html[lang=bg-BG] .font-size-big-sm-min.font-family-dharma,
      html[lang=ja-JP] .font-size-big-sm-min.font-family-dharma {
        font-size: 4.6112rem
      }
    }

    @media(max-width:575px) {
      .font-size-big-sm-max {
        font-size: 3rem;
        line-height: 1.25
      }

      .font-size-big-sm-max.font-family-dharma {
        font-size: 4rem;
        line-height: .9
      }

      html[lang=bg-BG] .font-size-big-sm-max.font-family-dharma,
      html[lang=ja-JP] .font-size-big-sm-max.font-family-dharma {
        font-size: 3rem;
        line-height: 1.08
      }
    }

    @media(max-width:575px)and (min-width:992px) {
      .font-size-big-sm-max.font-family-dharma {
        font-size: 6.7rem
      }

      html[lang=bg-BG] .font-size-big-sm-max.font-family-dharma,
      html[lang=ja-JP] .font-size-big-sm-max.font-family-dharma {
        font-size: 4.6112rem
      }
    }

    @media(min-width:768px) {
      .font-size-big-md-min {
        font-size: 3rem;
        line-height: 1.25
      }

      .font-size-big-md-min.font-family-dharma {
        font-size: 4rem;
        line-height: .9
      }

      html[lang=bg-BG] .font-size-big-md-min.font-family-dharma,
      html[lang=ja-JP] .font-size-big-md-min.font-family-dharma {
        font-size: 3rem;
        line-height: 1.08
      }
    }

    @media(min-width:768px)and (min-width:992px) {
      .font-size-big-md-min.font-family-dharma {
        font-size: 6.7rem
      }

      html[lang=bg-BG] .font-size-big-md-min.font-family-dharma,
      html[lang=ja-JP] .font-size-big-md-min.font-family-dharma {
        font-size: 4.6112rem
      }
    }

    @media(max-width:767px) {
      .font-size-big-md-max {
        font-size: 3rem;
        line-height: 1.25
      }

      .font-size-big-md-max.font-family-dharma {
        font-size: 4rem;
        line-height: .9
      }

      html[lang=bg-BG] .font-size-big-md-max.font-family-dharma,
      html[lang=ja-JP] .font-size-big-md-max.font-family-dharma {
        font-size: 3rem;
        line-height: 1.08
      }
    }

    @media(max-width:767px)and (min-width:992px) {
      .font-size-big-md-max.font-family-dharma {
        font-size: 6.7rem
      }

      html[lang=bg-BG] .font-size-big-md-max.font-family-dharma,
      html[lang=ja-JP] .font-size-big-md-max.font-family-dharma {
        font-size: 4.6112rem
      }
    }

    @media(min-width:992px) {
      .font-size-big-lg-min {
        font-size: 3rem;
        line-height: 1.25
      }

      .font-size-big-lg-min.font-family-dharma {
        font-size: 4rem;
        line-height: .9
      }

      html[lang=bg-BG] .font-size-big-lg-min.font-family-dharma,
      html[lang=ja-JP] .font-size-big-lg-min.font-family-dharma {
        font-size: 3rem;
        line-height: 1.08
      }
    }

    @media(min-width:992px)and (min-width:992px) {
      .font-size-big-lg-min.font-family-dharma {
        font-size: 6.7rem
      }

      html[lang=bg-BG] .font-size-big-lg-min.font-family-dharma,
      html[lang=ja-JP] .font-size-big-lg-min.font-family-dharma {
        font-size: 4.6112rem
      }
    }

    @media(max-width:991px) {
      .font-size-big-lg-max {
        font-size: 3rem;
        line-height: 1.25
      }

      .font-size-big-lg-max.font-family-dharma {
        font-size: 4rem;
        line-height: .9
      }

      html[lang=bg-BG] .font-size-big-lg-max.font-family-dharma,
      html[lang=ja-JP] .font-size-big-lg-max.font-family-dharma {
        font-size: 3rem;
        line-height: 1.08
      }
    }

    @media(max-width:991px)and (min-width:992px) {
      .font-size-big-lg-max.font-family-dharma {
        font-size: 6.7rem
      }

      html[lang=bg-BG] .font-size-big-lg-max.font-family-dharma,
      html[lang=ja-JP] .font-size-big-lg-max.font-family-dharma {
        font-size: 4.6112rem
      }
    }

    .font-size-large {
      font-size: 2rem;
      line-height: 1.25
    }

    .font-size-large.font-family-dharma {
      font-size: 3.4rem;
      line-height: .9
    }

    html[lang=bg-BG] .font-size-large.font-family-dharma,
    html[lang=ja-JP] .font-size-large.font-family-dharma {
      font-size: 2.4rem;
      line-height: 1.1
    }

    @media(min-width:992px) {
      .font-size-large.font-family-dharma {
        font-size: 5.6rem
      }

      html[lang=bg-BG] .font-size-large.font-family-dharma,
      html[lang=ja-JP] .font-size-large.font-family-dharma {
        font-size: 3.8333rem
      }
    }

    @media(min-width:576px) {
      .font-size-large-sm-min {
        font-size: 2rem;
        line-height: 1.25
      }

      .font-size-large-sm-min.font-family-dharma {
        font-size: 3.4rem;
        line-height: .9
      }

      html[lang=bg-BG] .font-size-large-sm-min.font-family-dharma,
      html[lang=ja-JP] .font-size-large-sm-min.font-family-dharma {
        font-size: 2.4rem;
        line-height: 1.1
      }
    }

    @media(min-width:576px)and (min-width:992px) {
      .font-size-large-sm-min.font-family-dharma {
        font-size: 5.6rem
      }

      html[lang=bg-BG] .font-size-large-sm-min.font-family-dharma,
      html[lang=ja-JP] .font-size-large-sm-min.font-family-dharma {
        font-size: 3.8333rem
      }
    }

    @media(max-width:575px) {
      .font-size-large-sm-max {
        font-size: 2rem;
        line-height: 1.25
      }

      .font-size-large-sm-max.font-family-dharma {
        font-size: 3.4rem;
        line-height: .9
      }

      html[lang=bg-BG] .font-size-large-sm-max.font-family-dharma,
      html[lang=ja-JP] .font-size-large-sm-max.font-family-dharma {
        font-size: 2.4rem;
        line-height: 1.1
      }
    }

    @media(max-width:575px)and (min-width:992px) {
      .font-size-large-sm-max.font-family-dharma {
        font-size: 5.6rem
      }

      html[lang=bg-BG] .font-size-large-sm-max.font-family-dharma,
      html[lang=ja-JP] .font-size-large-sm-max.font-family-dharma {
        font-size: 3.8333rem
      }
    }

    @media(min-width:768px) {
      .font-size-large-md-min {
        font-size: 2rem;
        line-height: 1.25
      }

      .font-size-large-md-min.font-family-dharma {
        font-size: 3.4rem;
        line-height: .9
      }

      html[lang=bg-BG] .font-size-large-md-min.font-family-dharma,
      html[lang=ja-JP] .font-size-large-md-min.font-family-dharma {
        font-size: 2.4rem;
        line-height: 1.1
      }
    }

    @media(min-width:768px)and (min-width:992px) {
      .font-size-large-md-min.font-family-dharma {
        font-size: 5.6rem
      }

      html[lang=bg-BG] .font-size-large-md-min.font-family-dharma,
      html[lang=ja-JP] .font-size-large-md-min.font-family-dharma {
        font-size: 3.8333rem
      }
    }

    @media(max-width:767px) {
      .font-size-large-md-max {
        font-size: 2rem;
        line-height: 1.25
      }

      .font-size-large-md-max.font-family-dharma {
        font-size: 3.4rem;
        line-height: .9
      }

      html[lang=bg-BG] .font-size-large-md-max.font-family-dharma,
      html[lang=ja-JP] .font-size-large-md-max.font-family-dharma {
        font-size: 2.4rem;
        line-height: 1.1
      }
    }

    @media(max-width:767px)and (min-width:992px) {
      .font-size-large-md-max.font-family-dharma {
        font-size: 5.6rem
      }

      html[lang=bg-BG] .font-size-large-md-max.font-family-dharma,
      html[lang=ja-JP] .font-size-large-md-max.font-family-dharma {
        font-size: 3.8333rem
      }
    }

    @media(min-width:992px) {
      .font-size-large-lg-min {
        font-size: 2rem;
        line-height: 1.25
      }

      .font-size-large-lg-min.font-family-dharma {
        font-size: 3.4rem;
        line-height: .9
      }

      html[lang=bg-BG] .font-size-large-lg-min.font-family-dharma,
      html[lang=ja-JP] .font-size-large-lg-min.font-family-dharma {
        font-size: 2.4rem;
        line-height: 1.1
      }
    }

    @media(min-width:992px)and (min-width:992px) {
      .font-size-large-lg-min.font-family-dharma {
        font-size: 5.6rem
      }

      html[lang=bg-BG] .font-size-large-lg-min.font-family-dharma,
      html[lang=ja-JP] .font-size-large-lg-min.font-family-dharma {
        font-size: 3.8333rem
      }
    }

    @media(max-width:991px) {
      .font-size-large-lg-max {
        font-size: 2rem;
        line-height: 1.25
      }

      .font-size-large-lg-max.font-family-dharma {
        font-size: 3.4rem;
        line-height: .9
      }

      html[lang=bg-BG] .font-size-large-lg-max.font-family-dharma,
      html[lang=ja-JP] .font-size-large-lg-max.font-family-dharma {
        font-size: 2.4rem;
        line-height: 1.1
      }
    }

    @media(max-width:991px)and (min-width:992px) {
      .font-size-large-lg-max.font-family-dharma {
        font-size: 5.6rem
      }

      html[lang=bg-BG] .font-size-large-lg-max.font-family-dharma,
      html[lang=ja-JP] .font-size-large-lg-max.font-family-dharma {
        font-size: 3.8333rem
      }
    }

    .font-size-medium {
      font-size: 1.5rem;
      line-height: 1.25
    }

    .font-size-medium.font-family-dharma {
      font-size: 2.8rem;
      line-height: 1
    }

    html[lang=bg-BG] .font-size-medium.font-family-dharma,
    html[lang=ja-JP] .font-size-medium.font-family-dharma {
      font-size: 2rem;
      line-height: 1.1
    }

    @media(min-width:992px) {
      .font-size-medium.font-family-dharma {
        font-size: 4.4rem;
        line-height: .9
      }

      html[lang=bg-BG] .font-size-medium.font-family-dharma,
      html[lang=ja-JP] .font-size-medium.font-family-dharma {
        font-size: 3rem
      }
    }

    @media(min-width:576px) {
      .font-size-medium-sm-min {
        font-size: 1.5rem;
        line-height: 1.25
      }

      .font-size-medium-sm-min.font-family-dharma {
        font-size: 2.8rem;
        line-height: 1
      }

      html[lang=bg-BG] .font-size-medium-sm-min.font-family-dharma,
      html[lang=ja-JP] .font-size-medium-sm-min.font-family-dharma {
        font-size: 2rem;
        line-height: 1.1
      }
    }

    @media(min-width:576px)and (min-width:992px) {
      .font-size-medium-sm-min.font-family-dharma {
        font-size: 4.4rem;
        line-height: .9
      }

      html[lang=bg-BG] .font-size-medium-sm-min.font-family-dharma,
      html[lang=ja-JP] .font-size-medium-sm-min.font-family-dharma {
        font-size: 3rem
      }
    }

    @media(max-width:575px) {
      .font-size-medium-sm-max {
        font-size: 1.5rem;
        line-height: 1.25
      }

      .font-size-medium-sm-max.font-family-dharma {
        font-size: 2.8rem;
        line-height: 1
      }

      html[lang=bg-BG] .font-size-medium-sm-max.font-family-dharma,
      html[lang=ja-JP] .font-size-medium-sm-max.font-family-dharma {
        font-size: 2rem;
        line-height: 1.1
      }
    }

    @media(max-width:575px)and (min-width:992px) {
      .font-size-medium-sm-max.font-family-dharma {
        font-size: 4.4rem;
        line-height: .9
      }

      html[lang=bg-BG] .font-size-medium-sm-max.font-family-dharma,
      html[lang=ja-JP] .font-size-medium-sm-max.font-family-dharma {
        font-size: 3rem
      }
    }

    @media(min-width:768px) {
      .font-size-medium-md-min {
        font-size: 1.5rem;
        line-height: 1.25
      }

      .font-size-medium-md-min.font-family-dharma {
        font-size: 2.8rem;
        line-height: 1
      }

      html[lang=bg-BG] .font-size-medium-md-min.font-family-dharma,
      html[lang=ja-JP] .font-size-medium-md-min.font-family-dharma {
        font-size: 2rem;
        line-height: 1.1
      }
    }

    @media(min-width:768px)and (min-width:992px) {
      .font-size-medium-md-min.font-family-dharma {
        font-size: 4.4rem;
        line-height: .9
      }

      html[lang=bg-BG] .font-size-medium-md-min.font-family-dharma,
      html[lang=ja-JP] .font-size-medium-md-min.font-family-dharma {
        font-size: 3rem
      }
    }

    @media(max-width:767px) {
      .font-size-medium-md-max {
        font-size: 1.5rem;
        line-height: 1.25
      }

      .font-size-medium-md-max.font-family-dharma {
        font-size: 2.8rem;
        line-height: 1
      }

      html[lang=bg-BG] .font-size-medium-md-max.font-family-dharma,
      html[lang=ja-JP] .font-size-medium-md-max.font-family-dharma {
        font-size: 2rem;
        line-height: 1.1
      }
    }

    @media(max-width:767px)and (min-width:992px) {
      .font-size-medium-md-max.font-family-dharma {
        font-size: 4.4rem;
        line-height: .9
      }

      html[lang=bg-BG] .font-size-medium-md-max.font-family-dharma,
      html[lang=ja-JP] .font-size-medium-md-max.font-family-dharma {
        font-size: 3rem
      }
    }

    @media(min-width:992px) {
      .font-size-medium-lg-min {
        font-size: 1.5rem;
        line-height: 1.25
      }

      .font-size-medium-lg-min.font-family-dharma {
        font-size: 2.8rem;
        line-height: 1
      }

      html[lang=bg-BG] .font-size-medium-lg-min.font-family-dharma,
      html[lang=ja-JP] .font-size-medium-lg-min.font-family-dharma {
        font-size: 2rem;
        line-height: 1.1
      }
    }

    @media(min-width:992px)and (min-width:992px) {
      .font-size-medium-lg-min.font-family-dharma {
        font-size: 4.4rem;
        line-height: .9
      }

      html[lang=bg-BG] .font-size-medium-lg-min.font-family-dharma,
      html[lang=ja-JP] .font-size-medium-lg-min.font-family-dharma {
        font-size: 3rem
      }
    }

    @media(max-width:991px) {
      .font-size-medium-lg-max {
        font-size: 1.5rem;
        line-height: 1.25
      }

      .font-size-medium-lg-max.font-family-dharma {
        font-size: 2.8rem;
        line-height: 1
      }

      html[lang=bg-BG] .font-size-medium-lg-max.font-family-dharma,
      html[lang=ja-JP] .font-size-medium-lg-max.font-family-dharma {
        font-size: 2rem;
        line-height: 1.1
      }
    }

    @media(max-width:991px)and (min-width:992px) {
      .font-size-medium-lg-max.font-family-dharma {
        font-size: 4.4rem;
        line-height: .9
      }

      html[lang=bg-BG] .font-size-medium-lg-max.font-family-dharma,
      html[lang=ja-JP] .font-size-medium-lg-max.font-family-dharma {
        font-size: 3rem
      }
    }

    .font-size-intermediate {
      font-size: 1.25rem;
      line-height: 1.5
    }

    .font-size-intermediate.font-family-dharma {
      font-size: 2.3rem;
      line-height: 1
    }

    html[lang=bg-BG] .font-size-intermediate.font-family-dharma,
    html[lang=ja-JP] .font-size-intermediate.font-family-dharma {
      font-size: 1.6rem;
      line-height: 1.15
    }

    @media(min-width:992px) {
      .font-size-intermediate.font-family-dharma {
        font-size: 3.3rem
      }

      html[lang=bg-BG] .font-size-intermediate.font-family-dharma,
      html[lang=ja-JP] .font-size-intermediate.font-family-dharma {
        font-size: 2.2778rem
      }
    }

    @media(min-width:576px) {
      .font-size-intermediate-sm-min {
        font-size: 1.25rem;
        line-height: 1.5
      }

      .font-size-intermediate-sm-min.font-family-dharma {
        font-size: 2.3rem;
        line-height: 1
      }

      html[lang=bg-BG] .font-size-intermediate-sm-min.font-family-dharma,
      html[lang=ja-JP] .font-size-intermediate-sm-min.font-family-dharma {
        font-size: 1.6rem;
        line-height: 1.15
      }
    }

    @media(min-width:576px)and (min-width:992px) {
      .font-size-intermediate-sm-min.font-family-dharma {
        font-size: 3.3rem
      }

      html[lang=bg-BG] .font-size-intermediate-sm-min.font-family-dharma,
      html[lang=ja-JP] .font-size-intermediate-sm-min.font-family-dharma {
        font-size: 2.2778rem
      }
    }

    @media(max-width:575px) {
      .font-size-intermediate-sm-max {
        font-size: 1.25rem;
        line-height: 1.5
      }

      .font-size-intermediate-sm-max.font-family-dharma {
        font-size: 2.3rem;
        line-height: 1
      }

      html[lang=bg-BG] .font-size-intermediate-sm-max.font-family-dharma,
      html[lang=ja-JP] .font-size-intermediate-sm-max.font-family-dharma {
        font-size: 1.6rem;
        line-height: 1.15
      }
    }

    @media(max-width:575px)and (min-width:992px) {
      .font-size-intermediate-sm-max.font-family-dharma {
        font-size: 3.3rem
      }

      html[lang=bg-BG] .font-size-intermediate-sm-max.font-family-dharma,
      html[lang=ja-JP] .font-size-intermediate-sm-max.font-family-dharma {
        font-size: 2.2778rem
      }
    }

    @media(min-width:768px) {
      .font-size-intermediate-md-min {
        font-size: 1.25rem;
        line-height: 1.5
      }

      .font-size-intermediate-md-min.font-family-dharma {
        font-size: 2.3rem;
        line-height: 1
      }

      html[lang=bg-BG] .font-size-intermediate-md-min.font-family-dharma,
      html[lang=ja-JP] .font-size-intermediate-md-min.font-family-dharma {
        font-size: 1.6rem;
        line-height: 1.15
      }
    }

    @media(min-width:768px)and (min-width:992px) {
      .font-size-intermediate-md-min.font-family-dharma {
        font-size: 3.3rem
      }

      html[lang=bg-BG] .font-size-intermediate-md-min.font-family-dharma,
      html[lang=ja-JP] .font-size-intermediate-md-min.font-family-dharma {
        font-size: 2.2778rem
      }
    }

    @media(max-width:767px) {
      .font-size-intermediate-md-max {
        font-size: 1.25rem;
        line-height: 1.5
      }

      .font-size-intermediate-md-max.font-family-dharma {
        font-size: 2.3rem;
        line-height: 1
      }

      html[lang=bg-BG] .font-size-intermediate-md-max.font-family-dharma,
      html[lang=ja-JP] .font-size-intermediate-md-max.font-family-dharma {
        font-size: 1.6rem;
        line-height: 1.15
      }
    }

    @media(max-width:767px)and (min-width:992px) {
      .font-size-intermediate-md-max.font-family-dharma {
        font-size: 3.3rem
      }

      html[lang=bg-BG] .font-size-intermediate-md-max.font-family-dharma,
      html[lang=ja-JP] .font-size-intermediate-md-max.font-family-dharma {
        font-size: 2.2778rem
      }
    }

    @media(min-width:992px) {
      .font-size-intermediate-lg-min {
        font-size: 1.25rem;
        line-height: 1.5
      }

      .font-size-intermediate-lg-min.font-family-dharma {
        font-size: 2.3rem;
        line-height: 1
      }

      html[lang=bg-BG] .font-size-intermediate-lg-min.font-family-dharma,
      html[lang=ja-JP] .font-size-intermediate-lg-min.font-family-dharma {
        font-size: 1.6rem;
        line-height: 1.15
      }
    }

    @media(min-width:992px)and (min-width:992px) {
      .font-size-intermediate-lg-min.font-family-dharma {
        font-size: 3.3rem
      }

      html[lang=bg-BG] .font-size-intermediate-lg-min.font-family-dharma,
      html[lang=ja-JP] .font-size-intermediate-lg-min.font-family-dharma {
        font-size: 2.2778rem
      }
    }

    @media(max-width:991px) {
      .font-size-intermediate-lg-max {
        font-size: 1.25rem;
        line-height: 1.5
      }

      .font-size-intermediate-lg-max.font-family-dharma {
        font-size: 2.3rem;
        line-height: 1
      }

      html[lang=bg-BG] .font-size-intermediate-lg-max.font-family-dharma,
      html[lang=ja-JP] .font-size-intermediate-lg-max.font-family-dharma {
        font-size: 1.6rem;
        line-height: 1.15
      }
    }

    @media(max-width:991px)and (min-width:992px) {
      .font-size-intermediate-lg-max.font-family-dharma {
        font-size: 3.3rem
      }

      html[lang=bg-BG] .font-size-intermediate-lg-max.font-family-dharma,
      html[lang=ja-JP] .font-size-intermediate-lg-max.font-family-dharma {
        font-size: 2.2778rem
      }
    }

    .font-size-regular {
      font-size: 1rem;
      line-height: 1.5
    }

    .font-size-regular.font-family-dharma {
      font-size: 1.9rem;
      line-height: 1
    }

    html[lang=bg-BG] .font-size-regular.font-family-dharma,
    html[lang=ja-JP] .font-size-regular.font-family-dharma {
      font-size: 1.3rem;
      line-height: 1.15
    }

    @media(min-width:992px) {
      .font-size-regular.font-family-dharma {
        font-size: 2.8rem
      }

      html[lang=bg-BG] .font-size-regular.font-family-dharma,
      html[lang=ja-JP] .font-size-regular.font-family-dharma {
        font-size: 1.944rem
      }
    }

    @media(min-width:576px) {
      .font-size-regular-sm-min {
        font-size: 1rem;
        line-height: 1.5
      }

      .font-size-regular-sm-min.font-family-dharma {
        font-size: 1.9rem;
        line-height: 1
      }

      html[lang=bg-BG] .font-size-regular-sm-min.font-family-dharma,
      html[lang=ja-JP] .font-size-regular-sm-min.font-family-dharma {
        font-size: 1.3rem;
        line-height: 1.15
      }
    }

    @media(min-width:576px)and (min-width:992px) {
      .font-size-regular-sm-min.font-family-dharma {
        font-size: 2.8rem
      }

      html[lang=bg-BG] .font-size-regular-sm-min.font-family-dharma,
      html[lang=ja-JP] .font-size-regular-sm-min.font-family-dharma {
        font-size: 1.944rem
      }
    }

    @media(max-width:575px) {
      .font-size-regular-sm-max {
        font-size: 1rem;
        line-height: 1.5
      }

      .font-size-regular-sm-max.font-family-dharma {
        font-size: 1.9rem;
        line-height: 1
      }

      html[lang=bg-BG] .font-size-regular-sm-max.font-family-dharma,
      html[lang=ja-JP] .font-size-regular-sm-max.font-family-dharma {
        font-size: 1.3rem;
        line-height: 1.15
      }
    }

    @media(max-width:575px)and (min-width:992px) {
      .font-size-regular-sm-max.font-family-dharma {
        font-size: 2.8rem
      }

      html[lang=bg-BG] .font-size-regular-sm-max.font-family-dharma,
      html[lang=ja-JP] .font-size-regular-sm-max.font-family-dharma {
        font-size: 1.944rem
      }
    }

    @media(min-width:768px) {
      .font-size-regular-md-min {
        font-size: 1rem;
        line-height: 1.5
      }

      .font-size-regular-md-min.font-family-dharma {
        font-size: 1.9rem;
        line-height: 1
      }

      html[lang=bg-BG] .font-size-regular-md-min.font-family-dharma,
      html[lang=ja-JP] .font-size-regular-md-min.font-family-dharma {
        font-size: 1.3rem;
        line-height: 1.15
      }
    }

    @media(min-width:768px)and (min-width:992px) {
      .font-size-regular-md-min.font-family-dharma {
        font-size: 2.8rem
      }

      html[lang=bg-BG] .font-size-regular-md-min.font-family-dharma,
      html[lang=ja-JP] .font-size-regular-md-min.font-family-dharma {
        font-size: 1.944rem
      }
    }

    @media(max-width:767px) {
      .font-size-regular-md-max {
        font-size: 1rem;
        line-height: 1.5
      }

      .font-size-regular-md-max.font-family-dharma {
        font-size: 1.9rem;
        line-height: 1
      }

      html[lang=bg-BG] .font-size-regular-md-max.font-family-dharma,
      html[lang=ja-JP] .font-size-regular-md-max.font-family-dharma {
        font-size: 1.3rem;
        line-height: 1.15
      }
    }

    @media(max-width:767px)and (min-width:992px) {
      .font-size-regular-md-max.font-family-dharma {
        font-size: 2.8rem
      }

      html[lang=bg-BG] .font-size-regular-md-max.font-family-dharma,
      html[lang=ja-JP] .font-size-regular-md-max.font-family-dharma {
        font-size: 1.944rem
      }
    }

    @media(min-width:992px) {
      .font-size-regular-lg-min {
        font-size: 1rem;
        line-height: 1.5
      }

      .font-size-regular-lg-min.font-family-dharma {
        font-size: 1.9rem;
        line-height: 1
      }

      html[lang=bg-BG] .font-size-regular-lg-min.font-family-dharma,
      html[lang=ja-JP] .font-size-regular-lg-min.font-family-dharma {
        font-size: 1.3rem;
        line-height: 1.15
      }
    }

    @media(min-width:992px)and (min-width:992px) {
      .font-size-regular-lg-min.font-family-dharma {
        font-size: 2.8rem
      }

      html[lang=bg-BG] .font-size-regular-lg-min.font-family-dharma,
      html[lang=ja-JP] .font-size-regular-lg-min.font-family-dharma {
        font-size: 1.944rem
      }
    }

    @media(max-width:991px) {
      .font-size-regular-lg-max {
        font-size: 1rem;
        line-height: 1.5
      }

      .font-size-regular-lg-max.font-family-dharma {
        font-size: 1.9rem;
        line-height: 1
      }

      html[lang=bg-BG] .font-size-regular-lg-max.font-family-dharma,
      html[lang=ja-JP] .font-size-regular-lg-max.font-family-dharma {
        font-size: 1.3rem;
        line-height: 1.15
      }
    }

    @media(max-width:991px)and (min-width:992px) {
      .font-size-regular-lg-max.font-family-dharma {
        font-size: 2.8rem
      }

      html[lang=bg-BG] .font-size-regular-lg-max.font-family-dharma,
      html[lang=ja-JP] .font-size-regular-lg-max.font-family-dharma {
        font-size: 1.944rem
      }
    }

    .font-size-small,
    small {
      font-size: .75rem;
      line-height: 1.5
    }

    .font-size-small.font-family-dharma,
    small.font-family-dharma {
      font-size: 1.6rem;
      line-height: 1
    }

    html[lang=bg-BG] .font-size-small.font-family-dharma,
    html[lang=bg-BG] small.font-family-dharma,
    html[lang=ja-JP] .font-size-small.font-family-dharma,
    html[lang=ja-JP] small.font-family-dharma {
      font-size: 1.1rem;
      line-height: 1.2
    }

    @media(min-width:992px) {

      .font-size-small.font-family-dharma,
      small.font-family-dharma {
        font-size: 2.2rem
      }

      html[lang=bg-BG] .font-size-small.font-family-dharma,
      html[lang=bg-BG] small.font-family-dharma,
      html[lang=ja-JP] .font-size-small.font-family-dharma,
      html[lang=ja-JP] small.font-family-dharma {
        font-size: 1.555rem
      }
    }

    @media(min-width:576px) {
      .font-size-small-sm-min {
        font-size: .75rem;
        line-height: 1.5
      }

      .font-size-small-sm-min.font-family-dharma {
        font-size: 1.6rem;
        line-height: 1
      }

      html[lang=bg-BG] .font-size-small-sm-min.font-family-dharma,
      html[lang=ja-JP] .font-size-small-sm-min.font-family-dharma {
        font-size: 1.1rem;
        line-height: 1.2
      }
    }

    @media(min-width:576px)and (min-width:992px) {
      .font-size-small-sm-min.font-family-dharma {
        font-size: 2.2rem
      }

      html[lang=bg-BG] .font-size-small-sm-min.font-family-dharma,
      html[lang=ja-JP] .font-size-small-sm-min.font-family-dharma {
        font-size: 1.555rem
      }
    }

    @media(max-width:575px) {
      .font-size-small-sm-max {
        font-size: .75rem;
        line-height: 1.5
      }

      .font-size-small-sm-max.font-family-dharma {
        font-size: 1.6rem;
        line-height: 1
      }

      html[lang=bg-BG] .font-size-small-sm-max.font-family-dharma,
      html[lang=ja-JP] .font-size-small-sm-max.font-family-dharma {
        font-size: 1.1rem;
        line-height: 1.2
      }
    }

    @media(max-width:575px)and (min-width:992px) {
      .font-size-small-sm-max.font-family-dharma {
        font-size: 2.2rem
      }

      html[lang=bg-BG] .font-size-small-sm-max.font-family-dharma,
      html[lang=ja-JP] .font-size-small-sm-max.font-family-dharma {
        font-size: 1.555rem
      }
    }

    @media(min-width:768px) {
      .font-size-small-md-min {
        font-size: .75rem;
        line-height: 1.5
      }

      .font-size-small-md-min.font-family-dharma {
        font-size: 1.6rem;
        line-height: 1
      }

      html[lang=bg-BG] .font-size-small-md-min.font-family-dharma,
      html[lang=ja-JP] .font-size-small-md-min.font-family-dharma {
        font-size: 1.1rem;
        line-height: 1.2
      }
    }

    @media(min-width:768px)and (min-width:992px) {
      .font-size-small-md-min.font-family-dharma {
        font-size: 2.2rem
      }

      html[lang=bg-BG] .font-size-small-md-min.font-family-dharma,
      html[lang=ja-JP] .font-size-small-md-min.font-family-dharma {
        font-size: 1.555rem
      }
    }

    @media(max-width:767px) {
      .font-size-small-md-max {
        font-size: .75rem;
        line-height: 1.5
      }

      .font-size-small-md-max.font-family-dharma {
        font-size: 1.6rem;
        line-height: 1
      }

      html[lang=bg-BG] .font-size-small-md-max.font-family-dharma,
      html[lang=ja-JP] .font-size-small-md-max.font-family-dharma {
        font-size: 1.1rem;
        line-height: 1.2
      }
    }

    @media(max-width:767px)and (min-width:992px) {
      .font-size-small-md-max.font-family-dharma {
        font-size: 2.2rem
      }

      html[lang=bg-BG] .font-size-small-md-max.font-family-dharma,
      html[lang=ja-JP] .font-size-small-md-max.font-family-dharma {
        font-size: 1.555rem
      }
    }

    @media(min-width:992px) {
      .font-size-small-lg-min {
        font-size: .75rem;
        line-height: 1.5
      }

      .font-size-small-lg-min.font-family-dharma {
        font-size: 1.6rem;
        line-height: 1
      }

      html[lang=bg-BG] .font-size-small-lg-min.font-family-dharma,
      html[lang=ja-JP] .font-size-small-lg-min.font-family-dharma {
        font-size: 1.1rem;
        line-height: 1.2
      }
    }

    @media(min-width:992px)and (min-width:992px) {
      .font-size-small-lg-min.font-family-dharma {
        font-size: 2.2rem
      }

      html[lang=bg-BG] .font-size-small-lg-min.font-family-dharma,
      html[lang=ja-JP] .font-size-small-lg-min.font-family-dharma {
        font-size: 1.555rem
      }
    }

    @media(max-width:991px) {
      .font-size-small-lg-max {
        font-size: .75rem;
        line-height: 1.5
      }

      .font-size-small-lg-max.font-family-dharma {
        font-size: 1.6rem;
        line-height: 1
      }

      html[lang=bg-BG] .font-size-small-lg-max.font-family-dharma,
      html[lang=ja-JP] .font-size-small-lg-max.font-family-dharma {
        font-size: 1.1rem;
        line-height: 1.2
      }
    }

    @media(max-width:991px)and (min-width:992px) {
      .font-size-small-lg-max.font-family-dharma {
        font-size: 2.2rem
      }

      html[lang=bg-BG] .font-size-small-lg-max.font-family-dharma,
      html[lang=ja-JP] .font-size-small-lg-max.font-family-dharma {
        font-size: 1.555rem
      }
    }

    .font-size-client {
      font-size: 16px;
      line-height: 1.5
    }

    .heading-big,
    h1 {
      font-size: 3rem;
      line-height: 1.25;
      margin-bottom: 1.5rem
    }

    .heading-big.font-family-dharma,
    h1.font-family-dharma {
      font-size: 4rem;
      line-height: .9
    }

    html[lang=bg-BG] .heading-big.font-family-dharma,
    html[lang=bg-BG] h1.font-family-dharma,
    html[lang=ja-JP] .heading-big.font-family-dharma,
    html[lang=ja-JP] h1.font-family-dharma {
      font-size: 3rem;
      line-height: 1.08
    }

    @media(min-width:992px) {

      .heading-big.font-family-dharma,
      h1.font-family-dharma {
        font-size: 6.7rem
      }

      html[lang=bg-BG] .heading-big.font-family-dharma,
      html[lang=bg-BG] h1.font-family-dharma,
      html[lang=ja-JP] .heading-big.font-family-dharma,
      html[lang=ja-JP] h1.font-family-dharma {
        font-size: 4.6112rem
      }
    }

    .heading-big:not(:first-child),
    h1:not(:first-child) {
      margin-top: 5rem
    }

    .heading-large,
    h2 {
      font-size: 2rem;
      line-height: 1.25;
      margin-bottom: 1.5rem
    }

    .heading-large.font-family-dharma,
    h2.font-family-dharma {
      font-size: 3.4rem;
      line-height: .9
    }

    html[lang=bg-BG] .heading-large.font-family-dharma,
    html[lang=bg-BG] h2.font-family-dharma,
    html[lang=ja-JP] .heading-large.font-family-dharma,
    html[lang=ja-JP] h2.font-family-dharma {
      font-size: 2.4rem;
      line-height: 1.1
    }

    @media(min-width:992px) {

      .heading-large.font-family-dharma,
      h2.font-family-dharma {
        font-size: 5.6rem
      }

      html[lang=bg-BG] .heading-large.font-family-dharma,
      html[lang=bg-BG] h2.font-family-dharma,
      html[lang=ja-JP] .heading-large.font-family-dharma,
      html[lang=ja-JP] h2.font-family-dharma {
        font-size: 3.8333rem
      }
    }

    .heading-large:not(:first-child),
    h2:not(:first-child) {
      margin-top: 4rem
    }

    .heading-medium,
    h3 {
      font-size: 1.5rem;
      line-height: 1.25;
      margin-bottom: 1rem
    }

    .heading-medium.font-family-dharma,
    h3.font-family-dharma {
      font-size: 2.8rem;
      line-height: 1
    }

    html[lang=bg-BG] .heading-medium.font-family-dharma,
    html[lang=bg-BG] h3.font-family-dharma,
    html[lang=ja-JP] .heading-medium.font-family-dharma,
    html[lang=ja-JP] h3.font-family-dharma {
      font-size: 2rem;
      line-height: 1.1
    }

    @media(min-width:992px) {

      .heading-medium.font-family-dharma,
      h3.font-family-dharma {
        font-size: 4.4rem;
        line-height: .9
      }

      html[lang=bg-BG] .heading-medium.font-family-dharma,
      html[lang=bg-BG] h3.font-family-dharma,
      html[lang=ja-JP] .heading-medium.font-family-dharma,
      html[lang=ja-JP] h3.font-family-dharma {
        font-size: 3rem
      }
    }

    .heading-medium:not(:first-child),
    h3:not(:first-child) {
      margin-top: 3rem
    }

    .heading-intermediate,
    h4 {
      font-size: 1.25rem;
      line-height: 1.5;
      margin-bottom: .75rem
    }

    .heading-intermediate.font-family-dharma,
    h4.font-family-dharma {
      font-size: 2.3rem;
      line-height: 1
    }

    html[lang=bg-BG] .heading-intermediate.font-family-dharma,
    html[lang=bg-BG] h4.font-family-dharma,
    html[lang=ja-JP] .heading-intermediate.font-family-dharma,
    html[lang=ja-JP] h4.font-family-dharma {
      font-size: 1.6rem;
      line-height: 1.15
    }

    @media(min-width:992px) {

      .heading-intermediate.font-family-dharma,
      h4.font-family-dharma {
        font-size: 3.3rem
      }

      html[lang=bg-BG] .heading-intermediate.font-family-dharma,
      html[lang=bg-BG] h4.font-family-dharma,
      html[lang=ja-JP] .heading-intermediate.font-family-dharma,
      html[lang=ja-JP] h4.font-family-dharma {
        font-size: 2.2778rem
      }
    }

    .heading-intermediate:not(:first-child),
    h4:not(:first-child) {
      margin-top: 3rem
    }

    .heading-regular,
    h5 {
      font-size: 1rem;
      line-height: 1.5;
      margin: .5rem 0
    }

    .heading-regular.font-family-dharma,
    h5.font-family-dharma {
      font-size: 1.9rem;
      line-height: 1
    }

    html[lang=bg-BG] .heading-regular.font-family-dharma,
    html[lang=bg-BG] h5.font-family-dharma,
    html[lang=ja-JP] .heading-regular.font-family-dharma,
    html[lang=ja-JP] h5.font-family-dharma {
      font-size: 1.3rem;
      line-height: 1.15
    }

    @media(min-width:992px) {

      .heading-regular.font-family-dharma,
      h5.font-family-dharma {
        font-size: 2.8rem
      }

      html[lang=bg-BG] .heading-regular.font-family-dharma,
      html[lang=bg-BG] h5.font-family-dharma,
      html[lang=ja-JP] .heading-regular.font-family-dharma,
      html[lang=ja-JP] h5.font-family-dharma {
        font-size: 1.944rem
      }
    }

    .heading-regular:not(:first-child),
    h5:not(:first-child) {
      margin-top: 2rem
    }

    .heading-small,
    h6 {
      font-size: .75rem;
      line-height: 1.5;
      font-weight: 400;
      margin: .5rem 0;
      opacity: .6;
      text-transform: uppercase
    }

    .heading-small.font-family-dharma,
    h6.font-family-dharma {
      font-size: 1.6rem;
      line-height: 1
    }

    html[lang=bg-BG] .heading-small.font-family-dharma,
    html[lang=bg-BG] h6.font-family-dharma,
    html[lang=ja-JP] .heading-small.font-family-dharma,
    html[lang=ja-JP] h6.font-family-dharma {
      font-size: 1.1rem;
      line-height: 1.2
    }

    @media(min-width:992px) {

      .heading-small.font-family-dharma,
      h6.font-family-dharma {
        font-size: 2.2rem
      }

      html[lang=bg-BG] .heading-small.font-family-dharma,
      html[lang=bg-BG] h6.font-family-dharma,
      html[lang=ja-JP] .heading-small.font-family-dharma,
      html[lang=ja-JP] h6.font-family-dharma {
        font-size: 1.555rem
      }
    }

    .heading-small:not(:first-child),
    h6:not(:first-child) {
      margin-top: 2rem
    }

    a,
    u {
      -webkit-text-decoration-skip: ink;
      text-decoration-skip-ink: auto
    }

    a {
      background-color: transparent;
      color: currentColor
    }

    a:not(.custom-focus):focus-visible,
    button:not(.custom-focus):focus-visible,
    input:not(.custom-focus):focus-visible {
      outline: 1px solid #0ff;
      outline-offset: 2px
    }

    a:not(.custom-focus):focus-visible.visible-offset-0,
    button:not(.custom-focus):focus-visible.visible-offset-0,
    input:not(.custom-focus):focus-visible.visible-offset-0 {
      outline-offset: -1px
    }

    .theme-native a:not(.custom-focus):focus-visible,
    .theme-native button:not(.custom-focus):focus-visible,
    .theme-native input:not(.custom-focus):focus-visible,
    [class*=theme-] .theme-native a:not(.custom-focus):focus-visible,
    [class*=theme-] .theme-native button:not(.custom-focus):focus-visible,
    [class*=theme-] .theme-native input:not(.custom-focus):focus-visible {
      outline-color: #00f;
      outline-color: Highlight;
      outline-color: -webkit-focus-ring-color
    }

    @media screen and (prefers-color-scheme:dark) {

      .theme-native a:not(.custom-focus):focus-visible,
      .theme-native button:not(.custom-focus):focus-visible,
      .theme-native input:not(.custom-focus):focus-visible,
      [class*=theme-] .theme-native a:not(.custom-focus):focus-visible,
      [class*=theme-] .theme-native button:not(.custom-focus):focus-visible,
      [class*=theme-] .theme-native input:not(.custom-focus):focus-visible {
        outline-color: #0ff
      }
    }

    .theme-native [class*=theme-]:not(.theme-native):not(.theme-white):not(.theme-grey-light):not(.theme-semi-transparent) a:not(.custom-focus):focus-visible,
    .theme-native [class*=theme-]:not(.theme-native):not(.theme-white):not(.theme-grey-light):not(.theme-semi-transparent) button:not(.custom-focus):focus-visible,
    .theme-native [class*=theme-]:not(.theme-native):not(.theme-white):not(.theme-grey-light):not(.theme-semi-transparent) input:not(.custom-focus):focus-visible {
      outline-color: #0ff
    }

    .theme-white a:not(.custom-focus):focus-visible,
    .theme-white button:not(.custom-focus):focus-visible,
    .theme-white input:not(.custom-focus):focus-visible,
    [class*=theme-] .theme-white a:not(.custom-focus):focus-visible,
    [class*=theme-] .theme-white button:not(.custom-focus):focus-visible,
    [class*=theme-] .theme-white input:not(.custom-focus):focus-visible {
      outline-color: #00f;
      outline-color: Highlight;
      outline-color: -webkit-focus-ring-color
    }

    small {
      display: inline-block;
      margin: .5rem 0
    }

    blockquote {
      padding: 2.25rem 0
    }

    .wrapper {
      clear: both;
      padding-left: 3rem;
      padding-right: 3rem;
      margin-left: auto;
      margin-right: auto;
      max-width: 1404px;
      width: 100%
    }

    @media(max-width:575px) {
      .wrapper {
        padding-left: 1.5rem;
        padding-right: 1.5rem
      }
    }

    .row {
      display: flex !important;
      flex-wrap: wrap;
      margin-left: -.75rem;
      margin-right: -.75rem
    }

    .row-full {
      min-width: 100%;
      width: 100vw;
      margin-left: 50%;
      margin-right: 0;
      transform: translateX(-50%)
    }

    [class*=col-] {
      float: left;
      margin-bottom: 1.5rem;
      padding-left: .75rem;
      padding-right: .75rem;
      min-height: 1px;
      min-width: 0;
      width: 100%
    }

    [class*=col-] .row:last-child {
      margin-bottom: -1.5rem
    }

    @media(min-width:992px) {
      .col-align-right {
        margin-left: auto;
        float: right;
        order: 1
      }
    }

    @media(min-width:992px) {
      .col-1 {
        flex-shrink: 0;
        width: 8.3333333333%
      }

      .col-1:nth-child(12n+1) {
        clear: both
      }
    }

    @media(min-width:992px) {
      .col-2 {
        flex-shrink: 0;
        width: 16.6666666667%
      }

      .col-2:nth-child(6n+1) {
        clear: both
      }
    }

    @media(min-width:992px) {
      .col-3 {
        flex-shrink: 0;
        width: 25%
      }

      .col-3:nth-child(4n+1) {
        clear: both
      }
    }

    @media(min-width:992px) {
      .col-4 {
        flex-shrink: 0;
        width: 33.3333333333%
      }

      .col-4:nth-child(3n+1) {
        clear: both
      }
    }

    @media(min-width:992px) {
      .col-5 {
        flex-shrink: 0;
        width: 41.6666666667%
      }
    }

    @media(min-width:992px) {
      .col-6 {
        flex-shrink: 0;
        width: 50%
      }

      .col-6:nth-child(odd) {
        clear: both
      }
    }

    @media(min-width:992px) {
      .col-7 {
        flex-shrink: 0;
        width: 58.3333333333%
      }
    }

    @media(min-width:992px) {
      .col-8 {
        flex-shrink: 0;
        width: 66.6666666667%
      }
    }

    @media(min-width:992px) {
      .col-9 {
        flex-shrink: 0;
        width: 75%
      }
    }

    @media(min-width:992px) {
      .col-10 {
        flex-shrink: 0;
        width: 83.3333333333%
      }
    }

    @media(min-width:992px) {
      .col-11 {
        flex-shrink: 0;
        width: 91.6666666667%
      }
    }

    @media(min-width:992px) {
      .col-12 {
        flex-shrink: 0;
        width: 100%
      }

      .col-12:nth-child(1n+1) {
        clear: both
      }
    }

    @media(min-width:992px) {
      .col-offset-0 {
        margin-left: 0
      }
    }

    @media(min-width:992px) {
      .col-offset-right-0 {
        margin-right: 0
      }
    }

    @media(min-width:992px) {
      .col-offset-1 {
        margin-left: 8.3333333333%
      }
    }

    @media(min-width:992px) {
      .col-offset-right-1 {
        margin-right: 8.3333333333%
      }
    }

    @media(min-width:992px) {
      .col-offset-2 {
        margin-left: 16.6666666667%
      }
    }

    @media(min-width:992px) {
      .col-offset-right-2 {
        margin-right: 16.6666666667%
      }
    }

    @media(min-width:992px) {
      .col-offset-3 {
        margin-left: 25%
      }
    }

    @media(min-width:992px) {
      .col-offset-right-3 {
        margin-right: 25%
      }
    }

    @media(min-width:992px) {
      .col-offset-4 {
        margin-left: 33.3333333333%
      }
    }

    @media(min-width:992px) {
      .col-offset-right-4 {
        margin-right: 33.3333333333%
      }
    }

    @media(min-width:992px) {
      .col-offset-5 {
        margin-left: 41.6666666667%
      }
    }

    @media(min-width:992px) {
      .col-offset-right-5 {
        margin-right: 41.6666666667%
      }
    }

    @media(min-width:992px) {
      .col-offset-6 {
        margin-left: 50%
      }
    }

    @media(min-width:992px) {
      .col-offset-right-6 {
        margin-right: 50%
      }
    }

    [class*=col-centered-] {
      float: none;
      margin-left: auto;
      margin-right: auto;
      clear: both
    }

    .col-centered-1 {
      flex-basis: 100%;
      max-width: 114.75px;
      width: auto
    }

    .col-centered-2 {
      flex-basis: 100%;
      max-width: 229.5px;
      width: auto
    }

    .col-centered-3 {
      flex-basis: 100%;
      max-width: 344.25px;
      width: auto
    }

    .col-centered-4 {
      flex-basis: 100%;
      max-width: 459px;
      width: auto
    }

    .col-centered-5 {
      flex-basis: 100%;
      max-width: 573.75px;
      width: auto
    }

    .col-centered-6 {
      flex-basis: 100%;
      max-width: 688.5px;
      width: auto
    }

    .col-centered-7 {
      flex-basis: 100%;
      max-width: 803.25px;
      width: auto
    }

    .col-centered-8 {
      flex-basis: 100%;
      max-width: 918px;
      width: auto
    }

    .col-centered-9 {
      flex-basis: 100%;
      max-width: 1032.75px;
      width: auto
    }

    .col-centered-10 {
      flex-basis: 100%;
      max-width: 1147.5px;
      width: auto
    }

    .col-centered-11 {
      flex-basis: 100%;
      max-width: 1262.25px;
      width: auto
    }

    .col-centered-12 {
      flex-basis: 100%;
      max-width: 1377px;
      width: auto
    }

    @media(min-width:768px)and (max-width:991px) {
      [class*=col-md-] {
        float: left
      }
    }

    @media(min-width:768px)and (max-width:991px) {
      .col-md-align-right {
        margin-left: auto;
        float: right;
        order: 1
      }
    }

    @media(min-width:768px)and (max-width:991px) {
      .col-md-1 {
        flex-shrink: 0;
        width: 8.3333333333%
      }

      .col-md-1:nth-child(12n+1) {
        clear: both
      }
    }

    @media(min-width:768px)and (max-width:991px) {
      .col-md-2 {
        flex-shrink: 0;
        width: 16.6666666667%
      }

      .col-md-2:nth-child(6n+1) {
        clear: both
      }
    }

    @media(min-width:768px)and (max-width:991px) {
      .col-md-3 {
        flex-shrink: 0;
        width: 25%
      }

      .col-md-3:nth-child(4n+1) {
        clear: both
      }
    }

    @media(min-width:768px)and (max-width:991px) {
      .col-md-4 {
        flex-shrink: 0;
        width: 33.3333333333%
      }

      .col-md-4:nth-child(3n+1) {
        clear: both
      }
    }

    @media(min-width:768px)and (max-width:991px) {
      .col-md-5 {
        flex-shrink: 0;
        width: 41.6666666667%
      }
    }

    @media(min-width:768px)and (max-width:991px) {
      .col-md-6 {
        flex-shrink: 0;
        width: 50%
      }

      .col-md-6:nth-child(odd) {
        clear: both
      }
    }

    @media(min-width:768px)and (max-width:991px) {
      .col-md-7 {
        flex-shrink: 0;
        width: 58.3333333333%
      }
    }

    @media(min-width:768px)and (max-width:991px) {
      .col-md-8 {
        flex-shrink: 0;
        width: 66.6666666667%
      }
    }

    @media(min-width:768px)and (max-width:991px) {
      .col-md-9 {
        flex-shrink: 0;
        width: 75%
      }
    }

    @media(min-width:768px)and (max-width:991px) {
      .col-md-10 {
        flex-shrink: 0;
        width: 83.3333333333%
      }
    }

    @media(min-width:768px)and (max-width:991px) {
      .col-md-11 {
        flex-shrink: 0;
        width: 91.6666666667%
      }
    }

    @media(min-width:768px)and (max-width:991px) {
      .col-md-12 {
        flex-shrink: 0;
        width: 100%
      }

      .col-md-12:nth-child(1n+1) {
        clear: both
      }
    }

    @media(min-width:768px)and (max-width:991px) {
      .col-md-offset-0 {
        margin-left: 0
      }
    }

    @media(min-width:768px)and (max-width:991px) {
      .col-md-offset-right-0 {
        margin-right: 0
      }
    }

    @media(min-width:768px)and (max-width:991px) {
      .col-md-offset-1 {
        margin-left: 8.3333333333%
      }
    }

    @media(min-width:768px)and (max-width:991px) {
      .col-md-offset-right-1 {
        margin-right: 8.3333333333%
      }
    }

    @media(min-width:768px)and (max-width:991px) {
      .col-md-offset-2 {
        margin-left: 16.6666666667%
      }
    }

    @media(min-width:768px)and (max-width:991px) {
      .col-md-offset-right-2 {
        margin-right: 16.6666666667%
      }
    }

    @media(min-width:768px)and (max-width:991px) {
      .col-md-offset-3 {
        margin-left: 25%
      }
    }

    @media(min-width:768px)and (max-width:991px) {
      .col-md-offset-right-3 {
        margin-right: 25%
      }
    }

    @media(min-width:768px)and (max-width:991px) {
      .col-md-offset-4 {
        margin-left: 33.3333333333%
      }
    }

    @media(min-width:768px)and (max-width:991px) {
      .col-md-offset-right-4 {
        margin-right: 33.3333333333%
      }
    }

    @media(min-width:768px)and (max-width:991px) {
      .col-md-offset-5 {
        margin-left: 41.6666666667%
      }
    }

    @media(min-width:768px)and (max-width:991px) {
      .col-md-offset-right-5 {
        margin-right: 41.6666666667%
      }
    }

    @media(min-width:768px)and (max-width:991px) {
      .col-md-offset-6 {
        margin-left: 50%
      }
    }

    @media(min-width:768px)and (max-width:991px) {
      .col-md-offset-right-6 {
        margin-right: 50%
      }
    }

    @media(max-width:991px) {
      [class*=col-md-centered-] {
        float: none;
        margin-left: auto;
        margin-right: auto;
        clear: both
      }
    }

    @media(max-width:991px) {
      .col-md-centered-1 {
        flex-basis: 100%;
        max-width: 114.75px;
        width: auto
      }
    }

    @media(max-width:991px) {
      .col-md-centered-2 {
        flex-basis: 100%;
        max-width: 229.5px;
        width: auto
      }
    }

    @media(max-width:991px) {
      .col-md-centered-3 {
        flex-basis: 100%;
        max-width: 344.25px;
        width: auto
      }
    }

    @media(max-width:991px) {
      .col-md-centered-4 {
        flex-basis: 100%;
        max-width: 459px;
        width: auto
      }
    }

    @media(max-width:991px) {
      .col-md-centered-5 {
        flex-basis: 100%;
        max-width: 573.75px;
        width: auto
      }
    }

    @media(max-width:991px) {
      .col-md-centered-6 {
        flex-basis: 100%;
        max-width: 688.5px;
        width: auto
      }
    }

    @media(max-width:991px) {
      .col-md-centered-7 {
        flex-basis: 100%;
        max-width: 803.25px;
        width: auto
      }
    }

    @media(max-width:991px) {
      .col-md-centered-8 {
        flex-basis: 100%;
        max-width: 918px;
        width: auto
      }
    }

    @media(max-width:991px) {
      .col-md-centered-9 {
        flex-basis: 100%;
        max-width: 1032.75px;
        width: auto
      }
    }

    @media(max-width:991px) {
      .col-md-centered-10 {
        flex-basis: 100%;
        max-width: 1147.5px;
        width: auto
      }
    }

    @media(max-width:991px) {
      .col-md-centered-11 {
        flex-basis: 100%;
        max-width: 1262.25px;
        width: auto
      }
    }

    @media(max-width:991px) {
      .col-md-centered-12 {
        flex-basis: 100%;
        max-width: 1377px;
        width: auto
      }
    }

    @media(max-width:767px) {
      [class*=col-sm-] {
        float: left
      }
    }

    @media(max-width:767px) {
      .col-sm-align-right {
        margin-left: auto;
        float: right;
        order: 1
      }
    }

    @media(max-width:767px) {
      .col-sm-1 {
        flex-shrink: 0;
        width: 8.3333333333%
      }

      .col-sm-1:nth-child(12n+1) {
        clear: both
      }
    }

    @media(max-width:767px) {
      .col-sm-2 {
        flex-shrink: 0;
        width: 16.6666666667%
      }

      .col-sm-2:nth-child(6n+1) {
        clear: both
      }
    }

    @media(max-width:767px) {
      .col-sm-3 {
        flex-shrink: 0;
        width: 25%
      }

      .col-sm-3:nth-child(4n+1) {
        clear: both
      }
    }

    @media(max-width:767px) {
      .col-sm-4 {
        flex-shrink: 0;
        width: 33.3333333333%
      }

      .col-sm-4:nth-child(3n+1) {
        clear: both
      }
    }

    @media(max-width:767px) {
      .col-sm-5 {
        flex-shrink: 0;
        width: 41.6666666667%
      }
    }

    @media(max-width:767px) {
      .col-sm-6 {
        flex-shrink: 0;
        width: 50%
      }

      .col-sm-6:nth-child(odd) {
        clear: both
      }
    }

    @media(max-width:767px) {
      .col-sm-7 {
        flex-shrink: 0;
        width: 58.3333333333%
      }
    }

    @media(max-width:767px) {
      .col-sm-8 {
        flex-shrink: 0;
        width: 66.6666666667%
      }
    }

    @media(max-width:767px) {
      .col-sm-9 {
        flex-shrink: 0;
        width: 75%
      }
    }

    @media(max-width:767px) {
      .col-sm-10 {
        flex-shrink: 0;
        width: 83.3333333333%
      }
    }

    @media(max-width:767px) {
      .col-sm-11 {
        flex-shrink: 0;
        width: 91.6666666667%
      }
    }

    @media(max-width:767px) {
      .col-sm-12 {
        flex-shrink: 0;
        width: 100%
      }

      .col-sm-12:nth-child(1n+1) {
        clear: both
      }
    }

    @media(max-width:767px) {
      .col-sm-offset-0 {
        margin-left: 0
      }
    }

    @media(max-width:767px) {
      .col-sm-offset-right-0 {
        margin-right: 0
      }
    }

    @media(max-width:767px) {
      .col-sm-offset-1 {
        margin-left: 8.3333333333%
      }
    }

    @media(max-width:767px) {
      .col-sm-offset-right-1 {
        margin-right: 8.3333333333%
      }
    }

    @media(max-width:767px) {
      .col-sm-offset-2 {
        margin-left: 16.6666666667%
      }
    }

    @media(max-width:767px) {
      .col-sm-offset-right-2 {
        margin-right: 16.6666666667%
      }
    }

    @media(max-width:767px) {
      .col-sm-offset-3 {
        margin-left: 25%
      }
    }

    @media(max-width:767px) {
      .col-sm-offset-right-3 {
        margin-right: 25%
      }
    }

    @media(max-width:767px) {
      .col-sm-offset-4 {
        margin-left: 33.3333333333%
      }
    }

    @media(max-width:767px) {
      .col-sm-offset-right-4 {
        margin-right: 33.3333333333%
      }
    }

    @media(max-width:767px) {
      .col-sm-offset-5 {
        margin-left: 41.6666666667%
      }
    }

    @media(max-width:767px) {
      .col-sm-offset-right-5 {
        margin-right: 41.6666666667%
      }
    }

    @media(max-width:767px) {
      .col-sm-offset-6 {
        margin-left: 50%
      }
    }

    @media(max-width:767px) {
      .col-sm-offset-right-6 {
        margin-right: 50%
      }
    }

    @media(max-width:767px) {
      [class*=col-sm-centered-] {
        float: none;
        margin-left: auto;
        margin-right: auto;
        clear: both
      }
    }

    @media(max-width:767px) {
      .col-sm-centered-1 {
        flex-basis: 100%;
        max-width: 114.75px;
        width: auto
      }
    }

    @media(max-width:767px) {
      .col-sm-centered-2 {
        flex-basis: 100%;
        max-width: 229.5px;
        width: auto
      }
    }

    @media(max-width:767px) {
      .col-sm-centered-3 {
        flex-basis: 100%;
        max-width: 344.25px;
        width: auto
      }
    }

    @media(max-width:767px) {
      .col-sm-centered-4 {
        flex-basis: 100%;
        max-width: 459px;
        width: auto
      }
    }

    @media(max-width:767px) {
      .col-sm-centered-5 {
        flex-basis: 100%;
        max-width: 573.75px;
        width: auto
      }
    }

    @media(max-width:767px) {
      .col-sm-centered-6 {
        flex-basis: 100%;
        max-width: 688.5px;
        width: auto
      }
    }

    @media(max-width:767px) {
      .col-sm-centered-7 {
        flex-basis: 100%;
        max-width: 803.25px;
        width: auto
      }
    }

    @media(max-width:767px) {
      .col-sm-centered-8 {
        flex-basis: 100%;
        max-width: 918px;
        width: auto
      }
    }

    @media(max-width:767px) {
      .col-sm-centered-9 {
        flex-basis: 100%;
        max-width: 1032.75px;
        width: auto
      }
    }

    @media(max-width:767px) {
      .col-sm-centered-10 {
        flex-basis: 100%;
        max-width: 1147.5px;
        width: auto
      }
    }

    @media(max-width:767px) {
      .col-sm-centered-11 {
        flex-basis: 100%;
        max-width: 1262.25px;
        width: auto
      }
    }

    @media(max-width:767px) {
      .col-sm-centered-12 {
        flex-basis: 100%;
        max-width: 1377px;
        width: auto
      }
    }

    @media(max-width:575px) {
      .col-xs-fullwidth {
        flex-shrink: 0;
        width: 100%;
        float: left;
        clear: both;
        margin-left: auto;
        margin-right: auto
      }
    }

    .row-custom [class*=col-] {
      clear: none
    }

    .no-transition-leave {
      display: none
    }

    @media(min-width:992px) {
      .tidal-scroll-item {
        opacity: 0;
        transition: opacity .25s cubic-bezier(.4, 0, 1, 1);
        will-change: opacity, transform
      }
    }

    .tidal-scroll-item:-webkit-full-screen-ancestor {
      -webkit-animation: none !important;
      animation: none !important
    }

    @media(min-width:768px) {
      .tidal-tablet-animation {
        opacity: 0;
        transition: opacity .25s cubic-bezier(.4, 0, 1, 1);
        will-change: opacity, transform
      }
    }

    .tidal-mobile-animation {
      will-change: opacity, transform
    }

    .tidal-lazy-image,
    .tidal-mobile-animation {
      opacity: 0;
      transition: opacity .25s cubic-bezier(.4, 0, 1, 1)
    }

    .tidal-lazy-image {
      will-change: opacity
    }

    .tidal-lazy-image--handled,
    .tidal-scroll-item--handled {
      will-change: auto
    }

    @media screen and (prefers-reduced-motion) {

      .tidal-lazy-image--handled,
      .tidal-scroll-item--handled {
        opacity: 1
      }
    }

    .tidal-lazy-image-wrapper--loaded.placeholder {
      background-color: transparent;
      transition: background-color .25s cubic-bezier(.4, 0, 1, 1)
    }

    .fade-in,
    .fade-in--reverse,
    .fade-in-enter-active {
      -webkit-animation: fade-in .25s cubic-bezier(.4, 0, 1, 1) both;
      animation: fade-in .25s cubic-bezier(.4, 0, 1, 1) both
    }

    .fade-in-top,
    .fade-in-top--reverse,
    .fade-in-top-enter-active {
      -webkit-animation: fade-in-top .25s cubic-bezier(.39, .575, .565, 1) both;
      animation: fade-in-top .25s cubic-bezier(.39, .575, .565, 1) both
    }

    .fade-in-bottom,
    .fade-in-bottom--reverse,
    .fade-in-bottom-enter-active {
      -webkit-animation: fade-in-bottom .25s cubic-bezier(.39, .575, .565, 1) both;
      animation: fade-in-bottom .25s cubic-bezier(.39, .575, .565, 1) both
    }

    .fade-in-right,
    .fade-in-right--reverse,
    .fade-in-right-enter-active {
      -webkit-animation: fade-in-right .25s cubic-bezier(.39, .575, .565, 1) both;
      animation: fade-in-right .25s cubic-bezier(.39, .575, .565, 1) both
    }

    .fade-in-left,
    .fade-in-left--reverse,
    .fade-in-left-enter-active {
      -webkit-animation: fade-in-left .25s cubic-bezier(.39, .575, .565, 1) both;
      animation: fade-in-left .25s cubic-bezier(.39, .575, .565, 1) both
    }

    .slide-in,
    .slide-in--reverse,
    .slide-in-enter-active {
      -webkit-animation: slide-in .5s cubic-bezier(.39, .575, .565, 1) both;
      animation: slide-in .5s cubic-bezier(.39, .575, .565, 1) both
    }

    .slide-in-above,
    .slide-in-above--reverse,
    .slide-in-above-enter-active {
      -webkit-animation: slide-in-above .5s cubic-bezier(.39, .575, .565, 1) both;
      animation: slide-in-above .5s cubic-bezier(.39, .575, .565, 1) both
    }

    .slide-in-left,
    .slide-in-left--reverse,
    .slide-in-left-enter-active {
      -webkit-animation: fade-in-left .5s cubic-bezier(.39, .575, .565, 1) both;
      animation: fade-in-left .5s cubic-bezier(.39, .575, .565, 1) both
    }

    .slide-in-right,
    .slide-in-right--reverse,
    .slide-in-right-enter-active {
      -webkit-animation: fade-in-right .5s cubic-bezier(.39, .575, .565, 1) both;
      animation: fade-in-right .5s cubic-bezier(.39, .575, .565, 1) both
    }

    [class*="--reverse"] {
      animation-direction: reverse
    }

    @media screen and (prefers-reduced-motion) {

      [class*=fade-in],
      [class*=slide-in] {
        -webkit-animation: unset;
        animation: unset
      }
    }

    @media(max-width:767px) {

      [class*=fade-in].tidal-scroll-item:not(.tidal-mobile-animation),
      [class*=slide-in].tidal-scroll-item:not(.tidal-mobile-animation) {
        -webkit-animation: unset;
        animation: unset
      }
    }

    @media(max-width:991px) {

      [class*=fade-in].tidal-scroll-item:not(.tidal-mobile-animation):not(.tidal-tablet-animation),
      [class*=slide-in].tidal-scroll-item:not(.tidal-mobile-animation):not(.tidal-tablet-animation) {
        -webkit-animation: unset;
        animation: unset
      }
    }

    @-webkit-keyframes fade-in {
      0% {
        opacity: 0
      }

      to {
        opacity: 1
      }
    }

    @keyframes fade-in {
      0% {
        opacity: 0
      }

      to {
        opacity: 1
      }
    }

    @-webkit-keyframes fade-in-top {
      0% {
        transform: translateY(-.75rem);
        opacity: 0
      }

      to {
        transform: translateY(0);
        opacity: 1
      }
    }

    @keyframes fade-in-top {
      0% {
        transform: translateY(-.75rem);
        opacity: 0
      }

      to {
        transform: translateY(0);
        opacity: 1
      }
    }

    @-webkit-keyframes fade-in-bottom {
      0% {
        transform: translateY(.75rem);
        opacity: 0
      }

      to {
        transform: translateY(0);
        opacity: 1
      }
    }

    @keyframes fade-in-bottom {
      0% {
        transform: translateY(.75rem);
        opacity: 0
      }

      to {
        transform: translateY(0);
        opacity: 1
      }
    }

    @-webkit-keyframes fade-in-right {
      0% {
        transform: translateX(3.75rem);
        opacity: 0
      }

      to {
        transform: translateX(0);
        opacity: 1
      }
    }

    @keyframes fade-in-right {
      0% {
        transform: translateX(3.75rem);
        opacity: 0
      }

      to {
        transform: translateX(0);
        opacity: 1
      }
    }

    @-webkit-keyframes fade-in-left {
      0% {
        transform: translateX(-3.75rem);
        opacity: 0
      }

      to {
        transform: translateX(0);
        opacity: 1
      }
    }

    @keyframes fade-in-left {
      0% {
        transform: translateX(-3.75rem);
        opacity: 0
      }

      to {
        transform: translateX(0);
        opacity: 1
      }
    }

    @-webkit-keyframes slide-in {
      0% {
        opacity: 0;
        transform: translateY(3.75rem) translateZ(0)
      }

      to {
        opacity: 1;
        transform: translateY(0) translateZ(0)
      }
    }

    @keyframes slide-in {
      0% {
        opacity: 0;
        transform: translateY(3.75rem) translateZ(0)
      }

      to {
        opacity: 1;
        transform: translateY(0) translateZ(0)
      }
    }

    @-webkit-keyframes slide-in-above {
      0% {
        opacity: 0;
        transform: translateY(-3.75rem) translateZ(0)
      }

      to {
        opacity: 1;
        transform: translateY(0) translateZ(0)
      }
    }

    @keyframes slide-in-above {
      0% {
        opacity: 0;
        transform: translateY(-3.75rem) translateZ(0)
      }

      to {
        opacity: 1;
        transform: translateY(0) translateZ(0)
      }
    }

    .plain-button,
    [class*=btn-] {
      -webkit-appearance: none;
      -moz-appearance: none;
      appearance: none;
      -webkit-user-select: none;
      -moz-user-select: none;
      user-select: none
    }

    .plain-button:focus,
    [class*=btn-]:focus {
      outline: 0
    }

    .plain-button::-moz-focus-inner,
    [class*=btn-]::-moz-focus-inner {
      border: 0
    }

    .plain-button {
      text-align: inherit
    }

    .plain-button,
    [class*=btn-] {
      color: inherit;
      cursor: pointer;
      background-color: transparent
    }

    [class*=btn-] {
      border: 1px solid transparent;
      border-radius: 6px;
      display: inline-block;
      font-family: inherit;
      font-size: 1rem;
      font-weight: 600;
      line-height: 3rem;
      height: 3rem;
      padding: 0 1.5rem;
      text-align: center;
      text-decoration: none;
      vertical-align: middle;
      white-space: nowrap;
      transition: color .25s cubic-bezier(.4, 0, 1, 1), background-color .25s cubic-bezier(.4, 0, 1, 1), border-color .25s cubic-bezier(.4, 0, 1, 1), opacity .25s cubic-bezier(.4, 0, 1, 1), transform .25s cubic-bezier(.4, 0, 1, 1)
    }

    [class*=btn-].text-uppercase {
      font-size: .75rem
    }

    [class*=btn-].text-uppercase.btn-small {
      font-size: .6667rem
    }

    [class*=btn-].disabled,
    [class*=btn-]:disabled {
      border-color: #a9a9a9;
      color: #a9a9a9;
      opacity: .4;
      cursor: not-allowed
    }

    [class*=btn-].disabled:hover,
    [class*=btn-]:disabled:hover {
      color: #a9a9a9
    }

    [class*=btn-]:active {
      transform: scale(.98)
    }

    [class*=btn-] i {
      margin-right: .5em
    }

    .btn-primary,
    .btn-primary-outline,
    .btn-secondary,
    .btn-secondary-outline,
    .btn-semi-transparent {
      position: relative;
      z-index: 0
    }

    .btn-primary-outline:before,
    .btn-primary:before,
    .btn-secondary-outline:before,
    .btn-secondary:before,
    .btn-semi-transparent:before {
      content: "";
      border-radius: inherit;
      position: absolute;
      z-index: -1;
      top: -1px;
      left: -1px;
      height: calc(100% + 2px);
      width: calc(100% + 2px);
      transition: background-color .25s cubic-bezier(.4, 0, 1, 1), opacity .25s cubic-bezier(.4, 0, 1, 1)
    }

    .btn-primary-outline:focus:before,
    .btn-primary-outline:hover:before,
    .btn-primary:focus:before,
    .btn-primary:hover:before,
    .btn-secondary-outline:focus:before,
    .btn-secondary-outline:hover:before,
    .btn-secondary:focus:before,
    .btn-secondary:hover:before,
    .btn-semi-transparent:focus:before,
    .btn-semi-transparent:hover:before {
      opacity: .8
    }

    .btn-primary-outline.disabled,
    .btn-primary-outline:disabled,
    .btn-primary.disabled,
    .btn-primary:disabled,
    .btn-secondary-outline.disabled,
    .btn-secondary-outline:disabled,
    .btn-secondary.disabled,
    .btn-secondary:disabled,
    .btn-semi-transparent.disabled,
    .btn-semi-transparent:disabled {
      color: #26282d
    }

    .btn-primary-outline.disabled:before,
    .btn-primary-outline:disabled:before,
    .btn-primary.disabled:before,
    .btn-primary:disabled:before,
    .btn-secondary-outline.disabled:before,
    .btn-secondary-outline:disabled:before,
    .btn-secondary.disabled:before,
    .btn-secondary:disabled:before,
    .btn-semi-transparent.disabled:before,
    .btn-semi-transparent:disabled:before {
      background-color: #a9a9a9;
      opacity: 1
    }

    .btn-primary-outline.disabled:focus,
    .btn-primary-outline.disabled:hover,
    .btn-primary-outline:disabled:focus,
    .btn-primary-outline:disabled:hover,
    .btn-primary.disabled:focus,
    .btn-primary.disabled:hover,
    .btn-primary:disabled:focus,
    .btn-primary:disabled:hover,
    .btn-secondary-outline.disabled:focus,
    .btn-secondary-outline.disabled:hover,
    .btn-secondary-outline:disabled:focus,
    .btn-secondary-outline:disabled:hover,
    .btn-secondary.disabled:focus,
    .btn-secondary.disabled:hover,
    .btn-secondary:disabled:focus,
    .btn-secondary:disabled:hover,
    .btn-semi-transparent.disabled:focus,
    .btn-semi-transparent.disabled:hover,
    .btn-semi-transparent:disabled:focus,
    .btn-semi-transparent:disabled:hover {
      color: #26282d
    }

    .btn-primary-outline.disabled:focus:before,
    .btn-primary-outline.disabled:hover:before,
    .btn-primary-outline:disabled:focus:before,
    .btn-primary-outline:disabled:hover:before,
    .btn-primary.disabled:focus:before,
    .btn-primary.disabled:hover:before,
    .btn-primary:disabled:focus:before,
    .btn-primary:disabled:hover:before,
    .btn-secondary-outline.disabled:focus:before,
    .btn-secondary-outline.disabled:hover:before,
    .btn-secondary-outline:disabled:focus:before,
    .btn-secondary-outline:disabled:hover:before,
    .btn-secondary.disabled:focus:before,
    .btn-secondary.disabled:hover:before,
    .btn-secondary:disabled:focus:before,
    .btn-secondary:disabled:hover:before,
    .btn-semi-transparent.disabled:focus:before,
    .btn-semi-transparent.disabled:hover:before,
    .btn-semi-transparent:disabled:focus:before,
    .btn-semi-transparent:disabled:hover:before {
      opacity: 1
    }

    .btn-medium {
      line-height: 2.50005rem;
      height: 2.50005rem;
      padding: 0 1rem
    }

    .btn-small {
      font-size: .75rem;
      line-height: 2.25rem;
      height: 2.25rem;
      padding: 0 .75rem
    }

    .btn-primary {
      color: #26282d;
      color: var(--ts-color-button-secondary, #26282d);
      border-color: #0ff;
      border-color: var(--ts-color-button-primary, #0ff)
    }

    .btn-primary:before {
      background-color: #0ff;
      background-color: var(--ts-color-button-primary, #0ff)
    }

    .btn-primary-outline {
      color: #0ff;
      color: var(--ts-color-button-primary, #0ff);
      border-color: #0ff;
      border-color: var(--ts-color-button-primary, #0ff)
    }

    .btn-primary-outline:before {
      background-color: #0ff;
      background-color: var(--ts-color-button-primary, #0ff);
      opacity: 0
    }

    .btn-primary-outline:focus:before,
    .btn-primary-outline:hover:before {
      opacity: .2
    }

    .btn-primary-outline.disabled,
    .btn-primary-outline:disabled {
      color: currentColor !important
    }

    .btn-primary-outline.disabled:before,
    .btn-primary-outline:disabled:before {
      opacity: 0 !important
    }

    .btn-semi-transparent {
      color: var(--ts-color-button-secondary, inherit)
    }

    .btn-semi-transparent:before {
      background-color: currentColor;
      background-color: var(--ts-color-button-primary, currentColor);
      opacity: .4
    }

    .btn-semi-transparent:focus:before,
    .btn-semi-transparent:hover:before {
      opacity: .2
    }

    .btn-secondary {
      color: #26282d;
      color: var(--ts-color-button-primary, #26282d);
      border-color: #fff;
      border-color: var(--ts-color-button-secondary, #fff)
    }

    .btn-secondary:before {
      background-color: #fff;
      background-color: var(--ts-color-button-secondary, #fff)
    }

    .theme-native .btn-secondary {
      color: #fff;
      color: var(--ts-color-button-primary, #fff);
      border-color: #000;
      border-color: var(--ts-color-button-secondary, #000)
    }

    .theme-native .btn-secondary:before {
      background-color: #000;
      background-color: var(--ts-color-button-secondary, #000)
    }

    @media screen and (prefers-color-scheme:dark) {
      .theme-native .btn-secondary {
        color: #26282d;
        color: var(--ts-color-button-primary, #26282d);
        border-color: #fff;
        border-color: var(--ts-color-button-secondary, #fff)
      }

      .theme-native .btn-secondary:before {
        background-color: #fff;
        background-color: var(--ts-color-button-secondary, #fff)
      }
    }

    .theme-dark .btn-secondary {
      color: #26282d;
      color: var(--ts-color-button-primary, #26282d);
      border-color: #fff;
      border-color: var(--ts-color-button-secondary, #fff)
    }

    .theme-dark .btn-secondary:before {
      background-color: #fff;
      background-color: var(--ts-color-button-secondary, #fff)
    }

    .theme-white .btn-secondary {
      color: #fff;
      color: var(--ts-color-button-primary, #fff);
      border-color: #000;
      border-color: var(--ts-color-button-secondary, #000)
    }

    .theme-white .btn-secondary:before {
      background-color: #000;
      background-color: var(--ts-color-button-secondary, #000)
    }

    .btn-secondary.disabled,
    .btn-secondary:disabled {
      color: #26282d !important;
      border-color: #a9a9a9 !important
    }

    .btn-secondary.disabled:before,
    .btn-secondary:disabled:before {
      background-color: #a9a9a9 !important
    }

    .btn-secondary-outline {
      color: var(--ts-color-button-secondary, inherit);
      border-color: currentColor;
      border-color: var(--ts-color-button-secondary, currentColor)
    }

    .btn-secondary-outline:before {
      background-color: #fff;
      background-color: var(--ts-color-button-secondary, #fff);
      opacity: 0
    }

    .theme-native .btn-secondary-outline:focus:before,
    .theme-native .btn-secondary-outline:hover:before {
      background-color: rgba(0, 0, 0, .2);
      background-color: var(--ts-color-button-secondary, rgba(0, 0, 0, .2))
    }

    @media screen and (prefers-color-scheme:dark) {

      .theme-native .btn-secondary-outline:focus:before,
      .theme-native .btn-secondary-outline:hover:before {
        background-color: #fff;
        background-color: var(--ts-color-button-secondary, #fff)
      }
    }

    .theme-dark .btn-secondary-outline:focus:before,
    .theme-dark .btn-secondary-outline:hover:before {
      background-color: #fff;
      background-color: var(--ts-color-button-secondary, #fff)
    }

    .theme-white .btn-secondary-outline:focus:before,
    .theme-white .btn-secondary-outline:hover:before {
      background-color: rgba(0, 0, 0, .2);
      background-color: var(--ts-color-button-secondary, rgba(0, 0, 0, .2))
    }

    .btn-secondary-outline:focus:before,
    .btn-secondary-outline:hover:before {
      opacity: .2
    }

    .btn-secondary-outline.disabled,
    .btn-secondary-outline:disabled {
      color: currentColor !important
    }

    .btn-secondary-outline.disabled:before,
    .btn-secondary-outline:disabled:before {
      opacity: 0 !important
    }

    .btn-action {
      color: #fff;
      background-color: #26282d;
      border-color: #26282d;
      padding-left: .375rem;
      padding-right: .375rem;
      max-width: 100%;
      min-width: 11rem
    }

    .btn-action.btn-medium {
      min-width: 10rem
    }

    .btn-action.btn-small {
      min-width: 8rem
    }

    .btn-action.fullwidth {
      min-width: 100%
    }

    .btn-action:focus,
    .btn-action:hover {
      background-color: #000
    }

    .btn-action.disabled,
    .btn-action:disabled {
      border-color: #26282d
    }

    .btn-link {
      color: var(--ts-color-button-secondary, inherit)
    }

    .btn-link:focus,
    .btn-link:hover {
      color: #0ff;
      color: var(--ts-color-button-primary, #0ff)
    }

    .btn-link.disabled,
    .btn-link:disabled {
      border-color: transparent
    }

    .btn-link:active {
      transform: none
    }

    [class*=btn-client-] {
      transition: background-color .25s cubic-bezier(.4, 0, 1, 1), color .25s cubic-bezier(.4, 0, 1, 1);
      border-radius: 16px;
      border: 0;
      font-size: 16px;
      padding-top: 1px;
      line-height: 3em;
      height: 48px
    }

    [class*=btn-client-]+[class*=btn-client-] {
      margin-top: 16px
    }

    [class*=btn-client-].disabled,
    [class*=btn-client-].disabled:hover,
    [class*=btn-client-]:disabled,
    [class*=btn-client-]:disabled:hover {
      color: rgba(229, 238, 255, .6);
      background-color: rgba(229, 238, 255, .2)
    }

    .btn-client-primary {
      background-color: #fff;
      color: #000
    }

    .btn-client-primary:focus,
    .btn-client-primary:hover {
      background-color: hsla(0, 0%, 100%, .8)
    }

    .btn-client-secondary {
      background-color: rgba(229, 238, 255, .2);
      color: #fff
    }

    .btn-client-secondary:focus,
    .btn-client-secondary:hover {
      background-color: rgba(229, 238, 255, .3)
    }

    .btn-client-facebook {
      background-color: #1877f2;
      color: #fff
    }

    .btn-client-facebook:focus,
    .btn-client-facebook:hover {
      background-color: rgba(24, 119, 242, .8)
    }

    .btn-client-twitter {
      background-color: #55acee;
      color: #fff
    }

    .btn-client-twitter:focus,
    .btn-client-twitter:hover {
      background-color: rgba(85, 172, 238, .8)
    }

    .btn-extra-padding {
      padding-left: 3rem;
      padding-right: 3rem
    }

    .btn-rounded {
      border-radius: 10px
    }

    .img-round {
      border-radius: 100%
    }

    .img-cover {
      -o-object-fit: cover;
      object-fit: cover
    }

    @supports((-o-object-fit: cover) or (object-fit: cover)) {
      .img-cover {
        height: 100%;
        width: 100%
      }
    }

    .placeholder {
      background-color: rgba(120, 119, 127, .12);
      height: 0
    }

    .placeholder.transparent {
      background-color: transparent !important
    }

    .table-wrapper {
      max-width: 100%;
      overflow-x: auto;
      -webkit-overflow-scrolling: touch
    }

    table {
      border-spacing: 0;
      border-collapse: collapse;
      font-size: .8889rem;
      overflow: auto;
      width: 100%
    }

    tr {
      transition: background-color .25s cubic-bezier(.4, 0, 1, 1);
      border-bottom: 1px solid rgba(120, 119, 127, .2)
    }

    tbody tr:hover {
      background-color: hsla(0, 0%, 60.8%, .06)
    }

    td,
    th {
      padding: .75rem 1.5rem
    }

    th {
      font-size: .75rem;
      font-weight: 600;
      letter-spacing: .05rem;
      text-align: left;
      text-transform: uppercase
    }

    .table-striped tr:nth-of-type(2n) {
      background-color: hsla(0, 0%, 60.8%, .08)
    }

    .table-striped tr:hover {
      background-color: hsla(0, 0%, 60.8%, .16)
    }

    .table-bordered,
    .table-bordered td,
    .table-bordered th {
      border: 1px solid rgba(120, 119, 127, .2)
    }

    .table-compact td,
    .table-compact th {
      padding: .75rem
    }

    .table-header thead {
      background-color: hsla(0, 0%, 60.8%, .16)
    }

    .tidal-icon {
      position: relative;
      top: .1667em;
      display: inline-block
    }

    .tidal-icon svg {
      fill: currentColor;
      height: 1em;
      width: 1em
    }

    .tidal-icon--stroke svg {
      stroke: currentColor
    }

    .tidal-icon--stroke-only svg {
      fill: inherit;
      stroke: currentColor
    }

    .transition-bg-color,
    [class*=theme-] {
      transition: color .25s cubic-bezier(.4, 0, 1, 1), background-color .25s cubic-bezier(.4, 0, 1, 1)
    }

    .theme-native,
    [class*=theme-] .theme-native {
      background-color: #fff;
      color: #26282d
    }

    @media screen and (prefers-color-scheme:dark) {

      .theme-native,
      [class*=theme-] .theme-native {
        background-color: #101012;
        color: #fff
      }
    }

    .theme-dark,
    [class*=theme-] .theme-dark {
      background-color: #000;
      color: #fff
    }

    .theme-light-dark,
    [class*=theme-] .theme-light-dark {
      background-color: #101012;
      color: #fff
    }

    @media screen and (prefers-color-scheme:dark) {

      .theme-light-dark,
      [class*=theme-] .theme-light-dark {
        background-color: #09090a
      }
    }

    .theme-white,
    [class*=theme-] .theme-white {
      background-color: #fff;
      color: #26282d
    }

    .theme-grey-light,
    [class*=theme-] .theme-grey-light {
      background-color: #f4f4f4;
      color: #26282d
    }

    @media screen and (prefers-color-scheme:dark) {
      .theme-native .theme-grey-light {
        background-color: #17171a;
        color: #fff
      }
    }

    .theme-grey,
    [class*=theme-] .theme-grey {
      background-color: #26282d;
      color: #fff
    }

    @media screen and (prefers-color-scheme:dark) {
      .theme-native .theme-grey {
        background-color: #1d1e22
      }
    }

    .theme-transparent,
    [class*=theme-] .theme-transparent {
      background-color: transparent
    }

    .theme-mint,
    [class*=theme-] .theme-mint {
      background-color: #0ff;
      color: #26282d
    }

    .theme-raspberry,
    [class*=theme-] .theme-raspberry {
      background-color: #cd005e;
      color: #fff
    }

    .theme-semi-transparent,
    [class*=theme-] .theme-semi-transparent {
      background-color: hsla(0, 0%, 100%, .1)
    }

    .theme-native .theme-semi-transparent,
    .theme-native [class*=theme-] .theme-semi-transparent,
    .theme-white .theme-semi-transparent,
    .theme-white [class*=theme-] .theme-semi-transparent {
      background-color: rgba(38, 40, 45, .05)
    }

    @media screen and (prefers-color-scheme:dark) {

      .theme-native .theme-semi-transparent,
      .theme-native [class*=theme-] .theme-semi-transparent {
        background-color: hsla(0, 0%, 100%, .1)
      }
    }

    .theme-success,
    [class*=theme-] .theme-success {
      background-color: #0abf37;
      color: #fff
    }

    .theme-error,
    [class*=theme-] .theme-error {
      background-color: #ed1c24;
      color: #fff
    }

    .gradient-top {
      background-image: linear-gradient(180deg, rgba(38, 40, 45, .6) 0, rgba(35, 37, 41, .4428) 19%, rgba(31, 33, 37, .3246) 34%, rgba(27, 28, 32, .2292) 47%, rgba(23, 24, 27, .1668) 56.5%, rgba(19, 20, 22, .1164) 65%, rgba(14, 15, 16, .0756) 73%, rgba(9, 10, 11, .045) 80.2%, rgba(6, 6, 7, .0252) 86.1%, rgba(3, 3, 4, .0126) 91%, rgba(1, 1, 1, .0048) 95.2%, rgba(0, 0, 0, .0012) 98.2%, transparent)
    }

    .gradient-bottom {
      background-image: linear-gradient(0deg, rgba(38, 40, 45, .6) 0, rgba(35, 37, 41, .4428) 19%, rgba(31, 33, 37, .3246) 34%, rgba(27, 28, 32, .2292) 47%, rgba(23, 24, 27, .1668) 56.5%, rgba(19, 20, 22, .1164) 65%, rgba(14, 15, 16, .0756) 73%, rgba(9, 10, 11, .045) 80.2%, rgba(6, 6, 7, .0252) 86.1%, rgba(3, 3, 4, .0126) 91%, rgba(1, 1, 1, .0048) 95.2%, rgba(0, 0, 0, .0012) 98.2%, transparent)
    }

    .background-cover {
      background-size: cover;
      background-position: 50%;
      background-repeat: no-repeat
    }

    @media(min-width:1404px) {
      .background-attachment {
        background-attachment: fixed
      }
    }

    .block {
      display: block
    }

    .inline {
      display: inline
    }

    .inline-block {
      display: inline-block
    }

    .overflow-hidden {
      overflow: hidden
    }

    .overflow-scroll {
      overflow-y: auto;
      -webkit-overflow-scrolling: touch
    }

    .overflow-contain {
      overscroll-behavior-y: contain
    }

    .flex {
      display: flex !important;
      flex-wrap: wrap
    }

    @media (-ms-high-contrast:active),
    (-ms-high-contrast:none) {
      .flex:after {
        content: "";
        display: block;
        min-height: inherit;
        font-size: 0
      }
    }

    @media(min-width:576px) {
      .flex-sm-min {
        display: flex !important;
        flex-wrap: wrap
      }
    }

    @media(min-width:576px)and (-ms-high-contrast:active),
    (min-width:576px)and (-ms-high-contrast:none) {
      .flex-sm-min:after {
        content: "";
        display: block;
        min-height: inherit;
        font-size: 0
      }
    }

    @media(max-width:575px) {
      .flex-sm-max {
        display: flex !important;
        flex-wrap: wrap
      }
    }

    @media(max-width:575px)and (-ms-high-contrast:active),
    (max-width:575px)and (-ms-high-contrast:none) {
      .flex-sm-max:after {
        content: "";
        display: block;
        min-height: inherit;
        font-size: 0
      }
    }

    @media(min-width:768px) {
      .flex-md-min {
        display: flex !important;
        flex-wrap: wrap
      }
    }

    @media(min-width:768px)and (-ms-high-contrast:active),
    (min-width:768px)and (-ms-high-contrast:none) {
      .flex-md-min:after {
        content: "";
        display: block;
        min-height: inherit;
        font-size: 0
      }
    }

    @media(max-width:767px) {
      .flex-md-max {
        display: flex !important;
        flex-wrap: wrap
      }
    }

    @media(max-width:767px)and (-ms-high-contrast:active),
    (max-width:767px)and (-ms-high-contrast:none) {
      .flex-md-max:after {
        content: "";
        display: block;
        min-height: inherit;
        font-size: 0
      }
    }

    @media(min-width:992px) {
      .flex-lg-min {
        display: flex !important;
        flex-wrap: wrap
      }
    }

    @media(min-width:992px)and (-ms-high-contrast:active),
    (min-width:992px)and (-ms-high-contrast:none) {
      .flex-lg-min:after {
        content: "";
        display: block;
        min-height: inherit;
        font-size: 0
      }
    }

    @media(max-width:991px) {
      .flex-lg-max {
        display: flex !important;
        flex-wrap: wrap
      }
    }

    @media(max-width:991px)and (-ms-high-contrast:active),
    (max-width:991px)and (-ms-high-contrast:none) {
      .flex-lg-max:after {
        content: "";
        display: block;
        min-height: inherit;
        font-size: 0
      }
    }

    .flex-nowrap {
      flex-wrap: nowrap
    }

    .flex-column {
      flex-flow: column nowrap;
      min-height: 100%
    }

    .flex-column:after {
      min-height: auto
    }

    @media(min-width:576px) {
      .flex-column-sm-min {
        flex-flow: column nowrap;
        min-height: 100%
      }

      .flex-column-sm-min:after {
        min-height: auto
      }
    }

    @media(max-width:575px) {
      .flex-column-sm-max {
        flex-flow: column nowrap;
        min-height: 100%
      }

      .flex-column-sm-max:after {
        min-height: auto
      }
    }

    @media(min-width:768px) {
      .flex-column-md-min {
        flex-flow: column nowrap;
        min-height: 100%
      }

      .flex-column-md-min:after {
        min-height: auto
      }
    }

    @media(max-width:767px) {
      .flex-column-md-max {
        flex-flow: column nowrap;
        min-height: 100%
      }

      .flex-column-md-max:after {
        min-height: auto
      }
    }

    @media(min-width:992px) {
      .flex-column-lg-min {
        flex-flow: column nowrap;
        min-height: 100%
      }

      .flex-column-lg-min:after {
        min-height: auto
      }
    }

    @media(max-width:991px) {
      .flex-column-lg-max {
        flex-flow: column nowrap;
        min-height: 100%
      }

      .flex-column-lg-max:after {
        min-height: auto
      }
    }

    .flex-grow {
      flex-grow: 1;
      max-width: 100%
    }

    .flex-expand {
      flex: 1;
      max-width: 100%
    }

    .flex-noshrink {
      flex-shrink: 0
    }

    .flex-clear {
      min-height: 1px;
      min-width: 0;
      width: 100%
    }

    .border-radius {
      border-radius: 6px
    }

    @media(min-width:576px) {
      .border-radius-sm-min {
        border-radius: 6px
      }
    }

    @media(max-width:575px) {
      .border-radius-sm-max {
        border-radius: 6px
      }
    }

    @media(min-width:768px) {
      .border-radius-md-min {
        border-radius: 6px
      }
    }

    @media(max-width:767px) {
      .border-radius-md-max {
        border-radius: 6px
      }
    }

    @media(min-width:992px) {
      .border-radius-lg-min {
        border-radius: 6px
      }
    }

    @media(max-width:991px) {
      .border-radius-lg-max {
        border-radius: 6px
      }
    }

    .border-radius-small {
      border-radius: 3px
    }

    @media(min-width:576px) {
      .border-radius-small-sm-min {
        border-radius: 3px
      }
    }

    @media(max-width:575px) {
      .border-radius-small-sm-max {
        border-radius: 3px
      }
    }

    @media(min-width:768px) {
      .border-radius-small-md-min {
        border-radius: 3px
      }
    }

    @media(max-width:767px) {
      .border-radius-small-md-max {
        border-radius: 3px
      }
    }

    @media(min-width:992px) {
      .border-radius-small-lg-min {
        border-radius: 3px
      }
    }

    @media(max-width:991px) {
      .border-radius-small-lg-max {
        border-radius: 3px
      }
    }

    .border-radius-medium {
      border-radius: 8px
    }

    @media(min-width:576px) {
      .border-radius-medium-sm-min {
        border-radius: 8px
      }
    }

    @media(max-width:575px) {
      .border-radius-medium-sm-max {
        border-radius: 8px
      }
    }

    @media(min-width:768px) {
      .border-radius-medium-md-min {
        border-radius: 8px
      }
    }

    @media(max-width:767px) {
      .border-radius-medium-md-max {
        border-radius: 8px
      }
    }

    @media(min-width:992px) {
      .border-radius-medium-lg-min {
        border-radius: 8px
      }
    }

    @media(max-width:991px) {
      .border-radius-medium-lg-max {
        border-radius: 8px
      }
    }

    .border-radius-large {
      border-radius: 10px
    }

    @media(min-width:576px) {
      .border-radius-large-sm-min {
        border-radius: 10px
      }
    }

    @media(max-width:575px) {
      .border-radius-large-sm-max {
        border-radius: 10px
      }
    }

    @media(min-width:768px) {
      .border-radius-large-md-min {
        border-radius: 10px
      }
    }

    @media(max-width:767px) {
      .border-radius-large-md-max {
        border-radius: 10px
      }
    }

    @media(min-width:992px) {
      .border-radius-large-lg-min {
        border-radius: 10px
      }
    }

    @media(max-width:991px) {
      .border-radius-large-lg-max {
        border-radius: 10px
      }
    }

    .border-radius-huge {
      border-radius: 16px
    }

    @media(min-width:576px) {
      .border-radius-huge-sm-min {
        border-radius: 16px
      }
    }

    @media(max-width:575px) {
      .border-radius-huge-sm-max {
        border-radius: 16px
      }
    }

    @media(min-width:768px) {
      .border-radius-huge-md-min {
        border-radius: 16px
      }
    }

    @media(max-width:767px) {
      .border-radius-huge-md-max {
        border-radius: 16px
      }
    }

    @media(min-width:992px) {
      .border-radius-huge-lg-min {
        border-radius: 16px
      }
    }

    @media(max-width:991px) {
      .border-radius-huge-lg-max {
        border-radius: 16px
      }
    }

    .border-radius-full {
      border-radius: 100%
    }

    @media(min-width:576px) {
      .border-radius-full-sm-min {
        border-radius: 100%
      }
    }

    @media(max-width:575px) {
      .border-radius-full-sm-max {
        border-radius: 100%
      }
    }

    @media(min-width:768px) {
      .border-radius-full-md-min {
        border-radius: 100%
      }
    }

    @media(max-width:767px) {
      .border-radius-full-md-max {
        border-radius: 100%
      }
    }

    @media(min-width:992px) {
      .border-radius-full-lg-min {
        border-radius: 100%
      }
    }

    @media(max-width:991px) {
      .border-radius-full-lg-max {
        border-radius: 100%
      }
    }

    .position-absolute {
      position: absolute
    }

    .position-relative {
      position: relative
    }

    .position-top {
      top: 0
    }

    .position-bottom {
      bottom: 0
    }

    .position-stretch {
      left: 0;
      right: 0
    }

    .position-center-wrapper {
      display: flex !important;
      flex-direction: column;
      flex: 100;
      justify-content: center;
      text-align: center;
      min-height: 100%;
      position: relative
    }

    .position-center-content {
      position: absolute;
      top: 50%;
      left: 0;
      right: 0;
      transform: translateY(-50%);
      max-height: 100%
    }

    .position-center-item {
      margin-left: auto;
      margin-right: auto
    }

    @media(min-width:576px) {
      .position-reset-sm-min {
        position: static;
        top: auto;
        bottom: auto;
        left: auto;
        right: auto
      }
    }

    @media(max-width:575px) {
      .position-reset-sm-max {
        position: static;
        top: auto;
        bottom: auto;
        left: auto;
        right: auto
      }
    }

    @media(min-width:768px) {
      .position-reset-md-min {
        position: static;
        top: auto;
        bottom: auto;
        left: auto;
        right: auto
      }
    }

    @media(max-width:767px) {
      .position-reset-md-max {
        position: static;
        top: auto;
        bottom: auto;
        left: auto;
        right: auto
      }
    }

    @media(min-width:992px) {
      .position-reset-lg-min {
        position: static;
        top: auto;
        bottom: auto;
        left: auto;
        right: auto
      }
    }

    @media(max-width:991px) {
      .position-reset-lg-max {
        position: static;
        top: auto;
        bottom: auto;
        left: auto;
        right: auto
      }
    }

    @media(min-width:576px) {
      .position-center-content.position-reset-sm-min {
        transform: none
      }
    }

    @media(max-width:575px) {
      .position-center-content.position-reset-sm-max {
        transform: none
      }
    }

    @media(min-width:768px) {
      .position-center-content.position-reset-md-min {
        transform: none
      }
    }

    @media(max-width:767px) {
      .position-center-content.position-reset-md-max {
        transform: none
      }
    }

    @media(min-width:992px) {
      .position-center-content.position-reset-lg-min {
        transform: none
      }
    }

    @media(max-width:991px) {
      .position-center-content.position-reset-lg-max {
        transform: none
      }
    }

    .text-center {
      text-align: center
    }

    .text-right {
      text-align: right
    }

    @media(min-width:576px) {
      .text-right-sm-min {
        text-align: right
      }
    }

    @media(max-width:575px) {
      .text-right-sm-max {
        text-align: right
      }
    }

    @media(min-width:768px) {
      .text-right-md-min {
        text-align: right
      }
    }

    @media(max-width:767px) {
      .text-right-md-max {
        text-align: right
      }
    }

    @media(min-width:992px) {
      .text-right-lg-min {
        text-align: right
      }
    }

    @media(max-width:991px) {
      .text-right-lg-max {
        text-align: right
      }
    }

    .text-left {
      text-align: left
    }

    @media(min-width:576px) {
      .text-left-sm-min {
        text-align: left
      }
    }

    @media(max-width:575px) {
      .text-left-sm-max {
        text-align: left
      }
    }

    @media(min-width:768px) {
      .text-left-md-min {
        text-align: left
      }
    }

    @media(max-width:767px) {
      .text-left-md-max {
        text-align: left
      }
    }

    @media(min-width:992px) {
      .text-left-lg-min {
        text-align: left
      }
    }

    @media(max-width:991px) {
      .text-left-lg-max {
        text-align: left
      }
    }

    .text-align-intermediate {
      position: relative;
      top: .065em
    }

    .text-align-medium {
      position: relative;
      top: .125em
    }

    .text-align-large {
      position: relative;
      top: .2em
    }

    .text-align-huge {
      position: relative;
      top: .25em
    }

    .align-center {
      align-items: center
    }

    @media(min-width:576px) {
      .align-center-sm-min {
        align-items: center
      }
    }

    @media(max-width:575px) {
      .align-center-sm-max {
        align-items: center
      }
    }

    @media(min-width:768px) {
      .align-center-md-min {
        align-items: center
      }
    }

    @media(max-width:767px) {
      .align-center-md-max {
        align-items: center
      }
    }

    @media(min-width:992px) {
      .align-center-lg-min {
        align-items: center
      }
    }

    @media(max-width:991px) {
      .align-center-lg-max {
        align-items: center
      }
    }

    .justify-center {
      justify-content: center
    }

    @media(min-width:576px) {
      .justify-center-sm-min {
        justify-content: center
      }
    }

    @media(max-width:575px) {
      .justify-center-sm-max {
        justify-content: center
      }
    }

    @media(min-width:768px) {
      .justify-center-md-min {
        justify-content: center
      }
    }

    @media(max-width:767px) {
      .justify-center-md-max {
        justify-content: center
      }
    }

    @media(min-width:992px) {
      .justify-center-lg-min {
        justify-content: center
      }
    }

    @media(max-width:991px) {
      .justify-center-lg-max {
        justify-content: center
      }
    }

    .nowrap {
      white-space: nowrap;
      flex-wrap: nowrap;
      overflow-x: auto;
      overflow-y: hidden;
      -webkit-overflow-scrolling: touch
    }

    .nowrap-element,
    .nowrap-wrapper>[class*=col-] {
      display: inline-block;
      vertical-align: top;
      white-space: normal;
      float: none
    }

    @media(max-width:991px) {
      .nowrap-fill {
        margin-left: -3rem;
        margin-right: -3rem;
        padding-left: 2.25rem;
        padding-right: 3rem
      }

      .nowrap-fill:after {
        content: "";
        display: block;
        flex-basis: 2.25rem;
        flex-shrink: 0
      }
    }

    @media(max-width:575px) {
      .nowrap-fill {
        margin-left: -1.5rem;
        margin-right: -1.5rem;
        padding-left: .75rem;
        padding-right: 1.5rem
      }

      .nowrap-fill:after {
        flex-basis: .75rem
      }
    }

    .float-left {
      float: left;
      margin-right: auto
    }

    @media(min-width:576px) {
      .float-left-sm-min {
        float: left;
        margin-right: auto
      }
    }

    @media(max-width:575px) {
      .float-left-sm-max {
        float: left;
        margin-right: auto
      }
    }

    @media(min-width:768px) {
      .float-left-md-min {
        float: left;
        margin-right: auto
      }
    }

    @media(max-width:767px) {
      .float-left-md-max {
        float: left;
        margin-right: auto
      }
    }

    @media(min-width:992px) {
      .float-left-lg-min {
        float: left;
        margin-right: auto
      }
    }

    @media(max-width:991px) {
      .float-left-lg-max {
        float: left;
        margin-right: auto
      }
    }

    .float-right {
      float: right;
      margin-left: auto
    }

    @media(min-width:576px) {
      .float-right-sm-min {
        float: right;
        margin-left: auto
      }
    }

    @media(max-width:575px) {
      .float-right-sm-max {
        float: right;
        margin-left: auto
      }
    }

    @media(min-width:768px) {
      .float-right-md-min {
        float: right;
        margin-left: auto
      }
    }

    @media(max-width:767px) {
      .float-right-md-max {
        float: right;
        margin-left: auto
      }
    }

    @media(min-width:992px) {
      .float-right-lg-min {
        float: right;
        margin-left: auto
      }
    }

    @media(max-width:991px) {
      .float-right-lg-max {
        float: right;
        margin-left: auto
      }
    }

    .fullwidth {
      display: block;
      width: 100%
    }

    .fullheight {
      display: block;
      height: 100%
    }

    .fullscreen,
    .fullscreen-h {
      min-height: 30rem;
      min-height: 100vh
    }

    .fullscreen-h {
      min-height: calc(100vh - 4.5rem)
    }

    @media(min-width:992px) {
      .fullscreen-h {
        min-height: calc(100vh - 5.25rem)
      }
    }

    .fullwidth-stretch {
      overflow: hidden;
      margin-left: -3rem !important;
      margin-right: -3rem !important
    }

    @media(max-width:575px) {
      .fullwidth-stretch {
        margin-left: -1.5rem !important;
        margin-right: -1.5rem !important
      }
    }

    @media(min-width:576px) {
      .fullwidth-stretch-sm-min {
        overflow: hidden;
        margin-left: -3rem !important;
        margin-right: -3rem !important
      }
    }

    @media(min-width:576px)and (max-width:575px) {
      .fullwidth-stretch-sm-min {
        margin-left: -1.5rem !important;
        margin-right: -1.5rem !important
      }
    }

    @media(max-width:575px) {
      .fullwidth-stretch-sm-max {
        overflow: hidden;
        margin-left: -3rem !important;
        margin-right: -3rem !important
      }
    }

    @media(max-width:575px)and (max-width:575px) {
      .fullwidth-stretch-sm-max {
        margin-left: -1.5rem !important;
        margin-right: -1.5rem !important
      }
    }

    @media(min-width:768px) {
      .fullwidth-stretch-md-min {
        overflow: hidden;
        margin-left: -3rem !important;
        margin-right: -3rem !important
      }
    }

    @media(min-width:768px)and (max-width:575px) {
      .fullwidth-stretch-md-min {
        margin-left: -1.5rem !important;
        margin-right: -1.5rem !important
      }
    }

    @media(max-width:767px) {
      .fullwidth-stretch-md-max {
        overflow: hidden;
        margin-left: -3rem !important;
        margin-right: -3rem !important
      }
    }

    @media(max-width:767px)and (max-width:575px) {
      .fullwidth-stretch-md-max {
        margin-left: -1.5rem !important;
        margin-right: -1.5rem !important
      }
    }

    @media(min-width:992px) {
      .fullwidth-stretch-lg-min {
        overflow: hidden;
        margin-left: -3rem !important;
        margin-right: -3rem !important
      }
    }

    @media(min-width:992px)and (max-width:575px) {
      .fullwidth-stretch-lg-min {
        margin-left: -1.5rem !important;
        margin-right: -1.5rem !important
      }
    }

    @media(max-width:991px) {
      .fullwidth-stretch-lg-max {
        overflow: hidden;
        margin-left: -3rem !important;
        margin-right: -3rem !important
      }
    }

    @media(max-width:991px)and (max-width:575px) {
      .fullwidth-stretch-lg-max {
        margin-left: -1.5rem !important;
        margin-right: -1.5rem !important
      }
    }

    .max-screen,
    .max-screen-h {
      max-height: 100vh;
      overflow: hidden
    }

    .max-screen-h {
      max-height: calc(100vh - 4.5rem)
    }

    @media(min-width:992px) {
      .max-screen-h {
        max-height: calc(100vh - 5.25rem)
      }
    }

    .min-screen,
    .min-screen-h {
      min-height: 100vh
    }

    .min-screen-h {
      min-height: calc(100vh - 4.5rem)
    }

    @media(min-width:992px) {
      .min-screen-h {
        min-height: calc(100vh - 5.25rem)
      }
    }

    @media(min-width:576px) {
      .height-reset-sm-min {
        min-height: auto;
        max-height: none
      }
    }

    @media(max-width:575px) {
      .height-reset-sm-max {
        min-height: auto;
        max-height: none
      }
    }

    @media(min-width:768px) {
      .height-reset-md-min {
        min-height: auto;
        max-height: none
      }
    }

    @media(max-width:767px) {
      .height-reset-md-max {
        min-height: auto;
        max-height: none
      }
    }

    @media(min-width:992px) {
      .height-reset-lg-min {
        min-height: auto;
        max-height: none
      }
    }

    @media(max-width:991px) {
      .height-reset-lg-max {
        min-height: auto;
        max-height: none
      }
    }

    .margin-auto {
      margin: auto
    }

    @media(min-width:576px) {
      .margin-auto-sm-min {
        margin: auto
      }
    }

    @media(max-width:575px) {
      .margin-auto-sm-max {
        margin: auto
      }
    }

    @media(min-width:768px) {
      .margin-auto-md-min {
        margin: auto
      }
    }

    @media(max-width:767px) {
      .margin-auto-md-max {
        margin: auto
      }
    }

    @media(min-width:992px) {
      .margin-auto-lg-min {
        margin: auto
      }
    }

    @media(max-width:991px) {
      .margin-auto-lg-max {
        margin: auto
      }
    }

    .margin-0 {
      margin: 0 !important
    }

    .padding-0 {
      padding: 0 !important
    }

    .margin-1 {
      margin: 1.5rem !important
    }

    .padding-1 {
      padding: 1.5rem !important
    }

    .margin-2 {
      margin: 3rem !important
    }

    .padding-2 {
      padding: 3rem !important
    }

    .margin-3 {
      margin: 4.5rem !important
    }

    .padding-3 {
      padding: 4.5rem !important
    }

    .margin-4 {
      margin: 6rem !important
    }

    .padding-4 {
      padding: 6rem !important
    }

    .margin-top-nudge {
      margin-top: .75rem !important
    }

    .margin-top-nudge-1 {
      margin-top: 2.25rem !important
    }

    .margin-bottom-nudge {
      margin-bottom: .75rem !important
    }

    .margin-bottom-nudge-1 {
      margin-bottom: 2.25rem !important
    }

    .margin-left-nudge {
      margin-left: .75rem !important
    }

    .margin-left-nudge-1 {
      margin-left: 2.25rem !important
    }

    .margin-right-nudge {
      margin-right: .75rem !important
    }

    .margin-right-nudge-1 {
      margin-right: 2.25rem !important
    }

    .padding-top-nudge {
      padding-top: .75rem !important
    }

    .padding-top-nudge-1 {
      padding-top: 2.25rem !important
    }

    .padding-bottom-nudge {
      padding-bottom: .75rem !important
    }

    .padding-bottom-nudge-1 {
      padding-bottom: 2.25rem !important
    }

    .padding-left-nudge {
      padding-left: .75rem !important
    }

    .padding-left-nudge-1 {
      padding-left: 2.25rem !important
    }

    .padding-right-nudge {
      padding-right: .75rem !important
    }

    .padding-right-nudge-1 {
      padding-right: 2.25rem !important
    }

    .margin-top-0 {
      margin-top: 0 !important
    }

    .margin-bottom-0 {
      margin-bottom: 0 !important
    }

    .padding-top-0 {
      padding-top: 0 !important
    }

    .padding-bottom-0 {
      padding-bottom: 0 !important
    }

    .margin-top-1 {
      margin-top: 1.5rem !important
    }

    .margin-bottom-1 {
      margin-bottom: 1.5rem !important
    }

    .padding-top-1 {
      padding-top: 1.5rem !important
    }

    .padding-bottom-1 {
      padding-bottom: 1.5rem !important
    }

    .margin-top-2 {
      margin-top: 3rem !important
    }

    .margin-bottom-2 {
      margin-bottom: 3rem !important
    }

    .padding-top-2 {
      padding-top: 3rem !important
    }

    .padding-bottom-2 {
      padding-bottom: 3rem !important
    }

    .margin-top-3 {
      margin-top: 4.5rem !important
    }

    .margin-bottom-3 {
      margin-bottom: 4.5rem !important
    }

    .padding-top-3 {
      padding-top: 4.5rem !important
    }

    .padding-bottom-3 {
      padding-bottom: 4.5rem !important
    }

    .margin-top-4 {
      margin-top: 6rem !important
    }

    .margin-bottom-4 {
      margin-bottom: 6rem !important
    }

    .padding-top-4 {
      padding-top: 6rem !important
    }

    .padding-bottom-4 {
      padding-bottom: 6rem !important
    }

    @media(min-width:576px) {
      .margin-top-sm-min-0 {
        margin-top: 0 !important
      }
    }

    @media(max-width:575px) {
      .margin-top-sm-max-0 {
        margin-top: 0 !important
      }
    }

    @media(min-width:768px) {
      .margin-top-md-min-0 {
        margin-top: 0 !important
      }
    }

    @media(max-width:767px) {
      .margin-top-md-max-0 {
        margin-top: 0 !important
      }
    }

    @media(min-width:992px) {
      .margin-top-lg-min-0 {
        margin-top: 0 !important
      }
    }

    @media(max-width:991px) {
      .margin-top-lg-max-0 {
        margin-top: 0 !important
      }
    }

    @media(min-width:576px) {
      .margin-bottom-sm-min-0 {
        margin-bottom: 0 !important
      }
    }

    @media(max-width:575px) {
      .margin-bottom-sm-max-0 {
        margin-bottom: 0 !important
      }
    }

    @media(min-width:768px) {
      .margin-bottom-md-min-0 {
        margin-bottom: 0 !important
      }
    }

    @media(max-width:767px) {
      .margin-bottom-md-max-0 {
        margin-bottom: 0 !important
      }
    }

    @media(min-width:992px) {
      .margin-bottom-lg-min-0 {
        margin-bottom: 0 !important
      }
    }

    @media(max-width:991px) {
      .margin-bottom-lg-max-0 {
        margin-bottom: 0 !important
      }
    }

    @media(min-width:576px) {
      .padding-top-sm-min-0 {
        padding-top: 0 !important
      }
    }

    @media(max-width:575px) {
      .padding-top-sm-max-0 {
        padding-top: 0 !important
      }
    }

    @media(min-width:768px) {
      .padding-top-md-min-0 {
        padding-top: 0 !important
      }
    }

    @media(max-width:767px) {
      .padding-top-md-max-0 {
        padding-top: 0 !important
      }
    }

    @media(min-width:992px) {
      .padding-top-lg-min-0 {
        padding-top: 0 !important
      }
    }

    @media(max-width:991px) {
      .padding-top-lg-max-0 {
        padding-top: 0 !important
      }
    }

    @media(min-width:576px) {
      .padding-bottom-sm-min-0 {
        padding-bottom: 0 !important
      }
    }

    @media(max-width:575px) {
      .padding-bottom-sm-max-0 {
        padding-bottom: 0 !important
      }
    }

    @media(min-width:768px) {
      .padding-bottom-md-min-0 {
        padding-bottom: 0 !important
      }
    }

    @media(max-width:767px) {
      .padding-bottom-md-max-0 {
        padding-bottom: 0 !important
      }
    }

    @media(min-width:992px) {
      .padding-bottom-lg-min-0 {
        padding-bottom: 0 !important
      }
    }

    @media(max-width:991px) {
      .padding-bottom-lg-max-0 {
        padding-bottom: 0 !important
      }
    }

    @media(min-width:576px) {
      .margin-top-sm-min-1 {
        margin-top: 1.5rem !important
      }
    }

    @media(max-width:575px) {
      .margin-top-sm-max-1 {
        margin-top: 1.5rem !important
      }
    }

    @media(min-width:768px) {
      .margin-top-md-min-1 {
        margin-top: 1.5rem !important
      }
    }

    @media(max-width:767px) {
      .margin-top-md-max-1 {
        margin-top: 1.5rem !important
      }
    }

    @media(min-width:992px) {
      .margin-top-lg-min-1 {
        margin-top: 1.5rem !important
      }
    }

    @media(max-width:991px) {
      .margin-top-lg-max-1 {
        margin-top: 1.5rem !important
      }
    }

    @media(min-width:576px) {
      .margin-bottom-sm-min-1 {
        margin-bottom: 1.5rem !important
      }
    }

    @media(max-width:575px) {
      .margin-bottom-sm-max-1 {
        margin-bottom: 1.5rem !important
      }
    }

    @media(min-width:768px) {
      .margin-bottom-md-min-1 {
        margin-bottom: 1.5rem !important
      }
    }

    @media(max-width:767px) {
      .margin-bottom-md-max-1 {
        margin-bottom: 1.5rem !important
      }
    }

    @media(min-width:992px) {
      .margin-bottom-lg-min-1 {
        margin-bottom: 1.5rem !important
      }
    }

    @media(max-width:991px) {
      .margin-bottom-lg-max-1 {
        margin-bottom: 1.5rem !important
      }
    }

    @media(min-width:576px) {
      .padding-top-sm-min-1 {
        padding-top: 1.5rem !important
      }
    }

    @media(max-width:575px) {
      .padding-top-sm-max-1 {
        padding-top: 1.5rem !important
      }
    }

    @media(min-width:768px) {
      .padding-top-md-min-1 {
        padding-top: 1.5rem !important
      }
    }

    @media(max-width:767px) {
      .padding-top-md-max-1 {
        padding-top: 1.5rem !important
      }
    }

    @media(min-width:992px) {
      .padding-top-lg-min-1 {
        padding-top: 1.5rem !important
      }
    }

    @media(max-width:991px) {
      .padding-top-lg-max-1 {
        padding-top: 1.5rem !important
      }
    }

    @media(min-width:576px) {
      .padding-bottom-sm-min-1 {
        padding-bottom: 1.5rem !important
      }
    }

    @media(max-width:575px) {
      .padding-bottom-sm-max-1 {
        padding-bottom: 1.5rem !important
      }
    }

    @media(min-width:768px) {
      .padding-bottom-md-min-1 {
        padding-bottom: 1.5rem !important
      }
    }

    @media(max-width:767px) {
      .padding-bottom-md-max-1 {
        padding-bottom: 1.5rem !important
      }
    }

    @media(min-width:992px) {
      .padding-bottom-lg-min-1 {
        padding-bottom: 1.5rem !important
      }
    }

    @media(max-width:991px) {
      .padding-bottom-lg-max-1 {
        padding-bottom: 1.5rem !important
      }
    }

    @media(min-width:576px) {
      .margin-top-sm-min-2 {
        margin-top: 3rem !important
      }
    }

    @media(max-width:575px) {
      .margin-top-sm-max-2 {
        margin-top: 3rem !important
      }
    }

    @media(min-width:768px) {
      .margin-top-md-min-2 {
        margin-top: 3rem !important
      }
    }

    @media(max-width:767px) {
      .margin-top-md-max-2 {
        margin-top: 3rem !important
      }
    }

    @media(min-width:992px) {
      .margin-top-lg-min-2 {
        margin-top: 3rem !important
      }
    }

    @media(max-width:991px) {
      .margin-top-lg-max-2 {
        margin-top: 3rem !important
      }
    }

    @media(min-width:576px) {
      .margin-bottom-sm-min-2 {
        margin-bottom: 3rem !important
      }
    }

    @media(max-width:575px) {
      .margin-bottom-sm-max-2 {
        margin-bottom: 3rem !important
      }
    }

    @media(min-width:768px) {
      .margin-bottom-md-min-2 {
        margin-bottom: 3rem !important
      }
    }

    @media(max-width:767px) {
      .margin-bottom-md-max-2 {
        margin-bottom: 3rem !important
      }
    }

    @media(min-width:992px) {
      .margin-bottom-lg-min-2 {
        margin-bottom: 3rem !important
      }
    }

    @media(max-width:991px) {
      .margin-bottom-lg-max-2 {
        margin-bottom: 3rem !important
      }
    }

    @media(min-width:576px) {
      .padding-top-sm-min-2 {
        padding-top: 3rem !important
      }
    }

    @media(max-width:575px) {
      .padding-top-sm-max-2 {
        padding-top: 3rem !important
      }
    }

    @media(min-width:768px) {
      .padding-top-md-min-2 {
        padding-top: 3rem !important
      }
    }

    @media(max-width:767px) {
      .padding-top-md-max-2 {
        padding-top: 3rem !important
      }
    }

    @media(min-width:992px) {
      .padding-top-lg-min-2 {
        padding-top: 3rem !important
      }
    }

    @media(max-width:991px) {
      .padding-top-lg-max-2 {
        padding-top: 3rem !important
      }
    }

    @media(min-width:576px) {
      .padding-bottom-sm-min-2 {
        padding-bottom: 3rem !important
      }
    }

    @media(max-width:575px) {
      .padding-bottom-sm-max-2 {
        padding-bottom: 3rem !important
      }
    }

    @media(min-width:768px) {
      .padding-bottom-md-min-2 {
        padding-bottom: 3rem !important
      }
    }

    @media(max-width:767px) {
      .padding-bottom-md-max-2 {
        padding-bottom: 3rem !important
      }
    }

    @media(min-width:992px) {
      .padding-bottom-lg-min-2 {
        padding-bottom: 3rem !important
      }
    }

    @media(max-width:991px) {
      .padding-bottom-lg-max-2 {
        padding-bottom: 3rem !important
      }
    }

    @media(min-width:576px) {
      .margin-top-sm-min-3 {
        margin-top: 4.5rem !important
      }
    }

    @media(max-width:575px) {
      .margin-top-sm-max-3 {
        margin-top: 4.5rem !important
      }
    }

    @media(min-width:768px) {
      .margin-top-md-min-3 {
        margin-top: 4.5rem !important
      }
    }

    @media(max-width:767px) {
      .margin-top-md-max-3 {
        margin-top: 4.5rem !important
      }
    }

    @media(min-width:992px) {
      .margin-top-lg-min-3 {
        margin-top: 4.5rem !important
      }
    }

    @media(max-width:991px) {
      .margin-top-lg-max-3 {
        margin-top: 4.5rem !important
      }
    }

    @media(min-width:576px) {
      .margin-bottom-sm-min-3 {
        margin-bottom: 4.5rem !important
      }
    }

    @media(max-width:575px) {
      .margin-bottom-sm-max-3 {
        margin-bottom: 4.5rem !important
      }
    }

    @media(min-width:768px) {
      .margin-bottom-md-min-3 {
        margin-bottom: 4.5rem !important
      }
    }

    @media(max-width:767px) {
      .margin-bottom-md-max-3 {
        margin-bottom: 4.5rem !important
      }
    }

    @media(min-width:992px) {
      .margin-bottom-lg-min-3 {
        margin-bottom: 4.5rem !important
      }
    }

    @media(max-width:991px) {
      .margin-bottom-lg-max-3 {
        margin-bottom: 4.5rem !important
      }
    }

    @media(min-width:576px) {
      .padding-top-sm-min-3 {
        padding-top: 4.5rem !important
      }
    }

    @media(max-width:575px) {
      .padding-top-sm-max-3 {
        padding-top: 4.5rem !important
      }
    }

    @media(min-width:768px) {
      .padding-top-md-min-3 {
        padding-top: 4.5rem !important
      }
    }

    @media(max-width:767px) {
      .padding-top-md-max-3 {
        padding-top: 4.5rem !important
      }
    }

    @media(min-width:992px) {
      .padding-top-lg-min-3 {
        padding-top: 4.5rem !important
      }
    }

    @media(max-width:991px) {
      .padding-top-lg-max-3 {
        padding-top: 4.5rem !important
      }
    }

    @media(min-width:576px) {
      .padding-bottom-sm-min-3 {
        padding-bottom: 4.5rem !important
      }
    }

    @media(max-width:575px) {
      .padding-bottom-sm-max-3 {
        padding-bottom: 4.5rem !important
      }
    }

    @media(min-width:768px) {
      .padding-bottom-md-min-3 {
        padding-bottom: 4.5rem !important
      }
    }

    @media(max-width:767px) {
      .padding-bottom-md-max-3 {
        padding-bottom: 4.5rem !important
      }
    }

    @media(min-width:992px) {
      .padding-bottom-lg-min-3 {
        padding-bottom: 4.5rem !important
      }
    }

    @media(max-width:991px) {
      .padding-bottom-lg-max-3 {
        padding-bottom: 4.5rem !important
      }
    }

    @media(min-width:576px) {
      .margin-top-sm-min-4 {
        margin-top: 6rem !important
      }
    }

    @media(max-width:575px) {
      .margin-top-sm-max-4 {
        margin-top: 6rem !important
      }
    }

    @media(min-width:768px) {
      .margin-top-md-min-4 {
        margin-top: 6rem !important
      }
    }

    @media(max-width:767px) {
      .margin-top-md-max-4 {
        margin-top: 6rem !important
      }
    }

    @media(min-width:992px) {
      .margin-top-lg-min-4 {
        margin-top: 6rem !important
      }
    }

    @media(max-width:991px) {
      .margin-top-lg-max-4 {
        margin-top: 6rem !important
      }
    }

    @media(min-width:576px) {
      .margin-bottom-sm-min-4 {
        margin-bottom: 6rem !important
      }
    }

    @media(max-width:575px) {
      .margin-bottom-sm-max-4 {
        margin-bottom: 6rem !important
      }
    }

    @media(min-width:768px) {
      .margin-bottom-md-min-4 {
        margin-bottom: 6rem !important
      }
    }

    @media(max-width:767px) {
      .margin-bottom-md-max-4 {
        margin-bottom: 6rem !important
      }
    }

    @media(min-width:992px) {
      .margin-bottom-lg-min-4 {
        margin-bottom: 6rem !important
      }
    }

    @media(max-width:991px) {
      .margin-bottom-lg-max-4 {
        margin-bottom: 6rem !important
      }
    }

    @media(min-width:576px) {
      .padding-top-sm-min-4 {
        padding-top: 6rem !important
      }
    }

    @media(max-width:575px) {
      .padding-top-sm-max-4 {
        padding-top: 6rem !important
      }
    }

    @media(min-width:768px) {
      .padding-top-md-min-4 {
        padding-top: 6rem !important
      }
    }

    @media(max-width:767px) {
      .padding-top-md-max-4 {
        padding-top: 6rem !important
      }
    }

    @media(min-width:992px) {
      .padding-top-lg-min-4 {
        padding-top: 6rem !important
      }
    }

    @media(max-width:991px) {
      .padding-top-lg-max-4 {
        padding-top: 6rem !important
      }
    }

    @media(min-width:576px) {
      .padding-bottom-sm-min-4 {
        padding-bottom: 6rem !important
      }
    }

    @media(max-width:575px) {
      .padding-bottom-sm-max-4 {
        padding-bottom: 6rem !important
      }
    }

    @media(min-width:768px) {
      .padding-bottom-md-min-4 {
        padding-bottom: 6rem !important
      }
    }

    @media(max-width:767px) {
      .padding-bottom-md-max-4 {
        padding-bottom: 6rem !important
      }
    }

    @media(min-width:992px) {
      .padding-bottom-lg-min-4 {
        padding-bottom: 6rem !important
      }
    }

    @media(max-width:991px) {
      .padding-bottom-lg-max-4 {
        padding-bottom: 6rem !important
      }
    }

    .margin-left-0 {
      margin-left: 0 !important
    }

    .margin-right-0 {
      margin-right: 0 !important
    }

    .padding-left-0 {
      padding-left: 0 !important
    }

    .padding-right-0 {
      padding-right: 0 !important
    }

    .margin-left-1 {
      margin-left: 1.5rem !important
    }

    .margin-right-1 {
      margin-right: 1.5rem !important
    }

    .padding-left-1 {
      padding-left: 1.5rem !important
    }

    .padding-right-1 {
      padding-right: 1.5rem !important
    }

    .margin-left-2 {
      margin-left: 3rem !important
    }

    .margin-right-2 {
      margin-right: 3rem !important
    }

    .padding-left-2 {
      padding-left: 3rem !important
    }

    .padding-right-2 {
      padding-right: 3rem !important
    }

    @media(min-width:576px) {
      .margin-left-sm-min-0 {
        margin-left: 0 !important
      }
    }

    @media(max-width:575px) {
      .margin-left-sm-max-0 {
        margin-left: 0 !important
      }
    }

    @media(min-width:768px) {
      .margin-left-md-min-0 {
        margin-left: 0 !important
      }
    }

    @media(max-width:767px) {
      .margin-left-md-max-0 {
        margin-left: 0 !important
      }
    }

    @media(min-width:992px) {
      .margin-left-lg-min-0 {
        margin-left: 0 !important
      }
    }

    @media(max-width:991px) {
      .margin-left-lg-max-0 {
        margin-left: 0 !important
      }
    }

    @media(min-width:576px) {
      .margin-right-sm-min-0 {
        margin-right: 0 !important
      }
    }

    @media(max-width:575px) {
      .margin-right-sm-max-0 {
        margin-right: 0 !important
      }
    }

    @media(min-width:768px) {
      .margin-right-md-min-0 {
        margin-right: 0 !important
      }
    }

    @media(max-width:767px) {
      .margin-right-md-max-0 {
        margin-right: 0 !important
      }
    }

    @media(min-width:992px) {
      .margin-right-lg-min-0 {
        margin-right: 0 !important
      }
    }

    @media(max-width:991px) {
      .margin-right-lg-max-0 {
        margin-right: 0 !important
      }
    }

    @media(min-width:576px) {
      .padding-left-sm-min-0 {
        padding-left: 0 !important
      }
    }

    @media(max-width:575px) {
      .padding-left-sm-max-0 {
        padding-left: 0 !important
      }
    }

    @media(min-width:768px) {
      .padding-left-md-min-0 {
        padding-left: 0 !important
      }
    }

    @media(max-width:767px) {
      .padding-left-md-max-0 {
        padding-left: 0 !important
      }
    }

    @media(min-width:992px) {
      .padding-left-lg-min-0 {
        padding-left: 0 !important
      }
    }

    @media(max-width:991px) {
      .padding-left-lg-max-0 {
        padding-left: 0 !important
      }
    }

    @media(min-width:576px) {
      .padding-right-sm-min-0 {
        padding-right: 0 !important
      }
    }

    @media(max-width:575px) {
      .padding-right-sm-max-0 {
        padding-right: 0 !important
      }
    }

    @media(min-width:768px) {
      .padding-right-md-min-0 {
        padding-right: 0 !important
      }
    }

    @media(max-width:767px) {
      .padding-right-md-max-0 {
        padding-right: 0 !important
      }
    }

    @media(min-width:992px) {
      .padding-right-lg-min-0 {
        padding-right: 0 !important
      }
    }

    @media(max-width:991px) {
      .padding-right-lg-max-0 {
        padding-right: 0 !important
      }
    }

    @media(min-width:576px) {
      .margin-left-sm-min-1 {
        margin-left: 1.5rem !important
      }
    }

    @media(max-width:575px) {
      .margin-left-sm-max-1 {
        margin-left: 1.5rem !important
      }
    }

    @media(min-width:768px) {
      .margin-left-md-min-1 {
        margin-left: 1.5rem !important
      }
    }

    @media(max-width:767px) {
      .margin-left-md-max-1 {
        margin-left: 1.5rem !important
      }
    }

    @media(min-width:992px) {
      .margin-left-lg-min-1 {
        margin-left: 1.5rem !important
      }
    }

    @media(max-width:991px) {
      .margin-left-lg-max-1 {
        margin-left: 1.5rem !important
      }
    }

    @media(min-width:576px) {
      .margin-right-sm-min-1 {
        margin-right: 1.5rem !important
      }
    }

    @media(max-width:575px) {
      .margin-right-sm-max-1 {
        margin-right: 1.5rem !important
      }
    }

    @media(min-width:768px) {
      .margin-right-md-min-1 {
        margin-right: 1.5rem !important
      }
    }

    @media(max-width:767px) {
      .margin-right-md-max-1 {
        margin-right: 1.5rem !important
      }
    }

    @media(min-width:992px) {
      .margin-right-lg-min-1 {
        margin-right: 1.5rem !important
      }
    }

    @media(max-width:991px) {
      .margin-right-lg-max-1 {
        margin-right: 1.5rem !important
      }
    }

    @media(min-width:576px) {
      .padding-left-sm-min-1 {
        padding-left: 1.5rem !important
      }
    }

    @media(max-width:575px) {
      .padding-left-sm-max-1 {
        padding-left: 1.5rem !important
      }
    }

    @media(min-width:768px) {
      .padding-left-md-min-1 {
        padding-left: 1.5rem !important
      }
    }

    @media(max-width:767px) {
      .padding-left-md-max-1 {
        padding-left: 1.5rem !important
      }
    }

    @media(min-width:992px) {
      .padding-left-lg-min-1 {
        padding-left: 1.5rem !important
      }
    }

    @media(max-width:991px) {
      .padding-left-lg-max-1 {
        padding-left: 1.5rem !important
      }
    }

    @media(min-width:576px) {
      .padding-right-sm-min-1 {
        padding-right: 1.5rem !important
      }
    }

    @media(max-width:575px) {
      .padding-right-sm-max-1 {
        padding-right: 1.5rem !important
      }
    }

    @media(min-width:768px) {
      .padding-right-md-min-1 {
        padding-right: 1.5rem !important
      }
    }

    @media(max-width:767px) {
      .padding-right-md-max-1 {
        padding-right: 1.5rem !important
      }
    }

    @media(min-width:992px) {
      .padding-right-lg-min-1 {
        padding-right: 1.5rem !important
      }
    }

    @media(max-width:991px) {
      .padding-right-lg-max-1 {
        padding-right: 1.5rem !important
      }
    }

    @media(min-width:576px) {
      .margin-left-sm-min-2 {
        margin-left: 3rem !important
      }
    }

    @media(max-width:575px) {
      .margin-left-sm-max-2 {
        margin-left: 3rem !important
      }
    }

    @media(min-width:768px) {
      .margin-left-md-min-2 {
        margin-left: 3rem !important
      }
    }

    @media(max-width:767px) {
      .margin-left-md-max-2 {
        margin-left: 3rem !important
      }
    }

    @media(min-width:992px) {
      .margin-left-lg-min-2 {
        margin-left: 3rem !important
      }
    }

    @media(max-width:991px) {
      .margin-left-lg-max-2 {
        margin-left: 3rem !important
      }
    }

    @media(min-width:576px) {
      .margin-right-sm-min-2 {
        margin-right: 3rem !important
      }
    }

    @media(max-width:575px) {
      .margin-right-sm-max-2 {
        margin-right: 3rem !important
      }
    }

    @media(min-width:768px) {
      .margin-right-md-min-2 {
        margin-right: 3rem !important
      }
    }

    @media(max-width:767px) {
      .margin-right-md-max-2 {
        margin-right: 3rem !important
      }
    }

    @media(min-width:992px) {
      .margin-right-lg-min-2 {
        margin-right: 3rem !important
      }
    }

    @media(max-width:991px) {
      .margin-right-lg-max-2 {
        margin-right: 3rem !important
      }
    }

    @media(min-width:576px) {
      .padding-left-sm-min-2 {
        padding-left: 3rem !important
      }
    }

    @media(max-width:575px) {
      .padding-left-sm-max-2 {
        padding-left: 3rem !important
      }
    }

    @media(min-width:768px) {
      .padding-left-md-min-2 {
        padding-left: 3rem !important
      }
    }

    @media(max-width:767px) {
      .padding-left-md-max-2 {
        padding-left: 3rem !important
      }
    }

    @media(min-width:992px) {
      .padding-left-lg-min-2 {
        padding-left: 3rem !important
      }
    }

    @media(max-width:991px) {
      .padding-left-lg-max-2 {
        padding-left: 3rem !important
      }
    }

    @media(min-width:576px) {
      .padding-right-sm-min-2 {
        padding-right: 3rem !important
      }
    }

    @media(max-width:575px) {
      .padding-right-sm-max-2 {
        padding-right: 3rem !important
      }
    }

    @media(min-width:768px) {
      .padding-right-md-min-2 {
        padding-right: 3rem !important
      }
    }

    @media(max-width:767px) {
      .padding-right-md-max-2 {
        padding-right: 3rem !important
      }
    }

    @media(min-width:992px) {
      .padding-right-lg-min-2 {
        padding-right: 3rem !important
      }
    }

    @media(max-width:991px) {
      .padding-right-lg-max-2 {
        padding-right: 3rem !important
      }
    }

    .font-weight-bold {
      font-weight: 600
    }

    .font-weight-bolder {
      font-weight: 700
    }

    .font-weight-normal {
      font-weight: 400
    }

    .font-style-italic {
      font-style: italic
    }

    .text-dec-none {
      border-bottom: 0
    }

    .text-dec-none,
    .text-dec-none a {
      text-decoration: none
    }

    .text-dec-hover,
    .text-dec-underline {
      cursor: pointer;
      border-bottom: 1px solid transparent;
      text-decoration: none;
      transition: border-color .25s cubic-bezier(.4, 0, 1, 1)
    }

    .text-dec-hover:focus,
    .text-dec-hover:hover {
      border-color: currentColor
    }

    .text-dec-hover:focus {
      outline: 0
    }

    .text-dec-underline {
      border-color: currentColor
    }

    .text-uppercase {
      text-transform: uppercase
    }

    .a-primary,
    .a-primary-hover {
      cursor: pointer;
      text-decoration: none;
      border-bottom: 1px solid
    }

    .a-primary,
    .color-mint,
    .primary {
      color: #0ff
    }

    .primary,
    .primary-hover {
      text-decoration: none
    }

    .primary-hover {
      cursor: pointer
    }

    .a-primary-hover,
    .primary-hover {
      transition: color .25s cubic-bezier(.4, 0, 1, 1), border-color .25s cubic-bezier(.4, 0, 1, 1), opacity .25s cubic-bezier(.4, 0, 1, 1)
    }

    .a-primary-hover:focus,
    .a-primary-hover:hover,
    .primary-hover:focus,
    .primary-hover:hover {
      color: #0ff;
      opacity: 1
    }

    .a-primary-hover:focus,
    .primary-hover:focus {
      outline: 0
    }

    .current-hover {
      transition: color .25s cubic-bezier(.4, 0, 1, 1)
    }

    .current-hover:focus,
    .current-hover:hover {
      color: currentColor
    }

    .current-hover:focus {
      outline: 0
    }

    .list-style-none {
      list-style: none;
      padding-left: 0
    }

    .list-style-custom,
    .list-style-number {
      list-style-type: none;
      padding-left: 0;
      margin-left: 1.5rem
    }

    .list-style-custom>li,
    .list-style-number>li {
      position: relative
    }

    .list-style-number {
      counter-reset: ts-list-style-number
    }

    .list-style-number>li {
      counter-increment: ts-list-style-number
    }

    .list-style-number>li:before {
      content: counter(ts-list-style-number) ".";
      position: absolute;
      left: -1.5rem;
      font-weight: 600
    }

    .list-style-number .list-style-number>li:before {
      content: counters(ts-list-style-number, ".") "."
    }

    .list-style-extra-padding {
      margin-left: 2.25rem
    }

    .list-style-extra-padding.list-style-number>li:before {
      left: -2.25rem
    }

    .list-style-number-wrapper {
      counter-reset: ts-list-style-number
    }

    .list-style-number-wrapper .list-style-number {
      counter-reset: none
    }

    .list-style-custom>li i,
    .list-style-custom>li i.tidal-icon {
      position: absolute;
      text-align: center;
      line-height: inherit;
      left: -1.5rem
    }

    .list-style-custom.list-style-extra-padding>li i,
    .list-style-custom.list-style-extra-padding>li i.tidal-icon {
      left: -2.25rem
    }

    .ellipsis {
      text-overflow: ellipsis;
      overflow: hidden;
      word-wrap: break-word;
      white-space: nowrap
    }

    @media(min-width:576px) {
      .ellipsis-sm-min {
        text-overflow: ellipsis;
        overflow: hidden;
        word-wrap: break-word;
        white-space: nowrap
      }
    }

    @media(max-width:575px) {
      .ellipsis-sm-max {
        text-overflow: ellipsis;
        overflow: hidden;
        word-wrap: break-word;
        white-space: nowrap
      }
    }

    @media(min-width:768px) {
      .ellipsis-md-min {
        text-overflow: ellipsis;
        overflow: hidden;
        word-wrap: break-word;
        white-space: nowrap
      }
    }

    @media(max-width:767px) {
      .ellipsis-md-max {
        text-overflow: ellipsis;
        overflow: hidden;
        word-wrap: break-word;
        white-space: nowrap
      }
    }

    @media(min-width:992px) {
      .ellipsis-lg-min {
        text-overflow: ellipsis;
        overflow: hidden;
        word-wrap: break-word;
        white-space: nowrap
      }
    }

    @media(max-width:991px) {
      .ellipsis-lg-max {
        text-overflow: ellipsis;
        overflow: hidden;
        word-wrap: break-word;
        white-space: nowrap
      }
    }

    .break-word {
      overflow-wrap: break-word;
      word-wrap: break-word;
      word-break: break-word
    }

    .no-select {
      -webkit-user-select: none;
      -moz-user-select: none;
      user-select: none
    }

    .color-blackest {
      color: #000
    }

    .color-white {
      color: #fff
    }

    .color-grey-lightest {
      color: #f4f4f4
    }

    .color-grey-lighter {
      color: #efefef
    }

    .color-grey-light {
      color: #9b9b9b
    }

    .color-grey {
      color: #78777f
    }

    .color-grey-dark {
      color: #686a71
    }

    .color-grey-darker {
      color: #49494f
    }

    .color-blacker {
      color: #101012
    }

    .color-black {
      color: #26282d
    }

    .color-gold {
      color: #ffbe7d
    }

    .color-blue-light {
      color: #acdbff
    }

    .color-disabled {
      color: #a9a9a9
    }

    .color-raspberry {
      color: #cd005e
    }

    .color-error,
    .color-red {
      color: #ed1c24
    }

    .color-muted,
    .color-native-grey {
      transition: color .25s cubic-bezier(.4, 0, 1, 1)
    }

    .color-native-grey {
      color: #686a71
    }

    @media screen and (prefers-color-scheme:dark) {
      .color-native-grey {
        color: #9b9b9b
      }
    }

    .color-muted {
      color: #9b9b9b
    }

    .theme-native .color-muted,
    [class*=theme-] .theme-native .color-muted {
      color: #686a71
    }

    @media screen and (prefers-color-scheme:dark) {

      .theme-native .color-muted,
      [class*=theme-] .theme-native .color-muted {
        color: #9b9b9b
      }
    }

    .theme-native [class*=theme-]:not(.theme-native):not(.theme-white):not(.theme-grey-light):not(.theme-semi-transparent) .color-muted {
      color: #9b9b9b
    }

    .theme-white .color-muted,
    [class*=theme-] .theme-white .color-muted {
      color: #686a71
    }

    .hidden {
      display: none !important
    }

    @media(min-width:576px) {
      .hidden-sm-min {
        display: none !important
      }
    }

    @media(max-width:575px) {
      .hidden-sm-max {
        display: none !important
      }
    }

    @media(min-width:768px) {
      .hidden-md-min {
        display: none !important
      }
    }

    @media(max-width:767px) {
      .hidden-md-max {
        display: none !important
      }
    }

    @media(min-width:992px) {
      .hidden-lg-min {
        display: none !important
      }
    }

    @media(max-width:991px) {
      .hidden-lg-max {
        display: none !important
      }
    }

    .opacity-0 {
      opacity: 0
    }

    @media(min-width:576px) {
      .opacity-sm-min-0 {
        opacity: 0
      }
    }

    @media(max-width:575px) {
      .opacity-sm-max-0 {
        opacity: 0
      }
    }

    @media(min-width:768px) {
      .opacity-md-min-0 {
        opacity: 0
      }
    }

    @media(max-width:767px) {
      .opacity-md-max-0 {
        opacity: 0
      }
    }

    @media(min-width:992px) {
      .opacity-lg-min-0 {
        opacity: 0
      }
    }

    @media(max-width:991px) {
      .opacity-lg-max-0 {
        opacity: 0
      }
    }

    .opacity-20 {
      opacity: .2
    }

    @media(min-width:576px) {
      .opacity-sm-min-20 {
        opacity: .2
      }
    }

    @media(max-width:575px) {
      .opacity-sm-max-20 {
        opacity: .2
      }
    }

    @media(min-width:768px) {
      .opacity-md-min-20 {
        opacity: .2
      }
    }

    @media(max-width:767px) {
      .opacity-md-max-20 {
        opacity: .2
      }
    }

    @media(min-width:992px) {
      .opacity-lg-min-20 {
        opacity: .2
      }
    }

    @media(max-width:991px) {
      .opacity-lg-max-20 {
        opacity: .2
      }
    }

    .opacity-40 {
      opacity: .4
    }

    @media(min-width:576px) {
      .opacity-sm-min-40 {
        opacity: .4
      }
    }

    @media(max-width:575px) {
      .opacity-sm-max-40 {
        opacity: .4
      }
    }

    @media(min-width:768px) {
      .opacity-md-min-40 {
        opacity: .4
      }
    }

    @media(max-width:767px) {
      .opacity-md-max-40 {
        opacity: .4
      }
    }

    @media(min-width:992px) {
      .opacity-lg-min-40 {
        opacity: .4
      }
    }

    @media(max-width:991px) {
      .opacity-lg-max-40 {
        opacity: .4
      }
    }

    .opacity-60 {
      opacity: .6
    }

    @media(min-width:576px) {
      .opacity-sm-min-60 {
        opacity: .6
      }
    }

    @media(max-width:575px) {
      .opacity-sm-max-60 {
        opacity: .6
      }
    }

    @media(min-width:768px) {
      .opacity-md-min-60 {
        opacity: .6
      }
    }

    @media(max-width:767px) {
      .opacity-md-max-60 {
        opacity: .6
      }
    }

    @media(min-width:992px) {
      .opacity-lg-min-60 {
        opacity: .6
      }
    }

    @media(max-width:991px) {
      .opacity-lg-max-60 {
        opacity: .6
      }
    }

    .opacity-80 {
      opacity: .8
    }

    @media(min-width:576px) {
      .opacity-sm-min-80 {
        opacity: .8
      }
    }

    @media(max-width:575px) {
      .opacity-sm-max-80 {
        opacity: .8
      }
    }

    @media(min-width:768px) {
      .opacity-md-min-80 {
        opacity: .8
      }
    }

    @media(max-width:767px) {
      .opacity-md-max-80 {
        opacity: .8
      }
    }

    @media(min-width:992px) {
      .opacity-lg-min-80 {
        opacity: .8
      }
    }

    @media(max-width:991px) {
      .opacity-lg-max-80 {
        opacity: .8
      }
    }

    .opacity-100 {
      opacity: 1
    }

    @media(min-width:576px) {
      .opacity-sm-min-100 {
        opacity: 1
      }
    }

    @media(max-width:575px) {
      .opacity-sm-max-100 {
        opacity: 1
      }
    }

    @media(min-width:768px) {
      .opacity-md-min-100 {
        opacity: 1
      }
    }

    @media(max-width:767px) {
      .opacity-md-max-100 {
        opacity: 1
      }
    }

    @media(min-width:992px) {
      .opacity-lg-min-100 {
        opacity: 1
      }
    }

    @media(max-width:991px) {
      .opacity-lg-max-100 {
        opacity: 1
      }
    }

    [class*=opacity-hover] {
      transition: opacity .25s cubic-bezier(.4, 0, 1, 1)
    }

    .opacity-hover-0:hover {
      opacity: 0
    }

    .opacity-hover-20:hover {
      opacity: .2
    }

    .opacity-hover-40:hover {
      opacity: .4
    }

    .opacity-hover-60:hover {
      opacity: .6
    }

    .opacity-hover-80:hover {
      opacity: .8
    }

    .opacity-hover-100:hover {
      opacity: 1
    }

    .sr-only {
      position: absolute;
      left: -10000px;
      top: auto;
      width: 1px;
      height: 1px;
      overflow: hidden
    }

    @media print {

      .theme-mint,
      .theme-native,
      .theme-white {
        color: #000 !important
      }

      *,
      :after,
      :before,
      :first-letter {
        box-shadow: none !important;
        text-shadow: none !important
      }

      a[href^=http]:not([href*="tidal.com"]):after {
        content: " (" attr(href) ")"
      }

      a[href^="#"]:after,
      a[href^="javascript:"]:after {
        content: ""
      }

      .nowrap,
      pre {
        white-space: pre-wrap !important;
        flex-wrap: wrap !important
      }

      .tidal-lazy-image,
      .tidal-mobile-animation,
      .tidal-scroll-item {
        opacity: 1 !important;
        -webkit-animation: unset;
        animation: unset
      }

      .tidal-lazy-image-wrapper {
        padding-bottom: 0 !important;
        height: auto
      }

      .tidal-lazy-image {
        position: relative
      }
    }

    body {
      background: #101012
    }

    .progress-bar-wrapper {
      width: 100%;
      height: 1px
    }

    .progress-bar {
      background: #0ff;
      height: 1px;
      width: 0;
      transition: width .5s cubic-bezier(.39, .575, .565, 1)
    }

    .artist-list,
    .secondary-title {
      color: #9b9b9b
    }

    .track-controls:hover .artist-list,
    .track-controls:hover .secondary-title,
    .track-highlight .artist-list,
    .track-highlight .secondary-title {
      color: #a8a8a8
    }

    .artist-list-link:not(:last-child):after {
      content: ", "
    }

    @media(min-width:992px) {
      .hover-desktop[href]:hover {
        text-decoration: underline
      }
    }

    @supports(-webkit-line-clamp:2) {
      .ellipsis-double {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        word-wrap: unset;
        white-space: unset
      }
    }

    .btn-secondary.btn-rounded {
      border: 0
    }

    .nuxt-progress {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      height: 2px;
      width: 0;
      opacity: 1;
      transition: width .1s, opacity .4s;
      background-color: #0ff;
      z-index: 999999
    }

    .nuxt-progress.nuxt-progress-notransition {
      transition: none
    }

    .nuxt-progress-failed {
      background-color: red
    }

    #main-content {
      outline: 0
    }

    .accessibility-menu[data-v-fcb80d4c] {
      position: absolute;
      top: auto;
      left: -99999px;
      height: 1px;
      width: 1px;
      overflow: hidden;
      z-index: -999
    }

    .accessibility-menu[data-v-fcb80d4c]:focus-within {
      box-shadow: 0 4px 30px 2px rgba(0, 0, 0, .5);
      background: #26282d;
      left: auto;
      height: auto;
      width: auto;
      z-index: 10;
      top: 4.5rem
    }

    @media(min-width:992px) {
      .accessibility-menu[data-v-fcb80d4c]:focus-within {
        top: 5.25rem
      }
    }

    @media print {
      .accessibility-menu[data-v-fcb80d4c] {
        display: none
      }
    }

    .accessibility-item[data-v-fcb80d4c] {
      padding: .75rem 1.5rem
    }

    .tidal-header-wrapper[data-v-fcb80d4c] {
      position: relative
    }

    .tidal-header-wrapper.is-open[data-v-fcb80d4c],
    .tidal-header-wrapper.is-sticky[data-v-fcb80d4c] {
      padding-top: 4.5rem
    }

    @media(min-width:992px) {

      .tidal-header-wrapper.is-open[data-v-fcb80d4c],
      .tidal-header-wrapper.is-sticky[data-v-fcb80d4c] {
        padding-top: 5.25rem
      }
    }

    .tidal-header[data-v-fcb80d4c] {
      padding: .75rem 1.5rem;
      height: 4.5rem;
      z-index: 50;
      outline: 0
    }

    @media(min-width:992px) {
      .tidal-header[data-v-fcb80d4c] {
        padding: 1.2rem 1.5rem;
        height: 5.25rem
      }
    }

    @media(min-width:1404px) {
      .tidal-header[data-v-fcb80d4c] {
        padding: 1.2rem 3rem
      }
    }

    .tidal-header-wrapper.is-open--fixed .tidal-header[data-v-fcb80d4c],
    .tidal-header-wrapper.is-open .tidal-header[data-v-fcb80d4c],
    .tidal-header-wrapper.is-sticky--fixed .tidal-header[data-v-fcb80d4c],
    .tidal-header-wrapper.is-sticky .tidal-header[data-v-fcb80d4c] {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      z-index: 90;
      transform: translateZ(0)
    }

    @media(max-width:991px) {
      .tidal-header-wrapper.is-open--fixed .tidal-header.theme-transparent[data-v-fcb80d4c] {
        background: #000
      }
    }

    @media(min-width:992px) {

      .tidal-header-wrapper.is-open--fixed .tidal-header[data-v-fcb80d4c],
      .tidal-header-wrapper.is-open .tidal-header[data-v-fcb80d4c] {
        position: absolute
      }
    }

    .tidal-header-wrapper.is-sticky--fixed.is-open--fixed .tidal-header[data-v-fcb80d4c],
    .tidal-header-wrapper.is-sticky.is-open .tidal-header[data-v-fcb80d4c] {
      position: fixed
    }

    .tidal-header-content[data-v-fcb80d4c] {
      line-height: 3rem
    }

    .tidal-header-burger[data-v-fcb80d4c] {
      transition: transform .1875s cubic-bezier(.4, 0, 1, 1), opacity .1875s cubic-bezier(.4, 0, 1, 1)
    }

    .tidal-header-burger-1[data-v-fcb80d4c],
    .tidal-header-burger-2[data-v-fcb80d4c] {
      transform: rotate(0) translate(0)
    }

    .tidal-header-burger-3[data-v-fcb80d4c] {
      opacity: 1
    }

    .is-active .tidal-header-burger-1[data-v-fcb80d4c] {
      transform: rotate(-45grad) translate(-24px, -4px)
    }

    .is-active .tidal-header-burger-2[data-v-fcb80d4c] {
      transform: rotate(45grad) translate(10px, -5px)
    }

    .is-active .tidal-header-burger-3[data-v-fcb80d4c] {
      opacity: 0
    }

    .tidal-header-button[data-v-fcb80d4c] {
      line-height: 2.25rem;
      height: 2.25rem;
      padding: 0 1.5rem
    }

    @media(max-width:575px) {
      .tidal-header-button[data-v-fcb80d4c] {
        font-size: .75rem
      }
    }

    .tidal-header-logo[data-v-fcb80d4c] {
      margin-right: auto
    }

    .tidal-header-logo--image[data-v-fcb80d4c] {
      margin-right: 1.5rem;
      vertical-align: middle;
      width: 7.75rem
    }

    @media(min-width:1200px) {
      .tidal-header-logo--image[data-v-fcb80d4c] {
        width: 8.88rem
      }
    }

    .tidal-header-main-menu[data-v-fcb80d4c] {
      position: fixed;
      top: 4.5rem;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 89
    }

    @media(min-width:992px) {
      .tidal-header-main-menu[data-v-fcb80d4c] {
        top: 5.25rem
      }
    }

    .header-btn[data-v-fcb80d4c] {
      line-height: 2.2222222222rem;
      height: 2.2222222222rem
    }

    .tidal-header-sub-nav-link[data-v-fcb80d4c] {
      color: #fff !important
    }

    .tidal-header-sub-nav-link[data-v-fcb80d4c]:focus,
    .tidal-header-sub-nav-link[data-v-fcb80d4c]:focus-within,
    .tidal-header-sub-nav-link[data-v-fcb80d4c]:hover,
    .tidal-header-sub-nav-link[aria-expanded=true][data-v-fcb80d4c] {
      background: #2f3136;
      opacity: 1;
      outline: 0
    }

    .tidal-header-sub-nav-link[aria-expanded=true]+.tidal-header-sub-content[data-v-fcb80d4c] {
      pointer-events: auto;
      opacity: 1;
      visibility: visible;
      transform: translateY(0)
    }

    .tidal-header-sub-content[data-v-fcb80d4c] {
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 9px 20px rgba(0, 0, 0, .07);
      line-height: normal;
      max-width: 14rem;
      opacity: 0;
      padding: .75rem 0;
      pointer-events: none;
      position: absolute;
      transform: translateY(-20px);
      transition: opacity .25s cubic-bezier(.4, 0, 1, 1), transform .25s ease-in-out;
      visibility: hidden;
      z-index: 50
    }

    .tidal-header-sub-content.is-right[data-v-fcb80d4c] {
      right: 0
    }

    .tidal-header-sub-content .btn-link[data-v-fcb80d4c] {
      transition: background .0625s cubic-bezier(.4, 0, 1, 1);
      padding: 0 1.5rem;
      text-align: left;
      min-width: 12.5rem;
      color: #26282d
    }

    .tidal-header-sub-content .btn-link[data-v-fcb80d4c]:focus,
    .tidal-header-sub-content .btn-link[data-v-fcb80d4c]:hover {
      color: #26282d;
      text-decoration: underline
    }

    .tidal-header-sub-content .header-logout[data-v-fcb80d4c] {
      margin-top: -.3333rem;
      padding-left: 1.1rem;
      padding-bottom: 3rem;
      color: #686a71
    }

    .tidal-header-subheader[data-v-fcb80d4c] {
      letter-spacing: .1em
    }

    .tidal-header-margin[data-v-fcb80d4c] {
      margin-bottom: 1.125rem
    }

    .borders[data-v-3bb938c8] {
      border-top: 1px solid;
      border-bottom: 1px solid
    }

    .borders+.tidal-accordion[data-v-3bb938c8] {
      border-top: 0
    }

    .icon[data-v-3bb938c8] .tidal-icon svg {
      transition: transform .1875s cubic-bezier(.4, 0, 1, 1)
    }

    @media(prefers-reduced-motion:reduce) {
      .icon[data-v-3bb938c8] .tidal-icon svg {
        transition: unset !important
      }
    }

    .icon.is-active[data-v-3bb938c8] svg {
      transform: rotate(135deg)
    }

    .icon.is-active.is-arrow[data-v-3bb938c8] svg {
      transform: rotate(180deg)
    }

    @media print {
      .content[data-v-3bb938c8] {
        display: block !important
      }
    }

    .tidal-icon[data-v-51a7f762] {
      display: inline-block;
      position: relative;
      top: .1667em
    }

    .tidal-icon[data-v-51a7f762] svg {
      fill: currentColor;
      height: 1em;
      width: 1em
    }

    .tidal-icon--stroke[data-v-51a7f762] svg {
      stroke: currentColor
    }

    .tidal-icon--stroke-only[data-v-51a7f762] svg {
      fill: inherit;
      stroke: currentColor
    }

    .block .tidal-icon[data-v-51a7f762] {
      display: inherit;
      position: static;
      top: 0
    }

    .block .tidal-icon[data-v-51a7f762] svg {
      display: inherit
    }

    .auto-expand-enter[data-v-53026b1a],
    .auto-expand-enter-active[data-v-53026b1a],
    .auto-expand-leave[data-v-53026b1a],
    .auto-expand-leave-active[data-v-53026b1a] {
      transition: all .25s cubic-bezier(.4, 0, 1, 1) !important;
      overflow: hidden
    }

    @media(prefers-reduced-motion:reduce) {

      .auto-expand-enter[data-v-53026b1a],
      .auto-expand-enter-active[data-v-53026b1a],
      .auto-expand-leave[data-v-53026b1a],
      .auto-expand-leave-active[data-v-53026b1a] {
        transition: unset !important
      }
    }

    .image-grid-wrapper[data-v-0d875eb7] {
      position: relative;
      pointer-events: none;
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: -1
    }

    .image-grid-wrapper[data-v-0d875eb7]:after {
      content: "";
      position: absolute;
      background-image: linear-gradient(180deg, rgba(16, 16, 18, 0) 30%, #101012 70%);
      top: 0;
      left: 0;
      height: 575px;
      width: 100%;
      z-index: 2
    }

    @media(max-width:767px) {
      .image-grid-wrapper[data-v-0d875eb7]:after {
        height: 420px
      }
    }

    .bg-grid[data-v-0d875eb7] {
      position: absolute;
      top: 0;
      background: #26282d;
      min-height: 575px;
      width: 100%
    }

    @media(max-width:767px) {
      .bg-grid[data-v-0d875eb7] {
        height: 420px
      }
    }

    .image-grid[data-v-0d875eb7] {
      display: grid;
      grid-auto-flow: column;
      grid-template-columns: repeat(3, minmax(575px, auto));
      position: absolute;
      top: 0;
      margin-top: -5%;
      z-index: 1
    }

    @media(max-width:767px) {
      .image-grid[data-v-0d875eb7] {
        grid-template-columns: repeat(3, minmax(420px, auto))
      }
    }

    .image-grid img[data-v-0d875eb7] {
      width: 100%;
      height: 100%
    }

    .image-grid[data-v-0d875eb7]>:first-child,
    .image-grid[data-v-0d875eb7]>:nth-child(3) {
      opacity: .3
    }

    .grid-content[data-v-0d875eb7] {
      flex-flow: column;
      min-height: 400px
    }

    @media(max-width:767px) {
      .grid-content[data-v-0d875eb7] {
        min-height: 370px
      }
    }

    .grid-col[data-v-0d875eb7] {
      margin-top: auto
    }

    .bg-wrapper[data-v-0d875eb7] {
      background-image: var(--bg-url);
      background-position-y: 10%;
      filter: blur(60px);
      position: absolute;
      top: 0;
      height: 1000px;
      width: 100%;
      z-index: -2
    }

    @media(max-width:991px) {
      .bg-wrapper[data-v-0d875eb7] {
        height: 600px
      }
    }

    @media(max-width:767px) {
      .bg-wrapper[data-v-0d875eb7] {
        filter: blur(20px)
      }
    }

    .bg-gradient[data-v-0d875eb7]:before {
      content: "";
      background: linear-gradient(180deg, rgba(16, 16, 18, .7) 0, #101012 57%);
      position: absolute;
      height: 1100px;
      width: 100%;
      z-index: -1
    }

    @media(max-width:991px) {
      .bg-gradient[data-v-0d875eb7]:before {
        height: 700px
      }
    }

    @media(min-width:992px) {
      .item-info[data-v-0d875eb7] {
        margin-top: 1px
      }
    }

    @media(min-width:768px) {
      .description--ellipsis[data-v-0d875eb7] {
        display: block;
        white-space: unset;
        word-wrap: unset
      }
    }

    @media(max-width:767px) {
      .description--ellipsis[data-v-0d875eb7] {
        cursor: pointer
      }

      .description--ellipsis br[data-v-0d875eb7] {
        display: none
      }
    }

    .button-wrapper[data-v-0d875eb7] {
      display: flex;
      flex-wrap: nowrap
    }

    @media(max-width:575px) {
      .main-button[data-v-0d875eb7] {
        flex-grow: 1
      }
    }

    @media(min-width:576px) {
      .main-button[data-v-0d875eb7] {
        min-width: 225px
      }
    }

    .secondary-buttons[data-v-0d875eb7] {
      display: flex;
      align-items: center
    }

    @media(min-width:576px) {
      .secondary-buttons[data-v-0d875eb7] {
        flex-grow: 1
      }

      .secondary-buttons[data-v-0d875eb7] :first-child {
        margin-left: auto
      }
    }

    .secondary-button[data-v-0d875eb7] {
      transition: opacity .25s cubic-bezier(.39, .575, .565, 1);
      text-align: center;
      margin-left: 1.5rem
    }

    .secondary-button[data-v-0d875eb7]:hover {
      opacity: .75
    }

    .secondary-button[data-v-0d875eb7] .button-text {
      font-size: .75rem;
      line-height: 1.5;
      display: block;
      color: #efefef
    }

    .secondary-button[data-v-0d875eb7] .button-text.font-family-dharma {
      font-size: 1.6rem;
      line-height: 1
    }

    html[lang=bg-BG] .secondary-button[data-v-0d875eb7] .button-text.font-family-dharma,
    html[lang=ja-JP] .secondary-button[data-v-0d875eb7] .button-text.font-family-dharma {
      font-size: 1.1rem;
      line-height: 1.2
    }

    @media(min-width:992px) {
      .secondary-button[data-v-0d875eb7] .button-text.font-family-dharma {
        font-size: 2.2rem
      }

      html[lang=bg-BG] .secondary-button[data-v-0d875eb7] .button-text.font-family-dharma,
      html[lang=ja-JP] .secondary-button[data-v-0d875eb7] .button-text.font-family-dharma {
        font-size: 1.555rem
      }
    }

    .button-margin[data-v-0d875eb7] {
      margin-bottom: .495rem
    }

    .info-item+.info-item[data-v-a6ac5e68] {
      margin-left: 4px
    }

    .info-label[data-v-a6ac5e68] {
      position: relative;
      top: -1px;
      display: inline-block;
      text-transform: uppercase;
      padding: 3px 5px 1px 6px;
      border-radius: 4px;
      border: 1px solid #49494f;
      color: #9b9b9b;
      font-size: 8px;
      letter-spacing: .8px;
      font-weight: 700;
      vertical-align: middle
    }

    @media(max-width:767px) {
      .info-label-small[data-v-a6ac5e68] {
        top: -3px;
        padding: 1px 3px 0 4px
      }
    }

    .secondary-title[data-v-a6ac5e68] {
      font-size: .666rem;
      text-transform: uppercase;
      vertical-align: middle
    }

    .modal-secondary-button[data-v-f16a66fa] {
      padding: 0 .5rem !important
    }

    .dropdown .btn-secondary[data-v-f16a66fa] {
      border-radius: 10px 0 0 10px !important
    }

    .dropdown-btn[data-v-f16a66fa] {
      background-color: #fff;
      padding: 12px;
      border-left: 1px solid #000;
      border-radius: 0 10px 10px 0;
      cursor: pointer
    }

    .dropdown-btn[data-v-f16a66fa]:focus,
    .dropdown-btn[data-v-f16a66fa]:hover {
      background-color: hsla(0, 0%, 100%, .8);
      transition: background-color .25s cubic-bezier(.4, 0, 1, 1), opacity .25s cubic-bezier(.4, 0, 1, 1)
    }

    @media(min-width:1200px) {
      .dropdown-btn[data-v-f16a66fa] {
        padding: 13.5px
      }
    }

    .dropdown-content[data-v-f16a66fa] {
      margin-top: 2px;
      z-index: 10;
      background: #1e1e22;
      box-shadow: 0 4px 10px 6px rgba(0, 0, 0, .2);
      border-radius: 10px;
      min-width: 300px
    }

    .dropdown-list[data-v-f16a66fa]:focus,
    .dropdown-list[data-v-f16a66fa]:hover {
      background: hsla(0, 0%, 100%, .1)
    }

    .dropdown-list[data-v-f16a66fa]:first-child {
      border-radius: 10px 10px 0 0
    }

    .dropdown-list[data-v-f16a66fa]:nth-child(2) {
      border-radius: 0 0 10px 10px
    }

    .check[data-v-f16a66fa] {
      visibility: hidden
    }

    .dropdown-link[data-v-f16a66fa] {
      display: block;
      padding: .5625rem .5625rem .5625rem 1.5rem
    }

    .dropdown-link.active-client[data-v-f16a66fa] {
      color: #0ff
    }

    .dropdown-link.active-client .check[data-v-f16a66fa] {
      visibility: visible
    }

    .flex-nowrap[data-v-54a2867b] {
      flex-wrap: nowrap
    }

    .view-all[data-v-54a2867b] {
      transition-property: background, color;
      transition-duration: .25s;
      transition-timing-function: cubic-bezier(.39, .575, .565, 1);
      align-self: center;
      flex-shrink: 0;
      background: #26282d;
      border-radius: 6px;
      font-size: 10px;
      letter-spacing: .8px;
      padding: 5px 8px 3px 10px
    }

    .view-all[data-v-54a2867b]:focus,
    .view-all[data-v-54a2867b]:hover {
      background: #34373e;
      color: #efefef
    }

    .track-wrapper[data-v-54a2867b] {
      border-radius: 10px;
      margin-bottom: 0;
      overflow: hidden
    }

    .has-controls[data-v-54a2867b]:hover {
      background-color: hsla(0, 0%, 95.7%, .07);
      cursor: pointer
    }

    .active-controls:focus .album-play[data-v-54a2867b],
    .active-controls:focus .track-controls[data-v-54a2867b],
    .has-controls:hover .album-play[data-v-54a2867b],
    .has-controls:hover .track-controls[data-v-54a2867b],
    .track-active .album-play[data-v-54a2867b],
    .track-active .track-controls[data-v-54a2867b] {
      opacity: 1
    }

    .active-controls:focus .album-number[data-v-54a2867b],
    .has-controls:hover .album-number[data-v-54a2867b],
    .track-active .album-number[data-v-54a2867b] {
      opacity: 0
    }

    .track-active[data-v-54a2867b],
    .track-highlight[data-v-54a2867b] {
      background-color: #26282d
    }

    .track-item[data-v-54a2867b] {
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      float: left;
      width: 100%
    }

    @media(min-width:992px) {
      .track-item[data-v-54a2867b] {
        padding: .75rem 0
      }
    }

    .track-image[data-v-54a2867b] {
      position: relative;
      float: left;
      outline: 0;
      height: 55px;
      width: 55px;
      margin-right: .75rem
    }

    @media(max-width:767px) {
      .track-image[data-v-54a2867b] {
        margin-right: .375rem
      }
    }

    .album-number[data-v-54a2867b],
    .album-play[data-v-54a2867b],
    .track-controls[data-v-54a2867b] {
      opacity: 0;
      transition: opacity .25s cubic-bezier(.39, .575, .565, 1)
    }

    .album-number[data-v-54a2867b] {
      opacity: 1
    }

    .track-controls[data-v-54a2867b] {
      position: absolute;
      top: -1px;
      bottom: 0;
      right: 0;
      left: 0;
      padding-top: 1rem;
      background: rgba(0, 0, 0, .5);
      text-align: center;
      z-index: 3;
      cursor: pointer
    }

    @media(min-width:1200px) {
      .track-controls[data-v-54a2867b] {
        padding: .75rem
      }
    }

    .track-artists[data-v-54a2867b],
    .track-info[data-v-54a2867b],
    .track-name[data-v-54a2867b] {
      float: left;
      padding: .75rem
    }

    .track-name[data-v-54a2867b] {
      flex-basis: 50%;
      width: 50%
    }

    .has-info>.track-name[data-v-54a2867b] {
      flex-basis: 33%;
      width: 33%
    }

    @media(max-width:991px) {
      .track-name[data-v-54a2867b] {
        flex-grow: 1;
        width: 70% !important
      }
    }

    .track-artists[data-v-54a2867b] {
      flex-grow: 1;
      flex-basis: 42%;
      width: 42%
    }

    .has-info>.track-artists[data-v-54a2867b],
    .track-info[data-v-54a2867b] {
      flex-grow: 0;
      flex-basis: 25%;
      width: 25%
    }

    .track-info[data-v-54a2867b] {
      flex-grow: 1
    }

    .track-action[data-v-54a2867b] {
      height: 28px;
      max-width: 6rem;
      overflow: hidden;
      margin: .75rem 0;
      float: right;
      text-align: right
    }

    .track-options[data-v-54a2867b] {
      color: #9b9b9b;
      width: 1.5rem
    }

    .track-options[data-v-54a2867b]:focus,
    .track-options[data-v-54a2867b]:hover {
      color: #fff
    }

    .track-options[data-v-54a2867b]:focus-visible svg {
      outline: 1px solid #0ff;
      outline-offset: -1px
    }

    .album-play[data-v-54a2867b] {
      position: absolute;
      top: 0;
      left: .375rem
    }

    .progress-bar-wrapper[data-v-54a2867b] {
      float: left;
      margin: -2px -.75rem 0;
      width: calc(100% + 1.5rem)
    }

    .track-active .progress-bar-wrapper[data-v-54a2867b] {
      background: #49494f
    }

    .album-controls[data-v-54a2867b] {
      position: relative;
      float: left;
      margin: .75rem .375rem .75rem 0;
      text-align: center;
      min-width: 1.25rem;
      outline: 0
    }

    .album-controls[data-v-54a2867b]:not(.active-controls),
    .track-image[data-v-54a2867b]:not(.active-controls) {
      cursor: auto
    }

    .info-wrapper[data-v-54a2867b] {
      flex-shrink: 0
    }

    .contain-width[data-v-3aa8e486] {
      margin-left: auto;
      margin-right: auto;
      padding-left: .75rem;
      padding-right: 3rem;
      max-width: 1404px
    }

    @supports(padding:calc(100vw)) {
      .contain-width[data-v-3aa8e486] {
        max-width: none;
        width: 100%
      }

      .contain-width[data-v-3aa8e486]:after {
        content: "";
        flex: 0 0 auto;
        height: 1px;
        width: .75rem
      }

      @media(min-width:576px) {
        .contain-width[data-v-3aa8e486]:after {
          width: 2.25rem
        }
      }

      @media(min-width:1404px) {
        .contain-width[data-v-3aa8e486]:after {
          width: calc(50vw - 702px + 2.25rem)
        }
      }

      @media(min-width:576px) {
        .contain-width[data-v-3aa8e486] {
          padding-left: 2.25rem;
          padding-right: 2.25rem
        }
      }

      @media(min-width:1404px) {
        .contain-width[data-v-3aa8e486] {
          padding-left: calc(50vw - 702px + 2.25rem);
          padding-right: calc(50vw - 702px + 2.25rem)
        }
      }
    }

    .tidal-carousel-wrapper[data-v-3aa8e486] {
      overflow: hidden;
      min-width: 100%
    }

    @media(max-width:991px) {
      .tidal-carousel-wrapper.nowrap-fill[data-v-3aa8e486] {
        padding-left: 0;
        padding-right: 0
      }

      .tidal-carousel-wrapper.nowrap-fill.padding-fill[data-v-3aa8e486] {
        margin-left: -1.5rem;
        margin-right: -1.5rem
      }
    }

    .tidal-carousel[data-v-3aa8e486] {
      -ms-overflow-style: none;
      overflow: -moz-scrollbars-none
    }

    .tidal-carousel[data-v-3aa8e486]::-webkit-scrollbar {
      display: none
    }

    .tidal-carousel[data-v-3aa8e486]:focus,
    .tidal-carousel[data-v-3aa8e486]:hover {
      will-change: scroll-position
    }

    .tidal-carousel [tabindex="-1"][data-v-3aa8e486] {
      outline: none
    }

    @supports(-moz-appearance:none) {
      .tidal-carousel[data-v-3aa8e486] {
        overflow: hidden
      }
    }

    @supports(scrollbar-width:none) {
      .tidal-carousel[data-v-3aa8e486] {
        overflow-x: auto;
        scrollbar-width: none
      }
    }

    .tidal-carousel.can-snap[data-v-3aa8e486] {
      scroll-snap-type: x mandatory
    }

    .tidal-carousel.can-snap .carousel-ref[data-v-3aa8e486],
    .tidal-carousel.can-snap .nowrap-element[data-v-3aa8e486],
    .tidal-carousel.can-snap>[class*=col-][data-v-3aa8e486] {
      scroll-snap-align: center
    }

    .nowrap-fill.padding-fill .tidal-carousel[data-v-3aa8e486] {
      padding: 1.5rem
    }

    @media(min-width:576px)and (max-width:767px) {
      .nowrap-fill.padding-fill .tidal-carousel[data-v-3aa8e486] {
        padding: 1.5rem 3rem
      }
    }

    .nowrap-fill.padding-fill .tidal-carousel[data-v-3aa8e486]:after {
      flex: 0 0 auto;
      height: 1px;
      width: 1.5rem
    }

    @media(min-width:576px)and (max-width:767px) {
      .nowrap-fill.padding-fill .tidal-carousel[data-v-3aa8e486]:after {
        width: 3rem
      }
    }

    @media(max-width:991px) {
      .nowrap-fill .tidal-carousel[data-v-3aa8e486] {
        padding-left: 1.5rem;
        padding-right: 1.5rem
      }
    }

    @media(max-width:991px)and (min-width:576px) {
      .nowrap-fill .tidal-carousel[data-v-3aa8e486] {
        padding-left: 3rem;
        padding-right: 3rem
      }
    }

    @media(max-width:991px) {
      .nowrap-fill .tidal-carousel[data-v-3aa8e486]:after {
        flex: 0 0 auto;
        height: 1px;
        width: 1.5rem
      }
    }

    @media(max-width:991px)and (min-width:576px) {
      .nowrap-fill .tidal-carousel[data-v-3aa8e486]:after {
        width: 3rem
      }
    }

    .tidal-carousel-animate[data-v-3aa8e486] {
      transition: transform .5s cubic-bezier(.39, .575, .565, 1)
    }

    .tidal-carousel-control[data-v-3aa8e486] {
      position: absolute;
      top: 0;
      bottom: 0;
      z-index: 3;
      color: #fff;
      outline: 0;
      width: 3.3333rem;
      opacity: .8;
      transition: opacity .25s cubic-bezier(.39, .575, .565, 1), transform .25s cubic-bezier(.39, .575, .565, 1)
    }

    .tidal-carousel-control-bg[data-v-3aa8e486] {
      opacity: 1;
      z-index: 2
    }

    .theme-native .tidal-carousel-control-bg[data-v-3aa8e486] {
      filter: invert(1)
    }

    @media screen and (prefers-color-scheme:dark) {
      .theme-native .tidal-carousel-control-bg[data-v-3aa8e486] {
        filter: none
      }
    }

    .theme-dark .tidal-carousel-control-bg[data-v-3aa8e486] {
      filter: none
    }

    .theme-white .tidal-carousel-control-bg[data-v-3aa8e486] {
      filter: invert(1)
    }

    .tidal-carousel-icon[data-v-3aa8e486] {
      filter: drop-shadow(0 0 2px rgba(0, 0, 0, .9))
    }

    .tidal-carousel-control--next[data-v-3aa8e486],
    .tidal-carousel-control--prev[data-v-3aa8e486] {
      background-color: transparent;
      cursor: pointer;
      text-align: center
    }

    .tidal-carousel-control--prev[data-v-3aa8e486],
    .tidal-carousel-control--prev-bg[data-v-3aa8e486] {
      left: 0;
      transform: translateX(-3.3333rem)
    }

    .tidal-carousel-control--prev-bg[data-v-3aa8e486] {
      background-image: linear-gradient(90deg, rgba(0, 0, 0, .6) 0, rgba(0, 0, 0, .4428) 19%, rgba(0, 0, 0, .3246) 34%, rgba(0, 0, 0, .2292) 47%, rgba(0, 0, 0, .1668) 56.5%, rgba(0, 0, 0, .1164) 65%, rgba(0, 0, 0, .0756) 73%, rgba(0, 0, 0, .045) 80.2%, rgba(0, 0, 0, .0252) 86.1%, rgba(0, 0, 0, .0126) 91%, rgba(0, 0, 0, .0048) 95.2%, rgba(0, 0, 0, .0012) 98.2%, transparent)
    }

    .tidal-carousel-control--next[data-v-3aa8e486],
    .tidal-carousel-control--next-bg[data-v-3aa8e486] {
      right: 0;
      transform: translateX(3.3333rem)
    }

    .tidal-carousel-control--next-bg[data-v-3aa8e486] {
      background-image: linear-gradient(270deg, rgba(0, 0, 0, .6) 0, rgba(0, 0, 0, .4428) 19%, rgba(0, 0, 0, .3246) 34%, rgba(0, 0, 0, .2292) 47%, rgba(0, 0, 0, .1668) 56.5%, rgba(0, 0, 0, .1164) 65%, rgba(0, 0, 0, .0756) 73%, rgba(0, 0, 0, .045) 80.2%, rgba(0, 0, 0, .0252) 86.1%, rgba(0, 0, 0, .0126) 91%, rgba(0, 0, 0, .0048) 95.2%, rgba(0, 0, 0, .0012) 98.2%, transparent)
    }

    .tidal-carousel-control[data-v-3aa8e486]:focus,
    .tidal-carousel-control[data-v-3aa8e486]:hover {
      opacity: 1
    }

    .touch .tidal-carousel-controls[data-v-3aa8e486] {
      display: none !important
    }

    .tidal-carousel-control[data-v-3aa8e486]:focus,
    .tidal-carousel-control:focus+.tidal-carousel-control-bg[data-v-3aa8e486],
    .tidal-carousel-control[data-v-3aa8e486]:hover,
    .tidal-carousel-control:hover+.tidal-carousel-control-bg[data-v-3aa8e486],
    .tidal-carousel-wrapper:hover .tidal-carousel-control[data-v-3aa8e486],
    .tidal-carousel:focus .tidal-carousel-control[data-v-3aa8e486] {
      transform: translateX(0)
    }

    .tidal-carousel-control--prev[data-v-3aa8e486]:disabled,
    .tidal-carousel-control--prev:disabled+.tidal-carousel-control-bg[data-v-3aa8e486] {
      transform: translateX(-3.3333rem) !important
    }

    .tidal-carousel-control--next[data-v-3aa8e486]:disabled,
    .tidal-carousel-control--next:disabled+.tidal-carousel-control-bg[data-v-3aa8e486] {
      transform: translateX(3.3333rem) !important
    }

    .tidal-carousel-wrapper:focus .tidal-carousel-controls[data-v-3aa8e486],
    .tidal-carousel-wrapper:hover .tidal-carousel-controls[data-v-3aa8e486] {
      display: block
    }

    .floating-wrapper[data-v-1727d62b] {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      opacity: 0;
      pointer-events: none;
      transform: translateY(100%);
      transition: opacity .25s cubic-bezier(.4, 0, 1, 1), bottom .25s cubic-bezier(.4, 0, 1, 1), transform .25s cubic-bezier(.4, 0, 1, 1);
      will-change: opacity, bottom, transform;
      z-index: 10
    }

    .floating--is-visible[data-v-1727d62b] {
      bottom: 1rem;
      opacity: .95;
      transform: translate(0)
    }

    .floating--is-visible[data-v-1727d62b]:focus,
    .floating--is-visible[data-v-1727d62b]:hover {
      opacity: 1
    }

    @supports(bottom:constant(safe-area-inset-bottom)) {
      .floating--is-visible[data-v-1727d62b] {
        bottom: calc(.75rem + constant(safe-area-inset-bottom))
      }
    }

    @supports(bottom:env(safe-area-inset-bottom)) {
      .floating--is-visible[data-v-1727d62b] {
        bottom: calc(.75rem + env(safe-area-inset-bottom))
      }
    }

    .floating-content[data-v-1727d62b] {
      display: inline-block;
      color: #000;
      background-color: #fff;
      border-radius: 10px;
      pointer-events: all;
      padding: 1rem 1.5rem
    }

    @media(max-width:560px) {
      .floating--is-visible[data-v-1727d62b] {
        opacity: 1;
        bottom: 0
      }

      .floating-content[data-v-1727d62b] {
        display: block;
        border-radius: 0
      }

      @supports(padding-bottom:constant(safe-area-inset-bottom)) {
        .floating-content[data-v-1727d62b] {
          padding-bottom: calc(.75rem + constant(safe-area-inset-bottom))
        }
      }

      @supports(padding-bottom:env(safe-area-inset-bottom)) {
        .floating-content[data-v-1727d62b] {
          padding-bottom: calc(.75rem + env(safe-area-inset-bottom))
        }
      }

      .floating-text[data-v-1727d62b] {
        display: block
      }

      .floating-line[data-v-1727d62b] {
        display: none
      }

      .floating-link[data-v-1727d62b] {
        display: inline-block;
        padding-top: .25rem
      }
    }

    .tidal-footer[data-v-3bd76d68] {
      background: #000
    }

    .tidal-footer[data-v-d410bc9a] {
      outline: 0
    }

    @media(min-width:768px) {
      .tidal-footer-line[data-v-d410bc9a] {
        border-top: 1px solid hsla(0, 0%, 100%, .1)
      }
    }

    .tidal-footer-slot[data-v-d410bc9a] {
      line-height: 3.5rem
    }

    @media(min-width:768px) {
      .tidal-footer-links[data-v-d410bc9a] {
        line-height: 5
      }
    }

    @media(min-width:768px) {
      .inline-block-md-min[data-v-d410bc9a] {
        display: inline-block
      }
    }

    .large-icon[data-v-d410bc9a] svg {
      width: 1.333em
    }

    .language-wrapper {
      background: rgba(38, 40, 45, .6);
      transition: background-color .25s cubic-bezier(.4, 0, 1, 1);
      border: 1px solid rgba(73, 73, 79, .6);
      border-radius: 6px
    }

    @media(max-width:767px) {
      .language-wrapper {
        margin-bottom: .75rem
      }
    }

    @media(min-width:768px) {
      .language-wrapper {
        margin-left: .75rem
      }
    }

    .language-wrapper.is-active,
    .language-wrapper:hover {
      background: #26282d
    }

    .language-wrapper .tidal-dropdown-button {
      padding: 0 .75rem
    }

    .language-wrapper .tidal-dropdown-menu-wrapper {
      bottom: 3.5rem !important
    }

    .language-wrapper .tidal-icon {
      margin-right: .33em
    }

    .custom-dropdown {
      line-height: 1.5
    }

    .tidal-dropdown[data-v-5970ef6c] {
      line-height: 1.5
    }

    .tidal-dropdown-arrow[data-v-5970ef6c] svg {
      transition: transform .1875s cubic-bezier(.4, 0, 1, 1);
      margin-left: .5rem
    }

    .is-active .tidal-dropdown-arrow[data-v-5970ef6c] svg {
      transform: rotate(180deg)
    }

    .tidal-dropdown-menu-wrapper[data-v-5970ef6c] {
      position: absolute;
      top: 3rem;
      min-width: 100%;
      z-index: 10;
      overflow: visible !important
    }

    .is-reversed .tidal-dropdown-menu-wrapper[data-v-5970ef6c] {
      top: auto;
      bottom: 3rem
    }

    .tidal-dropdown-menu[data-v-5970ef6c] {
      position: absolute;
      min-width: 100%;
      right: 0;
      background-color: #26282d;
      box-shadow: 0 4px 10px 0 rgba(0, 0, 0, .4)
    }

    .is-reversed .tidal-dropdown-menu[data-v-5970ef6c] {
      bottom: 0
    }

    .is-centered .tidal-dropdown-menu[data-v-5970ef6c] {
      right: auto;
      left: 0;
      margin-left: 50%;
      transform: translateX(-50%)
    }

    .tidal-dropdown-menu--triangle[data-v-5970ef6c] {
      position: absolute;
      top: -14px;
      right: 50%;
      margin-right: -12px;
      width: 0;
      height: 0;
      border-color: transparent transparent #26282d;
      border-style: solid;
      border-width: 0 12px 14px;
      -webkit-transform: rotate(1turn)
    }

    .is-reversed .tidal-dropdown-menu--triangle[data-v-5970ef6c] {
      top: auto;
      bottom: -14px;
      transform: rotate(180deg)
    }

    .tidal-dropdown-menu--item[data-v-5970ef6c] {
      border-bottom: 1px solid hsla(0, 0%, 100%, .05);
      margin: 0;
      white-space: nowrap
    }

    .tidal-dropdown-menu--item[data-v-5970ef6c]:last-child {
      border: 0
    }

    .tidal-dropdown-menu--link[data-v-5970ef6c] {
      display: block;
      padding: .75rem 1.5rem
    }

    .tidal-dropdown-menu--subtitle[data-v-5970ef6c] {
      max-width: 11rem;
      min-width: 100%
    }
  </style>
  <script src="https://cdn.cookielaw.org/scripttemplates/202304.1.0/otBannerSdk.js" async="" type="text/javascript"></script>
  <style type="text/css">
    .tidal-header-user[data-v-978b7822] {
      vertical-align: middle;
      height: 3rem;
      width: 3rem
    }

    @media(min-width:992px) {
      .tidal-header-user[data-v-978b7822] {
        position: relative;
        top: -2px
      }
    }
  </style>
  <script charset="utf-8" src="/browse/_nuxt/ef31be5.modern.js"></script>
  <script charset="utf-8" src="/browse/_nuxt/e254352.modern.js"></script>
  <script charset="utf-8" src="/browse/_nuxt/07809cf.modern.js"></script>
  <script charset="utf-8" src="/browse/_nuxt/1e57559.modern.js"></script>
  <style id="onetrust-style">
    #onetrust-banner-sdk {
      -ms-text-size-adjust: 100%;
      -webkit-text-size-adjust: 100%
    }

    #onetrust-banner-sdk .onetrust-vendors-list-handler {
      cursor: pointer;
      color: #1f96db;
      font-size: inherit;
      font-weight: bold;
      text-decoration: none;
      margin-left: 5px
    }

    #onetrust-banner-sdk .onetrust-vendors-list-handler:hover {
      color: #1f96db
    }

    #onetrust-banner-sdk:focus {
      outline: 2px solid #000;
      outline-offset: -2px
    }

    #onetrust-banner-sdk a:focus {
      outline: 2px solid #000
    }

    #onetrust-banner-sdk #onetrust-accept-btn-handler,
    #onetrust-banner-sdk #onetrust-reject-all-handler,
    #onetrust-banner-sdk #onetrust-pc-btn-handler {
      outline-offset: 1px
    }

    #onetrust-banner-sdk.ot-bnr-w-logo .ot-bnr-logo {
      height: 64px;
      width: 64px
    }

    #onetrust-banner-sdk .ot-close-icon,
    #onetrust-pc-sdk .ot-close-icon,
    #ot-sync-ntfy .ot-close-icon {
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
      height: 12px;
      width: 12px
    }

    #onetrust-banner-sdk .powered-by-logo,
    #onetrust-banner-sdk .ot-pc-footer-logo a,
    #onetrust-pc-sdk .powered-by-logo,
    #onetrust-pc-sdk .ot-pc-footer-logo a,
    #ot-sync-ntfy .powered-by-logo,
    #ot-sync-ntfy .ot-pc-footer-logo a {
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
      height: 25px;
      width: 152px;
      display: block;
      text-decoration: none;
      font-size: .75em
    }

    #onetrust-banner-sdk .powered-by-logo:hover,
    #onetrust-banner-sdk .ot-pc-footer-logo a:hover,
    #onetrust-pc-sdk .powered-by-logo:hover,
    #onetrust-pc-sdk .ot-pc-footer-logo a:hover,
    #ot-sync-ntfy .powered-by-logo:hover,
    #ot-sync-ntfy .ot-pc-footer-logo a:hover {
      color: #565656
    }

    #onetrust-banner-sdk h3 *,
    #onetrust-banner-sdk h4 *,
    #onetrust-banner-sdk h6 *,
    #onetrust-banner-sdk button *,
    #onetrust-banner-sdk a[data-parent-id] *,
    #onetrust-pc-sdk h3 *,
    #onetrust-pc-sdk h4 *,
    #onetrust-pc-sdk h6 *,
    #onetrust-pc-sdk button *,
    #onetrust-pc-sdk a[data-parent-id] *,
    #ot-sync-ntfy h3 *,
    #ot-sync-ntfy h4 *,
    #ot-sync-ntfy h6 *,
    #ot-sync-ntfy button *,
    #ot-sync-ntfy a[data-parent-id] * {
      font-size: inherit;
      font-weight: inherit;
      color: inherit
    }

    #onetrust-banner-sdk .ot-hide,
    #onetrust-pc-sdk .ot-hide,
    #ot-sync-ntfy .ot-hide {
      display: none !important
    }

    #onetrust-banner-sdk button.ot-link-btn:hover,
    #onetrust-pc-sdk button.ot-link-btn:hover,
    #ot-sync-ntfy button.ot-link-btn:hover {
      text-decoration: underline;
      opacity: 1
    }

    #onetrust-pc-sdk .ot-sdk-row .ot-sdk-column {
      padding: 0
    }

    #onetrust-pc-sdk .ot-sdk-container {
      padding-right: 0
    }

    #onetrust-pc-sdk .ot-sdk-row {
      flex-direction: initial;
      width: 100%
    }

    #onetrust-pc-sdk [type=checkbox]:checked,
    #onetrust-pc-sdk [type=checkbox]:not(:checked) {
      pointer-events: initial
    }

    #onetrust-pc-sdk [type=checkbox]:disabled+label::before,
    #onetrust-pc-sdk [type=checkbox]:disabled+label:after,
    #onetrust-pc-sdk [type=checkbox]:disabled+label {
      pointer-events: none;
      opacity: .7
    }

    #onetrust-pc-sdk #vendor-list-content {
      transform: translate3d(0, 0, 0)
    }

    #onetrust-pc-sdk li input[type=checkbox] {
      z-index: 1
    }

    #onetrust-pc-sdk li .ot-checkbox label {
      z-index: 2
    }

    #onetrust-pc-sdk li .ot-checkbox input[type=checkbox] {
      height: auto;
      width: auto
    }

    #onetrust-pc-sdk li .host-title a,
    #onetrust-pc-sdk li .ot-host-name a,
    #onetrust-pc-sdk li .accordion-text,
    #onetrust-pc-sdk li .ot-acc-txt {
      z-index: 2;
      position: relative
    }

    #onetrust-pc-sdk input {
      margin: 3px .1ex
    }

    #onetrust-pc-sdk .pc-logo,
    #onetrust-pc-sdk .ot-pc-logo {
      height: 60px;
      width: 180px;
      background-position: center;
      background-size: contain;
      background-repeat: no-repeat;
      display: inline-flex;
      justify-content: center;
      align-items: center
    }

    #onetrust-pc-sdk .pc-logo img,
    #onetrust-pc-sdk .ot-pc-logo img {
      max-height: 100%;
      max-width: 100%
    }

    #onetrust-pc-sdk .screen-reader-only,
    #onetrust-pc-sdk .ot-scrn-rdr,
    .ot-sdk-cookie-policy .screen-reader-only,
    .ot-sdk-cookie-policy .ot-scrn-rdr {
      border: 0;
      clip: rect(0 0 0 0);
      height: 1px;
      margin: -1px;
      overflow: hidden;
      padding: 0;
      position: absolute;
      width: 1px
    }

    #onetrust-pc-sdk.ot-fade-in,
    .onetrust-pc-dark-filter.ot-fade-in,
    #onetrust-banner-sdk.ot-fade-in {
      animation-name: onetrust-fade-in;
      animation-duration: 400ms;
      animation-timing-function: ease-in-out
    }

    #onetrust-pc-sdk.ot-hide {
      display: none !important
    }

    .onetrust-pc-dark-filter.ot-hide {
      display: none !important
    }

    #ot-sdk-btn.ot-sdk-show-settings,
    #ot-sdk-btn.optanon-show-settings {
      color: #68b631;
      border: 1px solid #68b631;
      height: auto;
      white-space: normal;
      word-wrap: break-word;
      padding: .8em 2em;
      font-size: .8em;
      line-height: 1.2;
      cursor: pointer;
      -moz-transition: .1s ease;
      -o-transition: .1s ease;
      -webkit-transition: 1s ease;
      transition: .1s ease
    }

    #ot-sdk-btn.ot-sdk-show-settings:hover,
    #ot-sdk-btn.optanon-show-settings:hover {
      color: #fff;
      background-color: #68b631
    }

    .onetrust-pc-dark-filter {
      background: rgba(0, 0, 0, .5);
      z-index: 2147483646;
      width: 100%;
      height: 100%;
      overflow: hidden;
      position: fixed;
      top: 0;
      bottom: 0;
      left: 0
    }

    @keyframes onetrust-fade-in {
      0% {
        opacity: 0
      }

      100% {
        opacity: 1
      }
    }

    .ot-cookie-label {
      text-decoration: underline
    }

    @media only screen and (min-width: 426px)and (max-width: 896px)and (orientation: landscape) {
      #onetrust-pc-sdk p {
        font-size: .75em
      }
    }

    #onetrust-banner-sdk .banner-option-input:focus+label {
      outline: 1px solid #000;
      outline-style: auto
    }

    .category-vendors-list-handler+a:focus,
    .category-vendors-list-handler+a:focus-visible {
      outline: 2px solid #000
    }

    #onetrust-pc-sdk .ot-userid-title {
      margin-top: 10px
    }

    #onetrust-pc-sdk .ot-userid-title>span,
    #onetrust-pc-sdk .ot-userid-timestamp>span {
      font-weight: 700
    }

    #onetrust-pc-sdk .ot-userid-desc {
      font-style: italic
    }

    #onetrust-pc-sdk .ot-host-desc a {
      pointer-events: initial
    }

    #onetrust-pc-sdk .ot-ven-hdr>p a {
      position: relative;
      z-index: 2;
      pointer-events: initial
    }

    #onetrust-pc-sdk .ot-vnd-serv .ot-vnd-item .ot-vnd-info a,
    #onetrust-pc-sdk .ot-vs-list .ot-vnd-item .ot-vnd-info a {
      margin-right: auto
    }

    #onetrust-pc-sdk .ot-pc-footer-logo img {
      width: 136px;
      height: 16px
    }

    #onetrust-banner-sdk .ot-optout-signal,
    #onetrust-pc-sdk .ot-optout-signal {
      border: 1px solid #32ae88;
      border-radius: 3px;
      padding: 5px;
      margin-bottom: 10px;
      background-color: #f9fffa;
      font-size: .85rem;
      line-height: 2
    }

    #onetrust-banner-sdk .ot-optout-signal .ot-optout-icon,
    #onetrust-pc-sdk .ot-optout-signal .ot-optout-icon {
      display: inline;
      margin-right: 5px
    }

    #onetrust-banner-sdk .ot-optout-signal svg,
    #onetrust-pc-sdk .ot-optout-signal svg {
      height: 20px;
      width: 30px;
      transform: scale(0.5)
    }

    #onetrust-banner-sdk .ot-optout-signal svg path,
    #onetrust-pc-sdk .ot-optout-signal svg path {
      fill: #32ae88
    }

    #onetrust-banner-sdk,
    #onetrust-pc-sdk,
    #ot-sdk-cookie-policy,
    #ot-sync-ntfy {
      font-size: 16px
    }

    #onetrust-banner-sdk *,
    #onetrust-banner-sdk ::after,
    #onetrust-banner-sdk ::before,
    #onetrust-pc-sdk *,
    #onetrust-pc-sdk ::after,
    #onetrust-pc-sdk ::before,
    #ot-sdk-cookie-policy *,
    #ot-sdk-cookie-policy ::after,
    #ot-sdk-cookie-policy ::before,
    #ot-sync-ntfy *,
    #ot-sync-ntfy ::after,
    #ot-sync-ntfy ::before {
      -webkit-box-sizing: content-box;
      -moz-box-sizing: content-box;
      box-sizing: content-box
    }

    #onetrust-banner-sdk div,
    #onetrust-banner-sdk span,
    #onetrust-banner-sdk h1,
    #onetrust-banner-sdk h2,
    #onetrust-banner-sdk h3,
    #onetrust-banner-sdk h4,
    #onetrust-banner-sdk h5,
    #onetrust-banner-sdk h6,
    #onetrust-banner-sdk p,
    #onetrust-banner-sdk img,
    #onetrust-banner-sdk svg,
    #onetrust-banner-sdk button,
    #onetrust-banner-sdk section,
    #onetrust-banner-sdk a,
    #onetrust-banner-sdk label,
    #onetrust-banner-sdk input,
    #onetrust-banner-sdk ul,
    #onetrust-banner-sdk li,
    #onetrust-banner-sdk nav,
    #onetrust-banner-sdk table,
    #onetrust-banner-sdk thead,
    #onetrust-banner-sdk tr,
    #onetrust-banner-sdk td,
    #onetrust-banner-sdk tbody,
    #onetrust-banner-sdk .ot-main-content,
    #onetrust-banner-sdk .ot-toggle,
    #onetrust-banner-sdk #ot-content,
    #onetrust-banner-sdk #ot-pc-content,
    #onetrust-banner-sdk .checkbox,
    #onetrust-pc-sdk div,
    #onetrust-pc-sdk span,
    #onetrust-pc-sdk h1,
    #onetrust-pc-sdk h2,
    #onetrust-pc-sdk h3,
    #onetrust-pc-sdk h4,
    #onetrust-pc-sdk h5,
    #onetrust-pc-sdk h6,
    #onetrust-pc-sdk p,
    #onetrust-pc-sdk img,
    #onetrust-pc-sdk svg,
    #onetrust-pc-sdk button,
    #onetrust-pc-sdk section,
    #onetrust-pc-sdk a,
    #onetrust-pc-sdk label,
    #onetrust-pc-sdk input,
    #onetrust-pc-sdk ul,
    #onetrust-pc-sdk li,
    #onetrust-pc-sdk nav,
    #onetrust-pc-sdk table,
    #onetrust-pc-sdk thead,
    #onetrust-pc-sdk tr,
    #onetrust-pc-sdk td,
    #onetrust-pc-sdk tbody,
    #onetrust-pc-sdk .ot-main-content,
    #onetrust-pc-sdk .ot-toggle,
    #onetrust-pc-sdk #ot-content,
    #onetrust-pc-sdk #ot-pc-content,
    #onetrust-pc-sdk .checkbox,
    #ot-sdk-cookie-policy div,
    #ot-sdk-cookie-policy span,
    #ot-sdk-cookie-policy h1,
    #ot-sdk-cookie-policy h2,
    #ot-sdk-cookie-policy h3,
    #ot-sdk-cookie-policy h4,
    #ot-sdk-cookie-policy h5,
    #ot-sdk-cookie-policy h6,
    #ot-sdk-cookie-policy p,
    #ot-sdk-cookie-policy img,
    #ot-sdk-cookie-policy svg,
    #ot-sdk-cookie-policy button,
    #ot-sdk-cookie-policy section,
    #ot-sdk-cookie-policy a,
    #ot-sdk-cookie-policy label,
    #ot-sdk-cookie-policy input,
    #ot-sdk-cookie-policy ul,
    #ot-sdk-cookie-policy li,
    #ot-sdk-cookie-policy nav,
    #ot-sdk-cookie-policy table,
    #ot-sdk-cookie-policy thead,
    #ot-sdk-cookie-policy tr,
    #ot-sdk-cookie-policy td,
    #ot-sdk-cookie-policy tbody,
    #ot-sdk-cookie-policy .ot-main-content,
    #ot-sdk-cookie-policy .ot-toggle,
    #ot-sdk-cookie-policy #ot-content,
    #ot-sdk-cookie-policy #ot-pc-content,
    #ot-sdk-cookie-policy .checkbox,
    #ot-sync-ntfy div,
    #ot-sync-ntfy span,
    #ot-sync-ntfy h1,
    #ot-sync-ntfy h2,
    #ot-sync-ntfy h3,
    #ot-sync-ntfy h4,
    #ot-sync-ntfy h5,
    #ot-sync-ntfy h6,
    #ot-sync-ntfy p,
    #ot-sync-ntfy img,
    #ot-sync-ntfy svg,
    #ot-sync-ntfy button,
    #ot-sync-ntfy section,
    #ot-sync-ntfy a,
    #ot-sync-ntfy label,
    #ot-sync-ntfy input,
    #ot-sync-ntfy ul,
    #ot-sync-ntfy li,
    #ot-sync-ntfy nav,
    #ot-sync-ntfy table,
    #ot-sync-ntfy thead,
    #ot-sync-ntfy tr,
    #ot-sync-ntfy td,
    #ot-sync-ntfy tbody,
    #ot-sync-ntfy .ot-main-content,
    #ot-sync-ntfy .ot-toggle,
    #ot-sync-ntfy #ot-content,
    #ot-sync-ntfy #ot-pc-content,
    #ot-sync-ntfy .checkbox {
      font-family: inherit;
      font-weight: normal;
      -webkit-font-smoothing: auto;
      letter-spacing: normal;
      line-height: normal;
      padding: 0;
      margin: 0;
      height: auto;
      min-height: 0;
      max-height: none;
      width: auto;
      min-width: 0;
      max-width: none;
      border-radius: 0;
      border: none;
      clear: none;
      float: none;
      position: static;
      bottom: auto;
      left: auto;
      right: auto;
      top: auto;
      text-align: left;
      text-decoration: none;
      text-indent: 0;
      text-shadow: none;
      text-transform: none;
      white-space: normal;
      background: none;
      overflow: visible;
      vertical-align: baseline;
      visibility: visible;
      z-index: auto;
      box-shadow: none
    }

    #onetrust-banner-sdk label:before,
    #onetrust-banner-sdk label:after,
    #onetrust-banner-sdk .checkbox:after,
    #onetrust-banner-sdk .checkbox:before,
    #onetrust-pc-sdk label:before,
    #onetrust-pc-sdk label:after,
    #onetrust-pc-sdk .checkbox:after,
    #onetrust-pc-sdk .checkbox:before,
    #ot-sdk-cookie-policy label:before,
    #ot-sdk-cookie-policy label:after,
    #ot-sdk-cookie-policy .checkbox:after,
    #ot-sdk-cookie-policy .checkbox:before,
    #ot-sync-ntfy label:before,
    #ot-sync-ntfy label:after,
    #ot-sync-ntfy .checkbox:after,
    #ot-sync-ntfy .checkbox:before {
      content: "";
      content: none
    }

    #onetrust-banner-sdk .ot-sdk-container,
    #onetrust-pc-sdk .ot-sdk-container,
    #ot-sdk-cookie-policy .ot-sdk-container {
      position: relative;
      width: 100%;
      max-width: 100%;
      margin: 0 auto;
      padding: 0 20px;
      box-sizing: border-box
    }

    #onetrust-banner-sdk .ot-sdk-column,
    #onetrust-banner-sdk .ot-sdk-columns,
    #onetrust-pc-sdk .ot-sdk-column,
    #onetrust-pc-sdk .ot-sdk-columns,
    #ot-sdk-cookie-policy .ot-sdk-column,
    #ot-sdk-cookie-policy .ot-sdk-columns {
      width: 100%;
      float: left;
      box-sizing: border-box;
      padding: 0;
      display: initial
    }

    @media(min-width: 400px) {

      #onetrust-banner-sdk .ot-sdk-container,
      #onetrust-pc-sdk .ot-sdk-container,
      #ot-sdk-cookie-policy .ot-sdk-container {
        width: 90%;
        padding: 0
      }
    }

    @media(min-width: 550px) {

      #onetrust-banner-sdk .ot-sdk-container,
      #onetrust-pc-sdk .ot-sdk-container,
      #ot-sdk-cookie-policy .ot-sdk-container {
        width: 100%
      }

      #onetrust-banner-sdk .ot-sdk-column,
      #onetrust-banner-sdk .ot-sdk-columns,
      #onetrust-pc-sdk .ot-sdk-column,
      #onetrust-pc-sdk .ot-sdk-columns,
      #ot-sdk-cookie-policy .ot-sdk-column,
      #ot-sdk-cookie-policy .ot-sdk-columns {
        margin-left: 4%
      }

      #onetrust-banner-sdk .ot-sdk-column:first-child,
      #onetrust-banner-sdk .ot-sdk-columns:first-child,
      #onetrust-pc-sdk .ot-sdk-column:first-child,
      #onetrust-pc-sdk .ot-sdk-columns:first-child,
      #ot-sdk-cookie-policy .ot-sdk-column:first-child,
      #ot-sdk-cookie-policy .ot-sdk-columns:first-child {
        margin-left: 0
      }

      #onetrust-banner-sdk .ot-sdk-two.ot-sdk-columns,
      #onetrust-pc-sdk .ot-sdk-two.ot-sdk-columns,
      #ot-sdk-cookie-policy .ot-sdk-two.ot-sdk-columns {
        width: 13.3333333333%
      }

      #onetrust-banner-sdk .ot-sdk-three.ot-sdk-columns,
      #onetrust-pc-sdk .ot-sdk-three.ot-sdk-columns,
      #ot-sdk-cookie-policy .ot-sdk-three.ot-sdk-columns {
        width: 22%
      }

      #onetrust-banner-sdk .ot-sdk-four.ot-sdk-columns,
      #onetrust-pc-sdk .ot-sdk-four.ot-sdk-columns,
      #ot-sdk-cookie-policy .ot-sdk-four.ot-sdk-columns {
        width: 30.6666666667%
      }

      #onetrust-banner-sdk .ot-sdk-eight.ot-sdk-columns,
      #onetrust-pc-sdk .ot-sdk-eight.ot-sdk-columns,
      #ot-sdk-cookie-policy .ot-sdk-eight.ot-sdk-columns {
        width: 65.3333333333%
      }

      #onetrust-banner-sdk .ot-sdk-nine.ot-sdk-columns,
      #onetrust-pc-sdk .ot-sdk-nine.ot-sdk-columns,
      #ot-sdk-cookie-policy .ot-sdk-nine.ot-sdk-columns {
        width: 74%
      }

      #onetrust-banner-sdk .ot-sdk-ten.ot-sdk-columns,
      #onetrust-pc-sdk .ot-sdk-ten.ot-sdk-columns,
      #ot-sdk-cookie-policy .ot-sdk-ten.ot-sdk-columns {
        width: 82.6666666667%
      }

      #onetrust-banner-sdk .ot-sdk-eleven.ot-sdk-columns,
      #onetrust-pc-sdk .ot-sdk-eleven.ot-sdk-columns,
      #ot-sdk-cookie-policy .ot-sdk-eleven.ot-sdk-columns {
        width: 91.3333333333%
      }

      #onetrust-banner-sdk .ot-sdk-twelve.ot-sdk-columns,
      #onetrust-pc-sdk .ot-sdk-twelve.ot-sdk-columns,
      #ot-sdk-cookie-policy .ot-sdk-twelve.ot-sdk-columns {
        width: 100%;
        margin-left: 0
      }
    }

    #onetrust-banner-sdk h1,
    #onetrust-banner-sdk h2,
    #onetrust-banner-sdk h3,
    #onetrust-banner-sdk h4,
    #onetrust-banner-sdk h5,
    #onetrust-banner-sdk h6,
    #onetrust-pc-sdk h1,
    #onetrust-pc-sdk h2,
    #onetrust-pc-sdk h3,
    #onetrust-pc-sdk h4,
    #onetrust-pc-sdk h5,
    #onetrust-pc-sdk h6,
    #ot-sdk-cookie-policy h1,
    #ot-sdk-cookie-policy h2,
    #ot-sdk-cookie-policy h3,
    #ot-sdk-cookie-policy h4,
    #ot-sdk-cookie-policy h5,
    #ot-sdk-cookie-policy h6 {
      margin-top: 0;
      font-weight: 600;
      font-family: inherit
    }

    #onetrust-banner-sdk h1,
    #onetrust-pc-sdk h1,
    #ot-sdk-cookie-policy h1 {
      font-size: 1.5rem;
      line-height: 1.2
    }

    #onetrust-banner-sdk h2,
    #onetrust-pc-sdk h2,
    #ot-sdk-cookie-policy h2 {
      font-size: 1.5rem;
      line-height: 1.25
    }

    #onetrust-banner-sdk h3,
    #onetrust-pc-sdk h3,
    #ot-sdk-cookie-policy h3 {
      font-size: 1.5rem;
      line-height: 1.3
    }

    #onetrust-banner-sdk h4,
    #onetrust-pc-sdk h4,
    #ot-sdk-cookie-policy h4 {
      font-size: 1.5rem;
      line-height: 1.35
    }

    #onetrust-banner-sdk h5,
    #onetrust-pc-sdk h5,
    #ot-sdk-cookie-policy h5 {
      font-size: 1.5rem;
      line-height: 1.5
    }

    #onetrust-banner-sdk h6,
    #onetrust-pc-sdk h6,
    #ot-sdk-cookie-policy h6 {
      font-size: 1.5rem;
      line-height: 1.6
    }

    @media(min-width: 550px) {

      #onetrust-banner-sdk h1,
      #onetrust-pc-sdk h1,
      #ot-sdk-cookie-policy h1 {
        font-size: 1.5rem
      }

      #onetrust-banner-sdk h2,
      #onetrust-pc-sdk h2,
      #ot-sdk-cookie-policy h2 {
        font-size: 1.5rem
      }

      #onetrust-banner-sdk h3,
      #onetrust-pc-sdk h3,
      #ot-sdk-cookie-policy h3 {
        font-size: 1.5rem
      }

      #onetrust-banner-sdk h4,
      #onetrust-pc-sdk h4,
      #ot-sdk-cookie-policy h4 {
        font-size: 1.5rem
      }

      #onetrust-banner-sdk h5,
      #onetrust-pc-sdk h5,
      #ot-sdk-cookie-policy h5 {
        font-size: 1.5rem
      }

      #onetrust-banner-sdk h6,
      #onetrust-pc-sdk h6,
      #ot-sdk-cookie-policy h6 {
        font-size: 1.5rem
      }
    }

    #onetrust-banner-sdk p,
    #onetrust-pc-sdk p,
    #ot-sdk-cookie-policy p {
      margin: 0 0 1em 0;
      font-family: inherit;
      line-height: normal
    }

    #onetrust-banner-sdk a,
    #onetrust-pc-sdk a,
    #ot-sdk-cookie-policy a {
      color: #565656;
      text-decoration: underline
    }

    #onetrust-banner-sdk a:hover,
    #onetrust-pc-sdk a:hover,
    #ot-sdk-cookie-policy a:hover {
      color: #565656;
      text-decoration: none
    }

    #onetrust-banner-sdk .ot-sdk-button,
    #onetrust-banner-sdk button,
    #onetrust-pc-sdk .ot-sdk-button,
    #onetrust-pc-sdk button,
    #ot-sdk-cookie-policy .ot-sdk-button,
    #ot-sdk-cookie-policy button {
      margin-bottom: 1rem;
      font-family: inherit
    }

    #onetrust-banner-sdk .ot-sdk-button,
    #onetrust-banner-sdk button,
    #onetrust-pc-sdk .ot-sdk-button,
    #onetrust-pc-sdk button,
    #ot-sdk-cookie-policy .ot-sdk-button,
    #ot-sdk-cookie-policy button {
      display: inline-block;
      height: 38px;
      padding: 0 30px;
      color: #555;
      text-align: center;
      font-size: .9em;
      font-weight: 400;
      line-height: 38px;
      letter-spacing: .01em;
      text-decoration: none;
      white-space: nowrap;
      background-color: rgba(0, 0, 0, 0);
      border-radius: 2px;
      border: 1px solid #bbb;
      cursor: pointer;
      box-sizing: border-box
    }

    #onetrust-banner-sdk .ot-sdk-button:hover,
    #onetrust-banner-sdk :not(.ot-leg-btn-container)>button:not(.ot-link-btn):hover,
    #onetrust-banner-sdk :not(.ot-leg-btn-container)>button:not(.ot-link-btn):focus,
    #onetrust-pc-sdk .ot-sdk-button:hover,
    #onetrust-pc-sdk :not(.ot-leg-btn-container)>button:not(.ot-link-btn):hover,
    #onetrust-pc-sdk :not(.ot-leg-btn-container)>button:not(.ot-link-btn):focus,
    #ot-sdk-cookie-policy .ot-sdk-button:hover,
    #ot-sdk-cookie-policy :not(.ot-leg-btn-container)>button:not(.ot-link-btn):hover,
    #ot-sdk-cookie-policy :not(.ot-leg-btn-container)>button:not(.ot-link-btn):focus {
      color: #333;
      border-color: #888;
      opacity: .7
    }

    #onetrust-banner-sdk .ot-sdk-button:focus,
    #onetrust-banner-sdk :not(.ot-leg-btn-container)>button:focus,
    #onetrust-pc-sdk .ot-sdk-button:focus,
    #onetrust-pc-sdk :not(.ot-leg-btn-container)>button:focus,
    #ot-sdk-cookie-policy .ot-sdk-button:focus,
    #ot-sdk-cookie-policy :not(.ot-leg-btn-container)>button:focus {
      outline: 2px solid #000
    }

    #onetrust-banner-sdk .ot-sdk-button.ot-sdk-button-primary,
    #onetrust-banner-sdk button.ot-sdk-button-primary,
    #onetrust-banner-sdk input[type=submit].ot-sdk-button-primary,
    #onetrust-banner-sdk input[type=reset].ot-sdk-button-primary,
    #onetrust-banner-sdk input[type=button].ot-sdk-button-primary,
    #onetrust-pc-sdk .ot-sdk-button.ot-sdk-button-primary,
    #onetrust-pc-sdk button.ot-sdk-button-primary,
    #onetrust-pc-sdk input[type=submit].ot-sdk-button-primary,
    #onetrust-pc-sdk input[type=reset].ot-sdk-button-primary,
    #onetrust-pc-sdk input[type=button].ot-sdk-button-primary,
    #ot-sdk-cookie-policy .ot-sdk-button.ot-sdk-button-primary,
    #ot-sdk-cookie-policy button.ot-sdk-button-primary,
    #ot-sdk-cookie-policy input[type=submit].ot-sdk-button-primary,
    #ot-sdk-cookie-policy input[type=reset].ot-sdk-button-primary,
    #ot-sdk-cookie-policy input[type=button].ot-sdk-button-primary {
      color: #fff;
      background-color: #33c3f0;
      border-color: #33c3f0
    }

    #onetrust-banner-sdk .ot-sdk-button.ot-sdk-button-primary:hover,
    #onetrust-banner-sdk button.ot-sdk-button-primary:hover,
    #onetrust-banner-sdk input[type=submit].ot-sdk-button-primary:hover,
    #onetrust-banner-sdk input[type=reset].ot-sdk-button-primary:hover,
    #onetrust-banner-sdk input[type=button].ot-sdk-button-primary:hover,
    #onetrust-banner-sdk .ot-sdk-button.ot-sdk-button-primary:focus,
    #onetrust-banner-sdk button.ot-sdk-button-primary:focus,
    #onetrust-banner-sdk input[type=submit].ot-sdk-button-primary:focus,
    #onetrust-banner-sdk input[type=reset].ot-sdk-button-primary:focus,
    #onetrust-banner-sdk input[type=button].ot-sdk-button-primary:focus,
    #onetrust-pc-sdk .ot-sdk-button.ot-sdk-button-primary:hover,
    #onetrust-pc-sdk button.ot-sdk-button-primary:hover,
    #onetrust-pc-sdk input[type=submit].ot-sdk-button-primary:hover,
    #onetrust-pc-sdk input[type=reset].ot-sdk-button-primary:hover,
    #onetrust-pc-sdk input[type=button].ot-sdk-button-primary:hover,
    #onetrust-pc-sdk .ot-sdk-button.ot-sdk-button-primary:focus,
    #onetrust-pc-sdk button.ot-sdk-button-primary:focus,
    #onetrust-pc-sdk input[type=submit].ot-sdk-button-primary:focus,
    #onetrust-pc-sdk input[type=reset].ot-sdk-button-primary:focus,
    #onetrust-pc-sdk input[type=button].ot-sdk-button-primary:focus,
    #ot-sdk-cookie-policy .ot-sdk-button.ot-sdk-button-primary:hover,
    #ot-sdk-cookie-policy button.ot-sdk-button-primary:hover,
    #ot-sdk-cookie-policy input[type=submit].ot-sdk-button-primary:hover,
    #ot-sdk-cookie-policy input[type=reset].ot-sdk-button-primary:hover,
    #ot-sdk-cookie-policy input[type=button].ot-sdk-button-primary:hover,
    #ot-sdk-cookie-policy .ot-sdk-button.ot-sdk-button-primary:focus,
    #ot-sdk-cookie-policy button.ot-sdk-button-primary:focus,
    #ot-sdk-cookie-policy input[type=submit].ot-sdk-button-primary:focus,
    #ot-sdk-cookie-policy input[type=reset].ot-sdk-button-primary:focus,
    #ot-sdk-cookie-policy input[type=button].ot-sdk-button-primary:focus {
      color: #fff;
      background-color: #1eaedb;
      border-color: #1eaedb
    }

    #onetrust-banner-sdk input[type=text],
    #onetrust-pc-sdk input[type=text],
    #ot-sdk-cookie-policy input[type=text] {
      height: 38px;
      padding: 6px 10px;
      background-color: #fff;
      border: 1px solid #d1d1d1;
      border-radius: 4px;
      box-shadow: none;
      box-sizing: border-box
    }

    #onetrust-banner-sdk input[type=text],
    #onetrust-pc-sdk input[type=text],
    #ot-sdk-cookie-policy input[type=text] {
      -webkit-appearance: none;
      -moz-appearance: none;
      appearance: none
    }

    #onetrust-banner-sdk input[type=text]:focus,
    #onetrust-pc-sdk input[type=text]:focus,
    #ot-sdk-cookie-policy input[type=text]:focus {
      border: 1px solid #000;
      outline: 0
    }

    #onetrust-banner-sdk label,
    #onetrust-pc-sdk label,
    #ot-sdk-cookie-policy label {
      display: block;
      margin-bottom: .5rem;
      font-weight: 600
    }

    #onetrust-banner-sdk input[type=checkbox],
    #onetrust-pc-sdk input[type=checkbox],
    #ot-sdk-cookie-policy input[type=checkbox] {
      display: inline
    }

    #onetrust-banner-sdk ul,
    #onetrust-pc-sdk ul,
    #ot-sdk-cookie-policy ul {
      list-style: circle inside
    }

    #onetrust-banner-sdk ul,
    #onetrust-pc-sdk ul,
    #ot-sdk-cookie-policy ul {
      padding-left: 0;
      margin-top: 0
    }

    #onetrust-banner-sdk ul ul,
    #onetrust-pc-sdk ul ul,
    #ot-sdk-cookie-policy ul ul {
      margin: 1.5rem 0 1.5rem 3rem;
      font-size: 90%
    }

    #onetrust-banner-sdk li,
    #onetrust-pc-sdk li,
    #ot-sdk-cookie-policy li {
      margin-bottom: 1rem
    }

    #onetrust-banner-sdk th,
    #onetrust-banner-sdk td,
    #onetrust-pc-sdk th,
    #onetrust-pc-sdk td,
    #ot-sdk-cookie-policy th,
    #ot-sdk-cookie-policy td {
      padding: 12px 15px;
      text-align: left;
      border-bottom: 1px solid #e1e1e1
    }

    #onetrust-banner-sdk button,
    #onetrust-pc-sdk button,
    #ot-sdk-cookie-policy button {
      margin-bottom: 1rem;
      font-family: inherit
    }

    #onetrust-banner-sdk .ot-sdk-container:after,
    #onetrust-banner-sdk .ot-sdk-row:after,
    #onetrust-pc-sdk .ot-sdk-container:after,
    #onetrust-pc-sdk .ot-sdk-row:after,
    #ot-sdk-cookie-policy .ot-sdk-container:after,
    #ot-sdk-cookie-policy .ot-sdk-row:after {
      content: "";
      display: table;
      clear: both
    }

    #onetrust-banner-sdk .ot-sdk-row,
    #onetrust-pc-sdk .ot-sdk-row,
    #ot-sdk-cookie-policy .ot-sdk-row {
      margin: 0;
      max-width: none;
      display: block
    }

    #onetrust-banner-sdk {
      box-shadow: 0 0 18px rgba(0, 0, 0, .2)
    }

    #onetrust-banner-sdk.otFloatingRounded {
      position: fixed;
      background-color: #fff;
      width: 60%;
      max-width: 700px;
      z-index: 2147483645;
      border-radius: 2.5px;
      bottom: 2em;
      left: 1em;
      font-size: 16px;
      max-height: 90%;
      overflow-y: auto;
      overflow-x: hidden
    }

    #onetrust-banner-sdk.otRelFont {
      font-size: 1rem
    }

    #onetrust-banner-sdk.ot-bottom-left[dir=rtl] {
      right: 1em
    }

    #onetrust-banner-sdk.ot-bottom-right {
      left: auto;
      right: 1em
    }

    #onetrust-banner-sdk.ot-bottom-right[dir=rtl] {
      left: 1em;
      right: auto
    }

    #onetrust-banner-sdk .accept-btn-only {
      float: none !important;
      width: 100%
    }

    #onetrust-banner-sdk .cookie-settings-btn-only {
      width: 100%
    }

    #onetrust-banner-sdk::-webkit-scrollbar {
      width: 11px
    }

    #onetrust-banner-sdk::-webkit-scrollbar-thumb {
      border-radius: 10px;
      background: #c1c1c1
    }

    #onetrust-banner-sdk {
      scrollbar-arrow-color: #c1c1c1;
      scrollbar-darkshadow-color: #c1c1c1;
      scrollbar-face-color: #c1c1c1;
      scrollbar-shadow-color: #c1c1c1
    }

    #onetrust-banner-sdk h3 {
      margin-bottom: 0
    }

    #onetrust-banner-sdk h3,
    #onetrust-banner-sdk p {
      color: dimgray
    }

    #onetrust-banner-sdk .ot-sdk-container {
      padding: 0;
      width: 100%;
      margin: 0
    }

    #onetrust-banner-sdk #onetrust-policy {
      margin-top: 40px
    }

    #onetrust-banner-sdk .ot-optout-signal {
      margin: 0 1.875rem .625rem 1.875rem
    }

    #onetrust-banner-sdk #onetrust-policy-title,
    #onetrust-banner-sdk #onetrust-button-group {
      float: left
    }

    #onetrust-banner-sdk #onetrust-button-group-parent {
      padding: 10px 30px 4px 30px
    }

    #onetrust-banner-sdk .ot-gv-list-handler {
      padding: 0 30px 10px 30px;
      font-size: .812em;
      margin-bottom: 0;
      border: 0;
      line-height: normal;
      height: auto;
      width: auto
    }

    #onetrust-banner-sdk #onetrust-policy-text,
    #onetrust-banner-sdk .ot-b-addl-desc {
      padding: 0 30px 10px 30px;
      clear: both;
      font-size: .813em;
      line-height: 1.5;
      margin: 0
    }

    #onetrust-banner-sdk #onetrust-policy-text>*,
    #onetrust-banner-sdk #onetrust-policy-text a,
    #onetrust-banner-sdk .ot-b-addl-desc>*,
    #onetrust-banner-sdk .ot-b-addl-desc a {
      margin-bottom: 0px;
      font-size: inherit;
      line-height: inherit
    }

    #onetrust-banner-sdk #onetrust-policy-text a,
    #onetrust-banner-sdk .ot-b-addl-desc a {
      font-weight: bold
    }

    #onetrust-banner-sdk .ot-b-addl-desc {
      display: block
    }

    #onetrust-banner-sdk .ot-dpd-desc>.ot-b-addl-desc {
      padding: 0;
      margin-top: 10px;
      margin-bottom: 10px;
      line-height: 1.5;
      font-size: 1em
    }

    #onetrust-banner-sdk #onetrust-policy-title {
      display: inline-block;
      max-width: calc(100% - 75px);
      padding-left: 30px;
      font-size: 1em;
      line-height: 1.5;
      padding-bottom: 10px
    }

    #onetrust-banner-sdk #onetrust-button-group {
      float: right
    }

    #onetrust-banner-sdk .onetrust-banner-options {
      float: right;
      max-width: 55%
    }

    #onetrust-banner-sdk #onetrust-accept-btn-handler,
    #onetrust-banner-sdk #onetrust-reject-all-handler,
    #onetrust-banner-sdk #onetrust-pc-btn-handler {
      background-color: #68b631;
      color: #fff;
      border-color: #68b631;
      margin-right: 1em;
      min-width: 130px;
      height: auto;
      white-space: normal;
      word-break: break-word;
      word-wrap: break-word;
      padding: 12px 10px;
      line-height: 1.2;
      font-weight: 600;
      font-size: .813em
    }

    #onetrust-banner-sdk #onetrust-accept-btn-handler {
      margin-right: 0
    }

    #onetrust-banner-sdk #onetrust-pc-btn-handler {
      border: 1px solid #68b631;
      min-width: 175px;
      max-width: 40%
    }

    #onetrust-banner-sdk #onetrust-pc-btn-handler.cookie-setting-link {
      background-color: #fff;
      border: none;
      color: #68b631;
      text-decoration: underline;
      margin-right: 1rem
    }

    #onetrust-banner-sdk .has-reject-all-button #onetrust-button-group {
      width: 100%
    }

    #onetrust-banner-sdk .has-reject-all-button #onetrust-pc-btn-handler.cookie-setting-link {
      text-align: left;
      padding-left: 0;
      padding-right: 0
    }

    #onetrust-banner-sdk #onetrust-close-btn-container {
      right: 20px;
      top: 10px;
      position: absolute
    }

    #onetrust-banner-sdk .ot-close-icon {
      height: 44px;
      width: 44px;
      background-size: 12px;
      margin: -10px -10px 0 0;
      display: inline-block;
      border: none;
      padding: 0px
    }

    #onetrust-banner-sdk .banner_logo {
      display: none
    }

    #onetrust-banner-sdk.ot-bnr-w-logo #onetrust-policy {
      margin-top: 75px
    }

    #onetrust-banner-sdk.ot-bnr-w-logo .ot-bnr-logo {
      position: absolute;
      top: 10px
    }

    #onetrust-banner-sdk #banner-options {
      float: left;
      padding: 0 30px 0 30px;
      width: calc(100% - 60px)
    }

    #onetrust-banner-sdk .banner-option-input {
      cursor: pointer;
      width: auto;
      height: auto;
      border: none;
      padding: 0;
      padding-right: 3px;
      margin: 0 0 6px;
      font-size: .82em;
      line-height: 1.4
    }

    #onetrust-banner-sdk .banner-option-input * {
      pointer-events: none;
      font-size: inherit;
      line-height: inherit
    }

    #onetrust-banner-sdk .banner-option-input[aria-expanded=true] .ot-arrow-container {
      transform: rotate(90deg)
    }

    #onetrust-banner-sdk .banner-option-input[aria-expanded=true]~.banner-option-details {
      height: auto;
      display: block
    }

    #onetrust-banner-sdk .banner-option-header {
      cursor: pointer;
      display: inline-block
    }

    #onetrust-banner-sdk .banner-option-header :first-child {
      color: dimgray;
      font-weight: bold;
      float: left
    }

    #onetrust-banner-sdk .banner-option {
      margin-bottom: 10px
    }

    #onetrust-banner-sdk .ot-arrow-container,
    #onetrust-banner-sdk .banner-option-details {
      transition: all 300ms ease-in 0s;
      -webkit-transition: all 300ms ease-in 0s;
      -moz-transition: all 300ms ease-in 0s;
      -o-transition: all 300ms ease-in 0s
    }

    #onetrust-banner-sdk .ot-arrow-container {
      display: inline-block;
      border-top: 6px solid rgba(0, 0, 0, 0);
      border-bottom: 6px solid rgba(0, 0, 0, 0);
      border-left: 6px solid dimgray;
      margin-left: 10px;
      vertical-align: middle
    }

    #onetrust-banner-sdk .banner-option-details {
      display: none;
      font-size: .83em;
      line-height: 1.5;
      height: 0px;
      padding: 10px 10px 5px 10px
    }

    #onetrust-banner-sdk .banner-option-details * {
      font-size: inherit;
      line-height: inherit;
      color: dimgray
    }

    #onetrust-banner-sdk .ot-dpd-container {
      float: left;
      padding: 0 30px 0 30px;
      clear: both
    }

    #onetrust-banner-sdk .ot-dpd-title {
      font-weight: bold;
      padding-bottom: 10px;
      line-height: 1.4;
      font-size: 1em
    }

    #onetrust-banner-sdk .ot-dpd-desc {
      font-size: .813em;
      line-height: 1.5
    }

    #onetrust-banner-sdk .ot-dpd-desc .onetrust-vendors-list-handler {
      display: block;
      margin-left: 0px;
      margin-top: 5px;
      padding: 0;
      margin-bottom: 0;
      border: 0;
      line-height: normal;
      height: auto;
      width: auto
    }

    #onetrust-banner-sdk #onetrust-policy-text a {
      margin-left: 5px
    }

    #onetrust-banner-sdk :not(.ot-dpd-desc)>.ot-b-addl-desc {
      font-size: .813em;
      line-height: 1.5;
      margin: 0;
      padding-bottom: 10px
    }

    #onetrust-banner-sdk.ot-close-btn-link #onetrust-close-btn-container {
      top: 15px;
      transform: none;
      right: 15px
    }

    #onetrust-banner-sdk.ot-close-btn-link #onetrust-close-btn-container button {
      padding: 0;
      white-space: pre-wrap;
      border: none;
      height: auto;
      line-height: 1.5;
      text-decoration: underline;
      font-size: .69em
    }

    #onetrust-banner-sdk.ot-close-btn-link.ot-wo-title #onetrust-group-container {
      margin-top: 20px
    }

    @media only screen and (max-width: 425px) {
      #onetrust-banner-sdk #onetrust-button-group {
        margin-top: 0
      }

      #onetrust-banner-sdk button {
        padding: 0 20px
      }

      #onetrust-banner-sdk #onetrust-close-btn-container {
        right: 15px
      }

      #onetrust-banner-sdk #onetrust-policy-title {
        padding-left: 15px
      }

      #onetrust-banner-sdk #onetrust-button-group-parent,
      #onetrust-banner-sdk .ot-dpd-container {
        padding: 0 15px 0px 15px
      }

      #onetrust-banner-sdk #onetrust-policy-text,
      #onetrust-banner-sdk .ot-gv-list-handler,
      #onetrust-banner-sdk :not(.ot-dpd-desc)>.ot-b-addl-desc {
        padding: 0 15px 10px 15px
      }

      #onetrust-banner-sdk .ot-optout-signal {
        margin: 0 1.875rem .625rem 1.875rem
      }

      #onetrust-banner-sdk #ot-gv-link-ctnr {
        margin-bottom: 15px
      }

      #onetrust-banner-sdk #onetrust-button-group button {
        width: 100%
      }

      #onetrust-banner-sdk #banner-options {
        padding: 0 15px 0 15px;
        width: calc(100% - 30px)
      }

      #onetrust-banner-sdk .banner-option {
        margin-bottom: 6px
      }

      #onetrust-banner-sdk #onetrust-button-group,
      #onetrust-banner-sdk .onetrust-banner-options {
        width: 100%
      }

      #onetrust-banner-sdk #onetrust-pc-btn-handler {
        margin-right: 0px
      }

      #onetrust-banner-sdk.otFloatingRounded,
      #onetrust-banner-sdk.otFloatingRounded[dir=rtl] {
        left: 0;
        bottom: 0;
        width: 100%;
        right: 0
      }
    }

    @media only screen and (max-width: 550px) {
      #onetrust-banner-sdk .ot-close-icon {
        padding: 0
      }

      #onetrust-banner-sdk.ot-close-btn-link #onetrust-group-container {
        margin-top: 20px
      }
    }

    @media only screen and (min-width: 426px)and (max-width: 896px) {
      #onetrust-banner-sdk.otFloatingRounded {
        width: 95%
      }

      #onetrust-banner-sdk #onetrust-button-group-parent {
        width: 100%
      }
    }

    @media only screen and (max-width: 640px) {
      #onetrust-banner-sdk #onetrust-button-group {
        text-align: center
      }

      #onetrust-banner-sdk .onetrust-banner-options,
      #onetrust-banner-sdk #onetrust-pc-btn-handler {
        max-width: 100%;
        display: inline-block;
        text-align: center
      }

      #onetrust-banner-sdk .has-reject-all-button .onetrust-banner-options {
        width: 100%
      }

      #onetrust-banner-sdk .has-reject-all-button #onetrust-pc-btn-handler.cookie-setting-link {
        text-align: center
      }
    }

    #onetrust-consent-sdk #onetrust-banner-sdk {
      background-color: #26282d;
    }

    #onetrust-consent-sdk #onetrust-policy-title,
    #onetrust-consent-sdk #onetrust-policy-text,
    #onetrust-consent-sdk .ot-b-addl-desc,
    #onetrust-consent-sdk .ot-dpd-desc,
    #onetrust-consent-sdk .ot-dpd-title,
    #onetrust-consent-sdk #onetrust-policy-text *:not(.onetrust-vendors-list-handler),
    #onetrust-consent-sdk .ot-dpd-desc *:not(.onetrust-vendors-list-handler),
    #onetrust-consent-sdk #onetrust-banner-sdk #banner-options *,
    #onetrust-banner-sdk .ot-cat-header,
    #onetrust-banner-sdk .ot-optout-signal {
      color: #FFFFFF;
    }

    #onetrust-consent-sdk #onetrust-banner-sdk .banner-option-details {
      background-color: #26282d;
    }

    #onetrust-consent-sdk #onetrust-banner-sdk a[href],
    #onetrust-consent-sdk #onetrust-banner-sdk a[href] font,
    #onetrust-consent-sdk #onetrust-banner-sdk .ot-link-btn {
      color: #FFFFFF;
    }

    #onetrust-consent-sdk #onetrust-accept-btn-handler,
    #onetrust-banner-sdk #onetrust-reject-all-handler {
      background-color: #FFFFFF;
      border-color: #FFFFFF;
      color: #000000;
    }

    #onetrust-consent-sdk #onetrust-banner-sdk *:focus,
    #onetrust-consent-sdk #onetrust-banner-sdk:focus {
      outline-color: #00FFFF;
      outline-width: 1px;
    }

    #onetrust-consent-sdk #onetrust-pc-btn-handler,
    #onetrust-consent-sdk #onetrust-pc-btn-handler.cookie-setting-link {
      color: #000000;
      border-color: #000000;
      background-color:
        #FFFFFF;
    }

    body #onetrust-consent-sdk a:hover {
      color: currentColor;
      text-decoration: underline
    }

    body #onetrust-consent-sdk em {
      font-style: italic
    }

    body #onetrust-consent-sdk #onetrust-policy-title,
    body #onetrust-consent-sdk #onetrust-policy-text,
    body #onetrust-consent-sdk .ot-b-addl-desc,
    body #onetrust-consent-sdk .ot-dpd-desc,
    body #onetrust-consent-sdk .ot-dpd-title,
    body #onetrust-consent-sdk .ot-dpd-desc :not(.onetrust-vendors-list-handler),
    body #onetrust-consent-sdk #onetrust-policy-text :not(.onetrust-vendors-list-handler),
    body #onetrust-consent-sdk #banner-options *,
    body #onetrust-consent-sdk .ot-sdk-button,
    body #onetrust-consent-sdk button,
    body #onetrust-consent-sdk #onetrust-pc-sdk .ot-sdk-button,
    body #onetrust-consent-sdk #onetrust-pc-sdk button,
    body #onetrust-consent-sdk #onetrust-pc-sdk #ot-host-lst .ot-host-name,
    body #onetrust-consent-sdk #onetrust-pc-sdk #ot-host-lst .ot-host-name a,
    body #onetrust-consent-sdk #ot-sdk-cookie-policy .ot-sdk-button,
    body #onetrust-consent-sdk #ot-sdk-cookie-policy button,
    body #onetrust-consent-sdk .ot-cat-header {
      color: #fff
    }

    body #onetrust-consent-sdk #onetrust-banner-sdk {
      background-color: #26282d;
      box-shadow: 0 0 10px 0 rgba(0, 0, 0, .5)
    }

    body #onetrust-consent-sdk #onetrust-banner-sdk.ot-fade-in {
      animation-duration: .2s;
      animation-timing-function: cubic-bezier(0, 0, 0.1, 1)
    }

    @media(min-width: 426px) {

      body #onetrust-consent-sdk #onetrust-banner-sdk.otFloatingRounded,
      body #onetrust-consent-sdk #onetrust-banner-sdk.otFloatingRoundedCorner {
        border-radius: 6px;
        bottom: 3em;
        left: 3em;
        right: 3em;
        width: auto
      }
    }

    body #onetrust-consent-sdk #onetrust-banner-sdk.ot-wo-title #onetrust-group-container {
      margin-top: 0
    }

    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-policy {
      margin-bottom: 20px;
      margin-top: 30px
    }

    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-policy #onetrust-policy-text {
      padding-bottom: 0
    }

    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-policy #onetrust-policy-text h3 {
      font-size: 1.5em;
      margin: 0 0 15px
    }

    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-policy #onetrust-policy-text>* {
      margin: 0 0 1em
    }

    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-policy #onetrust-policy-text a {
      font-weight: normal;
      margin: 0
    }

    @media(max-width: 425px) {
      body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-policy {
        margin-top: 20px
      }

      body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-policy #onetrust-policy-text {
        max-height: 200px;
        overflow: auto
      }

      body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-policy #onetrust-policy-text .scroll-overlay {
        background: linear-gradient(to top, #26282d 0, transparent 100%);
        bottom: -10px;
        height: 50px;
        margin-bottom: 0;
        position: sticky
      }
    }

    body #onetrust-consent-sdk #onetrust-banner-sdk a[href],
    body #onetrust-consent-sdk #onetrust-banner-sdk a[href] font,
    body #onetrust-consent-sdk #onetrust-banner-sdk .ot-link-btn {
      color: currentColor
    }

    body #onetrust-consent-sdk #onetrust-banner-sdk .onetrust-banner-options {
      text-align: center
    }

    @media(max-width: 640px) {
      body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-button-group {
        width: 100%
      }

      body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-button-group button {
        width: 100%
      }

      body #onetrust-consent-sdk #onetrust-banner-sdk .onetrust-banner-options {
        display: flex;
        flex-direction: column;
        width: 100%
      }

      body #onetrust-consent-sdk #onetrust-banner-sdk .onetrust-banner-options #onetrust-accept-btn-handler {
        order: 1
      }

      body #onetrust-consent-sdk #onetrust-banner-sdk .onetrust-banner-options #onetrust-reject-all-handler {
        order: 2
      }
    }

    body #onetrust-consent-sdk #onetrust-banner-sdk *:focus-visible {
      outline-offset: 2px;
      outline: 1px solid aqua
    }

    body #onetrust-consent-sdk #onetrust-banner-sdk:focus,
    body #onetrust-consent-sdk #onetrust-banner-sdk:focus-visible {
      outline-offset: 2px;
      outline: 1px solid rgba(255, 255, 255, .3)
    }

    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-button-group-parent {
      font-size: 16px
    }

    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-accept-btn-handler,
    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-reject-all-handler,
    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-pc-btn-handler {
      background-color: rgba(0, 0, 0, 0);
      border-radius: 6px;
      border: 1px solid rgba(0, 0, 0, 0);
      color: #fff;
      cursor: pointer;
      position: relative;
      z-index: 0
    }

    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-accept-btn-handler::before,
    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-reject-all-handler::before,
    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-pc-btn-handler::before {
      content: "";
      border-radius: inherit;
      position: absolute;
      z-index: -1;
      top: -1px;
      left: -1px;
      height: calc(100% + 2px);
      width: calc(100% + 2px);
      transition: background-color .25s cubic-bezier(0.4, 0, 1, 1), opacity .25s cubic-bezier(0.4, 0, 1, 1)
    }

    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-accept-btn-handler:focus,
    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-accept-btn-handler:hover,
    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-reject-all-handler:focus,
    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-reject-all-handler:hover,
    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-pc-btn-handler:focus,
    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-pc-btn-handler:hover {
      opacity: 1
    }

    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-accept-btn-handler:focus::before,
    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-accept-btn-handler:hover::before,
    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-reject-all-handler:focus::before,
    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-reject-all-handler:hover::before,
    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-pc-btn-handler:focus::before,
    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-pc-btn-handler:hover::before {
      opacity: .8
    }

    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-accept-btn-handler:disabled,
    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-accept-btn-handler.disabled,
    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-reject-all-handler:disabled,
    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-reject-all-handler.disabled,
    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-pc-btn-handler:disabled,
    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-pc-btn-handler.disabled {
      color: #26282d
    }

    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-accept-btn-handler:disabled::before,
    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-accept-btn-handler.disabled::before,
    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-reject-all-handler:disabled::before,
    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-reject-all-handler.disabled::before,
    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-pc-btn-handler:disabled::before,
    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-pc-btn-handler.disabled::before {
      background-color: #a9a9a9;
      opacity: 1
    }

    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-accept-btn-handler:disabled:focus,
    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-accept-btn-handler:disabled:hover,
    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-accept-btn-handler.disabled:focus,
    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-accept-btn-handler.disabled:hover,
    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-reject-all-handler:disabled:focus,
    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-reject-all-handler:disabled:hover,
    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-reject-all-handler.disabled:focus,
    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-reject-all-handler.disabled:hover,
    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-pc-btn-handler:disabled:focus,
    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-pc-btn-handler:disabled:hover,
    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-pc-btn-handler.disabled:focus,
    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-pc-btn-handler.disabled:hover {
      color: #26282d
    }

    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-accept-btn-handler:disabled:focus::before,
    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-accept-btn-handler:disabled:hover::before,
    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-accept-btn-handler.disabled:focus::before,
    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-accept-btn-handler.disabled:hover::before,
    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-reject-all-handler:disabled:focus::before,
    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-reject-all-handler:disabled:hover::before,
    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-reject-all-handler.disabled:focus::before,
    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-reject-all-handler.disabled:hover::before,
    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-pc-btn-handler:disabled:focus::before,
    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-pc-btn-handler:disabled:hover::before,
    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-pc-btn-handler.disabled:focus::before,
    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-pc-btn-handler.disabled:hover::before {
      opacity: 1
    }

    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-accept-btn-handler,
    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-reject-all-handler {
      color: #26282d;
      border-color: #fff
    }

    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-accept-btn-handler::before,
    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-reject-all-handler::before {
      content: "";
      background-color: #fff
    }

    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-pc-btn-handler {
      border-color: currentColor;
      max-width: none
    }

    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-pc-btn-handler::before {
      background-color: #fff;
      opacity: 0
    }

    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-pc-btn-handler:focus::before,
    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-pc-btn-handler:hover::before {
      background-color: #fff;
      opacity: .2
    }

    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-pc-btn-handler:disabled,
    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-pc-btn-handler.disabled {
      color: currentColor !important
    }

    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-pc-btn-handler:disabled::before,
    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-pc-btn-handler.disabled::before {
      opacity: 0 !important
    }

    body #onetrust-consent-sdk #onetrust-banner-sdk #onetrust-close-btn-container {
      display: none
    }

    .ot-sdk-cookie-policy {
      font-family: inherit;
      font-size: 16px
    }

    .ot-sdk-cookie-policy.otRelFont {
      font-size: 1rem
    }

    .ot-sdk-cookie-policy h3,
    .ot-sdk-cookie-policy h4,
    .ot-sdk-cookie-policy h6,
    .ot-sdk-cookie-policy p,
    .ot-sdk-cookie-policy li,
    .ot-sdk-cookie-policy a,
    .ot-sdk-cookie-policy th,
    .ot-sdk-cookie-policy #cookie-policy-description,
    .ot-sdk-cookie-policy .ot-sdk-cookie-policy-group,
    .ot-sdk-cookie-policy #cookie-policy-title {
      color: dimgray
    }

    .ot-sdk-cookie-policy #cookie-policy-description {
      margin-bottom: 1em
    }

    .ot-sdk-cookie-policy h4 {
      font-size: 1.2em
    }

    .ot-sdk-cookie-policy h6 {
      font-size: 1em;
      margin-top: 2em
    }

    .ot-sdk-cookie-policy th {
      min-width: 75px
    }

    .ot-sdk-cookie-policy a,
    .ot-sdk-cookie-policy a:hover {
      background: #fff
    }

    .ot-sdk-cookie-policy thead {
      background-color: #f6f6f4;
      font-weight: bold
    }

    .ot-sdk-cookie-policy .ot-mobile-border {
      display: none
    }

    .ot-sdk-cookie-policy section {
      margin-bottom: 2em
    }

    .ot-sdk-cookie-policy table {
      border-collapse: inherit
    }

    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy {
      font-family: inherit;
      font-size: 1rem
    }

    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy h3,
    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy h4,
    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy h6,
    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy p,
    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy li,
    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy a,
    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy th,
    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy #cookie-policy-description,
    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy .ot-sdk-cookie-policy-group,
    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy #cookie-policy-title {
      color: dimgray
    }

    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy #cookie-policy-description {
      margin-bottom: 1em
    }

    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy .ot-sdk-subgroup {
      margin-left: 1.5em
    }

    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy #cookie-policy-description,
    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy .ot-sdk-cookie-policy-group-desc,
    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy .ot-table-header,
    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy a,
    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy span,
    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy td {
      font-size: .9em
    }

    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy td span,
    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy td a {
      font-size: inherit
    }

    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy .ot-sdk-cookie-policy-group {
      font-size: 1em;
      margin-bottom: .6em
    }

    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy .ot-sdk-cookie-policy-title {
      margin-bottom: 1.2em
    }

    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy>section {
      margin-bottom: 1em
    }

    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy th {
      min-width: 75px
    }

    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy a,
    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy a:hover {
      background: #fff
    }

    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy thead {
      background-color: #f6f6f4;
      font-weight: bold
    }

    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy .ot-mobile-border {
      display: none
    }

    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy section {
      margin-bottom: 2em
    }

    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy .ot-sdk-subgroup ul li {
      list-style: disc;
      margin-left: 1.5em
    }

    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy .ot-sdk-subgroup ul li h4 {
      display: inline-block
    }

    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table {
      border-collapse: inherit;
      margin: auto;
      border: 1px solid #d7d7d7;
      border-radius: 5px;
      border-spacing: initial;
      width: 100%;
      overflow: hidden
    }

    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table th,
    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table td {
      border-bottom: 1px solid #d7d7d7;
      border-right: 1px solid #d7d7d7
    }

    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table tr:last-child td {
      border-bottom: 0px
    }

    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table tr th:last-child,
    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table tr td:last-child {
      border-right: 0px
    }

    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table .ot-host,
    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table .ot-cookies-type {
      width: 25%
    }

    .ot-sdk-cookie-policy[dir=rtl] {
      text-align: left
    }

    #ot-sdk-cookie-policy h3 {
      font-size: 1.5em
    }

    @media only screen and (max-width: 530px) {

      .ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) table,
      .ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) thead,
      .ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) tbody,
      .ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) th,
      .ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) td,
      .ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) tr {
        display: block
      }

      .ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) thead tr {
        position: absolute;
        top: -9999px;
        left: -9999px
      }

      .ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) tr {
        margin: 0 0 1em 0
      }

      .ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) tr:nth-child(odd),
      .ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) tr:nth-child(odd) a {
        background: #f6f6f4
      }

      .ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) td {
        border: none;
        border-bottom: 1px solid #eee;
        position: relative;
        padding-left: 50%
      }

      .ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) td:before {
        position: absolute;
        height: 100%;
        left: 6px;
        width: 40%;
        padding-right: 10px
      }

      .ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) .ot-mobile-border {
        display: inline-block;
        background-color: #e4e4e4;
        position: absolute;
        height: 100%;
        top: 0;
        left: 45%;
        width: 2px
      }

      .ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) td:before {
        content: attr(data-label);
        font-weight: bold
      }

      .ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) li {
        word-break: break-word;
        word-wrap: break-word
      }

      #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table {
        overflow: hidden
      }

      #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table td {
        border: none;
        border-bottom: 1px solid #d7d7d7
      }

      #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table,
      #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy thead,
      #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy tbody,
      #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy th,
      #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy td,
      #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy tr {
        display: block
      }

      #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table .ot-host,
      #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table .ot-cookies-type {
        width: auto
      }

      #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy tr {
        margin: 0 0 1em 0
      }

      #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy td:before {
        height: 100%;
        width: 40%;
        padding-right: 10px
      }

      #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy td:before {
        content: attr(data-label);
        font-weight: bold
      }

      #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy li {
        word-break: break-word;
        word-wrap: break-word
      }

      #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy thead tr {
        position: absolute;
        top: -9999px;
        left: -9999px;
        z-index: -9999
      }

      #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table tr:last-child td {
        border-bottom: 1px solid #d7d7d7;
        border-right: 0px
      }

      #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table tr:last-child td:last-child {
        border-bottom: 0px
      }
    }

    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy h5,
    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy h6,
    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy li,
    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy p,
    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy a,
    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy span,
    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy td,
    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy #cookie-policy-description {
      color: #696969;
    }

    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy th {
      color: #696969;
    }

    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy .ot-sdk-cookie-policy-group {
      color: #696969;
    }

    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy #cookie-policy-title {
      color: #696969;
    }


    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table th {
      background-color: #F8F8F8;
    }

    .ot-floating-button__front {
      background-image: url('https://cdn.cookielaw.org/logos/static/ot_persistent_cookie_icon.png')
    }
  </style>
  <script charset="utf-8" src="/browse/_nuxt/7415368.modern.js"></script>
  <script charset="utf-8" src="/browse/_nuxt/6b1552d.modern.js"></script>
  <script src="https://bat.bing.com/p/action/52008716.js" type="text/javascript" async="" data-ueto="ueto_4b4f9e0343"></script>
  <script charset="utf-8" src="https://analytics.tiktok.com/i18n/pixel/static/identify_7f4c1.js"></script>
  <script charset="utf-8" src="/browse/_nuxt/eef4d15.modern.js"></script>
  <script charset="utf-8" src="/browse/_nuxt/3557df3.modern.js"></script>
  <style type="text/css">
    @media(max-width:767px) {
      [data-v-6b9f4c2c] .modal-content {
        background: #26282d
      }
    }

    @media(min-width:768px) {
      [data-v-6b9f4c2c] .modal-inner {
        position: relative;
        padding: 4.5rem 3rem 3.75rem calc(40% + 3rem) !important
      }
    }

    @media(max-width:767px) {
      .modal-top[data-v-6b9f4c2c] {
        display: flex;
        align-items: center
      }
    }

    @media(max-width:767px) {
      .modal-main[data-v-6b9f4c2c] {
        margin-top: 1.5rem;
        clear: both
      }
    }

    .modal-title[data-v-6b9f4c2c] {
      letter-spacing: .1em
    }

    @media(max-width:767px) {
      .modal-title[data-v-6b9f4c2c] {
        margin-right: 2.25rem
      }
    }

    .modal-description[data-v-6b9f4c2c] {
      margin-bottom: .75rem;
      font-size: 36px;
      line-height: 45px
    }

    @media(min-width:768px) {
      .modal-description[data-v-6b9f4c2c] {
        margin: .75rem 0 1.5rem
      }
    }

    @media(min-width:992px) {
      .modal-description[data-v-6b9f4c2c] {
        font-size: 75px;
        line-height: 85px
      }
    }

    @media(min-width:768px) {
      .modal-cta[data-v-6b9f4c2c] {
        max-width: 300px
      }
    }

    @media(max-width:767px) {
      .modal-trial[data-v-6b9f4c2c] {
        width: 100%
      }
    }

    @media(min-width:768px) {
      .image-container[data-v-6b9f4c2c] {
        background: #000;
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        width: 40%
      }
    }

    @media(max-width:767px) {
      .image-container[data-v-6b9f4c2c] {
        margin-right: .75rem;
        float: left;
        width: 3rem
      }
    }

    .image-src[data-v-6b9f4c2c] {
      border-radius: 5px
    }

    @media(min-width:768px) {
      .image-src[data-v-6b9f4c2c] {
        filter: drop-shadow(2px 6px 30px rgba(0, 0, 0, .15));
        position: absolute;
        left: 15%;
        top: 50%;
        transform: translateY(-50%);
        width: 70%
      }
    }

    .image-bg[data-v-6b9f4c2c] {
      opacity: .2;
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      width: 100%
    }

    .list-style-custom[data-v-6b9f4c2c] svg {
      background: #26282d;
      border-radius: 100%
    }

    @media(max-width:767px) {
      .list-style-custom[data-v-6b9f4c2c] svg {
        background: #49494f
      }
    }

    @media(max-width:767px) {
      .list-item[data-v-6b9f4c2c] {
        color: #fff
      }
    }
  </style>
  <style type="text/css">
    .modal-wrapper[data-v-4c55d064] {
      background: rgba(0, 0, 0, .3);
      display: flex;
      position: fixed;
      top: -3.75rem;
      padding-top: 3.75rem;
      bottom: 0;
      left: 0;
      right: 0;
      z-index: 99
    }

    @media(max-width:991px) {
      .modal-wrapper[data-v-4c55d064] {
        z-index: 88
      }
    }

    .modal-close[data-v-4c55d064] {
      position: fixed;
      top: .75rem;
      right: .75rem;
      z-index: 1
    }

    @media(max-width:767px) {
      .modal-close[data-v-4c55d064] {
        top: 1.5rem
      }
    }

    .modal-close[data-v-4c55d064] svg {
      background: #26282d;
      transition: background .25s cubic-bezier(.39, .575, .565, 1);
      border-radius: 100%;
      padding: 4px
    }

    @media(max-width:767px) {
      .modal-close[data-v-4c55d064] svg {
        background: #49494f
      }
    }

    .modal-close[data-v-4c55d064]:focus svg,
    .modal-close[data-v-4c55d064]:hover svg {
      background: #49494f
    }

    .modal-content[data-v-4c55d064] {
      display: flex;
      flex-direction: column;
      background: #101012;
      margin: auto;
      position: relative;
      max-height: 100%;
      outline: 0;
      transform: translateZ(0)
    }

    .modal-content[data-v-4c55d064]:focus-visible {
      outline: 1px solid #0ff
    }

    @media(min-width:768px) {
      .modal-content[data-v-4c55d064] {
        border-radius: 10px;
        box-shadow: 0 4px 100px rgba(0, 0, 0, .5);
        max-height: 94%;
        max-width: 1076px;
        width: 94%
      }

      @supports((-webkit-backdrop-filter: blur(20px)) or (backdrop-filter: blur(20px))) or (-webkit-backdrop-filter:blur(20px)) {
        .is-blurred .modal-content[data-v-4c55d064] {
          background: rgba(16, 16, 18, .8);
          -webkit-backdrop-filter: blur(20px);
          backdrop-filter: blur(20px)
        }
      }
    }

    @media(max-width:767px) {
      .modal-content[data-v-4c55d064] {
        margin-bottom: 0;
        width: 100%
      }

      @supports(padding-bottom:constant(safe-area-inset-bottom)) {
        .modal-content[data-v-4c55d064] {
          padding-bottom: constant(safe-area-inset-bottom)
        }
      }

      @supports(padding-bottom:env(safe-area-inset-bottom)) {
        .modal-content[data-v-4c55d064] {
          padding-bottom: env(safe-area-inset-bottom)
        }
      }
    }

    .modal-inner[data-v-4c55d064] {
      padding: 1.5rem;
      max-height: 100vh
    }

    @media(min-width:768px) {
      .modal-inner[data-v-4c55d064] {
        padding: 4.5rem 3rem 3rem;
        max-height: 94vh
      }
    }
  </style>
</head>

<body>
  <noscript data-n-head="ssr" data-hid="gtm-noscript" data-pbody="true"><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-5XWHVHQ&" height="0" width="0" style="display:none;visibility:hidden" title="gtm"></iframe></noscript>
  <div id="__nuxt"><!---->
    <div id="__layout">
      <div class="min-screen flex flex-column"><!---->
        <header class="tidal-header-wrapper" data-v-fcb80d4c="">
          <div tabindex="-1" class="tidal-header flex theme-dark" data-v-fcb80d4c="">
            <div class="tidal-header-logo tidal-header-content" data-v-fcb80d4c=""><a href="/" data-v-fcb80d4c=""><svg viewBox="0 0 160 22" fill="#fff" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" class="tidal-header-logo--image" data-v-fcb80d4c="">
                  <path d="M58.2177 21.1555H54.1108V6.58787H48.7992V3.06799H63.5293V6.58787H58.2177V21.1555Z" data-v-fcb80d4c=""></path>
                  <path d="M75.819 21.1553H79.9501V3.06799H75.819V21.1553Z" data-v-fcb80d4c=""></path>
                  <path d="M152.855 17.5861H160V21.1555H148.724V3.06799H152.855V17.5861Z" data-v-fcb80d4c=""></path>
                  <path d="M97.7115 17.6108H100.273C103.72 17.6108 106.018 15.4605 106.018 12.0382C106.018 8.78718 103.696 6.58813 100.371 6.58813H97.7115V17.6108ZM93.7028 3.06799H100.395C105.333 3.06799 110.247 5.68264 110.247 12.0626C110.247 18.0761 105.431 21.1551 100.64 21.1551L93.7028 21.1553V3.06799Z" data-v-fcb80d4c=""></path>
                  <path d="M125.334 14.375L128.058 7.30083L130.757 14.375H125.334ZM133.175 21.1555H137.715L130.264 3.06799H125.999L118.475 21.1555H122.894L124.227 17.5865H131.853L133.175 21.1555Z" data-v-fcb80d4c=""></path>
                  <path d="M21.1775 5.70464L15.8834 10.9992L10.589 5.70464L15.8834 0.411743L21.1775 5.70464Z" data-v-fcb80d4c=""></path>
                  <path d="M21.1775 16.2936L15.8834 21.5882L10.589 16.2936L15.8834 10.9993L21.1775 16.2936Z" data-v-fcb80d4c=""></path>
                  <path d="M10.5886 5.70506L5.29417 10.9997L0 5.70506L5.29417 0.411743L10.5886 5.70506Z" data-v-fcb80d4c=""></path>
                  <path d="M31.7655 5.70506L26.4715 10.9997L21.1769 5.70506L26.4715 0.411743L31.7655 5.70506Z" data-v-fcb80d4c=""></path>
                </svg> <span class="sr-only" data-v-fcb80d4c="">TIDAL</span></a></div>
            <div class="accessibility-menu font-size-client" data-v-fcb80d4c=""><a data-v-fcb80d4c="" href="#main-content" class="accessibility-item visible-offset-0 primary-hover block">Skip to main content</a> <a data-v-fcb80d4c="" href="#footer-content" class="accessibility-item visible-offset-0 primary-hover block">Skip to footer</a> <a data-v-fcb80d4c="" href="#language-switcher" class="accessibility-item visible-offset-0 primary-hover block">Skip to language switcher</a></div>
            <nav aria-label="Primary Navigation" class="tidal-header-content flex align-center position-relative hidden-lg-max" data-v-fcb80d4c="">
              <div data-v-fcb80d4c=""><button id="about-button" data-haspopup="menu" aria-expanded="false" aria-controls="about_sub_menu" class="tidal-header-sub-nav-link header-btn btn-link btn-medium border-radius font-weight-bold" data-v-fcb80d4c="">
                  About
                </button>
                <ul id="about_sub_menu" class="tidal-header-sub-content list-style-none" data-v-fcb80d4c="">
                  <li class="margin-0" data-v-fcb80d4c=""><a href="/about" class="btn-link btn-medium visible-offset-0 font-weight-normal" data-v-fcb80d4c="">
                      What is TIDAL?
                    </a></li>
                  <li class="margin-0" data-v-fcb80d4c=""><a href="/explore-the-app" class="btn-link btn-medium visible-offset-0 font-weight-normal" data-v-fcb80d4c="">
                      Explore the App
                    </a></li>
                  <li class="margin-0" data-v-fcb80d4c=""><a href="/formusic" class="btn-link btn-medium visible-offset-0 font-weight-normal" data-v-fcb80d4c="">
                      For Music
                    </a></li>
                  <li class="margin-0" data-v-fcb80d4c=""><a href="/forartists" class="btn-link btn-medium visible-offset-0 font-weight-normal" data-v-fcb80d4c="">
                      For Artists
                    </a></li>
                  <li class="margin-0" data-v-fcb80d4c=""><a href="/culture" class="btn-link btn-medium visible-offset-0 font-weight-normal" data-v-fcb80d4c="">
                      Culture
                    </a></li>
                  <li class="margin-0" data-v-fcb80d4c=""><a href="/magazine" class="btn-link btn-medium visible-offset-0 font-weight-normal" data-v-fcb80d4c="">
                      Magazine
                    </a></li>
                  <li class="margin-0" data-v-fcb80d4c=""><a href="/pricing" class="btn-link btn-medium visible-offset-0 font-weight-normal" data-v-fcb80d4c="">
                      Pricing &amp; Plans
                    </a></li>
                </ul>
              </div>
              <div data-v-fcb80d4c=""><button id="support-button" data-haspopup="menu" aria-expanded="false" aria-controls="support_sub_menu" class="tidal-header-sub-nav-link header-btn btn-link btn-medium border-radius font-weight-bold" data-v-fcb80d4c="">
                  Support
                </button>
                <ul id="support_sub_menu" class="tidal-header-sub-content list-style-none" data-v-fcb80d4c="">
                  <li class="margin-0" data-v-fcb80d4c=""><a href="/download" class="btn-link btn-medium visible-offset-0 font-weight-normal" data-v-fcb80d4c="">
                      Download TIDAL
                    </a></li>
                  <li class="margin-0" data-v-fcb80d4c=""><a href="/transfer-music" class="btn-link btn-medium visible-offset-0 font-weight-normal" data-v-fcb80d4c="">
                      Transfer Music
                    </a></li>
                  <li class="margin-0" data-v-fcb80d4c=""><a href="/supported-devices" class="btn-link btn-medium visible-offset-0 font-weight-normal" data-v-fcb80d4c="">
                      Supported Devices
                    </a></li>
                  <li class="margin-0" data-v-fcb80d4c=""><a href="/help" class="btn-link btn-medium visible-offset-0 font-weight-normal" data-v-fcb80d4c="">
                      Get Help
                    </a></li>
                </ul>
              </div>
            </nav>
            <div class="tidal-header-content position-relative hidden-md-max hidden-lg-max" data-v-fcb80d4c=""><button id="login-button" data-haspopup="menu" aria-controls="login_sub_menu" aria-expanded="false" class="tidal-header-sub-nav-link header-btn btn-link btn-medium border-radius font-weight-bold" data-v-fcb80d4c=""><i aria-hidden="true" class="tidal-icon" data-v-fcb80d4c=""><svg width="20" height="18" viewBox="0 0 20 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                      d="M9.99982 2.00876C8.11717 2.00876 6.34982 3.86753 6.34982 6.48376C6.34982 9.09999 8.11717 10.9588 9.99982 10.9588C11.8825 10.9588 13.6498 9.09999 13.6498 6.48376C13.6498 3.86753 11.8825 2.00876 9.99982 2.00876ZM4.57482 6.48376C4.57482 3.17674 6.87048 0.233765 9.99982 0.233765C13.1292 0.233765 15.4248 3.17674 15.4248 6.48376C15.4248 9.79079 13.1292 12.7338 9.99982 12.7338C6.87048 12.7338 4.57482 9.79079 4.57482 6.48376ZM4.86864 11.6946C5.18024 12.0729 5.12611 12.6323 4.74775 12.9439C3.4899 13.9797 2.62172 15.3686 2.33358 16.9173C2.24393 17.3992 1.78061 17.7172 1.29872 17.6275C0.81684 17.5379 0.498874 17.0746 0.588527 16.5927C0.960387 14.5939 2.07221 12.8478 3.61936 11.5737C3.99773 11.2621 4.55705 11.3162 4.86864 11.6946ZM16.4215 11.5737C16.0431 11.2621 15.4838 11.3162 15.1722 11.6946C14.8606 12.0729 14.9147 12.6323 15.2931 12.9439C16.5522 13.9807 17.4129 15.3641 17.6626 16.8976C17.7413 17.3814 18.1974 17.7097 18.6811 17.631C19.1649 17.5522 19.4933 17.0962 19.4145 16.6124C19.0866 14.5984 17.9674 12.8468 16.4215 11.5737Z"
                      fill="currentColor"></path>
                  </svg></i>
                Log in
                <!----></button>
              <ul id="login_sub_menu" class="tidal-header-sub-content list-style-none" data-v-fcb80d4c=""><!---->
                <li class="margin-0" data-v-fcb80d4c=""><a href="https://account.tidal.com" class="btn-link btn-medium fullwidth visible-offset-0 font-weight-normal" data-v-fcb80d4c="">
                    Manage Account
                  </a></li>
                <li class="margin-0" data-v-fcb80d4c=""><a href="https://listen.tidal.com/login?autoredirect=true&amp;lang=en" class="btn-link fullwidth btn-medium font-weight-normal visible-offset-0 hidden-lg-max player-url" data-v-fcb80d4c="">Web Player</a></li> <!----> <!---->
              </ul>
            </div>
            <div class="tidal-header-content position-relative margin-left-1 hidden-lg-max" data-v-fcb80d4c=""><a href="/try-now" class="tidal-header-button btn-secondary btn-medium" data-v-fcb80d4c=""><span data-v-fcb80d4c="" class="">Start Free Trial</span> <!----></a></div>
            <div class="tidal-header-content position-relative hidden-lg-min margin-left-nudge" data-v-fcb80d4c=""><button aria-label="Main Menu" aria-controls="tidalHeaderMainMenu" class="btn-link btn-medium current-hover padding-0" data-v-fcb80d4c=""><span class="font-size-medium text-align-medium" data-v-fcb80d4c=""><i class="tidal-icon tidal-icon--stroke-only inline-block margin-0" data-v-fcb80d4c=""><svg viewBox="0 0 48 48" data-v-fcb80d4c="">
                      <line transform="" x1="6" y1="38" x2="42" y2="38" stroke-miterlimit="10" stroke-width="3" class="tidal-header-burger tidal-header-burger-1" data-v-fcb80d4c=""></line>
                      <line transform="" x1="6" y1="10" x2="42" y2="10" stroke-miterlimit="10" stroke-width="3" class="tidal-header-burger tidal-header-burger-2" data-v-fcb80d4c=""></line>
                      <line opacity="1" x1="6" y1="24" x2="42" y2="24" stroke-miterlimit="10" stroke-width="3" class="tidal-header-burger tidal-header-burger-3" data-v-fcb80d4c=""></line>
                    </svg></i></span></button></div>
          </div>
          <div id="tidalHeaderMainMenu" role="menu" class="tidal-header-main-menu theme-dark hidden-lg-min" style="display:none;" data-v-fcb80d4c="">
            <div class="flex flex-column max-screen-h" data-v-fcb80d4c="">
              <div class="overflow-scroll overflow-contain font-size-medium" data-v-fcb80d4c="">
                <div class="fullwidth overflow-hidden padding-1 inline-block text-left" data-v-fcb80d4c="">
                  <div data-v-fcb80d4c="">
                    <ul class="list-style-none text-dec-none margin-bottom-0" data-v-fcb80d4c="">
                      <div class="tidal-accordion padding-right-0 padding-left-0" data-v-3bb938c8="" data-v-fcb80d4c="">
                        <div class="title-wrapper flex flex-nowrap align-center" data-v-3bb938c8=""> <button aria-expanded="true" class="title flex flex-nowrap align-center plain-button visible-offset-0 flex-grow padding-top-0" data-v-3bb938c8="" aria-controls="xmxnl"><!---->
                            <p class="flex-grow margin-0 font-weight-bold" data-v-3bb938c8="">About</p> <span aria-hidden="false" class="hidden" data-v-3bb938c8="">Click to close accordion</span>
                            <div class="icon is-active is-arrow margin-left-nudge" data-v-3bb938c8=""><span class="block font-size-medium" data-v-51a7f762="" data-v-3bb938c8=""><i class="tidal-icon" data-v-51a7f762=""><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" focusable="false" aria-hidden="true">
                                    <path d="M12 14.6L18.3 8.3 19.7 9.7 12 17.4 4.3 9.7 5.7 8.3z"></path>
                                  </svg></i></span></div>
                          </button></div>
                        <div class="content overflow-hidden" data-v-3bb938c8="" data-v-53026b1a="" id="xmxnl">
                          <div data-v-3bb938c8="" class="">
                            <li class="font-size-intermediate margin-0 margin-top-nudge margin-left-nudge" data-v-fcb80d4c=""><a href="/about" class="primary-hover fullwidth" data-v-fcb80d4c="">
                                What is TIDAL?
                              </a></li>
                            <li class="font-size-intermediate margin-0 margin-top-nudge margin-left-nudge" data-v-fcb80d4c=""><a href="/explore-the-app" class="primary-hover fullwidth" data-v-fcb80d4c="">
                                Explore the App
                              </a></li>
                            <li class="font-size-intermediate margin-0 margin-top-nudge margin-left-nudge" data-v-fcb80d4c=""><a href="/formusic" class="primary-hover fullwidth" data-v-fcb80d4c="">
                                For Music
                              </a></li>
                            <li class="font-size-intermediate margin-0 margin-top-nudge margin-left-nudge" data-v-fcb80d4c=""><a href="/forartists" class="primary-hover fullwidth" data-v-fcb80d4c="">
                                For Artists
                              </a></li>
                            <li class="font-size-intermediate margin-0 margin-top-nudge margin-left-nudge" data-v-fcb80d4c=""><a href="/culture" class="primary-hover fullwidth" data-v-fcb80d4c="">
                                Culture
                              </a></li>
                            <li class="font-size-intermediate margin-0 margin-top-nudge margin-left-nudge" data-v-fcb80d4c=""><a href="/magazine" class="primary-hover fullwidth" data-v-fcb80d4c="">
                                Magazine
                              </a></li>
                            <li class="font-size-intermediate margin-0 margin-top-nudge margin-left-nudge" data-v-fcb80d4c=""><a href="/pricing" class="primary-hover fullwidth" data-v-fcb80d4c="">
                                Pricing &amp; Plans
                              </a></li>
                          </div>
                        </div>
                      </div>
                      <div class="tidal-accordion padding-right-0 padding-left-0 margin-top-1" data-v-3bb938c8="" data-v-fcb80d4c="">
                        <div class="title-wrapper flex flex-nowrap align-center" data-v-3bb938c8=""> <button aria-expanded="false" class="title flex flex-nowrap align-center plain-button visible-offset-0 flex-grow padding-top-0" data-v-3bb938c8="" aria-controls="qggvh"><!---->
                            <p class="flex-grow margin-0 font-weight-bold" data-v-3bb938c8="">Support</p> <span aria-hidden="false" class="hidden" data-v-3bb938c8="">Click to expand accordion</span>
                            <div class="icon is-arrow margin-left-nudge" data-v-3bb938c8=""><span class="block font-size-medium" data-v-51a7f762="" data-v-3bb938c8=""><i class="tidal-icon" data-v-51a7f762=""><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" focusable="false" aria-hidden="true">
                                    <path d="M12 14.6L18.3 8.3 19.7 9.7 12 17.4 4.3 9.7 5.7 8.3z"></path>
                                  </svg></i></span></div>
                          </button></div>
                        <div class="content overflow-hidden" style="display:none;" data-v-3bb938c8="" data-v-53026b1a="" id="qggvh">
                          <div data-v-3bb938c8="" class="">
                            <li class="font-size-intermediate margin-0 margin-top-nudge margin-left-nudge" data-v-fcb80d4c=""><a href="/download" class="primary-hover fullwidth" data-v-fcb80d4c="">
                                Download TIDAL
                              </a></li>
                            <li class="font-size-intermediate margin-0 margin-top-nudge margin-left-nudge" data-v-fcb80d4c=""><a href="/transfer-music" class="primary-hover fullwidth" data-v-fcb80d4c="">
                                Transfer Music
                              </a></li>
                            <li class="font-size-intermediate margin-0 margin-top-nudge margin-left-nudge" data-v-fcb80d4c=""><a href="/supported-devices" class="primary-hover fullwidth" data-v-fcb80d4c="">
                                Supported Devices
                              </a></li>
                            <li class="font-size-intermediate margin-0 margin-top-nudge margin-left-nudge" data-v-fcb80d4c=""><a href="/help" class="primary-hover fullwidth" data-v-fcb80d4c="">
                                Get Help
                              </a></li>
                          </div>
                        </div>
                      </div>
                    </ul>
                  </div> <!----> <!----> <a href="/try-now" class="btn-secondary fullwidth margin-top-1" data-v-fcb80d4c="">
                    Start Free Trial
                  </a> <a href="https://account.tidal.com" class="btn-dark btn-action fullwidth margin-top-nudge" data-v-fcb80d4c=""><i class="tidal-icon" data-v-fcb80d4c=""><svg width="20" height="18" viewBox="0 0 20 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" clip-rule="evenodd"
                          d="M9.99982 2.00876C8.11717 2.00876 6.34982 3.86753 6.34982 6.48376C6.34982 9.09999 8.11717 10.9588 9.99982 10.9588C11.8825 10.9588 13.6498 9.09999 13.6498 6.48376C13.6498 3.86753 11.8825 2.00876 9.99982 2.00876ZM4.57482 6.48376C4.57482 3.17674 6.87048 0.233765 9.99982 0.233765C13.1292 0.233765 15.4248 3.17674 15.4248 6.48376C15.4248 9.79079 13.1292 12.7338 9.99982 12.7338C6.87048 12.7338 4.57482 9.79079 4.57482 6.48376ZM4.86864 11.6946C5.18024 12.0729 5.12611 12.6323 4.74775 12.9439C3.4899 13.9797 2.62172 15.3686 2.33358 16.9173C2.24393 17.3992 1.78061 17.7172 1.29872 17.6275C0.81684 17.5379 0.498874 17.0746 0.588527 16.5927C0.960387 14.5939 2.07221 12.8478 3.61936 11.5737C3.99773 11.2621 4.55705 11.3162 4.86864 11.6946ZM16.4215 11.5737C16.0431 11.2621 15.4838 11.3162 15.1722 11.6946C14.8606 12.0729 14.9147 12.6323 15.2931 12.9439C16.5522 13.9807 17.4129 15.3641 17.6626 16.8976C17.7413 17.3814 18.1974 17.7097 18.6811 17.631C19.1649 17.5522 19.4933 17.0962 19.4145 16.6124C19.0866 14.5984 17.9674 12.8468 16.4215 11.5737Z"
                          fill="currentColor"></path>
                      </svg></i>
                    Log in
                  </a>
                </div>
              </div>
            </div>
          </div>
        </header>
        <main id="main-content" tabindex="-1" aria-label="Main content" class="flex-grow">
          <div>
            <section class="overflow-hidden bg-gradient" data-v-0d875eb7="">
              <div class="bg-wrapper background-cover" style="--bg-url: url(//resources.tidal.com/images/55b2bcae/8e9e/4ec2/9845/d1f83e2ff61c/1080x1080.jpg);" data-v-0d875eb7=""></div>
              <div class="wrapper margin-top-md-min-2 margin-top-lg-min-3 margin-top-1" data-v-0d875eb7="">
                <div class="row margin-bottom-nudge trigger-sign-up" data-v-0d875eb7="">
                  <div class="col-4 col-md-4 col-sm-centered-2" data-v-0d875eb7="">
                    <div class="tidal-image-wrapper block placeholder position-relative tidal-lazy-image-wrapper--loaded" style="padding-bottom:100%;" data-v-0d875eb7=""><img src="//resources.tidal.com/images/55b2bcae/8e9e/4ec2/9845/d1f83e2ff61c/640x640.jpg" srcset="//resources.tidal.com/images/55b2bcae/8e9e/4ec2/9845/d1f83e2ff61c/640x640.jpg 640w, //resources.tidal.com/images/55b2bcae/8e9e/4ec2/9845/d1f83e2ff61c/320x320.jpg 320w, //resources.tidal.com/images/55b2bcae/8e9e/4ec2/9845/d1f83e2ff61c/160x160.jpg 160w" sizes="(min-width: 768px) calc(33.33vw - 3rem), calc(100vw - 3rem)" alt="DESPECHÁ" class="tidal-image border-radius block position-absolute"> <!----></div>
                  </div>
                  <div class="col-8 col-md-8 col-sm-12 padding-left-lg-min-1" data-v-0d875eb7="">
                    <div class="position-center-wrapper text-left" data-v-0d875eb7="">
                      <div class="text-center text-left-md-min" data-v-0d875eb7="">
                        <h1 class="font-size-large font-size-medium-lg-max margin-bottom-0" data-v-0d875eb7=""><!----> <a href="/browse/album/240175607" class="text-dec-none visible-offset-0" data-v-0d875eb7="">DESPECHÁ</a></h1>
                        <div class="artist-list font-size-intermediate font-size-regular-lg-max font-weight-bold" data-v-0d875eb7=""><a href="/browse/artist/4748331" class="text-dec-none visible-offset-0 artist-list-link hover-desktop" data-v-0d875eb7="">ROSALÍA</a></div> <span class="item-info block" data-v-a6ac5e68="" data-v-0d875eb7=""><span class="info-item secondary-title font-weight-bold opacity-80" data-v-a6ac5e68="">2022</span> <span aria-label="Explicit" class="info-item info-label" data-v-a6ac5e68=""><span aria-hidden="true" data-v-a6ac5e68="">E<span data-v-a6ac5e68="">xplicit</span></span></span> <!----></span> <!---->
                      </div> <!---->
                    </div>
                  </div>
                  <div class="margin-bottom-md-max-0 col-12 margin-top-nudge margin-top-md-max-0" data-v-0d875eb7="">
                    <div class="button-wrapper font-weight-bold" data-v-0d875eb7="">
                      <div class="main-button" data-v-0d875eb7=""><!---->
                        <div data-v-f16a66fa="" data-v-0d875eb7=""><!---->
                          <div class="dropdown" data-v-f16a66fa=""><button class="btn-secondary btn-rounded overflow-hidden float-left" data-v-f16a66fa=""><span data-v-51a7f762="" data-v-f16a66fa=""><i class="tidal-icon" data-v-51a7f762=""><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" focusable="false" aria-hidden="true">
                                    <path fill-rule="evenodd" d="M4 0L4 24 22 12 4 0z"></path>
                                  </svg></i></span><span data-v-f16a66fa="">Play on TIDAL</span></button> <button aria-haspopup="listbox" aria-expanded="false" aria-controls="client-list" aria-label="TIDAL client selector" class="dropdown-btn" data-v-f16a66fa=""><span data-v-51a7f762="" data-v-f16a66fa=""><i class="tidal-icon" data-v-51a7f762=""><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" focusable="false" aria-hidden="true">
                                    <path d="M12 14.6L18.3 8.3 19.7 9.7 12 17.4 4.3 9.7 5.7 8.3z"></path>
                                  </svg></i></span></button>
                            <div id="client-list" class="dropdown-content position-absolute" style="display:none;" data-v-f16a66fa="">
                              <ul role="listbox" class="list-style-none margin-bottom-0" data-v-f16a66fa="">
                                <li role="option" aria-selected="true" class="margin-bottom-0 dropdown-list" data-v-f16a66fa=""><button class="dropdown-link plain-button fullwidth active-client" data-v-f16a66fa=""><span class="margin-right-2" data-v-f16a66fa="">
                                      <p class="margin-0 inline-block" data-v-f16a66fa="">Open in Web App</p> <span class="block check float-right font-size-medium" data-v-51a7f762="" data-v-f16a66fa=""><i class="tidal-icon" data-v-51a7f762=""><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" focusable="false" aria-hidden="true">
                                            <path d="M11.2 17.1L6.5 13.6 7.5 12.4 10.8 14.9 16.4 6.6 17.6 7.4 11.2 17.1z"></path>
                                          </svg></i></span>
                                    </span></button></li>
                                <li role="option" class="margin-bottom-0 dropdown-list" data-v-f16a66fa=""><button class="dropdown-link plain-button fullwidth" data-v-f16a66fa=""><span class="margin-right-2" data-v-f16a66fa="">
                                      <p class="margin-0 inline-block" data-v-f16a66fa="">Open in Desktop App</p> <span class="block check float-right font-size-medium" data-v-51a7f762="" data-v-f16a66fa=""><i class="tidal-icon" data-v-51a7f762=""><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" focusable="false" aria-hidden="true">
                                            <path d="M11.2 17.1L6.5 13.6 7.5 12.4 10.8 14.9 16.4 6.6 17.6 7.4 11.2 17.1z"></path>
                                          </svg></i></span>
                                    </span></button></li>
                              </ul>
                            </div>
                          </div> <!---->
                        </div>
                      </div>
                      <div class="secondary-buttons" data-v-0d875eb7=""><!---->
                        <div class="secondary-button" data-v-0d875eb7=""><button class="plain-button"><span data-v-51a7f762=""><i class="tidal-icon" data-v-51a7f762=""><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" focusable="false" aria-hidden="true">
                                  <path d="M18.5,14.75a3.73,3.73,0,0,0-2.94,1.45L9.11,13a3.36,3.36,0,0,0,0-1.93L15.56,7.8a3.73,3.73,0,1,0-.81-2.3,3.7,3.7,0,0,0,.14,1L8.44,9.7a3.75,3.75,0,1,0,0,4.61l6.46,3.23a3.7,3.7,0,0,0-.14,1,3.75,3.75,0,1,0,3.75-3.75Zm0-11.5A2.25,2.25,0,1,1,16.25,5.5,2.25,2.25,0,0,1,18.5,3.25Zm-13,11A2.25,2.25,0,1,1,7.75,12,2.25,2.25,0,0,1,5.5,14.25Zm13,6.5a2.25,2.25,0,1,1,2.25-2.25A2.25,2.25,0,0,1,18.5,20.75Z"></path>
                                </svg></i></span> <span class="button-text">Share</span></button></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </section>
            <div class="wrapper" data-v-54a2867b="">
              <section aria-label="Track list" class="track-list margin-bottom-nudge-1" data-v-54a2867b=""><!---->
                <div class="row row-custom font-weight-bold padding-bottom-nudge" data-v-54a2867b="">
                  <div class="track-wrapper col-12 has-controls track-active" data-v-54a2867b="">
                    <div class="track-item" data-v-54a2867b=""><button title="Play track 1" class="album-controls plain-button active-controls" data-v-54a2867b="">
                        <p aria-hidden="true" class="album-number secondary-title margin-0" data-v-54a2867b="">1</p>
                        <div data-v-54a2867b="" class="album-play"><span data-v-51a7f762="" data-v-54a2867b="" class="font-size-small hidden"><i data-v-51a7f762="" class="tidal-icon"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" focusable="false" aria-hidden="true">
                                <path fill-rule="evenodd" d="M4 0L4 24 22 12 4 0z"></path>
                              </svg></i></span> <span data-v-51a7f762="" data-v-54a2867b="" class="font-size-small"><i data-v-51a7f762="" class="tidal-icon"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" focusable="false" aria-hidden="true">
                                <path d="M4 0H10V24H4z"></path>
                                <path d="M14 0H20V24H14z"></path>
                              </svg></i></span></div>
                      </button>
                      <div class="track-name margin-0" data-v-54a2867b="">
                        <div class="flex flex-nowrap align-center" data-v-54a2867b="">
                          <p class="color-grey-lightest ellipsis margin-0" data-v-54a2867b=""><a href="/browse/track/240175608" aria-current="page" class="text-dec-none visible-offset-0 nuxt-link-exact-active nuxt-link-active hover-desktop" data-v-54a2867b="">
                              DESPECHÁ
                              <!----></a></p> <span class="info-wrapper margin-left-nudge" data-v-a6ac5e68="" data-v-54a2867b=""><!----> <span aria-label="Explicit" class="info-item info-label info-label-small" data-v-a6ac5e68=""><span aria-hidden="true" data-v-a6ac5e68="">E<span class="hidden-md-max" data-v-a6ac5e68="">xplicit</span></span></span> <!----></span>
                        </div>
                        <p class="artist-list hidden-lg-min ellipsis margin-0" data-v-54a2867b=""><a href="/browse/artist/4748331" class="text-dec-none visible-offset-0 artist-list-link hover-desktop" data-v-54a2867b="">ROSALÍA</a></p>
                      </div>
                      <div class="track-artists hidden-lg-max margin-0" data-v-54a2867b="">
                        <p class="artist-list ellipsis margin-0" data-v-54a2867b=""><a href="/browse/artist/4748331" class="text-dec-none visible-offset-0 artist-list-link hover-desktop" data-v-54a2867b="">ROSALÍA</a></p>
                      </div> <!---->
                      <div class="track-action" data-v-54a2867b=""><button title="Share track “DESPECHÁ”" class="track-options plain-button custom-focus font-size-intermediate" data-v-54a2867b=""><span data-v-51a7f762="" data-v-54a2867b=""><i class="tidal-icon" data-v-51a7f762=""><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" focusable="false" aria-hidden="true">
                                <circle cx="5.5" cy="12" r="1.5"></circle>
                                <circle cx="12" cy="12" r="1.5"></circle>
                                <circle cx="18.5" cy="12" r="1.5"></circle>
                              </svg></i></span></button></div>
                    </div>
                    <div class="progress-bar-wrapper" data-v-54a2867b="">
                      <div data-v-54a2867b="" class="progress-bar" style="width: 6.74879%;"></div>
                    </div>
                  </div>
                </div>
              </section> <!---->
            </div>
            <section class="margin-bottom-2">
              <div class="margin-bottom-1">
                <div class="wrapper">
                  <h2 class="font-size-intermediate margin-0">Other Albums by ROSALÍA</h2>
                </div>
              </div>
              <div class="tidal-carousel-wrapper position-relative" data-v-3aa8e486="">
                <div role="region" aria-label="carousel" class="tidal-carousel row nowrap nowrap-wrapper contain-width can-snap" data-v-3aa8e486="">
                  <div class="font-weight-bold col-3 col-md-4 col-sm-9 margin-bottom-0" data-v-3aa8e486="">
                    <div class="margin-bottom-nudge"><a href="/browse/album/220809479" tabindex="-1" class="text-dec-none visible-offset-0" data-tabindex="-1">
                        <div class="tidal-image-wrapper block placeholder position-relative tidal-lazy-image-wrapper tidal-lazy-image-wrapper--loaded" style="padding-bottom:100%;"><img src="//resources.tidal.com/images/5a4f508b/f83a/4d0b/a8f7/718bd6fefd9b/640x640.jpg" sizes="(min-width: 1404px) calc(330px - 1.5rem), (min-width: 992px) calc(25vw - 3rem), (min-width: 768px) calc(33.33vw - 3rem), calc(75vw - 1.5rem)" alt="MOTOMAMI" class="tidal-image border-radius-small block position-absolute tidal-lazy-image fade-in tidal-lazy-image--handled" data-disabled="false" srcset="//resources.tidal.com/images/5a4f508b/f83a/4d0b/a8f7/718bd6fefd9b/640x640.jpg 640w, //resources.tidal.com/images/5a4f508b/f83a/4d0b/a8f7/718bd6fefd9b/320x320.jpg 320w, //resources.tidal.com/images/5a4f508b/f83a/4d0b/a8f7/718bd6fefd9b/160x160.jpg 160w" data-loaded="true"> <noscript><img src="//resources.tidal.com/images/5a4f508b/f83a/4d0b/a8f7/718bd6fefd9b/640x640.jpg" alt="MOTOMAMI"
                              class="tidal-image border-radius-small block position-absolute" /></noscript></div>
                      </a></div>
                    <div class="ellipsis color-grey-lightest"><a href="/browse/album/220809479" class="text-dec-none visible-offset-0 hover-desktop">MOTOMAMI</a></div>
                    <div class="artist-list ellipsis"><a href="/browse/artist/4748331" class="text-dec-none visible-offset-0 hover-desktop">ROSALÍA</a></div> <span class="block" data-v-a6ac5e68=""><span class="info-item secondary-title font-weight-bold opacity-80" data-v-a6ac5e68="">2022</span> <span aria-label="Explicit" class="info-item info-label" data-v-a6ac5e68=""><span aria-hidden="true" data-v-a6ac5e68="">E<span data-v-a6ac5e68="">xplicit</span></span></span> <!----></span>
                  </div>
                  <div class="font-weight-bold col-3 col-md-4 col-sm-9 margin-bottom-0" data-v-3aa8e486="">
                    <div class="margin-bottom-nudge"><a href="/browse/album/246965715" tabindex="-1" class="text-dec-none visible-offset-0" data-tabindex="-1">
                        <div class="tidal-image-wrapper block placeholder position-relative tidal-lazy-image-wrapper tidal-lazy-image-wrapper--loaded" style="padding-bottom:100%;"><img src="//resources.tidal.com/images/c840576e/c7da/4d47/a641/e1d9ada3b26d/640x640.jpg" sizes="(min-width: 1404px) calc(330px - 1.5rem), (min-width: 992px) calc(25vw - 3rem), (min-width: 768px) calc(33.33vw - 3rem), calc(75vw - 1.5rem)" alt="MOTOMAMI +" class="tidal-image border-radius-small block position-absolute tidal-lazy-image fade-in tidal-lazy-image--handled" data-disabled="false" srcset="//resources.tidal.com/images/c840576e/c7da/4d47/a641/e1d9ada3b26d/640x640.jpg 640w, //resources.tidal.com/images/c840576e/c7da/4d47/a641/e1d9ada3b26d/320x320.jpg 320w, //resources.tidal.com/images/c840576e/c7da/4d47/a641/e1d9ada3b26d/160x160.jpg 160w" data-loaded="true"> <noscript><img src="//resources.tidal.com/images/c840576e/c7da/4d47/a641/e1d9ada3b26d/640x640.jpg" alt="MOTOMAMI +"
                              class="tidal-image border-radius-small block position-absolute" /></noscript></div>
                      </a></div>
                    <div class="ellipsis color-grey-lightest"><a href="/browse/album/246965715" class="text-dec-none visible-offset-0 hover-desktop">MOTOMAMI +</a></div>
                    <div class="artist-list ellipsis"><a href="/browse/artist/4748331" class="text-dec-none visible-offset-0 hover-desktop">ROSALÍA</a></div> <span class="block" data-v-a6ac5e68=""><span class="info-item secondary-title font-weight-bold opacity-80" data-v-a6ac5e68="">2022</span> <span aria-label="Explicit" class="info-item info-label" data-v-a6ac5e68=""><span aria-hidden="true" data-v-a6ac5e68="">E<span data-v-a6ac5e68="">xplicit</span></span></span> <!----></span>
                  </div>
                  <div class="font-weight-bold col-3 col-md-4 col-sm-9 margin-bottom-0" data-v-3aa8e486="">
                    <div class="margin-bottom-nudge"><a href="/browse/album/95094922" tabindex="-1" class="text-dec-none visible-offset-0" data-tabindex="-1">
                        <div class="tidal-image-wrapper block placeholder position-relative tidal-lazy-image-wrapper tidal-lazy-image-wrapper--loaded" style="padding-bottom:100%;"><img src="//resources.tidal.com/images/5526067b/4e00/4999/8871/28998dd6fc58/640x640.jpg" sizes="(min-width: 1404px) calc(330px - 1.5rem), (min-width: 992px) calc(25vw - 3rem), (min-width: 768px) calc(33.33vw - 3rem), calc(75vw - 1.5rem)" alt="El Mal Querer" class="tidal-image border-radius-small block position-absolute tidal-lazy-image fade-in tidal-lazy-image--handled" data-disabled="false" srcset="//resources.tidal.com/images/5526067b/4e00/4999/8871/28998dd6fc58/640x640.jpg 640w, //resources.tidal.com/images/5526067b/4e00/4999/8871/28998dd6fc58/320x320.jpg 320w, //resources.tidal.com/images/5526067b/4e00/4999/8871/28998dd6fc58/160x160.jpg 160w" data-loaded="true"> <noscript><img src="//resources.tidal.com/images/5526067b/4e00/4999/8871/28998dd6fc58/640x640.jpg" alt="El Mal Querer"
                              class="tidal-image border-radius-small block position-absolute" /></noscript></div>
                      </a></div>
                    <div class="ellipsis color-grey-lightest"><a href="/browse/album/95094922" class="text-dec-none visible-offset-0 hover-desktop">El Mal Querer</a></div>
                    <div class="artist-list ellipsis"><a href="/browse/artist/4748331" class="text-dec-none visible-offset-0 hover-desktop">ROSALÍA</a></div> <span class="block" data-v-a6ac5e68=""><span class="info-item secondary-title font-weight-bold opacity-80" data-v-a6ac5e68="">2018</span> <!----> <!----></span>
                  </div>
                  <div class="font-weight-bold col-3 col-md-4 col-sm-9 margin-bottom-0" data-v-3aa8e486="">
                    <div class="margin-bottom-nudge"><a href="/browse/album/170958917" tabindex="-1" class="text-dec-none visible-offset-0" data-tabindex="-1">
                        <div class="tidal-image-wrapper block placeholder position-relative tidal-lazy-image-wrapper tidal-lazy-image-wrapper--loaded" style="padding-bottom:100%;"><img src="//resources.tidal.com/images/9389271b/8fed/4050/8291/c5438719dd5c/640x640.jpg" sizes="(min-width: 1404px) calc(330px - 1.5rem), (min-width: 992px) calc(25vw - 3rem), (min-width: 768px) calc(33.33vw - 3rem), calc(75vw - 1.5rem)" alt="Los Ángeles" class="tidal-image border-radius-small block position-absolute tidal-lazy-image fade-in tidal-lazy-image--handled" data-disabled="false" srcset="//resources.tidal.com/images/9389271b/8fed/4050/8291/c5438719dd5c/640x640.jpg 640w, //resources.tidal.com/images/9389271b/8fed/4050/8291/c5438719dd5c/320x320.jpg 320w, //resources.tidal.com/images/9389271b/8fed/4050/8291/c5438719dd5c/160x160.jpg 160w" data-loaded="true"> <noscript><img src="//resources.tidal.com/images/9389271b/8fed/4050/8291/c5438719dd5c/640x640.jpg" alt="Los Ángeles"
                              class="tidal-image border-radius-small block position-absolute" /></noscript></div>
                      </a></div>
                    <div class="ellipsis color-grey-lightest"><a href="/browse/album/170958917" class="text-dec-none visible-offset-0 hover-desktop">Los Ángeles</a></div>
                    <div class="artist-list ellipsis"><a href="/browse/artist/4748331" class="text-dec-none visible-offset-0 hover-desktop">ROSALÍA</a></div> <span class="block" data-v-a6ac5e68=""><span class="info-item secondary-title font-weight-bold opacity-80" data-v-a6ac5e68="">2017</span> <!----> <!----></span>
                  </div>
                </div>
                <div aria-label="carousel controls" class="tidal-carousel-controls" data-v-3aa8e486=""><button disabled="disabled" aria-label="previous" class="tidal-carousel-control tidal-carousel-control--prev visible-offset-0" data-v-3aa8e486=""><span class="tidal-carousel-icon font-size-intermediate" data-v-51a7f762="" data-v-3aa8e486=""><i class="tidal-icon" data-v-51a7f762=""><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" focusable="false" aria-hidden="true">
                          <path d="M15.3 20.7L6.6 12 15.3 3.3 16.7 4.7 9.4 12 16.7 19.3 15.3 20.7z"></path>
                        </svg></i></span></button>
                  <div class="tidal-carousel-control tidal-carousel-control-bg tidal-carousel-control--prev-bg" data-v-3aa8e486=""></div> <button aria-label="next" class="tidal-carousel-control tidal-carousel-control--next visible-offset-0" data-v-3aa8e486="" disabled="disabled"><span class="tidal-carousel-icon font-size-intermediate" data-v-51a7f762="" data-v-3aa8e486=""><i class="tidal-icon" data-v-51a7f762=""><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" focusable="false" aria-hidden="true">
                          <path d="M8.7 20.7L7.3 19.3 14.6 12 7.3 4.7 8.7 3.3 17.4 12 8.7 20.7z"></path>
                        </svg></i></span></button>
                  <div class="tidal-carousel-control tidal-carousel-control-bg tidal-carousel-control--next-bg" data-v-3aa8e486=""></div>
                </div>
              </div>
            </section>
          </div> <!---->
        </main>
        <div class="floating-wrapper text-center floating--is-visible" data-v-1727d62b=""><a href="https://tidal.com/try-now" class="floating-content text-dec-none" data-v-1727d62b=""><span class="floating-text" data-v-1727d62b="">Try TIDAL for free. Cancel anytime.</span> <span class="floating-line opacity-40 padding-left-nudge padding-right-nudge" data-v-1727d62b="">|</span> <span class="floating-link font-weight-bold" data-v-1727d62b="">Start Free Trial</span></a></div>
        <footer id="footer-content" tabindex="-1" aria-label="Footer content" login="" class="tidal-footer theme-light-dark padding-top-4 padding-top-lg-max-2" data-v-d410bc9a="" data-v-3bd76d68="">
          <div class="wrapper" data-v-d410bc9a="">
            <div class="row" data-v-d410bc9a="">
              <div class="col-9 margin-bottom-md-min-0" data-v-d410bc9a="">
                <nav aria-label="Footer Navigation" class="row" data-v-d410bc9a="">
                  <div class="col-3 col-md-6 col-sm-6 col-xs-fullwidth margin-bottom-lg-max-2" data-v-d410bc9a=""><span class="color-grey-light block baseline-flow" data-v-d410bc9a="">Get Started</span>
                    <ul class="list-style-none" data-v-d410bc9a="">
                      <li class="baseline-flow hidden-lg-min" data-v-d410bc9a=""><a href="/try-now" class="primary-hover text-dec-hover" data-v-d410bc9a="">Start Free Trial</a></li>
                      <li class="baseline-flow" data-v-d410bc9a=""><a href="/download" class="primary-hover text-dec-hover" data-v-d410bc9a="">Download TIDAL</a></li>
                      <li class="baseline-flow" data-v-d410bc9a=""><a href="/pricing" class="primary-hover text-dec-hover" data-v-d410bc9a="">Pricing &amp; Plans</a></li>
                      <li class="baseline-flow" data-v-d410bc9a=""><a href="/transfer-music" class="primary-hover text-dec-hover" data-v-d410bc9a="">Transfer Music</a></li>
                      <li class="baseline-flow" data-v-d410bc9a=""><a href="/supported-devices" class="primary-hover text-dec-hover" data-v-d410bc9a="">Supported Devices</a></li>
                      <li class="baseline-flow" data-v-d410bc9a=""><a href="/help" class="primary-hover text-dec-hover" data-v-d410bc9a="">Get Support</a></li>
                    </ul>
                  </div>
                  <div class="col-3 col-md-6 col-sm-6 col-xs-fullwidth margin-bottom-lg-max-2" data-v-d410bc9a=""><span class="color-grey-light block baseline-flow" data-v-d410bc9a="">Discover TIDAL</span>
                    <ul class="list-style-none" data-v-d410bc9a="">
                      <li class="baseline-flow" data-v-d410bc9a=""><a href="/about" class="primary-hover text-dec-hover" data-v-d410bc9a="">About</a></li>
                      <li class="baseline-flow" data-v-d410bc9a=""><a href="/explore-the-app" class="primary-hover text-dec-hover" data-v-d410bc9a="">Explore the App</a></li>
                      <li class="baseline-flow" data-v-d410bc9a=""><a href="/formusic" class="primary-hover text-dec-hover" data-v-d410bc9a="">For Music</a></li>
                      <li class="baseline-flow" data-v-d410bc9a=""><a href="/forartists" class="primary-hover text-dec-hover" data-v-d410bc9a="">For Artists</a></li>
                      <li class="baseline-flow" data-v-d410bc9a=""><a href="/culture" class="primary-hover text-dec-hover" data-v-d410bc9a="">Culture</a></li>
                      <li class="baseline-flow" data-v-d410bc9a=""><a href="/magazine" class="primary-hover text-dec-hover" data-v-d410bc9a="">Magazine</a></li>
                      <li class="baseline-flow" data-v-d410bc9a=""><a href="/originals" class="primary-hover text-dec-hover" data-v-d410bc9a="">Originals</a></li>
                      <li class="baseline-flow" data-v-d410bc9a=""><a href="/rising" class="primary-hover text-dec-hover" data-v-d410bc9a="">Rising</a></li>
                      <li class="baseline-flow" data-v-d410bc9a=""><a href="/sound-quality" class="primary-hover text-dec-hover" data-v-d410bc9a="">Sound Quality</a></li>
                      <li class="baseline-flow" data-v-d410bc9a=""><a href="/connect" class="primary-hover text-dec-hover" data-v-d410bc9a="">TIDAL Connect</a></li>
                      <li class="baseline-flow" data-v-d410bc9a=""><a href="/unplugged" class="primary-hover text-dec-hover" data-v-d410bc9a="">Unplugged</a></li>
                    </ul>
                  </div>
                  <div class="col-3 col-md-6 col-sm-6 col-xs-fullwidth margin-bottom-md-max-2" data-v-d410bc9a=""><span class="color-grey-light block baseline-flow" data-v-d410bc9a="">Account</span>
                    <ul class="list-style-none" data-v-d410bc9a="">
                      <li class="baseline-flow" data-v-d410bc9a=""><a href="/try-now" class="primary-hover text-dec-hover" data-v-d410bc9a="">Sign Up</a></li>
                      <li class="baseline-flow" data-v-d410bc9a=""><a href="/voucher" class="primary-hover text-dec-hover" data-v-d410bc9a="">Redeem Voucher</a></li>
                      <li class="baseline-flow" data-v-d410bc9a=""><a href="/giftcard" class="primary-hover text-dec-hover" data-v-d410bc9a="">Redeem Giftcard</a></li>
                      <li class="baseline-flow" data-v-d410bc9a=""><a href="https://account.tidal.com" class="primary-hover text-dec-hover" data-v-d410bc9a="">Manage Account</a></li>
                    </ul>
                  </div>
                  <div class="col-3 col-md-6 col-sm-6 col-xs-fullwidth" data-v-d410bc9a=""><span class="color-grey-light block baseline-flow" data-v-d410bc9a="">Company</span>
                    <ul class="list-style-none" data-v-d410bc9a="">
                      <li class="baseline-flow" data-v-d410bc9a=""><a href="/whatistidal" class="primary-hover text-dec-hover" data-v-d410bc9a="">What is TIDAL?</a></li>
                      <li class="baseline-flow" data-v-d410bc9a=""><a href="/partners" class="primary-hover text-dec-hover" data-v-d410bc9a="">Partners</a></li>
                      <li class="baseline-flow" data-v-d410bc9a=""><a href="https://careers.tidal.com" class="primary-hover text-dec-hover" data-v-d410bc9a="">Careers</a></li>
                      <li class="baseline-flow" data-v-d410bc9a=""><a href="/press" class="primary-hover text-dec-hover" data-v-d410bc9a="">Press</a></li>
                    </ul>
                  </div>
                </nav>
              </div>
              <div class="col-3 col-md-centered-3 margin-bottom-lg-min-0 text-center hidden-lg-max" data-v-d410bc9a=""><span class="block" data-v-d410bc9a=""><svg viewBox="0 0 300 153" fill="none" class="block fullwidth padding-left-nudge padding-right-nudge margin-bottom-2">
                    <path d="M55.5561798,83.6335178 L82.7174952,83.7716499 C85.1938121,83.7842435 87.1946103,85.7952428 87.1946103,88.2715917 L87.1946103,130.960674 L172.410017,130.960674 L172.519072,64.6597133 C172.523144,62.1838722 174.526462,60.1763765 177.00229,60.1671464 L245.567416,59.9115285 L245.567416,5 C245.567416,2.51471863 243.552697,0.5 241.067416,0.5 L60.0561798,0.5 C57.5708984,0.5 55.5561798,2.51471863 55.5561798,5 C55.5561798,13.3216841 55.5561798,13.3216841 55.5561798,21.6433682 C55.5561798,29.9650522 55.5561798,29.9650522 55.5561798,38.2867363 C55.5561798,49.2374314 55.5561798,49.2374314 55.5561798,60.5964194 C55.5561798,71.9971276 55.5561798,72.1572724 55.5561798,83.6335178 Z" stroke="#9B9B9B"></path>
                    <path d="M94.3820225,138.202247 L171.910112,138.202247" stroke="#9B9B9B" stroke-linecap="square"></path>
                    <rect stroke="#979797" x="165.668539" y="146.567416" width="133.831461" height="5.74157303" rx="2"></rect>
                    <path d="M176.910112,60.0505618 C174.424831,60.0505618 172.410112,62.0652804 172.410112,64.5505618 C172.410112,69.3544475 172.410112,69.3544475 172.410112,74.1583331 C172.410112,78.9622188 172.410112,78.9622188 172.410112,83.7661044 C172.410112,97.4891474 172.410112,97.4891474 172.410112,111.21219 L172.410112,137.696629 C172.410112,140.181911 174.424831,142.196629 176.910112,142.196629 L288.258427,142.196629 C290.743708,142.196629 292.758427,140.181911 292.758427,137.696629 L292.758427,64.5505618 C292.758427,62.0652804 290.743708,60.0505618 288.258427,60.0505618 L176.910112,60.0505618 Z" stroke="#9B9B9B"></path>
                    <path d="M42.0786517,83.6460674 C39.5933703,83.6460674 37.5786517,85.660786 37.5786517,88.1460674 C37.5786517,95.7904164 37.5786517,95.7904164 37.5786517,103.434765 C37.5786517,114.93245 37.5786517,114.93245 37.5786517,126.430135 L37.5786517,147.808989 C37.5786517,150.29427 39.5933703,152.308989 42.0786517,152.308989 L82.6404494,152.308989 C85.1257308,152.308989 87.1404494,150.29427 87.1404494,147.808989 L87.1404494,88.1460674 C87.1404494,85.660786 85.1257308,83.6460674 82.6404494,83.6460674 L42.0786517,83.6460674 Z" stroke="#9B9B9B"></path>
                    <path d="M5,100.5 C2.51471863,100.5 0.5,102.514719 0.5,105 C0.5,110.190071 0.5,110.190071 0.5,115.380142 C0.5,119.738135 0.5,119.738135 0.5,124.096129 C0.5,128.454122 0.5,128.454122 0.5,132.812115 L0.5,147.808989 C0.5,150.29427 2.51471863,152.308989 5,152.308989 L21.9662921,152.308989 C24.4515735,152.308989 26.4662921,150.29427 26.4662921,147.808989 L26.4662921,105 C26.4662921,102.514719 24.4515735,100.5 21.9662921,100.5 L5,100.5 Z" stroke="#9B9B9B"></path>
                  </svg></span> <a href="/try-now" class="btn-primary-outline fullwidth margin-bottom-lg-max-1" data-v-d410bc9a="">Start Free Trial</a></div>
            </div>
            <div class="row" data-v-d410bc9a="">
              <div class="col-12 margin-top-1 margin-top-md-min-4" data-v-d410bc9a="">
                <nav aria-label="Social Links" class="float-right-md-min margin-bottom-md-max-1 margin-left-nudge-1 margin-left-md-max-0 clearfix" data-v-d410bc9a=""><span class="font-size-intermediate text-align-intermediate" data-v-d410bc9a=""><a title="TIDAL on Twitter" href="https://twitter.com/TIDAL" rel="noopener" target="_blank" class="color-grey-light current-hover" data-v-d410bc9a=""><span data-v-51a7f762="" data-v-d410bc9a=""><i class="tidal-icon" data-v-51a7f762=""><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 15" focusable="false" aria-hidden="true">
                            <path fill-rule="evenodd"
                              d="M5.660856 14.9697794c6.792768 0 10.507392-5.74713234 10.507392-10.73058822 0-.16330883-.00324-.32588236-.010584-.48757353.721008-.5322794 1.34784-1.1964706 1.842336-1.9527206-.661752.30014707-1.373832.5025-2.120832.59367648.76248-.4667647 1.34784-1.20558824 1.623888-2.08617647-.713664.4319853-1.50372.74617647-2.344896.91536765C14.484312.48875 13.524552.0302206 12.462552.0302206c-2.039616 0-3.693528 1.6890441-3.693528 3.77117646 0 .2960294.032472.58375.09576.85992647C5.79564 4.50375 3.07404 3.00294118 1.2528.72095588c-.317448.55720588-.500112 1.20485294-.500112 1.8955147 0 1.30852942.65196 2.46360295 1.643328 3.13933824-.605664-.0190441-1.174824-.1890441-1.67256-.47183823-.000864.01573528-.000864.0315441-.000864.04808822 0 1.8267647 1.273176 3.35161765 2.9628 3.69742647-.310104.08617646-.636552.13264705-.973512.13264705-.237888 0-.469296-.0240441-.694224-.0680147.47016 1.49838235 1.833408 2.58874995 3.449952 2.61948525-1.264176 1.0115442-2.856384 1.6143383-4.586616 1.6143383-.298008 0-.591912-.017353-.880992-.0522059 1.634472 1.0696323 3.575016 1.6940441 5.660856 1.6940441">
                            </path>
                          </svg></i></span></a><a title="TIDAL on Instagram" href="https://instagram.com/tidal_spain/" rel="noopener" target="_blank" class="color-grey-light current-hover margin-left-nudge-1" data-v-d410bc9a=""><span data-v-51a7f762="" data-v-d410bc9a=""><i class="tidal-icon" data-v-51a7f762=""><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" focusable="false" aria-hidden="true">
                            <g fill-rule="evenodd">
                              <path d="M19.7142857,6 C18.7675714,6 18,5.23242857 18,4.28571429 C18,3.339 18.7675714,2.57142857 19.7142857,2.57142857 C20.661,2.57142857 21.4285714,3.339 21.4285714,4.28571429 C21.4285714,5.23242857 20.661,6 19.7142857,6 M12.0714286,19.5714286 C7.92928571,19.5714286 4.57142857,16.2135714 4.57142857,12.0714286 C4.57142857,7.92928571 7.92928571,4.57142857 12.0714286,4.57142857 C16.2135714,4.57142857 19.5714286,7.92928571 19.5714286,12.0714286 C19.5714286,16.2135714 16.2135714,19.5714286 12.0714286,19.5714286 M17.3318571,0 L6.66814286,0 C3.00071429,0 0,3.00057143 0,6.66814286 L0,17.3318571 C0,20.9992857 3.00071429,24 6.66814286,24 L17.3318571,24 C20.9992857,24 24,20.9992857 24,17.3318571 L24,6.66814286 C24,3.00057143 20.9992857,0 17.3318571,0"></path>
                              <path d="M12,8 C9.79441509,8 8,9.79441509 8,12 C8,14.2055849 9.79441509,16 12,16 C14.2055849,16 16,14.2055849 16,12 C16,9.79441509 14.2055849,8 12,8"></path>
                            </g>
                          </svg></i></span></a><a title="TIDAL on Facebook" href="https://facebook.com/TIDALEspana/" rel="noopener" target="_blank" class="color-grey-light current-hover margin-left-nudge-1" data-v-d410bc9a=""><span data-v-51a7f762="" data-v-d410bc9a=""><i class="tidal-icon" data-v-51a7f762=""><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" focusable="false" aria-hidden="true">
                            <path fill-rule="evenodd" d="M16.9998202 0H.9930337C.44453934 0 0 .44453933 0 .9930337v16.0067865c0 .548427.44453933.9931011.9930337.9931011h8.61755057v-6.9678202H7.26573034V8.3095955h2.34485393V6.30701125c0-2.32395506 1.41937083-3.58941573 3.49253933-3.58941573.9931011 0 1.8465843.07388765 2.0952809.10692135v2.4287191l-1.4378427.00067416c-1.1274607 0-1.3458202.53575282-1.3458202 1.32195507V8.3095955h2.6890112l-.3500899 2.7155056h-2.3389213v6.9678202h4.5850786c.548427 0 .9930337-.4446741.9930337-.9931011V.9930337C17.9928539.44453934 17.5482472 0 16.9998202 0"></path>
                          </svg></i></span></a><a title="TIDAL on YouTube" href="https://youtube.com/tidal" rel="noopener" target="_blank" class="color-grey-light current-hover margin-left-nudge-1 large-icon" data-v-d410bc9a=""><span data-v-51a7f762="" data-v-d410bc9a=""><i class="tidal-icon" data-v-51a7f762=""><svg width="35" height="25" viewBox="0 0 35 25" fill="none" xmlns="http://www.w3.org/2000/svg" focusable="false" aria-hidden="true">
                            <path d="M33.3523 4.0823C32.9613 2.60746 31.8058 1.44617 30.3406 1.05133C27.6852 0.335205 17.0323 0.335205 17.0323 0.335205C17.0323 0.335205 6.37936 0.335205 3.72387 1.05133C2.25871 1.44617 1.10323 2.60746 0.712258 4.0823C0 6.75714 0 12.3352 0 12.3352C0 12.3352 0 17.9133 0.712258 20.5881C1.10323 22.0629 2.25871 23.2242 3.72387 23.6191C6.37936 24.3352 17.0323 24.3352 17.0323 24.3352C17.0323 24.3352 27.6852 24.3352 30.3406 23.6191C31.8058 23.2242 32.9613 22.0629 33.3523 20.5881C34.0645 17.9133 34.0645 12.3352 34.0645 12.3352C34.0645 12.3352 34.0645 6.75714 33.3523 4.0823ZM13.5484 17.4004V7.27004L22.4516 12.3352L13.5484 17.4004Z" fill="currentColor"></path>
                          </svg></i></span></a><a title="TIDAL on TikTok" href="https://tiktok.com/@tidal" rel="noopener" target="_blank" class="color-grey-light current-hover margin-left-nudge-1" data-v-d410bc9a=""><span data-v-51a7f762="" data-v-d410bc9a=""><i class="tidal-icon" data-v-51a7f762=""><svg width="23" height="25" viewBox="0 0 23 25" fill="none" xmlns="http://www.w3.org/2000/svg" focusable="false" aria-hidden="true">
                            <path
                              d="M12.001 8.66986C12.001 5.84602 12.001 3.02149 12.001 0.197644C12.001 0.00100366 12.001 0.000308819 12.1931 0.000308819C13.4767 0.000308819 14.7596 -0.000386024 16.0432 0.000308819C16.2539 0.000308819 16.2477 0.00169851 16.26 0.218489C16.342 1.70823 17.0619 3.0868 18.1163 4.11239C19.1074 5.07614 20.5879 5.97804 21.9968 5.96415C22.1276 5.96276 22.1668 6.00584 22.1675 6.13716C22.1778 7.40733 22.1936 8.6782 22.2081 9.94837C22.2081 9.96088 22.2081 9.97339 22.2081 9.9859C22.2067 10.2611 22.2067 10.2631 21.9417 10.2611C20.0862 10.2458 18.3799 9.71491 16.801 8.74421C16.695 8.6789 16.5945 8.60455 16.4872 8.54132C16.4438 8.51561 16.3888 8.51005 16.3392 8.49546C16.3261 8.54132 16.302 8.58648 16.302 8.63234C16.3 9.17293 16.3034 9.71421 16.3027 10.2548C16.3013 12.5881 16.3199 14.9221 16.291 17.2553C16.2683 19.1043 15.6372 20.7407 14.4485 22.1526C12.6879 24.2441 9.93827 25.3329 7.23474 24.9097C3.70046 24.3559 0.913649 21.2653 0.641094 17.682C0.541983 16.3833 0.750529 15.0582 1.27637 13.8666C2.65498 10.7398 5.9869 8.87971 9.32915 9.21045C9.45717 9.22296 9.49158 9.28202 9.49227 9.4064C9.50053 10.721 9.51429 12.035 9.52531 13.3496C9.52737 13.6199 9.51085 13.6324 9.24793 13.5643C6.78667 12.9313 4.45412 15.1993 4.92628 17.6987C5.2649 19.4913 7.01587 20.8032 8.8157 20.6232C10.6382 20.4405 11.9886 18.8299 11.9969 17.0115C12.0079 14.2307 12.0003 11.4499 12.001 8.66986Z"
                              fill="currentColor"></path>
                          </svg></i></span></a></span></nav> <span class="color-grey-light font-size-small" data-v-d410bc9a="">TIDAL is the first global music streaming service with high fidelity sound, hi-def video quality, along with expertly curated playlists and original content — making it a trusted source for music and culture.</span> <!---->
              </div>
            </div>
            <div class="tidal-footer-links font-size-small clearfix padding-top-nudge padding-top-md-max-0 tidal-footer-line" data-v-d410bc9a=""><span class="hidden-lg-max inline-block margin-bottom-1" data-v-d410bc9a=""><a href="/" title="TIDAL" class="font-size-medium text-align-medium color-grey-light current-hover" data-v-d410bc9a=""><span data-v-51a7f762="" data-v-d410bc9a=""><i class="tidal-icon" data-v-51a7f762=""><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" focusable="false" aria-hidden="true">
                        <path d="M12 5.6L8.3 9.3 4.6 5.6 0.9 9.3 4.6 13 8.3 9.3 12 13 8.3 16.7 12 20.4 15.7 16.7 12 13 15.7 9.3 12 5.6z"></path>
                        <path d="M16.8 6.7H22V11.9H16.8z" transform="rotate(-45 19.4 9.3)"></path>
                      </svg></i></span></a> <span class="color-grey-light margin-left-nudge" data-v-d410bc9a="">© 2023 Aspiro AB</span></span>
              <div class="float-right-lg-min clearfix" data-v-d410bc9a="">
                <nav aria-label="Bottom Navigation" class="float-left-md-min" data-v-d410bc9a="">
                  <ul class="list-style-none margin-bottom-nudge" data-v-d410bc9a="">
                    <li class="inline-block-md-min margin-bottom-md-min-0 margin-right-1" data-v-d410bc9a=""><a href="/privacy" class="text-dec-none primary-hover text-dec-hover" data-v-d410bc9a="">Privacy Notice</a></li>
                    <li class="inline-block-md-min margin-bottom-md-min-0 margin-right-1" data-v-d410bc9a=""><a href="/terms" class="text-dec-none primary-hover text-dec-hover" data-v-d410bc9a="">Terms and Conditions</a></li>
                    <li class="inline-block-md-min margin-bottom-md-min-0 margin-right-1" data-v-d410bc9a=""><a href="#" class="text-dec-none primary-hover text-dec-hover" data-v-d410bc9a="">Cookie Settings</a></li>
                    <li class="inline-block-md-min margin-bottom-md-min-0 margin-right-1" data-v-d410bc9a=""><a href="/accessibility" class="text-dec-none primary-hover text-dec-hover" data-v-d410bc9a="">Accessibility Statement</a></li> <!---->
                    <li class="inline-block-md-min margin-bottom-md-min-0" data-v-d410bc9a=""><a href="/contact" class="text-dec-none primary-hover text-dec-hover" data-v-d410bc9a="">Contact</a></li>
                  </ul>
                </nav> <span class="float-left float-right-lg-min margin-left-nudge margin-left-md-max-0 tidal-footer-slot" data-v-d410bc9a="">
                  <div class="tidal-dropdown inline-block position-relative language-wrapper is-reversed is-centered" data-v-5970ef6c="" data-v-3bd76d68="" data-v-d410bc9a=""><button id="language-switcher" aria-haspopup="listbox" aria-expanded="false" aria-controls="dropdown--963210897" aria-label="Language switcher. English." class="tidal-dropdown-button btn-link btn-medium current-hover" data-v-5970ef6c=""><span class="opacity-80" data-v-51a7f762="" data-v-5970ef6c=""><i class="tidal-icon" data-v-51a7f762=""><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" focusable="false" aria-hidden="true">
                            <circle cx="8" cy="8" r="7.4" fill="none" stroke="#fff" stroke-width="1.2"></circle>
                            <path
                              d="M4.3437 1.55172C3.73696 1.55172 3.3523 1.91802 2.13881 3.2069C1.93656 3.41639 1.92097 3.77071 2.32547 3.77071C2.72996 3.77071 2.32547 4.86129 2.52772 5.85273C2.72996 6.84417 4.24683 7.73647 4.04458 7.53818C3.84233 7.33989 3.64008 6.54674 4.24683 7.1416C4.85357 7.73647 5.66256 8.62876 6.16818 8.82705C6.6738 9.02534 7.58392 8.82705 7.58392 9.22363C7.58392 9.6202 8.59516 10.1159 9.10078 10.3142C9.6064 10.5125 8.89853 10.8099 8.69629 11.3057C8.49404 11.8014 9.6064 13.2885 9.90977 13.6851C10.2131 14.0817 10.4154 14.1808 10.0109 14.974C9.6873 15.6085 10 14.9051 10 15.5C13.2469 13.6851 13.2877 13.3966 14.8081 11.4828C14.3016 11.1641 13.348 11.2065 13.2469 10.8099C13.1457 10.4134 12.6401 10.4134 12.1345 10.0168C11.6289 9.6202 10.8199 9.71935 10.2131 9.71935C9.6064 9.71935 9.10078 9.81849 8.89853 9.71935C8.69629 9.6202 8.59516 9.22363 8.39291 8.82705C8.19067 8.43048 8.08954 8.62876 7.78617 8.52962C7.4828 8.43048 7.88729 8.13304 7.78617 7.93476C7.68504 7.73647 7.38167 7.93476 7.38167 8.13304C7.38167 8.33133 6.37043 8.13304 6.37043 7.24075C6.37043 6.34845 7.38167 6.64588 8.08954 6.54674C8.79741 6.4476 8.59516 7.33989 8.89853 7.24075C9.20191 7.1416 9.20191 6.74503 9.10078 6.34845C8.99966 5.95188 9.6064 5.5553 9.90977 4.86129C10.2131 4.16728 11.9323 4.86129 11.5278 4.46472C11.1233 4.06814 11.1233 3.86985 11.9323 3.77071C12.7413 3.67156 11.4266 2.97756 10.8199 2.68012C10.2131 2.38269 8.89853 2.87841 8.29179 2.97755C7.68504 3.0767 6.87605 2.68012 6.97718 2.08526C7.0783 1.49039 10.0109 1.98611 10.4154 2.08526C10.8199 2.1844 11.1233 1.98611 10.4154 1.39128C9.70753 0.796437 8.08954 1.39128 7.38167 1.09386C6.6738 0.796436 6.6738 1.29213 5.56144 1.39128C4.79273 1.45979 4.97003 1.55172 4.3437 1.55172Z"
                              fill="#fff"></path>
                          </svg></i></span> <span data-v-5970ef6c="">English</span> <!----></button>
                    <div id="dropdown--963210897" class="tidal-dropdown-menu-wrapper" style="display:none;" data-v-5970ef6c="">
                      <div class="tidal-dropdown-menu theme-dark custom-dropdown" data-v-5970ef6c="">
                        <div class="tidal-dropdown-menu--triangle" data-v-5970ef6c=""></div>
                        <ul role="listbox" class="overflow-hidden list-style-none text-dec-none margin-bottom-0" data-v-5970ef6c="">
                          <li role="option" aria-selected="true" class="tidal-dropdown-menu--item font-weight-bold" style="animation-delay:0s;" data-v-5970ef6c=""><button class="tidal-dropdown-menu--link primary-hover visible-offset-0 plain-button fullwidth" data-v-5970ef6c=""><span data-v-5970ef6c="">English</span> <!----> <!----></button></li>
                          <li role="option" class="tidal-dropdown-menu--item" style="animation-delay:0.05s;" data-v-5970ef6c=""><button class="tidal-dropdown-menu--link primary-hover visible-offset-0 plain-button fullwidth" data-v-5970ef6c=""><span data-v-5970ef6c="">Български</span> <!----> <!----></button></li>
                          <li role="option" class="tidal-dropdown-menu--item" style="animation-delay:0.1s;" data-v-5970ef6c=""><button class="tidal-dropdown-menu--link primary-hover visible-offset-0 plain-button fullwidth" data-v-5970ef6c=""><span data-v-5970ef6c="">Deutsch</span> <!----> <!----></button></li>
                          <li role="option" class="tidal-dropdown-menu--item" style="animation-delay:0.15000000000000002s;" data-v-5970ef6c=""><button class="tidal-dropdown-menu--link primary-hover visible-offset-0 plain-button fullwidth" data-v-5970ef6c=""><span data-v-5970ef6c="">Español</span> <!----> <!----></button></li>
                          <li role="option" class="tidal-dropdown-menu--item" style="animation-delay:0.2s;" data-v-5970ef6c=""><button class="tidal-dropdown-menu--link primary-hover visible-offset-0 plain-button fullwidth" data-v-5970ef6c=""><span data-v-5970ef6c="">Français</span> <!----> <!----></button></li>
                          <li role="option" class="tidal-dropdown-menu--item" style="animation-delay:0.25s;" data-v-5970ef6c=""><button class="tidal-dropdown-menu--link primary-hover visible-offset-0 plain-button fullwidth" data-v-5970ef6c=""><span data-v-5970ef6c="">Hrvatski</span> <!----> <!----></button></li>
                          <li role="option" class="tidal-dropdown-menu--item" style="animation-delay:0.30000000000000004s;" data-v-5970ef6c=""><button class="tidal-dropdown-menu--link primary-hover visible-offset-0 plain-button fullwidth" data-v-5970ef6c=""><span data-v-5970ef6c="">Italiano</span> <!----> <!----></button></li>
                          <li role="option" class="tidal-dropdown-menu--item" style="animation-delay:0.35000000000000003s;" data-v-5970ef6c=""><button class="tidal-dropdown-menu--link primary-hover visible-offset-0 plain-button fullwidth" data-v-5970ef6c=""><span data-v-5970ef6c="">日本語</span> <!----> <!----></button></li>
                          <li role="option" class="tidal-dropdown-menu--item" style="animation-delay:0.4s;" data-v-5970ef6c=""><button class="tidal-dropdown-menu--link primary-hover visible-offset-0 plain-button fullwidth" data-v-5970ef6c=""><span data-v-5970ef6c="">Norsk</span> <!----> <!----></button></li>
                          <li role="option" class="tidal-dropdown-menu--item" style="animation-delay:0.45s;" data-v-5970ef6c=""><button class="tidal-dropdown-menu--link primary-hover visible-offset-0 plain-button fullwidth" data-v-5970ef6c=""><span data-v-5970ef6c="">Polski</span> <!----> <!----></button></li>
                          <li role="option" class="tidal-dropdown-menu--item" style="animation-delay:0.5s;" data-v-5970ef6c=""><button class="tidal-dropdown-menu--link primary-hover visible-offset-0 plain-button fullwidth" data-v-5970ef6c=""><span data-v-5970ef6c="">Português</span> <!----> <!----></button></li>
                          <li role="option" class="tidal-dropdown-menu--item" style="animation-delay:0.55s;" data-v-5970ef6c=""><button class="tidal-dropdown-menu--link primary-hover visible-offset-0 plain-button fullwidth" data-v-5970ef6c=""><span data-v-5970ef6c="">Português (Brasil)</span> <!----> <!----></button></li>
                          <li role="option" class="tidal-dropdown-menu--item" style="animation-delay:0.6000000000000001s;" data-v-5970ef6c=""><button class="tidal-dropdown-menu--link primary-hover visible-offset-0 plain-button fullwidth" data-v-5970ef6c=""><span data-v-5970ef6c="">Slovenščina</span> <!----> <!----></button></li>
                          <li role="option" class="tidal-dropdown-menu--item" style="animation-delay:0.65s;" data-v-5970ef6c=""><button class="tidal-dropdown-menu--link primary-hover visible-offset-0 plain-button fullwidth" data-v-5970ef6c=""><span data-v-5970ef6c="">Srpski</span> <!----> <!----></button></li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </span>
              </div> <span class="hidden-lg-min inline-block margin-bottom-1" data-v-d410bc9a=""><a href="/" title="TIDAL" class="font-size-medium text-align-medium color-grey-light current-hover" data-v-d410bc9a=""><span data-v-51a7f762="" data-v-d410bc9a=""><i class="tidal-icon" data-v-51a7f762=""><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" focusable="false" aria-hidden="true">
                        <path d="M12 5.6L8.3 9.3 4.6 5.6 0.9 9.3 4.6 13 8.3 9.3 12 13 8.3 16.7 12 20.4 15.7 16.7 12 13 15.7 9.3 12 5.6z"></path>
                        <path d="M16.8 6.7H22V11.9H16.8z" transform="rotate(-45 19.4 9.3)"></path>
                      </svg></i></span></a> <span class="color-grey-light margin-left-nudge" data-v-d410bc9a="">© 2023 Aspiro AB</span></span>
            </div> <!---->
          </div>
        </footer> <!---->
      </div>
    </div>
  </div>
  <script>window.__NUXT__ = (function (a, b, c, d, e, f, g, h, i, j, k, l, m, n, o, p, q, r, s, t, u, v, w) { n.artist = { id: b, name: c, albums: o }; n.artists = [{ id: b, name: c }]; n.contentType = e; n.id = h; n.image = { original: a, large: i, medium: j, small: k, xsmall: l }; n.explicit = d; n.releaseDate = "2022-07-28T00:00:00.000Z"; n.title = f; n.type = "SINGLE"; n.tracks = q; o[0] = { artist: { id: b, name: c }, artists: [{ id: b, name: c }], contentType: e, explicit: d, id: 220809479, image: { original: a, large: "\u002F\u002Fresources.tidal.com\u002Fimages\u002F5a4f508b\u002Ff83a\u002F4d0b\u002Fa8f7\u002F718bd6fefd9b\u002F1080x1080.jpg", medium: "\u002F\u002Fresources.tidal.com\u002Fimages\u002F5a4f508b\u002Ff83a\u002F4d0b\u002Fa8f7\u002F718bd6fefd9b\u002F640x640.jpg", small: "\u002F\u002Fresources.tidal.com\u002Fimages\u002F5a4f508b\u002Ff83a\u002F4d0b\u002Fa8f7\u002F718bd6fefd9b\u002F320x320.jpg", xsmall: "\u002F\u002Fresources.tidal.com\u002Fimages\u002F5a4f508b\u002Ff83a\u002F4d0b\u002Fa8f7\u002F718bd6fefd9b\u002F160x160.jpg" }, releaseDate: p, title: "MOTOMAMI", type: e }; o[1] = { artist: { id: b, name: c }, artists: [{ id: b, name: c }], contentType: e, explicit: d, id: 246965715, image: { original: a, large: "\u002F\u002Fresources.tidal.com\u002Fimages\u002Fc840576e\u002Fc7da\u002F4d47\u002Fa641\u002Fe1d9ada3b26d\u002F1080x1080.jpg", medium: "\u002F\u002Fresources.tidal.com\u002Fimages\u002Fc840576e\u002Fc7da\u002F4d47\u002Fa641\u002Fe1d9ada3b26d\u002F640x640.jpg", small: "\u002F\u002Fresources.tidal.com\u002Fimages\u002Fc840576e\u002Fc7da\u002F4d47\u002Fa641\u002Fe1d9ada3b26d\u002F320x320.jpg", xsmall: "\u002F\u002Fresources.tidal.com\u002Fimages\u002Fc840576e\u002Fc7da\u002F4d47\u002Fa641\u002Fe1d9ada3b26d\u002F160x160.jpg" }, releaseDate: p, title: "MOTOMAMI +", type: e }; o[2] = { artist: { id: b, name: c }, artists: [{ id: b, name: c }], contentType: e, explicit: g, id: 95094922, image: { original: a, large: "\u002F\u002Fresources.tidal.com\u002Fimages\u002F5526067b\u002F4e00\u002F4999\u002F8871\u002F28998dd6fc58\u002F1080x1080.jpg", medium: "\u002F\u002Fresources.tidal.com\u002Fimages\u002F5526067b\u002F4e00\u002F4999\u002F8871\u002F28998dd6fc58\u002F640x640.jpg", small: "\u002F\u002Fresources.tidal.com\u002Fimages\u002F5526067b\u002F4e00\u002F4999\u002F8871\u002F28998dd6fc58\u002F320x320.jpg", xsmall: "\u002F\u002Fresources.tidal.com\u002Fimages\u002F5526067b\u002F4e00\u002F4999\u002F8871\u002F28998dd6fc58\u002F160x160.jpg" }, releaseDate: "2018-11-02T00:00:00.000Z", title: "El Mal Querer", type: e }; o[3] = { artist: { id: b, name: c }, artists: [{ id: b, name: c }], contentType: e, explicit: g, id: 170958917, image: { original: a, large: "\u002F\u002Fresources.tidal.com\u002Fimages\u002F9389271b\u002F8fed\u002F4050\u002F8291\u002Fc5438719dd5c\u002F1080x1080.jpg", medium: "\u002F\u002Fresources.tidal.com\u002Fimages\u002F9389271b\u002F8fed\u002F4050\u002F8291\u002Fc5438719dd5c\u002F640x640.jpg", small: "\u002F\u002Fresources.tidal.com\u002Fimages\u002F9389271b\u002F8fed\u002F4050\u002F8291\u002Fc5438719dd5c\u002F320x320.jpg", xsmall: "\u002F\u002Fresources.tidal.com\u002Fimages\u002F9389271b\u002F8fed\u002F4050\u002F8291\u002Fc5438719dd5c\u002F160x160.jpg" }, releaseDate: "2017-02-10T00:00:00.000Z", title: "Los Ángeles", type: e }; q[0] = { albumID: h, albumTitle: f, artists: [{ id: b, name: c }], contentType: r, credits: a, duration: s, explicit: d, id: t, image: { original: a, large: i, medium: j, small: k, xsmall: l }, itemType: "track", title: f, trackNumber: m, version: a }; return { layout: "default", data: [{ album: n, albums: { items: o }, track: { albumID: h, albumTitle: f, artist: { id: b, name: c }, artists: [{ id: b, name: c }], contentType: r, credits: a, duration: s, explicit: d, id: t, image: { original: a, large: i, medium: j, small: k, xsmall: l }, itemType: a, title: f, trackNumber: m, version: a, album: n }, tracks: { items: q } }], fetch: {}, error: a, state: { country: "es", disableExplicit: g, disableVideos: g, env: { APP_DOMAIN: u, API_URL_BROWSER: v, IS_DEVELOPMENT: "false", OFFER_ORIGIN: "https:\u002F\u002Foffer.tidal.com" }, error: w, previousRoute: "", shareData: w, signUpBannerHidden: d, actionTypes: {}, mutationTypes: {}, auth: { user: a, transientData: a } }, serverRendered: d, routePath: "\u002Ftrack\u002F240175608", config: { tracking: { baseUrl: v, cookieOptions: { secure: d, domain: u }, enable: d, integrations: { facebook: d, tidalTracking: { useProductionEndpoint: d, oauthClientId: "bakYq0nMtpuRYDtM", serviceVersion: "7.14.8", useProxy: d } }, logLevel: m }, _app: { basePath: "\u002Fbrowse\u002F", assetsPath: "\u002Fbrowse\u002F_nuxt\u002F", cdnURL: a } }, __i18n: { langs: {} } } }(null, 4748331, "ROSALÍA", true, "ALBUM", "DESPECHÁ", false, 240175607, "\u002F\u002Fresources.tidal.com\u002Fimages\u002F55b2bcae\u002F8e9e\u002F4ec2\u002F9845\u002Fd1f83e2ff61c\u002F1080x1080.jpg", "\u002F\u002Fresources.tidal.com\u002Fimages\u002F55b2bcae\u002F8e9e\u002F4ec2\u002F9845\u002Fd1f83e2ff61c\u002F640x640.jpg", "\u002F\u002Fresources.tidal.com\u002Fimages\u002F55b2bcae\u002F8e9e\u002F4ec2\u002F9845\u002Fd1f83e2ff61c\u002F320x320.jpg", "\u002F\u002Fresources.tidal.com\u002Fimages\u002F55b2bcae\u002F8e9e\u002F4ec2\u002F9845\u002Fd1f83e2ff61c\u002F160x160.jpg", 1, {}, Array(4), "2022-03-18T00:00:00.000Z", Array(1), "TRACK", 157, 240175608, "tidal.com", "https:\u002F\u002Ftidal.com\u002Fbrowse", void 0));</script>
  <script src="/browse/_nuxt/66ce7ec.modern.js" defer=""></script>
  <script src="/browse/_nuxt/889e134.modern.js" defer=""></script>
  <script src="/browse/_nuxt/ea4067f.modern.js" defer=""></script>
  <script src="/browse/_nuxt/bbddaa9.modern.js" defer=""></script>
  <script src="/browse/_nuxt/24901a5.modern.js" defer=""></script>
  <script src="/browse/_nuxt/963f8f7.modern.js" defer=""></script>
  <script src="/browse/_nuxt/d1ca131.modern.js" defer=""></script>
  <script src="/browse/_nuxt/8a1cd62.modern.js" defer=""></script>


  <div id="onetrust-consent-sdk">
    <div class="onetrust-pc-dark-filter ot-hide ot-fade-in"></div>
  </div>
  <script type="text/javascript" id="">!function (b, e, f, g, a, c, d) { b.fbq || (a = b.fbq = function () { a.callMethod ? a.callMethod.apply(a, arguments) : a.queue.push(arguments) }, b._fbq || (b._fbq = a), a.push = a, a.loaded = !0, a.version = "2.0", a.queue = [], c = e.createElement(f), c.async = !0, c.src = g, d = e.getElementsByTagName(f)[0], d.parentNode.insertBefore(c, d)) }(window, document, "script", "https://connect.facebook.net/en_US/fbevents.js"); fbq("init", "837463869618040");</script>
  <script type="text/javascript" id="">fbq("trackSingle", "837463869618040", "PageView", void 0, { eventID: "744ead0d-07bf-45bd-a784-4f26979788ec" });</script>
  <noscript><img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=837463869618040&amp;ev=PageView&amp;noscript=1"></noscript>
  <script type="text/javascript" id="">!function (d, g, e) {
      d.TiktokAnalyticsObject = e; var a = d[e] = d[e] || []; a.methods = "page track identify instances debug on off once ready alias group enableCookie disableCookie".split(" "); a.setAndDefer = function (b, c) { b[c] = function () { b.push([c].concat(Array.prototype.slice.call(arguments, 0))) } }; for (d = 0; d < a.methods.length; d++)a.setAndDefer(a, a.methods[d]); a.instance = function (b) { b = a._i[b] || []; for (var c = 0; c < a.methods.length; c++)a.setAndDefer(b, a.methods[c]); return b }; a.load = function (b, c) {
        var f = "https://analytics.tiktok.com/i18n/pixel/events.js";
        a._i = a._i || {}; a._i[b] = []; a._i[b]._u = f; a._t = a._t || {}; a._t[b] = +new Date; a._o = a._o || {}; a._o[b] = c || {}; c = document.createElement("script"); c.type = "text/javascript"; c.async = !0; c.src = f + "?sdkid\x3d" + b + "\x26lib\x3d" + e; b = document.getElementsByTagName("script")[0]; b.parentNode.insertBefore(c, b)
      }; a.load("C7S0A50FLK2NRAISTAQG"); a.page()
    }(window, document, "ttq");</script>
  <script type="text/javascript" id="">!function (a, b) { if (!a.rdt) { var c = a.rdt = function () { c.sendEvent ? c.sendEvent.apply(c, arguments) : c.callQueue.push(arguments) }; c.callQueue = []; a = b.createElement("script"); a.src = "https://www.redditstatic.com/ads/pixel.js"; a.async = !0; b = b.getElementsByTagName("script")[0]; b.parentNode.insertBefore(a, b) } }(window, document); rdt("init", "t2_2y9wns5r");</script>

  <script type="text/javascript" id="">rdt("track", "PageVisit");</script>
  <script type="text/javascript" id="">(function (a, b, d) { if (!a.snaptr) { var c = a.snaptr = function () { c.handleRequest ? c.handleRequest.apply(c, arguments) : c.queue.push(arguments) }; c.queue = []; a = "script"; r = b.createElement(a); r.async = !0; r.src = d; b = b.getElementsByTagName(a)[0]; b.parentNode.insertBefore(r, b) } })(window, document, "https://sc-static.net/scevent.min.js"); snaptr("init", "4d24efb0-f252-4535-a1d2-d1056340748f", { user_email: "undefined" }); snaptr("track", "PAGE_VIEW");</script>

  <script type="text/javascript" id="" src="//static.ads-twitter.com/oct.js"></script>
  <script type="text/javascript" id="">!function (a, b) { var e = "38253"; if (a.zemApi) b = function (d) { return "[object Array]" === Object.prototype.toString.call(d) ? d : [d] }, a.zemApi.marketerId = b(a.zemApi.marketerId).concat(b(e)); else { var c = a.zemApi = function () { c.dispatch ? c.dispatch.apply(c, arguments) : c.queue.push(arguments) }; c.version = "1.0"; c.loaded = !0; c.marketerId = e; c.queue = []; a = b.createElement("script"); a.async = !0; a.src = "//js-tag.zemanta.com/zcpt.js"; a.type = "text/javascript"; b = b.getElementsByTagName("script")[0]; b.parentNode.insertBefore(a, b) } }(window,
      document); zemApi("track", "PAGE_VIEW");</script>
  <noscript><img src="//p1.zemanta.com/v2/p/ns/38253/PAGE_VIEW/?referrer=https%3A%2F%2Ftidal.com%2Fbrowse%2Ftrack%2F240175608" height="1" width="1" border="0" alt=""></noscript>

  <img src="https://tags.w55c.net/rs?id=26505dfd9d3847198a9f9da332e29301&amp;t=homepage" style="position:absolute;top:0;left:-500px;">
  <div style="width:0px; height:0px; display:none; visibility:hidden;" id="batBeacon960971320980"><img style="width:0px; height:0px; display:none; visibility:hidden;" id="batBeacon602558992706" width="0" height="0" alt="" src="https://bat.bing.com/action/0?ti=52008716&amp;tm=gtm002&amp;Ver=2&amp;mid=1471f643-8f47-4d5e-aa79-772128e2f2c3&amp;sid=dcebc280369811eeb15dc128b06ad69b&amp;vid=dcebeec0369811ee9b18abd4b9044e7f&amp;vids=0&amp;msclkid=N&amp;pi=918639831&amp;lg=en&amp;sw=1512&amp;sh=982&amp;sc=30&amp;tl=DESPECH%C3%81%20by%20ROSAL%C3%8DA%20on%20TIDAL&amp;p=https%3A%2F%2Ftidal.com%2Fbrowse%2Ftrack%2F240175608&amp;r=&amp;lt=302&amp;evt=pageLoad&amp;sv=1&amp;rn=56976"></div><iframe id="snap2312955" src="https://tr.snapchat.com/cm/i?pid=4d24efb0-f252-4535-a1d2-d1056340748f&amp;u_scsid=3e0f03cc-4ed3-46f3-822e-7d04a6a3af64&amp;u_sclid=32f278b7-2029-4544-afa4-ee92b29971c3"
    style="display: none !important; height: 1px !important; overflow: hidden !important; position: absolute !important; width: 1px !important;"></iframe>
  <script type="text/javascript" id="">twttr.conversion.trackPid("o78om", { tw_sale_amount: 0, tw_order_quantity: 0 });</script>
  <noscript>
    <img height="1" width="1" style="display:none;" alt="" src="https://analytics.twitter.com/i/adsct?txn_id=o78om&amp;p_id=Twitter&amp;tw_sale_amount=0&amp;tw_order_quantity=0">
    <img height="1" width="1" style="display:none;" alt="" src="//t.co/i/adsct?txn_id=o78om&amp;p_id=Twitter&amp;tw_sale_amount=0&amp;tw_order_quantity=0">
  </noscript>
  <img src="https://t.co/i/adsct?bci=1&amp;eci=1&amp;event_id=78fc61c7-8301-4170-97bd-ae5718c41aa7&amp;integration=advertiser&amp;p_id=Twitter&amp;p_user_id=0&amp;pl_id=d9477048-4d50-4d17-95be-e4708048a8a3&amp;tw_document_href=https%3A%2F%2Ftidal.com%2Fbrowse%2Ftrack%2F240175608&amp;tw_iframe_status=0&amp;tw_order_quantity=0&amp;tw_sale_amount=0&amp;txn_id=o78om&amp;type=javascript&amp;version=2.3.29" height="1" width="1" style="display: none;"><img src="https://analytics.twitter.com/i/adsct?bci=1&amp;eci=1&amp;event_id=78fc61c7-8301-4170-97bd-ae5718c41aa7&amp;integration=advertiser&amp;p_id=Twitter&amp;p_user_id=0&amp;pl_id=d9477048-4d50-4d17-95be-e4708048a8a3&amp;tw_document_href=https%3A%2F%2Ftidal.com%2Fbrowse%2Ftrack%2F240175608&amp;tw_iframe_status=0&amp;tw_order_quantity=0&amp;tw_sale_amount=0&amp;txn_id=o78om&amp;type=javascript&amp;version=2.3.29" height="1" width="1" style="display: none;">
</body><iframe id="__JSBridgeIframe_1.0__" title="jsbridge___JSBridgeIframe_1.0__" style="display: none;"></iframe><iframe id="__JSBridgeIframe_SetResult_1.0__" title="jsbridge___JSBridgeIframe_SetResult_1.0__" style="display: none;"></iframe><iframe id="__JSBridgeIframe__" title="jsbridge___JSBridgeIframe__" style="display: none;"></iframe><iframe id="__JSBridgeIframe_SetResult__" title="jsbridge___JSBridgeIframe_SetResult__" style="display: none;"></iframe>

</html>