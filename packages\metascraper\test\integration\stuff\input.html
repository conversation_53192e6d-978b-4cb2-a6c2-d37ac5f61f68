<!DOCTYPE html>
<html lang="en">
<head>
  <!-- META -->
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="robots" content="noarchive, max-image-preview:large"/>
  <meta name="format-detection" content="telephone=no">
  <meta property="fb:app_id" content="***************">
  <meta name="verify-v1" content="RazNiYjuvNuEsMeFXxfR9l9cDZIKxcq2VjQZA25CHgM=">

  <link rel="preconnect" href="https://assets.stuff.co.nz"/>
  <link rel="preconnect" href="https://resources.stuff.co.nz"/>
  <link rel="preconnect" href="https://www.googletagservices.com"/>
  <link rel="preconnect" href="https://assets.adobedtm.com"/>
  <link rel="preconnect" href="https://my.stuff.co.nz"/>
  <link rel="preconnect" href="https://adfeeds.stuff.co.nz"/>
  <link rel="preconnect" href="https://adapi.stuff.co.nz"/>
  <link rel="preconnect" href="https://fairfax.demdex.net"/>
  <link rel="preconnect" href="//secure.adnxs.com"/>
  <link rel="preconnect" href="//securepubads.g.doubleclick.net"/>
  <link rel="preconnect" href="//s.go-mpulse.net"/>

  <link rel="preconnect" href="//c.aaxads.com">
  <link rel="preconnect" href="//videos.oovvuu.com">

  <link rel="preload" href="//assets.adobedtm.com/launch-ENc2c0d9c06c2d4b1a877b126c3b8fc473.min.js" as="script">
  <link rel="preload" href="//c.aaxads.com/aax.js?pub=AAX76609S&hst=www.stuff.co.nz&ver=1.2" as="script">
  <link rel="preload" href="//www.googletagservices.com/tag/js/gpt.js" as="script">
  <link rel="preload" href="https://www.stuff.co.nz/static/stuff-adfliction/latest/stuff-adfliction.js" as="script">
  <link rel="preload" href="https://www.stuff.co.nz/static/stuff-header-bidding/latest/stuff-header-bidding.js" as="script">
  <link rel="preload" href="https://www.stuff.co.nz/static/stuff-login-browser-sdk/1.1.2/stuff-login-sdk.js" as="script">
  <link rel="preload" href="https://videos.oovvuu.com/stuf/v1/ovu_rec.js" as="script">
  <link rel="preload" href="/static/spade/fonts/roboto.css" crossorigin="anonymous" as="style" />
  <link rel="preload" href="/static/spade/fonts/roboto-v20-latin_latin-ext/roboto-v20-latin_latin-ext-italic.woff2" crossorigin="anonymous" as="font" />
  <link rel="preload" href="/static/spade/fonts/roboto-v20-latin_latin-ext/roboto-v20-latin_latin-ext-700.woff2" crossorigin="anonymous" as="font" />
  <link rel="preload" href="/static/spade/fonts/roboto-v20-latin_latin-ext/roboto-v20-latin_latin-ext-900.woff2" crossorigin="anonymous" as="font" />
  <link rel="preload" href="/static/spade/fonts/roboto-v20-latin_latin-ext/roboto-v20-latin_latin-ext-regular.woff2" crossorigin="anonymous" as="font" />

  <!-- FAVICONS -->
  <link rel="apple-touch-icon" sizes="180x180" href="/sics-assets/images/favicons/apple-touch-icon.png">
  <link rel="icon" type="image/png" sizes="32x32" href="/sics-assets/images/favicons/favicon-32x32.png">
  <link rel="icon" type="image/png" sizes="16x16" href="/sics-assets/images/favicons/favicon-16x16.png">
  <link rel="manifest" href="/sics-assets/images/favicons/manifest.json">
  <link rel="mask-icon" href="/sics-assets/images/favicons/safari-pinned-tab.svg" color="#5bbad5">
  <link rel="shortcut icon" href="/sics-assets/images/favicons/favicon.ico">
  <meta name="msapplication-config" content="/sics-assets/images/favicons/browserconfig.xml">
  <meta name="theme-color" content="#ffffff">

  <link rel="stylesheet" type="text/css" href="/static/spade/fonts/roboto.css" crossorigin="anonymous" />

  <script async="" type="text/javascript">  var googletag = googletag || {};
    googletag.cmd = googletag.cmd || [];
    (function () {
      var gads = document.createElement("script");
      gads.async = true;
      gads.type = "text/javascript";
      var useSSL = "https:" == document.location.protocol;
      gads.src = (useSSL ? "https:" : "http:") + "//www.googletagservices.com/tag/js/gpt.js";
      var node = document.getElementsByTagName("script")[0];
      node.parentNode.insertBefore(gads, node);
    })();
  </script>

  <!-- APPLICATION SOURCE -->
<script type="text/javascript" src="/sics-assets/js/promise.708cad0e2c1413d28a53.js"></script>
  <script>
    var sics = "1";
    var nz = window.nz || {};
    nz.ffx = nz.ffx || {};
    nz.stuff = nz.stuff || {},
      nz.stuff.applicationName = "sics";
    nz.ffx.dependencies = nz.ffx.dependencies || {};
    nz.ffx.dependencies.main = nz.ffx.dependencies.main || {};
    nz.ffx.dependencies.main.promise = Promise.resolve();
    nz.ffx.dependencies.domLoaded = nz.ffx.dependencies.domLoaded || {};
    nz.ffx.dependencies.domLoaded.promise = Promise.resolve();
  </script>

  <!-- AKAMAI MPULSE -->
  <script>
    (function () {
      if (window.BOOMR && window.BOOMR.version) {
        return;
      }
      var dom, doc, where, iframe = document.createElement("iframe"), win = window;

      function boomerangSaveLoadTime(e) {
        win.BOOMR_onload = (e && e.timeStamp) || new Date().getTime();
      }

      if (win.addEventListener) {
        win.addEventListener("load", boomerangSaveLoadTime, false);
      } else if (win.attachEvent) {
        win.attachEvent("onload", boomerangSaveLoadTime);
      }

      iframe.src = "javascript:false";
      iframe.title = "";
      iframe.role = "presentation";
      (iframe.frameElement || iframe).style.cssText = "width:0;height:0;border:0;display:none;";
      where = document.getElementsByTagName("script")[0];
      where.parentNode.insertBefore(iframe, where);

      try {
        doc = iframe.contentWindow.document;
      } catch (e) {
        dom = document.domain;
        iframe.src = "javascript:var d=document.open();d.domain='" + dom + "';void(0);";
        doc = iframe.contentWindow.document;
      }
      doc.open()._l = function () {
        var js = this.createElement("script");
        if (dom) this.domain = dom;
        js.id = "boomr-if-as";
        js.async = true;
        js.src = "https://s.go-mpulse.net/boomerang/" + "R9YSD-XHS3U-KQGGF-8W2UT-K37B7";
        BOOMR_lstart = new Date().getTime();
        this.body.appendChild(js);
      };
      doc.write('<body onload="document._l();">');
      doc.close();
    })();
  </script>

  <!-- DATA LAYER -->
  <script>
    window.digitalData = {
  "events": [],
  "page": {
    "suppressPageviewBeacon": true,
    "pageInfo": {
      "articleID": "*********",
      "keywords": [
        "Teacher",
        "LAW",
        "COUNCIL",
        "study",
        "Ross",
        "Voter",
        "more",
        "Who",
        "family",
        "SCHOOL",
        "CITY COUNCIL",
        "General Election",
        "WOOD",
        "HISTORY",
        "Man",
        "Ministry",
        "FINAL",
        "Election",
        "LANDFILL",
        "Stuff",
        "MP",
        "Rules",
        "Refugee",
        "EDUCATION",
        "WORK",
        "CITY",
        "english",
        "TANGI",
        "Housing",
        "Politics",
        "Masters",
        "HIGH SCHOOL",
        "run",
        "WIN",
        "FEBRUARY",
        "BRAIN",
        "Community",
        "NZ FIRST",
        "ball",
        "PALMERSTON NORTH",
        "New",
        "National",
        "general",
        "INTERVIEW",
        "Electoral",
        "NORTH",
        "MAYOR",
        "Palmerston",
        "FRUIT",
        "VOTE",
        "Congo",
        "first",
        "Ministry of Education",
        "Awapuni"
      ],
      "pageName": "Orphee Mickalad leading Palmerston North by-election ",
      "variant": "1",
      "version": "1",
      "publisher": "stuff",
      "source": "Stuff",
      "sysEnv": "mobile",
      "pageID": "*********",
      "headline": "Orphee Mickalad leading Palmerston North by-election ",
      "author": "Jono Galuszka",
      "lastPublishedTime": "2021-02-17 14:40:47"
    },
    "category": {
      "pageType": "article",
      "primaryCategory": "manawatu-standard",
      "subCategory1": "news",
      "subCategory2": null,
      "subCategory3": null,
      "subCategory4": null
    },
    "ads": {
      "exclusions": [],
      "environment": "prod",
      "sections": [
        "manawatu-standard",
        "news"
      ]
    }
  },
  "user": [
    {
      "profile": [
        {
          "profileInfo": {}
        }
      ],
      "segment": {}
    }
  ]
}
  </script>

  <!-- LAUNCH -->
  <script src="//assets.adobedtm.com/launch-ENc2c0d9c06c2d4b1a877b126c3b8fc473.min.js"></script>

  <!-- NATIVFORM (AKA ADFLICTION) DATALAYER -->
  <script>
    window.nz.ffx.config = window.nz.ffx.config || {};
    window.nz.ffx.config.adfliction = {
  "stuffAdflictionUri": "/static/stuff-adfliction/latest/stuff-adfliction.js",
  "channelApiUrl": "https://adfeeds.stuff.co.nz/v1/channel",
  "clientLocationUrl": "/static/scripts/stuff-plugins/client-location/1.0.1/client-location.js",
  "configUrl": "https://adapi.stuff.co.nz/smudge/appconfig/configmobileweb-100-adv3.json",
  "feedUrl": "https://adapi.stuff.co.nz/adcontrol/mobileweb.json",
  "extraConfigUrl": "https://adapi.stuff.co.nz/adcontrol/config/config-nativform-article-mobileweb-v1.json",
  "extraConfigUrlCacheTime": 300,
  "trackerUrl": "adclick.stuff.co.nz",
  "platform": "desktop",
  "placementConfigs": [
    {
      "selector": ".sics-component__story__body--nativform > p",
      "adTypes": [
        "Parallax Image",
        "GPT"
      ]
    }
  ]
}
  </script>

    <!-- GIGYA CONFIG -->
    <script>
      // Set gigya global vars before loading gigya script
      // @see http://developers.gigya.com/display/GD/Global+Conf+JS
      window.__gigyaConf = {
        forceProvidersLogout: false,
        enableSSOToken: true
      };
    </script>

  <!-- STUFF PLUGINS -->
  <script async src="https://www.stuff.co.nz/static/scripts/stuff-plugins/stuff-plugins.min.js"></script>


    <!-- ISOMORPHIC (nfl/react-helmet) -->

    <title data-react-helmet="true">Orphee Mickalad leading Palmerston North by-election  | Stuff.co.nz</title>
    <meta data-react-helmet="true" name="description" content="Orphee Mickalad is on track to replace his former history teacher Tangi Utikere on Palmerston North City Council."/><meta data-react-helmet="true" name="source" content="Stuff"/><meta data-react-helmet="true" property="og:description" content="Orphee Mickalad is on track to replace his former history teacher Tangi Utikere on Palmerston North City Council."/><meta data-react-helmet="true" property="og:title" content="Orphee Mickalad leading Palmerston North by-election "/><meta data-react-helmet="true" property="og:url" content="https://www.stuff.co.nz/manawatu-standard/news/*********/orphee-mickalad-leading-palmerston-north-byelection"/><meta data-react-helmet="true" property="og:type" content="article"/><meta data-react-helmet="true" property="og:image" content="https://resources.stuff.co.nz/content/dam/images/4/y/p/h/8/h/image.related.StuffLandscapeSixteenByNine.1420x800.4yr12n.png/1613526047477.jpg"/><meta data-react-helmet="true" property="og:site_name" content="Stuff"/><meta data-react-helmet="true" property="article:published_time" content="2021-02-17T01:40:47.222Z"/><meta data-react-helmet="true" property="fb:pages" content="21253884267"/><meta data-react-helmet="true" name="parsely-type" content="post"/><meta data-react-helmet="true" name="parsely-title" content="Orphee Mickalad leading Palmerston North by-election "/><meta data-react-helmet="true" name="parsely-link" content="https://www.stuff.co.nz/manawatu-standard/news/*********"/><meta data-react-helmet="true" name="parsely-image-url" content="https://resources.stuff.co.nz/content/dam/images/4/y/p/h/8/h/image.related.StuffLandscapeSixteenByNine.1420x800.4yr12n.png/1613526047477.jpg"/><meta data-react-helmet="true" name="parsely-pub-date" content="2021-02-17T01:40:47.222Z"/><meta data-react-helmet="true" name="parsely-author" content="Jono Galuszka"/><meta data-react-helmet="true" name="parsely-section" content="manawatu-standard"/><meta data-react-helmet="true" name="parsely-tags" content="manawatu-standard,news"/><meta data-react-helmet="true" name="parsely-post-id" content="*********"/><meta data-react-helmet="true" property="article:section" content="manawatu-standard"/><meta data-react-helmet="true" property="article:modified" content="2021-02-17T01:40:47.222Z"/>
    <script data-react-helmet="true" src="https://static.apester.com/js/sdk/latest/apester-sdk.js" async="true"></script>
    <link data-react-helmet="true" rel="canonical" href="https://www.stuff.co.nz/manawatu-standard/news/*********/orphee-mickalad-leading-palmerston-north-byelection"/>
    <!-- STYLES -->
    <link rel="stylesheet" href="/sics-assets/css/style.a9b6b5c4c133356.css">


  <!-- APP_CONFIG -->
  <script>
    window.APP_CONFIG = {applicationName:'sics',applicationVariant:'1',baseUrl:'https://www.stuff.co.nz',stuffNationUgcUrl:'https://ugc.stuff.co.nz/assignment',advertising:{noAdsSections:['sponsored-content']},noMostPopularBox:['national/kea-kids-news'],mode:'application',defaultLogLevel:'INFO',apiNormativeLastStoryId:********,useClientRouting:false,runtimeNodeEnv:'production',stuffUrlRegex:/^http(s)?:\/\/(([^\.]+)\.)?stuff\.co\.nz\//,stuffArticleUrlRegex:/^\/.*\/\d\d*($|\/)/,tokens:{facebook:'***************',sentryIo:'https://<EMAIL>/150508'},sanctuary:{enabled:false,rootUrl:'https://accounts.stuff.co.nz',profileUrl:'https://my.stuff.co.nz/home'},sentryIo:{dsn:'https://<EMAIL>/150508',environment:'stuff-isomorphic-component-server-publish-production',sampleRate:0.01,tags:{},ignoreUrls:[/outbrain\.com/,/teads\.tv/,/vidazoo\.com/],ignoreErrors:['/^Can\\\'t execute code from a freed script$','Invalid calling Object','Illegal invocation','Error retrieving article'],whitelistUrls:['www.stuff.co.nz/sics-assets/js','i.stuff.co.nz/sics-assets/js','www-preprod.stuff.co.nz/sics-assets/js','i-preprod.stuff.co.nz/sics-assets/js'],captureUnhandledRejections:false,captureHttpBreadcrumbs:true,captureConsoleBreadcrumbs:true,consoleErrors:/\/^(?!.*VIDEOJS: ERROR: videojs-contrib-ads).*VIDEOJS.*$\//,lowPriorityErrors:/\/^Can\'t execute code from a freed script$|^Invalid calling Object$|^Illegal invocation$|^Unable to set nz\.ffx\.GigyaStuff\:getCommentCount$|^Error retrieving article|^Article with id: \d+ errored|^\'abort\' called on an object that does not implement interface XMLHttpRequest\//,lowPrioritySampleRate:0.001,updatedSDKEnabled:true},componentClassnamePrefix:'sics-component',defaultImageType:{imageTypeId:'StuffLandscapeSixteenByNine',urlMap:{'200x200':'/sics-assets/images/svg/default-poster.svg'}},defaultLogoImageType:{imageTypeId:'StuffLandscapeSixteenByNine',urlMap:{'200x200':'/sics-assets/images/png/stuff-200x200.png'}},brightcove:{dataAccount:'*************',dataPlayer:'Syx4Zr1Keb',experiment:true,experimentSection:'all'},gigya:{apiKey:'3_9JitkeW_HEZvUcTSahg2tBTNm_psp2j-F58dCHDCilHVDYpGUAnC0vHmZMfro1_V'},scrappers:'coral',coral:{featureFlag:true,basePath:'https://stuff.coral.coralproject.net',embedUri:'/assets/js/embed.js',section:'all',tokenServiceApi:'https://api.stuff.co.nz/token/coral',sicsUriPath:'/article-metadata/coral/',commentsCountApi:'https://stuff.coral.coralproject.net/api/story/count'},mPulse:{id:'R9YSD-XHS3U-KQGGF-8W2UT-K37B7'},ports:{stats:8082},suppressEsi:false,useGraphqlPersistedIds:true,services:{ttl:{time:600000},nativForm:{stuffAdflictionUri:'/static/stuff-adfliction/latest/stuff-adfliction.js',channelApiUrl:'https://adfeeds.stuff.co.nz/v1/channel',clientLocationUrl:'/static/scripts/stuff-plugins/client-location/1.0.1/client-location.js',configUrl:'https://adapi.stuff.co.nz/smudge/appconfig/configmobileweb-100-adv3.json',feedUrl:'https://adapi.stuff.co.nz/adcontrol/mobileweb.json',extraConfigUrl:'https://adapi.stuff.co.nz/adcontrol/config/config-nativform-article-mobileweb-v1.json',extraConfigUrlCacheTime:300,trackerUrl:'adclick.stuff.co.nz',platform:'mobileweb',placementConfigAdTypes:['Parallax Image','GPT'],placementConfigSelector:'.sics-component__story__body--nativform > p'}},components:{headElement:{rendition:'1420x630'},mostPopular:{rendition:'200x200',limit:10,version:'V2'},htmlInjector:{voidElements:['area','base','br','col','command','embed','hr','img','input','keygen','link','meta','param','source','track','wbr']},story:{assetOrdering:true},storyImage:{rendition:'710x400',useSrcSetInMobile:false},storyGallery:{rendition:'600x400'},storyVideo:{rendition:'620x350'},storyQuiz:{scriptUrl:'//www.stuff.co.nz/assets/static/quizbattles/stuff-quiz.js'}},apis:{quiz:'/xml/quiz',isomorphicExternalBasepath:'https://api.stuff.co.nz/isomorphic-api',application:'https://api.stuff.co.nz/sics'},embeds:{intrinsicScaleSources:['youtube.com','youtube-nocookie.com','washingtonpost.com/video/','google.co(.nz|m)/maps/','facebook.com/plugins/video.php','soundcloud.com'],transformScaleSources:['livestream.com','video-api.wsj.com/api-video/player/iframe.html','assets.stuff.co.nz/interactives/'],pixelScaleSources:['nzonscreen.com/embed','giphy.com/embed/'],iframeResizerSource:'https://cdnjs.cloudflare.com/ajax/libs/iframe-resizer/3.5.14/iframeResizer.min.js',blacklistRules:[/opta-scoreboard/,/vidible.tv/]},widgets:{colliers:{business:'https://cdn.neighbourly.co.nz/widget/real-estate/177859/desktop'},play_stuff_desktop:{world:'https://www.playwidget.stuff.co.nz/hshelf/5d3a9acfa0e845001af9b559',business:'https://www.playwidget.stuff.co.nz/hshelf/5d3a9af323eec6001bb9564a',sport:'https://www.playwidget.stuff.co.nz/hshelf/5d3a9b16a0e845001ab5a8d9',entertainment:'https://www.playwidget.stuff.co.nz/hshelf/5d3fc3e1a0e845001d40a56e',travel:'https://www.playwidget.stuff.co.nz/hshelf/5d3fc51423eec6001d80b8dd',motoring:'https://www.playwidget.stuff.co.nz/hshelf/5d3fca20a0e845001caee778',technology:'https://www.playwidget.stuff.co.nz/hshelf/5d3fca9ba0e845001c7c9d35','life-style':'https://www.playwidget.stuff.co.nz/hshelf/5d3fc44423eec6001d7741a3','life-style/food-wine':'https://www.playwidget.stuff.co.nz/hshelf/5d3fc4891de1c4001c1128cf','life-style/well-good':'https://www.playwidget.stuff.co.nz/hshelf/5d3fc4cd1de1c4001c1128d7','entertainment/bravo':'https://www.playwidget.stuff.co.nz/hshelf/5d3fca7123eec6001bb959ee','life-style/homed':'https://www.playwidget.stuff.co.nz/hshelf/5d3fcac423eec6001d7741c2'},play_stuff_mobile:{world:'https://www.playwidget.stuff.co.nz/shelf/5d3a9ab623eec6001c7998c3',business:'https://www.playwidget.stuff.co.nz/shelf/5d3a9ae0a6f547001c4a42d0',sport:'https://www.playwidget.stuff.co.nz/shelf/5d3a9b05a0e845001ca1538c',entertainment:'https://www.playwidget.stuff.co.nz/shelf/5d3a9b2f23eec6001bb95659',travel:'https://www.playwidget.stuff.co.nz/shelf/5d3fc4f423eec6001d7741ba',motoring:'https://www.playwidget.stuff.co.nz/shelf/5d3fc767a0e845001af9b89f',technology:'https://www.playwidget.stuff.co.nz/shelf/5d3fca8623eec6001d80b8ed','life-style':'https://www.playwidget.stuff.co.nz/shelf/5d3fc41e23eec6001e563341','life-style/food-wine':'https://www.playwidget.stuff.co.nz/shelf/5d3fc468a0e845001c7c9d2d','life-style/well-good':'https://www.playwidget.stuff.co.nz/shelf/5d3fc4a923eec6001d7741ab','entertainment/bravo':'https://www.playwidget.stuff.co.nz/shelf/5d3fca55a0e845001af9b8a7','life-style/homed':'https://www.playwidget.stuff.co.nz/shelf/5d3fcaafa0e845001ab5ad1b','life-style/complex':''},play_stuff_complex:{'life-style/complex':'https://www.playwidget.stuff.co.nz/grid/5fd90b32a6f547001b91ac8e'},neighbourly:{'*':'https://www.neighbourly.co.nz/stuff/1233'}},parselySections:{prosper:'business/prosper','Climate-Change':'environment/climate-news',politics:'national/politics',homed:'life-style/homed'},asyncFlag:'async',lazyLoading:{widget:true},mobileDomainRegex:/^(.*i|i-.*)\.stuff\.co\.nz/,assetsBaseUri:{excludeDomains:/((www|i)(-preprod)?\.stuff\.co\.nz)/},stuffPluginsUri:'/static/scripts/stuff-plugins/stuff-plugins.min.js',stuffHeaderBiddingUri:'/static/stuff-header-bidding/latest/stuff-header-bidding.js',stuffAdobeLaunchUri:'//assets.adobedtm.com/launch-ENc2c0d9c06c2d4b1a877b126c3b8fc473.min.js',pressPatron:{isAdded:true,url:'//dashboard.presspatron.com/dev/banner?b=TJu26zZFBKa635NQ13AZRn8S'},oovvuuUri:'https://videos.oovvuu.com/stuf/v1/ovu_rec.js',idm:{authority:'https://my.stuff.co.nz',clientId:'6380a421-afcd-45b4-b9ab-393d3f105da3',stuffLoginSdkUri:'/static/stuff-login-browser-sdk/1.1.2/stuff-login-sdk.js',loginRedirectUri:'/static/stuff-login-browser-sdk/1.1.2/callback/signin-callback.html',silentRequestTimeout:15000,responseType:'id_token token',scope:'openid profile email address',userProfilePictureRegex:/stuff\.co\.nz/g}}
  </script>



  <script>
    window.addEventListener('load', function () {
      (window.adsbygoogle = window.adsbygoogle || []).push({});
    });
  </script>
</head>
<body>
<!-- APPLICATION -->
<div itemType="https://schema.org/NewsArticle" itemScope id="content"><div class="sics-component__app" data-reactroot=""><span></span><header class="sics-component__header"><div class="sics-component__header__fixed sics-component__header__fixed--down" itemProp="publisher" itemscope="" itemType="http://schema.org/NewsMediaOrganization" style="top:0"><meta itemProp="name" content="Stuff.co.nz"/><div><div class="sics-component__main-menu "><div class="sics-component__main-menu__wrap"><nav class="sics-component__main-menu__menu"><div class="sics-component__main-menu__menu-bg"><div class="sics-component__main-menu__container"><div class="sics-component__main-menu__menu-inner"><div class="search-bar"><form action="/searchresults" method="get"><input type="search" name="q" placeholder="Search" aria-label="search"/><label><input type="submit"/><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30.239 30.239"><path d="M20.194 3.46c-4.613-4.613-12.121-4.613-16.734 0-4.612 4.614-4.612 12.121 0 16.735 4.108 4.107 10.506 4.547 15.116 1.34.097.459.319.897.676 1.254l6.718 6.718a2.498 2.498 0 003.535 0 2.496 2.496 0 000-3.535l-6.718-6.72a2.5 2.5 0 00-1.253-.674c3.209-4.611 2.769-11.008-1.34-15.118zm-2.121 14.614c-3.444 3.444-9.049 3.444-12.492 0-3.442-3.444-3.442-9.048 0-12.492 3.443-3.443 9.048-3.443 12.492 0 3.444 3.444 3.444 9.048 0 12.492z"></path></svg></label></form></div></div><div class="sics-component__main-menu__menu-inner"><div class="sics-component__main-menu__menu-col"><ul class="sics-component__main-menu__menu-list"><li class="sics-component__main-menu__section-title"><h5>news</h5><button></button><i class="arrow"></i></li><li class="sics-component__main-menu__menu-item"><a href="https://www.stuff.co.nz/national">National</a></li><li class="sics-component__main-menu__menu-item"><a href="https://www.stuff.co.nz/world">World</a></li><li class="sics-component__main-menu__menu-item"><a href="https://www.stuff.co.nz/environment/climate-news">Climate Change</a></li><li class="sics-component__main-menu__menu-item"><a href="https://www.stuff.co.nz/national/politics">Politics</a></li><li class="sics-component__main-menu__menu-item"><a href="https://www.stuff.co.nz/business">Business</a></li><li class="sics-component__main-menu__menu-item"><a href="https://www.stuff.co.nz/business/prosper">prosper</a></li><li class="sics-component__main-menu__menu-item"><a href="https://www.stuff.co.nz/business/farming">Farming</a></li><li class="sics-component__main-menu__menu-item"><a href="https://www.stuff.co.nz/technology">Technology</a></li><li class="sics-component__main-menu__menu-item"><a href="https://www.stuff.co.nz/sport">Sport</a></li><li class="sics-component__main-menu__menu-item"><a href="https://www.stuff.co.nz/sport/rugby">Rugby</a></li></ul></div><div class="sics-component__main-menu__menu-col"><ul class="sics-component__main-menu__menu-list"><li class="sics-component__main-menu__section-title"><h5>voices &amp; in depth</h5><button></button><i class="arrow"></i></li><li class="sics-component__main-menu__menu-item"><a href="https://www.stuff.co.nz/opinion">perspectives</a></li><li class="sics-component__main-menu__menu-item"><a href="https://www.stuff.co.nz/pou-tiaki">Pou Tiaki</a></li><li class="sics-component__main-menu__menu-item"><a href="https://www.stuff.co.nz/national/premium">Spotlight</a></li><li class="sics-component__main-menu__menu-item"><a href="https://www.stuff.co.nz/stuff-nation">Stuff Nation</a></li><li class="sics-component__main-menu__menu-item"><a href="https://www.stuff.co.nz/opinion/cartoons">Cartoons</a></li><li class="sics-component__main-menu__menu-item"><a href="https://www.stuff.co.nz/national/kea-kids-news ">KEA Kids News</a></li></ul></div><div class="sics-component__main-menu__menu-col"><ul class="sics-component__main-menu__menu-list"><li class="sics-component__main-menu__section-title"><h5>living</h5><button></button><i class="arrow"></i></li><li class="sics-component__main-menu__menu-item"><a href="https://www.stuff.co.nz/travel">Travel</a></li><li class="sics-component__main-menu__menu-item"><a href="https://www.stuff.co.nz/life-style/homed">Homed</a></li><li class="sics-component__main-menu__menu-item"><a href="https://www.stuff.co.nz/life-style">LifeStyle</a></li><li class="sics-component__main-menu__menu-item"><a href="https://www.stuff.co.nz/entertainment">Entertainment</a></li><li class="sics-component__main-menu__menu-item"><a href="https://www.stuff.co.nz/bravo">bravo</a></li><li class="sics-component__main-menu__menu-item"><a href="https://www.stuff.co.nz/life-style/complex">Complex</a></li><li class="sics-component__main-menu__menu-item"><a href="https://www.stuff.co.nz/motoring">Motoring</a></li><li class="sics-component__main-menu__menu-item"><a href="https://www.stuff.co.nz/life-style/food-wine">Food &amp; Wine</a></li><li class="sics-component__main-menu__menu-item"><a href="https://www.stuff.co.nz/oddstuff">Oddstuff</a></li></ul></div><div class="sics-component__main-menu__menu-col"><ul class="sics-component__main-menu__menu-list"><li class="sics-component__main-menu__section-title"><h5>regions</h5><button></button><i class="arrow"></i></li><li class="sics-component__main-menu__menu-item"><a href="https://www.stuff.co.nz/northland">northland</a></li><li class="sics-component__main-menu__menu-item"><a href="https://www.stuff.co.nz/auckland">Auckland</a></li><li class="sics-component__main-menu__menu-item"><a href="https://www.stuff.co.nz/waikato-times">Waikato</a></li><li class="sics-component__main-menu__menu-item"><a href="https://www.stuff.co.nz/bay-of-plenty">Bay of Plenty</a></li><li class="sics-component__main-menu__menu-item"><a href="https://www.stuff.co.nz/taranaki-daily-news">Taranaki</a></li><li class="sics-component__main-menu__menu-item"><a href="https://www.stuff.co.nz/dominion-post/news/hawkes-bay">hawke&#x27;s bay</a></li><li class="sics-component__main-menu__menu-item"><a href="https://www.stuff.co.nz/manawatu-standard">manawatu</a></li><li class="sics-component__main-menu__menu-item"><a href="https://www.stuff.co.nz/dominion-post">wellington</a></li><li class="sics-component__main-menu__menu-item"><a href="https://www.stuff.co.nz/nelson-mail">nelson</a></li><li class="sics-component__main-menu__menu-item"><a href="https://www.stuff.co.nz/marlborough-express">marlborough</a></li><li class="sics-component__main-menu__menu-item"><a href="https://www.stuff.co.nz/the-press">canterbury</a></li><li class="sics-component__main-menu__menu-item"><a href="https://www.stuff.co.nz/timaru-herald">south canterbury</a></li><li class="sics-component__main-menu__menu-item"><a href="https://www.stuff.co.nz/otago">otago</a></li><li class="sics-component__main-menu__menu-item"><a href="https://www.stuff.co.nz/southland-times">southland</a></li></ul></div><div class="sics-component__main-menu__menu-col"><ul class="sics-component__main-menu__menu-list"><li class="sics-component__main-menu__section-title"><h5>more</h5><button></button><i class="arrow"></i></li><li class="sics-component__main-menu__menu-item"><a href="https://www.stuff.co.nz/national/weather/">Weather</a></li><li class="sics-component__main-menu__menu-item"><a href="https://www.stuff.co.nz/national/quizzes">Quizzes</a></li><li class="sics-component__main-menu__menu-item"><a href="https://www.stuff.co.nz/life-style/puzzles">Puzzles</a></li><li class="sics-component__main-menu__menu-item"><a href="https://www.stuff.co.nz/national/90621395/feed-your-news-hunger-with-stuffs-many-delicious-newsletters">Newsletters</a></li></ul></div><div class="sics-component__main-menu__menu-col"><ul class="sics-component__main-menu__menu-list"><li class="sics-component__main-menu__section-title"><h5>about stuff</h5><button></button><i class="arrow"></i></li><li class="sics-component__main-menu__menu-item"><a href="https://interactives.stuff.co.nz/2020/03/support-stuffs-journalism/?utm_source=stuff&amp;utm_medium=hppointer&amp;utm_campaign=supporter-program&amp;utm_content=trust">contribute</a></li><li class="sics-component__main-menu__menu-item"><a href="https://advertise.stuff.co.nz/">Advertising</a></li><li class="sics-component__main-menu__menu-item"><a href="https://stuff.co.nz/careers">Careers</a></li><li class="sics-component__main-menu__menu-item"><a href="https://stuff.co.nz/about-stuff/10648385/Privacy-Policy">Privacy</a></li><li class="sics-component__main-menu__menu-item"><a href="https://stuff.co.nz/about-stuff/33785/Contact-Us">Contact</a></li></ul><ul class="sics-component__main-menu__menu-list"><li class="sics-component__main-menu__section-title"><h5>stuff family</h5><button></button><i class="arrow"></i></li><li class="sics-component__main-menu__menu-item"><a href="https://play.stuff.co.nz" target="_blank">Play Stuff</a></li><li class="sics-component__main-menu__menu-item"><a href="https://neighbourly.co.nz" target="_blank">neighbourly</a></li><li class="sics-component__main-menu__menu-item"><a href="https://mags4gifts.co.nz" target="_blank">mags4gifts</a></li><li class="sics-component__main-menu__menu-item"><a href="https://www.stuffevents.co.nz" target="_blank">stuff events</a></li><li class="sics-component__main-menu__menu-item"><a href="https://coupons.stuff.co.nz" target="_blank">stuff coupons</a></li></ul></div></div></div></div><div class="sics-component__main-menu__menu-translucent"></div></nav></div></div></div><div class="sics-component__header__top"><div class="sics-component__header__body"><div class="sics-component__header__left"><div class="sics-component__header__logo" itemProp="logo" itemscope="" itemType="https://schema.org/ImageObject"><meta itemProp="url" content="https://www.stuff.co.nz/sics-assets/images/png/stuff_logo.png"/><a class="sics-component__icon sics-component__icon--stuff-logo " href="/" title="Stuff - stuff.co.nz"></a></div><a class="sics-component__header__main-menu-toggle "><span></span></a></div><div class="sics-component__header__center"><nav aria-label="Top section main navigation" class="main-navigation"><ol class="sics-component__main-navigation__top-list"><li class="sics-component__main-navigation__section"><a class="sics-component__main-navigation__link" href="/manawatu-standard">Manawatū Standard</a></li></ol></nav></div><div class="sics-component__header__right"></div></div></div><span class="sics-component__color-bar"></span><nav class="sics-component__sub-nav sics-component__sub-nav--down" aria-label="Sub section navigation"><div class="sics-component__sub-nav__content-wrapper"><div class="sics-component__sub-nav__content"><a class="sics-component__sub-nav__heading-link sics-component__sub-nav__heading-link--selected" href="/manawatu-standard/news"><h2 class="sics-component__sub-nav__heading">News</h2></a></div></div></nav><span class="sics-component__color-bar"></span></div></header><div><div><div class="sics-component__breaking-news" style="display:none" data-reactroot=""></div></div></div><div class="sics-component__app__content"><main class="sics-component__app__main"><div class="sics-component__news-page"><div class="sics-component__news-page__container"><meta itemProp="mainEntityOfPage" content="https://www.stuff.co.nz/manawatu-standard/news/*********/orphee-mickalad-leading-palmerston-north-byelection"/><div class="sics-component__headline"><h1 class="sics-component__headline__title" itemProp="headline">Orphee Mickalad leading Palmerston North by-election </h1></div><div class="sics-component__byline-wrapper"><div class="sics-component__byline"><span itemProp="author" itemscope="" itemType="https://schema.org/NewsMediaOrganization" class="sics-component__byline__author"><span itemProp="name" content="Stuff.co.nz">Jono Galuszka</span></span><span><span itemProp="dateModified" content="2021-02-17T01:40:47.222Z" class="sics-component__byline__date">14:40, Feb 17 2021</span><meta itemProp="datePublished" content="2021-02-17T01:40:46.722Z"/></span></div><ul class="sics-component__sharebar"><li><a class="sics-component__sharebar__button sics-component__sharebar__button--facebook" href="javascript:void 0">Facebook</a></li><li><a class="sics-component__sharebar__button sics-component__sharebar__button--twitter" href="javascript:void 0">Twitter</a></li><li class="sics-component__sharebar__item"><a class="sics-component__sharebar__button sics-component__sharebar__button--whatsapp" href="javascript:void 0">Whats App</a></li><li><a class="sics-component__sharebar__button sics-component__sharebar__button--reddit" href="javascript:void 0">Reddit</a></li><li><a class="sics-component__sharebar__button sics-component__sharebar__button--email" href="mailto:?subject=Orphee%20Mickalad%20leading%20Palmerston%20North%20by-election%20&amp;body=https%3A%2F%2Fwww.stuff.co.nz%2Fmanawatu-standard%2Fnews%2F*********%2Forphee-mickalad-leading-palmerston-north-byelection">Email</a></li></ul></div><div class="sics-component__story"><div id="head-video" class="sics-component__oovvuu-recommendation"></div><figure class="sics-component__story-image"><div class="sics-component__story-image__image-wrapper" itemProp="image" itemscope="" itemType="http://schema.org/ImageObject"><meta itemProp="url" content="https://resources.stuff.co.nz/content/dam/images/4/y/p/h/8/h/image.related.StuffLandscapeSixteenByNine.710x400.4yr12n.png/1613526047477.jpg?format=pjpg&amp;optimize=medium"/><img class="lazyimg" alt="Orphee Mickalad is leading the Palmerston North City Council by-election." style="width:100%;display:inline-block"/></div><figcaption class="sics-component__caption"><div class="sics-component__caption__top-line"><span class="sics-component__caption__icon-text"></span><cite class="sics-component__caption__producer">David Unwin/Stuff</cite></div><div class="sics-component__caption__caption">Orphee Mickalad is leading the Palmerston North City Council by-election.</div></figcaption></figure><span class="sics-component__story__body sics-component__story__body--nativform"><p class="sics-component__html-injector sics-component__story__intro sics-component__story__paragraph">A man who barely spoke English when he arrived in Palmerston North as a refugee is set to replace his former history teacher on the city’s council.</p><p class="sics-component__html-injector sics-component__story__paragraph"> Progress results released on Wednesday afternoon had Orphee Mickalad​ leading in the Palmerston North City Council by-election.</p><p class="sics-component__html-injector sics-component__story__paragraph"> The by-election was required after former deputy mayor Tangi Utikere​ was elected as the city’s MP in the 2020 general election.</p><p class="sics-component__html-injector sics-component__story__paragraph"> Mickalad is leading more established names such as National’s candidate in the 2020 election, William Wood​, former NZ First MP Darroch Ball​, Andy Asquith​ – the highest polling person to miss out in the 2019 council election – and serial candidate Ross Barber​.</p><div id="fourth-par-video" class="sics-component__oovvuu-recommendation"></div><p class="sics-component__html-injector sics-component__story__paragraph"> <strong>READ MORE:<br> * <a href="https://www.stuff.co.nz/manawatu-standard/news/300210488/byelection-candidates-invited-to-pitch-for-your-votes?rm=a">By-election candidates invited to pitch for your votes</a><br> * <a href="https://www.stuff.co.nz/manawatu-standard/news/123227014/aleisha-rutherford-palmerston-norths-new-deputy-mayor?rm=a">Aleisha Rutherford Palmerston North's new deputy mayor</a><br> * <a href="https://www.stuff.co.nz/manawatu-standard/news/300136298/election-2020-palmerston-north-council-byelection-set-for-february-following-tangi-utikeres-win?rm=a">Election 2020: Palmerston North council by-election set for February following Tangi Utikere's win</a><br> </strong></p><p class="sics-component__html-injector sics-component__story__paragraph"> His exact lead was not published by the city council, and would be difficult to figure out due to the election being run under single transferrable vote rules.</p><span></span><p class="sics-component__html-injector sics-component__story__paragraph"> In <a href="https://www.stuff.co.nz/manawatu-standard/news/123386735/candidate-wants-better-prospects-for-palmerston-north-youth">an interview with <em>Stuff </em>when he declared his intention to run</a>, 30-year-old Mickalad said he moved to Palmerston North with his family from Congo in 2006 with minimal English skills.</p><p class="sics-component__html-injector sics-component__story__paragraph"> He went to Feryberg High School, where Utikere was his history teacher, worked at the Awapuni landfill, picked fruit, drove forklifts and cleaned offices.</p><p class="sics-component__html-injector sics-component__story__paragraph"> He went on to get a masters degree with a politics endorsement, study law and work in Parliamentary Services and as an adviser at the Ministry of Education.</p><p class="sics-component__html-injector sics-component__story__paragraph"> He ran on a platform of preventing a brain drain from Palmerston North, boosting the city’s social housing stock and improving communication between the council and community.</p><p class="sics-component__html-injector sics-component__story__paragraph"> Deputy electoral officer Hannah White​ said a final election result was expected on Friday.</p><p class="sics-component__html-injector sics-component__story__paragraph"> Voter turnout was 25.06 per cent, well down on the 2019 election turnout of 37.32 per cent.</p><span><div class="sics-component__html-asset sics-component__html-asset--no-scale"><div id="html-asset__*********" name="*********" width="100%"></div></div></span></span><div id="foot-video" class="sics-component__oovvuu-recommendation"></div><p class="sics-component__story__source">Stuff</p><ul class="sics-component__sharebar"><li><a class="sics-component__sharebar__button sics-component__sharebar__button--facebook" href="javascript:void 0">Facebook</a></li><li><a class="sics-component__sharebar__button sics-component__sharebar__button--twitter" href="javascript:void 0">Twitter</a></li><li class="sics-component__sharebar__item"><a class="sics-component__sharebar__button sics-component__sharebar__button--whatsapp" href="javascript:void 0">Whats App</a></li><li><a class="sics-component__sharebar__button sics-component__sharebar__button--reddit" href="javascript:void 0">Reddit</a></li><li><a class="sics-component__sharebar__button sics-component__sharebar__button--email" href="mailto:?subject=Orphee%20Mickalad%20leading%20Palmerston%20North%20by-election%20&amp;body=https%3A%2F%2Fwww.stuff.co.nz%2Fmanawatu-standard%2Fnews%2F*********%2Forphee-mickalad-leading-palmerston-north-byelection">Email</a></li></ul></div><span></span><div class="sics-component__outbrain"><div class="OUTBRAIN" data-src="https://www.stuff.co.nz/manawatu-standard/news/*********/orphee-mickalad-leading-palmerston-north-byelection" data-widget-id="AR_15" data-ob-template="Stuff.co.nz"></div></div><span></span><div class="sics-component__outbrain"><div class="OUTBRAIN" data-src="https://www.stuff.co.nz/manawatu-standard/news/*********/orphee-mickalad-leading-palmerston-north-byelection" data-widget-id="AR_5" data-ob-template="Stuff.co.nz"></div></div></div></div></main><aside class="sics-component__right-hand-column"><section class="sics-component__section sics-component__section--most-popular"><h2 class="sics-component__section__title">most popular</h2><div><div><div id="viewed" class="sics-component__most-popular ui-tabs-panel ui-widget-content ui-corner-bottom" aria-labelledby="ui-id-1" role="tabpanel" aria-expanded="true" aria-hidden="false" data-reactroot=""><ul class="sics-component__most-popular__list"><li class="sics-component__most-popular__list-item no-bullets mbs"><a class="sics-component__most-popular__link icon-story" href="https://www.stuff.co.nz/national/health/coronavirus/300232027/full-coverage-alert-level-changes-suggest-government-confident-covid19-outbreak-under-control-expert-says" data-icon-name="VIDEO">Full coverage: Alert level changes suggest Government confident Covid-19 outbreak under control, expert says<span class="sics-component__headline-flag"><span class="sics-component__icon sics-component__icon--video-logo sics-component__headline-flag__icon"></span></span></a></li><li class="sics-component__most-popular__list-item no-bullets mbs"><a class="sics-component__most-popular__link icon-story" href="https://www.stuff.co.nz/national/124281524/wellington-assault-victim-dies-after-life-support-switched-off" data-icon-name="no icon">Wellington assault victim dies after life support switched off</a></li><li class="sics-component__most-popular__list-item no-bullets mbs"><a class="sics-component__most-popular__link icon-story" href="https://www.stuff.co.nz/national/300233047/badly-injured-surfer-saved-on-remote-auckland-beach-after-writing-help-in-the-sand" data-icon-name="VIDEO">Badly injured surfer saved on remote Auckland beach after writing &#x27;HELP&#x27; in the sand<span class="sics-component__headline-flag"><span class="sics-component__icon sics-component__icon--video-logo sics-component__headline-flag__icon"></span></span></a></li><li class="sics-component__most-popular__list-item no-bullets mbs"><a class="sics-component__most-popular__link icon-story" href="https://www.stuff.co.nz/business/124276080/christchurchs-wealthy-gough-family-paid-out-78-million-from-the-sale-of-the-business" data-icon-name="no icon">Christchurch&#x27;s wealthy Gough family paid out $78 million from the sale of the business</a></li><li class="sics-component__most-popular__list-item no-bullets mbs"><a class="sics-component__most-popular__link icon-story" href="https://www.stuff.co.nz/business/124271328/jucy-rental-car-and-campervan-collapse-left-44m-debt-to-asb" data-icon-name="VIDEO">Jucy rental car and campervan collapse left $44m debt to ASB<span class="sics-component__headline-flag"><span class="sics-component__icon sics-component__icon--video-logo sics-component__headline-flag__icon"></span></span></a></li><li class="sics-component__most-popular__list-item no-bullets mbs"><a class="sics-component__most-popular__link icon-story" href="https://www.stuff.co.nz/national/health/coronavirus/300229944/covid19-all-the-locations-linked-to-south-aucklands-new-community-cases" data-icon-name="VIDEO">Covid-19: All the locations linked to south Auckland&#x27;s new community cases<span class="sics-component__headline-flag"><span class="sics-component__icon sics-component__icon--video-logo sics-component__headline-flag__icon"></span></span></a></li><li class="sics-component__most-popular__list-item no-bullets mbs"><a class="sics-component__most-popular__link icon-story" href="https://www.stuff.co.nz/national/300232950/friends-mourn-loss-of-cheeky-fun-man-who-lived-his-life-on-the-streets" data-icon-name="no icon">Friends mourn loss of &#x27;cheeky, fun&#x27; man who lived his life on the streets</a></li><li class="sics-component__most-popular__list-item no-bullets mbs"><a class="sics-component__most-popular__link icon-story" href="https://www.stuff.co.nz/national/quizzes/300232008/quiz-afternoon-trivia-challenge-february-17-2021" data-icon-name="no icon">Quiz: Afternoon trivia challenge: February 17, 2021</a></li><li class="sics-component__most-popular__list-item no-bullets mbs"><a class="sics-component__most-popular__link icon-story" href="https://www.stuff.co.nz/national/124270361/raymond-horns-sister-scared-as-hell-as-search-continues" data-icon-name="no icon">Raymond Horn&#x27;s sister &#x27;scared as hell&#x27; as search continues</a></li><li class="sics-component__most-popular__list-item no-bullets mbs"><a class="sics-component__most-popular__link icon-story" href="https://www.stuff.co.nz/sport/americas-cup/124279832/americas-cup-luna-rossas-hardline-stand-in-wanting-prada-cup-to-resume-on-friday-lacks-sportsmanship" data-icon-name="VIDEO">America&#x27;s Cup: Luna Rossa&#x27;s hardline stand in wanting Prada Cup to resume on Friday lacks &#x27;sportsmanship&#x27;<span class="sics-component__headline-flag"><span class="sics-component__icon sics-component__icon--video-logo sics-component__headline-flag__icon"></span></span></a></li></ul></div></div></div></section><div id="colliers"><div class="LazyLoad"></div></div><span></span></aside></div><footer class="sics-component__footer"><div class="sics-component__footer__top"><span class="sics-component__color-bar"></span></div><div class="sics-component__footer__links"><span class="sics-component__footer__link "><a href="https://www.neighbourly.co.nz" target="_blank" rel="noopener">Neighbourly</a></span><span class="sics-component__footer__link "><a href="https://travel-booking.stuff.co.nz/" target="_blank" rel="noopener">Travel Bookings</a></span><span class="sics-component__footer__link "><a href="https://play.stuff.co.nz/" target="_blank" rel="noopener">Play Stuff</a></span><span class="sics-component__footer__link "><a href="https://coupons.stuff.co.nz/" target="_blank" rel="noopener">Stuff Coupons</a></span><span class="sics-component__footer__link sics-component__footer__link--no-line"><a href="https://deaths.stuff.co.nz/obituaries/stuff-nz" target="_blank" rel="noopener">Death Notices</a></span><span class="sics-component__footer__link "><a href="https://www.stuffevents.co.nz/" target="_blank" rel="noopener">Stuff Events</a></span><span class="sics-component__footer__link "><a href="https://advertise.stuff.co.nz" target="_blank" rel="noopener">Advertising</a></span><span class="sics-component__footer__link "><a href="https://careers.stuff.co.nz" target="_blank" rel="noopener">Careers</a></span><span class="sics-component__footer__link sics-component__footer__link--no-line"><a href="https://stuff.co.nz/about-stuff/10648385/Privacy-Policy" target="_self" rel="noopener">Privacy Policy</a></span><span class="sics-component__footer__link "><a href="https://www.stuff.co.nz/about-stuff/300062240/stuff-cookies-policy-and-targeting-and-tracking-policy" target="_self" rel="noopener">Cookies Policy</a></span><span class="sics-component__footer__link "><a href="https://stuff.co.nz/about-stuff/10647720/Stuffs-terms-and-conditions" target="_self" rel="noopener">Terms &amp; Conditions</a></span><span class="sics-component__footer__link sics-component__footer__link--no-line"><a href="https://www.stuff.co.nz/about-stuff/300106664/stuff-editorial-code-of-practice-and-ethics" target="_self" rel="noopener">Editorial Code</a></span><span class="sics-component__footer__link "><a href="https://stuff.co.nz/about-stuff/33785/Contact-Us" target="_self" rel="noopener">Contact Us</a></span></div><div class="sics-component__footer__secondary"><div class="sics-component__footer__secondary-inner"><div class="sics-component__footer__secondary-breakingnews"><h4>Breaking news?</h4>Send your photos, videos and tip-offs to <br/><a href="mailto:<EMAIL>"><EMAIL></a>, or call us on 0800 697 8833</div><div class="sics-component__footer__secondary-social"><span class="sics-component__footer__secondary-social--icon facebook-footer"><a class="sics-component__icon sics-component__icon--facebook-footer " href="https://www.facebook.com/Stuff.co.nz" title="Facebook" target="_blank" rel="noopener"></a></span><span class="sics-component__footer__secondary-social--icon twitter-footer"><a class="sics-component__icon sics-component__icon--twitter-footer " href="https://twitter.com/NZStuff" title="Twitter" target="_blank" rel="noopener"></a></span><span class="sics-component__footer__secondary-social--icon snapchat-footer"><a class="sics-component__icon sics-component__icon--snapchat-footer " href="/national/blogs/from-the-newsroom/********/Join-Stuff-co-nz-on-Snapchat" title="Snapchat" target="_blank" rel="noopener"></a></span></div></div></div><div class="sics-component__footer__secondary-copyright"><div class="sics-component__copyright"><div class="sics-component__copyright__stuff"><p class="sics-component__copyright__stuff--notice">© <!-- -->2021<!-- --> Stuff Limited</p></div></div></div></footer></div></div>


  <!-- INITIAL STATE -->
  <script>
    window.__INITIAL_STATE__ = "{\"account\":{},\"app\":{\"sections\":[\"manawatu-standard\",\"news\"],\"error\":false,\"status\":200},\"breakingNews\":{},\"gigya\":{},\"mostPopular\":{},\"news\":{\"*********\":{\"viewing\":false,\"news\":{\"id\":\"*********\",\"seoNoIndex\":false,\"sslEnabled\":true,\"headers\":{\"x-relates-to\":\"********* ********* ********* ********* Article\"},\"headline\":\"Orphee Mickalad leading Palmerston North by-election \",\"headlineFlags\":[],\"advertisement\":false,\"sponsored\":false,\"byline\":\"Jono Galuszka\",\"urlSlug\":\"orphee-mickalad-leading-palmerston-north-byelection\",\"indexIntroText\":\"Orphee Mickalad is on track to replace his former history teacher Tangi Utikere on Palmerston North City Council.\",\"firstPublishTime\":\"2021-02-17T01:40:46.722Z\",\"lastBigModificationTime\":\"2021-02-17T01:40:47.222Z\",\"externalCanonicalURL\":null,\"filteredBodyMarkup\":\"&lt;p>A man who barely spoke English when he arrived in Palmerston North as a refugee is set to replace his former history teacher on the city’s council.&lt;/p> \\n&lt;p>Progress results released on Wednesday afternoon had Orphee Mickalad​ leading in the Palmerston North City Council by-election.&lt;/p> \\n&lt;p>The by-election was required after former deputy mayor Tangi Utikere​ was elected as the city’s MP in the 2020 general election.&lt;/p> \\n&lt;p>Mickalad is leading more established names such as National’s candidate in the 2020 election, William Wood​, former NZ First MP Darroch Ball​, Andy Asquith​ – the highest polling person to miss out in the 2019 council election – and serial candidate Ross Barber​.&lt;/p> \\n&lt;p>&lt;strong>READ MORE:&lt;br> * &lt;a href=\\\"https://www.stuff.co.nz/manawatu-standard/news/300210488/byelection-candidates-invited-to-pitch-for-your-votes?rm=a\\\">By-election candidates invited to pitch for your votes&lt;/a>&lt;br> * &lt;a href=\\\"https://www.stuff.co.nz/manawatu-standard/news/123227014/aleisha-rutherford-palmerston-norths-new-deputy-mayor?rm=a\\\">Aleisha Rutherford Palmerston North's new deputy mayor&lt;/a>&lt;br> * &lt;a href=\\\"https://www.stuff.co.nz/manawatu-standard/news/300136298/election-2020-palmerston-north-council-byelection-set-for-february-following-tangi-utikeres-win?rm=a\\\">Election 2020: Palmerston North council by-election set for February following Tangi Utikere's win&lt;/a>&lt;br> &lt;/strong>&lt;/p> \\n&lt;p>His exact lead was not published by the city council, and would be difficult to figure out due to the election being run under single transferrable vote rules.&lt;/p> \\n&lt;p>In &lt;a href=\\\"https://www.stuff.co.nz/manawatu-standard/news/123386735/candidate-wants-better-prospects-for-palmerston-north-youth\\\">an interview with &lt;em>Stuff &lt;/em>when he declared his intention to run&lt;/a>, 30-year-old Mickalad said he moved to Palmerston North with his family from Congo in 2006 with minimal English skills.&lt;/p> \\n&lt;p>He went to Feryberg High School, where Utikere was his history teacher, worked at the Awapuni landfill, picked fruit, drove forklifts and cleaned offices.&lt;/p> \\n&lt;p>He went on to get a masters degree with a politics endorsement, study law and work in Parliamentary Services and as an adviser at the Ministry of Education.&lt;/p> \\n&lt;p>He ran on a platform of preventing a brain drain from Palmerston North, boosting the city’s social housing stock and improving communication between the council and community.&lt;/p> \\n&lt;p>Deputy electoral officer Hannah White​ said a final election result was expected on Friday.&lt;/p> \\n&lt;p>Voter turnout was 25.06 per cent, well down on the 2019 election turnout of 37.32 per cent.&lt;/p>\",\"stuffNationAssignment\":false,\"stuffNationAssignmentId\":null,\"stuffNationContribution\":false,\"allowComments\":false,\"adExclusions\":[],\"brandsAndCategories\":[{\"categories\":[{\"category\":\"nz/manawatu-standard/news\"},{\"category\":\"nz/manawatu-standard/manawatu-top-stories\"},{\"category\":\"nz/national/politics/local-body-elections\"}]}],\"findStuffNationAssignmentArticlesById\":{\"queryResults\":[]},\"sources\":[{\"source\":\"Stuff\"}],\"getSource\":{\"jcrTitle\":\"Stuff\"},\"keywords\":[\"Teacher\",\"LAW\",\"COUNCIL\",\"study\",\"Ross\",\"Voter\",\"more\",\"Who\",\"family\",\"SCHOOL\",\"CITY COUNCIL\",\"General Election\",\"WOOD\",\"HISTORY\",\"Man\",\"Ministry\",\"FINAL\",\"Election\",\"LANDFILL\",\"Stuff\",\"MP\",\"Rules\",\"Refugee\",\"EDUCATION\",\"WORK\",\"CITY\",\"english\",\"TANGI\",\"Housing\",\"Politics\",\"Masters\",\"HIGH SCHOOL\",\"run\",\"WIN\",\"FEBRUARY\",\"BRAIN\",\"Community\",\"NZ FIRST\",\"ball\",\"PALMERSTON NORTH\",\"New\",\"National\",\"general\",\"INTERVIEW\",\"Electoral\",\"NORTH\",\"MAYOR\",\"Palmerston\",\"FRUIT\",\"VOTE\",\"Congo\",\"first\",\"Ministry of Education\",\"Awapuni\"],\"body\":[{\"name\":\"p\",\"attributes\":{},\"content\":\"A man who barely spoke English when he arrived in Palmerston North as a refugee is set to replace his former history teacher on the city’s council.\"},{\"name\":\"p\",\"attributes\":{},\"content\":\" Progress results released on Wednesday afternoon had Orphee Mickalad​ leading in the Palmerston North City Council by-election.\"},{\"name\":\"p\",\"attributes\":{},\"content\":\" The by-election was required after former deputy mayor Tangi Utikere​ was elected as the city’s MP in the 2020 general election.\"},{\"name\":\"p\",\"attributes\":{},\"content\":\" Mickalad is leading more established names such as National’s candidate in the 2020 election, William Wood​, former NZ First MP Darroch Ball​, Andy Asquith​ – the highest polling person to miss out in the 2019 council election – and serial candidate Ross Barber​.\"},{\"name\":\"p\",\"attributes\":{},\"content\":\" &lt;strong>READ MORE:&lt;br> * &lt;a href=\\\"https://www.stuff.co.nz/manawatu-standard/news/300210488/byelection-candidates-invited-to-pitch-for-your-votes?rm=a\\\">By-election candidates invited to pitch for your votes&lt;/a>&lt;br> * &lt;a href=\\\"https://www.stuff.co.nz/manawatu-standard/news/123227014/aleisha-rutherford-palmerston-norths-new-deputy-mayor?rm=a\\\">Aleisha Rutherford Palmerston North's new deputy mayor&lt;/a>&lt;br> * &lt;a href=\\\"https://www.stuff.co.nz/manawatu-standard/news/300136298/election-2020-palmerston-north-council-byelection-set-for-february-following-tangi-utikeres-win?rm=a\\\">Election 2020: Palmerston North council by-election set for February following Tangi Utikere's win&lt;/a>&lt;br> &lt;/strong>\"},{\"name\":\"p\",\"attributes\":{},\"content\":\" His exact lead was not published by the city council, and would be difficult to figure out due to the election being run under single transferrable vote rules.\"},{\"name\":\"p\",\"attributes\":{},\"content\":\" In &lt;a href=\\\"https://www.stuff.co.nz/manawatu-standard/news/123386735/candidate-wants-better-prospects-for-palmerston-north-youth\\\">an interview with &lt;em>Stuff &lt;/em>when he declared his intention to run&lt;/a>, 30-year-old Mickalad said he moved to Palmerston North with his family from Congo in 2006 with minimal English skills.\"},{\"name\":\"p\",\"attributes\":{},\"content\":\" He went to Feryberg High School, where Utikere was his history teacher, worked at the Awapuni landfill, picked fruit, drove forklifts and cleaned offices.\"},{\"name\":\"p\",\"attributes\":{},\"content\":\" He went on to get a masters degree with a politics endorsement, study law and work in Parliamentary Services and as an adviser at the Ministry of Education.\"},{\"name\":\"p\",\"attributes\":{},\"content\":\" He ran on a platform of preventing a brain drain from Palmerston North, boosting the city’s social housing stock and improving communication between the council and community.\"},{\"name\":\"p\",\"attributes\":{},\"content\":\" Deputy electoral officer Hannah White​ said a final election result was expected on Friday.\"},{\"name\":\"p\",\"attributes\":{},\"content\":\" Voter turnout was 25.06 per cent, well down on the 2019 election turnout of 37.32 per cent.\"}],\"layouts\":[],\"displayFlags\":[],\"section\":\"nz/manawatu-standard/news\",\"display_assets\":[{\"assetId\":\"*********\",\"href\":\"/services/content/v1/image/*********\",\"mediaAnchorPosition\":\"0\",\"type\":\"RelatedImage\",\"captionOverride\":\"Orphee Mickalad is leading the Palmerston North City Council by-election.\",\"imageTypes\":[{\"imageTypeId\":\"StuffLandscapeSixteenByNine\",\"urlMap\":{\"320x180\":\"https://resources.stuff.co.nz/content/dam/images/4/y/p/h/8/h/image.related.StuffLandscapeSixteenByNine.320x180.4yr12n.png/1613526047477.jpg\",\"380x214\":\"https://resources.stuff.co.nz/content/dam/images/4/y/p/h/8/h/image.related.StuffLandscapeSixteenByNine.380x214.4yr12n.png/1613526047477.jpg\",\"620x350\":\"https://resources.stuff.co.nz/content/dam/images/4/y/p/h/8/h/image.related.StuffLandscapeSixteenByNine.620x350.4yr12n.png/1613526047477.jpg\",\"620x349\":\"https://resources.stuff.co.nz/content/dam/images/4/y/p/h/8/h/image.related.StuffLandscapeSixteenByNine.620x349.4yr12n.png/1613526047477.jpg\",\"1240x700\":\"https://resources.stuff.co.nz/content/dam/images/4/y/p/h/8/h/image.related.StuffLandscapeSixteenByNine.1240x700.4yr12n.png/1613526047477.jpg\",\"710x400\":\"https://resources.stuff.co.nz/content/dam/images/4/y/p/h/8/h/image.related.StuffLandscapeSixteenByNine.710x400.4yr12n.png/1613526047477.jpg\",\"418x235\":\"https://resources.stuff.co.nz/content/dam/images/4/y/p/h/8/h/image.related.StuffLandscapeSixteenByNine.418x235.4yr12n.png/1613526047477.jpg\",\"1420x800\":\"https://resources.stuff.co.nz/content/dam/images/4/y/p/h/8/h/image.related.StuffLandscapeSixteenByNine.1420x800.4yr12n.png/1613526047477.jpg\"}},{\"imageTypeId\":\"StuffLandscapeThreeByTwo\",\"urlMap\":{\"300x200\":\"https://resources.stuff.co.nz/content/dam/images/4/y/p/h/8/h/image.related.StuffLandscapeThreeByTwo.300x200.4yr12n.png/1613526047477.jpg\",\"200x133\":\"https://resources.stuff.co.nz/content/dam/images/4/y/p/h/8/h/image.related.StuffLandscapeThreeByTwo.200x133.4yr12n.png/1613526047477.jpg\",\"600x400\":\"https://resources.stuff.co.nz/content/dam/images/4/y/p/h/8/h/image.related.StuffLandscapeThreeByTwo.600x400.4yr12n.png/1613526047477.jpg\",\"1151x768\":\"https://resources.stuff.co.nz/content/dam/images/4/y/p/h/8/h/image.related.StuffLandscapeThreeByTwo.1151x768.4yr12n.png/1613526047477.jpg\",\"490x327\":\"https://resources.stuff.co.nz/content/dam/images/4/y/p/h/8/h/image.related.StuffLandscapeThreeByTwo.490x327.4yr12n.png/1613526047477.jpg\",\"1464x976\":\"https://resources.stuff.co.nz/content/dam/images/4/y/p/h/8/h/image.related.StuffLandscapeThreeByTwo.1464x976.4yr12n.png/1613526047477.jpg\"}}],\"getImageById\":{\"photographer\":\"David Unwin/Stuff\"},\"photographer\":\"David Unwin/Stuff\",\"lazyImage\":true},{\"type\":\"RelatedHtmlAsset\",\"assetId\":\"*********\",\"embedCode\":{\"trusted\":true,\"embed\":\"&lt;div>\\n\\n\\n&lt;style>\\n    .support-brief-container{\\n        width: 100%;\\n        max-width: 650px;\\n        margin: 20px auto;\\n        box-sizing: border-box;\\n        overflow: hidden;\\n        position: relative;\\n        border-top: 3px solid #F74E19;\\n        border-bottom: 3px solid #F74E19;\\n        background-color: #F7F7F7;\\n        font-family: 'Roboto',sans-serif;\\n        padding: 0px 10px;\\n    }\\n    .support-brief-container-title{\\n        font-size: 20px;\\n        font-weight: 700;\\n        text-align: left !important;\\n        margin-top: 20px;\\n    }\\n    .support-brief-container-title-box{\\n        display: flex;\\n        flex-flow: row wrap;\\n        justify-content: space-between;\\n    }\\n    .support-brief-container--topbutton{\\n        color:#F74E19 !important;\\n        font-family: 'Roboto',sans-serif;\\n        font-size: 16px;\\n        display: flex;\\n        align-items: center;\\n        text-decoration: none !important;\\n        transition: all .2s;\\n        margin-top: 20px;\\n        font-weight: 500 !important;\\n  \\n    }\\n  \\n    .support-brief-container--topbutton span{\\n        font-size: 16px;\\n        white-space: nowrap;\\n    }\\n    .support-brief-container--topbutton:hover{\\n        opacity: .78;\\n    }\\n    .support-brief-container--topbutton--arrow{\\n        height: 26px !important;\\n        width: 26px !important;\\n        margin-left: 10px;\\n    }\\n    .support-brief-container-textbox{\\n        margin-top: 18px;\\n    }\\n    .support-brief-container-text{\\n        font-weight: 300 !important;\\n        font-size: 18px;\\n        line-height: 1.4;\\n        text-align: left !important;\\n    }\\n    .support-brief-container--bottompbutton{\\n        height: 38px;\\n        width: 225px;\\n        font-weight: 500 !important;\\n        background-color: #F74E19;\\n        color: #fff !important;\\n        display: flex;\\n        align-items: center;\\n        text-decoration: none !important;\\n        font-family: 16px;\\n        font-family: 'Roboto',sans-serif;\\n        border-radius: 100px;\\n        position: relative;\\n        margin: 25px 0;\\n        transition: all .2s;\\n    }\\n    .support-brief-container--bottompbutton span{\\n        position: absolute;\\n        top: 50%;\\n        transform: translateY(-50%);\\n        left: 20px;\\n    }\\n    .bottomarrow{\\n        position: absolute;\\n        top: 50%;\\n        transform: translateY(-50%);\\n        right: 5px;\\n    }\\n    .support-brief-container--bottompbutton:hover{\\n        width: 235px;\\n    }\\n  &lt;/style>\\n&lt;div class=\\\"support-brief-container\\\">\\n    &lt;div class=\\\"support-brief-container-title-box\\\">\\n    &lt;span class=\\\"support-brief-container-title\\\">If you value facts and being well-informed, please consider supporting Stuff.&lt;/span>\\n    &lt;a\\n    onclick=\\\"sendEvent({ event: 'article.make.a.contribution' })\\\" \\n    data-pp-page=\\\"pwyw\\\"\\n    data-pp-metadata=\\\"$pay-what-you-want-campaign-page-version-1.1\\\"\\n    href=\\\"https://dashboard.presspatron.com/donations/new?frame=1&t=TJu26zZFBKa635NQ13AZRn8S\\\"\\n    class=\\\"support-brief-container--topbutton\\\">&lt;span>Make a contribution&lt;/span>&lt;img class=\\\"support-brief-container--topbutton--arrow\\\" src=\\\"https://static3.stuff.co.nz/orangearrow-bc31e5a0.png\\\">&lt;/a>\\n    &lt;/div>\\n    &lt;div class=\\\"support-brief-container-textbox\\\">\\n        &lt;p class=\\\"support-brief-container-text\\\">Why? Because with the pandemic situation constantly changing, it’s easy for misinformation and rumours to take hold. You can rely on Stuff’s journalists to question the decision-makers, interview experts, and use eyewitness reporting to answer your key questions with facts and context.&lt;/p>\\n        &lt;p class=\\\"support-brief-container-text\\\">Stuff’s ethical reporting is built on accuracy, fairness and balance. With millions of New Zealanders turning to us every day, it’s our mission to make Aotearoa a better place.&lt;/p>\\n        &lt;p class=\\\"support-brief-container-text\\\">But the way journalism is funded is changing and we need your help to sustain local newsrooms.&lt;/p>\\n        &lt;p class=\\\"support-brief-container-text\\\">If Stuff is a regular part of your day, please consider becoming a supporter. &lt;b>You can make a contribution from as little as $1. Be part of our story, and help us tell yours. &lt;/b>&lt;/p>\\n    &lt;/div>\\n    &lt;a \\n    onclick=\\\"sendEvent({ event: 'article.become.a.supporter' })\\\" data-pp-page=\\\"pwyw\\\"\\n    data-pp-metadata=\\\"$pay-what-you-want-campaign-page-version-1.1\\\"\\n    href=\\\"https://dashboard.presspatron.com/donations/new?frame=1&t=TJu26zZFBKa635NQ13AZRn8S\\\"\\n    class=\\\"support-brief-container--bottompbutton\\\">&lt;span>Become a supporter&lt;/span>&lt;img class=\\\"support-brief-container--topbutton--arrow bottomarrow\\\" src=\\\"https://static3.stuff.co.nz/whitearrow-f62aea58.png\\\">&lt;/a>\\n  &lt;/div>\\n&lt;script>\\n\\nif(document.cookie.indexOf('supportprogramme_thankyou=1') !== -1){\\n  document.querySelector('.support-brief-container').style.display=\\\"none\\\";\\n}\\n\\nfunction sendEvent(event) {\\n    if (window.digitalData) {\\n      window.digitalData.events.push(event);\\n    }\\n  }\\n\\nwindow.addEventListener(\\\"message\\\", (event) => {\\nif (event.data && event.data.eventName === \\\"payment_successful\\\") {\\n    var oneDayInSec = 86400;\\n    var exclusionPeriod =\\n    event.data.payload.frequency === \\\"one-time\\\"\\n        ? 60 * oneDayInSec\\n        : 365 * oneDayInSec;\\n    document.cookie =\\n    \\\"supportprogramme_thankyou=1; max-age=\\\" +\\n    exclusionPeriod +\\n    \\\"; domain=.stuff.co.nz;path=/;\\\";\\n    sendEvent({\\n    event: \\\"donation.successful\\\",\\n    \\\"donation.amount\\\": event.data.payload.amount,\\n    \\\"donation.frequency\\\": event.data.payload.frequency,\\n    });\\n}\\n});\\n\\n&lt;/script>\\n&lt;/div>\"},\"mediaAnchorPosition\":\"12\"}],\"scripts\":[]},\"status\":200,\"error\":false}},\"category\":{\"category\":{\"title\":\"Manawatu Standard\",\"category\":\"nz/manawatu-standard\",\"path\":\"/manawatu-standard\",\"articleCategory\":false,\"children\":[{\"title\":\"News\",\"category\":\"nz/manawatu-standard/news\",\"path\":\"/manawatu-standard/news\",\"articleCategory\":true,\"children\":[]}]},\"error\":false},\"coral\":{}}"
  </script>



<!-- OIDC -->

<script src="https://www.stuff.co.nz/static/stuff-login-browser-sdk/1.1.2/stuff-login-sdk.js"></script>

<!-- Google Tag Manager (noscript) -->
<noscript>
  <iframe src="https://www.googletagmanager.com/ns.html?id=GTM-TR9KB9M"
          height="0" width="0" style="display:none;visibility:hidden"></iframe>
</noscript>
<!-- End Google Tag Manager (noscript) -->

<!-- STUFF HEADER BIDDING -->
<script async src="https://www.stuff.co.nz/static/stuff-header-bidding/latest/stuff-header-bidding.js"></script>

<!-- REACT LIBRARIES -->
<script type="text/javascript" src="/sics-assets/js/react.b5cebb40c56d05a121f1.js"></script><script type="text/javascript" src="/sics-assets/js/react-dom.85970029cc12a154344e.js"></script>
<!-- APPLICATION SOURCE -->
<script type="text/javascript" src="/sics-assets/js/bundle.2c2985e063e73b034845.js"></script><script type="text/javascript" src="/sics-assets/js/jquery.76ea9588279b8d447276.js"></script>

<!-- NATIVFORM (AKA ADFLICTION) SCRIPT -->
<script async src="https://www.stuff.co.nz/static/stuff-adfliction/latest/stuff-adfliction.js"></script>

<!-- OUTBRAIN -->

<script async src="https://widgets.outbrain.com/outbrain.js"></script>

<!-- Oovvuu video recommendations -->
<script async src="https://videos.oovvuu.com/stuf/v1/ovu_rec.js"></script>


<script>
  (function () {
    if (_satellite && _satellite.pageBottom) {
      _satellite.pageBottom();
    }
  })();
</script>

<script id = 'js-pp-banner' async src="//dashboard.presspatron.com/dev/banner?b=TJu26zZFBKa635NQ13AZRn8S"></script>

<!-- Google Tag Manager -->
<script>(function (w, d, s, l, i) {
  w[l] = w[l] || [];
  w[l].push({
    'gtm.start':
      new Date().getTime(), event: 'gtm.js'
  });
  var f = d.getElementsByTagName(s)[0],
    j = d.createElement(s), dl = l != 'dataLayer' ? '&l=' + l : '';
  j.async = true;
  j.src =
    'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
  f.parentNode.insertBefore(j, f);
})(window, document, 'script', 'dataLayer', 'GTM-TR9KB9M');</script>
<!-- End Google Tag Manager -->
</body>
</html>
