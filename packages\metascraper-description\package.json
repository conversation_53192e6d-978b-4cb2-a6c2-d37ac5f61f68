{"name": "metascraper-description", "description": "Get description property from HTML markup", "homepage": "https://github.com/microlinkhq/metascraper/packages/metascraper-description", "version": "5.49.1", "types": "src/index.d.ts", "main": "src/index.js", "author": {"email": "<EMAIL>", "name": "microlink.io", "url": "https://microlink.io"}, "repository": {"directory": "packages/metascraper-description", "type": "git", "url": "git+https://github.com/microlinkhq/metascraper.git#master"}, "bugs": {"url": "https://github.com/microlinkhq/metascraper/issues"}, "keywords": ["description", "metascraper"], "dependencies": {"@metascraper/helpers": "workspace:*"}, "devDependencies": {"ava": "5"}, "engines": {"node": ">= 16"}, "files": ["src"], "scripts": {"test": "NODE_PATH=.. TZ=UTC ava --timeout 15s"}, "license": "MIT"}