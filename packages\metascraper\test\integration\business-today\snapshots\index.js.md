# Snapshot report for `test/integration/business-today/index.js`

The actual snapshot is saved in `index.js.snap`.

Generated by [AVA](https://avajs.dev).

## business-today

> Snapshot 1

    {
      audio: null,
      author: null,
      description: 'HackerRank is helping companies recruit coding champions through online tests.',
      image: 'http://media2.intoday.in/btmt/images/stories/code505_051616043033.jpg',
      lang: 'en',
      logo: 'https://akm-img-a-in.tosshub.com/businesstoday/resource/img/favicon_v2.ico',
      publisher: 'Business Today',
      title: 'Cracking the Code',
      url: 'http://www.businesstoday.in/magazine/features/hackerrank-helping-cos-hire-coding-champions-via-online-tests/story/232567.html',
      video: null,
    }
