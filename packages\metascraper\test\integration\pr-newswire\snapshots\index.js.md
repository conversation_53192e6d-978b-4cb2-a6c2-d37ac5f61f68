# Snapshot report for `test/integration/pr-newswire/index.js`

The actual snapshot is saved in `index.js.snap`.

Generated by [AVA](https://avajs.dev).

## pr-newswire

> Snapshot 1

    {
      audio: null,
      author: 'HackerRank',
      date: '2018-06-29T09:07:45.000Z',
      description: '/PRNewswire/ -- HackerRank, a platform that ranks engineers based on their coding skills and helps companies discover talent faster, today announced a…',
      image: 'http://www.prnewswire.com/content/dam/prnewswire/homepage/prn_cision_logo_desktop.png',
      lang: 'en',
      logo: 'https://www.prnewswire.com/content/dam/prnewswire/icons/2019-Q4-PRN-Icon-32-32.png',
      publisher: 'PR Newswire',
      title: 'HackerRank & Cybermedia Technologies Partner to Help Close the STEM Skills Gap in Federal Government',
      url: 'https://www.prnewswire.com/news-releases/hackerrank--cybermedia-technologies-partner-to-help-close-the-stem-skills-gap-in-federal-government-300256929.html',
      video: null,
    }
