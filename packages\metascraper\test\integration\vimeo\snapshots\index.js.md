# Snapshot report for `test/integration/vimeo/index.js`

The actual snapshot is saved in `index.js.snap`.

Generated by [AVA](https://avajs.dev).

## vimeo

> Snapshot 1

    {
      audio: null,
      author: 'Vimeo Curation',
      date: '2025-05-07T18:48:37.000Z',
      description: 'The results are in! After much deliberation, our panel of judges have picked their favorites. See the winners at https://vimeo.com/bestoftheyear Music: “Darma”…',
      image: 'https://i.vimeocdn.com/video/752943117-c4b535107ecca677499c1c35b417dd75a1f1189def72c89d5456a012e39994f7-d?f=webp&region=us',
      lang: 'en',
      logo: 'https://f.vimeocdn.com/svg/legacy_view_support/iris_icon_v_64.svg?6cbd8227b072c4a6ca73ea08402547887b276449',
      publisher: 'Vimeo',
      title: 'Vimeo Best of the Year 2018: Winners',
      url: 'https://vimeo.com/311983548',
      video: null,
    }
