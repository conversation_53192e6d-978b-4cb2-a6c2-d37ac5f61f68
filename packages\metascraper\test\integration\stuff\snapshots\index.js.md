# Snapshot report for `test/integration/stuff/index.js`

The actual snapshot is saved in `index.js.snap`.

Generated by [AVA](https://avajs.dev).

## stuff

> Snapshot 1

    {
      audio: null,
      author: '<PERSON><PERSON>',
      date: '2021-02-17T01:40:47.222Z',
      description: '<PERSON><PERSON><PERSON> is on track to replace his former history teacher <PERSON><PERSON> on Palmerston North City Council.',
      image: 'https://resources.stuff.co.nz/content/dam/images/4/y/p/h/8/h/image.related.StuffLandscapeSixteenByNine.1420x800.4yr12n.png/1613526047477.jpg',
      lang: 'en',
      logo: 'https://www.stuff.co.nz/sics-assets/images/favicons/safari-pinned-tab.svg',
      publisher: 'Stuff',
      title: '<PERSON><PERSON><PERSON> leading Palmerston North by-election',
      url: 'https://www.stuff.co.nz/manawatu-standard/news/300232751/orphee-mick<PERSON><PERSON>-leading-palmerston-north-byelection',
      video: null,
    }
