# Snapshot report for `test/integration/instagram/index.js`

The actual snapshot is saved in `index.js.snap`.

Generated by [AVA](https://avajs.dev).

## instagram

> Snapshot 1

    {
      audio: null,
      author: null,
      date: '2017-07-09T04:58:10.000Z',
      description: '<PERSON><PERSON> shared a photo on Instagram: “The first Model 3 production car” • See 683 photos and videos on their profile.',
      image: 'https://scontent-mad1-1.cdninstagram.com/v/t51.2885-15/e35/19955323_327280991034910_1996793346030305280_n.jpg?_nc_ht=scontent-mad1-1.cdninstagram.com&_nc_cat=110&_nc_ohc=VDeNnhXahEwAX8BdW_o&edm=AABBvjUBAAAA&ccb=7-4&oh=91e77ff01116fe89635f8fac37f48add&oe=6178BE62&_nc_sid=83d603',
      lang: 'en',
      logo: 'https://www.instagram.com/static/images/ico/favicon-192.png/68d99ba29cc8.png',
      publisher: 'Instagram',
      title: 'Tesla on Instagram: “The first Model 3 production car”',
      url: 'https://www.instagram.com/p/BWUDBntl3_Z/',
      video: null,
    }
