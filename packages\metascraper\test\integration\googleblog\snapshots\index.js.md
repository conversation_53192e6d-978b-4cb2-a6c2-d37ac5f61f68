# Snapshot report for `test/integration/googleblog/index.js`

The actual snapshot is saved in `index.js.snap`.

Generated by [A<PERSON>](https://avajs.dev).

## googleblog

> Snapshot 1

    {
      audio: null,
      author: 'Google',
      date: '2016-09-29T12:00:00.000Z',
      description: 'Posted by <PERSON>, Vice President, Google Cloud As we officially move into the Google Cloud era, Google Cloud Platform (GCP) conti…',
      image: 'https://3.bp.blogspot.com/-m90zG1Qb7vc/Vel5wAn_isI/AAAAAAAARGE/iSOuuYWUXUA/s1600-r/CloudPlatform_128px_Retina.png',
      lang: 'en',
      logo: 'https://cloudplatform.googleblog.com/favicon.ico',
      publisher: 'Google Cloud Platform Blog',
      title: 'Google Cloud Platform sets a course for new horizons',
      url: 'https://cloudplatform.googleblog.com/2016/09/Google-Cloud-Platform-sets-a-course-for-new-horizons.html',
      video: null,
    }
