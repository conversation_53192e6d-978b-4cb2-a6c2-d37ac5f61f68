# Snapshot report for `test/integration/milanocittastato/index.js`

The actual snapshot is saved in `index.js.snap`.

Generated by [AVA](https://avajs.dev).

## milanocittastato

> Snapshot 1

    {
      audio: null,
      author: '<PERSON><PERSON><PERSON>',
      date: '2022-06-20T10:46:49.000Z',
      description: 'Milano città stato è un magazine di news, eventi, interviste, approfondimenti, immagini, video, per coinvolgere i cittadini a costruire la città ideale.',
      image: 'https://s.w.org/images/core/emoji/13.1.0/svg/1f1ec-1f1e7.svg',
      lang: 'it',
      logo: 'https://www.milanocittastato.it/wp-content/uploads/2016/01/Logo_1000x1000.jpg',
      publisher: 'Milano Città Stato',
      title: 'Milano Città Stato',
      url: 'https://www.milanocittastato.it/',
      video: null,
    }
