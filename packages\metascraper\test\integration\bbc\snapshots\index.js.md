# Snapshot report for `test/integration/bbc/index.js`

The actual snapshot is saved in `index.js.snap`.

Generated by [AVA](https://avajs.dev).

## bbc

> Snapshot 1

    {
      audio: null,
      author: '<PERSON>us<PERSON> reporter, BBC News',
      date: '2017-07-14T20:28:07.000Z',
      description: 'How an IT worker quit his day job, and despite having no ideas to begin with, launched a firm from home that made him a multi-millionaire.',
      image: 'https://ichef.bbci.co.uk/news/1024/branded_news/D20B/production/_96817735_carl4.jpg',
      lang: 'en',
      logo: 'https://static.files.bbci.co.uk/core/website/assets/static/icons/touch/news/touch-icon-512.685da29e9e8ce5e435a8.png',
      publisher: 'BBC',
      title: 'The man who built a $1bn firm in his basement',
      url: 'https://www.bbc.com/news/business-40504764',
      video: null,
    }
