# Snapshot report for `test/integration/computerworld/index.js`

The actual snapshot is saved in `index.js.snap`.

Generated by [AVA](https://avajs.dev).

## computerworld

> Snapshot 1

    {
      audio: null,
      author: '<PERSON>',
      date: '2016-04-15T20:52:00.000Z',
      description: 'My healthcare data is what I want protected the most (intimate details about my family’s health, where we live, and financial information). Anything and everything a hacker could want! It is safe? As a data security professional and citizen, I know the answer is not good.',
      image: 'http://images.techhive.com/images/article/2016/04/blog-31_apr15_image-1-100656409-primary.idge.jpg',
      lang: 'en',
      logo: 'https://idge.staticworld.net/ctw/computerworld-logo300x300.png',
      publisher: 'Computerworld',
      title: 'Healthcare Data Protection and Privacy Prognosis—Still Critical but New Treatment is Available',
      url: 'http://www.computerworld.com/article/3057179/data-analytics/healthcare-data-protection-and-privacy-prognosis-still-critical-but-new-treatment-is-available.html',
      video: null,
    }
