
























<!DOCTYPE html>
<!--[if lt IE 7]>  <html lang="en" class="no-js lt-ie9 lt-ie8 lt-ie7" xmlns="http://www.w3.org/1999/xhtml" xmlns:fb="http://ogp.me/ns/fb#"> <![endif]-->
<!--[if IE 7]> <html lang="en" class="no-js lt-ie10 lt-ie9 lt-ie8" xmlns="http://www.w3.org/1999/xhtml" xmlns:fb="http://ogp.me/ns/fb#"> <![endif]-->
<!--[if IE 8]> <html lang="en" class="no-js lt-ie10 lt-ie9" xmlns="http://www.w3.org/1999/xhtml" xmlns:fb="http://ogp.me/ns/fb#"> <![endif]-->
<!--[if IE 9]> <html lang="en" class="no-js lt-ie10" xmlns="http://www.w3.org/1999/xhtml" xmlns:fb="http://ogp.me/ns/fb#"> <![endif]-->
<!--[if gt IE 9]><!--> <html lang="en" class="no-js" xmlns="http://www.w3.org/1999/xhtml" xmlns:fb="http://ogp.me/ns/fb#"> <!--<![endif]-->











<head>

  <!-- metas -->
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
  <title>Healthcare Data Protection and Privacy Prognosis—Still Critical but New Treatment is Available | Computerworld</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">










  <link rel="canonical" href="http://www.computerworld.com/article/3057179/data-analytics/healthcare-data-protection-and-privacy-prognosis-still-critical-but-new-treatment-is-available.html" />





  <meta itemprop="url" content="http://www.computerworld.com/article/3057179/data-analytics/healthcare-data-protection-and-privacy-prognosis-still-critical-but-new-treatment-is-available.html" />
  <meta name="description" content="My healthcare data is what I want protected the most (intimate details about my family’s health, where we live, and financial information). Anything and everything a hacker could want! It is safe? As a data security professional and citizen, I know the answer is not good." />



      <meta name="DC.date.issued" content="2016-04-15T13:52-05:00" />
      <meta name="date" content="2016-04-15" />









      <meta property="og:title" content="Healthcare Data Protection and Privacy Prognosis—Still Critical but New Treatment is Available">







      <meta property="og:image" content="http://images.techhive.com/images/article/2016/04/blog-31_apr15_image-1-100656409-primary.idge.jpg" />
      <meta itemprop="image" content="http://images.techhive.com/images/article/2016/04/blog-31_apr15_image-1-100656409-primary.idge.jpg">
      <meta itemprop="thumbnailURl" content="http://images.techhive.com/images/article/2016/04/blog-31_apr15_image-1-100656409-primary.idge.jpg" />
      <link rel="image_src" href="http://images.techhive.com/images/article/2016/04/blog-31_apr15_image-1-100656409-primary.idge.jpg" />
      <meta name="twitter:image" content="http://images.techhive.com/images/article/2016/04/blog-31_apr15_image-1-100656409-primary.idge.jpg" />





      <meta property="og:type" content="article" />









      <meta name="author" content="Robert Shields">





        <meta name="robots" content="NOFOLLOW" />




  <meta property="og:site_name" content="Computerworld" />
  <meta property="fb:app_id" content="123026274413041" />


      <meta property="og:url" content="http://www.computerworld.com/article/3057179/data-analytics/healthcare-data-protection-and-privacy-prognosis-still-critical-but-new-treatment-is-available.html">
      <meta name="twitter:url" content="http://www.computerworld.com/article/3057179/data-analytics/healthcare-data-protection-and-privacy-prognosis-still-critical-but-new-treatment-is-available.html">











    <!-- Add to article pages only -->
      <meta name="twitter:card" content="summary_large_image">


  <meta name="twitter:card" content="summary">
  <meta name="twitter:site" content="@computerworld">

    <meta property="og:description" content="My healthcare data is what I want protected the most (intimate details about my family’s health, where we live, and financial information). Anything and everything a hacker could want! It is safe? As a data security professional and citizen, I know the answer is not good.">
    <meta name="twitter:description" content="My healthcare data is what I want protected the most (intimate details about my family’s health, where we live, and financial information). Anything and everything a hacker could want! It is safe? As a data security professional and citizen, I know the answer is not good.">
















    <meta property="fb:pages" content="103687894679" />


  <meta name="rating" content="General">

  <meta name="robots" content="NOODP,NOYDIR" />


    <meta name="google-site-verification" content="_JEYLpDBDU_w1eI6P77BYlB1lvpukoxFcYDS_-Aznko" />
















  <!-- Sailthru -->

    <meta name="sailthru.tags" content="data-analytics,healthcare-it,robert-shields,great-data-thinking,opinion,medium"/>

    <meta name="sailthru.image.thumb" content="http://core0.staticworld.net/images/article/2016/04/blog-31_apr15_image-1-100656409-small.idge.jpg"/>

    <meta name="sailthru.image.full" content="http://core5.staticworld.net/images/article/2016/04/blog-31_apr15_image-1-100656409-primary.idge.jpg"/>



  <!-- pagination -->



  <!-- css -->
  <link rel="stylesheet" href="/www.idge/css/normalize.css?v=20160525110859" />
  <link rel="stylesheet" href="/www.idge/css/prettify.css?v=20160525110859" />

    <link rel="stylesheet" href="/www.idge.ans/js/select2-3.5.0/select2.css" />



    <link rel="stylesheet" href="/www.idge/css/sitewrapper.css?v=20160525110859" />




        <link rel="stylesheet" href="/www.idge/css/article.css?v=20160525110859" />



    <link rel="stylesheet" href="/www.idge/css/print.css?v=20160525110859" media="print" />



    <link rel="stylesheet" href="/www.idge.ctw/css/sitewrapper.css?v=20160525110859" />

    <link rel="stylesheet" href="/www.idge.ctw/css/article.css?v=20160525110859" />



  <link rel="stylesheet" href="/www.idge/css/webfonts/ss-social.css?v=20160525110859" />
  <link rel="stylesheet" href="/www.idge/css/webfonts/ss-standard.css?v=20160525110859" />
  <!--[if lte IE 8]>
    <link href="/www.idge/css/webfonts/ss-ie8.css" rel="stylesheet" />
  <![endif]-->



    <link href="/www/css/ecom.css?v=20160525110859" rel="stylesheet" >




    <script type="text/javascript" src="http://fonts.staticworld.net/cav2eef.js"></script>
    <script type="text/javascript">try{Typekit.load();}catch(e){}</script>


  <!--  fav and touch icons -->
  <link rel="shortcut icon" type="image/x-icon" href="http://idge.staticworld.net/ctw/favicon.ico"/>
  <link rel="apple-touch-icon-precomposed" sizes="144x144" href="http://idge.staticworld.net/ctw/computerworld-logo144x144.png" />
  <link rel="apple-touch-icon-precomposed" sizes="114x114" href="http://idge.staticworld.net/ctw/computerworld-logo114x114.png" />
  <link rel="apple-touch-icon-precomposed" sizes="72x72" href="http://idge.staticworld.net/ctw/computerworld-logo72x72.png" />
  <link rel="apple-touch-icon-precomposed" sizes="57x57" href="http://idge.staticworld.net/ctw/computerworld-logo57x57.png" />
  <link rel="apple-touch-icon" href="http://idge.staticworld.net/ctw/computerworld-logo300x300.png" />

  <!-- js -->
  <script type="text/javascript">var _sf_startpt=(new Date()).getTime();</script>

  <script src="/www.idge/js/jquery/jquery-1.10.2.min.js"></script>

  <script src="/www/js/init_device.js?v=20160525110859"></script>

  <!-- google_dfp needs jquery defined first -->
















<script type="text/javascript" src="/www/js/ads/gpt_includes.js?v=20160525110859"></script>


    <script src="/www/js/ads/narf_prebid.js?v=20160525110859"></script>




<script type="text/javascript">
  // Set up ad related variables
  try {
    IDG.GPT.unitName = "/8456/IDG.US_E_Computerworld.com";
  }
  catch (exception) {
    console.log ("google_dfp can't use IDG.GPT "+ exception);
  }


  try {
    IDG.GPT.unitName = IDG.GPT.unitName + "/" + "analytics_section";
  }
  catch (exception) {
    console.log ("google_dfp can't use IDG.GPT "+ exception);
  }
  // global variables
  var global_ShowSuper = true;
  var global_ShowHero = false;

  //XFP global targeting, more targeting thm_pre
  var url = window.location.href;
  if(url.indexOf("?")>1){
    url=url.split('?')[0];
  }


  try {
    IDG.GPT.addTarget("URL", encodeURIComponent(url));
  }
  catch (exception) {
    console.log ("google_dfp can't use IDG.GPT "+ exception);
  }
  try {IDG.GPT.addTarget("zone", 'article-blog/data-analytics');} catch (exception) {console.log ("google_dfp can't use IDG.GPT "+ exception);}
  try{IDG.GPT.addTarget("blogId", '1484');} catch (exception) {console.log ("google_dfp can't use IDG.GPT "+ exception);}try{IDG.GPT.addTarget("articleId", '3057179');}  catch (exception) {console.log ("google_dfp can't use IDG.GPT "+ exception);}try{IDG.GPT.addTarget("type", 'opinion');} catch (exception) {console.log ("google_dfp can't use IDG.GPT "+ exception);}try{IDG.GPT.addTarget("typeId", '5');} catch (exception) {console.log ("google_dfp can't use IDG.GPT "+ exception);}try{IDG.GPT.addTarget("templateType", 'article-default');} catch (exception) {console.log ("google_dfp can't use IDG.GPT "+ exception);}

  try {IDG.GPT.addTarget("categoryIds", [3551,3631]);}  catch (exception) {console.log ("google_dfp can't use IDG.GPT "+ exception);}
  try{IDG.GPT.addTarget("categorySlugs", ['data-analytics','healthcare-it']);}  catch (exception) {console.log ("google_dfp can't use IDG.GPT "+ exception);}
  try {
    if (null != IDG.GPT.getQsVal("env")) {
      IDG.GPT.addTarget("env", IDG.GPT.getQsVal("env").replace(/\W/g, "") );
    }
  }
  catch (exception) {
    console.log ("google_dfp can't use IDG.GPT "+ exception);
  }

    try {
      IDG.GPT.addTarget("author", 'Robert Shields');
    }
    catch (exception) {
      console.log ("google_dfp can't use IDG.GPT "+ exception);
    }





  try {
    IDG.GPT.addTarget("page_type", '');
  }
  catch (exception) {
    console.log ("google_dfp can't use IDG.GPT "+ exception);
  }


    try {
      IDG.GPT.addTarget("blog_name", 'Great Data Thinking');
    }
    catch (exception) {
      console.log ("google_dfp can't use IDG.GPT "+ exception);
    }



    try {
      IDG.GPT.addTarget("sponsored", 'true');
    }
    catch (exception) {
      console.log ("google_dfp can't use IDG.GPT "+ exception);
    }


    try {
      IDG.GPT.addTarget("insiderContent", 'false');
    }
    catch (exception) {
      console.log ("google_dfp can't use IDG.GPT "+ exception);
    }



</script>


<script type="text/javascript" src="/www/js/ads/proximic.js?v=20160525110859"></script>
<script type="text/javascript">
   var currentUrl = encodeURIComponent(window.location.href);
   (function (u) {
    var f = "scr";
    var e = (u ? ("url=" + u) : "");
    document.write("<"+f+"ipt type='text/javascript' src='http://t.zqtk.net/c.js?m=1&s=idg_js&"+e.replace(/\'/g, "")+"'></"+f+"ipt>");
    })((typeof (currentUrl) === "undefined") ? "" : currentUrl);
</script>


<script type="text/javascript">




</script>

<script src="/www.idge/js/thm_pre.js?v=20160525110859"></script>


  <script>
    var isMobile = (typeof IDG.DEVICE !== 'undefined') ? IDG.DEVICE.isMobile() : false;








    IDG.GPT.addBiddingParams("yieldbotPsn", (isMobile ? "49b0" : "3b20"));
    IDG.GPT.addBiddingParams("appnexusPlacementId", (isMobile ? "6451309" : "6352920"));
  </script>
  <script type="text/javascript" src="/www/js/ads/prebid_launcher.js?v=20160525110859"></script>

<script type="text/javascript" src="/www/js/ads/gpt_launcher.js?v=20160525110859"></script>



  <script src="/www/js/swfobject.js?v=20160525110859"></script>

    <script src="/www.idge.ans/js/select2-3.5.0/select2.js"></script>






    <script>
       $(document).ready(function() {
         $("select#country").select2({
            placeholder: "country",
            minimumResultsForSearch: Infinity,
            allowClear: true
          });
          $("#jobPosition").select2({
            placeholder: "position",
            minimumResultsForSearch: Infinity,
            allowClear: true
          });
          $("#jobFunction").select2({
            placeholder: "function",
            minimumResultsForSearch: Infinity,
            allowClear: true
          });
          $("#companySize").select2({
            placeholder: "company size",
            minimumResultsForSearch: Infinity,
            allowClear: true
          });
          $("#industry").select2({
            placeholder: "industry",
            minimumResultsForSearch: Infinity,
            allowClear: true
          });
       });
     </script>













<script>

var brandCode = "ctw";
var brandName="Computerworld";
var regDebugLog=false;
var regApiUrl=" http://regdev.idge.int/api/ ";
var tokenPrefix='www';
var tokenSuffix='.com/insider/token?';
var brandDomain=".computerworld.com";
var insiderContentType="article";
var notEmptyArticle=false;
var notEmptyMediaResource=false;
var isInsiderPremium=false;
var isResourceInsiderPremium=false;


  regApiUrl="http://reg.idgenterprise.com/api/";



  regDebugLog=false;



  tokenPrefix="http://www";



  tokenSuffix="/insider/token";





  notEmptyArticle=true;








</script>

<script src="/www/js/utils/hashes.js?v=20160525110859"></script>
<script src="/www/js/utils/alc.js?v=20160525110859"></script>
<script src="/www/js/insider/insider_reg_api.js?v=20160525110859"></script>
<script src="/www/js/insider/jquery.maskedinput-1.4.min.js"></script>

<script>
$(document).ready(function(){
  if(typeof(IDG.insiderReg.readCookie('nsdr')) !== 'undefined'){
    $('#welcome-message').show();
    IDG.insiderReg.personalize();
  }else{
    $('.signin-register').show();
  }
});

</script>




  <!-- All JavaScript at the bottom, except for Modernizr which enables HTML5 elements & feature detects -->
  <script src="/www.idge/js/mule/modernizr.js?v=20160525110859"></script>
  <script src="/www.idge/js/mule/respond.min.js?v=20160525110859"></script>











<!-- Begin Eloqua Tracking -->
<script type="text/javascript" src="/www/js/analytics/eloqua/elqCfg.js"></script>
<script type="text/javascript" src="/www/js/analytics/eloqua/elqImg.js"></script>
<script type="text/javascript" src="/www/js/analytics/eloqua/elqFCS.js"></script>
<script type="text/javascript" src="/www/js/analytics/eloqua/elqScr.js"></script>
<script type="text/javascript" src="/www/js/analytics/eloqua/elqIDG.js"></script>



















<script type="text/javascript">
  IDG = window.IDG || {};
  IDG.PageInfo = IDG.PageInfo || {};
  IDG.PageInfo = function() {
    return {
      eloqua_type: "blogpost",
      eloqua_topic: "datacenter"
    };
  }();
  var elqCustomerGUID = "";
  if (this.GetElqCustomerGUID && typeof GetElqCustomerGUID === "function") {
          elqCustomerGUID = GetElqCustomerGUID();
  }
  IDG.Eloqua.invoke_flash();
</script>
<!-- End Eloqua Tracking -->



  <script type="text/javascript">
    var g_arrModules = new Array();
  </script>






  <script src='https://www.google.com/recaptcha/api.js'></script>











































































    <meta name="parsely-title" content="Healthcare Data Protection and Privacy Prognosis—Still Critical but New Treatment is Available" />
    <meta name="parsely-link" content="http://www.computerworld.com/article/3057179/data-analytics/healthcare-data-protection-and-privacy-prognosis-still-critical-but-new-treatment-is-available.html" />
    <meta name="parsely-type" content="NewsArticle" />
    <meta name="parsely-image-url" content="http://images.techhive.com/images/article/2016/04/blog-31_apr15_image-1-100656409-orig.jpg" />
    <meta name="parsely-pub-date" content="2016-04-15T20:52:00Z" />
    <meta name="parsely-section" content="Data Analytics" />
    <meta name="parsely-author" content="Robert Shields" />
    <meta name="parsely-tags" content="blog: Great Data Thinking,type: opinion,source: computerworld,original" />





  <script src="http://a.postrelease.com/serve/load.js?async=true"></script>


















  <script src="/www/js/analytics/mouse_down.js?v=20160525110859"></script>

  <script>
  dataLayer=[{
         'primaryCategory': 'Data Analytics',
         'articleType': 'Opinion',
         'articleId': '3057179',
         'blogName':'Great Data Thinking',

          'brandpost':'true',


          'parselyEnabled':'true',
          'parselyDomain':'computerworld.com',
        'ga_enabled':'true',

        'viglinksId':'f6962bb9927d153082cdc6b572121599'
  }];
  </script>














  <!-- Google Tag Manager -->
  <noscript><iframe src="//www.googletagmanager.com/ns.html?GTM-WBVXW9"
  height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
  <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
  new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
  j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
  '//www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
  })(window,document,'script','dataLayer','GTM-WBVXW9');</script>
  <!-- End Google Tag Manager -->


</head>



  <body id="article3057179" class="article blog1484 computerworld">














<!-- BEGIN PAGE HEADER -->
<header id="banner" >

    <div class="ad">





























        <div id="topleaderboard" class="">
        </div>
        <script type="text/javascript">
          IDG.GPT.addDisplayedAd("topleaderboard", "true");
          $('#topleaderboard').responsiveAd({screenSize:'971 1115', scriptTags: []});
        </script>





    </div>



      <script>
      if(!$thm.deviceWidthAtLeast($thm.deviceBreakpoints.tablet)){
      $('.main-header').insertBefore($('#banner .ad'));
      }
    </script>


    <div class="topics-wrap">
        <section class="topics">
            <nav id="scrollable">






























    <!-- blx4 #987 blox4.raw_link_list  -->








































  <ul>

    <li class="header">Trending<span class="colon">:</span></li>



        <li><a href="/article/3074548/microsoft-windows/microsoft-has-been-conning-windows-users-for-two-months.html" >Microsoft has been conning Windows users</a></li>



        <li><a href="/article/3073537/android/android-apps-on-chromebooks.html" >Android apps on Chromebooks are a big deal</a></li>



        <li><a href="http://www.computerworld.com/newsletters/signup.html" target="_blank" rel="nofollow">Subscribe to newsletters</a></li>



        <li><a href="/resources" >Resources/White Papers</a></li>


  </ul>





            </nav>

            <div class="fading-gradient"></div>


            <nav class="tools">
              <ul class="user">

                        <li class="search-icon"><a href="#search" id="search-btn"><i class="ss-icon">Search</i></a></li>

                   </ul>
                   <ul class="social">















  <li><a href="https://twitter.com/computerworld" itemprop="sameAs" rel="nofollow" target="_blank" onclick="brandFollowTrack('Twitter')"><i class="ss-icon ss-social-circle ss-twitter"></i></a></li>



  <li><a href="https://www.linkedin.com/company/computerworld" itemprop="sameAs" rel="nofollow" target="_blank" onclick="brandFollowTrack('LinkedIn')"><i class="ss-icon ss-social-circle brand ss-linkedin"></i></a></li>



  <li><a href="https://www.facebook.com/Computerworld" itemprop="sameAs" rel="nofollow" target="_blank" onclick="brandFollowTrack('Facebook')"><i class="ss-icon ss-social-circle brand ss-facebook"></i></a></li>



  <li><a href="https://plus.google.com/+computerworld/posts" itemprop="sameAs" rel="publisher" target="_blank" onclick="brandFollowTrack('Google+')"><i class="ss-icon ss-social-circle brand ss-googleplus"></i></a></li>







  <li><a href="/about/rss/" target="_blank"><i class="ss-icon ss-social-circle ss-rss"></i></a></li>




  <li class="more-social"><i class="ss-icon ss-ellipsis"></i></li>

                   </ul>
               </nav>

        </section>
      </div>


           <div id="sticky-anchor"></div>

      <section id="sticky-head" class="main">
          <div class="wrapper">
              <div class="masthead">

                  <button><i class="ss-icon ss-rows"></i></button>

                  <a href="/"><span class="ir logo">computerworld</span></a>

                <!--
                <img class="print-only" src="/www.idge.ctw/images/computerworld-logo-print.png">
                -->

                <img class="print-only" src="http://idge.staticworld.net/ctw/computerworld-logo-print.png">

              </div>
              <div id="reading-tools-wrapper">


                  <nav class="tools">

                      <ul class="user">






                      </ul>
                  </nav>
              </div>


                <div class="insider-signin">

                  <span class="signin-register">
                    <a href="/learn-about-insider/"><span class="insider"></span></a> <a href="javascript://" onclick="IDG.insiderReg.registerLinkEvent('insider-reg-signin')" class="js-open-modal signin" data-modal-id="insider-popup">Sign In</a> <span class="divider">|</span> <a href="javascript://" onclick="IDG.insiderReg.registerLinkEvent('insider-reg-form-short')" class="js-open-modal register" data-modal-id="insider-popup">Register</a>
                  </span>

                  <div id="welcome-message" class="insider-list">
                        <div id="insider-selector">
                            <div id="insider-welcome" class="label"></div>

                            <div class="stories">

                              <div class="personalization">Hi<span id="person-first-name"></span>! Here are the latest Insider stories.</div>



















































































  <ul>














































<li><a  href="/article/3075346/it-management/it-s-new-imperative-partnering-to-shape-the-future.html">IT’s new imperative: Partnering to shape the future</a></li>

















































<li><a  href="/article/3074965/application-development/microsoft-powerapps-first-look-create-mobile-apps-without-coding.html">Microsoft PowerApps first look: Create mobile apps without coding</a></li>

















































<li><a  href="/article/3072469/wireless-networking/13-things-you-need-to-know-about-mu-mimo-wi-fi.html">13 things you need to know about MU-MIMO Wi-Fi</a></li>

















































<li><a  href="/article/3072889/application-development/how-data-virtualization-delivers-on-the-devops-promise.html">How data virtualization delivers on the DevOps promise</a></li>




  </ul>



                  <div class="sign-out-link">
                    <a class="more-insider" href="/insider/">More Insider</a>
                    <a class="sign-out" href="javascript://" onclick="IDG.insiderReg.logout()">Sign Out</a>
                  </div>
                </div>

                        </div>
                    </div>

                </div>
                <script>
                  $('.signin-register').hide();
                  $('#welcome-message').hide();
                </script>



          </div>
      </section>
      <section class="tools-expand  ">







              <form class="search" id="search-form" action="/search">
                  <label for="search-box">Search for</label>
            <input type="text" name="query" id="banner-search-term" value="" />
                  <input type="submit" id="banner-search-submit" value="Go" />
                  <input type="hidden" name="contentType" value="article,resource" />
                  <div class="search-suggest">Suggestions for you</div>
              </form>


          <div class="user">
              <form>
                  <label for="login-email">Insider email</label> <input id="login-email" type="text" />
              </form>
          </div>

      </section>





        <div id="sticky-anchor-nav"></div>


      <nav id="sticky-nav" class="main">
        <ul class="topnav">





                  <li><a href="/category/cloud-computing/" class="hasChildren">Cloud Computing<span class="ss-icon ss-navigatedown"></span></a>





                    <ul class="subnav">
                        <li><a href="/category/cloud-computing/">All Cloud Computing</a></li>

                        <li><a href="/category/cloud-security/">Cloud Security</a></li>

                        <li><a href="/category/cloud-storage/">Cloud Storage</a></li>

                        <li><a href="/category/hybrid-cloud/">Hybrid Cloud</a></li>

                        <li><a href="/category/private-cloud/">Private Cloud</a></li>

                        <li><a href="/category/public-cloud/">Public Cloud</a></li>

                    </ul>

              </li>





                  <li><a href="/category/computer-hardware/" class="hasChildren">Computer Hardware<span class="ss-icon ss-navigatedown"></span></a>





                    <ul class="subnav">
                        <li><a href="/category/computer-hardware/">All Computer Hardware</a></li>

                        <li><a href="/category/apple-mac/">Apple Mac</a></li>

                        <li><a href="/category/chromebooks/">Chromebooks</a></li>

                        <li><a href="/category/computer-peripherals/">Computer Peripherals</a></li>

                        <li><a href="/category/computer-processors/">Computer Processors</a></li>

                        <li><a href="/category/solid-state-drives/">Solid State Drives</a></li>

                        <li><a href="/category/windows-pcs/">Windows PCs</a></li>

                    </ul>

              </li>





                  <li><a href="/category/consumerization-of-it/" class="hasChildren">Consumerization of IT<span class="ss-icon ss-navigatedown"></span></a>





                    <ul class="subnav">
                        <li><a href="/category/consumerization-of-it/">All Consumerization of IT</a></li>

                        <li><a href="/category/byod/">Bring Your Own Device (BYOD)</a></li>

                        <li><a href="/category/gamification/">Gamification</a></li>

                        <li><a href="/category/home-tech/">Home Tech</a></li>

                        <li><a href="/category/location-based-services/">Location-Based Services</a></li>

                        <li><a href="/category/personal-technology/">Personal Technology</a></li>

                    </ul>

              </li>





                  <li><a href="/category/data-center/" class="hasChildren">Data Center<span class="ss-icon ss-navigatedown"></span></a>





                    <ul class="subnav">
                        <li><a href="/category/data-center/">All Data Center</a></li>

                        <li><a href="/category/data-storage/">Data Storage</a></li>

                        <li><a href="/category/disaster-recovery/">Disaster Recovery</a></li>

                        <li><a href="/category/high-performance-computing/">High-Performance Computing (HPC)</a></li>

                        <li><a href="/category/infrastructure-management/">Infrastructure Management</a></li>

                        <li><a href="/category/sustainable-it/">Sustainable IT</a></li>

                        <li><a href="/category/virtualization/">Virtualization</a></li>

                    </ul>

              </li>





                  <li><a href="/category/emerging-technology/" class="hasChildren">Emerging Technology<span class="ss-icon ss-navigatedown"></span></a>





                    <ul class="subnav">
                        <li><a href="/category/emerging-technology/">All Emerging Technology</a></li>

                        <li><a href="/category/3d-printing/">3D Printing</a></li>

                        <li><a href="/category/car-tech/">Car Tech</a></li>

                        <li><a href="/category/environment/">Environment</a></li>

                        <li><a href="/category/internet-of-things/">Internet of Things</a></li>

                        <li><a href="/category/robotics/">Robotics</a></li>

                        <li><a href="/category/space-technology/">Space Technology</a></li>

                        <li><a href="/category/wearables/">Wearables</a></li>

                    </ul>

              </li>





                  <li><a href="/category/enterprise-applications/" class="hasChildren">Enterprise Applications<span class="ss-icon ss-navigatedown"></span></a>





                    <ul class="subnav">
                        <li><a href="/category/enterprise-applications/">All Enterprise Applications</a></li>

                        <li><a href="/category/application-development/">Application Development</a></li>

                        <li><a href="/category/big-data/">Big Data</a></li>

                        <li><a href="/category/business-intelligence/">Business Intelligence (BI)</a></li>

                        <li><a href="/category/business-process-management/">Business Process Management (BPM)</a></li>

                        <li><a href="/category/crm/">CRM</a></li>

                        <li><a href="/category/collaboration/">Collaboration</a></li>

                        <li><a href="/category/content-document-management/">Content/Document Management</a></li>

                        <li><a href="/category/data-analytics/">Data Analytics</a></li>

                        <li><a href="/category/database-management/">Database Management</a></li>

                        <li><a href="/category/desktop-apps/">Desktop Apps</a></li>

                        <li><a href="/category/enterprise-architecture/">Enterprise Architecture</a></li>

                        <li><a href="/category/enterprise-resource-planning/">Enterprise Resource Planning (ERP)</a></li>

                        <li><a href="/category/java-development/">Java Development</a></li>

                        <li><a href="/category/open-source-tools/">Open Source</a></li>

                        <li><a href="/category/social-business/">Social Business</a></li>

                        <li><a href="/category/software-integration/">Software Integration</a></li>

                        <li><a href="/category/software-as-a-service/">Software as a Service (SaaS)</a></li>

                        <li><a href="/category/unified-communications/">Unified Communications (UC)</a></li>

                    </ul>

              </li>





                  <li><a href="/category/it-management/" class="hasChildren">IT Management<span class="ss-icon ss-navigatedown"></span></a>





                    <ul class="subnav">
                        <li><a href="/category/it-management/">All IT Management</a></li>

                        <li><a href="/category/digital-transformation/">Digital Transformation</a></li>

                        <li><a href="/category/h1b/">H-1B</a></li>

                        <li><a href="/category/it-careers/">IT Careers</a></li>

                        <li><a href="/category/it-industry/">IT Industry</a></li>

                        <li><a href="/category/it-outsourcing/">IT Outsourcing</a></li>

                        <li><a href="/category/it-project-management/">IT Project Management</a></li>

                        <li><a href="/category/it-skills-training/">IT Skills &amp; Training</a></li>

                        <li><a href="/category/technology-law-regulation/">Technology Law &amp; Regulation</a></li>

                    </ul>

              </li>





                  <li><a href="/category/internet/" class="hasChildren">Internet<span class="ss-icon ss-navigatedown"></span></a>





                    <ul class="subnav">
                        <li><a href="/category/internet/">All Internet</a></li>

                        <li><a href="/category/e-commerce/">E-commerce</a></li>

                        <li><a href="/category/search/">Search</a></li>

                        <li><a href="/category/social-media/">Social Media</a></li>

                        <li><a href="/category/web-apps/">Web Apps</a></li>

                        <li><a href="/category/web-browsers/">Web Browsers</a></li>

                    </ul>

              </li>





                  <li><a href="/category/mobile-wireless/" class="hasChildren">Mobile &amp; Wireless<span class="ss-icon ss-navigatedown"></span></a>





                    <ul class="subnav">
                        <li><a href="/category/mobile-wireless/">All Mobile &amp; Wireless</a></li>

                        <li><a href="/category/android/">Android</a></li>

                        <li><a href="/category/apple-ios/">Apple iOS</a></li>

                        <li><a href="/category/blackberry/">BlackBerry</a></li>

                        <li><a href="/category/mobile-apps/">Mobile Apps</a></li>

                        <li><a href="/category/mobile-device-management/">Mobile Device Management</a></li>

                        <li><a href="/category/mobile-payments/">Mobile Payments</a></li>

                        <li><a href="/category/mobile-security/">Mobile Security</a></li>

                        <li><a href="/category/smartphones/">Smartphones</a></li>

                        <li><a href="/category/tablets/">Tablets</a></li>

                        <li><a href="/category/windows-phone-os/">Windows 10 Mobile</a></li>

                        <li><a href="/category/wireless-carriers/">Wireless Carriers</a></li>

                    </ul>

              </li>





                  <li><a href="/category/networking/" class="hasChildren">Networking<span class="ss-icon ss-navigatedown"></span></a>





                    <ul class="subnav">
                        <li><a href="/category/networking/">All Networking</a></li>

                        <li><a href="/category/network-security/">Network Security</a></li>

                        <li><a href="/category/servers/">Servers</a></li>

                        <li><a href="/category/wireless-networking/">Wireless Networking</a></li>

                    </ul>

              </li>





                  <li><a href="/category/operating-systems/" class="hasChildren">Operating Systems<span class="ss-icon ss-navigatedown"></span></a>





                    <ul class="subnav">
                        <li><a href="/category/operating-systems/">All Operating Systems</a></li>

                        <li><a href="/category/chrome-os/">Chrome OS</a></li>

                        <li><a href="/category/linux/">Linux</a></li>

                        <li><a href="/category/mac-os-x/">Mac OS X</a></li>

                        <li><a href="/category/microsoft-windows/">Windows 10</a></li>

                    </ul>

              </li>





                  <li><a href="/category/security/" class="hasChildren">Security<span class="ss-icon ss-navigatedown"></span></a>





                    <ul class="subnav">
                        <li><a href="/category/security/">All Security</a></li>

                        <li><a href="/category/application-security/">Application Security</a></li>

                        <li><a href="/category/cyberattacks/">Cyberattacks</a></li>

                        <li><a href="/category/cybercrime-hacking/">Cybercrime &amp; Hacking</a></li>

                        <li><a href="/category/data-privacy/">Data Privacy</a></li>

                        <li><a href="/category/data-security/">Data Security</a></li>

                        <li><a href="/category/encryption/">Encryption</a></li>

                        <li><a href="/category/endpoint-security/">Endpoint Security</a></li>

                        <li><a href="/category/malware-vulnerabilities/">Malware &amp; Vulnerabilities</a></li>

                    </ul>

              </li>





                  <li><a href="/category/vertical-it/" class="hasChildren">Vertical IT<span class="ss-icon ss-navigatedown"></span></a>





                    <ul class="subnav">
                        <li><a href="/category/vertical-it/">All Vertical IT</a></li>

                        <li><a href="/category/financial-it/">Financial Services IT</a></li>

                        <li><a href="/category/government-it/">Government IT</a></li>

                        <li><a href="/category/healthcare-it/">Healthcare IT</a></li>

                        <li><a href="/category/retail-it/">Retail IT</a></li>

                        <li><a href="/category/small-enterprise-it/">Small Enterprise IT</a></li>

                    </ul>

              </li>

      </ul>

        <ul class="secondary">












<li><a href="/about/sitemap.html">All Topics</a></li>
<li><a href="/news/">News</a></li>
<li><a href="/feature/">Features</a></li>
<li><a href="/reviews/">Reviews</a></li>
<li><a href="/blogs/">Blogs</a></li>
<li><a href="/opinion/">Opinions</a></li>
<li><a href="/insider/">Insider</a></li>
<li><a href="/blog/shark-tank/">Shark Tank</a></li>
<li><a href="/slideshows/">Slideshows</a></li>
<li><a href="/video/">Video</a></li>
<li><a href="/tag/computerworlddigitalmagazine/">Digital Magazine</a></li>
<li><a href="/resources/download-library">Digital Downloads</a></li>
<li><a href="/newsletters/signup.html">Newsletters</a></li>
<li><a target="_blank" rel="nofollow" href="http://www.ereg.me/IDGEvents">Computerworld Events</a></li>
<li><a href="http://itjobs.computerworld.com" target="_blank" rel="nofollow">IT Job Search</a></li>
<li><a href="/resources/">Resources/White Papers</a></li>

        </ul>
    </nav>



    <script>
      if(!$thm.deviceWidthAtLeast($thm.deviceBreakpoints.tablet)){
      $('div.insider-signin').prependTo('section.topics nav.tools');
      }
    </script>


    <script>
    $(document).ready(function(){
      if($thm.deviceWidthAtLeast($thm.deviceBreakpoints.tablet)){
        $("nav ul li a.hasChildren").attr("href", "#").addClass('notMobile');
        $('nav#sticky-nav ul.secondary').addClass('topnav');
      }
      if(!$thm.deviceWidthAtLeast($thm.deviceBreakpoints.tablet)){
        $("nav#sticky-nav > ul").switchClass("topnav", "primary");
      }
    });
    </script>



      <script>
      if(!$thm.deviceWidthAtLeast($thm.deviceBreakpoints.tablet)){
      $('#sticky-anchor, section.main, section.topics, section.tools-expand, #sticky-anchor-nav, nav.main').insertBefore($('#banner .ad'));
      $(document).ready(function(){
        $('#banner section.tools-expand').addClass('top-margin');
      });
      }
    </script>




</header>



<script>
$(document).ready(function(){
  $('body').addClass('insider-plus');

  var headerHeight = $('header#banner').height(); // height for desktop on load not fixed
  $('header#banner nav#sticky-nav.main.no-stick').css("top", headerHeight);

  if(!$thm.deviceWidthAtLeast($thm.deviceBreakpoints.tablet)){
    var stickyHeaderHeight = $('section.topics').height() + $('#sticky-head.main').height();
    stickyHeaderHeight = stickyHeaderHeight - 8;
  }

  // Listen for orientation changes - mobile devices have a problem resizing fixed position elements on
  // orientation change from portrait to landscape so scrolling to current position of header
  var supportsOrientationChange = "onorientationchange" in window,
    orientationEvent = supportsOrientationChange ? "orientationchange" : "resize";

  if($thm.deviceWidthAtLeast($thm.deviceBreakpoints.tablet)){
    window.addEventListener(orientationEvent, function() {
      var top = $('section#sticky-head.main').offset().top;
      if (top < 170) {
        top = 0; // so we don't miss the top leaderboard or ticker on change
      } else {
        top = top - 50; //acknowledging "shorter" top on landscape than portrait
      }
      if (window.orientation == 90 || window.orientation == -90) {
        window.scrollTo(0, top);
      }
    }, false);
  }

  if(!$thm.deviceWidthAtLeast($thm.deviceBreakpoints.tablet)){
    window.addEventListener(orientationEvent, function() {
      var top = $('section#sticky-head.main').offset().top;
      if (top < 50) {  // top is fixed on mobile so this is when user is at very top
        top = 5; // scroll 5 pixels from 0 to resize fixed header from portrait
      } else {
        top = top - 40;
      }
      if (window.orientation == 90 || window.orientation == -90) {
        window.scrollTo(0, top);
      }
    }, false);
  }

  //nexus 7 is sized mobile in portrait and tablet in landscape
  var ua = navigator.userAgent.toLowerCase();
  var isAndroid = ua.indexOf("android") > -1;
  window.addEventListener(orientationEvent, function() {
      if(isAndroid && (screen.width == 600 || screen.width == 960)) {
        location.reload(true); // refreshing page for changes to header
      }
  }, false);

  if ($thm.deviceWidthAtLeast($thm.deviceBreakpoints.desktop) && !$('html').hasClass('touch')){
    $("ul.social li.more-social").mouseenter(function() {
      $('ul.social > li').addClass('display-icon');
    });
    $("ul.social").mouseleave(function() {
      $('ul.social > li').removeClass('display-icon');
    });
  };

  if($thm.deviceWidthAtLeast($thm.deviceBreakpoints.tablet) && $('html').hasClass('touch')){
    $("ul.social li.more-social").click(function() {
      $('ul.social > li').toggleClass('display-icon');
    });
  };

});

function sticky_relocate() {
  var headerHeight = $('header#banner').height(); // height for desktop
  //$('header#banner nav#sticky-nav.main').css("top", headerHeight);

  var winHeight = $(window).height();
  // desktop
  if ( $thm.deviceWidthAtLeast($thm.deviceBreakpoints.wide ) ){
    winHeight = winHeight - 40;
    if (window.orientation == 90 || window.orientation == -90) {
      winHeight = winHeight + 50; // landscape tablet 1024
    }
  }// tablet
  else if ( $thm.deviceWidthAtLeast($thm.deviceBreakpoints.tablet) && !$thm.deviceWidthAtLeast($thm.deviceBreakpoints.wide) ) {
    winHeight = winHeight + 40;
  } else { // mobile
    winHeight = winHeight + 10;
  }

  $('#banner nav#sticky-nav.main').css("height", winHeight); // flyout nav height

  // tablet and desktop
  if($thm.deviceWidthAtLeast($thm.deviceBreakpoints.tablet)){
      var window_top = $(window).scrollTop();
      var div_top = $('#sticky-anchor').offset().top;
      if (window_top > div_top) {
          $('#sticky-head').addClass('stick');
          $('#page-wrapper').addClass('top-pad');
          $('#banner .ad').addClass('top-margin');
      } else {
          $('#sticky-head').removeClass('stick');
          $('#page-wrapper').removeClass('top-pad');
          $('#banner .ad').removeClass('top-margin');
      }
  }
  // mobile
  if(!$thm.deviceWidthAtLeast($thm.deviceBreakpoints.tablet)){
    $('section.topics').addClass('stick-topics');
    $('#sticky-head').addClass('stick-head');
    $('#page-wrapper').addClass('top-pad');
    $('#banner .ad').addClass('top-margin');
    $('#banner section.tools-expand').addClass('search');

    var window_top = $(window).scrollTop();
    var div_top = $('#sticky-anchor').offset().top + 30;
      if (window_top > div_top) {
        $('#sticky-head').addClass('stick');
        $('.topics.stick-topics, #search-form').addClass('remove');
        $('li.search-icon').fadeOut("fast");
      } else {
        $('#sticky-head').removeClass('stick');
        $('.topics.stick-topics, #search-form').removeClass('remove');
        $('li.search-icon').fadeIn("fast");
      }
  }// end mobile

  // tablet and desktop flyout nav
  if($thm.deviceWidthAtLeast($thm.deviceBreakpoints.tablet)){
      if ($('#sticky-head').hasClass('stick')) {
        var stickyHeaderHeight = $('section.main').height(); // height of the fixed header for tablet and desktop
        stickyHeaderHeight = 52; // setting fixed height - same across all sites
      $('header#banner nav#sticky-nav.main').css("top", stickyHeaderHeight);
          $('#sticky-nav').addClass('stick');
          $('#sticky-nav').removeClass('no-stick');
      } else {
        stickyHeaderHeight = $('header#banner').height();
        $('header#banner nav#sticky-nav.main').css("top", stickyHeaderHeight);
          $('#sticky-nav').removeClass('stick');
          $('#sticky-nav').addClass('no-stick');
      }

  }// end tablet/desktop


  // mobile flyout nav
  if(!$thm.deviceWidthAtLeast($thm.deviceBreakpoints.tablet)){

      var stickyHeaderHeight = $('section.topics').height() + $('#sticky-head.main.stick-head').height(); // height of the fixed header for mobile
      stickyHeaderHeight = stickyHeaderHeight - 8;  // 8 seems appropriate
      $('header#banner nav#sticky-nav.main').css("top", stickyHeaderHeight);
        $('#sticky-nav').addClass('stick');

        if ($('#sticky-head').hasClass('stick')) {
          $('#sticky-head.stick').siblings('#sticky-nav').addClass('nav-stick');
          $('#sticky-head').siblings('#sticky-nav').removeClass('no-stick');
        } else {
          $('#sticky-head').siblings('#sticky-nav').removeClass('nav-stick');
          $('#sticky-head').siblings('#sticky-nav').addClass('no-stick');
        }

  }// end mobile


} // end sticky_relocate

$(function () {
    $(window).scroll(sticky_relocate);
    sticky_relocate();
});
</script>



  <div id="insider-popup" class="modal-box ">

    <div class="insider-modal-wrapper">

      <div class="modal-header-title">
        <div class="modal-banner-insider"><span class="insider"></span></div>
          <div class="modal-close close">×</div>
        </div>

        <div class="modal-body">


            <script>
$(document).ready(function(){
  $('a[data-link]').click(function() {

      //get this link's dataLink value
      var dataLink = $(this).attr('data-link');

      //select the div with the same value
      var toKeep = 'div[data-link="'+dataLink+'"]';

      //select data-link divs that are not the above div
      $('div[data-link]').not(toKeep).hide();

      if($(this).hasClass('link-transition')){
        $(toKeep).fadeIn(800);
      } else {
        $(toKeep).show();
      }

      //prevent location change
      return false;
  });


});
</script>




        </div>

        <div class="modal-footer">
          <div class="modal-close close-btn">Close</div>
        </div>
    </div>

  </div>

  <script>
    $(document).ready(function(){

      var appendthis =  ("<div class='modal-overlay modal-close'></div>");
      if($thm.deviceWidthAtLeast($thm.deviceBreakpoints.tablet)){
        $('.modal-box').css({
          //top: ($(window).height() - $(".modal-box").outerHeight()) / 2,
          top: 20,
          left: 0
        });
      }


      if(!$thm.deviceWidthAtLeast($thm.deviceBreakpoints.tablet)){
        $('.modal-box').height($(document).height());
        $('.modal-box').css('top','0');
      }

      var userAg = navigator.userAgent;

      $('a[data-modal-id]').click(function(e) {
        e.preventDefault();
        $('body').append(appendthis);
        $('.modal-overlay').fadeTo(500, 0.9);
        $('.modal-overlay').height($(document).height());
        var modalBox = $(this).attr('data-modal-id');

        if ($(".modal-box").hasClass('triggered') || (userAg.indexOf("Firefox")!=-1)) {
          $('#'+modalBox).fadeIn($(this).data()); // triggered by something other than onclick of link such as scroll to point in window
        } else {
          if(!$thm.deviceWidthAtLeast($thm.deviceBreakpoints.tablet)){
            $('#'+modalBox).toggle('slide', {direction: 'up'}, 800);
          } else {
              $('#'+modalBox).toggle('slide', {direction: 'left'}, 600);
          }
        }

        if(!$thm.deviceWidthAtLeast($thm.deviceBreakpoints.tablet)){
          $("html, body").animate({
                  scrollTop: 0
              }, 700);


              $('a.btn.continue-on').click(function() {
                $('html, body').animate({
                  scrollTop: 0
                }, 500);
              });

              return false;

        }
        if (navigator.userAgent.match(/(iPad)/)) {
          $("html, body").animate({
                  scrollTop: 0
              }, 500);
        }
      });


      $(".modal-close, .modal-overlay").click(function() {
        $('.select2-drop').css('display','none');
        $(".modal-overlay").fadeOut(500, function() {
          $(".modal-overlay").remove();
        });
        if ($(".modal-box").hasClass('triggered') || (userAg.indexOf("Firefox")!=-1)) {
          $(".modal-box").fadeOut(500);
        } else {
          if(!$thm.deviceWidthAtLeast($thm.deviceBreakpoints.tablet)){
            $('.modal-box').toggle('slide', {direction: 'up'}, 800);
          } else {
            $('.modal-box').toggle('slide', {direction: 'left'}, 600);
          }
        }
      });

    });
  </script>


<!-- END PAGE HEADER -->




    <div id="page-wrapper" class="page-wrapper">
















<div id="skinAdTarget"></div>













































      <section role="main">

        <article itemscope itemtype="http://schema.org/BlogPosting"  class="blog" >





















      <div class="ticker-ad ad">





























        <div id="ticker" class="">
        </div>
        <script type="text/javascript">
          IDG.GPT.addDisplayedAd("ticker", "true");
          $('#ticker').responsiveAd({screenSize:'971 1115', scriptTags: []});
        </script>





      </div>






<!-- Events Header -->





















































<!-- //end Events Header -->

















































<header>










  <nav class="breadcrumbs horiz">
    <ul itemscope itemtype="http://data-vocabulary.org/Breadcrumb">
      <li><a href="/" itemprop="url"><span itemprop="title">Home</span></a></li>



          <li><a href="/category/enterprise-applications/" itemprop="url"><span itemprop="title">Enterprise Applications</span></a></li>


          <li><a href="/category/data-analytics/" itemprop="url"><span itemprop="title">Data Analytics</span></a></li>


    </ul>
  </nav>



    <div class="blog-byline vcard author " itemscope itemtype="http://schema.org/Person" itemprop="author">

      <div class="brand sponsored">

























  <a href="/blog/great-data-thinking/">
    <img  class="originalImage  imgId100580223 " src="http://core2.staticworld.net/images/article/2015/04/informatica_150x150-100580223-orig.jpeg" width="34" width="34" alt="Great Data Thinking" itemprop="image" />
  </a>






        <div class="blog-branding-text">

            <span class="brandpost">BrandPost</span> Sponsored by Informatica <span class="divider">|</span> <a class="learn-more" href="#">Learn More</a>

          <div class="blog-title"><a href="/blog/great-data-thinking/">Great Data Thinking</a></div>


        </div>
      </div>

        <div class="about">
          <div class="about-title">About <span class="divider">|</span> <a href="/blog/great-data-thinking/index.rss"><i class="ss-icon ss-social ss-rss"></i></a></div>
          <p>Thought-provoking analysis and commentary on how organizations can transform their businesses with truly great data.</p>
        </div>

    </div>







      <div class="category">sponsored</div>




  <h1 itemprop="headline">Healthcare Data Protection and Privacy Prognosis—Still Critical but New Treatment is Available</h1>

</header>













<section class="epo" id="drr-top-ad">










































<div class="ad">

































        <div id="topimu" class="adunit">
        </div>
        <script type="text/javascript">
          IDG.GPT.addDisplayedAd("topimu", "true");
          $('#topimu').responsiveAd({screenSize:'971 1115', scriptTags: []});
        </script>







</div>



</section>















      <figure itemprop="image" itemscope itemtype="http://schema.org/ImageObject" class="hero-img">
      <meta itemprop="representativeOfPage" content="1">

          <img src="http://images.techhive.com/images/article/2016/04/blog-31_apr15_image-1-100656409-primary.idge.jpg" alt="blog 31 apr15 image 1" itemprop="contentUrl" />
          <figcaption>


          </figcaption>
      </figure>




















































<div id="sharer" class="sidecar">
<ul id="share-tool">
  <li class="close-btn"> <i class="ss-icon ss-delete"></i> </li>

  <li class="sosh">
    <a href="https://twitter.com/intent/tweet?url=http%3A%2F%2Fwww.computerworld.com%2Farticle%2F3057179%2Fdata-analytics%2Fhealthcare-data-protection-and-privacy-prognosis-still-critical-but-new-treatment-is-available.html&via=computerworld&text=Healthcare+Data+Protection+and+Privacy+Prognosis%E2%80%94Still+Critical+but+New" target="_blank" onclick="sharingTrack('Twitter-vert', 'twshare')">
      <img class="sidecar-icon" src="http://idge.staticworld.net/images/twitter.svg" />
    </a>
  </li>
  <li class="sosh">
    <a href="https://www.facebook.com/sharer/sharer.php?u=http%3A%2F%2Fwww.computerworld.com%2Farticle%2F3057179%2Fdata-analytics%2Fhealthcare-data-protection-and-privacy-prognosis-still-critical-but-new-treatment-is-available.html" target="_blank" onclick="sharingTrack('Facebook-vert', 'fblike')">
      <img class="sidecar-icon" src="http://idge.staticworld.net/images/facebook.svg" />
    </a>
  </li>
  <li class="sosh">
    <a href="http://www.linkedin.com/shareArticle?url=http%3A%2F%2Fwww.computerworld.com%2Farticle%2F3057179%2Fdata-analytics%2Fhealthcare-data-protection-and-privacy-prognosis-still-critical-but-new-treatment-is-available.html&title=Healthcare+Data+Protection+and+Privacy+Prognosis%E2%80%94Still+Critical+but+New" target="_blank" onclick="sharingTrack('LinkedIn-vert')">
      <img class="sidecar-icon" src="http://idge.staticworld.net/images/linkedin.svg" />
    </a>
  </li>
  <li class="sosh">
    <a href="https://plus.google.com/share?url=http%3A%2F%2Fwww.computerworld.com%2Farticle%2F3057179%2Fdata-analytics%2Fhealthcare-data-protection-and-privacy-prognosis-still-critical-but-new-treatment-is-available.html" target="_blank" onclick="sharingTrack('GooglePlus-vert', 'googplus')">
      <img class="sidecar-icon" src="http://idge.staticworld.net/images/googleplus.svg" />
    </a>
  </li>
  <li class="sosh hide">
    <a href="http://reddit.com/submit?url=http%3A%2F%2Fwww.computerworld.com%2Farticle%2F3057179%2Fdata-analytics%2Fhealthcare-data-protection-and-privacy-prognosis-still-critical-but-new-treatment-is-available.html&title=Healthcare+Data+Protection+and+Privacy+Prognosis%E2%80%94Still+Critical+but+New" target="_blank" onclick="sharingTrack('Reddit-vert')">
      <img class="sidecar-icon" src="http://idge.staticworld.net/images/reddit.svg" />
    </a>
  </li>
  <li class="sosh hide">
    <a href="http://www.stumbleupon.com/submit?url=http%3A%2F%2Fwww.computerworld.com%2Farticle%2F3057179%2Fdata-analytics%2Fhealthcare-data-protection-and-privacy-prognosis-still-critical-but-new-treatment-is-available.html" target="_blank" onclick="sharingTrack('ShareStumbleupPrint-vert')">
      <img class="sidecar-icon" src="http://idge.staticworld.net/images/stumbleupon.svg" />
    </a>
  </li>
  <li class="sosh hide">
    <a href="#email" id="email-icon">
      <img class="sidecar-icon" src="http://idge.staticworld.net/images/mail.svg" />
    </a>
  </li>
  <li class="sosh hide print">
    <a href="javascript:window.print();">
      <img class="sidecar-icon" src="http://idge.staticworld.net/images/print.svg" />
    </a>
  </li>
  <li class="more">
    <div class="more-icon" onclick="document.getElementById('share-tool').className = 'expand';"></div>
  </li>

</ul>
</div>


<script type="text/javascript">
  function encodeQueryData(params) {
    var ret = [];
    for (var paramKey in params) {
      ret.push(encodeURIComponent(paramKey) + "=" + encodeURIComponent(params[paramKey]));
    }
    return ret.join("&");
  }
  $(document).ready(function() {
    $(document).on("click","#email-icon",function(event) {
      event.preventDefault();
      sharingTrack('Email-vert');
      $('#emailModal').fadeIn(800);
    });

    $(document).on("click","#emailModal .close-btn",function(event) {
      event.preventDefault();
            $('#emailModal').hide();
            $("#emailModal .eml-friend").show();
            $(".eml-friend-success").hide();
            $(".eml-friend-error").hide();
      $('#emailModal').fadeOut(200);
      $('#email-to').val('');
      $('#email-from').val('');
      $('#name').val('');
      $('#personalization').val('');
      $('#eml-from-address-message').html("");
      $('#eml-to-address-message').html("");
      $('#eml-friend-captcha-message').html("");
      Recaptcha.reload();
    });

    $(document).on("submit","#emailModal form",function(event) {
      event.preventDefault();
      var $form = $(this);
      var action = $form.attr('action');
      var formData = $form.serialize();
      var emailFrom = $('#email-from').val();
      var emailTo = $('#email-to').val();
      var uresponse=$('#recaptcha_response_field').val()
      $('#eml-from-address-message').html("");
      $('#eml-to-address-message').html("");
      $('#eml-friend-captcha-message').html("");
      if (isValidEmailAddress(emailFrom) && isValidEmailAddress(emailTo) && uresponse !="") {
        // eloqua
        var eloquaParam = {
          AssetCountforCurrentCampaign : "1",
          AssetName : "",
          AssetTopic : "",
          AssetType : "",
          BuyingCycle : "",
          C_Address1 : "",
          C_Address2 : "",
          C_BusPhone : "",
          C_City : "",
          C_Company_Size1 : "",
          C_Country : "",
          C_EmailAddress : "",
          C_FirstName : "",
          C_Industry1 : "",
          C_Job_Role1 : "",
          C_LastName : "",
          C_State_Prov : "",
          C_Zip_Postal : "",
          ClientName : "",
          ProgramName : "",
          brand : "",
          elqFormName : "CentralRegistrationMasterForm",
          formId : "3062313",
          elqSiteId : 1856,
          elqCustomerGUID : elqCustomerGUID,
          elqCookieWrite : 0,
          friend_email : emailFrom,
          friend_article_title : "Healthcare Data Protection and Privacy Prognosis—Still Critical but New Treatment is Available",
          friend_taxo : "Enterprise Applications",
          friend_source : "Computerworld",
          friend_article_url : "http://www.computerworld.com/article/3057179/data-analytics/healthcare-data-protection-and-privacy-prognosis-still-critical-but-new-treatment-is-available.html",
          device_platform : navigator.userAgent
        };
        $.ajax("http://now.eloqua.com/e/f2.aspx", {
          type: 'GET',
          data: eloquaParam,
          success: function(data, textStatus, xhr) {
          },
          error: function(xhr, textStatus, errorThrown) {
          }
        });
        // eloqua
        var eloquaParamMini = {
          elqCustomerGUID : elqCustomerGUID,
          friend_taxo : "Enterprise Applications",
          friend_source : "Computerworld",
          device_platform : navigator.userAgent
        };
        formData += "&" +encodeQueryData(eloquaParamMini);

        // email
        $.ajax(action, {
          type: 'POST',
          data: formData,
          success: function(data, textStatus, xhr) {
            if(data=='no-match') {
              Recaptcha.reload();
              $('#eml-friend-captcha-message').html("Invalid recaptcha - please re-enter the text below.");
            }
            else {
              $("#emailModal .eml-friend").hide();
              $(".eml-friend-success").fadeIn(800);
            }
          },
          error: function(xhr, textStatus, errorThrown) {
            $("#emailModal .eml-friend").hide();
            $(".eml-friend-error").fadeIn(800);
          }
        });
      }
      else {
        if(uresponse==""){
          $('#eml-friend-captcha-message').html("Please type the text below.");
        }
        if(!isValidEmailAddress(emailTo)){
          $('#eml-to-address-message').html("Please enter a valid email address.");
        }
        if(!isValidEmailAddress(emailFrom)){
          $('#eml-from-address-message').html("Please enter a valid email address.");
        }
      }
    });
  });

</script>


<script type="text/javascript">
 var RecaptchaOptions = {
    theme : 'clean'
 };
 </script>
<div class="modal eml-friend-wrapper" id="emailModal" style="display:none;">
  <form class="eml-friend" action="/eloquaemail/article?eloqua=true&id=3057179">
    <input type="hidden" name="title" value="Healthcare Data Protection and Privacy Prognosis—Still Critical but New Treatment is Available" />
    <input type="hidden" name="url" value="http://www.computerworld.com/article/3057179/data-analytics/healthcare-data-protection-and-privacy-prognosis-still-critical-but-new-treatment-is-available.html" />
    <a href="#" class="close-btn"><i class="ss-icon ss-delete"></i></a>
    <h3>Email a friend</h3>

    <div class="input-row">
      <div id="eml-to-address-message" class="error-msg"></div>
      <h4 class="input-label">To</h4>
      <input id="email-to" value="" placeholder="Email address (required)" type="email" name="recipients" tabindex="1">
      <p class="addl-text">Use commas to separate multiple email addresses</p>
    </div>
    <hr />
    <div class="input-row">
      <div id="eml-from-address-message" class="error-msg"></div>
      <h4 class="input-label">From</h4>
      <input id="name" value="" placeholder="Name (optional)" type="text" name="name" tabindex="2">
    </div>
    <div class="input-row">
      <input id="email-from" value="" placeholder="Email address (required)" type="email" name="sender" tabindex="3">
    </div>
    <div class="input-row">
      <textarea name="message" placeholder="Check out this article I found on Computerworld." id="personalization" tabindex="4"></textarea>
    </div>
    <hr />
    <div class="input-row">
      <div id="eml-friend-captcha-message" class="error-msg"></div>
    </div>
    <script type="text/javascript"
         src="http://www.google.com/recaptcha/api/challenge?k=6LdPbfsSAAAAAB89GpzHsyYe-AGzZXYy9K_4KAnF">
      </script>
      <noscript>
         <iframe src="http://www.google.com/recaptcha/api/noscript?k=6LdPbfsSAAAAAB89GpzHsyYe-AGzZXYy9K_4KAnF"
             height="300" width="500" frameborder="0"></iframe><br>
         <textarea id="recaptcha_challenge_field" name="recaptcha_challenge_field" rows="3" cols="40">
         </textarea>
         <input id="recaptcha_response_field" type="hidden" name="recaptcha_response_field"
             value="manual_challenge">
     </noscript>
    <hr />
    <div class="submit">
      <input type="submit" value="Send" class="input-submit" tabindex="5">
      <a href="/about/privacy.html" class="submit-privacy" target="_blank">Privacy Policy</a>
    </div>
  </form>

  <div class="eml-ty eml-friend-success" style="display:none;">
    <a href="#" class="close-btn"><i class="ss-icon ss-delete"></i></a>
    <h3>Thank you</h3>
    <p class="msg-sent">Your message has been sent.</p>
  </div>
  <div class="eml-friend-error" style="display:none;">
    <a href="#" class="close-btn"><i class="ss-icon ss-delete"></i></a>
    <h3>Sorry</h3>
    <p class="msg-sent">There was an error emailing this page.</p>
  </div>
</div>




  <div class="comment-count-mobile">














<div id="comment-bubble-idge">
  <a href="#comments" class="comment comments-bubble-cta" onclick="sharingTrack('Comment-vert', 'click')">
    <span id="comment-count" class="comment-count-bubble"></span><span id="comment-text" class="comment-text-bubble"> Comments</span>
  </a>
</div>
  </div>







  <div class="byline vcard author ">
    <div class="byline-wrapper">
      <p class="name" itemscope itemtype="http://schema.org/Person" itemprop="author">











































By <span class="fn" itemprop="name"><a rel="author" itemprop="url" href="/author/Robert-Shields/"><span itemprop="name">Robert Shields</span></a></span>
      </p>
























  <div class="meta">


        <a href="#follow" class="follow_btn">Follow</a>

      <ul >

        <!-- Always display RSS feed on hover: IDGMPM-6374 -->
        <li><a href="/author/Robert-Shields/index.rss"><i class="ss-icon ss-social ss-rss"></i></a></li>


        <li><a href="https://twitter.com/https://twitter.com/informaticacorp" rel="nofollow" onclick="followTrack()" target="_blank"><i class="ss-icon ss-social ss-twitter"></i></a></li>





        <li><a href="https://www.facebook.com/InformaticaCorporation" rel="nofollow" onclick="followTrack()" target="_blank"><i class="ss-icon ss-social ss-facebook"></i></a></li>



        <li><a href="https://www.linkedin.com/company/informatica" rel="nofollow" onclick="followTrack()" target="_blank"><i class="ss-icon ss-social ss-linkedin"></i></a></li>


      </ul>

  </div>



      <p class="dateline">



        <span itemprop="datePublished" content="2016-04-15T13:52-0700">Apr 15, 2016 1:52 PM
           PT
        </span>

      </p>
    </div>
  </div>












        <section class="bodee">















































    <div class="apart-alt tags">
    <span class="related">RELATED TOPICS</span>
    <ul itemprop="keywords">

      <li><a href="/category/data-analytics">Data Analytics</a></li>

      <li><a href="/category/healthcare-it">Healthcare IT</a></li>










    </ul>
  </div>



<div class="comment-count-main">














<div id="comment-bubble-idge">
  <a href="#comments" class="comment comments-bubble-cta" onclick="sharingTrack('Comment-vert', 'click')">
    <span id="comment-count" class="comment-count-bubble"></span><span id="comment-text" class="comment-text-bubble"> Comments</span>
  </a>
</div>
</div>





















<script type="text/javascript">

$(document).ready(function() {
  $('.articleBloxAd').filter( ":visible" ).each(function(index, item) {
    var id = $(item).attr('id');
    var divClass = $(item).attr('class');
    var adString = IDG.GPT.getLazyAdCode();
    $(item).replaceWith("<div id=\"" + id + "\" class=\"lazyload_blox_ad " + divClass + "\">" + adString + "</div>");
  });
  try {
    $("div.lazyload_blox_ad").lazyLoadAd({
          threshold    : 500,         // You can set threshold on how close to the edge ad should come before it is loaded. Default is 0 (when it is visible).
          forceLoad    : false,       // Ad is loaded even if not visible. Default is false.
          onLoad       : false,       // Callback function on call ad loading
          onComplete   : false,       // Callback function when load is loaded
          timeout      : 1500,        // Timeout ad load
          debug        : false,       // For debug use : draw colors border depends on load status
          xray         : false        // For debug use : display a complete page view with ad placements
    }) ;
  }
  catch (exception){
    console.log("error loading lazyload_ad " + exception);
  }
});

</script>









































<script type="text/javascript">

$(function() {
  var debug = false;
  var topImuHeight = 250;
  try {
    topImuHeight = getTallestNoScroll();
  }
  catch (e) {
    console.log(e);
  }
  var setInt = setInterval(checkHeight, 100);
  var count = 0;

  function checkHeight() {
    if ((topImuHeight > 0 && count > 10) || count > 50) {
      if (!topImuHeight > 0 ) {
        try {
          topImuHeight = getTallestWithScroll();
        }
        catch (e) {
          console.log(e);
        }
      }
      if (! topImuHeight > 0 || topImuHeight > 1200) {
        if (debug) {
          console.log("topimuheight is out of range so manually set to 250");
        }
        topImuHeight = 250;
      }
      clearInterval(setInt);
      executeDRR(topImuHeight);
    }
    else {
      if (debug) {
        console.log("have to check height again and counter is: " + count);
      }
      try {
        topImuHeight = getTallestNoScroll();
      }
      catch (e) {
        console.log(e);
      }
      count++;
    }
  }

  function getTallestNoScroll() {
      var bodyOffset = 0;
      var htmlHeight = 0;
      var htmlOffset = 0;

      if ($('#topimu div iframe').length) {
      var body = document.getElementById('topimu').getElementsByTagName('iframe')[0].contentWindow.document.body;
        var html = document.getElementById('topimu').getElementsByTagName('iframe')[0].contentWindow.document.documentElement;

        if (body != null) {
          bodyOffset = body.offsetHeight;
        }
        if (html != null) {
          htmlHeight = html.clientHeight;
          htmlOffset = html.offsetHeight;
        }
        if (debug) {
          console.log("in getTallestNoScroll");
          if (body != null && body.offsetHeight != null) { console.log("offsetHeight is " + body.offsetHeight); }
        if (body != null && body.scrollHeight != null) { console.log("scrollHeight is " + body.scrollHeight); }
        if (html != null && html.clientHeight != null) { console.log("htmlClientHeight is " + html.clientHeight); }
        if (html != null && html.scrollHeight != null) { console.log("htmlScrollHeight is " + html.scrollHeight); }
        if (html != null && html.offsetHeight != null) { console.log("htmlOffsetHeight is " + html.offsetHeight); }
        }
      }
      return Math.max(bodyOffset, htmlHeight, htmlOffset);
  }

  function getTallestWithScroll() {
      var bodyOffset = 0;
      var bodyScroll = 0;
      var htmlHeight = 0;
      var htmlOffset = 0;
      var htmlScroll = 0;

      if ($('#topimu div iframe').length) {
      var body = document.getElementById('topimu').getElementsByTagName('iframe')[0].contentWindow.document.body;
        var html = document.getElementById('topimu').getElementsByTagName('iframe')[0].contentWindow.document.documentElement;
        if (body != null) {
          bodyOffset = body.offsetHeight;
          bodyScroll = body.scrollHeight;
        }
        if (html != null) {
          htmlHeight = html.clientHeight;
          htmlOffset = html.offsetHeight;
          htmlScroll = html.scrollHeight;
        }
        if (debug) {
          if (body != null && body.offsetHeight != null) { console.log("offsetHeight is " + body.offsetHeight); }
          if (body != null && body.scrollHeight != null) { console.log("scrollHeight is " + body.scrollHeight); }
        if (html != null && html.clientHeight != null) { console.log("htmlClientHeight is " + html.clientHeight); }
        if (html != null && html.scrollHeight != null) { console.log("htmlScrollHeight is " + html.scrollHeight); }
        if (html != null && html.offsetHeight != null) { console.log("htmlOffsetHeight is " + html.offsetHeight); }
        }
      }
    return Math.max(bodyOffset, bodyScroll, htmlHeight, htmlOffset, htmlScroll);
  }
});

function executeDRR(imuHeight) {
    var debug = false;
    var topImuHeight = (imuHeight != null && imuHeight > 0) ? imuHeight : 250;
    var socialPages = ['facebook', 'twitter', 'reddit', 'tumblr'];

    // Set the calls to module content for right rail
    var articleDRRModuleList = ["most.popular.articles","module.resources","module.epo","module.answers.promo"];
    var resourceLibraryUrl = "";
    var moduleUrls = [];
    for (var i=0; i<articleDRRModuleList.length; i++) {
      var articleDRRModuleName = articleDRRModuleList[i];
      if (articleDRRModuleName == "module.epo") {
        articleDRRModuleName = getEpoParams();
      }
      if (articleDRRModuleName == "module.resources") {
        if (true) {
          continue;
        }
        articleDRRModuleName = "module.resources&pageSize=2&catId=3551";
        if (resourceLibraryUrl != null) {
          articleDRRModuleName += "&titleLink=" + resourceLibraryUrl;
        }
      }
      if (articleDRRModuleName == "module.category.articles") {
        if (true) {
          articleDRRModuleName = "module.category.articles&catId=3551";
        }
      }
      if (articleDRRModuleName.indexOf("module.resources")>=0) {
        moduleUrls.push("/napi/tile?def=" + articleDRRModuleName);
      }
      else if (articleDRRModuleName == "module.answers.promo") {
        moduleUrls.push("/napi/tile?def=module.answers.promo&catId=3551");
      }
      else {
        moduleUrls.push("/napi/tile?def=" + articleDRRModuleName + "&excludeCurrentArticle=true&excludeIds=3057179");
      }
    }

    var leftModuleUrl = "/napi/tile?def=insider.item";
    var leftModuleElementId = "drr-left";

    // Constants
    var interModuleHeight = 400;
    var grafHeight = 25;
    var moduleHeightBuffer = 350;
    var adHeightBuffer = 350;
    var mobileBreak = 768; // Width at which design becomes one column at top of article
    var leftPixelWindow = 700; // Need at least this much space to place left rail module near end of article
    var rightPixelWindow = 300;

    if (debug) {
      var topImu = $("#topimu").height(); // not using this one anymore
      console.log("old topImu height would have used this " + topImu);
    }

    var tagHeight = $(".tags").height();
    var cumulativeHeight = 0;
    var loopCounter = 0;
    var placementTarget = topImuHeight + moduleHeightBuffer + grafHeight + interModuleHeight;

    if ($(window).width() > mobileBreak) {
      // Desktop view
      if ($("figure.hero-img").height()) {
        cumulativeHeight += $("figure.hero-img").height();
      }
    }
    else {
      // Mobile View
      if ($("figure.hero-img").height()) {
        placementTarget += $("figure.hero-img").height();
      }
      // Add heights of all elements up through read these next (which is placed after eigth p tag)
      var firstModIndex = $("#drr-container > p:eq(7)").index();
      $("#drr-container").children().slice(0, firstModIndex).each(function() {
        placementTarget += $(this).height();
      });
      // Define first mobile ad here so imu counter shows imu1 first imu2 second, etc.
      var firstMobileAdHtml = getLazyLoadAdHtml();
    }

    var modules = [];
    var placementIndex = [];

    var moduleCounter = 0;
    var originalModuleUrlLength = moduleUrls.length;

    // Ordering should be [ad, module, module, ad, ad, ad, module, ad, ad, ad, module] per CSOB-445, IDGMPM-6788
    var adPositions = new Array(0,3,4,5,7,8,9);

    // Place Right-rail div containers
    $("#drr-container").children().each(function(index,value) {
      //ignore any hidden elements in the body, like the mobile-only "read this next" module
      if ($(this).is(':visible')) {
        if (debug) {
          console.log($(this));
        }
        if (cumulativeHeight >= placementTarget) {
          console.log("cumulatievHeight >= placementTarget and cumulativeHeight is " + cumulativeHeight + " and placementTarget is " + placementTarget);
          var placementDiff = 0;
          if ($.inArray(loopCounter, adPositions) != -1) {
            IDG.GPT.addExtIMU();
            if (true) {
              var adDivString = getLazyLoadAdHtml();
            } else {
              IDG.GPT.IMUCounter = IDG.GPT.IMUCounter + 1;
              var slotName = IDG.GPT.getIMUSlotName();
              IDG.GPT.defineGoogleTagSlot(slotName ,[[320,50],[300,250],[300,50]]);
              var adString = "<div id='" + slotName + "'></div><script>$('#" + slotName + "').responsiveAd({screenSize:'971 1115', scriptTags: []});if (Object.keys(IDG.GPT.companions).length > 0) {IDG.GPT.refreshAd('" + slotName + "');}<\/script>";
              var adDivString = "<div class='apart ad'>" + adString + "</div>";
            }

            placementDiff = applyInsert($(this), adDivString);
            if (debug) {
              console.log("Just placed an ad and the placementDiff is: " + placementDiff);
            }
            placementTarget = cumulativeHeight + placementDiff + interModuleHeight + adHeightBuffer;
          }
          else {
            var moduleDivString = "";
            var elementId = "drr-mod-"+moduleCounter;
            moduleDivString = "<div id=\"" + elementId + "\" />";
            modules.push(elementId);

            placementDiff = applyInsert($(this), moduleDivString);
            if (debug) {
              console.log("Just placed a module and the placementDiff is: " + placementDiff);
            }
            placementTarget = cumulativeHeight + placementDiff + interModuleHeight + moduleHeightBuffer;
            moduleCounter++;
          }
          loopCounter++;
        }
        // Avoid placing elements too soon due to non-large figures inflating the cumulative height
        if ($(this).is("figure") && !$(this).is("figure.large")) {
          cumulativeHeight += grafHeight;
        }
        else {
          cumulativeHeight += $(this).height() + grafHeight;
        }
      }
    });

    // clone Related Stories module to come in after eighth para in article body for mobile breakpoint display
    var $relatedStories = $('.related-promo-wrapper');
    if ($relatedStories.length) {
      var $relatedStoriesClone = $relatedStories.clone();
      $relatedStoriesClone.insertAfter( "#drr-container > p:eq(7)");
    }

    // For mobile only, place ad after second paragraph.
    if (firstMobileAdHtml) {
      $(firstMobileAdHtml).insertAfter("#drr-container > p:eq(1)");
    }

    var $insiderPromo = $('.insider-promo-wrapper');
    if ($insiderPromo.length) {
      var $insiderPromoClone = $insiderPromo.clone();
      $insiderPromoClone.insertAfter( "#drr-container > p:eq(1)");
    }

    //place left side element
    cumulativeHeight = 0;
    var leftPlacementTarget = tagHeight < 100 ? 100 : tagHeight;
    var leftPlacementLookaheadStart = null;
    var leftIntervalHeight = 650;
    var leftPlacementIndex = null;
    var $leftPlacementElement;

    // Only place left module if not a sponsored article and not on greenbot
    if (!true && true) {
      $("#drr-container").children().each(function(index,value) {
        if (debug) {
          console.log("leftRailProcessor iterate. index "+ index);
          console.log($(this));
        }
        //ignore any hidden elements in the body, like the mobile-only "read this next" module
        if($(this).is(':visible')) {
          if (cumulativeHeight >= leftPlacementTarget) {
            if (debug) {
              console.log("congratulations... we've passed the initial start point");
            }
            if (leftPlacementIndex == null) {
              //it's not good enough to not be a left avoid - it also shouldn't be a <p> with an immediately preceding small or medium image left avoid.
              if (!isLeftAvoid($(this)) && noPrevFigures($(this)) ) {
                leftPlacementIndex = $(this).index();
                $leftPlacementElement = $(this);
                leftPlacementLookaheadStart = cumulativeHeight;
                if (debug) {
                  console.log("is not a left avoid and no prev figures. ########## set placementIndex ("+leftPlacementIndex+") and lookaheadStart ("+leftPlacementLookaheadStart+") ##########");
                }
              } else {
                if (debug) {
                  console.log("is a left avoid or has previous figures. continue");
                }
              }
            } else {
              if (debug) {
                console.log("#### leftPlacementIndex already set to "+leftPlacementIndex+". looking ahead...");
              }
              //not null; has been set
              if ((cumulativeHeight - leftPlacementLookaheadStart) > leftIntervalHeight) {
                if (debug) {
                  console.log("###### THRESHOLD REACHED. LOOKAHEAD COMPLETE. END ###### (cumulativeHeight - leftPlacementLookaheadStart) ("+(cumulativeHeight-leftPlacementLookaheadStart)+") > leftIntervalHeight ("+leftIntervalHeight+").");
                }
                return false;
              } else {
                if (debug) {
                  console.log("threshold not reached: (cumulativeHeight - leftPlacementLookaheadStart) ("+(cumulativeHeight-leftPlacementLookaheadStart)+") < leftIntervalHeight ("+leftIntervalHeight+")");
                }
                if (isLeftAvoid($(this))) {
                  if (debug) {
                    console.log("This element is left avoid. #RESET, CONTINUE#");
                  }
                  leftPlacementIndex = null;
                  leftPlacementLookaheadStart = null;
                }
              }
            }
          }
          //we shouldn't be counting small or medium figures towards height since their heights are reflected in the following <p> tags
          if (!(isLeftAvoid($(this)) && ($(this).hasClass('small') || $(this).hasClass('inline-small') || $(this).hasClass('medium') || $(this).hasClass('inline-medium') || $(this).hasClass('apart') ))) {
            cumulativeHeight += $(this).height() + grafHeight;
          }
          if (debug) {
            console.log("-------------------- set cumulativeHeight("+cumulativeHeight+") ---------------");
            console.log("");
          }
        }
      });
    }

    if (leftPlacementIndex != null && elementNotNearEnd($leftPlacementElement, leftPixelWindow)) {
      if (debug) {
        console.log(" insert into index "+leftPlacementIndex);
      }
      $("#drr-container").children().eq(leftPlacementIndex).before("<div id=\""+leftModuleElementId+"\" />");
    }

    IDG.GPT.trackOmniture();

    // Add Right rail module content
    for (var i=0; i<modules.length; i++) {
      if (moduleUrls[i] !== undefined) {
        $.get( moduleUrls[i]+"&divId="+modules[i], function( data ) {
          var placementId = $(data).attr("data-placement-id");
           $( "#"+placementId ).html( data );
        });
      }
    }


    // Add Left rail module content, if not on a collection/package page
    if (leftPlacementIndex != null && elementNotNearEnd($leftPlacementElement, leftPixelWindow)) {
      $.get( leftModuleUrl+"&divId="+leftModuleElementId, function( data ) {
        var placementId = $(data).attr("data-placement-id");
        $( "#"+placementId ).html( data );
      });
    }




    //this needs to run once all of the modules are placed to make sure that any modules placed after page load are slated to be lazy-loaded
    $("div.lazyload_ad_article").lazyLoadAd({
          threshold    : 0,         // You can set threshold on how close to the edge ad should come before it is loaded. Default is 0 (when it is visible).
          forceLoad    : false,       // Ad is loaded even if not visible. Default is false.
          onLoad       : false,       // Callback function on call ad loading
          onComplete   : false,       // Callback function when load is loaded
          timeout      : 1500,        // Timeout ad load
          debug        : false,       // For debug use : draw colors border depends on load status
          xray         : false      // For debug use : display a complete page view with ad placements
    }) ;

    /*
    * Increments imu counter and generates a 'name' based on count like imu2, imu3, etc.
    * Returns the html and code script needed for the lazy load ad js.
    */
    function getLazyLoadAdHtml() {
      var adString = IDG.GPT.getLazyAdCode();
      return "<div class=\"apart ad lazyload_ad_article\">" + adString + "</div>";
    }

    function getEpoParams() {
      var parts = document.referrer.replace(/^https?:\/\//, '').split('/');
      var defaultCatId = 3029;
      var defaultTypeId = 2;
      var epoParams = "module.epo";

      parts.shift();

      // From HOMEPAGE; show default typeId articles
      if (parts.join('/') == "" && document.referrer.indexOf(document.domain)) {

        epoParams += "&displayId=11&referrer=home";


      }
      // From ARTICLE: Show articles w referrer catId
      else if (document.referrer != undefined && document.referrer.indexOf('article') >= 0) {
        var a = document.createElement('a');
        a.href = document.referrer;
        var uriParts = a.pathname.split('/');
        a = '';
        if (typeof uriParts[3] == 'undefined') {
          epoParams += "&typeId=" + defaultTypeId + "&referrer=home"; // default is 'home' behavior
        }
        else {
          var refCatSlug = uriParts[3];
          epoParams += "&catSlug=" + refCatSlug + "&referrer=article";
        }
      }
      // From SEARCH: Show article with catId same as current article
      else if (document.referrer.indexOf("google") >= 0 || document.referrer.indexOf("yahoo") >= 0 || document.referrer.indexOf("bing") >= 0) {
        var categories = [3551, 3631];
        if (categories instanceof Array && categories.length > 0) {
          var primaryCatId = categories[0];
          epoParams += "&catId=" + primaryCatId + "&referrer=search";
        }
        else {
          epoParams += "&typeId=" + defaultTypeId + "&referrer=home"; // default is 'home' behavior
        }
      }
      // Default is to show like coming from homepage
      else {

        epoParams += "&displayId=11&referrer=home";

         // default is 'home' behavior
      }
      return epoParams;
    }

    /**
     * @param jqo Original jquery object target
     * @param divString The div to be inserted.
     * @return Difference in height between original placement target and final target.
     * Checks first 6 elements for an allowable placement (600 pixel window).
     * If none, check nearby for elements that are not right avoids.
     * If none, place element before current target.
     */
    function applyInsert(jqo, divString) {
      if (debug) {
        console.log("applyInsert at top and jqo index is: " + jqo.index());
      }

      for (var i=0; i<=6; i++) {
        $thisElement = jqo.nextAll().andSelf().slice(i, i+1);
        if (debug) {
          console.log("Checking first six and i is: " + i + " and this element index is " + $thisElement.index() );
        }
        if ($thisElement.index() < 0) {
          break;
        }
        if (allowPlacement($thisElement)) {
          return addElement(jqo, $thisElement, divString);
        }
      }
      // If got here, no good place to put it.. just put it before one that is not right avoid!
      var $allowElement = null;
      if (($allowElement = findNearbyAllow(jqo)) != null) {
        return addElement(jqo, $allowElement, divString);
      }
      else {
        // nothing good so put in first spot that is not rightReject and not followed by reject.
        if (debug) {
          console.log("No nearby allows so just place in first spot that is not rightReject.");
        }
        var numElements = jqo.nextAll().length;
        var startIndex = jqo.index();
        for (var i=startIndex; i<=numElements; i++) {
          var $element = $("#drr-container").children().eq(i);
          // This element is eligible when not null, not in placement index, and not a rightReject nor is next element
          if ($element != null && (placementIndex == null || placementIndex.indexOf(i) == -1)) {
            if (!isRightReject($element) && !isRightReject($element.next())) {
              return addElement(jqo, $element, divString);
            }
          }
        }
        if (debug) {
          console.log("Not going to place element: return 0.");
        }
        return 0;
      }
    }

    /**
     * @param jqo Original jquery object
     * @param allowElement Element that is good placement for module/ad
     * @param divString The div to be inserted before the good element
     * @return placementHeightDiff Diff in height between original placement target and current target.
     *
     * If element is not too close to the end the insert the div before allowable element.
     * Add element index to placementIndex to keep track of which elements already have placements
     */
    function addElement(jqo, allowElement, divString) {
      var offset = allowElement.index() - jqo.index();
      if (debug) {
        console.log("addElement: jqo index is " + jqo.index() + " allowElement index is " + allowElement.index());
      }

      if (elementNotNearEnd(allowElement, rightPixelWindow)) {
        allowElement.before(divString);
        if (debug) {
          console.log("addElement: Adding " + allowElement.index() + " to placementIndex.");
        }
        placementIndex.push(allowElement.index());
        if (offset == 0) {
          return 0;
        }
        else {
          return getHeightDifference(jqo,allowElement);
        }

      }
      else {
        if (debug) {
          console.log("addElement: Near the end so do NOT add.");
        }
        return 0;
      }
    }

    function getHeightDifference(jqo,allowElement) {
      var offset = allowElement.index() - jqo.index();
      var height = 0;
      var children = null;
      if (offset > 0) {
        children = $("#drr-container").children().slice(jqo.index(), allowElement.index() );
      }
      else {
        children = $("#drr-container").children().slice(allowElement.index(), jqo.index());

      }
      if (children != null) {
        children.each(function(i) {
          if (debug) {
            console.log("About to add this element's height to heigh diff offset");
            console.log($(this));
          }
            height += $(this).height() + grafHeight;
        });
      }
      if (offset < 0) {
        height *= -1;
      }
      if (debug) {
        console.log("getHeightDifference: offset was " + offset + " and height diff is : " + height);
      }

      return height;
    }

    /**
     * Return true if next 550 pixels do not include a right avoid; false otherwise.
     */
    function allowPlacement(jqo) {
      $testElement = jqo;
      var height = 0;
      while (height < 550) {
        if ($testElement != null && !isRightAvoid($testElement)) {
          if (debug) {
            console.log("allowPlacement: this element height is " + $testElement.height() + " and index is: " + $testElement.index());
          }
          height += $testElement.height() + grafHeight;
        }
        else {
          return false;
        }
        $testElement = $testElement.next();
      }
      return true;
    }

    /**
     * Look ahead and back for element that is not rightAvoid
     * Return the index of the closest allowable element or null if none found.
     */
    function findNearbyAllow(jqo) {
      if (debug) {
        console.log("In nearby allow so could not find 600 px window.");
      }
      // Check current element first
      if (!isRightAvoid(jqo) && !isRightAvoid(jqo.next())) {
        return jqo;
      }
      for (var i=1; i<=2; i++) {
        if (!isRightAvoid(jqo.nextAll().slice(i-1, i)) && !isRightAvoid(jqo.nextAll().slice(i, i+1)) ) {
          return jqo.nextAll().slice(i-1, i);
        }
        if (!isRightAvoid(jqo.prevAll().slice(i-1, i)) && !isRightAvoid(jqo.prevAll().slice(i, i+1))  ) {
          return jqo.prevAll().slice(i, i+1);
        }
      }
      return null;
    }

    /**
    * Return true if this is avoid element or already has module/ad placed near it
    */
    function isRightAvoid(jqo) {
      if (placementIndex == null || placementIndex.indexOf(parseInt(jqo.index())) == -1) {
        if (jqo.is("pre") && jqo.height() > 300) {
          if (debug) {
            console.log("isRightAvoid: found pre. return true");
          }
          return true;
        }
        if (jqo.is("figure") && jqo.hasClass('large')) {
          if (debug) {
            console.log("isRightAvoid: found figure.large return true");
          }
          return true;
        }
        if (jqo.is("figure") && jqo.hasClass('medium') && jqo.hasClass('inline')) {
          if (debug) {
            console.log("isRightAvoid: found figure has class medium and inline.");
          }
          return true;
        }

        if (jqo.is('div') && jqo.hasClass('table-wrapper')) {
          if (debug) {
            console.log("isRightAvoid: found div with class table-wrapper");
          }
          return true;
        }
        if (jqo.is('aside')) {
          if (jqo.hasClass('sidebar') && !jqo.hasClass('medium')) {
            if (debug) {
              console.log("isRightAvoid: found aside with class sidebar, without class medium");
            }
            return true;
          }
          if (jqo.hasClass('statsTable')) {
            if (debug) {
              console.log("isRightAvoid: found aside with class statsTable");
            }
            return true;
          }
        }
        if (jqo.hasClass('download-asset')) {
          if (debug) {
            console.log("isRightAvoid: found class download-asset return true");
          }
          return true;
        }
        if (jqo.hasClass('tableLarge')) {
          if (debug) {
            console.log("isRightAvoid: found class tableLarge return true");
          }
          return true;
        }
        if (jqo.hasClass('reject')) {
          if (debug) {
            console.log("isRightAvoid: found class reject. return true");
          }
          return true;
        }
        if (jqo.is('table') && jqo.hasClass('scorecard')) {
          if (debug) {
            console.log("isRightAvoid: found div with class scorecard");
          }
          return true;
        }
        if (jqo.hasClass('product-list') || jqo.hasClass('fullwidth')) {
          if (debug) {
            console.log("isRightAvoid: found product list or fullwidth product sidebar");
          }
          return true;
        }
      }
      return false;
    }

    // Return true if element has class 'reject': will not place drr modules/ads next to these elements
    function isRightReject(jqo) {
      console.log("in isRightReject");
      if (jqo != null) {
        if (jqo.hasClass("reject")) {
          if (debug) {
            console.log("isRightReject: found 'reject' class");
          }
          return true;
        }
        return false;
      }
      return false;
    }

    // Returns true if height of all elements after this one is more than 500; false otherwise
    function elementNotNearEnd(element, pixelWindow) {
      if (pixelWindow == null) {
        pixelWindow = 500;
      }
      if (element == null) {
        return false;
      }
      var remainingHeight = 0;
      var children = $("#drr-container").children().slice(element.index());
      if (children == null) {
        return false;
      }
      children.each(function(i){
         remainingHeight += $(this).height();
      });
      if ( remainingHeight > pixelWindow) {
        return true;
      }
      else {
        if (debug) {
          console.log("Element too close to end. Remaining height is: " + remainingHeight + " and window is " + pixelWindow);
        }
        return false;
      }
    }

    /**
    * Return true if need to avoid this element when placing left module.
    */
    function isLeftAvoid(jqo) {
      if (jqo.is("figure")) {
        if (debug) {
          console.log("isLeftAvoid: found figure. return true");
        }
        return true;
      }
      if (jqo.is("aside.pullquote")) {
        if (debug) {
          console.log("isLeftAvoid: found pullquote. return true");
        }
        return true;
      }
      if (jqo.is("pre")) {
        if (debug) {
          console.log("isLeftAvoid: found pre. return true");
        }
        return true;
      }
      if (jqo.is("div.gist")) {
        if (debug) {
          console.log("isLeftAvoid: found github code block. return true");
        }
        return true;
      }

      if (jqo.is("aside") && jqo.hasClass("sidebar") && jqo.hasClass("medium")) {
        if (debug) {
          console.log("isLeftAvoid: found medium sidebar. return true");
        }
        return true;
      }

      if (jqo.hasClass("statsTable")) {
        if (debug) {
          console.log("isLeftAvoid: found class statsTable. return true");
        }
        return true;
      }

      if (jqo.hasClass("product-sidebar") && jqo.not(".fullwidth").length > 0) {
        if (debug) {
          console.log("isLeftAvoid: found class product-sidebar. return true");
        }
        return true;
      }
      return false;
    }

    /**
     * return true if there are no figures before the target placement that might bleed down into placement element
     */
    function noPrevFigures($originalTarget) {
      var targetIndex = $originalTarget.index();
      var numElementsLookBack = 5;
      var figureIndex = null;
      var figureHeight = null;
      var startIndex = targetIndex - numElementsLookBack < 0 ? 0 : targetIndex - numElementsLookBack;

      $("#drr-container").children().slice(startIndex, targetIndex).each(function(index, value) {
        if ($(this).is(':visible')) {
          if (($(this).is("figure") && !$(this).hasClass("large")) || $(this).hasClass("statsTable") || $(this).hasClass("product-sidebar")) {
            figureIndex = $(this).index();
            figureHeight = $(this).height();
            if (debug) {
              console.log("noPrevFigures: Found a figure and it's index is: " + figureIndex + " it's height is: " + figureHeight);
            }
          }
        }
      });
      if (figureIndex != null) {
        startIndex = figureIndex+1;
        var heightDiff = 0;
        $("#drr-container").children().slice(startIndex, targetIndex).each(function(index, value) {
          if ($(this).is(':visible')) {
            heightDiff += $(this).height();
          }
        });
        if ( heightDiff < figureHeight) {
          if (debug) {
            console.log("noPrevFigures: figureHeight is: " + figureHeight + " and heightDiff is " + heightDiff + " so return false.");
          }
          return false;
        }
      }
      return true;
    }

  }
</script>












<div id="drr-container" itemprop="articleBody">



























    <div class="lazyload_ad">
    <code type="text/javascript">
      <!--
      document.write('<div id="gpt-pin" class="">');
      IDG.GPT.addDisplayedAd("gpt-pin", "true");
      IDG.GPT.addLazyloadedAd("gpt-pin", "true");
      document.write('</div>');


          IDG.GPT.displayGoogleTagSlot('gpt-pin');



      if (Object.keys(IDG.GPT.companions).length > 0) {
        IDG.GPT.refreshAd('gpt-pin');
      }
      //-->
    </code>
    </div>





  <p>When I think of healthcare, I think of many different things: getting better from a cold or flu, receiving reassurance that I have no serious ailments, or possibly getting relief from allergies. I also think about cost, as a family of four will have numerous uncovered expenses. What I don’t want to know or fear is that my personal information (or that of my family) is not protected to the best extent possible.</p><p>However, what I want is not what I always get. My health information was or could have been comprised three times since the records of my providers were breached. Of course, there were the assurances that every step possible was taken to remediate the situation and so far, there has been no noticeable misuse of my data. However, the operative part of that sentence is “so far.”  It is possible that false claims, unauthorized credit, false tax returns, or unauthorized purchases under my name will occur. Then the real fun begins!</p><p>That is the personal perspective. Professionally, when I think of healthcare data, I think of a byzantine landscape of regulations for data handling. I also note that three of the top five data breaches of 2015 involved healthcare organizations, as reported by <a href="http://bit.ly/1QBahfI" target="new" rel="nofollow">CRN</a>. Drawing from <a href="http://now.informatica.com/en_ponemon-data-breaches-and-sensitive-data-risk_executive-brief_3076.html?cint=blog_idg_16q1_DS-RS-HealthSecurity_na_us_en&amp;Source=Social-Blog" target="new" rel="nofollow">research</a> conducted with Informatica, Scale Venture Partners, and Ponemon Institute, it’s clear that healthcare organizations (as well as other industries) struggle with the fundamental issues of data security. </p><p>These issues of data security are about intelligence on sensitive and private data: where is it; who is using/accessing it; where is it proliferating (i.e., where is it created and where is it being shared); and what is its value, its location, and other factors that create a profile of “sensitive data risk.” It is not that healthcare organizations have no idea at all about their sensitive data, but the details that are fundamental to security and privacy are lacking.</p><p>How did this happen? That is, how have organizations lost track of their most precious assets: sensitive and private data? One byte at a time is the answer, but the bytes are multiplying like a virus. Driven by new self-service applications, mobile users, cloud, analytics, and regulations, healthcare organizations have experienced an explosion of data growth and propagation.</p><p>Unfortunately, security practices and technologies have not kept up; you can see evidence of this at the yearly <a href="http://www.rsaconference.com/events/us16" target="new" rel="nofollow">RSA</a> Conference, where the latest innovations and trends for information security take center stage. The solutions and sessions have been updated and progressed from previous years, but the focus remains the same: keep the bad guys out and detect if the bad guys get in. Given the dismal track record of data breaches last year, the bad guys are getting in, undetected. Thank goodness the network protections are in place because we would have unmitigated chaos if they weren’t slowing the hordes of attackers.</p><p>Clearly something more is needed, and a data perimeter driven by actionable intelligence is the answer. Arm those with responsibility for data protection, meeting industry <a href="http://www.hhs.gov/hipaa/" target="new" rel="nofollow">guidelines</a> and <a href="https://iapp.org/" target="new" rel="nofollow">privacy</a> regulations, with the knowledge to understand what they are trying to protect and secure.</p><p>This is the role of <a href="https://www.informatica.com/products/data-security/secure-at-source.html?cint=blog_idg_16q1_DS-RS-HealthSecurity_na_us_en" target="new" rel="nofollow">data security intelligence</a>, which provides executives with strategic abstracts of an organization’s sensitive data landscape and actionable drill downs for practitioners. With data security intelligence, healthcare organizations (and other industries) can create the additional “data perimeter” that is necessary in this era of data proliferation, growth, and persistent attacks. Data and security teams can join forces to ensure that the sprawling data so necessary for competitive advantage is safeguarded in a manner that meets corporate, government and, most importantly from my perspective, consumer needs.</p>















</div>




    <div class="apart-alt tags">
    <span class="related">RELATED TOPICS</span>
    <ul>

      <li><a href="/category/data-analytics">Data Analytics</a></li>

      <li><a href="/category/healthcare-it">Healthcare IT</a></li>











    </ul>

  </div>



















































  <div class="prev-next">

























































































































<a  class="blog-nav prev" href="/article/3054568/enterprise-architecture/you-snooze-you-lose-the-road-to-business-value-is-paved-with-data.html">
  <span class="title"><i class="ss-icon ss-navigateleft"></i> Previous Post</span>
  <p>You Snooze You Lose: The Road to Business Value is Paved with Data</p>
</a>

































































































































<a  class="blog-nav next" href="/article/3058707/data-security/three-considerations-for-the-new-security-landscape.html">
  <span class="title">Next Post <i class="ss-icon ss-navigateright"></i></span>
  <p>Three Considerations for the New Security Landscape</p>
</a>







  </div>

















<div class="byline vcard author end-byline">





















  <div class="author-info">

    <p class="author-name">
      <a href="/author/Robert-Shields/">Robert Shields</a> &#8212; <span class="author-title">Product Marketing, Informatica's data security, data privacy and test data management solutions</span>

    </p>

    <ul class="social-links author">
      <li>
        <a href="/author/Robert-Shields/">
          <i class="ss-icon ss-user"></i>
        </a>
      </li>


        <li>
          <a href="mailto:<EMAIL>">
            <i class="ss-icon ss-mail"></i>
          </a>
        </li>



        <li>
          <a href="https://twitter.com/https://twitter.com/informaticacorp" class="twitter-follow" target="_blank">
            <i class="ss-icon ss-social ss-twitter"></i>
          </a>
        </li>



        <li>
          <a href="https://www.facebook.com/InformaticaCorporation" class="facebook-follow" target="_blank">
            <i class="ss-icon ss-social ss-facebook"></i>
          </a>
        </li>



        <li>
          <a href="https://www.linkedin.com/company/informatica" class="linkedin-follow" target="_blank">
            <i class="ss-icon ss-social ss-linkedin"></i>
          </a>
        </li>




      <li>
        <a href="/author/Robert-Shields/index.rss" class="rss">
          <i class="ss-icon ss-rss"></i>
        </a>
      </li>
    </ul>

  </div><!-- end .author-info -->

</div>

























<section class="pagination">



</section>
<script type="text/javascript">
$(document).ready(function(){
  $('.pagination .page-link').click(function(e){
    oClickTrack("Article Detail:Pagination");
  });
});
</script>








































    <!-- blx4 #1143 blox4.html  -->



































  <div class="article-intercept">
   <a href="http://www.computerworld.com/article/3071153/mobile-wireless/call-on-line-2-six-ways-to-add-a-second-line-to-your-smartphone.html">
   <em>Call on line 2! Six ways to add a second line to your smartphone</em></a>


  </div>











    <a href="#comments" class="btn comments-cta"><i class="ss-icon ss-chat"></i>View <span id="comment-cta-txt">Comments</span></a>






<script src="/www/js/video/embedder.js?v=20160525110859"></script>


























        </section><!-- /.bodee -->





























<div id="content-recommender" class="nolinks norewrite">
  <div class="head">You Might Like</div>
  <div id="rcjsload_46744d"></div>
</div>

<script type="text/javascript">
(function() {
function ad_block_test(e,o){if("undefined"!=typeof document.body){var t="0.1.2-dev",o=o?o:"sponsorText",n=document.createElement("DIV");n.id=o,n.style.position="absolute",n.style.left="-999px",n.appendChild(document.createTextNode("&nbsp;")),document.body.appendChild(n),setTimeout(function(){if(n){var o=0==n.clientHeight;try{}catch(d){console&&console.log&&console.log("ad-block-test error",d)}e(o,t),document.body.removeChild(n)}},175)}}
ad_block_test(function(is_blocked){
var widget_id = 30204;
if (is_blocked === true) {
widget_id = 30282;
}
var referer="";try{if(referer=document.referrer,"undefined"==typeof referer)throw"undefined"}catch(exception){referer=document.location.href,(""==referer||"undefined"==typeof referer)&&(referer=document.URL)}referer=referer.substr(0,700);
var rcel = document.createElement("script");
rcel.id = 'rc_' + Math.floor(Math.random() * 1000);
rcel.type = 'text/javascript';
rcel.src = "http://trends.revcontent.com/serve.js.php?w="+widget_id+"&t="+rcel.id+"&c="+(new Date()).getTime()+"&width="+(window.outerWidth || document.documentElement.clientWidth) +"&referer="+referer + '&is_blocked=' + is_blocked;
rcel.async = true;
var rcds = document.getElementById("rcjsload_46744d"); rcds.appendChild(rcel);
});
})();
</script>



































<script src='http://cdn.gigya.com/JS/socialize.js?apiKey=3_TYfXTakOryyA7satF-DrNB1wPrA_8UWylhhyVwBxS4xSvy0UagSyxzmn7uP0E3we' type='text/javascript'></script>


<script>
  function disableCommentBox() {
    var nodeList = document.querySelectorAll("textarea.gig-composebox-textarea");
    for (var i = 0, length = nodeList.length; i < length; i++) {
      nodeList[i].innerHTML = "Please sign in before writing your comment";
    }
    var sbutton = document.getElementById("submitButton");
    sbutton.style="display: inherit;";

  }

  function disableGlogin() {
    var nodeList = document.querySelectorAll(".gig-composebox-login");
    for (var i = 0, length = nodeList.length; i < length; i++){
      nodeList[i].style="display: none;";
    }
  }

</script>

<section id="comments" class="bodee">


  <div class="comments-hed clearfix">
    <div class="head">Join the discussion</div>
    <div class="subhead"><span class="firstToComment"><em>Be the first to comment on this article.</em> </span><a href="/about/comment-policy.html">Our Commenting Policies</a></div>
  </div>
  <div class="comments-body">
    <div id='commentsDiv'></div>
  </div>





































    <div class="lazyload_ad">
      <code type="text/javascript">
      <!--
        IDG.GPT.IMUCounter = IDG.GPT.IMUCounter + 1;
        document.write('<div id="' + IDG.GPT.getIMUSlotName() + '">');
        IDG.GPT.defineGoogleTagSlot(IDG.GPT.getIMUSlotName(), [300,250], false);
        document.write('</div>');
        $('#' + IDG.GPT.getIMUSlotName()).responsiveAd({screenSize:'971 1115', scriptTags: []});
        if (Object.keys(IDG.GPT.companions).length > 0) {
          IDG.GPT.refreshAd(IDG.GPT.getIMUSlotName());
        }
      //-->
      </code>
    </div>









</section>

<script type='text/javascript'>

function onSiteLoginHandler(event) {
    // Site Login button clicked
    NarfUser.showLogin();
}

function GigyaLogouthandle(eventObj) {
  logout();
}

function commentsLoadedHandler(e) {
  gigya.comments.getComments({
    categoryID: 'cw',
    streamID: '3057179',
    callback: function(response) {
      if (typeof response.commentCount !== 'undefined' && response.commentCount > 0) {
            $('.firstToComment').hide();
      }
    }
  });
}

function commentSubmittedHandler(eventObj) {
  var aid = eventObj.streamID;
  var brand = "ctw";
  $thm.logPlEvent({"b":brand,"e":"comment","t":"article","id":aid});
}

function commentVotedHandler(e) {
  var voteType = e.vote;
  if(voteType == 'pos') $thm.logPlEvent({"b":"ctw","e":"upcomment","t":"article","id":"3057179"});
  else if(voteType == 'neg') $thm.logPlEvent({"b":"ctw","e":"downcomment","t":"article","id":"3057179"});
}

function gigyaErrorHandler(e) {
  console.log(">> Gigya initialization failed <<");
  console.log("errorCode:    " + e.errorCode);
  console.log("errorMessage: " + e.errorMessage);
  console.log("errorDetails: " + e.errorDetails);
  console.log("category: cw");
  console.log("gigyaKey: 3_TYfXTakOryyA7satF-DrNB1wPrA_8UWylhhyVwBxS4xSvy0UagSyxzmn7uP0E3we");
  console.log(">> -- <<");
}
  gigya.socialize.addEventHandlers({
    onLogout:GigyaLogouthandle
  });

  var params = {
    categoryID: 'cw',
    streamID: '3057179',
    containerID: 'commentsDiv',
    useSiteLogin: true,
    onSiteLoginClicked: onSiteLoginHandler,
    cid:'',
    width: '100%',
    enabledShareProviders: 'facebook,twitter,yahoo,linkedin',
    showLoginBar: true,
    version:2,
    onLoad:commentsLoadedHandler,
    onCommentSubmitted:commentSubmittedHandler,
    onCommentVoted:commentVotedHandler,
    onError: gigyaErrorHandler
  }

 gigya.comments.showCommentsUI(params);

</script>
























<section id="funnel">
    <section class="popular-brand-cols">













































































<section class="popular-col">































































































































































    <div class="promo with-eyebrow with-image">
        <div class="eyebrow">What Readers Like</div>

          <div class="sized-img"><a  href="/article/3060005/mobile-wireless/scientists-can-now-make-lithium-ion-batteries-last-a-lifetime.html"><img  class="lazy carousel.idgeImage  imgId100657265 " data-original="http://core5.staticworld.net/images/article/2016/04/nanowires_160420_01_sz-100657265-carousel.idge.jpg" alt="nanowires lithium-ion batteries" itemprop="image" /></a></div>





      <div class="hed featured"><a  href="/article/3060005/mobile-wireless/scientists-can-now-make-lithium-ion-batteries-last-a-lifetime.html">Scientists can now make lithium-ion batteries last a lifetime</a></div>

        <p>Researchers at the University of California have discovered a way to use nanowires to allow lithium-ion...</p>

















































































    <ul>
      <li class="clearfix">

            <a  href="/article/3039353/it-careers/sen-durbin-calls-abbott-labs-it-layoffs-harsh-and-insensitive.html"><img  class="lazy carousel.idgeImage  imgId100630759 " data-original="http://core5.staticworld.net/images/article/2015/11/hp-48ss-layoffs-100630759-carousel.idge.jpg" alt="10 Signs Layoffs Are Coming" itemprop="image" /></a>





          <div class="hed"><a  href="/article/3039353/it-careers/sen-durbin-calls-abbott-labs-it-layoffs-harsh-and-insensitive.html">Sen. Durbin calls Abbott Labs&#039; IT layoffs &#039;harsh and insensitive’</a></div>
      </li>
















































































      <li class="clearfix">

            <a  href="/article/3051113/cloud-computing/google-april-fools-day-gmail-itbwcw.html"><img  class="lazy carousel.idgeImage  imgId100654012 " data-original="http://core0.staticworld.net/images/article/2016/04/google-april-fool-gmail-100654012-carousel.idge.jpg" alt="Google April Fool's Day Gmail mic drop" itemprop="image" /></a>





          <div class="hed"><a  href="/article/3051113/cloud-computing/google-april-fools-day-gmail-itbwcw.html">Google&#039;s asinine Gmail prank: What were these fools thinking?</a></div>
      </li>
    </ul>
    </div><!-- /.promo -->
    <div class="promo newsletter with-eyebrow">






























    <!-- blx4 #925 blox4.html  -->









































      <div class="eyebrow">Newsletters</div>
<div class="hed">Sign up and receive the latest news, reviews and trends on your favorite technology topics.</div>
<p>Get our daily newsletter</p>
<form action="/newsletters/signup.html"> <input type="email" name="email" placeholder="Enter your email address"> <button class="btn" type="submit">Go</button> </form>








    </div><!--  ./promo newsletter -->
















































































    <div class="promo with-image">

          <div class="sized-img"><a  href="/article/3046632/apple-ios/apple-delivers-a-significant-update-with-ios-9-3.html"><img  class="lazy mediumImage  imgId100638331 " data-original="http://core4.staticworld.net/images/article/2016/01/apple-night-shift-ios-stock-100638331-medium.jpg" alt="apple night shift ios stock" itemprop="image" /></a></div>





      <div class="hed"><a  href="/article/3046632/apple-ios/apple-delivers-a-significant-update-with-ios-9-3.html">Apple delivers a &#039;significant&#039; update with iOS 9.3</a></div>

         <p>After releasing seven developer betas and several public betas, Apple on Monday released a noteworthy...</p>

    </div>















































































    <div class="promo with-image">

          <div class="sized-img"><a  href="/article/3064712/h1b/us-uncovers-20m-h-1b-fraud-scheme.html"><img  class="lazy carousel.idgeImage  imgId100263598 " data-original="http://core4.staticworld.net/images/article/2014/04/american-justice-courtroom-gavel-legal-system-law-justice-flag-000000804982-100263598-carousel.idge.jpg" alt="american justice courtroom gavel legal system law justice flag" itemprop="image" /></a></div>





      <div class="hed"><a  href="/article/3064712/h1b/us-uncovers-20m-h-1b-fraud-scheme.html">U.S. uncovers $20M H-1B fraud scheme</a></div>

         <p>A Virginia couple and four other people have been indicted for running an H-1B visa-for-sale scheme the...</p>

    </div>
















</section>


        <section class="brand-col">































































































































  <div class="promo brandposts with-eyebrow" >
  <div class="eyebrow-wrapper"><div class="eyebrow">BrandPosts</div><a href="#" class="learn-more">Learn more</a></div>
  <ul class="sponsored">




























<li class="clearfix  with-image" >

  <div class="wrap">
    <a  href="/article/3063582/data-analytics/putting-big-data-to-work-for-marketing-finally-we-can-connect-all-the-dots.html"><img  class="lazy carousel.idgeImage  imgId100658732 " data-original="http://core5.staticworld.net/images/article/2016/04/blog-35_apr29_image-1-100658732-carousel.idge.jpg" alt="blog 35 apr29 image 1" itemprop="image" /></a>
  </div>


















<div class="blog-branding-text"><span class="sponsored-by">Sponsored by</span> Informatica</div>

  <div class="title"><a  href="/article/3063582/data-analytics/putting-big-data-to-work-for-marketing-finally-we-can-connect-all-the-dots.html">Putting Big Data to Work for Marketing – Finally We Can Connect All the Dots</a></div>
</li>




  </ul>
  </div>





























<div class="ad">



    <div class="lazyload_ad">
      <code type="text/javascript">
      <!--
        IDG.GPT.IMUCounter = IDG.GPT.IMUCounter + 1;
        document.write('<div id="' + IDG.GPT.getIMUSlotName() + '">');
        IDG.GPT.defineGoogleTagSlot(IDG.GPT.getIMUSlotName(), IDG.GPT.slots["topimu"], false);
        document.write('</div>');
        $('#' + IDG.GPT.getIMUSlotName()).responsiveAd({screenSize:'971 1115', scriptTags: []});
        if (Object.keys(IDG.GPT.companions).length > 0) {
          IDG.GPT.refreshAd(IDG.GPT.getIMUSlotName());
        }
      //-->
      </code>
    </div>






</div>







        </section>
    </section>

    <section class="featured-col">
































    <!-- blx4 #927 blox4.link_list  -->















































<div class="promo with-eyebrow with-image">
  <div class="eyebrow">Top Stories</div>

    <div class="sized-img"><a  href="/article/3075347/government-it/state-dept-it-staff-told-to-keep-quiet-about-clinton-s-server.html"><img  class="lazy medium.idgeImage  imgId100587315 " data-original="http://core5.staticworld.net/images/article/2015/05/hillary_clinton-100587315-medium.idge.jpg" alt="hillary clinton" itemprop="image" /></a></div>


  <div class="hed featured"><a  href="/article/3075347/government-it/state-dept-it-staff-told-to-keep-quiet-about-clinton-s-server.html">State Dept. IT staff told to keep quiet about Clinton’s server</a></div>
  <p>Two IT staff members who raised concerns about Hillary Clinton’s use of a private email server were...</p>
</div>











































































































































































  <div class="promo with-image">

    <div class="sized-img">
        <a  href="/article/3074250/it-careers/sen-sanders-attacks-h-1b-visa-use-at-disney.html"><img  class="lazy carousel.idgeImage  imgId100662900 " data-original="http://core1.staticworld.net/images/article/2016/05/rtsfrb6-100662900-carousel.idge.jpg" alt="rtsfrb6" itemprop="image" /></a>
      </div>



    <div class="hed">
      <a  href="/article/3074250/it-careers/sen-sanders-attacks-h-1b-visa-use-at-disney.html">Sen. Sanders attacks H-1B visa use at Disney</a>
    </div>
    <p>Vermont Sen. Bernie Sanders lashed out at the H-1B visa program Tuesday while campaigning in Anaheim,...</p>
  </div>






















































  <div class="promo with-image">

    <div class="sized-img">
        <a  href="/article/3075346/it-management/it-s-new-imperative-partnering-to-shape-the-future.html"><img  class="lazy carousel.idgeImage  imgId100613846 " data-original="http://core4.staticworld.net/images/article/2015/09/convergence-teamwork-synergy-collaboration-100613846-carousel.idge.jpg" alt="convergence teamwork synergy collaboration" itemprop="image" /></a>
      </div>


    <span class="insider"></span>
    <div class="hed">
      <a  href="/article/3075346/it-management/it-s-new-imperative-partnering-to-shape-the-future.html">IT’s new imperative: Partnering to shape the future</a>
    </div>
    <p>McKinsey's new Global Survey explains what partnering means, really, and some ways to go about it given...</p>
  </div>






















































  <div class="promo with-image">

    <div class="sized-img">
        <a  href="/article/3075298/mobile-wireless/free-mobile-data-is-good-for-consumers.html"><img  class="lazy carousel.idgeImage  imgId100662933 " data-original="http://core2.staticworld.net/images/article/2016/05/cellphone-100662933-carousel.idge.jpg" alt="cellphone" itemprop="image" /></a>
      </div>



    <div class="hed">
      <a  href="/article/3075298/mobile-wireless/free-mobile-data-is-good-for-consumers.html">Opinion: Free mobile data is good for consumers</a>
    </div>
    <p>Those opposed are overthinking zero rating.     </p>
  </div>








    </section>
</section>




























    <div class="lazyload_ad">
    <code type="text/javascript">
      <!--
      document.write('<div id="ciu" class="">');
      IDG.GPT.addDisplayedAd("ciu", "true");
      IDG.GPT.addLazyloadedAd("ciu", "true");
      document.write('</div>');



          $('#ciu').responsiveAd({screenSize:'971 1115', scriptTags: []});


      if (Object.keys(IDG.GPT.companions).length > 0) {
        IDG.GPT.refreshAd('ciu');
      }
      //-->
    </code>
    </div>









    <section id="resources-sponsored-links">


  <section class="sponsored-links">
    <div class="head">Sponsored Links</div>









































































  <ul class="first">































































<li>
  <a rel="nofollow" target="_blank" href="http://pubads.g.doubleclick.net/gampad/clk?id=520300616&iu=/8456/IDG.US_E_ComputerWorld.com" onclick="moduleTrack('Sponsored Links','resources-sponsored-links component')">
    Online Master of Science in Information Systems at Northwestern University
  </a>
</li>




  </ul>


  </section>
</section>




        </article>

      </section><!-- /role=main -->

    </div><!-- /#page-wrapper -->













<footer>
    <section class="brand" itemscope itemtype="http://schema.org/Organization">

      <link itemprop="url" href="http://www.computerworld.com">
        <a href="/"><span class="logo">Computerworld</span></a>
    <span class="tagline"> The Voice of Business Technology</span>

        <span class="follow">
      <label>Follow us</label>
              <ul>















  <li><a href="https://twitter.com/computerworld" itemprop="sameAs" rel="nofollow" target="_blank" onclick="brandFollowTrack('Twitter')"><i class="ss-icon ss-social-circle ss-twitter"></i></a></li>



  <li><a href="https://www.linkedin.com/company/computerworld" itemprop="sameAs" rel="nofollow" target="_blank" onclick="brandFollowTrack('LinkedIn')"><i class="ss-icon ss-social-circle brand ss-linkedin"></i></a></li>



  <li><a href="https://www.facebook.com/Computerworld" itemprop="sameAs" rel="nofollow" target="_blank" onclick="brandFollowTrack('Facebook')"><i class="ss-icon ss-social-circle brand ss-facebook"></i></a></li>



  <li><a href="https://plus.google.com/+computerworld/posts" itemprop="sameAs" rel="publisher" target="_blank" onclick="brandFollowTrack('Google+')"><i class="ss-icon ss-social-circle brand ss-googleplus"></i></a></li>







  <li><a href="/about/rss/" target="_blank"><i class="ss-icon ss-social-circle ss-rss"></i></a></li>



              </ul>
        </span>

    </section>

    <section class="topics">



            <nav id="ft1">
                <ul>























        <li><a href="/category/cloud-computing/">Cloud Computing</a>

        </li>

        <li><a href="/category/computer-hardware/">Computer Hardware</a>

        </li>

        <li><a href="/category/consumerization-of-it/">Consumerization of IT</a>

        </li>

        <li><a href="/category/data-center/">Data Center</a>

        </li>

        <li><a href="/category/emerging-technology/">Emerging Technology</a>

        </li>

        <li class="selected"><a href="/category/enterprise-applications/" class="active">Enterprise Applications</a>

        </li>

        <li><a href="/category/it-management/">IT Management</a>

        </li>

        <li><a href="/category/internet/">Internet</a>

        </li>

        <li><a href="/category/mobile-wireless/">Mobile &amp; Wireless</a>

        </li>

        <li><a href="/category/networking/">Networking</a>

        </li>

        <li><a href="/category/operating-systems/">Operating Systems</a>

        </li>

        <li><a href="/category/security/">Security</a>

        </li>

        <li><a href="/category/vertical-it/">Vertical IT</a>

        </li>






                </ul>
            </nav>




        <nav id="ft2">
            <ul>












<li><a href="/about/sitemap.html">All Topics</a></li>
<li><a href="/news">News</a></li>
<li><a href="/feature">Features</a></li>
<li><a href="/reviews">Reviews</a></li>
<li><a href="/blogs">Blogs</a></li>
<li><a href="/opinion">Opinions</a></li>
<li><a href="/insider">Insider</a></li>
<li><a href="/blog/shark-tank">Shark Tank</a></li>
<li><a href="/slideshows">Slideshows</a></li>
<li><a href="/video">Video</a></li>
<li><a href="/newsletters/signup.html">Newsletters</a></li>
<li><a target="_blank" rel="nofollow" href="http://www.ereg.me/IDGEvents">Computerworld Events</a></li>
<li><a href="/resources">Resources/White Papers</a></li>

            </ul>
        </nav>




    </section>


    <section class="about">
    <div class="wrapper">
          <nav class="tertiary" id="ft3">
              <ul>

















<li><a href="/about/about.html"  >About Us</a>

<li><a href="/about/contactus.html"  >Contact</a>

<li><a href="/about/privacy.html"  >Privacy Policies</a>

<li><a href="http://www.computerworld.com/article/2879003/download-computerworlds-editorial-calendar" target="_blank" rel="nofollow" >Editorial Calendar</a>

<li><a href="http://www.idgenterprise.com/reach/computerworld/" target="_blank" rel="nofollow" >Advertising</a>

<li><a href="http://careers.idg.com/" target="_blank" rel="nofollow" >Careers at IDG</a>

<li><a href="/about/sitemap.html"  >Site Map</a>

<li><a href="/about/adchoices.html"  >Ad Choices</a>




              </ul>
          </nav>
    </div>
  </section>

    <section class="copyright">
        <div class="wrapper">

          <p><a href="/about/copyright.html">Copyright</a> &copy; 1994 - 2016 Computerworld, Inc. All rights reserved.</p>
      <div class="network">
        <div id="network-selector">
                <div class="label">Explore the IDG Network <i class="ss-icon tick">descend</i></div>
                <ul>



















    <li><a href="http://www.cio.com" target="_blank" rel="nofollow">CIO</a></li>

    <li><a href="http://www.computerworld.com" target="_blank" rel="nofollow">Computerworld</a></li>

    <li><a href="http://www.csoonline.com" target="_blank" rel="nofollow">CSO</a></li>

    <li><a href="http://www.greenbot.com" target="_blank" rel="nofollow">Greenbot</a></li>

    <li><a href="http://www.idc.com" target="_blank" rel="nofollow">IDC</a></li>

    <li><a href="http://www.idg.com" target="_blank" rel="nofollow">IDG</a></li>

    <li><a href="http://www.idganswers.com" target="_blank" rel="nofollow">IDG Answers</a></li>

    <li><a href="http://www.idgconnect.com" target="_blank" rel="nofollow">IDG Connect</a></li>

    <li><a href="http://www.idgknowledgehub.com" target="_blank" rel="nofollow">IDG Knowledge Hub</a></li>

    <li><a href="http://www.idgtechnetwork.com" target="_blank" rel="nofollow">IDG TechNetwork</a></li>

    <li><a href="http://www.idg.tv" target="_blank" rel="nofollow">IDG.TV</a></li>

    <li><a href="http://www.idgventures.com" target="_blank" rel="nofollow">IDG Ventures</a></li>

    <li><a href="http://www.infoworld.com" target="_blank" rel="nofollow">Infoworld</a></li>

    <li><a href="http://www.itnews.com" target="_blank" rel="nofollow">IT News</a></li>

    <li><a href="http://www.itwhitepapers.com" target="_blank" rel="nofollow">ITwhitepapers</a></li>

    <li><a href="http://www.itworld.com" target="_blank" rel="nofollow">ITworld</a></li>

    <li><a href="http://www.javaworld.com" target="_blank" rel="nofollow">JavaWorld</a></li>

    <li><a href="http://www.linuxworld.com" target="_blank" rel="nofollow">LinuxWorld</a></li>

    <li><a href="http://www.macworld.com" target="_blank" rel="nofollow">Macworld</a></li>

    <li><a href="http://www.networkworld.com" target="_blank" rel="nofollow">Network World</a></li>

    <li><a href="http://www.pcworld.com" target="_blank" rel="nofollow">PC World</a></li>

    <li><a href="http://www.techhive.com" target="_blank" rel="nofollow">TechHive</a></li>


                </ul>
        </div><!-- /#network-selector -->
      </div><!-- /.network -->
    </div><!-- /.wrapper -->
    </section>
</footer>

<script src="/www/js/jquery/jquery-ui.js?v=20160525110859"></script>
<script src="/www/js/jquery/jquery.dfp.min.js?v=20160525110859"></script>

<script src="/www.idge/js/FTScroller_plugins.js?v=20160525110859"></script>
<script src="/www.idge/js/mule/shortstack_nav.js?v=20160525110859"></script>
<script src="/www.idge/js/jquery/jt-scrollable.min.js?v=20160525110859"></script>
<script src="/www/js/jquery/jquery.timeago.js?v=20160525110859"></script>
<script>
$(document).ready(function(){
  $(".timeago").timeago();
});
</script>






























        <!-- Begin welcome ad overlay - gpt-overlay position  -->
        <div id="superadunit" class="hidden">
          <div class="ad">
            <a href="javascript:unhide('superadunit');" id="superstitial-text">This ad will close in 20 seconds. Continue to site &raquo;</a>
            <div id="gpt-overlay" class="">
              <script type="text/javascript">
                IDG.GPT.addDisplayedAd("gpt-overlay", "true");
                IDG.GPT.displayGoogleTagSlot('gpt-overlay');
              </script>
            </div>
          </div>
        </div>
        <!--  End welcome ad overlay - gpt-overlay position -->






































        <div id="catfish" class="">
        </div>
        <script type="text/javascript">
          IDG.GPT.addDisplayedAd("catfish", "true");
          $('#catfish').responsiveAd({screenSize:'971 1115', scriptTags: []});
        </script>































        <!--  Begin gpt-skin/gpt-pin/inread -->
        <div id="gpt-skin" class="">
        </div>
        <script type="text/javascript">
          IDG.GPT.addDisplayedAd("gpt-skin", "true");
          IDG.GPT.displayGoogleTagSlot('gpt-skin');
        </script>
        <!--  End gpt-skin/gpt-pin/inread -->









































        <div id="mobilewelcomead" class="">
        </div>
        <script type="text/javascript">
          IDG.GPT.addDisplayedAd("mobilewelcomead", "true");
          $('#mobilewelcomead').responsiveAd({screenSize:'971 1115', scriptTags: []});
        </script>







<script src="/www/js/analytics/tracking.js?v=20160525110859"></script>
<script src="/www/js/autocomplete.js?v=20160525110859"></script>

<script type="text/javascript">
$(document).ready(function(){
  var listItems = $('#scrollable ul li');
  var listWidth = 0;
  for (var i=0;i < listItems.length; i++) {
    listWidth = listWidth + $(listItems[i]).outerWidth();
  }

  $('#scrollable ul').width(listWidth+100); //adding an extra 100px of buffer


  //we need to disable this for IE10 for now - link clicks don't work!
  var trackPointerEvents = window.navigator.msPointerEnabled;

  if (!trackPointerEvents) {
    //set up the FT Scroller for the topics
    var scroller = new FTScroller(document.getElementById('scrollable'), {
      scrollingY: false,
      bouncing: false,
      updateOnWindowResize: true,
      scrollbars: false
    });
  }

});
</script>


  <script src="/www.idge/js/social_sidecar.js?v=20160525110859"></script>
  <script src="/www/js/carousel-extend.js?v=20160525110859"></script>
  <script src="/www.idge/js/jquery/plugins/jquery.colorbox-min.js?v=20160525110859"></script>
  <script src="/www.idge/js/article.js?v=20160525110859"></script>
  <script src="/www/js/prettify.js?v=20160525110859"></script>
  <script src="/www.idge/js/jquery/responsive-tables.js?v=20160525110859"></script>
  <script src="/www.idge/js/jquery/jquery.tablesorter.min.js?v=20160525110859"></script>
  <script>
  $(document).ready(function() {
    $("table.tablesorter").tablesorter({
      widgets: ['zebra']
    });

    $("table.tablesorter tbody tr").hover(function() {
      $(this).toggleClass("selected");
    });

    $("table.tablesorter thead tr th").each(function(){
      if ($(this).find('.ss-icon').length < 1) {
        $(this).append('<i class="ss-icon"></i>');
      }
    });

  });
  </script>


<script src="/www/js/jquery/jquery.lazyload.min.js?v=20160525110859"></script>

<script src="/www.idge/js/global.js?v=************"></script>

<script src="/www/js/webfonts/ss-social.js?v=20160525110859"></script>
<script src="/www/js/webfonts/ss-standard.js?v=20160525110859"></script>
<script src="/www/js/analytics/brandAnalytics.js"></script>



































<script type='text/javascript'>var _sf_startpt=(new Date()).getTime()</script>

<script type="text/javascript">
  var _sf_async_config={};

  _sf_async_config.uid = 29363;

  _sf_async_config.path = "/article/3057179/data-analytics/healthcare-data-protection-and-privacy-prognosis-still-critical-but-new-treatment-is-available.html";
  _sf_async_config.title = "Healthcare Data Protection and Privacy Prognosis—Still Critical but New Treatment is Available | Computerworld";
  _sf_async_config.domain = "computerworld.com";
  if(window.location.href.indexOf("video")&&true) {
    _sf_async_config.playerdomain= _sf_async_config.domain.replace("www.","");
  }

  _sf_async_config.useCanonical = true;



      _sf_async_config.sections = "data-analytics";
      _sf_async_config.authors="Robert Shields";



  (function(){
    function loadChartbeat() {
      window._sf_endpt=(new Date()).getTime();
      var e = document.createElement("script");
      e.setAttribute("language", "javascript");
      e.setAttribute("type", "text/javascript");
      e.setAttribute("src", (("https:" == document.location.protocol) ?
        "https://a248.e.akamai.net/chartbeat.download.akamai.com/102508/" : "http://static.chartbeat.com/") +
        ((window.location.href.indexOf("video")) ? "js/chartbeat_video.js" : "js/chartbeat.js"));
      document.body.appendChild(e);
    }
    var oldonload = window.onload;
    window.onload = (typeof window.onload != "function") ?
   loadChartbeat : function() { oldonload(); loadChartbeat(); };
  })();
</script>












<!-- Begin BlueKai Tag -->
<iframe name="__bkframe" height="0" width="0" frameborder="0" src="javascript:void(0)"></iframe>
<script type="text/javascript" src="http://tags.bkrtx.com/js/bk-coretag.js"></script>
<script type="text/javascript" src="/www/js/jquery/jquery.cookie.js"></script>
<!-- CryptoJS -->
<script src="http://crypto-js.googlecode.com/svn/tags/3.1.2/build/rollups/md5.js"></script>
<script type="text/javascript" src="/www/js/analytics/idg_bk_coreapi.js?v=20160525110859"></script>

<script type="text/javascript">
var blueKaiId=14339;
var blueKaiPort=10;
var brandCode = "ctw";
var regUrl="http://regdev.idge.int/api/"
var daysToRefresh=30;
var bk_debug_log=false;



   regUrl="http://reg.idgenterprise.com/api/";



  daysToRefresh=30;







if ( typeof(s) != 'undefined' ) {
  bk_addPageCtx("pageType", (typeof(s.pageType)!='undefined')?s.pageType:'');
}


//CMS-Specific
bk_addPageCtx("cmscatids", "[3551,3631]");


bk_addPageCtx("cmpy", "[]");


bk_addPageCtx('tid', '5');




// Add the list name and email address hash params to bluekai tracking
// If the user came from a newsletter clickthru, also set the isnl pagectx
params = getUrlParams('phint');
if(Object.keys(params).length > 0)
  {
  bk_addPageCtx("idg_eid", params['idg_eid']);
  bk_addPageCtx("newt", params['newt']);
  bk_addPageCtx("isnl", "1");
  }

  try{
    IDG.bkCoreApi.start();
  }
  catch(e){
    //alert("Triggering catch")
    bk_doJSTag(blueKaiId ,blueKaiPort);
    if (window.console && window.console.log){
      window.console.log("Error Occured While Processing Profile Siubmission")
    }
  }

</script>



<!-- End BlueKai Tag -->













<!-- START Nielsen Online SiteCensus? V6.0 -->
<!-- COPYRIGHT 2010 Nielsen Online -->
<script type="text/javascript">
    (function () {
        var d = new Image(1, 1);
        d.onerror = d.onload = function () {
            d.onerror = d.onload = null;
        };
        d.src = ["http://secure-us.imrworldwide.com/cgi-bin/m?ci=us-203426h&cg=0&cc=1&si=", escape(window.location.href), "&rp=", escape(document.referrer), "&ts=compact&rnd=", (new Date()).getTime()].join();
    })();
</script>


<!-- END Nielsen Online SiteCensus? V6.0 -->

<script type="text/javascript" src="/www/js/ads/jquery.lazyload-ad.js?v=20160525110859"></script>

<script type="text/javascript">
// -- Load Lazy Advertisement placement as deferred
$("div.lazyload_ad").lazyLoadAd({
        threshold    : 500,         // You can set threshold on how close to the edge ad should come before it is loaded. Default is 0 (when it is visible).
        forceLoad    : false,       // Ad is loaded even if not visible. Default is false.
        onLoad       : false,       // Callback function on call ad loading
        onComplete   : false,       // Callback function when load is loaded
        timeout      : 1500,        // Timeout ad load
        debug        : false,       // For debug use : draw colors border depends on load status
        xray         : false        // For debug use : display a complete page view with ad placements
}) ;
</script>



<script type='text/javascript'>
g_arrModules[''] = true;
g_arrModules['Article Detail:Top:Breadcrumb'] = true;
g_arrModules['Article Detail:Social Sharing Vertical'] = true;
g_arrModules[''] = true;

$(window).load(function() {
  var commentsHeight = $(".gig-comments-comments").height();
  var commentNumber = $(".gig-comments-count").html();



  if (commentsHeight < 400) {
    $('.gig-comments-comments').height('auto').addClass('open');
      $(".gig-comments-more").css("display","none");
  }

  if (commentsHeight > 400) {
    $(".gig-comments-more").css("display","inline-block");
    $(".gig-comments-more").html("View All " + commentNumber);
    $('.gig-comments-comments').css("height","400px");

    $(".gig-comments-more").click(function() {
        $('.gig-comments-comments').height('auto').addClass('open');
        $(".gig-comments-more").css("display","none");
    });
  }

  $(".gig-comment-replyLink").click(function() {
      $('.gig-comments-comments').height('auto').addClass('open');
      $(".gig-comments-more").css("display","none");
  });
  $(".gig-composebox-textarea").click(function() {
    $('.gig-comments-comments').height('auto').addClass('open');
      $(".gig-comments-more").css("display","none");
  });

  //behavior for comments call-to-action - added per IDGMPM-8760
  if (commentNumber != "0 Comments") {
    $("#comment-cta-txt").text(commentNumber);
  }
  var commentCount = parseInt(commentNumber);
  if (commentCount>0) {
    $(".comment-count-bubble").text(commentCount);
  }
  else {
    $(".comment-count-bubble").text(""); //no number if zero per IDGMPM-9441 comments
  }
  if (commentCount==1) {
    $(".comment-text-bubble").text("Comment");
  }
  else {
    $(".comment-text-bubble").text("Comments");
  }

  var reposition = 0;
  if ($("body").hasClass("insider-plus")) reposition = 50; //for fixed header on IDGE Insider sites
  $(".comments-cta, .comments-bubble-cta").click(function(e) {
    e.preventDefault();
    $(".gig-comments-more").click(); //emulate clicking Gigya comments-more button
    $('html,body').animate({
        scrollTop: $("#comments").offset().top - reposition
    }, 600);
  });
});

g_arrModules['Article Detail:Funnel:Popular Articles'] = true;
g_arrModules[''] = true;
g_arrModules['Article Detail:Funnel:Sponsored Blog Posts'] = true;
g_arrModules['Article Detail:Funnel:Homepage Top Story'] = true;
g_arrModules['Article Detail:Funnel:Funnel Crawl'] = true;

    $('#resources-sponsored-links').addClass('show');

</script>















<script language="JavaScript" type="text/javascript">
  var omniture_account="computerworldcom";

  var omniture_trackingServer = "idgenterprise.d1.sc.omtrdc.net";

  g_sOmnitureReportSuite = "computerworldcom";


$(document).ready(function(){

  if(!!$('.sharing-tools').length || !!$('#ss-share').length || (!!$('#sharer').length && !!!$('#sharer.sidecar').length)){
    Socialite.load();
    Socialite.setup({
      twitter: {
        lang      : 'en',
            onclick   : function(e) {socialTrack("Article","Twitter","Click");},
            ontweet   : function(e) {
              socialTrack("Article","Twitter","Tweet");
              $thm.logPlEvent({"b":"ctw","e":"twshare","t":"article","id":"3057179"});
          },
            onretweet : function(e) {socialTrack("Article","Twitter","Retweet");},
            onfavorite: function(e) {socialTrack("Article","Twitter","Favorite");},
            onfollow  : function(e) {socialTrack("Article","Twitter","Follow");followTrack();}
        },
        facebook: {
          lang  : 'en_US',
          appId : '196366870381968',
          onlike  : function(url){
            socialTrack("Article","Facebook","Like");
            $thm.logPlEvent({"b":"ctw","e":"fblike","t":"article","id":"3057179"});
          },
          onunlike: function(url){socialTrack("Article","Facebook","Unlike");},
          xfbml : true,
          version :  'v2.1'
        },
        googleplus: {
            lang  : 'en-US',
            callback: function(el, e) {
              socialTrack("Article","Googleplus","Click");
              $thm.logPlEvent({"b":"ctw","e":"googplus","t":"article","id":"3057179"});
            }
        }
    });

    //$('.sharing-tools').show();
    $('#ss-share').show();

  }

  $('a.pinterest-pinit').click(function(e){
    socialTrack('Article','Pinterest','share');
  });
  $('li.redditShare').click(function(e) {
    socialTrack('Article','Reddit','share');
  });

  $('li.stumbleUponShare').click(function(e) {
    socialTrack('Article','StumbleUpon','share');
  });

  $('.banner-tw').click(function(e){
    brandFollowTrack("twitter");

  });
  $('.banner-fb').click(function(e){
    brandFollowTrack("facebook");
  });
  $('.mediaresource #download-asset').click(function(e){
    Analytics.logAssetDownload($(this).data('id'));
  });

  if (typeof(FB)!='undefined') {
    FB.Event.subscribe('edge.create', function(response){
      s = s_gi(g_sOmnitureReportSuite);
      s.linkTrackEvents = s.events = "event36";
      s.prop48="facebook share";
      s.linkTrackVars = "prop48,events";
      s.tl(true, 'o', 'Social Media share');
    });
  }

  if (typeof(digMag) != 'undefined'){
    if(typeof(digLogin) != 'undefined'){
      DigAnalytics.logSignIn(digLogin);
    }
  }


});

var Analytics = new Object();
Analytics.logArticleComment = function(){
  s = s_gi(g_sOmnitureReportSuite);
  s.linkTrackVars="prop25,eVar25,events";
  s.linkTrackEvents="event19";
  s.events="event19";
  s.prop25 = 'Article Comment';
  s.tl(this,'o','Article Comments');
};
Analytics.logSocialSignin = function(provider){
  s = s_gi(g_sOmnitureReportSuite);

  s.linkTrackEvents = s.events = "event32";
  s.prop19=provider;
  s.linkTrackVars = "prop19,events";
  s.tl(true, 'o', provider);
};
Analytics.logInternalSignin = function(){
  s = s_gi(g_sOmnitureReportSuite);
  s.linkTrackEvents = s.events = "event32";
  s.prop19="Internal Login";
  s.linkTrackVars = "prop19,events";
  s.tl(true, 'o', 'Login');
};
Analytics.logAssetDownload = function(assetname){
  s = s_gi(g_sOmnitureReportSuite);
  s.linkTrackVars="prop52,eVar37,events";
  s.linkTrackEvents="event49";
  s.pagename = 'filetype:'+s.pagename;
  s.events="event49";
  s.prop52 = 'asset:'+assetname;
  eVar37= s.prop52;
  s.tl(this,'o','Asset Download');
}

/**
 * Trigger Omniture and Google Analytics tracking code
 * loc - Home, Footer, Header, Article
 * network - Facebook, Twitter, LinkedIn, Googleplus
 * socialAction - Like, Unlike, Share, Follow, Tweet, Retweet, Favorite, Click
 */
function socialTrack(loc,network,socialAction) {
  //Omniture
  var trackString = network + ":" + loc + ":" + socialAction;
  console.log("socialTrack() "+trackString);
  s.prop48 = trackString;
  s.eVar27 = "D=c48";
  s.event36 = trackString;
  s.linkTrackEvents=s.events="event36";
  s.linkTrackVars = "prop48,eVar27,events";
  s.tl(true, 'o', 'Social media');
}

function linkedInTrack() {
  socialTrack("Article","LinkedIn","Share");
};

function followTrack() {
  console.log("followTrack()");
  s = s_gi(g_sOmnitureReportSuite);
  s.linkTrackEvents = s.events = "event37";
  s.prop49="social media";
  s.linkTrackVars = "prop49,events";
  s.tl(true, 'o', 'Social media follow');
}
function brandFollowTrack(network){
  console.log("brandFollowTrack()");
  s = s_gi(g_sOmnitureReportSuite);
  s.eVar16="followed on:"+network;
  s.linkTrackEvents = s.events = "event37";
  s.prop49="social media";
  s.linkTrackVars = "prop49,eVar16,events";
  s.tl(true, 'o', 'Social media brand follow');
    if (typeof IDG.Eloqua != "undefined") {
        IDG.Eloqua.invoke_flash("social-" + network);
  }
}
function sharingTrack(type, pixelLogString) {
  s = s_gi(g_sOmnitureReportSuite);
  s.eVar16="Sharing on: " + type;
  s.linkTrackEvents = s.events = "event37";
  s.prop49="social media sharing";
  s.linkTrackVars = "prop49,eVar16,events";
  s.tl(true, 'o', 'Social Media Sharing');
    if (typeof IDG.Eloqua != "undefined") {
        IDG.Eloqua.invoke_flash("social-" + type);
  }
    // Record Social Event in pixel logger
    if (pixelLogString !== undefined) {
      $thm.logPlEvent({"b":"ctw","e":pixelLogString,"t":"article","id":"3057179"});
    }
}
function moduleTrack(moduleName,loc) {
  console.log("moduleTrack()");
  s = s_gi(g_sOmnitureReportSuite);
  s.eVar42=moduleName;
  s.eVar44=loc;
  s.linkTrackEvents=s.events='event31';
  s.list3=s.prop42+","+s.eVar42;
  s.tl(true, 'o', 'Module click');
}
function trackSlideshow(articleId, slideTitle, slideNumber) {
  s = s_gi(g_sOmnitureReportSuite);
  s.prop15="page number:" + slideNumber;
  s.eVar15="D=c15";
  s.tl(true, 'o', 'Track Slideshow: articleId:' + articleId + ' title:' + slideTitle + " Slide No.:" + slideNumber);
}
function oClickTrack(key) {
  // test for empty key and don't log if empty
  if (typeof(key) != 'undefined' && key.length > 0){
    s = s_gi(g_sOmnitureReportSuite);

    s.linkTrackVars="eVar42,events";
      s.events="event49";
      s.linkTrackEvents="event49";

    s.eVar42 = key;
    s.list3=s.prop42+","+s.eVar42;
    s.tl(true,'o','module click');
  }
};
</script>
<!-- SiteCatalyst code version: H.26.2.
Copyright 1996-2013 Adobe, Inc. All Rights Reserved
More info available at http://www.omniture.com -->

<script language="JavaScript" type="text/javascript" src="/www.idge/js/analytics/s_code.js?v=20160525110859"></script>
<script language="JavaScript" type="text/javascript" id="omniVars"><!--
g_sOmnitureReportSuite = typeof g_sOmnitureReportSuite != 'undefined' && g_sOmnitureReportSuite ? g_sOmnitureReportSuite : "computerworldcom";

/* You may give each page an identifying name, server, and channel on
the next lines. */
//BEGIN OMNITURE VARS
s.pageName="Computerworld:article:opinion:Data Analytics:3057179:Healthcare Data Protection and Privacy Prognosis—Still Critical but New Treatment is Available";

s.pageType="";
if (typeof omnitureChannel != 'undefined'){
s.channel=omnitureChannel;
}
s.prop1="source:computerworld";s.eVar1="D=c1";
s.prop2="content type:opinion";s.eVar2="D=c2";
s.prop3="display type:article:opinion";s.eVar3="D=c3";
s.prop4="40 days";s.eVar4="D=c4";
s.prop5="blog";
s.prop6="";
s.prop7="blogs:great data thinking";s.eVar7="D=c7";
s.prop9="category:data analytics";s.eVar9="D=c9";
s.prop11="published:15-Apr-16";s.eVar11="D=c11";
s.prop12="aid:3057179";s.eVar12="D=c12";
s.prop13="";s.eVar13="D=c13";
s.prop14="author:robert shields";s.eVar14="D=c14";
s.prop15="page number:1";s.eVar15="D=c15";
s.prop17="uri:/article/3057179/data-analytics/healthcare-data-protection-and-privacy-prognosis-still-critical-but-new-treatment-is-available.html";s.eVar17="D=c17";
s.prop18="";s.eVar18="D=c18";
s.prop24=getLoginStatusForOmniture();s.eVar24="D=c24";
s.prop28=getTrackingToken('tk') ? "tokens:"+getTrackingToken('tk') : "";s.eVar28="D=c28";

s.prop37="rpn:ctw:opinion:computerworld:article:opinion:data analytics:3057179:healthcare data protection and privacy prognosis—still critical but new treatment is available";

s.eVar37="D=c37";
s.prop39="";s.eVar39="D=c39";
s.prop40=" sponsored";
s.prop41=IDG.GPT.getDisplayedAds(',') + ',' + IDG.GPT.getOtherAds(',');
s.prop42=IDG.GPT.getModules(',');s.eVar42="D=c42";


s.prop53="3551";s.eVar53="D=c53";
s.prop54="data analytics,healthcare it";s.eVar30="D=c54";
s.eVar33=s.prop55="3551,3631";s.eVar33="D=c55";
s.prop56="";s.eVar40="D=c56";
s.prop61="";s.eVar10="D=c61";
s.prop74="bigdata";s.eVar58="D=c74";
s.prop75="analytics";s.eVar60="D=c75";
s.prop58="bigdata:analytics";s.eVar59="D=c58";
//module tracking
var sModules = '';
for(var i in g_arrModules){
  if(g_arrModules[i] && i != ''){
    sModules += i+',';
  }
}
sModules = sModules.replace(/,$/,'');
s.prop42 = sModules;
s.eVar42="D=c42";
//now set events
s.events="";


  //article view
  s.events=s.apl(s.events,'event33',',');
  s.eVar41="15-Apr-16";

s.list3=s.prop42+","+s.eVar42;
//force all values to lower case for reporting consistency
for(var p in s){
  if(s[p] && p.match(/^(prop|ceVar)\d+$/)){
    //console.log(p+": "+s[p]);
    s[p] = s[p].toLowerCase();
    //console.log(p+": "+s[p]);
  }
}
//END OMNITURE VARS

/* Conversion Variables */
s.campaign=getTrackingToken('tk') ? "tokens:"+getTrackingToken('tk') : "";

/************* DO NOT ALTER ANYTHING BELOW THIS LINE ! **************/
var s_code=s.t();if(s_code)document.write(s_code)//--></script>
<script language="JavaScript" type="text/javascript"><!--
if(navigator.appVersion.indexOf('MSIE')>=0)document.write(unescape('%3C')+'\!-'+'-')
//--></script><noscript><img src="http://idgenterprise.d1.sc.omtrdc.net/b/ss/computerworldcom/1/H.25--NS/0"
height="1" width="1" border="0" alt="" /></noscript><!--/DO NOT REMOVE/-->
<!-- End SiteCatalyst code version: H.26.2. -->















<script type="text/javascript">
$thm.logPlEvent({"b":"ctw","e":"view","t":"article","id":"3057179"});
</script>



    <div id="loginModal"></div>
    <div id="logoffModal"></div>


















  <script type="text/javascript">

  (function(){

    function loadHorizon(){
      var s = document.createElement('script');
      s.type = 'text/javascript';
      s.async = true;
      s.src = location.protocol + '//ak.sail-horizon.com/horizon/v1.js';
      var x = document.getElementsByTagName('script')[0];
      x.parentNode.insertBefore(s, x);
    }

    loadHorizon();
    var oldOnLoad = window.onload;
    window.onload = function(){
              if (typeof oldOnLoad === 'function'){
                  oldOnLoad();
            }

        Sailthru.setup({
              domain: 'horizon.computerworld.com',
                useStoredTags: false
        });
    };
    if(console!=undefined){
      console.log('SAIL-23 fired Horizon');
    }

  })();
  </script>

  </body>
</html>
