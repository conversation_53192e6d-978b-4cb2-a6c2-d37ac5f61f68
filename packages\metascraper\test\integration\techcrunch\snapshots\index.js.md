# Snapshot report for `test/integration/techcrunch/index.js`

The actual snapshot is saved in `index.js.snap`.

Generated by [AVA](https://avajs.dev).

## techcrunch

> Snapshot 1

    {
      audio: null,
      author: '<PERSON>',
      date: '2016-01-12T21:25:54.000Z',
      description: 'Recruiting software engineers is a massive headache for both startups and established companies. For a while now, HackerRank has tried to make',
      image: 'https://techcrunch.com/wp-content/uploads/2015/08/10-interviewed.png',
      lang: 'en',
      logo: 'https://techcrunch.com/wp-content/uploads/2018/04/tc-logo-2018-square-reverse2x.png?resize=1200,1200',
      publisher: 'TechCrunch',
      title: '<PERSON><PERSON>Rank Makes Technical Recruiting More Transparent | TechCrunch',
      url: 'https://techcrunch.com/2016/01/12/hackerrank-jobs-takes-the-mystery-out-of-technical-recruiting/',
      video: null,
    }
