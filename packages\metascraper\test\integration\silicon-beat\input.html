
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US" prefix="og: http://ogp.me/ns#">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
        <title>VC: Time to &#039;come out as a woman&#039;</title>

  <link rel="stylesheet" type="text/css" href="http://www.siliconbeat.com/wp-content/themes/erudito-child/style.css" media="screen" />
  <link rel="stylesheet" type="text/css" href="http://fonts.googleapis.com/css?family=Open+Sans+Condensed:700" />

    <link rel="pingback" href="http://www.siliconbeat.com/xmlrpc.php" />


<script type="text/javascript">
var TB_pluginPath = 'http://www.siliconbeat.com/wp-content/plugins/tweet-blender', TB_C_pluginPath = 'http://www.siliconbeat.com/wp-content/plugins/tweet-blender-charts';
var TB_config = {
'widget_check_sources':true,
'rate_limit_data':'Array',
'ajax_url':'http://www.siliconbeat.com/wp-admin/admin-ajax.php'
}</script>
<link rel="canonical" href="http://www.siliconbeat.com/2016/04/19/time-come-woman/" />
<!-- This site is optimized with the Yoast SEO plugin v3.2.5 - https://yoast.com/wordpress/plugins/seo/ -->
<link rel="canonical" href="http://www.siliconbeat.com/2016/04/19/time-come-woman/" />
<meta property="og:locale" content="en_US" />
<meta property="og:type" content="article" />
<meta property="og:title" content="VC: Time to &#039;come out as a woman&#039;" />
<meta property="og:description" content="It&#8217;s no secret that few women hold positions of power at venture capital firms. A new study quantifies what many in the industry have reported anecdotally. According to CrunchBase data released Tuesday on &hellip;" />
<meta property="og:url" content="http://www.siliconbeat.com/2016/04/19/time-come-woman/" />
<meta property="og:site_name" content="SiliconBeat" />
<meta property="article:author" content="www.facebook.com/marisakendallnews" />
<meta property="article:tag" content="gender diversity" />
<meta property="article:tag" content="VC" />
<meta property="article:tag" content="venture capital" />
<meta property="article:section" content="Silicon Beat" />
<meta property="article:published_time" content="2016-04-19T18:52:32-07:00" />
<meta property="article:modified_time" content="2016-04-19T19:05:48-07:00" />
<meta property="og:updated_time" content="2016-04-19T19:05:48-07:00" />
<meta property="og:image" content="http://www.siliconbeat.com/wp-content/uploads/2016/04/Panel.jpg" />
<meta property="og:image:width" content="3264" />
<meta property="og:image:height" content="2448" />
<meta name="twitter:card" content="summary" />
<meta name="twitter:description" content="It&#8217;s no secret that few women hold positions of power at venture capital firms. A new study quantifies what many in the industry have reported anecdotally. According to CrunchBase data released Tuesday on [&hellip;]" />
<meta name="twitter:title" content="VC: Time to &#039;come out as a woman&#039;" />
<meta name="twitter:image" content="http://www.siliconbeat.com/wp-content/uploads/2016/04/Panel.jpg" />
<meta name="twitter:creator" content="@MarisaKendall" />
<!-- / Yoast SEO plugin. -->

<link rel="alternate" type="application/rss+xml" title="SiliconBeat &raquo; Feed" href="http://www.siliconbeat.com/feed/" />
<link rel="alternate" type="application/rss+xml" title="SiliconBeat &raquo; Comments Feed" href="http://www.siliconbeat.com/comments/feed/" />
<link rel="alternate" type="application/rss+xml" title="SiliconBeat &raquo; VC: Time to &#8216;come out as a woman&#8217; Comments Feed" href="http://www.siliconbeat.com/2016/04/19/time-come-woman/feed/" />
    <script type="text/javascript">
      window._wpemojiSettings = {"baseUrl":"https:\/\/s.w.org\/images\/core\/emoji\/72x72\/","ext":".png","source":{"concatemoji":"http:\/\/www.siliconbeat.com\/wp-includes\/js\/wp-emoji-release.min.js?ver=c2e8f7dcc3a52278d0c2e17868b455d8"}};
      !function(a,b,c){function d(a){var c,d,e,f=b.createElement("canvas"),g=f.getContext&&f.getContext("2d"),h=String.fromCharCode;if(!g||!g.fillText)return!1;switch(g.textBaseline="top",g.font="600 32px Arial",a){case"flag":return g.fillText(h(55356,56806,55356,56826),0,0),f.toDataURL().length>3e3;case"diversity":return g.fillText(h(55356,57221),0,0),c=g.getImageData(16,16,1,1).data,d=c[0]+","+c[1]+","+c[2]+","+c[3],g.fillText(h(55356,57221,55356,57343),0,0),c=g.getImageData(16,16,1,1).data,e=c[0]+","+c[1]+","+c[2]+","+c[3],d!==e;case"simple":return g.fillText(h(55357,56835),0,0),0!==g.getImageData(16,16,1,1).data[0];case"unicode8":return g.fillText(h(55356,57135),0,0),0!==g.getImageData(16,16,1,1).data[0]}return!1}function e(a){var c=b.createElement("script");c.src=a,c.type="text/javascript",b.getElementsByTagName("head")[0].appendChild(c)}var f,g,h,i;for(i=Array("simple","flag","unicode8","diversity"),c.supports={everything:!0,everythingExceptFlag:!0},h=0;h<i.length;h++)c.supports[i[h]]=d(i[h]),c.supports.everything=c.supports.everything&&c.supports[i[h]],"flag"!==i[h]&&(c.supports.everythingExceptFlag=c.supports.everythingExceptFlag&&c.supports[i[h]]);c.supports.everythingExceptFlag=c.supports.everythingExceptFlag&&!c.supports.flag,c.DOMReady=!1,c.readyCallback=function(){c.DOMReady=!0},c.supports.everything||(g=function(){c.readyCallback()},b.addEventListener?(b.addEventListener("DOMContentLoaded",g,!1),a.addEventListener("load",g,!1)):(a.attachEvent("onload",g),b.attachEvent("onreadystatechange",function(){"complete"===b.readyState&&c.readyCallback()})),f=c.source||{},f.concatemoji?e(f.concatemoji):f.wpemoji&&f.twemoji&&(e(f.twemoji),e(f.wpemoji)))}(window,document,window._wpemojiSettings);
    </script>
    <style type="text/css">
img.wp-smiley,
img.emoji {
  display: inline !important;
  border: none !important;
  box-shadow: none !important;
  height: 1em !important;
  width: 1em !important;
  margin: 0 .07em !important;
  vertical-align: -0.1em !important;
  background: none !important;
  padding: 0 !important;
}
</style>
<link rel='stylesheet' id='slideshow-css'  href='http://www.siliconbeat.com/wp-content/plugins/slideshow/slideshow.css?ver=0.1' type='text/css' media='all' />
<link rel='stylesheet' id='wpz-shortcodes-css'  href='http://www.siliconbeat.com/wp-content/themes/erudito/functions/wpzoom/assets/css/shortcodes.css?ver=c2e8f7dcc3a52278d0c2e17868b455d8' type='text/css' media='all' />
<link rel='stylesheet' id='zoom-font-awesome-css'  href='http://www.siliconbeat.com/wp-content/themes/erudito/functions/wpzoom/assets/css/font-awesome.min.css?ver=c2e8f7dcc3a52278d0c2e17868b455d8' type='text/css' media='all' />
<link rel='stylesheet' id='wzslider-css'  href='http://www.siliconbeat.com/wp-content/themes/erudito/functions/wpzoom/assets/css/wzslider.css?ver=c2e8f7dcc3a52278d0c2e17868b455d8' type='text/css' media='all' />
<link rel='stylesheet' id='wpzoom-theme-css'  href='http://www.siliconbeat.com/wp-content/themes/erudito/styles/blue.css?ver=c2e8f7dcc3a52278d0c2e17868b455d8' type='text/css' media='all' />
<link rel='stylesheet' id='wpzoom-custom-css'  href='http://www.siliconbeat.com/wp-content/themes/erudito/custom.css?ver=c2e8f7dcc3a52278d0c2e17868b455d8' type='text/css' media='all' />
<link rel='stylesheet' id='genericons-css'  href='http://www.siliconbeat.com/wp-content/plugins/jetpack/_inc/genericons/genericons/genericons.css?ver=3.1' type='text/css' media='all' />
<link rel='stylesheet' id='jetpack_css-css'  href='http://www.siliconbeat.com/wp-content/plugins/jetpack/css/jetpack.css?ver=4.0.2' type='text/css' media='all' />
<link rel='stylesheet' id='tb-css-css'  href='http://www.siliconbeat.com/wp-content/plugins/tweet-blender/css/tweets.css?ver=c2e8f7dcc3a52278d0c2e17868b455d8' type='text/css' media='all' />
<script type='text/javascript' src='http://www.siliconbeat.com/wp-includes/js/jquery/jquery.js?ver=1.12.3'></script>
<script type='text/javascript' src='http://www.siliconbeat.com/wp-includes/js/jquery/jquery-migrate.min.js?ver=1.4.0'></script>
<script type='text/javascript' src='http://local.digitalfirstmedia.com/common/dfm/assets/js/dfm-core-level1.min.js?ver=1.0'></script>
<script type='text/javascript' src='http://www.siliconbeat.com/wp-content/plugins/wp-featured-content-slider/scripts/jquery.cycle.all.2.72.js?ver=1.3'></script>
<script type='text/javascript' src='http://www.siliconbeat.com/wp-content/themes/erudito/js/init.js?ver=c2e8f7dcc3a52278d0c2e17868b455d8'></script>
<link rel='https://api.w.org/' href='http://www.siliconbeat.com/wp-json/' />
<link rel="EditURI" type="application/rsd+xml" title="RSD" href="http://www.siliconbeat.com/xmlrpc.php?rsd" />
<link rel="wlwmanifest" type="application/wlwmanifest+xml" href="http://www.siliconbeat.com/wp-includes/wlwmanifest.xml" />

<link rel='shortlink' href='http://wp.me/p2Eoq6-rvj' />
<link rel="alternate" type="application/json+oembed" href="http://www.siliconbeat.com/wp-json/oembed/1.0/embed?url=http%3A%2F%2Fwww.siliconbeat.com%2F2016%2F04%2F19%2Ftime-come-woman%2F" />
<link rel="alternate" type="text/xml+oembed" href="http://www.siliconbeat.com/wp-json/oembed/1.0/embed?url=http%3A%2F%2Fwww.siliconbeat.com%2F2016%2F04%2F19%2Ftime-come-woman%2F&#038;format=xml" />

    <script type="text/javascript" src="http://local.denverpost.com/common/dfm/dfm-core.js"></script>
    <!-- DFM API -->
    <script type="text/javascript">
        dfm.api("data","siteId",            "");
        dfm.api("data","company",           "");
        dfm.api("data","siteName",          "SiliconBeat");
        dfm.api("data","siteDomain",        "SiliconBeat");
        dfm.api("data","currentDomain",     "siliconbeat.com");
        dfm.api("data","contentId",         "");
        dfm.api("data","sectionName",       "Silicon Beat");
        dfm.api("data","pageID",            "");
        dfm.api("data","pageUrl",           "/2016/04/19/time-come-woman/");
        dfm.api("data","pageTitle",         "VC: Time to &#8216;come out as a woman&#8217;");
        dfm.api("data","sectionId",         "");
        dfm.api("data","keywords",          "");
        dfm.api("data","adTaxonomy",        "");
        dfm.api("data","Taxonomy1",         "Blogs");
        dfm.api("data","Taxonomy2",         "Silicon Beat");
        dfm.api("data","Taxonomy3",         "");
        dfm.api("data","Taxonomy4",         "");
        dfm.api("data","kv",                "Silicon Beat");
        dfm.api("data","googleUA",          "UA-61435456-5");
        dfm.api("data","contentGeography",  "");
        dfm.api("data","contentRegion",     "");
        dfm.api("data","contentPostalCode", "");
        dfm.api("data","contentPrice",      "");
        dfm.api("data","contentChannel",    "");
        dfm.api("data","contentType",       "");
        dfm.api("data","contentCity",       "");
        //Extra
        dfm.api("data","errorType",         "");
        dfm.api("data","pageType",          "");
        dfm.api("data", "quantcast",        "p-4ctCQwtnNBNs2");
        dfm.api("data", "comscore",         "6035443");
    </script>


    <!-- GTM Data Layer -->
    <script type="text/javascript">
      var dfm_gtm_dataLayer = true;

    var dfm_gtm_publisherproduct = dfm.api("data","currentDomain");
    var dataLayer_gaua = dfm.api("data", "googleUA");
    var dataLayer_digitalpublisher = dfm.api("data", "company");
    var dataLayer_taxonomy1 = dfm.api("data","Taxonomy1");
    var dataLayer_taxonomy2 = dfm.api("data","Taxonomy2");
    var dataLayer_quantcast = dfm.api("data", "quantcast");
    var dataLayer_comscore = dfm.api("data", "comscore");

      analyticsEvent = function() {};
      analyticsSocial = function() {};
      analyticsVPV = function() {};
      analyticsClearVPV = function() {};
      analyticsForm = function() {};
      window.dataLayer = window.dataLayer || [];
      (function() {
        window.gaJsonData = window.gaJsonData || [];
        var gaTempJson = {
          "comscore": "",
          "pubplatform": "WP",
          "releaseversion": "",
          "digitalpublisher": "",
          "publisherstate": ""
        };
        gaJsonData.push(gaTempJson);
        var data = gaJsonData[0];
        var json = {
          "gaua": function() {
            return data.gaua;
          },
          "quantcast": function () {
            return data.quantcast;
          },
          "comscore": function() {
            return data.comscore;
          },
          "pubplatform": function() {
            return data.pubplatform;
          },
          "releaseversion": function() {
            return data.releaseversion;
          },
          "digitalpublisher": function() {
            return data.digitalpublisher;
          },
          "publisherstate": function() {
            return data.publisherstate;
          }
        };
        /*
          Error type
        */
         var dataLayer_error = dfm.api("data","errorType");
        /*
          ga_ua
        */
        //var dataLayer_gaua = json["gaua"]();
        /*
          quantcast
        */
        //var dataLayer_quantcast = json["quantcast"]();
        /*
          comscore
        */
        //var dataLayer_comscore = json["comscore"]();
        /*
          Release Version
        */
        var dataLayer_releaseversion = json["releaseversion"]();
        /*
          Digital Publisher
        */
        //var dataLayer_digitalpublisher = json["digitalpublisher"]();
        /*
          Platform
        */
        var dataLayer_pubplatform = json["pubplatform"]();
        /*
          Publisher State
        */
        var dataLayer_publisherstate = json["publisherstate"]();
        // Push the values into the dataLayer
        dataLayer.push({
          "errorType":dataLayer_error,
          "ga_ua":dataLayer_gaua,
          "quantcast":dataLayer_quantcast,
          "comscore":dataLayer_comscore,
          "Release Version":dataLayer_releaseversion,
          "Digital Publisher":dataLayer_digitalpublisher,
          "Platform":dataLayer_pubplatform,
          "Publisher State":dataLayer_publisherstate
        });
      }());
    </script>

    <!-- GA global -->
    <script type="text/javascript" src="http://local.medianewsgroup.com/common/dfm/ga-datalayer.js"></script>
        <!-- Google Tag Manager dfm -->
        <noscript>
            <iframe src="//www.googletagmanager.com/ns.html?id=GTM-TLFP4R" height="0" width="0" style="display:none;visibility:hidden"></iframe>
        </noscript>
            <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
            '//www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
            })(window,document,'script','dataLayer','GTM-TLFP4R');</script>
        <!-- End Google Tag Manager --><style>#responsive-menu .appendLink, #responsive-menu .responsive-menu li a, #responsive-menu #responsive-menu-title a,#responsive-menu .responsive-menu, #responsive-menu div, #responsive-menu .responsive-menu li, #responsive-menu{box-sizing: content-box !important;-moz-box-sizing: content-box !important;-webkit-box-sizing: content-box !important;-o-box-sizing: content-box !important}.RMPushOpen{width: 100% !important;overflow-x: hidden !important;height: 100% !important}.RMPushSlide{position: relative;left: 75%}#responsive-menu{position: absolute;width: 75%;left: -75%;top: 0px;background: #43494C;z-index: 9999;box-shadow: 0px 1px 8px #333333;font-size: 13px !important;max-width: 999px;display: none}#responsive-menu.admin-bar-showing{padding-top: 32px}#click-menu.admin-bar-showing{margin-top: 32px}#responsive-menu #rm-additional-content{padding: 10px 5% !important;width: 90% !important;color: #FFFFFF}#responsive-menu .appendLink{right: 0px !important;position: absolute !important;border: 1px solid #3C3C3C !important;padding: 12px 10px !important;color: #FFFFFF !important;background: #43494C !important;height: 20px !important;line-height: 20px !important;border-right: 0px !important}#responsive-menu .appendLink:hover{cursor: pointer;background: #3C3C3C !important;color: #FFFFFF !important}#responsive-menu .responsive-menu, #responsive-menu div, #responsive-menu .responsive-menu li,#responsive-menu{text-align: left !important}#responsive-menu .RMImage{vertical-align: middle;margin-right: 10px;display: inline-block}#responsive-menu.RMOpened{}#responsive-menu,#responsive-menu input{}#responsive-menu #responsive-menu-title{width: 95% !important;font-size: 14px !important;padding: 20px 0px 20px 5% !important;margin-left: 0px !important;background: #43494C !important;white-space: nowrap !important}#responsive-menu #responsive-menu-title,#responsive-menu #responsive-menu-title a{color: #FFFFFF !important;text-decoration: none !important;overflow: hidden !important}#responsive-menu #responsive-menu-title a:hover{color: #FFFFFF !important;text-decoration: none !important}#responsive-menu .appendLink,#responsive-menu .responsive-menu li a,#responsive-menu #responsive-menu-title a{transition: 1s all;-webkit-transition: 1s all;-moz-transition: 1s all;-o-transition: 1s all}#responsive-menu .responsive-menu{width: 100% !important;list-style-type: none !important;margin: 0px !important}#responsive-menu .responsive-menu li.current-menu-item > a,#responsive-menu .responsive-menu li.current-menu-item > .appendLink,#responsive-menu .responsive-menu li.current_page_item > a,#responsive-menu .responsive-menu li.current_page_item > .appendLink{background: #43494C !important;color: #FFFFFF !important}#responsive-menu .responsive-menu li.current-menu-item > a:hover,#responsive-menu .responsive-menu li.current-menu-item > .appendLink:hover,#responsive-menu .responsive-menu li.current_page_item > a:hover,#responsive-menu .responsive-menu li.current_page_item > .appendLink:hover{background: #43494C !important;color: #FFFFFF !important}#responsive-menu.responsive-menu ul{margin-left: 0px !important}#responsive-menu .responsive-menu li{list-style-type: none !important;position: relative !important}#responsive-menu .responsive-menu ul li:last-child{padding-bottom: 0px !important}#responsive-menu .responsive-menu li a{padding: 12px 0px 12px 5% !important;width: 95% !important;display: block !important;height: 20px !important;line-height: 20px !important;overflow: hidden !important;white-space: nowrap !important;color: #FFFFFF !important;border-top: 1px solid #3C3C3C !important;text-decoration: none !important}#click-menu{text-align: center;cursor: pointer;font-size: 13px !important;display: none;position: absolute;right: 5%;top: 10px;color: #FFFFFF;background: #000000;padding: 5px;z-index: 9999}#responsive-menu #responsiveSearch{display: block !important;width: 95% !important;padding-left: 5% !important;border-top: 1px solid #3C3C3C !important;clear: both !important;padding-top: 10px !important;padding-bottom: 10px !important;height: 40px !important;line-height: 40px !important}#responsive-menu #responsiveSearchSubmit{display: none !important}#responsive-menu #responsiveSearchInput{width: 91% !important;padding: 5px 0px 5px 3% !important;-webkit-appearance: none !important;border-radius: 2px !important;border: 1px solid #3C3C3C !important}#responsive-menu .responsive-menu,#responsive-menu div,#responsive-menu .responsive-menu li{width: 100% !important;margin-left: 0px !important;padding-left: 0px !important}#responsive-menu .responsive-menu li li a{padding-left: 10% !important;width: 90% !important;overflow: hidden !important}#responsive-menu .responsive-menu li li li a{padding-left: 15% !important;width: 85% !important;overflow: hidden !important}#responsive-menu .responsive-menu li li li li a{padding-left: 20% !important;width: 80% !important;overflow: hidden !important}#responsive-menu .responsive-menu li li li li li a{padding-left: 25% !important;width: 75% !important;overflow: hidden !important}#responsive-menu .responsive-menu li a:hover{background: #3C3C3C !important;color: #FFFFFF !important;list-style-type: none !important;text-decoration: none !important}#click-menu #RMX{display: none;font-size: 24px;line-height: 27px !important;height: 27px !important;color: #FFFFFF !important;top: -4px !important;position: relative !important}#click-menu #RMX.threelines-float-left,#click-menu #RMX.threelines-float-right,#click-menu-label{line-height: 14px !important}#click-menu-label.click-menu-label-left{float: left !important;margin-right: 10px !important}#click-menu-label.click-menu-label-right{float: right !important;margin-left: 5px !important}#click-menu img,#click-menu .threeLines{width: 33px !important;height: 27px !important;margin: 6px auto !important}#click-menu img.rm-img-clicked{display: none}#click-menu img{height: auto !important}#click-menu img.click-menu-float-left,#click-menu img.click-menu-float-right,#click-menu .threeLines.threelines-float-left{float: left !important}#click-menu .threeLines .line{height: 5px !important;margin-bottom: 6px !important;background: #FFFFFF !important;width: 100% !important}#click-menu .threeLines .line.last{margin-bottom: 0px !important}@media only screen and ( min-width : 0px ) and ( max-width : 800px ){#click-menu{display: block}#main-menu{display: none !important}}</style><!-- BEGIN wp-parsely Plugin Version 1.8 -->
<meta name='wp-parsely_version' id='wp-parsely_version' content='1.8' />
   <script type="application/ld+json">
   {"@context":"http:\/\/schema.org","@type":"NewsArticle","headline":"VC: Time to &#8216;come out as a woman&#8217;","url":"http:\/\/www.siliconbeat.com\/2016\/04\/19\/time-come-woman\/","thumbnailUrl":"http:\/\/www.siliconbeat.com\/wp-content\/uploads\/2016\/04\/Panel-350x263.jpg","articleId":"blog-105729","dateCreated":"2016-04-20T01:52:32Z","articleSection":"Silicon Beat","creator":["Marisa Kendall"],"keywords":null}   </script>
<!-- END wp-parsely Plugin Version 1.8 -->

<link rel='dns-prefetch' href='//v0.wordpress.com'>
<!-- WPZOOM Theme / Framework -->
<meta name="generator" content="SiliconBeat's Erudito Child Theme 1.01" />
<meta name="generator" content="WPZOOM Framework 1.4.8" />
<style type="text/css"></style>
<script type="text/javascript">document.write('<style type="text/css">.tabber{display:none;}</style>');</script><style type="text/css">.broken_link, a.broken_link {
  text-decoration: line-through;
}</style><meta name="google-site-verification" content="uhWFzvcHoaF05-ERyNQjzCLgoR4J_UcW-xDTVipK3nE" />

<style>
#top-menu li.social-icons { display: none; }
</style>

<!-- chartBeat
<script type="text/javascript" src="http://extras.mercurynews.com/scripts/chartbeat_load_for_blogs.js"></script>
-->

<script type="text/javascript">
var _sf_startpt=(new Date()).getTime();
var _sf_async_config={};

jQuery(document).ready(function () {
  /** CONFIGURATION START **/
  _sf_async_config.uid = 53628;
  _sf_async_config.domain = 'mercurynews.com';
  _sf_async_config.useCanonical = true;
  _sf_async_config.sections = 'blogs';
  _sf_async_config.title = '[blog] '+ jQuery('h1.title').text();
  /** CONFIGURATION END **/
  (function(){
    function loadChartbeat() {
      window._sf_endpt=(new Date()).getTime();
      var e = document.createElement('script');
      e.setAttribute('language', 'javascript');
      e.setAttribute('type', 'text/javascript');
      e.setAttribute('src', '//static.chartbeat.com/js/chartbeat.js');
      document.body.appendChild(e);
    }
    var oldonload = window.onload;
    window.onload = (typeof window.onload != 'function') ?
       loadChartbeat : function() { oldonload(); loadChartbeat(); };
  });
});
/** END CHARTBEAT **/
</script>

<!-- NDN -->
<script type="text/javascript" async src="http://launch.newsinc.com/js/embed.js" id="_nw2e-js"></script></head>
<body class="single single-post postid-105729 single-format-standard">


    <!-- begin blog_MN-Business 728x90 Ad Tag -->
    <script type='text/javascript'>
    (function() {
    var useSSL = 'https:' == document.location.protocol;
    var src = (useSSL ? 'https:' : 'http:') +
    '//www.googletagservices.com/tag/js/gpt.js';
    document.write('<scr' + 'ipt src="' + src + '"></scr' + 'ipt>');
    })();
    </script>
    <div id='div-gpt-ad-1364842121944-3' style="margin:12px 0 33px;">
    <script type='text/javascript'>
    var mappingLeederbord = googletag.sizeMapping().
      //addSize([1024, 768], [970, 250]).
      addSize([640, 480], [728, 90]).
      addSize([1, 1], [320, 50]).
      // Fits browsers of any size smaller than 640 x 480
      build();

    googletag.defineSlot('/8013/mercurynews.com/Business', [[320, 50], [728, 90]], 'div-gpt-ad-1364842121944-3').setTargeting('pos', 'top_leaderboard').defineSizeMapping(mappingLeederbord).setTargeting('kv', 'business').addService(googletag.pubads());

    googletag.pubads().enableSyncRendering();
    googletag.enableServices();

    googletag.display('div-gpt-ad-1364842121944-3');
    </script>
    </div>
    <!-- end 728x90 Ad Tag -->


<div id="container">

  <div class="wrapper wrapper-main">

    <header>

      <div id="logo">


        <a href="http://www.siliconbeat.com" title="What&#039;s next in tech">
                      <img src="http://www.siliconbeat.com/wp-content/uploads/2013/03/SiliconBeat_header_559x124.jpg" alt="SiliconBeat" />
                  </a>



      </div><!-- end #logo -->


      <nav id="top-menu">


        <ul><li id="menu-item-37722" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-37722"><a href="http://www.siliconvalley.com">SiliconValley.com</a></li>
<li id="menu-item-37723" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-37723"><a href="https://twitter.com/siliconbeat">Twitter</a></li>
<li id="menu-item-37724" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-37724"><a href="http://www.facebook.com/pages/SiliconBeat/324955307584131">Facebook</a></li>
<li id="menu-item-37725" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-37725"><a href="https://plus.google.com/+Siliconbeat/posts">Google+</a></li>
<li id="menu-item-37726" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-37726"><a href="http://feeds.feedburner.com/SiliconbeatTech">RSS</a></li>
<li class="social-icons"><a href="http://www.siliconbeat.com/feed/" rel="external,nofollow"><img src="http://www.siliconbeat.com/wp-content/themes/erudito/images/icons/rss_small.png" width="16" height="16" alt="" /></a></li></ul>     </nav><!-- end #top-menu -->


      <div class="cleaner">&nbsp;</div>

    </header>


        <nav id="main-menu">


      <ul class="dropdown"><li id="menu-item-7728" class="menu-item menu-item-type-taxonomy menu-item-object-category menu-item-7728"><a href="http://www.siliconbeat.com/category/social/">Social</a></li>
<li id="menu-item-7484" class="menu-item menu-item-type-taxonomy menu-item-object-category menu-item-7484"><a href="http://www.siliconbeat.com/category/web/">Web</a></li>
<li id="menu-item-7399" class="menu-item menu-item-type-taxonomy menu-item-object-category menu-item-7399"><a href="http://www.siliconbeat.com/category/mobile-2/">Mobile</a></li>
<li id="menu-item-7401" class="menu-item menu-item-type-taxonomy menu-item-object-category menu-item-7401"><a href="http://www.siliconbeat.com/category/cloud/">Cloud</a></li>
<li id="menu-item-7397" class="menu-item menu-item-type-taxonomy menu-item-object-category menu-item-7397"><a href="http://www.siliconbeat.com/category/gadgets/">Gadgets</a></li>
<li id="menu-item-7398" class="menu-item menu-item-type-taxonomy menu-item-object-category menu-item-7398"><a href="http://www.siliconbeat.com/category/clean_tech/">Cleantech</a></li>
<li id="menu-item-7403" class="menu-item menu-item-type-taxonomy menu-item-object-category current-post-ancestor current-menu-parent current-post-parent menu-item-7403"><a href="http://www.siliconbeat.com/category/venturecapital/">VC</a></li>
<li id="menu-item-7482" class="menu-item menu-item-type-taxonomy menu-item-object-category menu-item-7482"><a href="http://www.siliconbeat.com/category/big-data/">Data</a></li>
<li id="menu-item-7487" class="menu-item menu-item-type-taxonomy menu-item-object-category menu-item-7487"><a href="http://www.siliconbeat.com/category/games/">Games</a></li>
<li id="menu-item-37643" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-37643"><a href="http://www.siliconbeat.com/category/government-policy/">Policy</a></li>
<li id="menu-item-37820" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-37820"><a href="http://www.siliconbeat.com/category/media/">Media</a></li>
<li class="cleaner">&nbsp;</li></ul>
    </nav><!-- end #main-menu -->


<div id="main" class="single-post">

  <div id="content">

    <div class="wrapper-content">


      <h1 class="title title-large">VC: Time to &#8216;come out as a woman&#8217;</h1>
      <p class="postmetadata">By <a href="http://www.siliconbeat.com/author/marisa-kendall/" title="Posts by Marisa Kendall" rel="author">Marisa Kendall</a>             / <time datetime="2016-04-19" pubdate>April 19, 2016 at 6:52 PM</time>           </p>

      <div class="post-content">
        <img src="http://www.siliconbeat.com/wp-content/uploads/2016/04/Panel.jpg" width="100%" /><!-- AddThis Sharing Buttons above --><div class='at-above-post addthis_default_style addthis_toolbox at-wordpress-hide' data-title='VC: Time to &#8216;come out as a woman&#8217;' data-url='http://www.siliconbeat.com/2016/04/19/time-come-woman/'></div><p>It&#8217;s no secret that few women hold positions of power at venture capital firms.</p>
<p>A new study quantifies what many in the industry have reported anecdotally. According to CrunchBase <a href="http://techcrunch.com/2016/04/19/the-first-comprehensive-study-on-women-in-venture-capital/" target="_blank">data</a> released Tuesday on TechCrunch, 7 percent of investing partners at the world&#8217;s top 100 VC firms are women.</p>
<p>That&#8217;s 53 women out of a total of 764 investing partners. The study found 38 of the top 100 firms have at least one female partner &#8212; 28 have one, while seven have two. CrunchBase also ranks the top VC firms in terms of their number of female partners. Sequoia Capital has two &#8212; both based in China. Canaan Partners, on Sand Hill Road, has three.</p>
<p>Only three firms were co-founded by women &#8212; Foster City-based Scale Venture Partners, New York and Los Angeles-based Greycroft Partners, and Palo Alto-based Floodgate. At Scale, two of the firm&#8217;s five partners are women, and at Greycroft, two of the firm&#8217;s six partners are women.</p>
<p>Scale co-founder and partner Kate Mitchell discussed the findings during a panel in San Francisco on Tuesday sponsored by the U.S. Small Business Administration and the National Venture Capital Association. Mitchell told the audience (which was overwhelmingly female) about succeeding in a male-dominated world.</p>
<p>&#8220;I really tried not to stand out as a woman. That really was not an asset,&#8221; she said. But Mitchell said that at a certain point, she decided it was time to stop wearing blue suits and ties and &#8220;come out as a woman.&#8221;</p>
<p>Gene Teare, head of research at CrunchBase, said the number of women in positions of power at VC firms needs to improve. With 28 of the top 100 firms  having just one female investing partner, if that one woman leaves, they&#8217;re back to zero, she said.</p>
<p>&#8220;Women have a toe-hold in this industry,&#8221; Teare said. &#8220;These numbers are not strong enough.&#8221;</p>
<p><em>Photo: Panelists discuss gender diversity in venture capital Tuesday at &#8220;Bridging the Gender Gap: Entrepreneurship, Women, and Investing&#8221; event. From left to right: Gene Teare, head of research at CrunchBase; Kate Mitchell, co-founder and partner at Scale Venture Partners; Administrator Maria Contreras-Sweet of the U.S. Small Business Administration; and Laurie Lumenti Garty, SVP of startup services for Square 1 Bank. (Marisa Kendall/ Mercury News). </em></p>
<!-- AddThis Sharing Buttons below --><div class='at-below-post addthis_default_style addthis_toolbox at-wordpress-hide' data-title='VC: Time to &#8216;come out as a woman&#8217;' data-url='http://www.siliconbeat.com/2016/04/19/time-come-woman/'></div><!-- AddThis Recommended Content below --> <div class='at-below-post-recommended addthis_default_style addthis_toolbox at-wordpress-hide'></div><script type="text/javascript"> jQuery.ajax({
              url: "http://www.siliconbeat.com/wp-content/plugins/top-10/includes/top-10-addcount.js.php",
              data: {
                top_ten_id: 105729,
                top_ten_blog_id: 1,
                activate_counter: 10,
                top10_rnd: (new Date()).getTime() + "-" + Math.floor(Math.random() * 100000)
              }
            }); </script><div class="tptn_counter" id="tptn_counter_105729"><script type="text/javascript" data-cfasync="false" src="http://www.siliconbeat.com/?top_ten_id=105729&amp;view_counter=1&amp;_wpnonce=ebecd24b4c"></script></div><script data-cfasync="false" type="text/javascript"></script>
                <!-- AddThis Settings Begin -->
                <script data-cfasync="false" type="text/javascript">
                    var addthis_product = "wpp-5.3.2";
                    var wp_product_version = "wpp-5.3.2";
                    var wp_blog_version = "4.5.2";
                    var addthis_plugin_info = {"info_status":"enabled","cms_name":"WordPress","cms_version":"4.5.2","plugin_name":"Share Buttons by AddThis","plugin_version":"5.3.2","anonymous_profile_id":"wp-43c5979041ef7276b0c8b5091905268b","plugin_mode":"AddThis","select_prefs":{"addthis_per_post_enabled":true,"addthis_above_enabled":false,"addthis_below_enabled":false,"addthis_sidebar_enabled":false,"addthis_mobile_toolbar_enabled":false,"addthis_above_showon_home":true,"addthis_above_showon_posts":true,"addthis_above_showon_pages":true,"addthis_above_showon_archives":true,"addthis_above_showon_categories":true,"addthis_above_showon_excerpts":true,"addthis_below_showon_home":true,"addthis_below_showon_posts":true,"addthis_below_showon_pages":true,"addthis_below_showon_archives":true,"addthis_below_showon_categories":true,"addthis_below_showon_excerpts":true,"addthis_sidebar_showon_home":true,"addthis_sidebar_showon_posts":true,"addthis_sidebar_showon_pages":true,"addthis_sidebar_showon_archives":true,"addthis_sidebar_showon_categories":true,"addthis_mobile_toolbar_showon_home":true,"addthis_mobile_toolbar_showon_posts":true,"addthis_mobile_toolbar_showon_pages":true,"addthis_mobile_toolbar_showon_archives":true,"addthis_mobile_toolbar_showon_categories":true,"sharing_enabled_on_post_via_metabox":true},"page_info":{"template":"posts","post_type":""}};
                    if (typeof(addthis_config) == "undefined") {
                        var addthis_config = {"data_track_clickback":true,"ui_atversion":300};
                    }
                    if (typeof(addthis_share) == "undefined") {
                        var addthis_share = {};
                    }
                    if (typeof(addthis_layers) == "undefined") {
                        var addthis_layers = {};
                    }
                </script>
                <script
                    data-cfasync="false"
                    type="text/javascript"
                    src="//s7.addthis.com/js/300/addthis_widget.js#pubid=ra-54dbd9264f5a3b33 "
                    async="async"
                >
                </script>
                <script data-cfasync="false" type="text/javascript">
                    (function() {
                        var at_interval = setInterval(function () {
                            if(window.addthis) {
                                clearInterval(at_interval);
                                addthis.layers(addthis_layers);
                            }
                        },1000)
                    }());
                </script>
                <div class="sharedaddy sd-sharing-enabled"><div class="robots-nocontent sd-block sd-social sd-social-icon-text sd-sharing"><h3 class="sd-title">Share this:</h3><div class="sd-content"><ul><li class="share-email"><a rel="nofollow" data-shared="" class="share-email sd-button share-icon" href="http://www.siliconbeat.com/2016/04/19/time-come-woman/?share=email" target="_blank" title="Click to email this to a friend"><span>Email</span></a></li><li class="share-twitter"><a rel="nofollow" data-shared="sharing-twitter-105729" class="share-twitter sd-button share-icon" href="http://www.siliconbeat.com/2016/04/19/time-come-woman/?share=twitter" target="_blank" title="Click to share on Twitter"><span>Twitter</span></a></li><li class="share-facebook"><a rel="nofollow" data-shared="sharing-facebook-105729" class="share-facebook sd-button share-icon" href="http://www.siliconbeat.com/2016/04/19/time-come-woman/?share=facebook" target="_blank" title="Click to share on Facebook"><span>Facebook</span></a></li><li class="share-linkedin"><a rel="nofollow" data-shared="sharing-linkedin-105729" class="share-linkedin sd-button share-icon" href="http://www.siliconbeat.com/2016/04/19/time-come-woman/?share=linkedin" target="_blank" title="Click to share on LinkedIn"><span>LinkedIn</span></a></li><li class="share-reddit"><a rel="nofollow" data-shared="" class="share-reddit sd-button share-icon" href="http://www.siliconbeat.com/2016/04/19/time-come-woman/?share=reddit" target="_blank" title="Click to share on Reddit"><span>Reddit</span></a></li><li class="share-google-plus-1"><a rel="nofollow" data-shared="sharing-google-105729" class="share-google-plus-1 sd-button share-icon" href="http://www.siliconbeat.com/2016/04/19/time-come-woman/?share=google-plus-1" target="_blank" title="Click to share on Google+"><span>Google</span></a></li><li><a href="#" class="sharing-anchor sd-button share-more"><span>More</span></a></li><li class="share-end"></li></ul><div class="sharing-hidden"><div class="inner" style="display: none;"><ul><li class="share-pinterest"><a rel="nofollow" data-shared="sharing-pinterest-105729" class="share-pinterest sd-button share-icon" href="http://www.siliconbeat.com/2016/04/19/time-come-woman/?share=pinterest" target="_blank" title="Click to share on Pinterest"><span>Pinterest</span></a></li><li class="share-tumblr"><a rel="nofollow" data-shared="" class="share-tumblr sd-button share-icon" href="http://www.siliconbeat.com/2016/04/19/time-come-woman/?share=tumblr" target="_blank" title="Click to share on Tumblr"><span>Tumblr</span></a></li><li class="share-end"></li><li class="share-end"></li></ul></div></div></div></div></div>
        <div class="cleaner">&nbsp;</div>

                <p><strong>Tags:</strong> <a href="http://www.siliconbeat.com/tag/gender-diversity/" rel="tag">gender diversity</a>, <a href="http://www.siliconbeat.com/tag/vc/" rel="tag">VC</a>, <a href="http://www.siliconbeat.com/tag/venture-capital/" rel="tag">venture capital</a></p>
      </div><!-- end .post-content -->

      <div class="divider">&nbsp;</div>

<!-- BEGIN OUTBRAIN -->
<div class="OUTBRAIN" data-src="http://www.siliconbeat.com/2016/04/19/time-come-woman/" data-widget-id="AR_1" data-ob-template="BANG"></div>
<div class="OUTBRAIN" data-src="http://www.siliconbeat.com/2016/04/19/time-come-woman/" data-widget-id="AR_2" data-ob-template="BANG"></div>
<script type="text/javascript" async="async" src="http://widgets.outbrain.com/outbrain.js"></script>
<!-- END OUTBRAIN -->

      <div class="post-actions">

        <div class="column">


<p class="title">More Posts in Silicon Beat</p>

  <ul class="posts posts-related">


    <li id="post-107703" class="post-grid">
      <article>

        <div class="cover"><a href="http://www.siliconbeat.com/2016/05/26/study/" title="Study: 62% of U.S. adults get news on social media"><img src="http://www.siliconbeat.com/wp-content/uploads/2016/04/facebook-30x30.jpg" alt="(FILES) A view of and Apple iPhone displaying the Facebook app&#039;s splash screen in front of the login page May 10, 2012 in Washington, DC. Facebook will become part of the Nasdaq 100 index of the largest non-financial companies listed on the electronic exchange, the market operator said December 5, 2012. Facebook will join the index on December 12, a statement from Nasdaq said. It will replace the IT firm Infosys, which is moving to the New York Stock Exchange. AFP PHOTO / Karen BLEIER /FILESKAREN BLEIER/AFP/Getty Images" class="Thumbnail thumbnail homepage-slider-thumbs " width="30" height="30" /></a></div>
        <div class="post-excerpt">
          <h2><a href="http://www.siliconbeat.com/2016/05/26/study/" title="Permalink to Study: 62% of U.S. adults get news on social media" rel="bookmark">Study: 62% of U.S. adults get news on social media</a></h2>
          <time datetime="2016-05-26" pubdate>May 26, 2016</time>       </div>
        <div class="cleaner">&nbsp;</div>
      </article>
    </li><!-- end #post-107703 -->


    <li id="post-107695" class="post-grid">
      <article>

        <div class="cover"><a href="http://www.siliconbeat.com/2016/05/26/overnight-airbnb-procrastinators-comes-bay-area/" title="Overnight, the Airbnb for procrastinators, comes to Bay Area"><img src="http://www.siliconbeat.com/wp-content/uploads/2016/05/Overnight2-30x30.jpg" alt="Overnight, the Airbnb for procrastinators, comes to Bay Area" class="Thumbnail thumbnail homepage-slider-thumbs " width="30" height="30" /></a></div>
        <div class="post-excerpt">
          <h2><a href="http://www.siliconbeat.com/2016/05/26/overnight-airbnb-procrastinators-comes-bay-area/" title="Permalink to Overnight, the Airbnb for procrastinators, comes to Bay Area" rel="bookmark">Overnight, the Airbnb for procrastinators, comes to Bay Area</a></h2>
          <time datetime="2016-05-26" pubdate>May 26, 2016</time>       </div>
        <div class="cleaner">&nbsp;</div>
      </article>
    </li><!-- end #post-107695 -->


    <li id="post-107694" class="post-grid post-last">
      <article>

        <div class="cover"><a href="http://www.siliconbeat.com/2016/05/26/topic-tech-news-know-now-4/" title="On Topic: Tech news to know now"><img src="http://www.siliconbeat.com/wp-content/uploads/2015/09/apple-logo-bang-30x30.jpg" alt="On Topic: Tech news to know now" class="Thumbnail thumbnail homepage-slider-thumbs " width="30" height="30" /></a></div>
        <div class="post-excerpt">
          <h2><a href="http://www.siliconbeat.com/2016/05/26/topic-tech-news-know-now-4/" title="Permalink to On Topic: Tech news to know now" rel="bookmark">On Topic: Tech news to know now</a></h2>
          <time datetime="2016-05-26" pubdate>May 26, 2016</time>       </div>
        <div class="cleaner">&nbsp;</div>
      </article>
    </li><!-- end #post-107694 -->


  </ul><!-- end .posts -->

  <div class="cleaner">&nbsp;</div>


          <div class="cleaner">&nbsp;</div>
        </div><!-- end .column -->

        <div class="column column-last">
                    <p class="title">Share this Post</p>

            <span class="share_btn"><a href="http://twitter.com/share" data-url="http://www.siliconbeat.com/2016/04/19/time-come-woman/" class="twitter-share-button" data-count="horizontal">Tweet</a><script type="text/javascript" src="http://platform.twitter.com/widgets.js"></script></span><br />
            <span class="share_btn"><iframe src="http://www.facebook.com/plugins/like.php?href=http%3A%2F%2Fwww.siliconbeat.com%2F2016%2F04%2F19%2Ftime-come-woman%2F&amp;layout=button_count&amp;show_faces=false&amp;width=80&amp;action=like&amp;font=arial&amp;colorscheme=light&amp;height=21" scrolling="no" frameborder="0" style="border:none; overflow:hidden; width:80px; height:21px;" allowTransparency="true"></iframe></span><br />
            <span class="share_btn"><g:plusone size="medium"></g:plusone></span>


          <div class="cleaner">&nbsp;</div>
        </div><!-- end .column -->
        <div class="cleaner">&nbsp;</div>

      </div><!-- end .post-actions -->

            <div class="divider">&nbsp;</div>

      <div id="comments">

<div id="disqus_thread">
    </div>

<script type="text/javascript">
var disqus_url = 'http://www.siliconbeat.com/2016/04/19/time-come-woman/';
var disqus_identifier = '105729 http://www.siliconbeat.com/?p=105729';
var disqus_container_id = 'disqus_thread';
var disqus_shortname = 'dfm-sv-siliconbeat';
var disqus_title = "VC: Time to &#8216;come out as a woman&#8217;";
var disqus_config_custom = window.disqus_config;
var disqus_config = function () {
    /*
    All currently supported events:
    onReady: fires when everything is ready,
    onNewComment: fires when a new comment is posted,
    onIdentify: fires when user is authenticated
    */


    this.language = '';
        this.callbacks.onReady.push(function () {

        // sync comments in the background so we don't block the page
        var script = document.createElement('script');
        script.async = true;
        script.src = '?cf_action=sync_comments&post_id=105729';

        var firstScript = document.getElementsByTagName('script')[0];
        firstScript.parentNode.insertBefore(script, firstScript);
    });

    if (disqus_config_custom) {
        disqus_config_custom.call(this);
    }
};

(function() {
    var dsq = document.createElement('script'); dsq.type = 'text/javascript';
    dsq.async = true;
    dsq.src = '//' + disqus_shortname + '.disqus.com/embed.js';
    (document.getElementsByTagName('head')[0] || document.getElementsByTagName('body')[0]).appendChild(dsq);
})();
</script>


      </div><!-- end #comments -->



      <div class="cleaner">&nbsp;</div>

    </div><!-- end .wrapper-content -->

  </div><!-- end #content -->

    <aside>



<div class="widget widget_search" id="search-2"><form role="search" method="get" id="searchform" action="http://www.siliconbeat.com/" >
    <div>
    <input type="text" value="" name="s" id="s" />
    <input type="submit" id="searchsubmit" value="Search" />
    </div>
    </form><div class="cleaner">&nbsp;</div></div><div class="widget widget_nav_menu" id="nav_menu-2"><div class="menu-navigation-line-2-container"><ul id="menu-navigation-line-2" class="menu"><li id="menu-item-11865" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-11865"><a href="http://www.siliconbeat.com/about-siliconbeat/">About SiliconBeat</a></li>
<li id="menu-item-37728" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-37728"><a href="http://direct.mercurynews.com/mrc/mrcreg.action">Sign up for the Good Morning Silicon Valley newsletter</a></li>
<li id="menu-item-37821" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-37821"><a href="http://www.siliconbeat.com/category/gmsv/">GMSV</a></li>
</ul></div><div class="cleaner">&nbsp;</div></div><div class="widget widget_text" id="text-5">      <div class="textwidget"><!-- begin blog_MN-Business 300x250 Cube1 Ad Tag -->

<center>

<script type='text/javascript'>
(function() {
var useSSL = 'https:' == document.location.protocol;
var src = (useSSL ? 'https:' : 'http:') +
'//www.googletagservices.com/tag/js/gpt.js';
document.write('<scr ' + 'ipt src="' + src + '"></scr>');
})();
</script>
<div id='div-gpt-ad-1364842121944-2'>
<script type='text/javascript'>
googletag.defineSlot('/8013/mercurynews.com/Business', [300,250], 'div-gpt-ad-1364842121944-2').setTargeting('pos', 'Cube1_RRail_ATF').setTargeting('kv', 'business').addService

(googletag.pubads());
googletag.pubads().enableSyncRendering();
googletag.enableServices();
googletag.display('div-gpt-ad-1364842121944-2');
</script>
</div>

</center>
<!-- end 300x250 Cube 1 Ad Tag -->
</div>
    <div class="cleaner">&nbsp;</div></div><div class="widget widget_text" id="text-2">     <div class="textwidget"><center><div style="margin: 0 auto;"><a class="twitter-timeline" href="https://twitter.com/siliconbeat/siliconbeat" data-widget-id="267060201625096192">Tweets from @siliconbeat/siliconbeat</a>
<script>!function(d,s,id){var js,fjs=d.getElementsByTagName(s)[0];if(!d.getElementById(id)){js=d.createElement(s);js.id=id;js.src="//platform.twitter.com/widgets.js";fjs.parentNode.insertBefore(js,fjs);}}(document,"script","twitter-wjs");</script>
</div>
</center></div>
    <div class="cleaner">&nbsp;</div></div><div class="widget widget_widget_tptn_pop" id="widget_tptn_pop-3"><p class="title">Popular Posts</p><div class="tptn_posts_daily  tptn_posts_widget tptn_posts_widget3"><ul><li><span class="tptn_after_thumb"><a href="http://www.siliconbeat.com/2016/05/24/yahoo-security-costs-ceo-marissa-mayer-rose-faced-threats/"     class="tptn_link"><span class="tptn_title">Yahoo security spending for CEO Marissa Mayer rose as she&hellip;</span></a></span></li><li><span class="tptn_after_thumb"><a href="http://www.siliconbeat.com/2016/05/25/whos-richest-california-mark-zuckerberg-larry-ellison/"     class="tptn_link"><span class="tptn_title">Who&#8217;s richest in California, Mark Zuckerberg or Larry&hellip;</span></a></span></li><li><span class="tptn_after_thumb"><a href="http://www.siliconbeat.com/2016/05/25/apples-taking-another-step-internet-things/"     class="tptn_link"><span class="tptn_title">Report: Apple working on rival to Amazon Echo</span></a></span></li><li><span class="tptn_after_thumb"><a href="http://www.siliconbeat.com/2016/05/24/biz-break-gopro-gets-wings-thanks-red-bull/"     class="tptn_link"><span class="tptn_title">Biz Break: GoPro gets some wings thanks to Red Bull</span></a></span></li><li><span class="tptn_after_thumb"><a href="http://www.siliconbeat.com/2016/05/20/apple-microsoft-google-cisco-oracle-hold-cash-moodys-report/"     class="tptn_link"><span class="tptn_title">Apple, Microsoft, Google, Cisco, Oracle hold the most cash:&hellip;</span></a></span></li></ul><div class="tptn_clear"></div></div><div class="cleaner">&nbsp;</div></div><div class="widget widget_text" id="text-3">      <div class="textwidget"><!-- begin blog_MN-Business 300x250 Cube4 Ad Tag -->

<center>

<script type='text/javascript'>
(function() {
var useSSL = 'https:' == document.location.protocol;
var src = (useSSL ? 'https:' : 'http:') +
'//www.googletagservices.com/tag/js/gpt.js';
document.write('<scr ' + 'ipt src="' + src + '"></scr>');
})();
</script>
<div id='div-gpt-ad-1364842121944-4'>
<script type='text/javascript'>
googletag.defineSlot('/8013/mercurynews.com/Business', [300,250], 'div-gpt-ad-1364842121944-4').setTargeting('pos', 'Cube4_BottomLine').setTargeting('kv', 'business').addService

(googletag.pubads());
googletag.pubads().enableSyncRendering();
googletag.enableServices();
googletag.display('div-gpt-ad-1364842121944-4');
</script>
</div>

</center>
<!-- end 300x250 Cube 4 Ad Tag -->
</div>
    <div class="cleaner">&nbsp;</div></div><div class="widget widget_archive" id="archives-6"><p class="title">Archives</p>   <label class="screen-reader-text" for="archives-dropdown-6">Archives</label>
    <select id="archives-dropdown-6" name="archive-dropdown" onchange='document.location.href=this.options[this.selectedIndex].value;'>

      <option value="">Select Month</option>
        <option value='http://www.siliconbeat.com/2016/05/'> May 2016 </option>
  <option value='http://www.siliconbeat.com/2016/04/'> April 2016 </option>
  <option value='http://www.siliconbeat.com/2016/03/'> March 2016 </option>
  <option value='http://www.siliconbeat.com/2016/02/'> February 2016 </option>
  <option value='http://www.siliconbeat.com/2016/01/'> January 2016 </option>
  <option value='http://www.siliconbeat.com/2015/12/'> December 2015 </option>
  <option value='http://www.siliconbeat.com/2015/11/'> November 2015 </option>
  <option value='http://www.siliconbeat.com/2015/10/'> October 2015 </option>
  <option value='http://www.siliconbeat.com/2015/09/'> September 2015 </option>
  <option value='http://www.siliconbeat.com/2015/08/'> August 2015 </option>
  <option value='http://www.siliconbeat.com/2015/07/'> July 2015 </option>
  <option value='http://www.siliconbeat.com/2015/06/'> June 2015 </option>
  <option value='http://www.siliconbeat.com/2015/05/'> May 2015 </option>
  <option value='http://www.siliconbeat.com/2015/04/'> April 2015 </option>
  <option value='http://www.siliconbeat.com/2015/03/'> March 2015 </option>
  <option value='http://www.siliconbeat.com/2015/02/'> February 2015 </option>
  <option value='http://www.siliconbeat.com/2015/01/'> January 2015 </option>
  <option value='http://www.siliconbeat.com/2014/12/'> December 2014 </option>
  <option value='http://www.siliconbeat.com/2014/11/'> November 2014 </option>
  <option value='http://www.siliconbeat.com/2014/10/'> October 2014 </option>
  <option value='http://www.siliconbeat.com/2014/09/'> September 2014 </option>
  <option value='http://www.siliconbeat.com/2014/08/'> August 2014 </option>
  <option value='http://www.siliconbeat.com/2014/07/'> July 2014 </option>
  <option value='http://www.siliconbeat.com/2014/06/'> June 2014 </option>
  <option value='http://www.siliconbeat.com/2014/05/'> May 2014 </option>
  <option value='http://www.siliconbeat.com/2014/04/'> April 2014 </option>
  <option value='http://www.siliconbeat.com/2014/03/'> March 2014 </option>
  <option value='http://www.siliconbeat.com/2014/02/'> February 2014 </option>
  <option value='http://www.siliconbeat.com/2014/01/'> January 2014 </option>
  <option value='http://www.siliconbeat.com/2013/12/'> December 2013 </option>
  <option value='http://www.siliconbeat.com/2013/11/'> November 2013 </option>
  <option value='http://www.siliconbeat.com/2013/10/'> October 2013 </option>
  <option value='http://www.siliconbeat.com/2013/09/'> September 2013 </option>
  <option value='http://www.siliconbeat.com/2013/08/'> August 2013 </option>
  <option value='http://www.siliconbeat.com/2013/07/'> July 2013 </option>
  <option value='http://www.siliconbeat.com/2013/06/'> June 2013 </option>
  <option value='http://www.siliconbeat.com/2013/05/'> May 2013 </option>
  <option value='http://www.siliconbeat.com/2013/04/'> April 2013 </option>
  <option value='http://www.siliconbeat.com/2013/03/'> March 2013 </option>
  <option value='http://www.siliconbeat.com/2013/02/'> February 2013 </option>
  <option value='http://www.siliconbeat.com/2013/01/'> January 2013 </option>
  <option value='http://www.siliconbeat.com/2012/12/'> December 2012 </option>
  <option value='http://www.siliconbeat.com/2012/11/'> November 2012 </option>
  <option value='http://www.siliconbeat.com/2012/10/'> October 2012 </option>
  <option value='http://www.siliconbeat.com/2012/09/'> September 2012 </option>
  <option value='http://www.siliconbeat.com/2012/08/'> August 2012 </option>
  <option value='http://www.siliconbeat.com/2012/07/'> July 2012 </option>
  <option value='http://www.siliconbeat.com/2012/06/'> June 2012 </option>
  <option value='http://www.siliconbeat.com/2012/05/'> May 2012 </option>
  <option value='http://www.siliconbeat.com/2012/04/'> April 2012 </option>
  <option value='http://www.siliconbeat.com/2012/03/'> March 2012 </option>
  <option value='http://www.siliconbeat.com/2012/02/'> February 2012 </option>
  <option value='http://www.siliconbeat.com/2012/01/'> January 2012 </option>
  <option value='http://www.siliconbeat.com/2011/12/'> December 2011 </option>
  <option value='http://www.siliconbeat.com/2011/11/'> November 2011 </option>
  <option value='http://www.siliconbeat.com/2011/10/'> October 2011 </option>
  <option value='http://www.siliconbeat.com/2011/09/'> September 2011 </option>
  <option value='http://www.siliconbeat.com/2011/08/'> August 2011 </option>
  <option value='http://www.siliconbeat.com/2011/07/'> July 2011 </option>
  <option value='http://www.siliconbeat.com/2011/06/'> June 2011 </option>
  <option value='http://www.siliconbeat.com/2011/05/'> May 2011 </option>
  <option value='http://www.siliconbeat.com/2011/04/'> April 2011 </option>
  <option value='http://www.siliconbeat.com/2011/03/'> March 2011 </option>
  <option value='http://www.siliconbeat.com/2011/02/'> February 2011 </option>
  <option value='http://www.siliconbeat.com/2011/01/'> January 2011 </option>
  <option value='http://www.siliconbeat.com/2010/12/'> December 2010 </option>
  <option value='http://www.siliconbeat.com/2010/11/'> November 2010 </option>
  <option value='http://www.siliconbeat.com/2010/10/'> October 2010 </option>
  <option value='http://www.siliconbeat.com/2010/09/'> September 2010 </option>
  <option value='http://www.siliconbeat.com/2010/08/'> August 2010 </option>
  <option value='http://www.siliconbeat.com/2010/07/'> July 2010 </option>
  <option value='http://www.siliconbeat.com/2010/06/'> June 2010 </option>
  <option value='http://www.siliconbeat.com/2010/05/'> May 2010 </option>
  <option value='http://www.siliconbeat.com/2010/04/'> April 2010 </option>
  <option value='http://www.siliconbeat.com/2010/03/'> March 2010 </option>
  <option value='http://www.siliconbeat.com/2010/02/'> February 2010 </option>
  <option value='http://www.siliconbeat.com/2010/01/'> January 2010 </option>
  <option value='http://www.siliconbeat.com/2009/12/'> December 2009 </option>
  <option value='http://www.siliconbeat.com/2009/11/'> November 2009 </option>
  <option value='http://www.siliconbeat.com/2009/10/'> October 2009 </option>
  <option value='http://www.siliconbeat.com/2009/09/'> September 2009 </option>
  <option value='http://www.siliconbeat.com/2009/08/'> August 2009 </option>
  <option value='http://www.siliconbeat.com/2009/07/'> July 2009 </option>
  <option value='http://www.siliconbeat.com/2009/06/'> June 2009 </option>
  <option value='http://www.siliconbeat.com/2009/05/'> May 2009 </option>
  <option value='http://www.siliconbeat.com/2009/04/'> April 2009 </option>
  <option value='http://www.siliconbeat.com/2009/03/'> March 2009 </option>
  <option value='http://www.siliconbeat.com/2009/02/'> February 2009 </option>
  <option value='http://www.siliconbeat.com/2009/01/'> January 2009 </option>
  <option value='http://www.siliconbeat.com/2008/12/'> December 2008 </option>
  <option value='http://www.siliconbeat.com/2008/11/'> November 2008 </option>
  <option value='http://www.siliconbeat.com/2008/10/'> October 2008 </option>
  <option value='http://www.siliconbeat.com/2008/09/'> September 2008 </option>
  <option value='http://www.siliconbeat.com/2008/08/'> August 2008 </option>
  <option value='http://www.siliconbeat.com/2008/07/'> July 2008 </option>
  <option value='http://www.siliconbeat.com/2008/06/'> June 2008 </option>
  <option value='http://www.siliconbeat.com/2008/05/'> May 2008 </option>
  <option value='http://www.siliconbeat.com/2008/04/'> April 2008 </option>
  <option value='http://www.siliconbeat.com/2008/03/'> March 2008 </option>
  <option value='http://www.siliconbeat.com/2008/02/'> February 2008 </option>
  <option value='http://www.siliconbeat.com/2008/01/'> January 2008 </option>
  <option value='http://www.siliconbeat.com/2007/12/'> December 2007 </option>
  <option value='http://www.siliconbeat.com/2007/11/'> November 2007 </option>
  <option value='http://www.siliconbeat.com/2007/10/'> October 2007 </option>
  <option value='http://www.siliconbeat.com/2007/09/'> September 2007 </option>
  <option value='http://www.siliconbeat.com/2007/08/'> August 2007 </option>
  <option value='http://www.siliconbeat.com/2007/07/'> July 2007 </option>
  <option value='http://www.siliconbeat.com/2007/06/'> June 2007 </option>
  <option value='http://www.siliconbeat.com/2007/05/'> May 2007 </option>
  <option value='http://www.siliconbeat.com/2007/04/'> April 2007 </option>
  <option value='http://www.siliconbeat.com/2007/03/'> March 2007 </option>
  <option value='http://www.siliconbeat.com/2007/02/'> February 2007 </option>
  <option value='http://www.siliconbeat.com/2007/01/'> January 2007 </option>
  <option value='http://www.siliconbeat.com/2006/12/'> December 2006 </option>
  <option value='http://www.siliconbeat.com/2006/11/'> November 2006 </option>
  <option value='http://www.siliconbeat.com/2006/10/'> October 2006 </option>
  <option value='http://www.siliconbeat.com/2006/09/'> September 2006 </option>
  <option value='http://www.siliconbeat.com/2006/08/'> August 2006 </option>
  <option value='http://www.siliconbeat.com/2006/07/'> July 2006 </option>
  <option value='http://www.siliconbeat.com/2006/06/'> June 2006 </option>
  <option value='http://www.siliconbeat.com/2006/05/'> May 2006 </option>
  <option value='http://www.siliconbeat.com/2006/04/'> April 2006 </option>
  <option value='http://www.siliconbeat.com/2006/03/'> March 2006 </option>
  <option value='http://www.siliconbeat.com/2006/02/'> February 2006 </option>
  <option value='http://www.siliconbeat.com/2006/01/'> January 2006 </option>
  <option value='http://www.siliconbeat.com/2005/12/'> December 2005 </option>
  <option value='http://www.siliconbeat.com/2005/11/'> November 2005 </option>
  <option value='http://www.siliconbeat.com/2005/10/'> October 2005 </option>
  <option value='http://www.siliconbeat.com/2005/09/'> September 2005 </option>
  <option value='http://www.siliconbeat.com/2005/08/'> August 2005 </option>
  <option value='http://www.siliconbeat.com/2005/07/'> July 2005 </option>
  <option value='http://www.siliconbeat.com/2005/06/'> June 2005 </option>
  <option value='http://www.siliconbeat.com/2005/05/'> May 2005 </option>
  <option value='http://www.siliconbeat.com/2005/04/'> April 2005 </option>
  <option value='http://www.siliconbeat.com/2005/03/'> March 2005 </option>
  <option value='http://www.siliconbeat.com/2005/02/'> February 2005 </option>

    </select>
    <div class="cleaner">&nbsp;</div></div><div class="widget widget_links" id="linkcat-1704"><p class="title">Related Feeds</p>
  <ul class='xoxo blogroll'>
<li><a href="http://feeds.mercurynews.com/mngi/rss/CustomRssServlet/568/200792.xml">60-Second Business Break</a></li>
<li><a href="http://feeds.siliconvalley.com/mngi/rss/CustomRssServlet/573/283802.xml">Green Energy</a></li>
<li><a href="http://feeds.mercurynews.com/mngi/rss/CustomRssServlet/568/200125.xml">News</a></li>
<li><a href="http://feeds.mercurynews.com/mngi/rss/CustomRssServlet/568/200224.xml">Opinion</a></li>
<li><a href="http://feeds.siliconvalley.com/mngi/rss/CustomRssServlet/573/206305.xml">Personal Tech</a></li>
<li><a href="http://feeds.mercurynews.com/mngi/rss/CustomRssServlet/568/200774.xml">Venture Capital</a></li>

  </ul>
<div class="cleaner">&nbsp;</div></div>


    <div class="cleaner">&nbsp;</div>

    <div id="aside-fade">&nbsp;</div><!-- end #aside-fade -->
  </aside>

  <div class="cleaner">&nbsp;</div>

</div><!-- end #main -->

  </div><!-- end .wrapper .wrapper-main -->

  <footer>

    <div class="wrapper">

      <div class="column column-wide">
                <div class="cleaner">&nbsp;</div>
      </div><!-- end .column -->

      <div class="column">
                <div class="cleaner">&nbsp;</div>
      </div><!-- end .column -->

      <div class="column">
                <div class="cleaner">&nbsp;</div>
      </div><!-- end .column -->

      <div class="column">
                <div class="cleaner">&nbsp;</div>
      </div><!-- end .column -->

      <div class="column column-last">
                <div class="cleaner">&nbsp;</div>
      </div><!-- end .column -->

      <div class="cleaner">&nbsp;</div>
      <div class="divider">&nbsp;</div>

      <p class="wpzoom"><a href="http://www.wpzoom.com" target="_blank" title="WordPress Themes"><img src="http://www.siliconbeat.com/wp-content/themes/erudito/images/wpzoom.png" alt="WPZOOM" /></a> <a href="http://www.wpzoom.com" target="_blank">Education WordPress Theme</a> by</p>
      <p class="copyright">Copyright &copy; 2016 Bay Area News Group. All Rights Reserved</p>

      <div class="cleaner">&nbsp;</div>

    </div><!-- end .wrapper -->

  </footer>

</div><!-- end #container -->

<img alt='css.php' src="http://www.siliconbeat.com/wp-content/plugins/cookies-for-comments/css.php?k=0534349904d0442d9b52b4581bf66c5a&amp;o=i&amp;t=442196546" width='1' height='1' />        <script type="text/javascript">
        // <![CDATA[
        var disqus_shortname = 'dfm-sv-siliconbeat';
        (function () {
            var nodes = document.getElementsByTagName('span');
            for (var i = 0, url; i < nodes.length; i++) {
                if (nodes[i].className.indexOf('dsq-postid') != -1) {
                    nodes[i].parentNode.setAttribute('data-disqus-identifier', nodes[i].getAttribute('data-dsqidentifier'));
                    url = nodes[i].parentNode.href.split('#', 1);
                    if (url.length == 1) { url = url[0]; }
                    else { url = url[1]; }
                    nodes[i].parentNode.href = url + '#disqus_thread';
                }
            }
            var s = document.createElement('script');
            s.async = true;
            s.type = 'text/javascript';
            s.src = '//' + disqus_shortname + '.disqus.com/count.js';
            (document.getElementsByTagName('HEAD')[0] || document.getElementsByTagName('BODY')[0]).appendChild(s);
        }());
        // ]]>
        </script>
        <script src="/wp-content/themes/erudito-child/jquery.fitvids.js"></script>
<script>
  // for FitVids.js
  jQuery(".post-content").fitVids({ customSelector: "iframe[src^='//www.tout.com']"});
  // Custom selector and No-Double-Wrapping
  // jQuery(".post-content").fitVids({ customSelector: "iframe[src^='http://www.tout.com']"});
</script>

<div id="omniture">
 <script src="http://extras.mnginteractive.com/live/omniture/custom_scripts/omnicore-blogs.js" type="text/javascript"></script>
 <noscript>
  <img src="http://mngi.112.2O7.net/b/ss/nasora/1/H.17--NS/0" height="1" width="1" border="0" alt="" />
 </noscript>
</div>
<!-- Added by Responsive Menu Plugin for WordPress - http://responsive.menu -->

<div id="responsive-menu" >


        <div id="responsive-menu-title">



            Menu Title

        </div>


        <form action="http://www.siliconbeat.com" id="responsiveSearch" method="get" role="search">

            <input type="search" name="s" value="" placeholder="Search" id="responsiveSearchInput">
            <input type="submit" id="responsiveSearchSubmit" />

        </form>

   <div class="menu-navigation-line-1-container"><ul id="menu-navigation-line-4" class="responsive-menu"><li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-37722"><a href="http://www.siliconvalley.com">SiliconValley.com</a></li>
<li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-37723"><a href="https://twitter.com/siliconbeat">Twitter</a></li>
<li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-37724"><a href="http://www.facebook.com/pages/SiliconBeat/324955307584131">Facebook</a></li>
<li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-37725"><a href="https://plus.google.com/+Siliconbeat/posts">Google+</a></li>
<li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-37726"><a href="http://feeds.feedburner.com/SiliconbeatTech">RSS</a></li>
</ul></div>
</div><!-- Added by Responsive Menu Plugin for WordPress - http://responsive.menu -->



<div id="click-menu"
     class="
     overlay"
     role="button"
     aria-label="Responsive Menu Button"
     >




    <div class="threeLines " id="RM3Lines">
        <div class="line"></div>
        <div class="line"></div>
        <div class="line"></div>
    </div>



</div><script>var $RMjQuery = jQuery.noConflict();$RMjQuery( document ).ready( function(){var isOpen = false;$RMjQuery( document ).on( 'click', '#click-menu', function(){$RMjQuery( '#responsive-menu' ).css( 'height', $RMjQuery( document ).height() );!isOpen ? openRM() : closeRM()});function openRM(){var MenuWidth = $RMjQuery('#responsive-menu').width();$RMjQuery( '#responsive-menu' ).css( 'display', 'block' );$RMjQuery( '#responsive-menu' ).addClass( 'RMOpened' );$RMjQuery( '#click-menu' ).addClass( 'click-menu-active' );$RMjQuery( 'body' ).addClass( 'responsive-menu-open' );$RMjQuery( '#responsive-menu' ).stop().animate({left: "0"}, 500, 'linear', function(){$RMjQuery( '#responsive-menu' ).css( 'height', $RMjQuery( document ).height() );isOpen = true})}function closeRM(){$RMjQuery( '#responsive-menu' ).animate({left: -$RMjQuery( '#responsive-menu' ).width()}, 500, 'linear', function(){$RMjQuery( '#responsive-menu' ).css( 'display', 'none' );$RMjQuery( '#responsive-menu' ).removeClass( 'RMOpened' );$RMjQuery( '#click-menu' ).removeClass( 'click-menu-active' );$RMjQuery( 'body' ).removeClass( 'responsive-menu-open' );isOpen = false})}$RMjQuery( window ).resize( function(){$RMjQuery( '#responsive-menu' ).stop( true, true );$RMjQuery( '#responsive-menu' ).css( 'height', $RMjQuery( document ).height() );if( $RMjQuery( window ).width() > 800 ){if( $RMjQuery( '#responsive-menu' ).css( 'left' ) != -$RMjQuery( '#responsive-menu' ).width() ){closeRM()}}});$RMjQuery( '#responsive-menu ul ul' ).css( 'display', 'none' );$RMjQuery( '#responsive-menu .current_page_ancestor.menu-item-has-children' ).children( 'ul' ).css( 'display', 'block' );$RMjQuery( '#responsive-menu .current-menu-ancestor.menu-item-has-children' ).children( 'ul' ).css( 'display', 'block' );$RMjQuery( '#responsive-menu .current-menu-item.menu-item-has-children' ).children( 'ul' ).css( 'display', 'block' );$RMjQuery( '#responsive-menu .current_page_ancestor.page_item_has_children' ).children( 'ul' ).css( 'display', 'block' );$RMjQuery( '#responsive-menu .current-menu-ancestor.page_item_has_children' ).children( 'ul' ).css( 'display', 'block' );$RMjQuery( '#responsive-menu .current-menu-item.page_item_has_children' ).children( 'ul' ).css( 'display', 'block' );var clickLink = '<span class=\"appendLink rm-append-inactive\">▼</span>';var clickedLink = '<span class=\"appendLink rm-append-active\">▲</span>';$RMjQuery( '#responsive-menu .responsive-menu li' ).each( function(){if( $RMjQuery( this ).children( 'ul' ).length > 0 ){if( $RMjQuery( this ).find( '> ul' ).css( 'display' ) == 'none' ){$RMjQuery( this ).prepend( clickLink )}else{$RMjQuery( this ).prepend( clickedLink )}}});$RMjQuery( '.appendLink' ).on( 'click', function(){$RMjQuery( this ).nextAll( '#responsive-menu ul ul' ).slideToggle();$RMjQuery( this ).html( $RMjQuery( this ).hasClass( 'rm-append-active' ) ? '▼' : '▲' );$RMjQuery( this ).toggleClass( 'rm-append-active rm-append-inactive' );$RMjQuery( '#responsive-menu' ).css( 'height', $RMjQuery( document ).height() )});$RMjQuery( '.rm-click-disabled' ).on( 'click', function(){$RMjQuery( this ).nextAll( '#responsive-menu ul ul' ).slideToggle();$RMjQuery( this ).siblings( '.appendLink' ).html( $RMjQuery( this ).hasClass( 'rm-append-active' ) ? '▼' : '▲' );$RMjQuery( this ).toggleClass( 'rm-append-active rm-append-inactive' );$RMjQuery( '#responsive-menu' ).css( 'height', $RMjQuery( document ).height() )});$RMjQuery( '.rm-append-inactive' ).siblings( 'ul' ).css( 'display', 'none' )});</script>
<!-- START Parse.ly Include: Standard -->
<div id="parsely-root" style="display: none">
  <div id="parsely-cfg" data-parsely-site="mercurynews.com"></div>
</div>
<script data-cfasync="false">
(function(s, p, d) {
  var h=d.location.protocol, i=p+"-"+s,
      e=d.getElementById(i), r=d.getElementById(p+"-root"),
      u=h==="https:"?"d1z2jf7jlzjs58.cloudfront.net"
      :"static."+p+".com";
  if (e) return;
  e = d.createElement(s); e.id = i; e.async = true;
  e.setAttribute('data-cfasync', 'false'); e.src = h+"//"+u+"/p.js"; r.appendChild(e);
})("script", "parsely", document);
</script>
<!-- END Parse.ly Include: Standard -->
  <div style="display:none">
  </div>
<script>(function($){$(document).ready(function(){});})(jQuery);</script>
  <script type="text/javascript">
    window.WPCOM_sharing_counts = {"http:\/\/www.siliconbeat.com\/2016\/04\/19\/time-come-woman\/":105729};
  </script>
  <div id="sharing_email" style="display: none;">
    <form action="/2016/04/19/time-come-woman/" method="post">
      <label for="target_email">Send to Email Address</label>
      <input type="email" name="target_email" id="target_email" value="" />


        <label for="source_name">Your Name</label>
        <input type="text" name="source_name" id="source_name" value="" />

        <label for="source_email">Your Email Address</label>
        <input type="email" name="source_email" id="source_email" value="" />

            <input type="text" id="jetpack-source_f_name" name="source_f_name" class="input" value="" size="25" autocomplete="off" />
      <script> document.getElementById('jetpack-source_f_name').value = ''; </script>

      <img style="float: right; display: none" class="loading" src="http://www.siliconbeat.com/wp-content/plugins/jetpack/modules/sharedaddy/images/loading.gif" alt="loading" width="16" height="16" />
      <input type="submit" value="Send Email" class="sharing_send" />
      <a rel="nofollow" href="#cancel" class="sharing_cancel">Cancel</a>

      <div class="errors errors-1" style="display: none;">
        Post was not sent - check your email addresses!     </div>

      <div class="errors errors-2" style="display: none;">
        Email check failed, please try again      </div>

      <div class="errors errors-3" style="display: none;">
        Sorry, your blog cannot share posts by email.     </div>
    </form>
  </div>
    <script type="text/javascript">
      var windowOpen;
    jQuery(document).on( 'ready post-load', function(){
      jQuery( 'a.share-twitter' ).on( 'click', function() {
        if ( 'undefined' !== typeof windowOpen ){ // If there's another sharing window open, close it.
          windowOpen.close();
        }
        windowOpen = window.open( jQuery(this).attr( 'href' ), 'wpcomtwitter', 'menubar=1,resizable=1,width=600,height=350' );
        return false;
      });
    });
    </script>
        <script type="text/javascript">
      var windowOpen;
    jQuery(document).on( 'ready post-load', function(){
      jQuery( 'a.share-facebook' ).on( 'click', function() {
        if ( 'undefined' !== typeof windowOpen ){ // If there's another sharing window open, close it.
          windowOpen.close();
        }
        windowOpen = window.open( jQuery(this).attr( 'href' ), 'wpcomfacebook', 'menubar=1,resizable=1,width=600,height=400' );
        return false;
      });
    });
    </script>
        <script type="text/javascript">
      var windowOpen;
    jQuery(document).on( 'ready post-load', function(){
      jQuery( 'a.share-linkedin' ).on( 'click', function() {
        if ( 'undefined' !== typeof windowOpen ){ // If there's another sharing window open, close it.
          windowOpen.close();
        }
        windowOpen = window.open( jQuery(this).attr( 'href' ), 'wpcomlinkedin', 'menubar=1,resizable=1,width=580,height=450' );
        return false;
      });
    });
    </script>
        <script type="text/javascript">
      var windowOpen;
    jQuery(document).on( 'ready post-load', function(){
      jQuery( 'a.share-google-plus-1' ).on( 'click', function() {
        if ( 'undefined' !== typeof windowOpen ){ // If there's another sharing window open, close it.
          windowOpen.close();
        }
        windowOpen = window.open( jQuery(this).attr( 'href' ), 'wpcomgoogle-plus-1', 'menubar=1,resizable=1,width=480,height=550' );
        return false;
      });
    });
    </script>
            <script type="text/javascript">
      var windowOpen;
    jQuery(document).on( 'ready post-load', function(){
      jQuery( 'a.share-tumblr' ).on( 'click', function() {
        if ( 'undefined' !== typeof windowOpen ){ // If there's another sharing window open, close it.
          windowOpen.close();
        }
        windowOpen = window.open( jQuery(this).attr( 'href' ), 'wpcomtumblr', 'menubar=1,resizable=1,width=450,height=450' );
        return false;
      });
    });
    </script>
    <link rel='stylesheet' id='addthis_output-css'  href='http://www.siliconbeat.com/wp-content/plugins/addthis/css/output.css?ver=c2e8f7dcc3a52278d0c2e17868b455d8' type='text/css' media='all' />
<script type='text/javascript'>
/* <![CDATA[ */
var TB_labels = {"no_config":"No configuration settings found","twitter_logo":"Twitter Logo","kino":"Development by Kirill Novitchenko","refresh":"Refresh","no_sources":"Twitter sources to blend are not defined","no_global_config":"Cannot retrieve Tweet Blender configuration options","version_msg":"Powered by Tweet Blender plugin v{0} blending {1}","limit_msg":"You reached Twitter API connection limit","no_tweets_msg":"No tweets found for {0}","loading_msg":"Loading tweets...","time_past":"{0} {1} ago","time_future":"in {0} {1}","second":"second","seconds":"seconds","minute":"minute","minutes":"minutes","hour":"hour","hours":"hours","day":"day","days":"days","week":"week","weeks":"weeks","month":"month","months":"months","year":"year","years":"years","check_fail":"Check failed","limit_num":"Max is {0}\/hour","limit_left":"You have {0} left","from":"from","reply":"reply","follow":"follow","limit_reset":"Next reset","view_more":"view more"};
/* ]]> */
</script>
<script type='text/javascript' src='http://www.siliconbeat.com/wp-content/plugins/tweet-blender/js/lib.js?ver=c2e8f7dcc3a52278d0c2e17868b455d8'></script>
<script type='text/javascript' src='http://www.siliconbeat.com/wp-content/plugins/tweet-blender/js/main.js?ver=c2e8f7dcc3a52278d0c2e17868b455d8'></script>
<script type='text/javascript' src='http://www.siliconbeat.com/wp-content/plugins/slideshow/slideshow.js?ver=0.1'></script>
<script type='text/javascript' src='http://s0.wp.com/wp-content/js/devicepx-jetpack.js?ver=201621'></script>
<script type='text/javascript' src='http://www.siliconbeat.com/wp-includes/js/comment-reply.min.js?ver=c2e8f7dcc3a52278d0c2e17868b455d8'></script>
<script type='text/javascript' src='http://www.siliconbeat.com/wp-content/themes/erudito/js/jquery.flexslider-min.js?ver=c2e8f7dcc3a52278d0c2e17868b455d8'></script>
<script type='text/javascript' src='http://www.siliconbeat.com/wp-content/themes/erudito/js/dropdown.js?ver=c2e8f7dcc3a52278d0c2e17868b455d8'></script>
<script type='text/javascript' src='http://www.siliconbeat.com/wp-content/themes/erudito/functions/wpzoom/assets/js/galleria.js'></script>
<script type='text/javascript' src='http://www.siliconbeat.com/wp-content/themes/erudito/functions/wpzoom/assets/js/wzslider.js'></script>
<script type='text/javascript' src='http://www.siliconbeat.com/wp-includes/js/wp-embed.min.js?ver=c2e8f7dcc3a52278d0c2e17868b455d8'></script>
<script type='text/javascript'>
/* <![CDATA[ */
var sharing_js_options = {"lang":"en","counts":"1"};
/* ]]> */
</script>
<script type='text/javascript' src='http://www.siliconbeat.com/wp-content/plugins/jetpack/modules/sharedaddy/sharing.js?ver=4.0.2'></script>
<script type='text/javascript' src='http://stats.wp.com/e-201621.js' async defer></script>
<script type='text/javascript'>
  _stq = window._stq || [];
  _stq.push([ 'view', {v:'ext',j:'1:4.0.2',blog:'39179666',post:'105729',tz:'-7',srv:'www.siliconbeat.com'} ]);
  _stq.push([ 'clickTrackerInit', '39179666', '105729' ]);
</script>
<!--wp_footer--><script type="text/javascript" src="https://apis.google.com/js/plusone.js"></script></body>
</html>
<!-- Performance optimized by W3 Total Cache. Learn more: http://www.w3-edge.com/wordpress-plugins/

Page Caching using apc
Database Caching 29/125 queries in 0.135 seconds using apc
Object Caching 3017/3361 objects using apc

 Served from: www.siliconbeat.com @ 2016-05-26 12:07:29 by W3 Total Cache -->
