name: pull_request

on:
  push:
    branches:
      - master
  pull_request:
    branches:
      - master

jobs:
  matrix:
    if: github.ref != 'refs/heads/master'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
      - id: matrix
        run: echo "matrix=$(node .github/workflows/get-matrix.mjs)" >> $GITHUB_OUTPUT
      - run: echo ${{ steps.matrix.outputs.matrix }}
    outputs:
      matrix: ${{ steps.matrix.outputs.matrix }}
  test:
    needs: matrix
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix: ${{fromJson(needs.matrix.outputs.matrix)}}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: lts/*
      - name: Setup PNPM
        uses: pnpm/action-setup@v4
        with:
          version: latest
          run_install: true
      - name: Test
        run: pnpm --filter "${{ matrix.package }}" exec c8 pnpm test
      - name: Coverage
        run: pnpm --filter "${{ matrix.package }}" exec c8 report --reporter=lcov --report-dir=coverage
      - name: Upload
        uses: coverallsapp/github-action@main
        with:
          flag-name: ${{ matrix.package }}
          parallel: true
          file: $(pnpm --filter "${{ matrix.package }}" exec pwd)/coverage/lcov.info

  finish:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - name: Coveralls Finished
        uses: coverallsapp/github-action@main
        with:
          parallel-finished: true
