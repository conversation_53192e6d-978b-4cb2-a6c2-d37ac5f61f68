import { createClient } from '@supabase/supabase-js';
import { AIContentGenerator } from '../src/lib/ai/content-generator';
import { PromptManager } from '../src/lib/ai/prompt-manager';
import fs from 'fs';
import path from 'path';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);

// Define the complete expected schema based on AI Dude implementation
const EXPECTED_AI_FIELDS = {
  // Core identification fields
  name: 'string',
  description: 'string',
  short_description: 'string',
  detailed_description: 'string',
  
  // Company and categorization
  company: 'string',
  category_id: 'string', // mapped from category_primary
  subcategory: 'string', // mapped from category_secondary
  
  // Core content fields
  features: 'array',
  pricing: 'object',
  pros_and_cons: 'object',
  
  // Social and external links
  social_links: 'object',
  
  // Additional content
  hashtags: 'array',
  tooltip: 'string',
  haiku: 'object',
  releases: 'array',
  
  // FAQ system
  faqs: 'array',
  
  // SEO fields
  meta_title: 'string',
  meta_description: 'string',
  meta_keywords: 'string',
  
  // System metadata (in generated_content)
  generated_content: 'object'
};

const EXPECTED_NESTED_STRUCTURES = {
  pricing: {
    type: 'string',
    plans: 'array',
    details: 'string'
  },
  pros_and_cons: {
    pros: 'array',
    cons: 'array'
  },
  haiku: {
    lines: 'array',
    theme: 'string'
  },
  social_links: {
    twitter: 'string|null',
    linkedin: 'string|null',
    github: 'string|null',
    facebook: 'string|null',
    youtube: 'string|null'
  },
  faqs: {
    structure: 'array_of_objects',
    required_fields: ['id', 'question', 'answer', 'category', 'source']
  },
  releases: {
    structure: 'array_of_objects',
    required_fields: ['version', 'releaseDate', 'changes']
  }
};

async function comprehensiveFieldParsingTest() {
  console.log('🧪 COMPREHENSIVE FIELD PARSING TEST');
  console.log('=' .repeat(70));
  
  // Test with a known tool
  const testUrl = 'https://smartlyq.com/';
  const toolId = 'test-field-parsing-' + Date.now();
  
  console.log(`Testing URL: ${testUrl}`);
  console.log(`Test Tool ID: ${toolId}`);
  
  let rawOpenAIResponse: any = null;
  let processedContent: any = null;
  let storedToolData: any = null;
  
  try {
    // Step 1: Get scraped content
    console.log('\n📋 Step 1: Getting scraped content...');
    const scrapedContentPath = path.join(process.cwd(), 'data', 'scraped-content', 'smartlyq.com');
    const files = fs.readdirSync(scrapedContentPath);
    const latestFile = files.sort().pop();
    
    if (!latestFile) {
      throw new Error('No scraped content found for smartlyq.com');
    }
    
    const scrapedContent = fs.readFileSync(path.join(scrapedContentPath, latestFile), 'utf-8');
    console.log(`✅ Loaded scraped content: ${scrapedContent.length} characters`);
    
    // Step 2: Test AI content generation with raw response capture
    console.log('\n📋 Step 2: Testing AI content generation...');
    
    // Create a custom AI generator to capture raw response
    const aiGenerator = new AIContentGenerator();
    
    // Monkey patch the OpenAI client to capture raw response
    const originalGenerate = (aiGenerator as any).openaiClient.generateContent;
    (aiGenerator as any).openaiClient.generateContent = async function(...args: any[]) {
      const result = await originalGenerate.apply(this, args);
      rawOpenAIResponse = result.rawResponse || result.content;
      return result;
    };
    
    const generationResult = await aiGenerator.generateContent(scrapedContent, testUrl, {
      aiProvider: 'openai',
      priority: 'quality'
    });
    
    if (!generationResult.success) {
      throw new Error(`AI generation failed: ${JSON.stringify(generationResult)}`);
    }
    
    processedContent = generationResult.content;
    console.log('✅ AI content generation completed');
    
    // Step 3: Process through AI Dude response mapper
    console.log('\n📋 Step 3: Testing AI Dude response processing...');
    const mappedContent = PromptManager.processAIDudeResponse(rawOpenAIResponse || processedContent);
    console.log('✅ AI Dude response processing completed');
    
    // Step 4: Simulate database storage
    console.log('\n📋 Step 4: Simulating database storage...');
    const toolData = {
      id: toolId,
      name: mappedContent.name || 'Test Tool',
      slug: toolId,
      website: testUrl,
      submission_source: 'test',
      submission_type: 'admin',
      content_status: 'draft',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      ...mappedContent
    };
    
    // Store in database for testing
    const { data: insertedTool, error: insertError } = await supabaseAdmin
      .from('tools')
      .insert([toolData])
      .select()
      .single();
      
    if (insertError) {
      console.warn(`Database insertion failed: ${insertError.message}`);
      storedToolData = toolData; // Use local data for comparison
    } else {
      storedToolData = insertedTool;
      console.log('✅ Tool stored in database for testing');
    }
    
  } catch (error) {
    console.error('❌ Test setup failed:', error);
    return;
  }
  
  // Step 5: Comprehensive field analysis
  console.log('\n📋 Step 5: Comprehensive Field Analysis');
  console.log('=' .repeat(70));
  
  await analyzeFieldParsing(rawOpenAIResponse, processedContent, storedToolData);
  
  // Step 6: Cleanup test data
  if (storedToolData && storedToolData.id) {
    try {
      await supabaseAdmin.from('tools').delete().eq('id', storedToolData.id);
      console.log('\n🧹 Test data cleaned up');
    } catch (error) {
      console.warn('⚠️ Failed to cleanup test data:', error);
    }
  }
}

async function analyzeFieldParsing(rawResponse: any, processedContent: any, storedData: any) {
  const analysis = {
    missing_fields: [] as string[],
    incorrect_types: [] as string[],
    nested_structure_issues: [] as string[],
    successful_fields: [] as string[],
    extra_fields: [] as string[]
  };
  
  console.log('\n🔍 FIELD-BY-FIELD ANALYSIS');
  console.log('-' .repeat(50));
  
  // Check each expected field
  for (const [fieldName, expectedType] of Object.entries(EXPECTED_AI_FIELDS)) {
    const rawValue = rawResponse?.[fieldName];
    const processedValue = processedContent?.[fieldName];
    const storedValue = storedData?.[fieldName];
    
    console.log(`\n📋 Field: ${fieldName}`);
    console.log(`   Expected Type: ${expectedType}`);
    console.log(`   Raw Response: ${rawValue ? typeof rawValue : 'missing'} - ${JSON.stringify(rawValue)?.substring(0, 100)}...`);
    console.log(`   Processed: ${processedValue ? typeof processedValue : 'missing'} - ${JSON.stringify(processedValue)?.substring(0, 100)}...`);
    console.log(`   Stored: ${storedValue ? typeof storedValue : 'missing'} - ${JSON.stringify(storedValue)?.substring(0, 100)}...`);
    
    // Check if field exists and has correct type
    if (!storedValue) {
      analysis.missing_fields.push(fieldName);
      console.log(`   ❌ MISSING in final storage`);
    } else if (typeof storedValue !== expectedType && expectedType !== 'object' && expectedType !== 'array') {
      analysis.incorrect_types.push(`${fieldName}: expected ${expectedType}, got ${typeof storedValue}`);
      console.log(`   ⚠️ TYPE MISMATCH: expected ${expectedType}, got ${typeof storedValue}`);
    } else {
      analysis.successful_fields.push(fieldName);
      console.log(`   ✅ CORRECT`);
    }
  }
  
  // Check nested structures
  console.log('\n🔍 NESTED STRUCTURE ANALYSIS');
  console.log('-' .repeat(50));
  
  for (const [fieldName, structure] of Object.entries(EXPECTED_NESTED_STRUCTURES)) {
    const storedValue = storedData?.[fieldName];
    
    if (!storedValue) {
      console.log(`\n📋 ${fieldName}: ❌ MISSING`);
      continue;
    }
    
    console.log(`\n📋 ${fieldName}:`);
    
    if (fieldName === 'faqs' || fieldName === 'releases') {
      // Check array of objects structure
      if (!Array.isArray(storedValue)) {
        analysis.nested_structure_issues.push(`${fieldName}: not an array`);
        console.log(`   ❌ Not an array`);
      } else if (storedValue.length > 0) {
        const firstItem = storedValue[0];
        const requiredFields = structure.required_fields as string[];
        const missingFields = requiredFields.filter(field => !(field in firstItem));
        
        if (missingFields.length > 0) {
          analysis.nested_structure_issues.push(`${fieldName}: missing fields ${missingFields.join(', ')}`);
          console.log(`   ⚠️ Missing required fields: ${missingFields.join(', ')}`);
        } else {
          console.log(`   ✅ Structure correct`);
        }
      } else {
        console.log(`   ⚠️ Empty array`);
      }
    } else {
      // Check object structure
      const expectedFields = Object.keys(structure).filter(key => key !== 'structure' && key !== 'required_fields');
      const missingFields = expectedFields.filter(field => !(field in storedValue));
      
      if (missingFields.length > 0) {
        analysis.nested_structure_issues.push(`${fieldName}: missing ${missingFields.join(', ')}`);
        console.log(`   ⚠️ Missing nested fields: ${missingFields.join(', ')}`);
      } else {
        console.log(`   ✅ Structure correct`);
      }
    }
  }
  
  // Check for extra fields that might be lost
  if (rawResponse) {
    const rawFields = Object.keys(rawResponse);
    const expectedFields = Object.keys(EXPECTED_AI_FIELDS);
    const extraFields = rawFields.filter(field => !expectedFields.includes(field));
    
    if (extraFields.length > 0) {
      analysis.extra_fields = extraFields;
      console.log(`\n⚠️ Extra fields in raw response not being stored: ${extraFields.join(', ')}`);
    }
  }
  
  // Final summary
  console.log('\n' + '=' .repeat(70));
  console.log('🎯 COMPREHENSIVE ANALYSIS SUMMARY');
  console.log('=' .repeat(70));
  
  console.log(`\n✅ Successfully parsed fields (${analysis.successful_fields.length}):`);
  analysis.successful_fields.forEach(field => console.log(`   ✅ ${field}`));
  
  if (analysis.missing_fields.length > 0) {
    console.log(`\n❌ Missing fields (${analysis.missing_fields.length}):`);
    analysis.missing_fields.forEach(field => console.log(`   ❌ ${field}`));
  }
  
  if (analysis.incorrect_types.length > 0) {
    console.log(`\n⚠️ Type mismatches (${analysis.incorrect_types.length}):`);
    analysis.incorrect_types.forEach(issue => console.log(`   ⚠️ ${issue}`));
  }
  
  if (analysis.nested_structure_issues.length > 0) {
    console.log(`\n🔧 Nested structure issues (${analysis.nested_structure_issues.length}):`);
    analysis.nested_structure_issues.forEach(issue => console.log(`   🔧 ${issue}`));
  }
  
  if (analysis.extra_fields.length > 0) {
    console.log(`\n📋 Extra fields not being stored (${analysis.extra_fields.length}):`);
    analysis.extra_fields.forEach(field => console.log(`   📋 ${field}`));
  }
  
  // Calculate success rate
  const totalExpectedFields = Object.keys(EXPECTED_AI_FIELDS).length;
  const successRate = (analysis.successful_fields.length / totalExpectedFields) * 100;
  
  console.log(`\n📊 PARSING SUCCESS RATE: ${successRate.toFixed(1)}% (${analysis.successful_fields.length}/${totalExpectedFields})`);
  
  if (successRate < 80) {
    console.log('\n🚨 CRITICAL: Field parsing success rate below 80%');
    console.log('🔧 RECOMMENDATIONS:');
    console.log('   1. Check AI Dude response mapping in PromptManager.processAIDudeResponse()');
    console.log('   2. Verify database schema matches expected fields');
    console.log('   3. Review content generation pipeline field handling');
    console.log('   4. Test with different AI providers (OpenAI vs OpenRouter)');
  } else if (successRate < 95) {
    console.log('\n⚠️ WARNING: Some fields are not being parsed correctly');
    console.log('🔧 Consider reviewing the missing/incorrect fields above');
  } else {
    console.log('\n🎉 EXCELLENT: Field parsing is working correctly!');
  }
}

// Additional test functions
async function testProviderComparison() {
  console.log('\n🔄 PROVIDER COMPARISON TEST');
  console.log('=' .repeat(70));

  const testUrl = 'https://www.foodieprep.ai/';
  const providers = ['openai', 'openrouter'];
  const results: Record<string, any> = {};

  for (const provider of providers) {
    console.log(`\n📋 Testing ${provider.toUpperCase()} provider...`);

    try {
      const aiGenerator = new AIContentGenerator();
      const scrapedContent = 'Sample scraped content for testing'; // Use actual scraped content

      const result = await aiGenerator.generateContent(scrapedContent, testUrl, {
        aiProvider: provider as 'openai' | 'openrouter',
        priority: 'quality'
      });

      if (result.success) {
        results[provider] = result.content;
        console.log(`✅ ${provider} generation successful`);

        // Quick field count
        const fieldCount = Object.keys(result.content || {}).length;
        console.log(`   Fields generated: ${fieldCount}`);
      } else {
        console.log(`❌ ${provider} generation failed`);
        results[provider] = null;
      }
    } catch (error) {
      console.log(`❌ ${provider} error:`, error);
      results[provider] = null;
    }
  }

  // Compare results
  if (results.openai && results.openrouter) {
    console.log('\n🔍 PROVIDER COMPARISON:');
    const openaiFields = Object.keys(results.openai);
    const openrouterFields = Object.keys(results.openrouter);

    const commonFields = openaiFields.filter(field => openrouterFields.includes(field));
    const openaiOnly = openaiFields.filter(field => !openrouterFields.includes(field));
    const openrouterOnly = openrouterFields.filter(field => !openaiFields.includes(field));

    console.log(`   Common fields: ${commonFields.length}`);
    console.log(`   OpenAI only: ${openaiOnly.length} - ${openaiOnly.join(', ')}`);
    console.log(`   OpenRouter only: ${openrouterOnly.length} - ${openrouterOnly.join(', ')}`);
  }
}

async function testDatabaseFieldMapping() {
  console.log('\n🗃️ DATABASE FIELD MAPPING TEST');
  console.log('=' .repeat(70));

  // Get actual database schema
  const { data: columns, error } = await supabaseAdmin
    .from('information_schema.columns')
    .select('column_name, data_type, is_nullable')
    .eq('table_name', 'tools')
    .eq('table_schema', 'public');

  if (error) {
    console.log('❌ Failed to get database schema:', error.message);
    return;
  }

  console.log(`\n📋 Database has ${columns?.length || 0} columns in tools table`);

  const dbFields = columns?.map(col => col.column_name) || [];
  const expectedFields = Object.keys(EXPECTED_AI_FIELDS);

  console.log('\n🔍 FIELD MAPPING ANALYSIS:');

  // Check which expected fields exist in database
  const missingInDb = expectedFields.filter(field => !dbFields.includes(field));
  const existingInDb = expectedFields.filter(field => dbFields.includes(field));

  console.log(`\n✅ Fields that exist in database (${existingInDb.length}):`);
  existingInDb.forEach(field => console.log(`   ✅ ${field}`));

  if (missingInDb.length > 0) {
    console.log(`\n❌ Expected fields missing from database (${missingInDb.length}):`);
    missingInDb.forEach(field => console.log(`   ❌ ${field}`));
  }

  // Check for JSONB fields that might store nested data
  const jsonbFields = columns?.filter(col => col.data_type === 'jsonb').map(col => col.column_name) || [];
  console.log(`\n📋 JSONB fields in database (${jsonbFields.length}):`);
  jsonbFields.forEach(field => console.log(`   📋 ${field}`));
}

async function runAllTests() {
  console.log('🧪 RUNNING ALL FIELD PARSING TESTS');
  console.log('=' .repeat(70));

  try {
    await comprehensiveFieldParsingTest();
    await testProviderComparison();
    await testDatabaseFieldMapping();

    console.log('\n🎯 ALL TESTS COMPLETED');
    console.log('=' .repeat(70));
    console.log('✅ Check the analysis above for any field parsing issues');
    console.log('✅ Review provider differences if any');
    console.log('✅ Verify database schema matches expected fields');

  } catch (error) {
    console.error('❌ Test suite failed:', error);
  }
}

// Run all tests
runAllTests().catch(console.error);
