# Snapshot report for `test/integration/hola/index.js`

The actual snapshot is saved in `index.js.snap`.

Generated by [AVA](https://avajs.dev).

## hola

> Snapshot 1

    {
      audio: null,
      author: '<PERSON>',
      date: '2024-06-24T20:23:34.721Z',
      description: '“She is gorgeous. Her vibe and personality are amazing. We had a great time working together,<PERSON> <PERSON><PERSON><PERSON> said about <PERSON><PERSON><PERSON>.',
      image: 'https://www.hola.com/us/horizon/landscape/2ec6ca11d80b-new-york-new-york-rauw-alejandro-and-bruna-marquezine-are-seen-during-a-photosho.jpg?im=Resize=(1200)',
      lang: 'en',
      logo: 'https://www.hola.com/us/favicon-192x192.png',
      publisher: 'Hola! US',
      title: '<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> on the dreams they want to accomplish: ‘To have kids and a serene love’',
      url: 'https://www.hola.com/us/celebrities/20240624701307/rauw-alejandro-bruna-marquezine-dreams-to-accomplish/',
      video: null,
    }
