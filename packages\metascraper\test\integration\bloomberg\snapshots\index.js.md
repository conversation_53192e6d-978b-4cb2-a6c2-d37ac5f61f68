# Snapshot report for `test/integration/bloomberg/index.js`

The actual snapshot is saved in `index.js.snap`.

Generated by [AVA](https://avajs.dev).

## bloomberg

> Snapshot 1

    {
      author: '<PERSON>',
      date: '2016-05-24T18:00:03.899Z',
      description: 'The HR startups go to war.',
      image: 'https://assets.bwbx.io/images/users/iqjWHBFdfxIU/ioh_yWEn8gHo/v3/1200x800.jpg',
      lang: 'en',
      logo: 'https://assets.bwbx.io/s3/javelin/public/javelin/images/favicon-black-63fe5249d3.png',
      publisher: 'Bloomberg',
      title: 'As Zenefits Stumbles, <PERSON><PERSON> Head-On by Selling Insurance',
      url: 'https://www.bloomberg.com/news/articles/2016-05-24/as-zenefits-stumbles-gusto-goes-head-on-by-selling-insurance',
      video: null,
    }
