# Snapshot report for `test/integration/softwarefordays/index.js`

The actual snapshot is saved in `index.js.snap`.

Generated by [AVA](https://avajs.dev).

## softwarefordays

> Snapshot 1

    {
      audio: null,
      author: null,
      date: null,
      description: 'Excerpted from Functional Programming and the Semantics of Change, State & Time.',
      image: 'https://softwarefordays.com/media/metavideoposter.gif',
      lang: 'en',
      logo: 'https://softwarefordays.com/media/favicon_io/favicon-32x32.png',
      publisher: null,
      title: 'Resolving the Time Paradox Implied by Functional Programs',
      url: 'https://softwarefordays.com/post/resolving-the-fp-time-paradox/',
      video: 'https://softwarefordays.com/media/metavideo.mp4',
    }
