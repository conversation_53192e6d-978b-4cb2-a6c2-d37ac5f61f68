# Snapshot report for `test/integration/theflip/index.js`

The actual snapshot is saved in `index.js.snap`.

Generated by [AVA](https://avajs.dev).

## theflip

> Snapshot 1

    {
      audio: 'https://chtbl.com/track/E29G91/cdn.simplecast.com/audio/0a6ef085-f6a9-4fbd-bcc2-77e7cfd4f65f/episodes/3ab2ec74-8d89-4344-9be0-a30fa53ac6a7/audio/7c63582d-7e0e-486a-b028-7e5692895e84/default_tc.mp3?aid=embed',
      author: null,
      date: '2021-01-21T08:01:16.000Z',
      description: 'The Flip podcast conversational series: digitizing healthcare with <PERSON><PERSON>, the Co-founder and CEO of Nigerian healthtech Helium Health.',
      image: 'https://theflip.africa/wp-content/uploads/2021/01/1600x500-EP2.jpg',
      lang: 'en',
      logo: 'https://theflip.africa/wp-content/uploads/2020/07/The-Flip-scaled.jpg',
      publisher: 'The Flip Africa',
      title: 'Digitizing Healthcare with Helium Health’s Goke Olubusi | The Flip Africa',
      url: 'http://theflip.africa/podcast/digitizing-healthcare-helium-health-goke-olubusi/',
      video: null,
    }
