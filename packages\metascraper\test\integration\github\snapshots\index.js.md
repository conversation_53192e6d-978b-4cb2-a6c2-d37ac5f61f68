# Snapshot report for `test/integration/github/index.js`

The actual snapshot is saved in `index.js.snap`.

Generated by [AVA](https://avajs.dev).

## github

> Snapshot 1

    {
      audio: null,
      author: 'microlinkhq',
      description: 'Microlink Query Language. The official HTTP client to interact with Microlink API for Node.js, browsers &amp; Deno. - microlinkhq/mql: Microlink Query Language. The official HTTP client to interact…',
      image: 'https://opengraph.githubassets.com/0d2af1011fc53210b9778b88a31fa2f993cc70fad0b68c8dfa4553cfc124f7dc/microlinkhq/mql',
      lang: 'en',
      logo: 'https://github.com/fluidicon.png',
      publisher: 'GitHub',
      title: 'microlinkhq/mql: Microlink Query Language. The official HTTP client to interact with Microlink API for Node.js, browsers & Deno.',
      url: 'https://github.com/microlinkhq/mql',
      video: null,
    }
