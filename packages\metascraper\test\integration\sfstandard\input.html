<!DOCTYPE html>
<html lang="en">
   <head>
      <meta http-equiv="origin-trial" content="Az520Inasey3TAyqLyojQa8MnmCALSEU29yQFW8dePZ7xQTvSt73pHazLFTK5f7SyLUJSo2uKLesEtEa9aUYcgMAAACPeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZS5jb206NDQzIiwiZmVhdHVyZSI6IkRpc2FibGVUaGlyZFBhcnR5U3RvcmFnZVBhcnRpdGlvbmluZyIsImV4cGlyeSI6MTcyNTQwNzk5OSwiaXNTdWJkb21haW4iOnRydWUsImlzVGhpcmRQYXJ0eSI6dHJ1ZX0=">
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width">
      <title>California junk fee ban: Restaurant workers brace for impact</title>
      <link rel="preload" as="image" imagesrcset="https://content.sfstandard.com/wp-content/uploads/2024/06/featured_20240621-junkfeeban.jpg?w=640&amp;q=75 640w, https://content.sfstandard.com/wp-content/uploads/2024/06/featured_20240621-junkfeeban.jpg?w=750&amp;q=75 750w, https://content.sfstandard.com/wp-content/uploads/2024/06/featured_20240621-junkfeeban.jpg?w=828&amp;q=75 828w, https://content.sfstandard.com/wp-content/uploads/2024/06/featured_20240621-junkfeeban.jpg?w=1080&amp;q=75 1080w, https://content.sfstandard.com/wp-content/uploads/2024/06/featured_20240621-junkfeeban.jpg?w=1200&amp;q=75 1200w, https://content.sfstandard.com/wp-content/uploads/2024/06/featured_20240621-junkfeeban.jpg?w=1920&amp;q=75 1920w, https://content.sfstandard.com/wp-content/uploads/2024/06/featured_20240621-junkfeeban.jpg?w=2048&amp;q=75 2048w, https://content.sfstandard.com/wp-content/uploads/2024/06/featured_20240621-junkfeeban.jpg?w=3840&amp;q=75 3840w" imagesizes="100vw">
      <meta name="parsely-title" content="Restaurant workers fear they could pay highest cost in junk-fee battle">
      <meta name="parsely-link" content="https://sfstandard.com/2024/06/24/service-fee-restaurants-san-francisco/">
      <meta name="parsely-type" content="post">
      <meta name="parsely-image-url" content="https://content.sfstandard.com/wp-content/uploads/2024/06/featured_20240621-junkfeeban.jpg">
      <meta name="parsely-pub-date" content="2024-06-24T10:00:00-07:00Z">
      <meta name="parsely-section" content="Food &amp; Drink">
      <meta name="parsely-author" content="Lauren Saria">
      <meta name="parsely-tags" content="Food,Restaurants">
      <meta name="parsely-metadata" content="{&quot;image&quot;:{&quot;src&quot;:&quot;https://content.sfstandard.com/wp-content/uploads/2024/06/featured_20240621-junkfeeban.jpg&quot;,&quot;alt&quot;:&quot;An illustration of a large glass tip jar breaking into pieces as people dressed as chefs and servers stand around and watch.&quot;,&quot;width&quot;:2048,&quot;height&quot;:1365},&quot;excerpt&quot;:&quot;Diners hate them. Restaurant owners say they need them. But in the battle over service fees, it’s servers and cooks on the front lines. &quot;,&quot;postSources&quot;:[],&quot;type&quot;:&quot;Post&quot;,&quot;cleanTitle&quot;:&quot;Restaurant workers fear they could pay highest cost in junk-fee battle&quot;,&quot;id&quot;:285407}">
      <meta name="sailthru.date" content="2024-06-24 10:00:00">
      <meta name="sailthru.author" content="Lauren Saria">
      <meta name="sailthru.tags" content="category-food-&amp; drink,food,restaurants">
      <meta name="sailthru.description" content="Diners hate them. Owners say they need them. But in the raging battle over restaurant service fees, it’s servers and cooks who are on the front lines.">
      <meta name="sailthru.title" content="Restaurant workers fear they could pay highest cost in junk-fee battle">
      <meta name="sailthru.image.full" content="https://content.sfstandard.com/wp-content/uploads/2024/06/featured_20240621-junkfeeban.jpg">
      <meta name="sailthru.image.thumb" content="https://content.sfstandard.com/wp-content/uploads/2024/06/featured_20240621-junkfeeban.jpg?resize=50px,50px&amp;q=100">
      <meta name="robots" content="follow, index, max-snippet:-1, max-image-preview:large, max-video-preview:-1">
      <meta name="description" content="San Francisco's restaurant workers worry &quot;junk fee ban&quot; could mean lower wages and career changes." data-meta-id="article-285407">
      <link rel="canonical" href="https://sfstandard.com/2024/06/24/service-fee-restaurants-san-francisco/" data-meta-id="article-285407">
      <meta property="og:type" content="article" data-meta-id="article-285407">
      <meta property="og:title" content="Restaurant workers fear they could pay highest cost in junk-fee battle" data-meta-id="article-285407">
      <meta property="og:description" content="Diners hate them. Owners say they need them. But in the raging battle over restaurant service fees, it’s servers and cooks who are on the front lines." data-meta-id="article-285407">
      <meta property="og:url" content="https://sfstandard.com/2024/06/24/service-fee-restaurants-san-francisco/" data-meta-id="article-285407">
      <meta property="og:site_name" content="The San Francisco Standard" data-meta-id="article-285407">
      <meta property="article:publisher" content="https://www.facebook.com/SFStandard" data-meta-id="article-285407">
      <meta property="article:published_time" content="2024-06-24T17:00:00+00:00" data-meta-id="article-285407">
      <meta property="article:modified_time" content="2024-06-24T18:59:54+00:00" data-meta-id="article-285407">
      <meta property="og:image" content="https://content.sfstandard.com/wp-content/uploads/2024/06/featured_20240621-junkfeeban.jpg?resize=1200%2C630" data-meta-id="article-285407">
      <meta property="og:image:width" content="1200" data-meta-id="article-285407">
      <meta property="og:image:height" content="630" data-meta-id="article-285407">
      <meta property="og:image:type" content="image/jpeg" data-meta-id="article-285407">
      <meta name="twitter:card" content="summary_large_image" data-meta-id="article-285407">
      <meta name="twitter:title" content="Restaurant workers fear they could pay highest cost in junk-fee battle" data-meta-id="article-285407">
      <meta name="twitter:description" content="Diners hate them. Owners say they need them. But in the raging battle over restaurant service fees, it’s servers and cooks who are on the front lines." data-meta-id="article-285407">
      <meta name="twitter:image" content="https://content.sfstandard.com/wp-content/uploads/2024/06/featured_20240621-junkfeeban.jpg?fit=2048%2C2048" data-meta-id="article-285407">
      <meta name="twitter:creator" content="@https://twitter.com/lhsaria" data-meta-id="article-285407">
      <meta name="twitter:site" content="@sfstandard" data-meta-id="article-285407">
      <meta name="twitter:label2" content="Est. reading time" data-meta-id="article-285407">
      <meta name="twitter:data2" content="6 minutes" data-meta-id="article-285407">
      <script src="//tru.am/scripts/ta-pagesocial-sdk.js"></script><script type="text/javascript" async="" src="https://cdn.parsely.com/keys/sfstandard.com/p.js?gtm_ver=3.1"></script><script type="text/javascript" async="" src="https://www.googletagmanager.com/gtag/js?id=G-PJ9RJEQKSZ&amp;l=dataLayer&amp;cx=c"></script><script async="" src="https://www.googletagmanager.com/gtm.js?id=GTM-MM9HQBT"></script><script type="text/javascript" async="" src="https://www.gstatic.com/recaptcha/releases/KXX4ARWFlYTftefkdODAYWZh/recaptcha__en.js" crossorigin="anonymous" integrity="sha384-9QqbrduyhvTkE1knPG7e8vZdHL95dTvL+5nDP2Q75ZzSBMqM7hBF4FBqPllxiD5J"></script><script type="application/ld+json">{"@context":"https://schema.org","@graph":[{"@type":"Article","@id":"https://sfstandard.com/2024/06/24/service-fee-restaurants-san-francisco/#article","isPartOf":{"@id":"https://sfstandard.com/2024/06/24/service-fee-restaurants-san-francisco/"},"author":[{"@id":"https://sfstandard.com/?post_type=profile&#038;p=281091"}],"headline":"Restaurant workers fear they could pay highest cost in junk-fee battle","datePublished":"2024-06-24T17:00:00+00:00","dateModified":"2024-06-24T18:59:54+00:00","mainEntityOfPage":{"@id":"https://sfstandard.com/2024/06/24/service-fee-restaurants-san-francisco/"},"wordCount":1184,"publisher":{"@id":"https://sfstandard.com/#organization"},"image":{"@id":"https://sfstandard.com/2024/06/24/service-fee-restaurants-san-francisco/#primaryimage"},"thumbnailUrl":"https://content.sfstandard.com/wp-content/uploads/2024/06/featured_20240621-junkfeeban.jpg","keywords":["Food","Restaurants"],"articleSection":["Food &amp; Drink"],"inLanguage":"en-US","url":"https://sfstandard.com/2024/06/24/service-fee-restaurants-san-francisco/","copyrightYear":"2024","copyrightHolder":{"@id":"https://sfstandard.com/#organization"}},{"@type":"WebPage","@id":"https://sfstandard.com/2024/06/24/service-fee-restaurants-san-francisco/","url":"https://sfstandard.com/2024/06/24/service-fee-restaurants-san-francisco/","name":"California junk fee ban: Restaurant workers brace for impact","isPartOf":{"@id":"https://sfstandard.com/#website"},"primaryImageOfPage":{"@id":"https://sfstandard.com/2024/06/24/service-fee-restaurants-san-francisco/#primaryimage"},"image":{"@id":"https://sfstandard.com/2024/06/24/service-fee-restaurants-san-francisco/#primaryimage"},"thumbnailUrl":"https://content.sfstandard.com/wp-content/uploads/2024/06/featured_20240621-junkfeeban.jpg","datePublished":"2024-06-24T17:00:00+00:00","dateModified":"2024-06-24T18:59:54+00:00","description":"San Francisco's restaurant workers worry \"junk fee ban\" could mean lower wages and career changes.","inLanguage":"en-US","potentialAction":[{"@type":"ReadAction","target":["https://sfstandard.com/2024/06/24/service-fee-restaurants-san-francisco/"]}]},{"@type":"ImageObject","inLanguage":"en-US","@id":"https://sfstandard.com/2024/06/24/service-fee-restaurants-san-francisco/#primaryimage","url":"https://content.sfstandard.com/wp-content/uploads/2024/06/featured_20240621-junkfeeban.jpg","contentUrl":"https://content.sfstandard.com/wp-content/uploads/2024/06/featured_20240621-junkfeeban.jpg","width":2048,"height":1365,"caption":"California's ban on \"junk fees\" goes into effect in just one week. Unless a hail mary effort to pass a carveout for restaurants succeeds, many San Francisco restaurant owners will likely raise prices. But its the service workers including servers and cooks who worry they'll suffer most due to the transition away from mandatory service fees."},{"@type":"WebSite","@id":"https://sfstandard.com/#website","url":"https://sfstandard.com/","name":"The San Francisco Standard","description":"The San Francisco Bay Area&#039;s essential source for daily news, politics, business, food, tech, culture and more","publisher":{"@id":"https://sfstandard.com/#organization"},"potentialAction":[{"@type":"SearchAction","target":{"@type":"EntryPoint","urlTemplate":"https://sfstandard.com/?s={search_term_string}"},"query-input":"required name=search_term_string"}],"inLanguage":"en-US"},{"@type":"Organization","@id":"https://sfstandard.com/#organization","name":"The San Francisco Standard","url":"https://sfstandard.com/","logo":{"@type":"ImageObject","inLanguage":"en-US","@id":"https://sfstandard.com/#/schema/logo/image/","url":"https://content.sfstandard.com/wp-content/uploads/2024/02/coloryellow-cropdefault-transparentfalse2x.png","contentUrl":"https://content.sfstandard.com/wp-content/uploads/2024/02/coloryellow-cropdefault-transparentfalse2x.png","width":512,"height":512,"caption":"The San Francisco Standard"},"image":{"@id":"https://sfstandard.com/#/schema/logo/image/"},"sameAs":["https://www.facebook.com/SFStandard","https://x.com/sfstandard","https://www.instagram.com/sfstandard/","https://www.linkedin.com/company/sfstandard/","https://www.youtube.com/c/SFStandard","https://www.tiktok.com/@sfstandard","https://www.threads.net/@sfstandard"],"publishingPrinciples":"https://sfstandard.com/about/","ownershipFundingInfo":"https://sfstandard.com/about/","actionableFeedbackPolicy":"https://sfstandard.com/ethics-standards/","correctionsPolicy":"https://sfstandard.com/ethics-standards/","ethicsPolicy":"https://sfstandard.com/ethics-standards/"},{"@type":"Person","@id":"https://sfstandard.com/?post_type=profile&#038;p=281091","name":"Lauren Saria","url":"https://sfstandard.com/author/lauren-saria/","description":"Lauren Saria is the deputy food editor at The Standard and has more than a decade of experience covering food, drink, and the restaurant industry. She was previously the site lead for Eater SF and has worked as the food editor at The Arizona Republic and Phoenix New Times. An alumna of the Walter Cronkite&hellip;","image":{"@type":"ImageObject","inLanguage":"en-US","@id":"https://sfstandard.com/#/schema/person/image/","url":"https://content.sfstandard.com/wp-content/uploads/2024/06/sfs-staffportrait-laurensaria.jpg","contentUrl":"https://content.sfstandard.com/wp-content/uploads/2024/06/sfs-staffportrait-laurensaria.jpg","width":4651,"height":3101,"caption":"Deputy Food Editor Lauren Saria poses for a portrait at the San Francisco Standard newsroom in San Francisco on Thursday, Jun. 6, 2024."}}]}</script>
      <meta name="next-head-count" content="44">
      <link rel="stylesheet" data-href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&amp;display=swap">
      <link rel="preconnect" crossorigin="true" data-href="https://use.typekit.net">
      <link rel="alternate" type="application/rss+xml" title="The San Francisco Standard » Feed" href="https://sfstandard.com/feed/">
      <link rel="icon" href="https://content.sfstandard.com/wp-content/uploads/2024/01/favicon-32x32-1.png" sizes="32x32">
      <link rel="icon" href="https://content.sfstandard.com/wp-content/uploads/2024/01/favicon-192x192-1.png" sizes="192x192">
      <link rel="apple-touch-icon" href="https://content.sfstandard.com/wp-content/uploads/2024/01/favicon-180x180-1.png">
      <meta name="msapplication-TileImage" content="https://content.sfstandard.com/wp-content/uploads/2024/01/favicon-270x270-1.png">
      <link rel="preload" href="/_next/static/css/c2978f49e63f6de7.css" as="style">
      <link rel="stylesheet" href="/_next/static/css/c2978f49e63f6de7.css" data-n-g="">
      <link rel="preload" href="/_next/static/css/bbbd3a7b38cce32b.css" as="style">
      <link rel="stylesheet" href="/_next/static/css/bbbd3a7b38cce32b.css" data-n-p="">
      <link rel="preload" href="/_next/static/css/9fe3e7eb6077bc85.css" as="style">
      <link rel="stylesheet" href="/_next/static/css/9fe3e7eb6077bc85.css" data-n-p="">
      <link rel="preload" href="/_next/static/css/f67a7be27b7d2e7a.css" as="style">
      <link rel="stylesheet" href="/_next/static/css/f67a7be27b7d2e7a.css" data-n-p="">
      <noscript data-n-css=""></noscript>
      <script defer="" nomodule="" src="/_next/static/chunks/polyfills-c67a75d1b6f99dc8.js"></script><script src="/_next/static/chunks/webpack-c0b44505f9d0ac1c.js" defer=""></script><script src="/_next/static/chunks/framework-4ed89e9640adfb9e.js" defer=""></script><script src="/_next/static/chunks/main-5db7a5a50323588c.js" defer=""></script><script src="/_next/static/chunks/pages/_app-9f501e02ce81351b.js" defer=""></script><script src="/_next/static/chunks/2303-3713eb74fc57cc96.js" defer=""></script><script src="/_next/static/chunks/6010-af98f04365e7f366.js" defer=""></script><script src="/_next/static/chunks/4570-ea7ad4ea47686028.js" defer=""></script><script src="/_next/static/chunks/8764-593a836a579a1569.js" defer=""></script><script src="/_next/static/chunks/7527-127afc63373cf5ac.js" defer=""></script><script src="/_next/static/chunks/1961-c0b4fd4b1967feec.js" defer=""></script><script src="/_next/static/chunks/7338-733a58f6deac46fb.js" defer=""></script><script src="/_next/static/chunks/3453-d45c956bbd3bf9f5.js" defer=""></script><script src="/_next/static/chunks/8278-a6821129567a0085.js" defer=""></script><script src="/_next/static/chunks/2926-4e6ebbc3de17862c.js" defer=""></script><script src="/_next/static/chunks/pages/%5B...slug%5D-a906b53e3804bc8f.js" defer=""></script><script src="/_next/static/5515QAup-wFM4UtsvvF4J/_buildManifest.js" defer=""></script><script src="/_next/static/5515QAup-wFM4UtsvvF4J/_ssgManifest.js" defer=""></script>
      <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&amp;display=swap">
      <script type="text/javascript" src="https://global.ketchcdn.com/web/v3/config/sfstandard/website_smart_tag/boot.js" async="" defer=""></script>
      <link rel="stylesheet" type="text/css" href="/_next/static/css/8c52810e219aa827.css">
      <script type="text/javascript" async="" defer="" src="https://cdn.ketchjs.com/lanyard/v2/lanyard.js"></script><script type="text/javascript" async="" defer="" src="https://cdn.ketchjs.com/plugins/v1/plugins.js"></script><script type="text/javascript" async="" defer="" src="https://cdn.ketchjs.com/ketchtag/stable/v2.12/ketch.js"></script>
      <style>*, ::before, ::after {
         --tw-border-spacing-x: 0;
         --tw-border-spacing-y: 0;
         --tw-translate-x: 0;
         --tw-translate-y: 0;
         --tw-rotate: 0;
         --tw-skew-x: 0;
         --tw-skew-y: 0;
         --tw-scale-x: 1;
         --tw-scale-y: 1;
         --tw-pan-x:  ;
         --tw-pan-y:  ;
         --tw-pinch-zoom:  ;
         --tw-scroll-snap-strictness: proximity;
         --tw-gradient-from-position:  ;
         --tw-gradient-via-position:  ;
         --tw-gradient-to-position:  ;
         --tw-ordinal:  ;
         --tw-slashed-zero:  ;
         --tw-numeric-figure:  ;
         --tw-numeric-spacing:  ;
         --tw-numeric-fraction:  ;
         --tw-ring-inset:  ;
         --tw-ring-offset-width: 0px;
         --tw-ring-offset-color: #fff;
         --tw-ring-color: rgb(59 130 246 / 0.5);
         --tw-ring-offset-shadow: 0 0 #0000;
         --tw-ring-shadow: 0 0 #0000;
         --tw-shadow: 0 0 #0000;
         --tw-shadow-colored: 0 0 #0000;
         --tw-blur:  ;
         --tw-brightness:  ;
         --tw-contrast:  ;
         --tw-grayscale:  ;
         --tw-hue-rotate:  ;
         --tw-invert:  ;
         --tw-saturate:  ;
         --tw-sepia:  ;
         --tw-drop-shadow:  ;
         --tw-backdrop-blur:  ;
         --tw-backdrop-brightness:  ;
         --tw-backdrop-contrast:  ;
         --tw-backdrop-grayscale:  ;
         --tw-backdrop-hue-rotate:  ;
         --tw-backdrop-invert:  ;
         --tw-backdrop-opacity:  ;
         --tw-backdrop-saturate:  ;
         --tw-backdrop-sepia:  ;
         }
         ::backdrop {
         --tw-border-spacing-x: 0;
         --tw-border-spacing-y: 0;
         --tw-translate-x: 0;
         --tw-translate-y: 0;
         --tw-rotate: 0;
         --tw-skew-x: 0;
         --tw-skew-y: 0;
         --tw-scale-x: 1;
         --tw-scale-y: 1;
         --tw-pan-x:  ;
         --tw-pan-y:  ;
         --tw-pinch-zoom:  ;
         --tw-scroll-snap-strictness: proximity;
         --tw-gradient-from-position:  ;
         --tw-gradient-via-position:  ;
         --tw-gradient-to-position:  ;
         --tw-ordinal:  ;
         --tw-slashed-zero:  ;
         --tw-numeric-figure:  ;
         --tw-numeric-spacing:  ;
         --tw-numeric-fraction:  ;
         --tw-ring-inset:  ;
         --tw-ring-offset-width: 0px;
         --tw-ring-offset-color: #fff;
         --tw-ring-color: rgb(59 130 246 / 0.5);
         --tw-ring-offset-shadow: 0 0 #0000;
         --tw-ring-shadow: 0 0 #0000;
         --tw-shadow: 0 0 #0000;
         --tw-shadow-colored: 0 0 #0000;
         --tw-blur:  ;
         --tw-brightness:  ;
         --tw-contrast:  ;
         --tw-grayscale:  ;
         --tw-hue-rotate:  ;
         --tw-invert:  ;
         --tw-saturate:  ;
         --tw-sepia:  ;
         --tw-drop-shadow:  ;
         --tw-backdrop-blur:  ;
         --tw-backdrop-brightness:  ;
         --tw-backdrop-contrast:  ;
         --tw-backdrop-grayscale:  ;
         --tw-backdrop-hue-rotate:  ;
         --tw-backdrop-invert:  ;
         --tw-backdrop-opacity:  ;
         --tw-backdrop-saturate:  ;
         --tw-backdrop-sepia:  ;
         }
         #lanyard_root * :is(.ketch-sr-only) {
         position: absolute;
         width: 1px;
         height: 1px;
         padding: 0;
         margin: -1px;
         overflow: hidden;
         clip: rect(0, 0, 0, 0);
         white-space: nowrap;
         border-width: 0;
         }
         #lanyard_root * :is(.ketch-pointer-events-none) {
         pointer-events: none;
         }
         #lanyard_root * :is(.ketch-fixed) {
         position: fixed;
         }
         #lanyard_root * :is(.ketch-absolute) {
         position: absolute;
         }
         #lanyard_root * :is(.ketch-relative) {
         position: relative;
         }
         #lanyard_root * :is(.ketch-inset-0) {
         inset: 0;
         }
         #lanyard_root * :is(.ketch-inset-y-0) {
         top: 0;
         bottom: 0;
         }
         #lanyard_root * :is(.\!ketch-left-0) {
         left: 0 !important;
         }
         #lanyard_root * :is(.\!ketch-right-0) {
         right: 0 !important;
         }
         #lanyard_root * :is(.ketch-bottom-0) {
         bottom: 0;
         }
         #lanyard_root * :is(.ketch-bottom-1) {
         bottom: 4px;
         }
         #lanyard_root * :is(.ketch-bottom-2\/4) {
         bottom: 50%;
         }
         #lanyard_root * :is(.ketch-bottom-\[50\%\]) {
         bottom: 50%;
         }
         #lanyard_root * :is(.ketch-bottom-auto) {
         bottom: auto;
         }
         #lanyard_root * :is(.ketch-end-\[2px\]) {
         inset-inline-end: 2px;
         }
         #lanyard_root * :is(.ketch-left-0) {
         left: 0;
         }
         #lanyard_root * :is(.ketch-left-1) {
         left: 4px;
         }
         #lanyard_root * :is(.ketch-left-auto) {
         left: auto;
         }
         #lanyard_root * :is(.ketch-right-0) {
         right: 0;
         }
         #lanyard_root * :is(.ketch-right-1) {
         right: 4px;
         }
         #lanyard_root * :is(.ketch-right-2) {
         right: 8px;
         }
         #lanyard_root * :is(.ketch-start-\[2px\]) {
         inset-inline-start: 2px;
         }
         #lanyard_root * :is(.ketch-top-0) {
         top: 0;
         }
         #lanyard_root * :is(.ketch-top-1) {
         top: 4px;
         }
         #lanyard_root * :is(.ketch-top-\[4px\]) {
         top: 4px;
         }
         #lanyard_root * :is(.-ketch-z-10) {
         z-index: -10;
         }
         #lanyard_root * :is(.ketch-z-0) {
         z-index: 0;
         }
         #lanyard_root * :is(.ketch-z-10) {
         z-index: 10;
         }
         #lanyard_root * :is(.ketch-z-40) {
         z-index: 40;
         }
         #lanyard_root * :is(.ketch-z-ketch-max-z-index) {
         z-index: 2147483647;
         }
         #lanyard_root * :is(.ketch-col-span-1) {
         grid-column: span 1 / span 1;
         }
         #lanyard_root * :is(.ketch-col-span-12) {
         grid-column: span 12 / span 12;
         }
         #lanyard_root * :is(.ketch-col-span-2) {
         grid-column: span 2 / span 2;
         }
         #lanyard_root * :is(.ketch-row-span-1) {
         grid-row: span 1 / span 1;
         }
         #lanyard_root * :is(.\!ketch-m-0) {
         margin: 0 !important;
         }
         #lanyard_root * :is(.ketch-m-0) {
         margin: 0;
         }
         #lanyard_root * :is(.ketch-mx-auto) {
         margin-left: auto;
         margin-right: auto;
         }
         #lanyard_root * :is(.ketch-my-0) {
         margin-top: 0;
         margin-bottom: 0;
         }
         #lanyard_root * :is(.ketch-my-4) {
         margin-top: 16px;
         margin-bottom: 16px;
         }
         #lanyard_root * :is(.ketch-mb-0) {
         margin-bottom: 0;
         }
         #lanyard_root * :is(.ketch-mb-1) {
         margin-bottom: 4px;
         }
         #lanyard_root * :is(.ketch-mb-2) {
         margin-bottom: 8px;
         }
         #lanyard_root * :is(.ketch-mb-3) {
         margin-bottom: 12px;
         }
         #lanyard_root * :is(.ketch-mb-4) {
         margin-bottom: 16px;
         }
         #lanyard_root * :is(.ketch-mb-6) {
         margin-bottom: 24px;
         }
         #lanyard_root * :is(.ketch-mb-8) {
         margin-bottom: 32px;
         }
         #lanyard_root * :is(.ketch-ml-0) {
         margin-left: 0;
         }
         #lanyard_root * :is(.ketch-ml-1) {
         margin-left: 4px;
         }
         #lanyard_root * :is(.ketch-ml-2) {
         margin-left: 8px;
         }
         #lanyard_root * :is(.ketch-ml-2\.5) {
         margin-left: 10px;
         }
         #lanyard_root * :is(.ketch-ml-\[18px\]) {
         margin-left: 18px;
         }
         #lanyard_root * :is(.ketch-ml-\[24px\]) {
         margin-left: 24px;
         }
         #lanyard_root * :is(.ketch-ml-auto) {
         margin-left: auto;
         }
         #lanyard_root * :is(.ketch-mr-4) {
         margin-right: 16px;
         }
         #lanyard_root * :is(.ketch-mr-auto) {
         margin-right: auto;
         }
         #lanyard_root * :is(.ketch-mt-1) {
         margin-top: 4px;
         }
         #lanyard_root * :is(.ketch-mt-3) {
         margin-top: 12px;
         }
         #lanyard_root * :is(.ketch-mt-5) {
         margin-top: 20px;
         }
         #lanyard_root * :is(.ketch-mt-8) {
         margin-top: 32px;
         }
         #lanyard_root * :is(.ketch-box-border) {
         box-sizing: border-box;
         }
         #lanyard_root * :is(.\!ketch-block) {
         display: block !important;
         }
         #lanyard_root * :is(.ketch-block) {
         display: block;
         }
         #lanyard_root * :is(.ketch-inline-block) {
         display: inline-block;
         }
         #lanyard_root * :is(.ketch-inline) {
         display: inline;
         }
         #lanyard_root * :is(.\!ketch-flex) {
         display: flex !important;
         }
         #lanyard_root * :is(.ketch-flex) {
         display: flex;
         }
         #lanyard_root * :is(.ketch-inline-flex) {
         display: inline-flex;
         }
         #lanyard_root * :is(.ketch-grid) {
         display: grid;
         }
         #lanyard_root * :is(.ketch-hidden) {
         display: none;
         }
         #lanyard_root * :is(.ketch-aspect-\[1024\/480\]) {
         aspect-ratio: 1024/480;
         }
         #lanyard_root * :is(.ketch-h-0) {
         height: 0;
         }
         #lanyard_root * :is(.ketch-h-10) {
         height: 40px;
         }
         #lanyard_root * :is(.ketch-h-11) {
         height: 44px;
         }
         #lanyard_root * :is(.ketch-h-12) {
         height: 48px;
         }
         #lanyard_root * :is(.ketch-h-16) {
         height: 64px;
         }
         #lanyard_root * :is(.ketch-h-4) {
         height: 16px;
         }
         #lanyard_root * :is(.ketch-h-5) {
         height: 20px;
         }
         #lanyard_root * :is(.ketch-h-6) {
         height: 24px;
         }
         #lanyard_root * :is(.ketch-h-9) {
         height: 36px;
         }
         #lanyard_root * :is(.ketch-h-\[17px\]) {
         height: 17px;
         }
         #lanyard_root * :is(.ketch-h-\[20px\]) {
         height: 20px;
         }
         #lanyard_root * :is(.ketch-h-\[22px\]) {
         height: 22px;
         }
         #lanyard_root * :is(.ketch-h-\[30px\]) {
         height: 30px;
         }
         #lanyard_root * :is(.ketch-h-\[50\%\]) {
         height: 50%;
         }
         #lanyard_root * :is(.ketch-h-auto) {
         height: auto;
         }
         #lanyard_root * :is(.ketch-h-dvh) {
         height: 100dvh;
         }
         #lanyard_root * :is(.ketch-h-full) {
         height: 100%;
         }
         #lanyard_root * :is(.ketch-h-screen) {
         height: 100vh;
         }
         #lanyard_root * :is(.ketch-max-h-44) {
         max-height: 176px;
         }
         #lanyard_root * :is(.ketch-max-h-6) {
         max-height: 24px;
         }
         #lanyard_root * :is(.ketch-max-h-80) {
         max-height: 320px;
         }
         #lanyard_root * :is(.ketch-max-h-9) {
         max-height: 36px;
         }
         #lanyard_root * :is(.ketch-max-h-\[30px\]) {
         max-height: 30px;
         }
         #lanyard_root * :is(.ketch-max-h-\[69px\]) {
         max-height: 69px;
         }
         #lanyard_root * :is(.ketch-max-h-\[75\%\]) {
         max-height: 75%;
         }
         #lanyard_root * :is(.ketch-max-h-\[90\%\]) {
         max-height: 90%;
         }
         #lanyard_root * :is(.ketch-max-h-dvh) {
         max-height: 100dvh;
         }
         #lanyard_root * :is(.ketch-max-h-full) {
         max-height: 100%;
         }
         #lanyard_root * :is(.ketch-max-h-max) {
         max-height: -moz-max-content;
         max-height: max-content;
         }
         #lanyard_root * :is(.ketch-min-h-0) {
         min-height: 0;
         }
         #lanyard_root * :is(.ketch-min-h-10) {
         min-height: 40px;
         }
         #lanyard_root * :is(.ketch-min-h-11) {
         min-height: 44px;
         }
         #lanyard_root * :is(.ketch-min-h-7) {
         min-height: 28px;
         }
         #lanyard_root * :is(.ketch-min-h-8) {
         min-height: 32px;
         }
         #lanyard_root * :is(.ketch-min-h-\[57px\]) {
         min-height: 57px;
         }
         #lanyard_root * :is(.ketch-min-h-\[75px\]) {
         min-height: 75px;
         }
         #lanyard_root * :is(.ketch-min-h-full) {
         min-height: 100%;
         }
         #lanyard_root * :is(.\!ketch-w-full) {
         width: 100% !important;
         }
         #lanyard_root * :is(.ketch-w-10) {
         width: 40px;
         }
         #lanyard_root * :is(.ketch-w-11) {
         width: 44px;
         }
         #lanyard_root * :is(.ketch-w-16) {
         width: 64px;
         }
         #lanyard_root * :is(.ketch-w-4) {
         width: 16px;
         }
         #lanyard_root * :is(.ketch-w-5) {
         width: 20px;
         }
         #lanyard_root * :is(.ketch-w-6) {
         width: 24px;
         }
         #lanyard_root * :is(.ketch-w-9) {
         width: 36px;
         }
         #lanyard_root * :is(.ketch-w-\[115px\]) {
         width: 115px;
         }
         #lanyard_root * :is(.ketch-w-\[134px\]) {
         width: 134px;
         }
         #lanyard_root * :is(.ketch-w-\[30px\]) {
         width: 30px;
         }
         #lanyard_root * :is(.ketch-w-\[328px\]) {
         width: 328px;
         }
         #lanyard_root * :is(.ketch-w-\[99\%\]) {
         width: 99%;
         }
         #lanyard_root * :is(.ketch-w-\[calc\(100\%-8px\)\]) {
         width: calc(100% - 8px);
         }
         #lanyard_root * :is(.ketch-w-auto) {
         width: auto;
         }
         #lanyard_root * :is(.ketch-w-full) {
         width: 100%;
         }
         #lanyard_root * :is(.ketch-min-w-0) {
         min-width: 0;
         }
         #lanyard_root * :is(.ketch-min-w-10) {
         min-width: 40px;
         }
         #lanyard_root * :is(.ketch-min-w-5) {
         min-width: 20px;
         }
         #lanyard_root * :is(.ketch-max-w-5xl) {
         max-width: 64rem;
         }
         #lanyard_root * :is(.ketch-max-w-6) {
         max-width: 24px;
         }
         #lanyard_root * :is(.ketch-max-w-9) {
         max-width: 36px;
         }
         #lanyard_root * :is(.ketch-max-w-\[30px\]) {
         max-width: 30px;
         }
         #lanyard_root * :is(.ketch-max-w-\[400px\]) {
         max-width: 400px;
         }
         #lanyard_root * :is(.ketch-max-w-\[560px\]) {
         max-width: 560px;
         }
         #lanyard_root * :is(.ketch-max-w-\[70\%\]) {
         max-width: 70%;
         }
         #lanyard_root * :is(.ketch-max-w-full) {
         max-width: 100%;
         }
         #lanyard_root * :is(.ketch-max-w-none) {
         max-width: none;
         }
         #lanyard_root * :is(.ketch-flex-1) {
         flex: 1 1 0%;
         }
         #lanyard_root * :is(.ketch-flex-shrink-0) {
         flex-shrink: 0;
         }
         #lanyard_root * :is(.ketch-shrink) {
         flex-shrink: 1;
         }
         #lanyard_root * :is(.ketch-shrink-0) {
         flex-shrink: 0;
         }
         #lanyard_root * :is(.ketch-flex-grow) {
         flex-grow: 1;
         }
         #lanyard_root * :is(.ketch-flex-grow-0) {
         flex-grow: 0;
         }
         #lanyard_root * :is(.ketch-grow) {
         flex-grow: 1;
         }
         #lanyard_root * :is(.ketch-grow-0) {
         flex-grow: 0;
         }
         #lanyard_root * :is(.ketch-basis-\[70\%\]) {
         flex-basis: 70%;
         }
         #lanyard_root * :is(.ketch-border-collapse) {
         border-collapse: collapse;
         }
         #lanyard_root * :is(.ketch-translate-y-2\/4) {
         --tw-translate-y: 50%;
         transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
         }
         #lanyard_root * :is(.ketch-translate-y-\[50\%\]) {
         --tw-translate-y: 50%;
         transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
         }
         #lanyard_root * :is(.-ketch-rotate-180) {
         --tw-rotate: -180deg;
         transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
         }
         #lanyard_root * :is(.-ketch-rotate-90) {
         --tw-rotate: -90deg;
         transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
         }
         #lanyard_root * :is(.ketch-rotate-0) {
         --tw-rotate: 0deg;
         transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
         }
         #lanyard_root * :is(.ketch-rotate-180) {
         --tw-rotate: 180deg;
         transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
         }
         @keyframes ketch-bannerAnimateCenter {
         from {
         opacity: 0;
         }
         to {
         opacity: 1;
         }
         }
         #lanyard_root * :is(.ketch-animate-bannerAnimateCenter) {
         animation: ketch-bannerAnimateCenter 0.4s forwards;
         }
         @keyframes ketch-bannerAnimateTop {
         from {
         transform: translateY(-900px);
         }
         to {
         transform: translateY(0);
         }
         }
         #lanyard_root * :is(.ketch-animate-bannerAnimateTop) {
         animation: ketch-bannerAnimateTop 0.4s forwards;
         }
         @keyframes ketch-bannerDesktopAnimate {
         from {
         transform: translateY(900px);
         }
         to {
         transform: translateY(0);
         }
         }
         #lanyard_root * :is(.ketch-animate-bannerDesktopAnimate) {
         animation: ketch-bannerDesktopAnimate 0.4s forwards;
         }
         @keyframes ketch-bannerMobileBottomAnimate {
         from {
         transform: translateY(900px);
         }
         to {
         transform: translateY(0);
         }
         }
         #lanyard_root * :is(.ketch-animate-bannerMobileBottomAnimate) {
         animation: ketch-bannerMobileBottomAnimate 0.4s forwards;
         }
         @keyframes ketch-modalAnimatedLeft {
         from {
         left: -900px;
         }
         to {
         left: 4px;
         }
         }
         #lanyard_root * :is(.ketch-animate-modalAnimatedLeft) {
         animation: ketch-modalAnimatedLeft 0.4s forwards;
         }
         @keyframes ketch-modalAnimatedRight {
         from {
         right: -900px;
         }
         to {
         right: 4px;
         }
         }
         #lanyard_root * :is(.ketch-animate-modalAnimatedRight) {
         animation: ketch-modalAnimatedRight 0.4s forwards;
         }
         #lanyard_root * :is(.ketch-animate-none) {
         animation: none;
         }
         @keyframes ketch-pulseOpacity {
         0%, 100% {
         opacity: 0.3;
         }
         50% {
         opacity: 1;
         }
         }
         #lanyard_root * :is(.ketch-animate-pulseOpacity) {
         animation: ketch-pulseOpacity 2s ease-in-out infinite;
         }
         #lanyard_root * :is(.ketch-cursor-not-allowed) {
         cursor: not-allowed;
         }
         #lanyard_root * :is(.ketch-cursor-pointer) {
         cursor: pointer;
         }
         #lanyard_root * :is(.ketch-select-none) {
         -webkit-user-select: none;
         -moz-user-select: none;
         user-select: none;
         }
         #lanyard_root * :is(.ketch-resize-none) {
         resize: none;
         }
         #lanyard_root * :is(.ketch-list-disc) {
         list-style-type: disc;
         }
         #lanyard_root * :is(.ketch-list-none) {
         list-style-type: none;
         }
         #lanyard_root * :is(.ketch-appearance-none) {
         -webkit-appearance: none;
         -moz-appearance: none;
         appearance: none;
         }
         #lanyard_root * :is(.ketch-auto-rows-min) {
         grid-auto-rows: min-content;
         }
         #lanyard_root * :is(.ketch-grid-cols-1) {
         grid-template-columns: repeat(1, minmax(0, 1fr));
         }
         #lanyard_root * :is(.ketch-grid-cols-12) {
         grid-template-columns: repeat(12, minmax(0, 1fr));
         }
         #lanyard_root * :is(.ketch-grid-cols-2) {
         grid-template-columns: repeat(2, minmax(0, 1fr));
         }
         #lanyard_root * :is(.ketch-grid-rows-1) {
         grid-template-rows: repeat(1, minmax(0, 1fr));
         }
         #lanyard_root * :is(.ketch-grid-rows-\[auto_auto_1fr\]) {
         grid-template-rows: auto auto 1fr;
         }
         #lanyard_root * :is(.ketch-flex-row) {
         flex-direction: row;
         }
         #lanyard_root * :is(.ketch-flex-col) {
         flex-direction: column;
         }
         #lanyard_root * :is(.ketch-flex-col-reverse) {
         flex-direction: column-reverse;
         }
         #lanyard_root * :is(.ketch-flex-wrap) {
         flex-wrap: wrap;
         }
         #lanyard_root * :is(.ketch-items-start) {
         align-items: flex-start;
         }
         #lanyard_root * :is(.ketch-items-end) {
         align-items: flex-end;
         }
         #lanyard_root * :is(.\!ketch-items-center) {
         align-items: center !important;
         }
         #lanyard_root * :is(.ketch-items-center) {
         align-items: center;
         }
         #lanyard_root * :is(.ketch-items-stretch) {
         align-items: stretch;
         }
         #lanyard_root * :is(.\!ketch-justify-start) {
         justify-content: flex-start !important;
         }
         #lanyard_root * :is(.ketch-justify-start) {
         justify-content: flex-start;
         }
         #lanyard_root * :is(.ketch-justify-end) {
         justify-content: flex-end;
         }
         #lanyard_root * :is(.ketch-justify-center) {
         justify-content: center;
         }
         #lanyard_root * :is(.\!ketch-justify-between) {
         justify-content: space-between !important;
         }
         #lanyard_root * :is(.ketch-justify-between) {
         justify-content: space-between;
         }
         #lanyard_root * :is(.ketch-justify-stretch) {
         justify-content: stretch;
         }
         #lanyard_root * :is(.ketch-justify-items-center) {
         justify-items: center;
         }
         #lanyard_root * :is(.ketch-gap-1) {
         gap: 4px;
         }
         #lanyard_root * :is(.ketch-gap-1\.5) {
         gap: 6px;
         }
         #lanyard_root * :is(.ketch-gap-12) {
         gap: 48px;
         }
         #lanyard_root * :is(.ketch-gap-2) {
         gap: 8px;
         }
         #lanyard_root * :is(.ketch-gap-3) {
         gap: 12px;
         }
         #lanyard_root * :is(.ketch-gap-4) {
         gap: 16px;
         }
         #lanyard_root * :is(.ketch-gap-5) {
         gap: 20px;
         }
         #lanyard_root * :is(.ketch-gap-6) {
         gap: 24px;
         }
         #lanyard_root * :is(.ketch-gap-8) {
         gap: 32px;
         }
         #lanyard_root * :is(.ketch-gap-\[-2px\]) {
         gap: -2px;
         }
         #lanyard_root * :is(.ketch-self-start) {
         align-self: flex-start;
         }
         #lanyard_root * :is(.ketch-self-end) {
         align-self: flex-end;
         }
         #lanyard_root * :is(.ketch-overflow-auto) {
         overflow: auto;
         }
         #lanyard_root * :is(.ketch-overflow-hidden) {
         overflow: hidden;
         }
         #lanyard_root * :is(.ketch-overflow-scroll) {
         overflow: scroll;
         }
         #lanyard_root * :is(.ketch-overflow-y-auto) {
         overflow-y: auto;
         }
         #lanyard_root * :is(.ketch-overflow-y-scroll) {
         overflow-y: scroll;
         }
         #lanyard_root * :is(.ketch-truncate) {
         overflow: hidden;
         text-overflow: ellipsis;
         white-space: nowrap;
         }
         #lanyard_root * :is(.ketch-whitespace-nowrap) {
         white-space: nowrap;
         }
         #lanyard_root * :is(.ketch-whitespace-break-spaces) {
         white-space: break-spaces;
         }
         #lanyard_root * :is(.ketch-break-words) {
         overflow-wrap: break-word;
         }
         #lanyard_root * :is(.\!ketch-rounded-\[--k-banner-buttons-primary-cornerRadius\]) {
         border-radius: var(--k-banner-buttons-primary-cornerRadius) !important;
         }
         #lanyard_root * :is(.\!ketch-rounded-\[--k-banner-buttons-secondary-cornerRadius\]) {
         border-radius: var(--k-banner-buttons-secondary-cornerRadius) !important;
         }
         #lanyard_root * :is(.\!ketch-rounded-\[--k-banner-buttons-tertiary-cornerRadius\]) {
         border-radius: var(--k-banner-buttons-tertiary-cornerRadius) !important;
         }
         #lanyard_root * :is(.\!ketch-rounded-\[--k-banner-container-cornerRadius\]) {
         border-radius: var(--k-banner-container-cornerRadius) !important;
         }
         #lanyard_root * :is(.\!ketch-rounded-\[--k-banner-header-returnButton-cornerRadius\]) {
         border-radius: var(--k-banner-header-returnButton-cornerRadius) !important;
         }
         #lanyard_root * :is(.\!ketch-rounded-\[--k-modal-container-cornerRadius\]) {
         border-radius: var(--k-modal-container-cornerRadius) !important;
         }
         #lanyard_root * :is(.\!ketch-rounded-\[--k-modal-footer-actionButton-cornerRadius\]) {
         border-radius: var(--k-modal-footer-actionButton-cornerRadius) !important;
         }
         #lanyard_root * :is(.\!ketch-rounded-\[--k-modal-header-returnButton-cornerRadius\]) {
         border-radius: var(--k-modal-header-returnButton-cornerRadius) !important;
         }
         #lanyard_root * :is(.\!ketch-rounded-\[--k-modal-purposeList-purposeListItems-purposeCornerRadius\]) {
         border-radius: var(--k-modal-purposeList-purposeListItems-purposeCornerRadius) !important;
         }
         #lanyard_root * :is(.\!ketch-rounded-\[--k-modal-purposeListHeader-acceptAllButton-cornerRadius\]) {
         border-radius: var(--k-modal-purposeListHeader-acceptAllButton-cornerRadius) !important;
         }
         #lanyard_root * :is(.\!ketch-rounded-\[--k-modal-purposeListHeader-rejectAllButton-cornerRadius\]) {
         border-radius: var(--k-modal-purposeListHeader-rejectAllButton-cornerRadius) !important;
         }
         #lanyard_root * :is(.\!ketch-rounded-\[--k-preference-exitButton-cornerRadius\]) {
         border-radius: var(--k-preference-exitButton-cornerRadius) !important;
         }
         #lanyard_root * :is(.\!ketch-rounded-\[--k-preference-navigation-layout-cornerRadius\]) {
         border-radius: var(--k-preference-navigation-layout-cornerRadius) !important;
         }
         #lanyard_root * :is(.\!ketch-rounded-\[--k-preference-navigation-layout-item-cornerRadius\]) {
         border-radius: var(--k-preference-navigation-layout-item-cornerRadius) !important;
         }
         #lanyard_root * :is(.\!ketch-rounded-\[--k-preference-tabs-purposes-footer-actionButton-cornerRadius\]) {
         border-radius: var(--k-preference-tabs-purposes-footer-actionButton-cornerRadius) !important;
         }
         #lanyard_root * :is(.\!ketch-rounded-\[--k-preference-tabs-purposes-purposeList-purposeListItems-purposeCornerRadius\]) {
         border-radius: var(--k-preference-tabs-purposes-purposeList-purposeListItems-purposeCornerRadius) !important;
         }
         #lanyard_root * :is(.\!ketch-rounded-\[--k-preference-tabs-purposes-purposeListHeader-acceptAllButton-cornerRadius\]) {
         border-radius: var(--k-preference-tabs-purposes-purposeListHeader-acceptAllButton-cornerRadius) !important;
         }
         #lanyard_root * :is(.\!ketch-rounded-\[--k-preference-tabs-purposes-purposeListHeader-rejectAllButton-cornerRadius\]) {
         border-radius: var(--k-preference-tabs-purposes-purposeListHeader-rejectAllButton-cornerRadius) !important;
         }
         #lanyard_root * :is(.\!ketch-rounded-\[--k-preference-tabs-requests-home-dsrPortalLink-cornerRadius\]) {
         border-radius: var(--k-preference-tabs-requests-home-dsrPortalLink-cornerRadius) !important;
         }
         #lanyard_root * :is(.\!ketch-rounded-\[--k-preference-tabs-requests-home-rightsList-item-cornerRadius\]) {
         border-radius: var(--k-preference-tabs-requests-home-rightsList-item-cornerRadius) !important;
         }
         #lanyard_root * :is(.\!ketch-rounded-\[--k-preference-tabs-requests-rightForm-actionButton-cornerRadius\]) {
         border-radius: var(--k-preference-tabs-requests-rightForm-actionButton-cornerRadius) !important;
         }
         #lanyard_root * :is(.\!ketch-rounded-\[--k-preference-tabs-requests-rightForm-form-fields-cornerRadius\]) {
         border-radius: var(--k-preference-tabs-requests-rightForm-form-fields-cornerRadius) !important;
         }
         #lanyard_root * :is(.\!ketch-rounded-\[--k-preference-tabs-requests-rightForm-header-returnButton-cornerRadius\]) {
         border-radius: var(--k-preference-tabs-requests-rightForm-header-returnButton-cornerRadius) !important;
         }
         #lanyard_root * :is(.\!ketch-rounded-\[--k-preference-tabs-requests-submitted-footer-actionButton-cornerRadius\]) {
         border-radius: var(--k-preference-tabs-requests-submitted-footer-actionButton-cornerRadius) !important;
         }
         #lanyard_root * :is(.\!ketch-rounded-\[--k-preference-tabs-requests-submitted-header-returnButton-cornerRadius\]) {
         border-radius: var(--k-preference-tabs-requests-submitted-header-returnButton-cornerRadius) !important;
         }
         #lanyard_root * :is(.\!ketch-rounded-\[--k-preference-tabs-subscriptions-footer-actionButton-cornerRadius\]) {
         border-radius: var(--k-preference-tabs-subscriptions-footer-actionButton-cornerRadius) !important;
         }
         #lanyard_root * :is(.\!ketch-rounded-\[--k-preference-tabs-subscriptions-list-layout-cornerRadius\]) {
         border-radius: var(--k-preference-tabs-subscriptions-list-layout-cornerRadius) !important;
         }
         #lanyard_root * :is(.\!ketch-rounded-\[--k-preference-tabs-subscriptions-unsubscribeAll-cornerRadius\]) {
         border-radius: var(--k-preference-tabs-subscriptions-unsubscribeAll-cornerRadius) !important;
         }
         #lanyard_root * :is(.\!ketch-rounded-none) {
         border-radius: 0px !important;
         }
         #lanyard_root * :is(.ketch-rounded-3xl) {
         border-radius: 1.5rem;
         }
         #lanyard_root * :is(.ketch-rounded-\[3px\]) {
         border-radius: 3px;
         }
         #lanyard_root * :is(.ketch-rounded-full) {
         border-radius: 9999px;
         }
         #lanyard_root * :is(.ketch-rounded-inherit) {
         border-radius: inherit;
         }
         #lanyard_root * :is(.ketch-rounded-md) {
         border-radius: 0.375rem;
         }
         #lanyard_root * :is(.ketch-rounded-none) {
         border-radius: 0px;
         }
         #lanyard_root * :is(.ketch-rounded-xl) {
         border-radius: 0.75rem;
         }
         #lanyard_root * :is(.\!ketch-rounded-tl-\[--k-preference-navigation-layout-item-cornerRadius\]) {
         border-top-left-radius: var(--k-preference-navigation-layout-item-cornerRadius) !important;
         }
         #lanyard_root * :is(.\!ketch-rounded-tr-\[--k-preference-navigation-layout-item-cornerRadius\]) {
         border-top-right-radius: var(--k-preference-navigation-layout-item-cornerRadius) !important;
         }
         #lanyard_root * :is(.ketch-border) {
         border-width: 1px;
         }
         #lanyard_root * :is(.ketch-border-0) {
         border-width: 0px;
         }
         #lanyard_root * :is(.ketch-border-2) {
         border-width: 2px;
         }
         #lanyard_root * :is(.ketch-border-\[1\.5px\]) {
         border-width: 1.5px;
         }
         #lanyard_root * :is(.ketch-border-x-0) {
         border-left-width: 0px;
         border-right-width: 0px;
         }
         #lanyard_root * :is(.ketch-border-b) {
         border-bottom-width: 1px;
         }
         #lanyard_root * :is(.ketch-border-l-0) {
         border-left-width: 0px;
         }
         #lanyard_root * :is(.ketch-border-r-0) {
         border-right-width: 0px;
         }
         #lanyard_root * :is(.ketch-border-t-0) {
         border-top-width: 0px;
         }
         #lanyard_root * :is(.ketch-border-solid) {
         border-style: solid;
         }
         #lanyard_root * :is(.ketch-border-none) {
         border-style: none;
         }
         #lanyard_root * :is(.\!ketch-border-\[--k-banner-buttons-primary-background-color\]) {
         border-color: var(--k-banner-buttons-primary-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-border-\[--k-banner-buttons-secondary-background-color\]) {
         border-color: var(--k-banner-buttons-secondary-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-border-\[--k-banner-buttons-tertiary-background-color\]) {
         border-color: var(--k-banner-buttons-tertiary-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-border-\[--k-modal-footer-actionButton-background-color\]) {
         border-color: var(--k-modal-footer-actionButton-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-border-\[--k-modal-purposeList-purposeListItems-purposeOutline-color\]) {
         border-color: var(--k-modal-purposeList-purposeListItems-purposeOutline-color) !important;
         }
         #lanyard_root * :is(.\!ketch-border-\[--k-modal-purposeList-switchButtons-off-background-color\]) {
         border-color: var(--k-modal-purposeList-switchButtons-off-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-border-\[--k-modal-purposeListHeader-acceptAllButton-background-color\]) {
         border-color: var(--k-modal-purposeListHeader-acceptAllButton-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-border-\[--k-modal-purposeListHeader-rejectAllButton-background-color\]) {
         border-color: var(--k-modal-purposeListHeader-rejectAllButton-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-border-\[--k-preference-tabs-purposes-footer-actionButton-background-color\]) {
         border-color: var(--k-preference-tabs-purposes-footer-actionButton-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-border-\[--k-preference-tabs-purposes-purposeList-purposeListItems-purposeOutline-color\]) {
         border-color: var(--k-preference-tabs-purposes-purposeList-purposeListItems-purposeOutline-color) !important;
         }
         #lanyard_root * :is(.\!ketch-border-\[--k-preference-tabs-purposes-purposeList-switchButtons-off-background-color\]) {
         border-color: var(--k-preference-tabs-purposes-purposeList-switchButtons-off-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-border-\[--k-preference-tabs-purposes-purposeListHeader-acceptAllButton-background-color\]) {
         border-color: var(--k-preference-tabs-purposes-purposeListHeader-acceptAllButton-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-border-\[--k-preference-tabs-purposes-purposeListHeader-rejectAllButton-background-color\]) {
         border-color: var(--k-preference-tabs-purposes-purposeListHeader-rejectAllButton-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-border-\[--k-preference-tabs-requests-rightForm-actionButton-background-color\]) {
         border-color: var(--k-preference-tabs-requests-rightForm-actionButton-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-border-\[--k-preference-tabs-requests-rightForm-form-checkboxes-error-background-color\]) {
         border-color: var(--k-preference-tabs-requests-rightForm-form-checkboxes-error-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-border-\[--k-preference-tabs-requests-rightForm-form-checkboxes-unselected-background-color\]) {
         border-color: var(--k-preference-tabs-requests-rightForm-form-checkboxes-unselected-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-border-\[--k-preference-tabs-requests-rightForm-form-fields-error-outline-color\]) {
         border-color: var(--k-preference-tabs-requests-rightForm-form-fields-error-outline-color) !important;
         }
         #lanyard_root * :is(.\!ketch-border-\[--k-preference-tabs-requests-rightForm-form-fields-inactive-outline-color\]) {
         border-color: var(--k-preference-tabs-requests-rightForm-form-fields-inactive-outline-color) !important;
         }
         #lanyard_root * :is(.\!ketch-border-\[--k-preference-tabs-requests-submitted-footer-actionButton-outline-color\]) {
         border-color: var(--k-preference-tabs-requests-submitted-footer-actionButton-outline-color) !important;
         }
         #lanyard_root * :is(.\!ketch-border-\[--k-preference-tabs-subscriptions-footer-actionButton-background-color\]) {
         border-color: var(--k-preference-tabs-subscriptions-footer-actionButton-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-border-\[--k-preference-tabs-subscriptions-list-checkbox-unselected-background-color\]) {
         border-color: var(--k-preference-tabs-subscriptions-list-checkbox-unselected-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-border-\[--k-preference-tabs-subscriptions-list-layout-background-color\]) {
         border-color: var(--k-preference-tabs-subscriptions-list-layout-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-border-\[--k-tabs-subscriptions-list-checkbox-error-text-color\]) {
         border-color: var(--k-tabs-subscriptions-list-checkbox-error-text-color) !important;
         }
         #lanyard_root * :is(.ketch-border-black) {
         --tw-border-opacity: 1;
         border-color: rgb(0 0 0 / var(--tw-border-opacity));
         }
         #lanyard_root * :is(.ketch-border-gray-300) {
         --tw-border-opacity: 1;
         border-color: rgb(209 213 219 / var(--tw-border-opacity));
         }
         #lanyard_root * :is(.ketch-border-gray-400) {
         --tw-border-opacity: 1;
         border-color: rgb(156 163 175 / var(--tw-border-opacity));
         }
         #lanyard_root * :is(.ketch-border-ketch-error) {
         border-color: rgba(252, 60, 96, 1);
         }
         #lanyard_root * :is(.\!ketch-bg-\[--k-banner-buttons-primary-background-color\]) {
         background-color: var(--k-banner-buttons-primary-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-bg-\[--k-banner-buttons-secondary-background-color\]) {
         background-color: var(--k-banner-buttons-secondary-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-bg-\[--k-banner-buttons-tertiary-background-color\]) {
         background-color: var(--k-banner-buttons-tertiary-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-bg-\[--k-banner-container-backdrop-background-color\]) {
         background-color: var(--k-banner-container-backdrop-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-bg-\[--k-banner-container-background-color\]) {
         background-color: var(--k-banner-container-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-bg-\[--k-banner-header-returnButton-background-color\]) {
         background-color: var(--k-banner-header-returnButton-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-bg-\[--k-modal-container-backdrop-background-color\]) {
         background-color: var(--k-modal-container-backdrop-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-bg-\[--k-modal-container-background-color\]) {
         background-color: var(--k-modal-container-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-bg-\[--k-modal-footer-actionButton-background-color\]) {
         background-color: var(--k-modal-footer-actionButton-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-bg-\[--k-modal-footer-background-color\]) {
         background-color: var(--k-modal-footer-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-bg-\[--k-modal-header-background-color\]) {
         background-color: var(--k-modal-header-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-bg-\[--k-modal-header-returnButton-background-color\]) {
         background-color: var(--k-modal-header-returnButton-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-bg-\[--k-modal-purposeList-purposeListItems-purposeFill-color\]) {
         background-color: var(--k-modal-purposeList-purposeListItems-purposeFill-color) !important;
         }
         #lanyard_root * :is(.\!ketch-bg-\[--k-modal-purposeList-switchButtons-off-background-color\]) {
         background-color: var(--k-modal-purposeList-switchButtons-off-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-bg-\[--k-modal-purposeListHeader-acceptAllButton-background-color\]) {
         background-color: var(--k-modal-purposeListHeader-acceptAllButton-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-bg-\[--k-modal-purposeListHeader-rejectAllButton-background-color\]) {
         background-color: var(--k-modal-purposeListHeader-rejectAllButton-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-bg-\[--k-preference-container-background-color\]) {
         background-color: var(--k-preference-container-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-bg-\[--k-preference-exitButton-background-color\]) {
         background-color: var(--k-preference-exitButton-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-bg-\[--k-preference-header-background-color\]) {
         background-color: var(--k-preference-header-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-bg-\[--k-preference-navigation-layout-background-color\]) {
         background-color: var(--k-preference-navigation-layout-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-bg-\[--k-preference-navigation-layout-item-selectedBackground-color\]) {
         background-color: var(--k-preference-navigation-layout-item-selectedBackground-color) !important;
         }
         #lanyard_root * :is(.\!ketch-bg-\[--k-preference-navigation-layout-item-unselectedBackground-color\]) {
         background-color: var(--k-preference-navigation-layout-item-unselectedBackground-color) !important;
         }
         #lanyard_root * :is(.\!ketch-bg-\[--k-preference-tabs-purposes-footer-actionButton-background-color\]) {
         background-color: var(--k-preference-tabs-purposes-footer-actionButton-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-bg-\[--k-preference-tabs-purposes-footer-background-color\]) {
         background-color: var(--k-preference-tabs-purposes-footer-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-bg-\[--k-preference-tabs-purposes-purposeList-purposeListItems-purposeFill-color\]) {
         background-color: var(--k-preference-tabs-purposes-purposeList-purposeListItems-purposeFill-color) !important;
         }
         #lanyard_root * :is(.\!ketch-bg-\[--k-preference-tabs-purposes-purposeList-switchButtons-off-background-color\]) {
         background-color: var(--k-preference-tabs-purposes-purposeList-switchButtons-off-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-bg-\[--k-preference-tabs-purposes-purposeListHeader-acceptAllButton-background-color\]) {
         background-color: var(--k-preference-tabs-purposes-purposeListHeader-acceptAllButton-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-bg-\[--k-preference-tabs-purposes-purposeListHeader-rejectAllButton-background-color\]) {
         background-color: var(--k-preference-tabs-purposes-purposeListHeader-rejectAllButton-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-bg-\[--k-preference-tabs-requests-home-dsrPortalLink-background-color\]) {
         background-color: var(--k-preference-tabs-requests-home-dsrPortalLink-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-bg-\[--k-preference-tabs-requests-home-rightsList-item-background-color\]) {
         background-color: var(--k-preference-tabs-requests-home-rightsList-item-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-bg-\[--k-preference-tabs-requests-rightForm-actionButton-background-color\]) {
         background-color: var(--k-preference-tabs-requests-rightForm-actionButton-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-bg-\[--k-preference-tabs-requests-rightForm-form-checkboxes-unselected-background-color\]) {
         background-color: var(--k-preference-tabs-requests-rightForm-form-checkboxes-unselected-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-bg-\[--k-preference-tabs-requests-rightForm-form-fields-error-background-color\]) {
         background-color: var(--k-preference-tabs-requests-rightForm-form-fields-error-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-bg-\[--k-preference-tabs-requests-rightForm-form-fields-inactive-background-color\]) {
         background-color: var(--k-preference-tabs-requests-rightForm-form-fields-inactive-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-bg-\[--k-preference-tabs-requests-rightForm-header-returnButton-background-color\]) {
         background-color: var(--k-preference-tabs-requests-rightForm-header-returnButton-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-bg-\[--k-preference-tabs-requests-submitted-footer-actionButton-background-color\]) {
         background-color: var(--k-preference-tabs-requests-submitted-footer-actionButton-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-bg-\[--k-preference-tabs-requests-submitted-footer-background-color\]) {
         background-color: var(--k-preference-tabs-requests-submitted-footer-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-bg-\[--k-preference-tabs-requests-submitted-header-returnButton-background-color\]) {
         background-color: var(--k-preference-tabs-requests-submitted-header-returnButton-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-bg-\[--k-preference-tabs-subscriptions-footer-actionButton-background-color\]) {
         background-color: var(--k-preference-tabs-subscriptions-footer-actionButton-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-bg-\[--k-preference-tabs-subscriptions-footer-background-color\]) {
         background-color: var(--k-preference-tabs-subscriptions-footer-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-bg-\[--k-preference-tabs-subscriptions-list-checkbox-unselected-background-color\]) {
         background-color: var(--k-preference-tabs-subscriptions-list-checkbox-unselected-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-bg-\[--k-preference-tabs-subscriptions-list-layout-background-color\]) {
         background-color: var(--k-preference-tabs-subscriptions-list-layout-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-bg-\[--k-preference-tabs-subscriptions-list-switchButton-off-background-color\]) {
         background-color: var(--k-preference-tabs-subscriptions-list-switchButton-off-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-bg-\[--k-preference-tabs-subscriptions-unsubscribeAll-background-color\]) {
         background-color: var(--k-preference-tabs-subscriptions-unsubscribeAll-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-bg-\[--k-preference-tabs-subscriptions-unsubscribeAll-switchButton-off-background-color\]) {
         background-color: var(--k-preference-tabs-subscriptions-unsubscribeAll-switchButton-off-background-color) !important;
         }
         #lanyard_root * :is(.\!ketch-bg-blue-400) {
         --tw-bg-opacity: 1 !important;
         background-color: rgb(96 165 250 / var(--tw-bg-opacity)) !important;
         }
         #lanyard_root * :is(.ketch-bg-gray-200) {
         --tw-bg-opacity: 1;
         background-color: rgb(229 231 235 / var(--tw-bg-opacity));
         }
         #lanyard_root * :is(.ketch-bg-gray-300) {
         --tw-bg-opacity: 1;
         background-color: rgb(209 213 219 / var(--tw-bg-opacity));
         }
         #lanyard_root * :is(.ketch-bg-gray-500) {
         --tw-bg-opacity: 1;
         background-color: rgb(107 114 128 / var(--tw-bg-opacity));
         }
         #lanyard_root * :is(.ketch-bg-inherit) {
         background-color: inherit;
         }
         #lanyard_root * :is(.ketch-bg-ketch-black-black-10) {
         background-color: rgba(7, 26, 36, 0.1);
         }
         #lanyard_root * :is(.ketch-bg-ketch-black-black-100) {
         background-color: rgba(7, 26, 36, 1);
         }
         #lanyard_root * :is(.ketch-bg-ketch-white-24) {
         background-color: rgba(255, 255, 255, 0.24);
         }
         #lanyard_root * :is(.ketch-bg-orange-200) {
         --tw-bg-opacity: 1;
         background-color: rgb(254 215 170 / var(--tw-bg-opacity));
         }
         #lanyard_root * :is(.ketch-bg-orange-400) {
         --tw-bg-opacity: 1;
         background-color: rgb(251 146 60 / var(--tw-bg-opacity));
         }
         #lanyard_root * :is(.ketch-bg-orange-600) {
         --tw-bg-opacity: 1;
         background-color: rgb(234 88 12 / var(--tw-bg-opacity));
         }
         #lanyard_root * :is(.ketch-bg-slate-200) {
         --tw-bg-opacity: 1;
         background-color: rgb(226 232 240 / var(--tw-bg-opacity));
         }
         #lanyard_root * :is(.ketch-bg-transparent) {
         background-color: transparent;
         }
         #lanyard_root * :is(.ketch-bg-white) {
         --tw-bg-opacity: 1;
         background-color: rgb(255 255 255 / var(--tw-bg-opacity));
         }
         #lanyard_root * :is(.ketch-bg-gradient-to-r) {
         background-image: linear-gradient(to right, var(--tw-gradient-stops));
         }
         #lanyard_root * :is(.ketch-from-states-focus-darken) {
         --tw-gradient-from: rgba(7, 26, 36, 0.08) var(--tw-gradient-from-position);
         --tw-gradient-to: rgba(7, 26, 36, 0) var(--tw-gradient-to-position);
         --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
         }
         #lanyard_root * :is(.ketch-from-states-selected-darken) {
         --tw-gradient-from: rgba(7, 26, 36, 0.06) var(--tw-gradient-from-position);
         --tw-gradient-to: rgba(7, 26, 36, 0) var(--tw-gradient-to-position);
         --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
         }
         #lanyard_root * :is(.ketch-to-states-focus-darken) {
         --tw-gradient-to: rgba(7, 26, 36, 0.08) var(--tw-gradient-to-position);
         }
         #lanyard_root * :is(.ketch-to-states-selected-darken) {
         --tw-gradient-to: rgba(7, 26, 36, 0.06) var(--tw-gradient-to-position);
         }
         #lanyard_root * :is(.\!ketch-fill-\[--k-banner-header-returnButton-icon-color\]) {
         fill: var(--k-banner-header-returnButton-icon-color) !important;
         }
         #lanyard_root * :is(.\!ketch-fill-\[--k-modal-header-returnButton-icon-color\]) {
         fill: var(--k-modal-header-returnButton-icon-color) !important;
         }
         #lanyard_root * :is(.\!ketch-fill-\[--k-modal-purposeList-purposeListItems-arrowIcon-color\]) {
         fill: var(--k-modal-purposeList-purposeListItems-arrowIcon-color) !important;
         }
         #lanyard_root * :is(.\!ketch-fill-\[--k-modal-purposeList-purposeListItems-purposeLinks-color\]) {
         fill: var(--k-modal-purposeList-purposeListItems-purposeLinks-color) !important;
         }
         #lanyard_root * :is(.\!ketch-fill-\[--k-modal-purposeListHeader-title-color\]) {
         fill: var(--k-modal-purposeListHeader-title-color) !important;
         }
         #lanyard_root * :is(.\!ketch-fill-\[--k-preference-exitButton-iconColor-color\]) {
         fill: var(--k-preference-exitButton-iconColor-color) !important;
         }
         #lanyard_root * :is(.\!ketch-fill-\[--k-preference-navigation-layout-item-arrowIcon-color\]) {
         fill: var(--k-preference-navigation-layout-item-arrowIcon-color) !important;
         }
         #lanyard_root * :is(.\!ketch-fill-\[--k-preference-tabs-purposes-purposeList-purposeListItems-arrowIcon-color\]) {
         fill: var(--k-preference-tabs-purposes-purposeList-purposeListItems-arrowIcon-color) !important;
         }
         #lanyard_root * :is(.\!ketch-fill-\[--k-preference-tabs-purposes-purposeList-purposeListItems-purposeLinks-color\]) {
         fill: var(--k-preference-tabs-purposes-purposeList-purposeListItems-purposeLinks-color) !important;
         }
         #lanyard_root * :is(.\!ketch-fill-\[--k-preference-tabs-purposes-purposeListHeader-title-color\]) {
         fill: var(--k-preference-tabs-purposes-purposeListHeader-title-color) !important;
         }
         #lanyard_root * :is(.\!ketch-fill-\[--k-preference-tabs-requests-form-dropdown-arrow-icon-color\]) {
         fill: var(--k-preference-tabs-requests-form-dropdown-arrow-icon-color) !important;
         }
         #lanyard_root * :is(.\!ketch-fill-\[--k-preference-tabs-requests-home-dsrPortalLink-arrowIcon-color\]) {
         fill: var(--k-preference-tabs-requests-home-dsrPortalLink-arrowIcon-color) !important;
         }
         #lanyard_root * :is(.\!ketch-fill-\[--k-preference-tabs-requests-home-rightsList-item-arrowIcon-color\]) {
         fill: var(--k-preference-tabs-requests-home-rightsList-item-arrowIcon-color) !important;
         }
         #lanyard_root * :is(.\!ketch-fill-\[--k-preference-tabs-requests-rightForm-header-returnButton-icon-color\]) {
         fill: var(--k-preference-tabs-requests-rightForm-header-returnButton-icon-color) !important;
         }
         #lanyard_root * :is(.\!ketch-fill-\[--k-preference-tabs-requests-submitted-footer-actionButton-icon-color\]) {
         fill: var(--k-preference-tabs-requests-submitted-footer-actionButton-icon-color) !important;
         }
         #lanyard_root * :is(.\!ketch-fill-\[--k-preference-tabs-requests-submitted-header-returnButton-icon-color\]) {
         fill: var(--k-preference-tabs-requests-submitted-header-returnButton-icon-color) !important;
         }
         #lanyard_root * :is(.\!ketch-fill-\[--k-preference-tabs-welcome-quickLinks-link-color\]) {
         fill: var(--k-preference-tabs-welcome-quickLinks-link-color) !important;
         }
         #lanyard_root * :is(.ketch-fill-\[--k-preference-tabs-requests-form-error-text-color\]) {
         fill: var(--k-preference-tabs-requests-form-error-text-color);
         }
         #lanyard_root * :is(.ketch-fill-\[--k-preference-welcome-quickLinks-link-color\]) {
         fill: var(--k-preference-welcome-quickLinks-link-color);
         }
         #lanyard_root * :is(.ketch-fill-black) {
         fill: #000;
         }
         #lanyard_root * :is(.ketch-fill-inherit) {
         fill: inherit;
         }
         #lanyard_root * :is(.ketch-fill-ketch-black-black-100) {
         fill: rgba(7, 26, 36, 1);
         }
         #lanyard_root * :is(.ketch-fill-white) {
         fill: #fff;
         }
         #lanyard_root * :is(.\!ketch-stroke-\[--k-preference-tabs-requests-form-dropdown-arrow-icon-color\]) {
         stroke: var(--k-preference-tabs-requests-form-dropdown-arrow-icon-color) !important;
         }
         #lanyard_root * :is(.ketch-stroke-\[--k-preference-tabs-requests-form-error-text-color\]) {
         stroke: var(--k-preference-tabs-requests-form-error-text-color);
         }
         #lanyard_root * :is(.ketch-stroke-black) {
         stroke: #000;
         }
         #lanyard_root * :is(.ketch-stroke-white) {
         stroke: #fff;
         }
         #lanyard_root * :is(.ketch-object-contain) {
         -o-object-fit: contain;
         object-fit: contain;
         }
         #lanyard_root * :is(.\!ketch-p-0) {
         padding: 0 !important;
         }
         #lanyard_root * :is(.\!ketch-p-4) {
         padding: 16px !important;
         }
         #lanyard_root * :is(.ketch-p-0) {
         padding: 0;
         }
         #lanyard_root * :is(.ketch-p-1) {
         padding: 4px;
         }
         #lanyard_root * :is(.ketch-p-3) {
         padding: 12px;
         }
         #lanyard_root * :is(.ketch-p-4) {
         padding: 16px;
         }
         #lanyard_root * :is(.ketch-px-0) {
         padding-left: 0;
         padding-right: 0;
         }
         #lanyard_root * :is(.ketch-px-1) {
         padding-left: 4px;
         padding-right: 4px;
         }
         #lanyard_root * :is(.ketch-px-2) {
         padding-left: 8px;
         padding-right: 8px;
         }
         #lanyard_root * :is(.ketch-px-3) {
         padding-left: 12px;
         padding-right: 12px;
         }
         #lanyard_root * :is(.ketch-px-4) {
         padding-left: 16px;
         padding-right: 16px;
         }
         #lanyard_root * :is(.ketch-px-5) {
         padding-left: 20px;
         padding-right: 20px;
         }
         #lanyard_root * :is(.ketch-px-6) {
         padding-left: 24px;
         padding-right: 24px;
         }
         #lanyard_root * :is(.ketch-px-\[1px\]) {
         padding-left: 1px;
         padding-right: 1px;
         }
         #lanyard_root * :is(.ketch-py-0) {
         padding-top: 0;
         padding-bottom: 0;
         }
         #lanyard_root * :is(.ketch-py-1) {
         padding-top: 4px;
         padding-bottom: 4px;
         }
         #lanyard_root * :is(.ketch-py-2) {
         padding-top: 8px;
         padding-bottom: 8px;
         }
         #lanyard_root * :is(.ketch-py-20) {
         padding-top: 80px;
         padding-bottom: 80px;
         }
         #lanyard_root * :is(.ketch-py-3) {
         padding-top: 12px;
         padding-bottom: 12px;
         }
         #lanyard_root * :is(.ketch-py-4) {
         padding-top: 16px;
         padding-bottom: 16px;
         }
         #lanyard_root * :is(.ketch-py-5) {
         padding-top: 20px;
         padding-bottom: 20px;
         }
         #lanyard_root * :is(.ketch-py-\[1px\]) {
         padding-top: 1px;
         padding-bottom: 1px;
         }
         #lanyard_root * :is(.ketch-pb-0) {
         padding-bottom: 0;
         }
         #lanyard_root * :is(.ketch-pb-1) {
         padding-bottom: 4px;
         }
         #lanyard_root * :is(.ketch-pb-2) {
         padding-bottom: 8px;
         }
         #lanyard_root * :is(.ketch-pb-3) {
         padding-bottom: 12px;
         }
         #lanyard_root * :is(.ketch-pb-4) {
         padding-bottom: 16px;
         }
         #lanyard_root * :is(.ketch-pb-6) {
         padding-bottom: 24px;
         }
         #lanyard_root * :is(.ketch-pb-8) {
         padding-bottom: 32px;
         }
         #lanyard_root * :is(.ketch-pb-\[--safe-area-inset-bottom\]) {
         padding-bottom: var(--safe-area-inset-bottom);
         }
         #lanyard_root * :is(.ketch-pb-\[max\(16px\,var\(--safe-area-inset-bottom\)\)\]) {
         padding-bottom: max(16px,var(--safe-area-inset-bottom));
         }
         #lanyard_root * :is(.ketch-pl-0) {
         padding-left: 0;
         }
         #lanyard_root * :is(.ketch-pl-1) {
         padding-left: 4px;
         }
         #lanyard_root * :is(.ketch-pl-10) {
         padding-left: 40px;
         }
         #lanyard_root * :is(.ketch-pl-2) {
         padding-left: 8px;
         }
         #lanyard_root * :is(.ketch-pl-3) {
         padding-left: 12px;
         }
         #lanyard_root * :is(.ketch-pl-4) {
         padding-left: 16px;
         }
         #lanyard_root * :is(.ketch-pl-5) {
         padding-left: 20px;
         }
         #lanyard_root * :is(.ketch-pl-6) {
         padding-left: 24px;
         }
         #lanyard_root * :is(.ketch-pl-\[--safe-area-inset-left\]) {
         padding-left: var(--safe-area-inset-left);
         }
         #lanyard_root * :is(.ketch-pl-\[calc\(16px\+var\(--safe-area-inset-left\)\)\]) {
         padding-left: calc(16px + var(--safe-area-inset-left));
         }
         #lanyard_root * :is(.ketch-pl-\[calc\(var\(--safe-area-inset-left\)\+4px\)\]) {
         padding-left: calc(var(--safe-area-inset-left) + 4px);
         }
         #lanyard_root * :is(.ketch-pl-\[max\(16px\,var\(--safe-area-inset-left\)\)\]) {
         padding-left: max(16px,var(--safe-area-inset-left));
         }
         #lanyard_root * :is(.ketch-pr-0) {
         padding-right: 0;
         }
         #lanyard_root * :is(.ketch-pr-14) {
         padding-right: 56px;
         }
         #lanyard_root * :is(.ketch-pr-3) {
         padding-right: 12px;
         }
         #lanyard_root * :is(.ketch-pr-6) {
         padding-right: 24px;
         }
         #lanyard_root * :is(.ketch-pr-8) {
         padding-right: 32px;
         }
         #lanyard_root * :is(.ketch-pr-\[--safe-area-inset-right\]) {
         padding-right: var(--safe-area-inset-right);
         }
         #lanyard_root * :is(.ketch-pr-\[calc\(16px\+var\(--safe-area-inset-right\)\)\]) {
         padding-right: calc(16px + var(--safe-area-inset-right));
         }
         #lanyard_root * :is(.ketch-pr-\[calc\(var\(--safe-area-inset-right\)\+4px\)\]) {
         padding-right: calc(var(--safe-area-inset-right) + 4px);
         }
         #lanyard_root * :is(.ketch-pr-\[max\(16px\,var\(--safe-area-inset-right\)\)\]) {
         padding-right: max(16px,var(--safe-area-inset-right));
         }
         #lanyard_root * :is(.ketch-pt-0) {
         padding-top: 0;
         }
         #lanyard_root * :is(.ketch-pt-0\.5) {
         padding-top: 2px;
         }
         #lanyard_root * :is(.ketch-pt-1) {
         padding-top: 4px;
         }
         #lanyard_root * :is(.ketch-pt-2) {
         padding-top: 8px;
         }
         #lanyard_root * :is(.ketch-pt-3) {
         padding-top: 12px;
         }
         #lanyard_root * :is(.ketch-pt-4) {
         padding-top: 16px;
         }
         #lanyard_root * :is(.ketch-pt-5) {
         padding-top: 20px;
         }
         #lanyard_root * :is(.ketch-pt-8) {
         padding-top: 32px;
         }
         #lanyard_root * :is(.ketch-pt-\[--safe-area-inset-top\]) {
         padding-top: var(--safe-area-inset-top);
         }
         #lanyard_root * :is(.ketch-pt-\[max\(16px\,var\(--safe-area-inset-top\)\)\]) {
         padding-top: max(16px,var(--safe-area-inset-top));
         }
         #lanyard_root * :is(.ketch-text-left) {
         text-align: left;
         }
         #lanyard_root * :is(.ketch-text-center) {
         text-align: center;
         }
         #lanyard_root * :is(.\!ketch-text-ketch-h2) {
         font-size: 20px !important;
         line-height: 30px !important;
         }
         #lanyard_root * :is(.\!ketch-text-ketch-l) {
         font-size: 14px !important;
         line-height: 21px !important;
         }
         #lanyard_root * :is(.ketch-text-ketch-h0) {
         font-size: 32px;
         line-height: 48px;
         }
         #lanyard_root * :is(.ketch-text-ketch-h1) {
         font-size: 28px;
         line-height: 39px;
         }
         #lanyard_root * :is(.ketch-text-ketch-h1-small) {
         font-size: 26px;
         line-height: 36.4px;
         }
         #lanyard_root * :is(.ketch-text-ketch-h2) {
         font-size: 20px;
         line-height: 30px;
         }
         #lanyard_root * :is(.ketch-text-ketch-h3) {
         font-size: 18px;
         line-height: 22.5px;
         }
         #lanyard_root * :is(.ketch-text-ketch-h4) {
         font-size: 16px;
         line-height: 24px;
         }
         #lanyard_root * :is(.ketch-text-ketch-l) {
         font-size: 14px;
         line-height: 21px;
         }
         #lanyard_root * :is(.ketch-text-ketch-m) {
         font-size: 13px;
         line-height: 16px;
         }
         #lanyard_root * :is(.ketch-text-ketch-s) {
         font-size: 12px;
         line-height: 15px;
         }
         #lanyard_root * :is(.ketch-text-ketch-xs) {
         font-size: 10px;
         line-height: 14.5px;
         }
         #lanyard_root * :is(.\!ketch-font-\[--k-banner-container-font\]) {
         font-weight: var(--k-banner-container-font) !important;
         }
         #lanyard_root * :is(.\!ketch-font-\[--k-modal-container-font\]) {
         font-weight: var(--k-modal-container-font) !important;
         }
         #lanyard_root * :is(.ketch-font-bold) {
         font-weight: 700;
         }
         #lanyard_root * :is(.ketch-font-medium) {
         font-weight: 500;
         }
         #lanyard_root * :is(.ketch-font-semibold) {
         font-weight: 600;
         }
         #lanyard_root * :is(.ketch-font-weight-inherit) {
         font-weight: inherit;
         }
         #lanyard_root * :is(.ketch-uppercase) {
         text-transform: uppercase;
         }
         #lanyard_root * :is(.ketch-italic) {
         font-style: italic;
         }
         #lanyard_root * :is(.ketch-leading-3) {
         line-height: .75rem;
         }
         #lanyard_root * :is(.ketch-tracking-\[-0\.12px\]) {
         letter-spacing: -0.12px;
         }
         #lanyard_root * :is(.ketch-tracking-\[-0\.13px\]) {
         letter-spacing: -0.13px;
         }
         #lanyard_root * :is(.ketch-tracking-\[-0\.14px\]) {
         letter-spacing: -0.14px;
         }
         #lanyard_root * :is(.ketch-tracking-\[-0\.16px\]) {
         letter-spacing: -0.16px;
         }
         #lanyard_root * :is(.ketch-tracking-\[-0\.18px\]) {
         letter-spacing: -0.18px;
         }
         #lanyard_root * :is(.ketch-tracking-\[-0\.1px\]) {
         letter-spacing: -0.1px;
         }
         #lanyard_root * :is(.ketch-tracking-\[-0\.26px\]) {
         letter-spacing: -0.26px;
         }
         #lanyard_root * :is(.ketch-tracking-\[-0\.28px\]) {
         letter-spacing: -0.28px;
         }
         #lanyard_root * :is(.ketch-tracking-\[-0\.2px\]) {
         letter-spacing: -0.2px;
         }
         #lanyard_root * :is(.ketch-tracking-\[-0\.64px\]) {
         letter-spacing: -0.64px;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-banner-buttons-primary-text-color\]) {
         color: var(--k-banner-buttons-primary-text-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-banner-buttons-secondary-text-color\]) {
         color: var(--k-banner-buttons-secondary-text-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-banner-buttons-tertiary-text-color\]) {
         color: var(--k-banner-buttons-tertiary-text-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-banner-description-link-color\]) {
         color: var(--k-banner-description-link-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-banner-description-text-color\]) {
         color: var(--k-banner-description-text-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-banner-header-returnButton-text-color\]) {
         color: var(--k-banner-header-returnButton-text-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-banner-header-title-color\]) {
         color: var(--k-banner-header-title-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-modal-description-link-color\]) {
         color: var(--k-modal-description-link-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-modal-description-text-color\]) {
         color: var(--k-modal-description-text-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-modal-description-title-color\]) {
         color: var(--k-modal-description-title-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-modal-footer-actionButton-text-color\]) {
         color: var(--k-modal-footer-actionButton-text-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-modal-header-returnButton-text-color\]) {
         color: var(--k-modal-header-returnButton-text-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-modal-header-title-color\]) {
         color: var(--k-modal-header-title-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-modal-purposeList-purposeListItems-arrowIcon-color\]) {
         color: var(--k-modal-purposeList-purposeListItems-arrowIcon-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-modal-purposeList-purposeListItems-purposeContent-color\]) {
         color: var(--k-modal-purposeList-purposeListItems-purposeContent-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-modal-purposeList-purposeListItems-purposeLinks-color\]) {
         color: var(--k-modal-purposeList-purposeListItems-purposeLinks-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-modal-purposeList-switchButtons-off-text-color\]) {
         color: var(--k-modal-purposeList-switchButtons-off-text-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-modal-purposeList-switchButtons-on-text-color\]) {
         color: var(--k-modal-purposeList-switchButtons-on-text-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-modal-purposeListHeader-acceptAllButton-text-color\]) {
         color: var(--k-modal-purposeListHeader-acceptAllButton-text-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-modal-purposeListHeader-rejectAllButton-text-color\]) {
         color: var(--k-modal-purposeListHeader-rejectAllButton-text-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-modal-purposeListHeader-title-color\]) {
         color: var(--k-modal-purposeListHeader-title-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-exitButton-text-color\]) {
         color: var(--k-preference-exitButton-text-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-header-title-color\]) {
         color: var(--k-preference-header-title-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-navigation-layout-item-selectedText-color\]) {
         color: var(--k-preference-navigation-layout-item-selectedText-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-navigation-layout-item-unselectedText-color\]) {
         color: var(--k-preference-navigation-layout-item-unselectedText-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-purposes-footer-actionButton-text-color\]) {
         color: var(--k-preference-tabs-purposes-footer-actionButton-text-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-purposes-header-description-color\]) {
         color: var(--k-preference-tabs-purposes-header-description-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-purposes-header-link-color\]) {
         color: var(--k-preference-tabs-purposes-header-link-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-purposes-header-title-color\]) {
         color: var(--k-preference-tabs-purposes-header-title-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-purposes-purposeList-purposeListItems-arrowIcon-color\]) {
         color: var(--k-preference-tabs-purposes-purposeList-purposeListItems-arrowIcon-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-purposes-purposeList-purposeListItems-purposeContent-color\]) {
         color: var(--k-preference-tabs-purposes-purposeList-purposeListItems-purposeContent-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-purposes-purposeList-purposeListItems-purposeLinks-color\]) {
         color: var(--k-preference-tabs-purposes-purposeList-purposeListItems-purposeLinks-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-purposes-purposeList-switchButtons-off-text-color\]) {
         color: var(--k-preference-tabs-purposes-purposeList-switchButtons-off-text-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-purposes-purposeList-switchButtons-on-text-color\]) {
         color: var(--k-preference-tabs-purposes-purposeList-switchButtons-on-text-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-purposes-purposeListHeader-acceptAllButton-text-color\]) {
         color: var(--k-preference-tabs-purposes-purposeListHeader-acceptAllButton-text-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-purposes-purposeListHeader-rejectAllButton-text-color\]) {
         color: var(--k-preference-tabs-purposes-purposeListHeader-rejectAllButton-text-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-purposes-purposeListHeader-title-color\]) {
         color: var(--k-preference-tabs-purposes-purposeListHeader-title-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-requests-form-error-text-color\]) {
         color: var(--k-preference-tabs-requests-form-error-text-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-requests-home-dsrPortalLink-description-color\]) {
         color: var(--k-preference-tabs-requests-home-dsrPortalLink-description-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-requests-home-dsrPortalLink-title-color\]) {
         color: var(--k-preference-tabs-requests-home-dsrPortalLink-title-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-requests-home-header-description-color\]) {
         color: var(--k-preference-tabs-requests-home-header-description-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-requests-home-header-link-color\]) {
         color: var(--k-preference-tabs-requests-home-header-link-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-requests-home-header-title-color\]) {
         color: var(--k-preference-tabs-requests-home-header-title-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-requests-home-rightsList-item-title-color\]) {
         color: var(--k-preference-tabs-requests-home-rightsList-item-title-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-requests-home-rightsList-title-color\]) {
         color: var(--k-preference-tabs-requests-home-rightsList-title-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-requests-rightForm-actionButton-text-color\]) {
         color: var(--k-preference-tabs-requests-rightForm-actionButton-text-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-requests-rightForm-form-checkboxes-error-label-color\]) {
         color: var(--k-preference-tabs-requests-rightForm-form-checkboxes-error-label-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-requests-rightForm-form-checkboxes-selected-label-color\]) {
         color: var(--k-preference-tabs-requests-rightForm-form-checkboxes-selected-label-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-requests-rightForm-form-checkboxes-unselected-label-color\]) {
         color: var(--k-preference-tabs-requests-rightForm-form-checkboxes-unselected-label-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-requests-rightForm-form-dividers-subtitle-color\]) {
         color: var(--k-preference-tabs-requests-rightForm-form-dividers-subtitle-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-requests-rightForm-form-dividers-title-color\]) {
         color: var(--k-preference-tabs-requests-rightForm-form-dividers-title-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-requests-rightForm-form-fields-fieldLabel-color\]) {
         color: var(--k-preference-tabs-requests-rightForm-form-fields-fieldLabel-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-requests-rightForm-form-fields-hintText-color\]) {
         color: var(--k-preference-tabs-requests-rightForm-form-fields-hintText-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-requests-rightForm-form-fields-inputText-color\]) {
         color: var(--k-preference-tabs-requests-rightForm-form-fields-inputText-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-requests-rightForm-header-title-color\]) {
         color: var(--k-preference-tabs-requests-rightForm-header-title-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-requests-submitted-footer-actionButton-text-color\]) {
         color: var(--k-preference-tabs-requests-submitted-footer-actionButton-text-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-requests-submitted-header-title-color\]) {
         color: var(--k-preference-tabs-requests-submitted-header-title-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-requests-submitted-text-description-color\]) {
         color: var(--k-preference-tabs-requests-submitted-text-description-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-requests-submitted-text-title-color\]) {
         color: var(--k-preference-tabs-requests-submitted-text-title-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-subscriptions-footer-actionButton-text-color\]) {
         color: var(--k-preference-tabs-subscriptions-footer-actionButton-text-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-subscriptions-header-description-color\]) {
         color: var(--k-preference-tabs-subscriptions-header-description-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-subscriptions-header-link-color\]) {
         color: var(--k-preference-tabs-subscriptions-header-link-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-subscriptions-header-title-color\]) {
         color: var(--k-preference-tabs-subscriptions-header-title-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-subscriptions-list-checkbox-selected-label-color\]) {
         color: var(--k-preference-tabs-subscriptions-list-checkbox-selected-label-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-subscriptions-list-checkbox-text-color\]) {
         color: var(--k-preference-tabs-subscriptions-list-checkbox-text-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-subscriptions-list-checkbox-unselected-label-color\]) {
         color: var(--k-preference-tabs-subscriptions-list-checkbox-unselected-label-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-subscriptions-list-layout-link-color\]) {
         color: var(--k-preference-tabs-subscriptions-list-layout-link-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-subscriptions-list-layout-text-color\]) {
         color: var(--k-preference-tabs-subscriptions-list-layout-text-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-subscriptions-list-switchButton-off-text-color\]) {
         color: var(--k-preference-tabs-subscriptions-list-switchButton-off-text-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-subscriptions-list-switchButton-on-text-color\]) {
         color: var(--k-preference-tabs-subscriptions-list-switchButton-on-text-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-subscriptions-unsubscribeAll-switchButton-off-text-color\]) {
         color: var(--k-preference-tabs-subscriptions-unsubscribeAll-switchButton-off-text-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-subscriptions-unsubscribeAll-switchButton-on-text-color\]) {
         color: var(--k-preference-tabs-subscriptions-unsubscribeAll-switchButton-on-text-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-subscriptions-unsubscribeAll-text-color\]) {
         color: var(--k-preference-tabs-subscriptions-unsubscribeAll-text-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-welcome-about-link-color\]) {
         color: var(--k-preference-tabs-welcome-about-link-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-welcome-about-title-color\]) {
         color: var(--k-preference-tabs-welcome-about-title-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-welcome-quickLinks-link-color\]) {
         color: var(--k-preference-tabs-welcome-quickLinks-link-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-welcome-quickLinks-title-color\]) {
         color: var(--k-preference-tabs-welcome-quickLinks-title-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-welcome-welcomeMsg-link-color\]) {
         color: var(--k-preference-tabs-welcome-welcomeMsg-link-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-welcome-welcomeMsg-subtitle-color\]) {
         color: var(--k-preference-tabs-welcome-welcomeMsg-subtitle-color) !important;
         }
         #lanyard_root * :is(.\!ketch-text-\[--k-preference-tabs-welcome-welcomeMsg-title-color\]) {
         color: var(--k-preference-tabs-welcome-welcomeMsg-title-color) !important;
         }
         #lanyard_root * :is(.ketch-text-\[--k-preference-tabs-welcome-about-text-color\]) {
         color: var(--k-preference-tabs-welcome-about-text-color);
         }
         #lanyard_root * :is(.ketch-text-black) {
         --tw-text-opacity: 1;
         color: rgb(0 0 0 / var(--tw-text-opacity));
         }
         #lanyard_root * :is(.ketch-text-gray-700) {
         --tw-text-opacity: 1;
         color: rgb(55 65 81 / var(--tw-text-opacity));
         }
         #lanyard_root * :is(.ketch-text-gray-900) {
         --tw-text-opacity: 1;
         color: rgb(17 24 39 / var(--tw-text-opacity));
         }
         #lanyard_root * :is(.ketch-text-inherit) {
         color: inherit;
         }
         #lanyard_root * :is(.ketch-text-ketch-black-black-100) {
         color: rgba(7, 26, 36, 1);
         }
         #lanyard_root * :is(.ketch-text-ketch-black-black-48) {
         color: rgba(7, 26, 36, 0.48);
         }
         #lanyard_root * :is(.ketch-text-ketch-error) {
         color: rgba(252, 60, 96, 1);
         }
         #lanyard_root * :is(.ketch-text-white) {
         --tw-text-opacity: 1;
         color: rgb(255 255 255 / var(--tw-text-opacity));
         }
         #lanyard_root * :is(.\!ketch-underline) {
         text-decoration-line: underline !important;
         }
         #lanyard_root * :is(.ketch-underline) {
         text-decoration-line: underline;
         }
         #lanyard_root * :is(.ketch-no-underline) {
         text-decoration-line: none;
         }
         #lanyard_root * :is(.ketch-placeholder-ketch-black-black-48)::-moz-placeholder {
         color: rgba(7, 26, 36, 0.48);
         }
         #lanyard_root * :is(.ketch-placeholder-ketch-black-black-48)::placeholder {
         color: rgba(7, 26, 36, 0.48);
         }
         #lanyard_root * :is(.ketch-opacity-0) {
         opacity: 0;
         }
         #lanyard_root * :is(.ketch-opacity-100) {
         opacity: 1;
         }
         #lanyard_root * :is(.ketch-opacity-50) {
         opacity: 0.5;
         }
         #lanyard_root * :is(.ketch-opacity-\[\.24\]) {
         opacity: .24;
         }
         #lanyard_root * :is(.ketch-opacity-\[0\.48\]) {
         opacity: 0.48;
         }
         #lanyard_root * :is(.ketch-opacity-\[0\.65\]) {
         opacity: 0.65;
         }
         #lanyard_root * :is(.ketch-shadow-\[0px_3px_10px_1px_rgba\(7\,26\,36\,0\.24\)\]) {
         --tw-shadow: 0px 3px 10px 1px rgba(7,26,36,0.24);
         --tw-shadow-colored: 0px 3px 10px 1px var(--tw-shadow-color);
         box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
         }
         #lanyard_root * :is(.ketch-shadow-none) {
         --tw-shadow: 0 0 #0000;
         --tw-shadow-colored: 0 0 #0000;
         box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
         }
         #lanyard_root * :is(.\!ketch-outline-2) {
         outline-width: 2px !important;
         }
         #lanyard_root * :is(.\!ketch-outline-offset-2) {
         outline-offset: 2px !important;
         }
         #lanyard_root * :is(.\!ketch-outline-ketch-black-black-100) {
         outline-color: rgba(7, 26, 36, 1) !important;
         }
         #lanyard_root * :is(.\!ketch-outline-white) {
         outline-color: #fff !important;
         }
         #lanyard_root * :is(.ketch-outline-default-outline) {
         outline-color: rgba(7, 26, 36, 0.48);
         }
         #lanyard_root * :is(.ketch-transition) {
         transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
         transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
         transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
         transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
         transition-duration: 150ms;
         }
         #lanyard_root * :is(.ketch-transition-transform) {
         transition-property: transform;
         transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
         transition-duration: 150ms;
         }
         #lanyard_root * :is(.ketch-duration-100) {
         transition-duration: 100ms;
         }
         #lanyard_root * :is(.ketch-duration-300) {
         transition-duration: 300ms;
         }
         #lanyard_root * :is(.ketch-ease-in) {
         transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
         }
         #lanyard_root * :is(.ketch-ease-in-out) {
         transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
         }
         @supports (padding: max(0px)) {
         .banner-mobile-bottom {
         bottom: 0px !important;
         }
         /* CSS Collision where text is uppercase */
         #lanyard_root * {
         text-transform: none;
         }
         }
         #lanyard_root * :is(.placeholder\:ketch-text-ketch-l)::-moz-placeholder {
         font-size: 14px;
         line-height: 21px;
         }
         #lanyard_root * :is(.placeholder\:ketch-text-ketch-l)::placeholder {
         font-size: 14px;
         line-height: 21px;
         }
         #lanyard_root * :is(.placeholder\:\!ketch-text-\[--k-preference-tabs-requests-rightForm-form-fields-hintText-color\])::-moz-placeholder {
         color: var(--k-preference-tabs-requests-rightForm-form-fields-hintText-color) !important;
         }
         #lanyard_root * :is(.placeholder\:\!ketch-text-\[--k-preference-tabs-requests-rightForm-form-fields-hintText-color\])::placeholder {
         color: var(--k-preference-tabs-requests-rightForm-form-fields-hintText-color) !important;
         }
         #lanyard_root * :is(.before\:ketch-absolute)::before {
         content: var(--tw-content);
         position: absolute;
         }
         #lanyard_root * :is(.before\:ketch-left-1)::before {
         content: var(--tw-content);
         left: 4px;
         }
         #lanyard_root * :is(.before\:ketch-top-1)::before {
         content: var(--tw-content);
         top: 4px;
         }
         #lanyard_root * :is(.before\:ketch-h-2)::before {
         content: var(--tw-content);
         height: 8px;
         }
         #lanyard_root * :is(.before\:ketch-w-2)::before {
         content: var(--tw-content);
         width: 8px;
         }
         #lanyard_root * :is(.before\:ketch-rounded-full)::before {
         content: var(--tw-content);
         border-radius: 9999px;
         }
         #lanyard_root * :is(.before\:ketch-bg-white)::before {
         content: var(--tw-content);
         --tw-bg-opacity: 1;
         background-color: rgb(255 255 255 / var(--tw-bg-opacity));
         }
         #lanyard_root * :is(.before\:ketch-opacity-0)::before {
         content: var(--tw-content);
         opacity: 0;
         }
         #lanyard_root * :is(.before\:ketch-content-\[\'\'\])::before {
         --tw-content: '';
         content: var(--tw-content);
         }
         #lanyard_root * :is(.after\:ketch-absolute)::after {
         content: var(--tw-content);
         position: absolute;
         }
         #lanyard_root * :is(.after\:-ketch-left-0)::after {
         content: var(--tw-content);
         left: 0;
         }
         #lanyard_root * :is(.after\:-ketch-left-0\.5)::after {
         content: var(--tw-content);
         left: -2px;
         }
         #lanyard_root * :is(.after\:-ketch-top-0)::after {
         content: var(--tw-content);
         top: 0;
         }
         #lanyard_root * :is(.after\:-ketch-top-0\.5)::after {
         content: var(--tw-content);
         top: -2px;
         }
         #lanyard_root * :is(.after\:ketch-left-0)::after {
         content: var(--tw-content);
         left: 0;
         }
         #lanyard_root * :is(.after\:ketch-start-\[2px\])::after {
         content: var(--tw-content);
         inset-inline-start: 2px;
         }
         #lanyard_root * :is(.after\:ketch-top-0)::after {
         content: var(--tw-content);
         top: 0;
         }
         #lanyard_root * :is(.after\:ketch-top-\[2px\])::after {
         content: var(--tw-content);
         top: 2px;
         }
         #lanyard_root * :is(.after\:ketch-h-5)::after {
         content: var(--tw-content);
         height: 20px;
         }
         #lanyard_root * :is(.after\:ketch-w-5)::after {
         content: var(--tw-content);
         width: 20px;
         }
         #lanyard_root * :is(.after\:ketch-cursor-pointer)::after {
         content: var(--tw-content);
         cursor: pointer;
         }
         #lanyard_root * :is(.after\:ketch-rounded-\[3px\])::after {
         content: var(--tw-content);
         border-radius: 3px;
         }
         #lanyard_root * :is(.after\:ketch-rounded-full)::after {
         content: var(--tw-content);
         border-radius: 9999px;
         }
         #lanyard_root * :is(.after\:ketch-bg-white)::after {
         content: var(--tw-content);
         --tw-bg-opacity: 1;
         background-color: rgb(255 255 255 / var(--tw-bg-opacity));
         }
         #lanyard_root * :is(.after\:ketch-transition-all)::after {
         content: var(--tw-content);
         transition-property: all;
         transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
         transition-duration: 150ms;
         }
         #lanyard_root * :is(.after\:ketch-content-\[\'\'\])::after {
         --tw-content: '';
         content: var(--tw-content);
         }
         #lanyard_root * :is(.odd\:ketch-bg-white:nth-child(odd)) {
         --tw-bg-opacity: 1;
         background-color: rgb(255 255 255 / var(--tw-bg-opacity));
         }
         #lanyard_root * :is(.even\:ketch-bg-gray-100:nth-child(even)) {
         --tw-bg-opacity: 1;
         background-color: rgb(243 244 246 / var(--tw-bg-opacity));
         }
         #lanyard_root * :is(.autofill\:\!ketch-shadow-\[inset_0_0_0px_1000px_var\(--k-preference-tabs-requests-rightForm-form-fields-inactive-background-color\)\]:-webkit-autofill) {
         --tw-shadow: inset 0 0 0px 1000px var(--k-preference-tabs-requests-rightForm-form-fields-inactive-background-color) !important;
         --tw-shadow-colored: inset 0 0 0px 1000px var(--tw-shadow-color) !important;
         box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
         }
         #lanyard_root * :is(.autofill\:\!ketch-shadow-\[inset_0_0_0px_1000px_var\(--k-preference-tabs-requests-rightForm-form-fields-inactive-background-color\)\]:autofill) {
         --tw-shadow: inset 0 0 0px 1000px var(--k-preference-tabs-requests-rightForm-form-fields-inactive-background-color) !important;
         --tw-shadow-colored: inset 0 0 0px 1000px var(--tw-shadow-color) !important;
         box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
         }
         #lanyard_root * :is(.hover\:ketch-cursor-pointer:hover) {
         cursor: pointer;
         }
         #lanyard_root * :is(.hover\:ketch-border-ketch-black-black-56:hover) {
         border-color: rgba(7, 26, 36, 0.56);
         }
         #lanyard_root * :is(.hover\:ketch-bg-gradient-to-r:hover) {
         background-image: linear-gradient(to right, var(--tw-gradient-stops));
         }
         #lanyard_root * :is(.hover\:ketch-from-states-hover-darken:hover) {
         --tw-gradient-from: rgba(7, 26, 36, 0.10) var(--tw-gradient-from-position);
         --tw-gradient-to: rgba(7, 26, 36, 0) var(--tw-gradient-to-position);
         --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
         }
         #lanyard_root * :is(.hover\:ketch-to-states-hover-darken:hover) {
         --tw-gradient-to: rgba(7, 26, 36, 0.10) var(--tw-gradient-to-position);
         }
         #lanyard_root * :is(.hover\:ketch-text-inherit:hover) {
         color: inherit;
         }
         #lanyard_root * :is(.focus-visible\:ketch-mb-1:focus-visible) {
         margin-bottom: 4px;
         }
         #lanyard_root * :is(.focus-visible\:\!ketch-border-\[--k-preference-tabs-requests-rightForm-form-fields-active-outline-color\]:focus-visible) {
         border-color: var(--k-preference-tabs-requests-rightForm-form-fields-active-outline-color) !important;
         }
         #lanyard_root * :is(.focus-visible\:ketch-border-blue-600:focus-visible) {
         --tw-border-opacity: 1;
         border-color: rgb(37 99 235 / var(--tw-border-opacity));
         }
         #lanyard_root * :is(.focus-visible\:\!ketch-bg-\[--k-preference-tabs-requests-rightForm-form-fields-active-background-color\]:focus-visible) {
         background-color: var(--k-preference-tabs-requests-rightForm-form-fields-active-background-color) !important;
         }
         #lanyard_root * :is(.focus-visible\:ketch-bg-gradient-to-r:focus-visible) {
         background-image: linear-gradient(to right, var(--tw-gradient-stops));
         }
         #lanyard_root * :is(.focus-visible\:ketch-from-states-focus-darken:focus-visible) {
         --tw-gradient-from: rgba(7, 26, 36, 0.08) var(--tw-gradient-from-position);
         --tw-gradient-to: rgba(7, 26, 36, 0) var(--tw-gradient-to-position);
         --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
         }
         #lanyard_root * :is(.focus-visible\:ketch-to-states-focus-darken:focus-visible) {
         --tw-gradient-to: rgba(7, 26, 36, 0.08) var(--tw-gradient-to-position);
         }
         #lanyard_root * :is(.focus-visible\:ketch-outline-none:focus-visible) {
         outline: 2px solid transparent;
         outline-offset: 2px;
         }
         #lanyard_root * :is(.focus-visible\:\!ketch-outline:focus-visible) {
         outline-style: solid !important;
         }
         #lanyard_root * :is(.focus-visible\:ketch-outline:focus-visible) {
         outline-style: solid;
         }
         #lanyard_root * :is(.focus-visible\:ketch-outline-2:focus-visible) {
         outline-width: 2px;
         }
         #lanyard_root * :is(.focus-visible\:\!ketch-outline-offset-2:focus-visible) {
         outline-offset: 2px !important;
         }
         #lanyard_root * :is(.focus-visible\:ketch-outline-offset-0:focus-visible) {
         outline-offset: 0px;
         }
         #lanyard_root * :is(.focus-visible\:ketch-outline-offset-2:focus-visible) {
         outline-offset: 2px;
         }
         #lanyard_root * :is(.focus-visible\:\!ketch-outline-\[--k-banner-header-returnButton-background-color\]:focus-visible) {
         outline-color: var(--k-banner-header-returnButton-background-color) !important;
         }
         #lanyard_root * :is(.focus-visible\:\!ketch-outline-\[--k-preference-tabs-requests-submitted-footer-actionButton-outline-color\]:focus-visible) {
         outline-color: var(--k-preference-tabs-requests-submitted-footer-actionButton-outline-color) !important;
         }
         #lanyard_root * :is(.focus-visible\:ketch-outline-default-outline:focus-visible) {
         outline-color: rgba(7, 26, 36, 0.48);
         }
         #lanyard_root * :is(.focus-visible\:ketch-outline-inherit:focus-visible) {
         outline-color: inherit;
         }
         #lanyard_root * :is(.focus-visible\:ketch-outline-ketch-black-black-100:focus-visible) {
         outline-color: rgba(7, 26, 36, 1);
         }
         #lanyard_root * :is(.focus-visible\:ketch-outline-white:focus-visible) {
         outline-color: #fff;
         }
         #lanyard_root * :is(.focus-visible\:-webkit-autofill\:\!ketch-shadow-\[inset_0_0_0px_1000px_var\(--k-preference-tabs-requests-rightForm-form-fields-active-background-color\)\]:-webkit-autofill:focus-visible) {
         --tw-shadow: inset 0 0 0px 1000px var(--k-preference-tabs-requests-rightForm-form-fields-active-background-color) !important;
         --tw-shadow-colored: inset 0 0 0px 1000px var(--tw-shadow-color) !important;
         box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
         }
         #lanyard_root * :is(.focus-visible\:autofill\:\!ketch-shadow-\[inset_0_0_0px_1000px_var\(--k-preference-tabs-requests-rightForm-form-fields-active-background-color\)\]:autofill:focus-visible) {
         --tw-shadow: inset 0 0 0px 1000px var(--k-preference-tabs-requests-rightForm-form-fields-active-background-color) !important;
         --tw-shadow-colored: inset 0 0 0px 1000px var(--tw-shadow-color) !important;
         box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
         }
         #lanyard_root * :is(.focus-visible\:-webkit-autofill\:\!ketch-shadow-\[inset_0_0_0px_1000px_var\(--k-preference-tabs-requests-rightForm-form-fields-error-background-color\)\]:-webkit-autofill:focus-visible) {
         --tw-shadow: inset 0 0 0px 1000px var(--k-preference-tabs-requests-rightForm-form-fields-error-background-color) !important;
         --tw-shadow-colored: inset 0 0 0px 1000px var(--tw-shadow-color) !important;
         box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
         }
         #lanyard_root * :is(.focus-visible\:autofill\:\!ketch-shadow-\[inset_0_0_0px_1000px_var\(--k-preference-tabs-requests-rightForm-form-fields-error-background-color\)\]:autofill:focus-visible) {
         --tw-shadow: inset 0 0 0px 1000px var(--k-preference-tabs-requests-rightForm-form-fields-error-background-color) !important;
         --tw-shadow-colored: inset 0 0 0px 1000px var(--tw-shadow-color) !important;
         box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
         }
         #lanyard_root * :is(.active\:ketch-bg-gradient-to-r:active) {
         background-image: linear-gradient(to right, var(--tw-gradient-stops));
         }
         #lanyard_root * :is(.active\:ketch-from-states-pressed-darken:active) {
         --tw-gradient-from: rgba(7, 26, 36, 0.24) var(--tw-gradient-from-position);
         --tw-gradient-to: rgba(7, 26, 36, 0) var(--tw-gradient-to-position);
         --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
         }
         #lanyard_root * :is(.active\:ketch-to-states-pressed-darken:active) {
         --tw-gradient-to: rgba(7, 26, 36, 0.24) var(--tw-gradient-to-position);
         }
         #lanyard_root * :is(.disabled\:ketch-pointer-events-none:disabled) {
         pointer-events: none;
         }
         #lanyard_root * :is(.disabled\:ketch-cursor-not-allowed:disabled) {
         cursor: not-allowed;
         }
         #lanyard_root * :is(.disabled\:ketch-select-none:disabled) {
         -webkit-user-select: none;
         -moz-user-select: none;
         user-select: none;
         }
         #lanyard_root * :is(.disabled\:ketch-opacity-\[\.24\]:disabled) {
         opacity: .24;
         }
         #lanyard_root * :is(.ketch-peer:checked ~ .peer-checked\:ketch-flex) {
         display: flex;
         }
         #lanyard_root * :is(.ketch-peer:checked ~ .peer-checked\:ketch-inline-flex) {
         display: inline-flex;
         }
         #lanyard_root * :is(.ketch-peer:checked ~ .peer-checked\:ketch-hidden) {
         display: none;
         }
         #lanyard_root * :is(.ketch-peer:checked ~ .peer-checked\:ketch-rounded-\[3px\]) {
         border-radius: 3px;
         }
         #lanyard_root * :is(.ketch-peer:checked ~ .peer-checked\:ketch-rounded-full) {
         border-radius: 9999px;
         }
         #lanyard_root * :is(.ketch-peer:checked ~ .peer-checked\:\!ketch-border-\[--k-modal-purposeList-switchButtons-on-background-color\]) {
         border-color: var(--k-modal-purposeList-switchButtons-on-background-color) !important;
         }
         #lanyard_root * :is(.ketch-peer:checked ~ .peer-checked\:\!ketch-border-\[--k-preference-tabs-purposes-purposeList-switchButtons-on-background-color\]) {
         border-color: var(--k-preference-tabs-purposes-purposeList-switchButtons-on-background-color) !important;
         }
         #lanyard_root * :is(.ketch-peer:checked ~ .peer-checked\:\!ketch-border-\[--k-preference-tabs-requests-rightForm-form-checkboxes-selected-background-color\]) {
         border-color: var(--k-preference-tabs-requests-rightForm-form-checkboxes-selected-background-color) !important;
         }
         #lanyard_root * :is(.ketch-peer:checked ~ .peer-checked\:\!ketch-border-\[--k-preference-tabs-subscriptions-list-checkbox-selected-background-color\]) {
         border-color: var(--k-preference-tabs-subscriptions-list-checkbox-selected-background-color) !important;
         }
         #lanyard_root * :is(.ketch-peer:checked ~ .peer-checked\:ketch-border-black) {
         --tw-border-opacity: 1;
         border-color: rgb(0 0 0 / var(--tw-border-opacity));
         }
         #lanyard_root * :is(.ketch-peer:checked ~ .peer-checked\:\!ketch-bg-\[--k-modal-purposeList-switchButtons-on-background-color\]) {
         background-color: var(--k-modal-purposeList-switchButtons-on-background-color) !important;
         }
         #lanyard_root * :is(.ketch-peer:checked ~ .peer-checked\:\!ketch-bg-\[--k-preference-tabs-purposes-purposeList-switchButtons-on-background-color\]) {
         background-color: var(--k-preference-tabs-purposes-purposeList-switchButtons-on-background-color) !important;
         }
         #lanyard_root * :is(.ketch-peer:checked ~ .peer-checked\:\!ketch-bg-\[--k-preference-tabs-requests-rightForm-form-checkboxes-selected-background-color\]) {
         background-color: var(--k-preference-tabs-requests-rightForm-form-checkboxes-selected-background-color) !important;
         }
         #lanyard_root * :is(.ketch-peer:checked ~ .peer-checked\:\!ketch-bg-\[--k-preference-tabs-subscriptions-list-checkbox-selected-background-color\]) {
         background-color: var(--k-preference-tabs-subscriptions-list-checkbox-selected-background-color) !important;
         }
         #lanyard_root * :is(.ketch-peer:checked ~ .peer-checked\:\!ketch-bg-\[--k-preference-tabs-subscriptions-list-switchButton-on-background-color\]) {
         background-color: var(--k-preference-tabs-subscriptions-list-switchButton-on-background-color) !important;
         }
         #lanyard_root * :is(.ketch-peer:checked ~ .peer-checked\:\!ketch-bg-\[--k-preference-tabs-subscriptions-unsubscribeAll-switchButton-on-background-color\]) {
         background-color: var(--k-preference-tabs-subscriptions-unsubscribeAll-switchButton-on-background-color) !important;
         }
         #lanyard_root * :is(.ketch-peer:checked ~ .peer-checked\:ketch-bg-black) {
         --tw-bg-opacity: 1;
         background-color: rgb(0 0 0 / var(--tw-bg-opacity));
         }
         #lanyard_root * :is(.ketch-peer:checked ~ .peer-checked\:ketch-font-semibold) {
         font-weight: 600;
         }
         #lanyard_root * :is(.ketch-peer:checked ~ .peer-checked\:before\:ketch-opacity-100)::before {
         content: var(--tw-content);
         opacity: 1;
         }
         #lanyard_root * :is(.ketch-peer:checked ~ .peer-checked\:after\:ketch-translate-x-4)::after {
         content: var(--tw-content);
         --tw-translate-x: 16px;
         transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
         }
         #lanyard_root * :is(.ketch-peer:hover ~ .peer-hover\:ketch-bg-gradient-to-r) {
         background-image: linear-gradient(to right, var(--tw-gradient-stops));
         }
         #lanyard_root * :is(.ketch-peer:hover ~ .peer-hover\:ketch-from-states-hover-darken) {
         --tw-gradient-from: rgba(7, 26, 36, 0.10) var(--tw-gradient-from-position);
         --tw-gradient-to: rgba(7, 26, 36, 0) var(--tw-gradient-to-position);
         --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
         }
         #lanyard_root * :is(.ketch-peer:hover ~ .peer-hover\:ketch-to-states-hover-darken) {
         --tw-gradient-to: rgba(7, 26, 36, 0.10) var(--tw-gradient-to-position);
         }
         #lanyard_root * :is(.ketch-peer:hover ~ .peer-hover\:after\:ketch-bg-gradient-to-r)::after {
         content: var(--tw-content);
         background-image: linear-gradient(to right, var(--tw-gradient-stops));
         }
         #lanyard_root * :is(.ketch-peer:hover ~ .peer-hover\:after\:ketch-from-states-hover-darken)::after {
         content: var(--tw-content);
         --tw-gradient-from: rgba(7, 26, 36, 0.10) var(--tw-gradient-from-position);
         --tw-gradient-to: rgba(7, 26, 36, 0) var(--tw-gradient-to-position);
         --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
         }
         #lanyard_root * :is(.ketch-peer:hover ~ .peer-hover\:after\:ketch-to-states-hover-darken)::after {
         content: var(--tw-content);
         --tw-gradient-to: rgba(7, 26, 36, 0.10) var(--tw-gradient-to-position);
         }
         #lanyard_root * :is(.ketch-peer:focus-visible ~ .peer-focus-visible\:ketch-bg-gradient-to-r) {
         background-image: linear-gradient(to right, var(--tw-gradient-stops));
         }
         #lanyard_root * :is(.ketch-peer:focus-visible ~ .peer-focus-visible\:ketch-from-states-focus-darken) {
         --tw-gradient-from: rgba(7, 26, 36, 0.08) var(--tw-gradient-from-position);
         --tw-gradient-to: rgba(7, 26, 36, 0) var(--tw-gradient-to-position);
         --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
         }
         #lanyard_root * :is(.ketch-peer:focus-visible ~ .peer-focus-visible\:ketch-to-states-focus-darken) {
         --tw-gradient-to: rgba(7, 26, 36, 0.08) var(--tw-gradient-to-position);
         }
         #lanyard_root * :is(.ketch-peer:focus-visible ~ .peer-focus-visible\:ketch-outline) {
         outline-style: solid;
         }
         #lanyard_root * :is(.ketch-peer:focus-visible ~ .peer-focus-visible\:ketch-outline-2) {
         outline-width: 2px;
         }
         #lanyard_root * :is(.ketch-peer:focus-visible ~ .peer-focus-visible\:ketch-outline-offset-2) {
         outline-offset: 2px;
         }
         #lanyard_root * :is(.ketch-peer:focus-visible ~ .peer-focus-visible\:ketch-outline-default-outline) {
         outline-color: rgba(7, 26, 36, 0.48);
         }
         #lanyard_root * :is(.ketch-peer:focus-visible:checked ~ .peer-focus-visible\:peer-checked\:ketch-outline-black) {
         outline-color: #000;
         }
         #lanyard_root * :is(.ketch-peer:focus-visible:checked ~ .peer-focus-visible\:peer-checked\:ketch-outline-default-outline) {
         outline-color: rgba(7, 26, 36, 0.48);
         }
         #lanyard_root * :is(.ketch-peer:checked:focus-visible ~ .peer-checked\:peer-focus-visible\:after\:ketch-bg-gradient-to-r)::after {
         content: var(--tw-content);
         background-image: linear-gradient(to right, var(--tw-gradient-stops));
         }
         #lanyard_root * :is(.ketch-peer:checked:focus-visible ~ .peer-checked\:peer-focus-visible\:after\:ketch-from-states-focus-darken)::after {
         content: var(--tw-content);
         --tw-gradient-from: rgba(7, 26, 36, 0.08) var(--tw-gradient-from-position);
         --tw-gradient-to: rgba(7, 26, 36, 0) var(--tw-gradient-to-position);
         --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
         }
         #lanyard_root * :is(.ketch-peer:checked:focus-visible ~ .peer-checked\:peer-focus-visible\:after\:ketch-to-states-focus-darken)::after {
         content: var(--tw-content);
         --tw-gradient-to: rgba(7, 26, 36, 0.08) var(--tw-gradient-to-position);
         }
         #lanyard_root * :is(.ketch-peer:active ~ .peer-active\:ketch-bg-gradient-to-r) {
         background-image: linear-gradient(to right, var(--tw-gradient-stops));
         }
         #lanyard_root * :is(.ketch-peer:active ~ .peer-active\:ketch-from-states-pressed-darken) {
         --tw-gradient-from: rgba(7, 26, 36, 0.24) var(--tw-gradient-from-position);
         --tw-gradient-to: rgba(7, 26, 36, 0) var(--tw-gradient-to-position);
         --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
         }
         #lanyard_root * :is(.ketch-peer:active ~ .peer-active\:ketch-to-states-pressed-darken) {
         --tw-gradient-to: rgba(7, 26, 36, 0.24) var(--tw-gradient-to-position);
         }
         #lanyard_root * :is(.ketch-peer:checked:active ~ .peer-checked\:peer-active\:after\:ketch-bg-gradient-to-r)::after {
         content: var(--tw-content);
         background-image: linear-gradient(to right, var(--tw-gradient-stops));
         }
         #lanyard_root * :is(.ketch-peer:checked:active ~ .peer-checked\:peer-active\:after\:ketch-from-states-pressed-darken)::after {
         content: var(--tw-content);
         --tw-gradient-from: rgba(7, 26, 36, 0.24) var(--tw-gradient-from-position);
         --tw-gradient-to: rgba(7, 26, 36, 0) var(--tw-gradient-to-position);
         --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
         }
         #lanyard_root * :is(.ketch-peer:checked:active ~ .peer-checked\:peer-active\:after\:ketch-to-states-pressed-darken)::after {
         content: var(--tw-content);
         --tw-gradient-to: rgba(7, 26, 36, 0.24) var(--tw-gradient-to-position);
         }
         #lanyard_root * :is(.ketch-peer:disabled ~ .peer-disabled\:ketch-pointer-events-none) {
         pointer-events: none;
         }
         #lanyard_root * :is(.ketch-peer:disabled ~ .peer-disabled\:ketch-cursor-not-allowed) {
         cursor: not-allowed;
         }
         #lanyard_root * :is(.ketch-peer:disabled ~ .peer-disabled\:ketch-select-none) {
         -webkit-user-select: none;
         -moz-user-select: none;
         user-select: none;
         }
         #lanyard_root * :is(.ketch-peer:disabled ~ .peer-disabled\:ketch-opacity-\[\.24\]) {
         opacity: .24;
         }
         #lanyard_root * :is(.ketch-peer:disabled:hover ~ .peer-disabled\:peer-hover\:ketch-bg-none) {
         background-image: none;
         }
         #lanyard_root * :is(.ketch-peer:disabled:hover ~ .peer-disabled\:peer-hover\:after\:ketch-bg-none)::after {
         content: var(--tw-content);
         background-image: none;
         }
         #lanyard_root * :is(.ketch-peer:disabled:focus-visible ~ .peer-disabled\:peer-focus-visible\:ketch-bg-none) {
         background-image: none;
         }
         #lanyard_root * :is(.ketch-peer:disabled:focus-visible ~ .peer-disabled\:peer-focus-visible\:after\:ketch-bg-none)::after {
         content: var(--tw-content);
         background-image: none;
         }
         #lanyard_root * :is(.ketch-peer:disabled:active ~ .peer-disabled\:peer-active\:ketch-bg-none) {
         background-image: none;
         }
         #lanyard_root * :is(.ketch-peer:disabled:active ~ .peer-disabled\:peer-active\:after\:ketch-bg-none)::after {
         content: var(--tw-content);
         background-image: none;
         }
         #lanyard_root * :is(.aria-selected\:\!ketch-bg-\[--k-preference-navigation-layout-item-selectedBackground-color\][aria-selected="true"]) {
         background-color: var(--k-preference-navigation-layout-item-selectedBackground-color) !important;
         }
         #lanyard_root * :is(.aria-selected\:\!ketch-text-\[--k-preference-navigation-layout-item-selectedText-color\][aria-selected="true"]) {
         color: var(--k-preference-navigation-layout-item-selectedText-color) !important;
         }
         #lanyard_root * :is(.aria-selected\:ketch-underline[aria-selected="true"]) {
         text-decoration-line: underline;
         }
         @media (min-width: 420px) {
         #lanyard_root * :is(.xs\:ketch-bottom-1) {
         bottom: 4px;
         }
         #lanyard_root * :is(.xs\:ketch-right-1) {
         right: 4px;
         }
         #lanyard_root * :is(.xs\:ketch-right-auto) {
         right: auto;
         }
         #lanyard_root * :is(.xs\:ketch-top-1) {
         top: 4px;
         }
         #lanyard_root * :is(.xs\:ketch-flex) {
         display: flex;
         }
         #lanyard_root * :is(.xs\:ketch-hidden) {
         display: none;
         }
         #lanyard_root * :is(.xs\:ketch-max-h-\[calc\(100dvh-8px\)\]) {
         max-height: calc(100dvh - 8px);
         }
         #lanyard_root * :is(.xs\:ketch-min-h-\[calc\(100dvh-8px\)\]) {
         min-height: calc(100dvh - 8px);
         }
         #lanyard_root * :is(.xs\:ketch-w-\[420px\]) {
         width: 420px;
         }
         #lanyard_root * :is(.xs\:ketch-w-\[calc\(100\%-8px\)\]) {
         width: calc(100% - 8px);
         }
         #lanyard_root * :is(.xs\:ketch-w-auto) {
         width: auto;
         }
         #lanyard_root * :is(.xs\:ketch-w-fit) {
         width: -moz-fit-content;
         width: fit-content;
         }
         #lanyard_root * :is(.xs\:ketch-w-full) {
         width: 100%;
         }
         #lanyard_root * :is(.xs\:ketch-max-w-\[184px\]) {
         max-width: 184px;
         }
         #lanyard_root * :is(.xs\:ketch-translate-y-0) {
         --tw-translate-y: 0;
         transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
         }
         #lanyard_root * :is(.xs\:ketch-flex-row) {
         flex-direction: row;
         }
         #lanyard_root * :is(.xs\:ketch-items-center) {
         align-items: center;
         }
         #lanyard_root * :is(.xs\:ketch-gap-0) {
         gap: 0;
         }
         #lanyard_root * :is(.xs\:ketch-px-6) {
         padding-left: 24px;
         padding-right: 24px;
         }
         #lanyard_root * :is(.xs\:ketch-py-5) {
         padding-top: 20px;
         padding-bottom: 20px;
         }
         #lanyard_root * :is(.xs\:ketch-text-ketch-h1-small) {
         font-size: 26px;
         line-height: 36.4px;
         }
         }
         @media (min-width: 480px) {
         #lanyard_root * :is(.sm\:ketch-left-2\/4) {
         left: 50%;
         }
         #lanyard_root * :is(.sm\:ketch-flex) {
         display: flex;
         }
         #lanyard_root * :is(.sm\:ketch-hidden) {
         display: none;
         }
         #lanyard_root * :is(.sm\:ketch-max-h-\[calc\(100dvh-8px\)\]) {
         max-height: calc(100dvh - 8px);
         }
         #lanyard_root * :is(.sm\:ketch-max-h-dvh) {
         max-height: 100dvh;
         }
         #lanyard_root * :is(.sm\:ketch-w-\[730px\]) {
         width: 730px;
         }
         #lanyard_root * :is(.sm\:ketch-w-auto) {
         width: auto;
         }
         #lanyard_root * :is(.sm\:ketch-w-fit) {
         width: -moz-fit-content;
         width: fit-content;
         }
         #lanyard_root * :is(.sm\:ketch-w-full) {
         width: 100%;
         }
         #lanyard_root * :is(.sm\:\!ketch-max-w-\[calc\(75\%\+var\(--safe-area-inset-left\)\+var\(--safe-area-inset-right\)\)\]) {
         max-width: calc(75% + var(--safe-area-inset-left) + var(--safe-area-inset-right)) !important;
         }
         #lanyard_root * :is(.sm\:ketch-max-w-\[184px\]) {
         max-width: 184px;
         }
         #lanyard_root * :is(.sm\:ketch-max-w-\[328px\]) {
         max-width: 328px;
         }
         #lanyard_root * :is(.sm\:ketch-max-w-\[40\%\]) {
         max-width: 40%;
         }
         #lanyard_root * :is(.sm\:ketch-max-w-\[calc\(100\%-8px\)\]) {
         max-width: calc(100% - 8px);
         }
         #lanyard_root * :is(.sm\:ketch-max-w-\[calc\(80\%\+var\(--safe-area-inset-left\)\+var\(--safe-area-inset-right\)\)\]) {
         max-width: calc(80% + var(--safe-area-inset-left) + var(--safe-area-inset-right));
         }
         #lanyard_root * :is(.sm\:-ketch-translate-x-2\/4) {
         --tw-translate-x: -50%;
         transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
         }
         #lanyard_root * :is(.sm\:ketch-flex-row) {
         flex-direction: row;
         }
         #lanyard_root * :is(.sm\:ketch-flex-col) {
         flex-direction: column;
         }
         #lanyard_root * :is(.sm\:ketch-items-end) {
         align-items: flex-end;
         }
         #lanyard_root * :is(.sm\:ketch-items-center) {
         align-items: center;
         }
         #lanyard_root * :is(.sm\:ketch-items-stretch) {
         align-items: stretch;
         }
         #lanyard_root * :is(.sm\:ketch-gap-0) {
         gap: 0;
         }
         #lanyard_root * :is(.sm\:ketch-gap-6) {
         gap: 24px;
         }
         #lanyard_root * :is(.sm\:ketch-gap-8) {
         gap: 32px;
         }
         #lanyard_root * :is(.sm\:ketch-overflow-visible) {
         overflow: visible;
         }
         #lanyard_root * :is(.sm\:ketch-overflow-scroll) {
         overflow: scroll;
         }
         #lanyard_root * :is(.sm\:ketch-px-6) {
         padding-left: 24px;
         padding-right: 24px;
         }
         #lanyard_root * :is(.sm\:ketch-py-5) {
         padding-top: 20px;
         padding-bottom: 20px;
         }
         #lanyard_root * :is(.sm\:ketch-text-center) {
         text-align: center;
         }
         #lanyard_root * :is(.sm\:ketch-text-ketch-h1-small) {
         font-size: 26px;
         line-height: 36.4px;
         }
         }
         @media (min-width: 769px) {
         #lanyard_root * :is(.md\:ketch-mx-auto) {
         margin-left: auto;
         margin-right: auto;
         }
         #lanyard_root * :is(.md\:ketch-block) {
         display: block;
         }
         #lanyard_root * :is(.md\:ketch-inline-block) {
         display: inline-block;
         }
         #lanyard_root * :is(.md\:ketch-flex) {
         display: flex;
         }
         #lanyard_root * :is(.md\:ketch-inline-flex) {
         display: inline-flex;
         }
         #lanyard_root * :is(.md\:ketch-hidden) {
         display: none;
         }
         #lanyard_root * :is(.md\:ketch-h-16) {
         height: 64px;
         }
         #lanyard_root * :is(.md\:ketch-max-h-80) {
         max-height: 320px;
         }
         #lanyard_root * :is(.md\:ketch-max-w-\[50\%\]) {
         max-width: 50%;
         }
         #lanyard_root * :is(.md\:ketch-max-w-\[80\%\]) {
         max-width: 80%;
         }
         #lanyard_root * :is(.md\:ketch-basis-\[80\%\]) {
         flex-basis: 80%;
         }
         #lanyard_root * :is(.md\:ketch-grid-cols-1) {
         grid-template-columns: repeat(1, minmax(0, 1fr));
         }
         #lanyard_root * :is(.md\:ketch-grid-cols-\[160px_1fr\]) {
         grid-template-columns: 160px 1fr;
         }
         #lanyard_root * :is(.md\:ketch-grid-cols-\[328px_1fr\]) {
         grid-template-columns: 328px 1fr;
         }
         #lanyard_root * :is(.md\:ketch-flex-row) {
         flex-direction: row;
         }
         #lanyard_root * :is(.md\:ketch-flex-col) {
         flex-direction: column;
         }
         #lanyard_root * :is(.md\:ketch-items-center) {
         align-items: center;
         }
         #lanyard_root * :is(.md\:ketch-justify-between) {
         justify-content: space-between;
         }
         #lanyard_root * :is(.md\:ketch-gap-4) {
         gap: 16px;
         }
         #lanyard_root * :is(.md\:ketch-gap-6) {
         gap: 24px;
         }
         #lanyard_root * :is(.md\:ketch-gap-8) {
         gap: 32px;
         }
         #lanyard_root * :is(.md\:ketch-px-0) {
         padding-left: 0;
         padding-right: 0;
         }
         #lanyard_root * :is(.md\:ketch-px-4) {
         padding-left: 16px;
         padding-right: 16px;
         }
         #lanyard_root * :is(.md\:ketch-px-8) {
         padding-left: 32px;
         padding-right: 32px;
         }
         #lanyard_root * :is(.md\:ketch-pb-0) {
         padding-bottom: 0;
         }
         #lanyard_root * :is(.md\:ketch-pb-6) {
         padding-bottom: 24px;
         }
         #lanyard_root * :is(.md\:ketch-pl-8) {
         padding-left: 32px;
         }
         #lanyard_root * :is(.md\:ketch-pr-0) {
         padding-right: 0;
         }
         #lanyard_root * :is(.md\:ketch-pr-3) {
         padding-right: 12px;
         }
         #lanyard_root * :is(.md\:ketch-pr-4) {
         padding-right: 16px;
         }
         #lanyard_root * :is(.md\:ketch-pt-8) {
         padding-top: 32px;
         }
         #lanyard_root * :is(.md\:ketch-text-center) {
         text-align: center;
         }
         }
         @media (min-width: 1024px) {
         #lanyard_root * :is(.lm\:ketch-col-span-1) {
         grid-column: span 1 / span 1;
         }
         #lanyard_root * :is(.lm\:ketch-max-h-\[90\%\]) {
         max-height: 90%;
         }
         }
         @media (min-width: 1440px) {
         #lanyard_root * :is(.lg\:ketch-mx-auto) {
         margin-left: auto;
         margin-right: auto;
         }
         #lanyard_root * :is(.lg\:ketch-max-h-\[480px\]) {
         max-height: 480px;
         }
         #lanyard_root * :is(.lg\:ketch-px-0) {
         padding-left: 0;
         padding-right: 0;
         }
         #lanyard_root * :is(.lg\:ketch-pr-0) {
         padding-right: 0;
         }
         #lanyard_root * :is(.lg\:ketch-pr-14) {
         padding-right: 56px;
         }
         #lanyard_root * :is(.lg\:ketch-pr-\[72px\]) {
         padding-right: 72px;
         }
         }
         #lanyard_root * :is(.ketch-peer:checked ~ .rtl\:peer-checked\:after\:-ketch-translate-x-4:where([dir="rtl"], [dir="rtl"] *))::after {
         content: var(--tw-content);
         --tw-translate-x: -16px;
         transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
         }
      </style>
      <style>.root__A7aXF{text-align:left}.root__A7aXF *{-webkit-box-sizing:border-box;box-sizing:border-box;font-family:Arial,Helvetica,sans-serif;font-weight:normal}</style>
   </head>
   <body style="">
      <div id="lanyard_root"></div>
      <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-MM9HQBT" height="0" width="0" style="display:none;visibility:hidden" aria-hidden="true"></iframe></noscript>
      <div id="__next">
         <div aria-hidden="false" class="relative redesign">
            <div id="top"></div>
            <a href="#main" class="skip-content">Skip to main content</a>
            <div id="main-header" class="Header_container__1d1No undefined ">
               <header class="Header_content__dAZ3X">
                  <div class="Header_top__PlXu0">
                     <div class="Header_left__5PtfC">
                        <button type="button" class="Header_menuButton__yPWe7" aria-haspopup="dialog" aria-expanded="false" aria-controls="sidebar-nav">
                           <span class="sr-only">Open search bar and full menu</span>
                           <svg width="23.823529411764703" height="15" viewBox="0 0 27 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M25.0879 14.4998L21.119 10.5309M22.7318 6.65327C22.7318 9.68555 20.2737 12.1437 17.2414 12.1437C14.2091 12.1437 11.751 9.68555 11.751 6.65327C11.751 3.62099 14.2091 1.16284 17.2414 1.16284C20.2737 1.16284 22.7318 3.62099 22.7318 6.65327Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="menu-search-icon__search"></path>
                              <path d="M1 8.5H8.5M1 1H9.38235M1 16H14.6765" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                           </svg>
                        </button>
                     </div>
                     <div class="Header_logoContainer__4UUBU"><a href="/" class="Header_logo__075y4" aria-label="The San Francisco Standard"><img src="/images/logo.svg" width="682" height="48" alt="The San Francisco Standard"></a></div>
                     <div class="Header_right__ajD1O">
                        <a href="/newsletters/" class="Header_newslettersIcon__G3TrK">
                           <span class="sr-only">Newsletters</span>
                           <svg width="18.529411764705884" height="15" viewBox="0 0 21 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path id="Icon" d="M1.25 3.8125L8.90461 9.17073C9.52446 9.60462 9.83438 9.82157 10.1715 9.9056C10.4693 9.97983 10.7807 9.97983 11.0785 9.9056C11.4156 9.82157 11.7255 9.60462 12.3454 9.17073L20 3.8125M5.75 16H15.5C17.0751 16 17.8627 16 18.4643 15.6935C18.9936 15.4238 19.4238 14.9936 19.6935 14.4643C20 13.8627 20 13.0751 20 11.5V5.5C20 3.92485 20 3.13728 19.6935 2.53565C19.4238 2.00645 18.9936 1.57619 18.4643 1.30654C17.8627 1 17.0751 1 15.5 1H5.75C4.17485 1 3.38728 1 2.78565 1.30654C2.25645 1.57619 1.82619 2.00645 1.55654 2.53565C1.25 3.13728 1.25 3.92485 1.25 5.5V11.5C1.25 13.0751 1.25 13.8627 1.55654 14.4643C1.82619 14.9936 2.25645 15.4238 2.78565 15.6935C3.38728 16 4.17485 16 5.75 16Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                           </svg>
                        </a>
                        <a href="/newsletters/" class="Header_newslettersButton__xr071 button button-blue button-xs">Newsletters</a>
                     </div>
                  </div>
               </header>
               <div class="relative">
                  <div class="categories Header_categoriesContainer__AO53Q categories-black">
                     <nav class="Header_categoriesNav__6nMp3">
                        <ul class="Header_categoriesList__wU7Os">
                           <li><a href="/news/">News</a></li>
                           <li><a href="/politics-policy/">Politics &amp; Policy</a></li>
                           <li><a href="/business/">Business</a></li>
                           <li><a href="/opinion/">Opinion</a></li>
                           <li><a href="/life/">Life</a></li>
                           <li><a href="/food-drink/">Food &amp; Drink</a></li>
                           <li><a href="/arts-entertainment/">Arts &amp; Entertainment</a></li>
                        </ul>
                     </nav>
                  </div>
               </div>
            </div>
            <div id="sidebar-nav" role="dialog" tabindex="-1" aria-label="Sidebar menu (press ESC to close)" class="SidebarNav_container__XurOe SidebarNav_containerLoaded__V3euj" aria-hidden="true" style="top: 105px;">
               <nav class="no-scrollbar  SidebarNav_nav__goL_D" aria-label="Sidebar navigation">
                  <button class="SidebarNav_closeButton__qMq2E" tabindex="-1">
                     <span class="sr-only">Close menu</span>
                     <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 13 13" fill="none">
                        <path d="M1 0.823364L6.5 6.20078M12 11.7011L6.5 6.20078M6.5 6.20078L12 0.945825M6.5 6.20078L1 11.8234" stroke="currentColor"></path>
                     </svg>
                  </button>
                  <div hidden="">
                     <form name="sidebar-search" action="/search" class="SidebarNav_searchForm__EJ7zF">
                        <label for="menu-search" class="sr-only">Search query</label><input name="query" id="menu-search" type="text" placeholder="Search" class="SidebarNav_searchInput__fdBrC" value="">
                        <button type="submit" class="SidebarNav_searchSubmit__95E_c" aria-disabled="true">
                           <span class="sr-only">Search</span>
                           <svg width="30" height="14" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 14" fill="none">
                              <path d="M0.891113 7H28.6525M28.6525 7L21.4104 1M28.6525 7L21.4104 13" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                           </svg>
                           <p class="sr-only" aria-live="assertive">Please provide a query to perform your search</p>
                        </button>
                     </form>
                     <ul class="SidebarNav_linkList__sGFXI">
                        <li class=""><a href="/news/">News</a></li>
                        <li class=""><a href="/politics-policy/">Politics &amp; Policy</a></li>
                        <li class=""><a href="/business/">Business</a></li>
                        <li class=""><a href="/opinion/">Opinion</a></li>
                        <li class=""><a href="/life/">Life</a></li>
                        <li class=""><a href="/food-drink/">Food &amp; Drink</a></li>
                        <li class=""><a href="/arts-entertainment/">Arts &amp; Entertainment</a></li>
                        <li class=""><a href="/newsletters/">Newsletters</a></li>
                        <li class=""><a href="https://www.lifeinsevensongs.com/">Podcast</a></li>
                     </ul>
                     <nav aria-labelledby="social-links-title-:Reh8m:">
                        <h2 class="sr-only" id="social-links-title-:Reh8m:">Social Links</h2>
                        <ul class="flex items-center SidebarNav_social__UwFZJ">
                           <li class="mx-3.5 first:ml-0 last:mr-0">
                              <a class="can-hover:hover:text-marigold" target="_blank" rel="noreferrer" aria-label="The San Francisco Standard on Instagram" href="https://www.instagram.com/sfstandard/">
                                 <svg width="18" height="18" viewBox="0 0 13 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M9.971 2.249C9.81673 2.249 9.66593 2.29475 9.53765 2.38045C9.40938 2.46616 9.30941 2.58798 9.25037 2.73051C9.19134 2.87303 9.17589 3.02987 9.20599 3.18117C9.23608 3.33248 9.31037 3.47146 9.41946 3.58054C9.52854 3.68963 9.66752 3.76392 9.81883 3.79401C9.97013 3.82411 10.127 3.80866 10.2695 3.74963C10.412 3.69059 10.5338 3.59062 10.6195 3.46234C10.7053 3.33407 10.751 3.18327 10.751 3.029C10.751 2.82213 10.6688 2.62373 10.5225 2.47746C10.3763 2.33118 10.1779 2.249 9.971 2.249ZM12.961 3.822C12.9484 3.28269 12.8474 2.74911 12.662 2.2425C12.4967 1.80904 12.2395 1.41653 11.908 1.092C11.5861 0.758827 11.1927 0.503215 10.7575 0.3445C10.2522 0.153505 9.71805 0.0501865 9.178 0.039C8.489 -3.63216e-08 8.268 0 6.5 0C4.732 0 4.511 -3.63216e-08 3.822 0.039C3.28195 0.0501865 2.74777 0.153505 2.2425 0.3445C1.80809 0.504822 1.41501 0.760214 1.092 1.092C0.758827 1.41387 0.503215 1.80729 0.3445 2.2425C0.153505 2.74777 0.0501865 3.28195 0.039 3.822C-3.63216e-08 4.511 0 4.732 0 6.5C0 8.268 -3.63216e-08 8.489 0.039 9.178C0.0501865 9.71805 0.153505 10.2522 0.3445 10.7575C0.503215 11.1927 0.758827 11.5861 1.092 11.908C1.41501 12.2398 1.80809 12.4952 2.2425 12.6555C2.74777 12.8465 3.28195 12.9498 3.822 12.961C4.511 13 4.732 13 6.5 13C8.268 13 8.489 13 9.178 12.961C9.71805 12.9498 10.2522 12.8465 10.7575 12.6555C11.1927 12.4968 11.5861 12.2412 11.908 11.908C12.241 11.5847 12.4984 11.1918 12.662 10.7575C12.8474 10.2509 12.9484 9.71731 12.961 9.178C12.961 8.489 13 8.268 13 6.5C13 4.732 13 4.511 12.961 3.822ZM11.791 9.1C11.7863 9.5126 11.7115 9.92141 11.57 10.309C11.4662 10.5919 11.2995 10.8475 11.0825 11.0565C10.8717 11.2713 10.6166 11.4377 10.335 11.544C9.94741 11.6855 9.5386 11.7603 9.126 11.765C8.476 11.7975 8.2355 11.804 6.526 11.804C4.8165 11.804 4.576 11.804 3.926 11.765C3.49758 11.773 3.07099 11.7071 2.665 11.57C2.39576 11.4582 2.15238 11.2923 1.95 11.0825C1.73426 10.8737 1.56965 10.6179 1.469 10.335C1.31031 9.94185 1.22229 9.52376 1.209 9.1C1.209 8.45 1.17 8.2095 1.17 6.5C1.17 4.7905 1.17 4.55 1.209 3.9C1.21191 3.47819 1.28892 3.06016 1.4365 2.665C1.55093 2.39064 1.72657 2.14608 1.95 1.95C2.14748 1.72651 2.39154 1.54901 2.665 1.43C3.06121 1.28703 3.4788 1.2123 3.9 1.209C4.55 1.209 4.7905 1.17 6.5 1.17C8.2095 1.17 8.45 1.17 9.1 1.209C9.5126 1.21373 9.92141 1.28846 10.309 1.43C10.6044 1.53962 10.8695 1.71785 11.0825 1.95C11.2955 2.14967 11.4619 2.39378 11.57 2.665C11.7145 3.06081 11.7892 3.47866 11.791 3.9C11.8235 4.55 11.83 4.7905 11.83 6.5C11.83 8.2095 11.8235 8.45 11.791 9.1ZM6.5 3.1655C5.84078 3.16678 5.19673 3.36344 4.64923 3.73062C4.10173 4.0978 3.67535 4.61903 3.42396 5.22844C3.17258 5.83785 3.10747 6.5081 3.23687 7.1545C3.36626 7.8009 3.68435 8.39445 4.15095 8.86014C4.61755 9.32582 5.21171 9.64276 5.85836 9.7709C6.50501 9.89903 7.17513 9.83262 7.78405 9.58005C8.39298 9.32748 8.91337 8.90009 9.27948 8.35187C9.6456 7.80366 9.841 7.15922 9.841 6.5C9.84186 6.06132 9.75598 5.62679 9.58831 5.22142C9.42063 4.81605 9.17446 4.44783 8.86397 4.13793C8.55347 3.82804 8.18477 3.58259 7.77907 3.4157C7.37337 3.24882 6.93868 3.16379 6.5 3.1655ZM6.5 8.6645C6.0719 8.6645 5.65342 8.53755 5.29747 8.29972C4.94152 8.06188 4.66409 7.72383 4.50026 7.32832C4.33644 6.93281 4.29357 6.4976 4.37709 6.07773C4.46061 5.65786 4.66676 5.27218 4.96947 4.96947C5.27218 4.66676 5.65786 4.46061 6.07773 4.37709C6.4976 4.29357 6.93281 4.33644 7.32832 4.50026C7.72383 4.66409 8.06188 4.94152 8.29972 5.29747C8.53755 5.65342 8.6645 6.0719 8.6645 6.5C8.6645 6.78425 8.60851 7.06571 8.49974 7.32832C8.39096 7.59093 8.23152 7.82954 8.03053 8.03053C7.82954 8.23152 7.59093 8.39096 7.32832 8.49974C7.06571 8.60851 6.78425 8.6645 6.5 8.6645Z" fill="currentColor"></path>
                                 </svg>
                              </a>
                           </li>
                           <li class="mx-3.5 first:ml-0 last:mr-0">
                              <a class="can-hover:hover:text-marigold" target="_blank" rel="noreferrer" aria-label="The San Francisco Standard on Twitter" href="https://twitter.com/sfstandard">
                                 <svg xmlns="http://www.w3.org/2000/svg" width="19.058823529411764" height="18" viewBox="0 0 18 17" fill="none">
                                    <path d="M10.5909 7.19834L16.8157 0H15.3406L9.93558 6.25022L5.61857 0H0.639404L7.16757 9.45144L0.639404 17H2.11459L7.82248 10.3996L12.3816 17H17.3607L10.5909 7.19834ZM8.57039 9.53471L7.90895 8.59356L2.64612 1.10473H4.91191L9.15908 7.14845L9.82052 8.0896L15.3413 15.9455H13.0755L8.57039 9.53471Z" fill="currentColor"></path>
                                 </svg>
                              </a>
                           </li>
                           <li class="mx-3.5 first:ml-0 last:mr-0">
                              <a class="can-hover:hover:text-marigold" target="_blank" rel="noreferrer" aria-label="The San Francisco Standard on Facebook" href="https://www.facebook.com/SFStandard">
                                 <svg xmlns="http://www.w3.org/2000/svg" width="9.6" height="18" viewBox="0 0 8 15" fill="none">
                                    <path d="M6.39293 2.49002H7.75V0.105022C7.09294 0.0340327 6.43274 -0.00101571 5.77214 2.2398e-05C3.80871 2.2398e-05 2.46607 1.24502 2.46607 3.52502V5.49001H0.25V8.16001H2.46607V15H5.12247V8.16001H7.33133L7.66338 5.49001H5.12247V3.78752C5.12247 3.00002 5.32459 2.49002 6.39293 2.49002Z" fill="currentColor"></path>
                                 </svg>
                              </a>
                           </li>
                           <li class="mx-3.5 first:ml-0 last:mr-0">
                              <a class="can-hover:hover:text-marigold" target="_blank" rel="noreferrer" aria-label="The San Francisco Standard on TikTok" href="https://www.tiktok.com/@sfstandard">
                                 <svg width="15.44014401440144" height="18" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 2859 3333" shape-rendering="geometricPrecision" text-rendering="geometricPrecision" image-rendering="optimizeQuality" fill-rule="evenodd" clip-rule="evenodd">
                                    <path d="M2081 0c55 473 319 755 778 785v532c-266 26-499-61-770-225v995c0 1264-1378 1659-1932 753-356-583-138-1606 1004-1647v561c-87 14-180 36-265 65-254 86-398 247-358 531 77 544 1075 705 992-358V1h551z" fill="currentColor"></path>
                                 </svg>
                              </a>
                           </li>
                           <li class="mx-3.5 first:ml-0 last:mr-0">
                              <a class="can-hover:hover:text-marigold" target="_blank" rel="noreferrer" aria-label="The San Francisco Standard on Threads" href="https://www.threads.net/@sfstandard">
                                 <svg width="15.804" height="18" version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 878 1000" xml:space="preserve">
                                    <g>
                                       <path d="M446.7,1000h-0.3c-149.2-1-263.9-50.2-341-146.2C36.9,768.3,1.5,649.4,0.3,500.4v-0.7c1.2-149.1,36.6-267.9,105.2-353.4 C182.5,50.2,297.3,1,446.4,0h0.3h0.3c114.4,0.8,210.1,30.2,284.4,87.4c69.9,53.8,119.1,130.4,146.2,227.8l-85,23.7 c-46-165-162.4-249.3-346-250.6c-121.2,0.9-212.9,39-272.5,113.2C118.4,271,89.6,371.4,88.5,500c1.1,128.6,29.9,229,85.7,298.5 c59.6,74.3,151.3,112.4,272.5,113.2c109.3-0.8,181.6-26.3,241.7-85.2c68.6-67.2,67.4-149.7,45.4-199.9 c-12.9-29.6-36.4-54.2-68.1-72.9c-8,56.3-25.9,101.9-53.5,136.3c-36.9,45.9-89.2,71-155.4,74.6c-50.1,2.7-98.4-9.1-135.8-33.4 c-44.3-28.7-70.2-72.5-73-123.5c-2.7-49.6,17-95.2,55.4-128.4c36.7-31.7,88.3-50.3,149.3-53.8c44.9-2.5,87-0.5,125.8,5.9 c-5.2-30.9-15.6-55.5-31.2-73.2c-21.4-24.4-54.5-36.8-98.3-37.1c-0.4,0-0.8,0-1.2,0c-35.2,0-83,9.7-113.4,55L261.2,327 c40.8-60.6,107-94,186.6-94c0.6,0,1.2,0,1.8,0c133.1,0.8,212.4,82.3,220.3,224.5c4.5,1.9,9,3.9,13.4,5.9 c62.1,29.2,107.5,73.4,131.4,127.9c33.2,75.9,36.3,199.6-64.5,298.3C673.1,965,579.6,999.1,447,1000L446.7,1000L446.7,1000z M488.5,512.9c-10.1,0-20.3,0.3-30.8,0.9c-76.5,4.3-124.2,39.4-121.5,89.3c2.8,52.3,60.5,76.6,116,73.6 c51-2.7,117.4-22.6,128.6-154.6C552.6,516,521.7,512.9,488.5,512.9z" fill="currentColor"></path>
                                    </g>
                                 </svg>
                              </a>
                           </li>
                           <li class="mx-3.5 first:ml-0 last:mr-0">
                              <a class="can-hover:hover:text-marigold" target="_blank" rel="noreferrer" aria-label="The San Francisco Standard on LinkedIn" href="https://www.linkedin.com/company/sfstandard">
                                 <svg xmlns="http://www.w3.org/2000/svg" width="19.2" height="18" viewBox="0 0 16 15" fill="none">
                                    <path d="M15.5 1.10294V13.8971C15.5 14.1896 15.3838 14.4701 15.177 14.677C14.9701 14.8838 14.6896 15 14.3971 15H1.60294C1.31042 15 1.02989 14.8838 0.823044 14.677C0.616202 14.4701 0.5 14.1896 0.5 13.8971V1.10294C0.5 0.810423 0.616202 0.529886 0.823044 0.323044C1.02989 0.116202 1.31042 0 1.60294 0L14.3971 0C14.6896 0 14.9701 0.116202 15.177 0.323044C15.3838 0.529886 15.5 0.810423 15.5 1.10294ZM4.91176 5.73529H2.70588V12.7941H4.91176V5.73529ZM5.11029 3.30882C5.11146 3.14197 5.07974 2.97651 5.01696 2.82191C4.95418 2.66731 4.86156 2.52659 4.7444 2.40779C4.62723 2.28898 4.48781 2.19442 4.3341 2.1295C4.18038 2.06458 4.01539 2.03057 3.84853 2.02941H3.80882C3.4695 2.02941 3.14408 2.16421 2.90414 2.40414C2.66421 2.64408 2.52941 2.9695 2.52941 3.30882C2.52941 3.64814 2.66421 3.97357 2.90414 4.2135C3.14408 4.45344 3.4695 4.58824 3.80882 4.58824C3.97569 4.59234 4.14173 4.56353 4.29746 4.50344C4.45319 4.44335 4.59555 4.35317 4.71642 4.23804C4.83728 4.12291 4.93427 3.9851 5.00186 3.83247C5.06944 3.67985 5.10629 3.5154 5.11029 3.34853V3.30882ZM13.2941 8.50588C13.2941 6.38382 11.9441 5.55882 10.6029 5.55882C10.1638 5.53684 9.72659 5.63037 9.33489 5.83009C8.94319 6.02981 8.6107 6.32874 8.37059 6.69706H8.30882V5.73529H6.23529V12.7941H8.44118V9.03971C8.40929 8.65519 8.53041 8.27362 8.77823 7.97789C9.02605 7.68217 9.38054 7.49616 9.76471 7.46029H9.84853C10.55 7.46029 11.0706 7.90147 11.0706 9.01324V12.7941H13.2765L13.2941 8.50588Z" fill="currentColor"></path>
                                 </svg>
                              </a>
                           </li>
                           <li class="mx-3.5 first:ml-0 last:mr-0">
                              <a class="can-hover:hover:text-marigold" target="_blank" rel="noreferrer" aria-label="The San Francisco Standard on Apple News" href="https://apple.news/TDHP6_LSbQJGxQ_S0TndMnw?subscribe=1">
                                 <svg version="1.1" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 361 361" xml:space="preserve" width="18" height="18">
                                    <path d="M359.99652,112.58435c0-4.29785,0.00348-8.59747-0.02289-12.89532c-0.02283-3.62109-0.065-7.24042-0.16168-10.86151 c-0.21448-7.88733-0.67847-15.8432-2.08112-23.64435c-1.42365-7.9137-3.74548-15.27893-7.40674-22.46838 c-3.5979-7.06635-8.29962-13.53162-13.90826-19.13904c-5.60693-5.60565-12.07507-10.30426-19.14264-13.90253 c-7.19403-3.6615-14.56567-5.98535-22.48566-7.40741c-7.80048-1.401-15.75562-1.86505-23.64398-2.07776 c-3.62073-0.09845-7.24152-0.14062-10.86401-0.16168C255.98029,0,251.68109,0.00171,247.38013,0.00171L197.99182,0h-36.01611 l-49.36902,0.00348c-4.29742,0-8.59668-0.00348-12.8941,0.02289c-3.62079,0.02283-7.23981,0.065-10.86053,0.16168 c-7.8866,0.21448-15.8418,0.67853-23.64221,2.0813C57.29688,3.69318,49.93231,6.0152,42.74347,9.67676 c-7.06573,3.59821-13.5304,8.30035-19.13733,13.90955C18.00098,29.19373,13.3028,35.66248,9.7049,42.73065 c-3.66119,7.1947-5.9848,14.56696-7.40674,22.48767c-1.40088,7.80121-1.86493,15.75702-2.07758,23.64612 c-0.09845,3.62109-0.14062,7.24219-0.16168,10.86505c-0.02637,4.29956-0.02466,8.59918-0.02466,12.90057l-0.00171,49.39276 L0,198.00964l0.00348,49.37347c0,4.29785-0.00348,8.59747,0.02289,12.89532c0.02283,3.62109,0.065,7.24042,0.16168,10.86151 c0.21448,7.88733,0.67847,15.84314,2.08112,23.64435c1.42365,7.91364,3.74548,15.27887,7.40674,22.46832 c3.5979,7.06641,8.29962,13.53168,13.90826,19.1391c5.60693,5.60565,12.07507,10.30426,19.14264,13.90253 c7.19403,3.6615,14.56567,5.98535,22.48566,7.40741c7.80048,1.401,15.75562,1.86505,23.64398,2.07776 c3.62073,0.09845,7.24152,0.14062,10.86401,0.16168c4.29926,0.02637,8.59845,0.02466,12.89941,0.02466l49.38831,0.00171 L198.05682,360l49.36896-0.00354c4.29749,0,8.59668,0.00354,12.89417-0.02283c3.62079-0.02289,7.23975-0.06506,10.86053-0.16174 c7.8866-0.21442,15.84174-0.67853,23.64221-2.08124c7.91296-1.42383,15.27753-3.74591,22.46631-7.40741 c7.0658-3.59827,13.53046-8.30042,19.13733-13.90961c5.60516-5.60742,10.30341-12.07617,13.90131-19.14429 c3.66119-7.19476,5.9848-14.56702,7.40674-22.48773c1.40088-7.80115,1.86487-15.75702,2.07758-23.64606 c0.09839-3.62109,0.14062-7.24219,0.16168-10.86505c0.02637-4.29962,0.0246-8.59924,0.0246-12.90057L360,197.97711v-36.01935 L359.99652,112.58435z M192.35278,76.875h62.67316l8.56824,0.00031c0.74597,0,1.49194-0.00037,2.23792,0.00415 c0.62836,0.00391,1.25659,0.01111,1.88483,0.02808c1.36823,0.03699,2.74854,0.11761,4.10162,0.3609 c1.37402,0.24695,2.65289,0.65021,3.90118,1.28601c1.22614,0.62463,2.34808,1.44043,3.32098,2.41382 c0.97296,0.97339,1.78851,2.09613,2.41284,3.32312c0.63513,1.24829,1.03802,2.52704,1.28485,3.90088 c0.24335,1.35437,0.32404,2.73572,0.36096,4.10529c0.01703,0.62848,0.02429,1.25702,0.02808,1.88568 c0.00452,0.74634,0.00421,1.49268,0.00421,2.23895l0.00024,8.57245v62.66766c0,1.80176-2.42157,2.39001-3.24384,0.78693 c-19.6405-38.29102-50.03455-68.68787-88.32208-88.33002C189.96301,79.29681,190.55121,76.875,192.35278,76.875z M167.67975,283.125 h-62.67316l-8.5683-0.00031c-0.74591,0-1.49188,0.00031-2.23785-0.00421c-0.62836-0.00385-1.25659-0.01111-1.88483-0.02802 c-1.36829-0.03705-2.7486-0.11768-4.10168-0.3609c-1.37402-0.24701-2.65283-0.65021-3.90112-1.28601 c-1.2262-0.62469-2.34808-1.44049-3.32098-2.41382c-0.97296-0.97345-1.78851-2.09619-2.4129-3.32312 c-0.63507-1.24829-1.03796-2.52704-1.28485-3.90094c-0.24329-1.35437-0.32397-2.73572-0.3609-4.10529 c-0.01703-0.62842-0.02429-1.25696-0.02814-1.88568c-0.00446-0.74628-0.00415-1.49255-0.00415-2.23895l-0.00031-8.57245v-62.66766 c0-1.8017,2.42157-2.38995,3.2439-0.78687c19.64044,38.29095,50.03455,68.68781,88.32208,88.33008 C170.06958,280.70325,169.48132,283.125,167.67975,283.125z M283.13177,263.58539c0,0.74585,0.00037,1.49225-0.00427,2.23804 c-0.00385,0.62878-0.01117,1.25659-0.02789,1.88489c-0.03705,1.36841-0.11786,2.74878-0.36096,4.10217 c-0.24689,1.37396-0.65002,2.65302-1.28601,3.90192c-0.62451,1.22577-1.44019,2.34766-2.41339,3.32086 c-0.97351,0.97308-2.09607,1.78857-3.323,2.41321c-1.24811,0.63513-2.52673,1.03772-3.90057,1.28467 c-1.35406,0.24347-2.73547,0.32404-4.10486,0.36102c-0.62842,0.01715-1.2569,0.02435-1.88538,0.02808 c-0.74628,0.00458-1.49268,0.0047-2.23877,0.0047H237.8374c-5.24506-0.00012-7.9292-0.97321-11.87524-4.91925L81.81934,134.0498 c-3.89667-3.89722-4.91876-6.61902-4.91876-11.87616l0.00012-25.75909c-0.00006-0.74573-0.00031-1.49225,0.00427-2.23804 c0.00391-0.62878,0.01117-1.25653,0.02789-1.88483c0.03711-1.36847,0.11786-2.74878,0.36096-4.10217 c0.24689-1.37402,0.65002-2.65308,1.28607-3.90186c0.62439-1.22577,1.44006-2.34778,2.41339-3.32092 c0.97351-0.97308,2.09607-1.78857,3.323-2.41321c1.24805-0.63513,2.52661-1.03772,3.90057-1.28473 c1.354-0.24347,2.73535-0.32404,4.1048-0.36102c0.62842-0.01715,1.2569-0.02435,1.88544-0.02802 c0.74628-0.00464,1.49261-0.0047,2.23871-0.0047h25.74927c5.24506,0.00006,7.9292,0.97314,11.87524,4.91925l144.14282,144.15582 c3.89673,3.89734,4.91882,6.61902,4.91882,11.87622L283.13177,263.58539z" fill="currentColor"></path>
                                 </svg>
                              </a>
                           </li>
                        </ul>
                     </nav>
                  </div>
               </nav>
            </div>
            <main id="main" aria-hidden="false">
               <div class="article-view">
                  <div class="HeaderDefault_header___IvIn">
                     <div class="HeaderDefault_container__H_ZBD">
                        <div class="HeaderDefault_content__WVUkg">
                           <a class="HeaderDefault_section__GiO1x" href="/food-drink/">Food &amp; Drink</a>
                           <h1 class="HeaderDefault_heading__nwXEl">Restaurant workers fear they could pay highest cost in junk-fee battle</h1>
                           <p class="HeaderDefault_subhead__TAbC2">Diners hate them. Restaurant owners say they need them. But in the battle over service fees, it’s servers and cooks on the front lines. </p>
                        </div>
                        <figure class="HeaderDefault_imageContainer__q_hni">
                           <div class=""><span style="box-sizing:border-box;display:block;overflow:hidden;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;position:relative"><span style="box-sizing:border-box;display:block;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;padding-top:66.650390625%"></span><img alt="An illustration of a large glass tip jar breaking into pieces as people dressed as chefs and servers stand around and watch." sizes="100vw" srcset="https://content.sfstandard.com/wp-content/uploads/2024/06/featured_20240621-junkfeeban.jpg?w=640&amp;q=75 640w, https://content.sfstandard.com/wp-content/uploads/2024/06/featured_20240621-junkfeeban.jpg?w=750&amp;q=75 750w, https://content.sfstandard.com/wp-content/uploads/2024/06/featured_20240621-junkfeeban.jpg?w=828&amp;q=75 828w, https://content.sfstandard.com/wp-content/uploads/2024/06/featured_20240621-junkfeeban.jpg?w=1080&amp;q=75 1080w, https://content.sfstandard.com/wp-content/uploads/2024/06/featured_20240621-junkfeeban.jpg?w=1200&amp;q=75 1200w, https://content.sfstandard.com/wp-content/uploads/2024/06/featured_20240621-junkfeeban.jpg?w=1920&amp;q=75 1920w, https://content.sfstandard.com/wp-content/uploads/2024/06/featured_20240621-junkfeeban.jpg?w=2048&amp;q=75 2048w, https://content.sfstandard.com/wp-content/uploads/2024/06/featured_20240621-junkfeeban.jpg?w=3840&amp;q=75 3840w" src="https://content.sfstandard.com/wp-content/uploads/2024/06/featured_20240621-junkfeeban.jpg?w=3840&amp;q=75" decoding="async" data-nimg="responsive" class="HeaderDefault_image__LRNYs lazyloaded" style="position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%"></span></div>
                           <figcaption class="HeaderDefault_imageCaption___Mrur"><span class="sr-only">Source: </span>AI illustration by Clark Miller for The Standard; photo by Unsplash</figcaption>
                        </figure>
                     </div>
                  </div>
                  <div class="content-grid article-content">
                     <div class="Metadata_container__AUoBj">
                        <div class="PopupSharer_container__GO8Ug">
                           <button class="Metadata_shareButton__hvaZs" aria-haspopup="dialog" aria-expanded="false" aria-controls="share-popup-:R1al8m:" id="share-button-:R1al8m:">
                              Share
                              <svg aria-hidden="true" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                 <path d="M15.3261 8.50617C15.5296 8.3318 15.6313 8.24461 15.6686 8.14086C15.7013 8.0498 15.7013 7.9502 15.6686 7.85914C15.6313 7.75539 15.5296 7.6682 15.3261 7.49383L8.26719 1.44331C7.917 1.14315 7.74191 0.993063 7.59367 0.989386C7.46483 0.986191 7.34177 1.04279 7.26035 1.14269C7.16667 1.25764 7.16667 1.48825 7.16667 1.94948V5.52886C5.38777 5.84007 3.75966 6.74146 2.54976 8.09489C1.23069 9.57043 0.501029 11.48 0.5 13.4591V13.9691C1.37445 12.9157 2.46626 12.0638 3.70063 11.4716C4.78891 10.9495 5.96535 10.6403 7.16667 10.5588V14.0505C7.16667 14.5117 7.16667 14.7424 7.26035 14.8573C7.34177 14.9572 7.46483 15.0138 7.59367 15.0106C7.74191 15.0069 7.917 14.8569 8.26719 14.5567L15.3261 8.50617Z" fill="currentColor"></path>
                              </svg>
                           </button>
                           <div role="dialog" hidden="" aria-hidden="true" aria-labelledby="share-popup-heading-:R1al8m:" id="share-popup-:R1al8m:" class="PopupSharer_popup__FJk6W">
                              <div class="PopupSharer_popupContent__K5DEQ">
                                 <h2 id="share-popup-heading-:R1al8m:">Share</h2>
                                 <ul aria-labelledby="share-button-:R1al8m:">
                                    <li class="PopupSharer_medium__ZsVyi">
                                       <div class="PopupSharer_divider__YC2O5"></div>
                                       <button class="PopupSharer_copyLink__obNYu">
                                          <span class="PopupSharer_icon__i0dNt">
                                             <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15.833333333333334" viewBox="0 0 18 19" fill="none">
                                                <path d="M7.50003 10.25C7.82212 10.6806 8.23305 11.0369 8.70494 11.2947C9.17684 11.5525 9.69866 11.7058 10.235 11.7443C10.7714 11.7827 11.3097 11.7053 11.8135 11.5173C12.3174 11.3294 12.7749 11.0353 13.155 10.655L15.405 8.405C16.0881 7.69775 16.4661 6.75049 16.4576 5.76725C16.449 4.78401 16.0546 3.84347 15.3593 3.14818C14.6641 2.4529 13.7235 2.05852 12.7403 2.04998C11.757 2.04143 10.8098 2.41941 10.1025 3.1025L8.81253 4.385M10.5 8.75C10.1779 8.31941 9.76701 7.96312 9.29512 7.7053C8.82322 7.44748 8.3014 7.29417 7.76504 7.25575C7.22868 7.21734 6.69034 7.29473 6.18652 7.48267C5.6827 7.67061 5.22519 7.9647 4.84503 8.345L2.59503 10.595C1.91194 11.3023 1.53396 12.2495 1.5425 13.2328C1.55104 14.216 1.94543 15.1565 2.64071 15.8518C3.33599 16.5471 4.27654 16.9415 5.25977 16.95C6.24301 16.9586 7.19027 16.5806 7.89753 15.8975L9.18003 14.615" stroke="currentColor" stroke-width="1.3" stroke-linecap="round" stroke-linejoin="round"></path>
                                             </svg>
                                          </span>
                                          Copy link<span class="sr-only">to this article</span>
                                       </button>
                                    </li>
                                    <li class="PopupSharer_medium__ZsVyi">
                                       <div class="PopupSharer_divider__YC2O5"></div>
                                       <a target="_blank" aria-label="Email this article" rel="noreferrer" href="mailto:?subject=Restaurant workers fear they could pay highest cost in junk-fee battle&amp;body=From The San Francisco Standard%0A%0ARestaurant workers fear they could pay highest cost in junk-fee battle%0A%0ADiners hate them. Restaurant owners say they need them. But in the battle over service fees, it’s servers and cooks on the front lines. %0A%0Ahttps://sfstandard.com/2024/06/24/service-fee-restaurants-san-francisco/?utm_source=email_sitebutton&amp;utm_medium=site_buttons&amp;utm_campaign=site_buttons">
                                          <span class="PopupSharer_icon__i0dNt">
                                             <svg xmlns="http://www.w3.org/2000/svg" width="13" height="10.833333333333334" viewBox="0 0 18 15" fill="none">
                                                <path d="M1.5 3.75L7.62369 8.03658C8.11957 8.3837 8.36751 8.55726 8.6372 8.62448C8.87542 8.68386 9.12458 8.68386 9.3628 8.62448C9.63249 8.55726 9.88043 8.3837 10.3763 8.03658L16.5 3.75M5.1 13.5H12.9C14.1601 13.5 14.7902 13.5 15.2715 13.2548C15.6948 13.039 16.039 12.6948 16.2548 12.2715C16.5 11.7902 16.5 11.1601 16.5 9.9V5.1C16.5 3.83988 16.5 3.20982 16.2548 2.72852C16.039 2.30516 15.6948 1.96095 15.2715 1.74524C14.7902 1.5 14.1601 1.5 12.9 1.5H5.1C3.83988 1.5 3.20982 1.5 2.72852 1.74524C2.30516 1.96095 1.96095 2.30516 1.74524 2.72852C1.5 3.20982 1.5 3.83988 1.5 5.1V9.9C1.5 11.1601 1.5 11.7902 1.74524 12.2715C1.96095 12.6948 2.30516 13.039 2.72852 13.2548C3.20982 13.5 3.83988 13.5 5.1 13.5Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                             </svg>
                                          </span>
                                          Email
                                       </a>
                                    </li>
                                    <li class="PopupSharer_medium__ZsVyi">
                                       <div class="PopupSharer_divider__YC2O5"></div>
                                       <a target="_blank" aria-label="Share this article on Facebook" rel="noreferrer" href="https://www.facebook.com/sharer.php?u=https://sfstandard.com/2024/06/24/service-fee-restaurants-san-francisco/?utm_source=facebook_sitebutton&amp;utm_medium=site_buttons&amp;utm_campaign=site_buttons">
                                          <span class="PopupSharer_icon__i0dNt">
                                             <svg xmlns="http://www.w3.org/2000/svg" width="6.933333333333334" height="13" viewBox="0 0 8 15" fill="none">
                                                <path d="M6.39293 2.49002H7.75V0.105022C7.09294 0.0340327 6.43274 -0.00101571 5.77214 2.2398e-05C3.80871 2.2398e-05 2.46607 1.24502 2.46607 3.52502V5.49001H0.25V8.16001H2.46607V15H5.12247V8.16001H7.33133L7.66338 5.49001H5.12247V3.78752C5.12247 3.00002 5.32459 2.49002 6.39293 2.49002Z" fill="currentColor"></path>
                                             </svg>
                                          </span>
                                          Facebook
                                       </a>
                                    </li>
                                    <li class="PopupSharer_medium__ZsVyi">
                                       <div class="PopupSharer_divider__YC2O5"></div>
                                       <a target="_blank" aria-label="Share this article on Twitter" rel="noreferrer" href="https://twitter.com/intent/tweet?text=Restaurant%20workers%20fear%20they%20could%20pay%20highest%20cost%20in%20junk-fee%20battle&amp;via=sfstandard&amp;url=https://sfstandard.com/2024/06/24/service-fee-restaurants-san-francisco/?utm_source=twitter_sitebutton&amp;utm_medium=site_buttons&amp;utm_campaign=site_buttons">
                                          <span class="PopupSharer_icon__i0dNt">
                                             <svg xmlns="http://www.w3.org/2000/svg" width="15.882352941176471" height="15" viewBox="0 0 18 17" fill="none">
                                                <path d="M10.5909 7.19834L16.8157 0H15.3406L9.93558 6.25022L5.61857 0H0.639404L7.16757 9.45144L0.639404 17H2.11459L7.82248 10.3996L12.3816 17H17.3607L10.5909 7.19834ZM8.57039 9.53471L7.90895 8.59356L2.64612 1.10473H4.91191L9.15908 7.14845L9.82052 8.0896L15.3413 15.9455H13.0755L8.57039 9.53471Z" fill="currentColor"></path>
                                             </svg>
                                          </span>
                                          Twitter
                                       </a>
                                    </li>
                                    <li class="PopupSharer_medium__ZsVyi">
                                       <div class="PopupSharer_divider__YC2O5"></div>
                                       <a target="_blank" aria-label="Share this article on LinkedIn" rel="noreferrer" href="https://www.linkedin.com/cws/share?url=https://sfstandard.com/2024/06/24/service-fee-restaurants-san-francisco/?utm_source=linkedin_sitebutton&amp;utm_medium=site_buttons&amp;utm_campaign=site_buttons&amp;title=Restaurant workers fear they could pay highest cost in junk-fee battle">
                                          <span class="PopupSharer_icon__i0dNt">
                                             <svg xmlns="http://www.w3.org/2000/svg" width="13.866666666666667" height="13" viewBox="0 0 16 15" fill="none">
                                                <path d="M15.5 1.10294V13.8971C15.5 14.1896 15.3838 14.4701 15.177 14.677C14.9701 14.8838 14.6896 15 14.3971 15H1.60294C1.31042 15 1.02989 14.8838 0.823044 14.677C0.616202 14.4701 0.5 14.1896 0.5 13.8971V1.10294C0.5 0.810423 0.616202 0.529886 0.823044 0.323044C1.02989 0.116202 1.31042 0 1.60294 0L14.3971 0C14.6896 0 14.9701 0.116202 15.177 0.323044C15.3838 0.529886 15.5 0.810423 15.5 1.10294ZM4.91176 5.73529H2.70588V12.7941H4.91176V5.73529ZM5.11029 3.30882C5.11146 3.14197 5.07974 2.97651 5.01696 2.82191C4.95418 2.66731 4.86156 2.52659 4.7444 2.40779C4.62723 2.28898 4.48781 2.19442 4.3341 2.1295C4.18038 2.06458 4.01539 2.03057 3.84853 2.02941H3.80882C3.4695 2.02941 3.14408 2.16421 2.90414 2.40414C2.66421 2.64408 2.52941 2.9695 2.52941 3.30882C2.52941 3.64814 2.66421 3.97357 2.90414 4.2135C3.14408 4.45344 3.4695 4.58824 3.80882 4.58824C3.97569 4.59234 4.14173 4.56353 4.29746 4.50344C4.45319 4.44335 4.59555 4.35317 4.71642 4.23804C4.83728 4.12291 4.93427 3.9851 5.00186 3.83247C5.06944 3.67985 5.10629 3.5154 5.11029 3.34853V3.30882ZM13.2941 8.50588C13.2941 6.38382 11.9441 5.55882 10.6029 5.55882C10.1638 5.53684 9.72659 5.63037 9.33489 5.83009C8.94319 6.02981 8.6107 6.32874 8.37059 6.69706H8.30882V5.73529H6.23529V12.7941H8.44118V9.03971C8.40929 8.65519 8.53041 8.27362 8.77823 7.97789C9.02605 7.68217 9.38054 7.49616 9.76471 7.46029H9.84853C10.55 7.46029 11.0706 7.90147 11.0706 9.01324V12.7941H13.2765L13.2941 8.50588Z" fill="currentColor"></path>
                                             </svg>
                                          </span>
                                          LinkedIn
                                       </a>
                                    </li>
                                    <li class="PopupSharer_medium__ZsVyi">
                                       <div class="PopupSharer_divider__YC2O5"></div>
                                       <a target="_blank" aria-label="Share this article on Telegram" rel="noreferrer" href="https://t.me/share/url?url=https://sfstandard.com/2024/06/24/service-fee-restaurants-san-francisco/?utm_source=telegram_sitebutton&amp;utm_medium=site_buttons&amp;utm_campaign=site_buttons&amp;text=Restaurant workers fear they could pay highest cost in junk-fee battle">
                                          <span class="PopupSharer_icon__i0dNt">
                                             <svg xmlns="http://www.w3.org/2000/svg" width="14.0625" height="15" viewBox="0 0 15 16" fill="none">
                                                <path fill-rule="evenodd" clip-rule="evenodd" d="M15 8.22339C15 12.3655 11.6421 15.7234 7.5 15.7234C3.35786 15.7234 0 12.3655 0 8.22339C0 4.08125 3.35786 0.723389 7.5 0.723389C11.6421 0.723389 15 4.08125 15 8.22339ZM7.76877 6.26022C7.03928 6.56364 5.58134 7.19164 3.39493 8.14422C3.0399 8.2854 2.85391 8.42352 2.83698 8.55858C2.80837 8.78682 3.09419 8.8767 3.48342 8.99909C3.53636 9.01574 3.59122 9.03299 3.64746 9.05127C4.0304 9.17575 4.54552 9.32137 4.8133 9.32716C5.05621 9.33241 5.32733 9.23226 5.62665 9.02673C7.66947 7.64776 8.72399 6.95077 8.79019 6.93574C8.8369 6.92514 8.90162 6.91181 8.94548 6.95079C8.98933 6.98977 8.98502 7.06359 8.98038 7.08339C8.95206 7.2041 7.83008 8.2472 7.24945 8.787C7.06844 8.95528 6.94005 9.07465 6.9138 9.10191C6.855 9.16298 6.79508 9.22075 6.73749 9.27627C6.38173 9.61922 6.11494 9.8764 6.75226 10.2964C7.05853 10.4982 7.3036 10.6651 7.5481 10.8316C7.81511 11.0134 8.08143 11.1948 8.42601 11.4207C8.5138 11.4782 8.59764 11.538 8.67931 11.5962C8.99005 11.8177 9.26922 12.0168 9.61412 11.985C9.81453 11.9666 10.0215 11.7781 10.1267 11.2161C10.3751 9.88785 10.8635 7.00992 10.9764 5.824C10.9863 5.72009 10.9738 5.58712 10.9638 5.52875C10.9539 5.47037 10.933 5.38721 10.8571 5.32564C10.7672 5.25272 10.6285 5.23735 10.5665 5.23844C10.2844 5.24341 9.8516 5.3939 7.76877 6.26022Z" fill="currentColor"></path>
                                             </svg>
                                          </span>
                                          Telegram
                                       </a>
                                    </li>
                                    <li class="PopupSharer_medium__ZsVyi">
                                       <div class="PopupSharer_divider__YC2O5"></div>
                                       <a target="_blank" aria-label="Share this article on WhatsApp" rel="noreferrer" href="https://api.whatsapp.com/send?text=Restaurant workers fear they could pay highest cost in junk-fee battle https://sfstandard.com/2024/06/24/service-fee-restaurants-san-francisco/?utm_source=whatsapp_sitebutton&amp;utm_medium=site_buttons&amp;utm_campaign=site_buttons">
                                          <span class="PopupSharer_icon__i0dNt">
                                             <svg xmlns="http://www.w3.org/2000/svg" width="14.0625" height="15" viewBox="0 0 15 16" fill="none">
                                                <path d="M0.0356445 15.7234L1.09002 11.8715C0.439394 10.744 0.0975195 9.46589 0.0981445 8.15526C0.10002 4.05776 3.43439 0.723389 7.53127 0.723389C9.51939 0.724014 11.3856 1.49839 12.7894 2.90339C14.1925 4.30839 14.965 6.17589 14.9644 8.16214C14.9625 12.2603 11.6281 15.5946 7.53127 15.5946C6.28752 15.594 5.06189 15.2821 3.97627 14.6896L0.0356445 15.7234ZM4.15877 13.344C5.20627 13.9659 6.20627 14.3384 7.52877 14.339C10.9338 14.339 13.7075 11.5678 13.7094 8.16089C13.7106 4.74714 10.95 1.97964 7.53377 1.97839C4.12627 1.97839 1.35439 4.74964 1.35314 8.15589C1.35252 9.54651 1.76002 10.5878 2.44439 11.6771L1.82002 13.9571L4.15877 13.344ZM11.2756 9.92901C11.2294 9.85151 11.1056 9.80526 10.9194 9.71214C10.7338 9.61901 9.82064 9.16964 9.65002 9.10776C9.48002 9.04589 9.35627 9.01464 9.23189 9.20089C9.10814 9.38651 8.75189 9.80526 8.64377 9.92901C8.53564 10.0528 8.42689 10.0684 8.24127 9.97526C8.05564 9.88214 7.45689 9.68651 6.74752 9.05339C6.19564 8.56089 5.82252 7.95276 5.71439 7.76651C5.60627 7.58089 5.70314 7.48026 5.79564 7.38776C5.87939 7.30464 5.98127 7.17089 6.07439 7.06214C6.16877 6.95464 6.19939 6.87714 6.26189 6.75276C6.32377 6.62901 6.29314 6.52026 6.24627 6.42714C6.19939 6.33464 5.82814 5.42026 5.67377 5.04839C5.52252 4.68651 5.36939 4.73526 5.25564 4.72964L4.89939 4.72339C4.77564 4.72339 4.57439 4.76964 4.40439 4.95589C4.23439 5.14214 3.75439 5.59089 3.75439 6.50526C3.75439 7.41964 4.42002 8.30276 4.51252 8.42651C4.60564 8.55026 5.82189 10.4265 7.68502 11.2309C8.12814 11.4221 8.47439 11.5365 8.74377 11.6221C9.18877 11.7634 9.59377 11.7434 9.91377 11.6959C10.2706 11.6428 11.0125 11.2465 11.1675 10.8128C11.3225 10.3784 11.3225 10.0065 11.2756 9.92901Z" fill="currentColor"></path>
                                             </svg>
                                          </span>
                                          WhatsApp
                                       </a>
                                    </li>
                                    <li class="PopupSharer_medium__ZsVyi">
                                       <div class="PopupSharer_divider__YC2O5"></div>
                                       <a target="_blank" aria-label="Share this article on Reddit" rel="noreferrer" href="https://www.reddit.com/submit?url=https://sfstandard.com/2024/06/24/service-fee-restaurants-san-francisco/?utm_source=reddit_sitebutton&amp;utm_medium=site_buttons&amp;utm_campaign=site_buttons&amp;title=Restaurant workers fear they could pay highest cost in junk-fee battle">
                                          <span class="PopupSharer_icon__i0dNt">
                                             <svg xmlns="http://www.w3.org/2000/svg" width="14.857142857142856" height="13" viewBox="0 0 16 14" fill="none">
                                                <path d="M15.061 6.83855C15.061 5.95567 14.3445 5.23914 13.4616 5.23914C13.0266 5.23914 12.6427 5.40547 12.3612 5.68697C11.2736 4.90646 9.76378 4.39465 8.10039 4.33067L8.82972 0.914334L11.1968 1.41335C11.2224 2.01473 11.7215 2.50095 12.3356 2.50095C12.9626 2.50095 13.4744 1.98914 13.4744 1.36217C13.4744 0.735201 12.9626 0.223389 12.3356 0.223389C11.8878 0.223389 11.5039 0.479294 11.3248 0.863152L8.67618 0.300161C8.59941 0.287366 8.52264 0.300161 8.45866 0.338546C8.39469 0.376932 8.3563 0.440909 8.33071 0.51768L7.52461 4.33067C5.82283 4.38185 4.3002 4.88087 3.1998 5.68697C2.91831 5.41827 2.52165 5.23914 2.09941 5.23914C1.21654 5.23914 0.5 5.95567 0.5 6.83855C0.5 7.4911 0.883858 8.0413 1.44685 8.29721C1.42126 8.45075 1.40846 8.61709 1.40846 8.78343C1.40846 11.2401 4.26181 13.2234 7.79331 13.2234C11.3248 13.2234 14.1781 11.2401 14.1781 8.78343C14.1781 8.61709 14.1654 8.46355 14.1398 8.31C14.6644 8.0541 15.061 7.4911 15.061 6.83855ZM4.12106 7.97733C4.12106 7.35036 4.63287 6.83855 5.25984 6.83855C5.88681 6.83855 6.39862 7.35036 6.39862 7.97733C6.39862 8.60429 5.88681 9.11611 5.25984 9.11611C4.63287 9.11611 4.12106 8.60429 4.12106 7.97733ZM10.4803 10.9842C9.6998 11.7647 8.21555 11.8159 7.78051 11.8159C7.34547 11.8159 5.84842 11.7519 5.08071 10.9842C4.96555 10.8691 4.96555 10.6771 5.08071 10.562C5.19587 10.4468 5.38779 10.4468 5.50295 10.562C5.98917 11.0482 7.03839 11.2273 7.79331 11.2273C8.54823 11.2273 9.58465 11.0482 10.0837 10.562C10.1988 10.4468 10.3907 10.4468 10.5059 10.562C10.5955 10.6899 10.5955 10.8691 10.4803 10.9842ZM10.2756 9.11611C9.64862 9.11611 9.13681 8.60429 9.13681 7.97733C9.13681 7.35036 9.64862 6.83855 10.2756 6.83855C10.9026 6.83855 11.4144 7.35036 11.4144 7.97733C11.4144 8.60429 10.9026 9.11611 10.2756 9.11611Z" fill="currentColor"></path>
                                             </svg>
                                          </span>
                                          Reddit
                                       </a>
                                    </li>
                                 </ul>
                                 <button class="absolute lg:opacity-0 top-2.5 right-2.5 can-hover:hover:opacity-80 transition focus:opacity-100">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 13 13" fill="none">
                                       <path d="M1 0.823364L6.5 6.20078M12 11.7011L6.5 6.20078M6.5 6.20078L12 0.945825M6.5 6.20078L1 11.8234" stroke="currentColor"></path>
                                    </svg>
                                    <span class="sr-only">Close share menu</span>
                                 </button>
                              </div>
                           </div>
                           <div class="lg:hidden fixed z-40 top-0 bottom-0 left-0 right-0 bg-black/50" hidden="" aria-hidden="true"></div>
                        </div>
                        <div class="Metadata_metadata__8i2T2">
                           <span>By <a class="" href="https://sfstandard.com/author/lauren-saria/">Lauren Saria</a></span>
                           <time datetime="2024-06-24T17:00:00.000Z">
                              Published <!-- -->Jun. 24, 2024 • 10:00am
                           </time>
                        </div>
                     </div>
                     <div class="floats">
                        <div class="article-body undefined text-left" id="">
                           <p>In an industry known for its sky-high turnover rates, Noel Madayag is an outlier. He’s been a cook at <a href="https://www.cassavasf.com/">Cassava</a> since the business opened 12 years ago. He likes working at the upscale restaurant, which has made <a href="https://www.bizjournals.com/sanfrancisco/news/2023/03/03/yuka-ioroi-cassava-north-beach-restaurant.html">headlines</a> for paying all staff at least $20 an hour and providing full health benefits including a 401k with a 5% employer match.&nbsp;</p>
                        </div>
                        <div class="article-body undefined text-left" id="">
                           <p>But as of last week, Madayag is preparing for a job hunt. “I have been working on my resume,” he said. “I need something to supplement [my income].”&nbsp;</p>
                        </div>
                        <div class="article-body undefined text-left" id="">
                           <p>Madayag is just one of many San Francisco restaurant industry workers bracing for the impact of <a href="https://leginfo.legislature.ca.gov/faces/billNavClient.xhtml?bill_id=202320240SB478">SB 478</a>, a California law aimed at eliminating <a href="https://www.nytimes.com/interactive/2024/02/23/business/what-is-drip-pricing.html">drip pricing</a> and junk fees. It’s set to go into effect on July 1 and would require restaurants to <a href="https://sfstandard.com/2024/05/02/restaurant-san-francisco-surcharges-service-fees-rob-bonta/">stop using mandatory surcharges</a>, including the 20% service fee that allows Cassava to provide its staff with health insurance and retirement funds.&nbsp;</p>
                        </div>
                        <div class="article-body undefined text-left" id="">
                           <p>Responding to a deafening roar from restaurateurs who fear the unintended consequences of the law’s passage, state legislators are hustling to pass a <a href="https://sfstandard.com/2024/06/06/san-francisco-haney-wiener-sb1528/">carveout for restaurants</a> this week. The revised bill has already received assembly approval, but if it fails to emerge out of the state senate or get signed into law by Governor Newsom next week, most restaurant owners say they’ll be forced to raise menu prices.&nbsp;</p>
                        </div>
                     </div>
                     <figure id="" class="ImageBlock_ImageFigure__YkqC2  inset-image mobile-grid-full mobile-grid-full">
                        <div class="relative">
                           <div>
                              <span style="box-sizing:border-box;display:block;overflow:hidden;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;position:relative">
                                 <span style="box-sizing:border-box;display:block;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;padding-top:66.7%"></span><img alt="A man holding a wine bottle explains something to three seated men in a modern restaurant, with food and drinks on the table and greenery in the foreground." src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" decoding="async" data-nimg="responsive" class="block  lazyloaded" style="position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%;background-size:cover;background-position:0% 0%;filter:blur(20px);background-image:url(&quot;data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==&quot;)">
                                 <noscript><img alt="A man holding a wine bottle explains something to three seated men in a modern restaurant, with food and drinks on the table and greenery in the foreground." sizes="(min-width: 1001px) 600px, (min-width: 768px) 700px, 100vw" srcSet="https://content.sfstandard.com/wp-content/uploads/2024/06/20240328_allthingsconsumed-me-19.jpg?w=640&amp;q=75 640w, https://content.sfstandard.com/wp-content/uploads/2024/06/20240328_allthingsconsumed-me-19.jpg?w=750&amp;q=75 750w, https://content.sfstandard.com/wp-content/uploads/2024/06/20240328_allthingsconsumed-me-19.jpg?w=768&amp;q=75 768w, https://content.sfstandard.com/wp-content/uploads/2024/06/20240328_allthingsconsumed-me-19.jpg?w=828&amp;q=75 828w, https://content.sfstandard.com/wp-content/uploads/2024/06/20240328_allthingsconsumed-me-19.jpg?w=1024&amp;q=75 1024w, https://content.sfstandard.com/wp-content/uploads/2024/06/20240328_allthingsconsumed-me-19.jpg?w=1080&amp;q=75 1080w, https://content.sfstandard.com/wp-content/uploads/2024/06/20240328_allthingsconsumed-me-19.jpg?w=1200&amp;q=75 1200w, https://content.sfstandard.com/wp-content/uploads/2024/06/20240328_allthingsconsumed-me-19.jpg?w=1920&amp;q=75 1920w, https://content.sfstandard.com/wp-content/uploads/2024/06/20240328_allthingsconsumed-me-19.jpg?w=2048&amp;q=75 2048w, https://content.sfstandard.com/wp-content/uploads/2024/06/20240328_allthingsconsumed-me-19.jpg?w=3840&amp;q=75 3840w" src="https://content.sfstandard.com/wp-content/uploads/2024/06/20240328_allthingsconsumed-me-19.jpg?w=3840&amp;q=75" decoding="async" data-nimg="responsive" style="position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%" class="block  lazyloaded" loading="lazy"/></noscript>
                              </span>
                           </div>
                           <button class="hidden md:block absolute bottom-sm right-sm drop-shadow-[0_0_5px_rgba(0,0,0,1)]" aria-label="Expand image">
                              <svg class="text-white can-hover:hover:text-highlight motion-safe:transition-colors" width="12" height="12" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                 <path d="M16.5 10.6667V12.5C16.5 13.9001 16.5 14.6002 16.2275 15.135C15.9878 15.6054 15.6054 15.9878 15.135 16.2275C14.6002 16.5 13.9001 16.5 12.5 16.5H10.6667M7.33333 1.5H5.5C4.09987 1.5 3.3998 1.5 2.86502 1.77248C2.39462 2.01217 2.01217 2.39462 1.77248 2.86502C1.5 3.3998 1.5 4.09987 1.5 5.5V7.33333M11.5 6.5L16.5 1.5M16.5 1.5H11.5M16.5 1.5V6.5M6.5 11.5L1.5 16.5M1.5 16.5H6.5M1.5 16.5L1.5 11.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                              </svg>
                           </button>
                        </div>
                        <figcaption class="basis-full mt-[10px] text-xs text-gray-550 font-inter leading-[18px]">Patrons dine at the Anchovy Bar in the Fillmore in San Francisco on Monday, March 25, 2024. | <span class="sr-only">Source: </span>Morgan Ellis/The Standard</figcaption>
                     </figure>
                     <div class="floats">
                        <div class="article-body undefined text-left" id="">
                           <p>If that comes to pass, workers like Madayag fear that higher prices will deter diners and cause restaurants to reduce their hours. If that happens, Madayag says he’d have to pick up a second job to support his family. That’s if he can find one.&nbsp;</p>
                        </div>
                        <div class="article-body undefined text-left" id="">
                           <p>“It’s not easy finding a second job, especially in this industry, given <a href="https://www.sfchronicle.com/food/restaurants/article/sales-revenue-san-francisco-18659409.php">it’s slowing down</a>,” he said.</p>
                        </div>
                        <div class="article-body undefined text-left" id="">
                           <p>SB 478 doesn’t just apply to restaurants; it also requires airlines, hotels and rental car companies to provide all-in pricing. But the change will be especially disruptive for San Francisco restaurants because of a 16-year-old city ordinance requiring most employers to <a href="https://www.sf.gov/information/section-overview-hcso-administrative-guidance">set aside funds for workers’ health insurance</a>.&nbsp;</p>
                        </div>
                        <div class="article-body undefined text-left" id="">
                           <p>The majority of San Francisco restaurants accomplish this by implementing a <a href="https://www.sf.gov/information/section-k-health-surcharges-hcso-administrative-guidance">healthcare surcharge</a>, typically 5-8%. Diners, however, have consistently expressed <a href="https://www.sfchronicle.com/restaurants/article/SF-restaurant-bill-surcharges-still-give-some-13254864.php">frustration</a> over their use. It’s not clear that the money really helps workers, critics say, pointing out that dozens of restaurants have been accused of <a href="https://www.sfchronicle.com/restaurants/article/Restaurateurs-settle-over-health-surcharge-4499854.php">misusing funds</a> that were supposed to go to worker healthcare.&nbsp;</p>
                        </div>
                        <div class="article-body undefined text-left" id="">
                           <p>Kelsey Bigelow, general manager at <a href="https://www.mestizasf.com/">Mestiza</a> in SoMa, said she benefits from the 5% surcharge the restaurant applies to diners’ bills. The money generated from the surcharge goes into one of Mesitza’s general ledger accounts and then gets dispersed monthly to workers, including herself.&nbsp;</p>
                        </div>
                        <div class="article-body undefined text-left" id="">
                           <p>Right now, Mestiza covers about 75% of the $700 she pays monthly for health insurance. But Bigelow said the revenue generated by the restaurant’s health care surcharge doesn’t fully cover what Mestiza pays out every month to workers. If the restaurant drops the fee and raises menu prices instead, she worries that the gap could get even larger. Combined with the <a href="https://restaurant.org/research-and-media/research/economists-notebook/economic-indicators/food-costs/">rising cost of ingredients</a> and <a href="https://www.sf.gov/information/minimum-wage-ordinance">minimum wage increase</a> also set to go into effect on July 1, Bigelow worries about her employer’s continued ability to provide livable wages and benefits for workers.&nbsp;</p>
                        </div>
                        <div class="article-body undefined text-left" id="">
                           <p>“[The surcharge] is definitely a helping hand for the employer, but also for the employee,” Bigelow said. “I know some people think that it just lines the owner’s pockets. But I feel like, if it’s clearly stated, I have no problem helping any of the places that I patronize knowing that it actually does transfer over into the employees benefit.”&nbsp;</p>
                        </div>
                        <div class="article-body undefined text-left" id="">
                           <p>SB 478 has also reignited the heated restaurant industry debate over tips, or rather, efforts to eradicate them. For the past decade or so, restaurants big and small—most famously, Danny Meyer’s New York-based <a href="https://www.ushg.com/">Union Square Hospitality Group</a>—have tried to <a href="https://www.nytimes.com/2015/10/15/dining/danny-meyer-restaurants-no-tips.html">eliminate tipping</a>, which has been shown to exacerbate social inequities in the industry. But switching to a no-tipping model has proven <a href="https://www.nytimes.com/2020/07/20/dining/danny-meyer-no-tips.html">difficult</a> for even the most successful restaurateurs, leaving most restaurant workers’ wages at the discretion of diners.&nbsp;</p>
                        </div>
                        <div class="article-body undefined text-left" id="">
                           <p></p>
                        </div>
                     </div>
                     <div class="article-body ">
                        <h2 class="wp-block-heading" id="h-i-m-not-stupid-i-can-work-in-tech">‘I’m not stupid, I can work in tech’</h2>
                     </div>
                     <div class="floats">
                        <div class="article-body undefined text-left" id="">
                           <p>One major sticking point for workers: SB 478 would make it illegal for restaurants to put an automatic tip on bills for large parties, a practice common at restaurants of all sizes. Kelsey Atchinson, who’s been in the service industry for 15 years and is currently a server at <a href="http://www.bluewhalerestaurant-lounge.com/">Blue Whale</a> restaurant in the Marina, wasn’t aware the law would impact so-called “automatic gratuities” for large groups. The news gave her pause, but she said she believes customers understand that a 20% tip is standard practice. “It could definitely affect things, but probably just a small percentage,” she said.&nbsp;</p>
                        </div>
                     </div>
                     <figure id="" class="ImageBlock_ImageFigure__YkqC2  inset-image mobile-grid-full mobile-grid-full">
                        <div class="relative">
                           <div>
                              <span style="box-sizing:border-box;display:block;overflow:hidden;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;position:relative">
                                 <span style="box-sizing:border-box;display:block;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;padding-top:66.66666666666666%"></span><img alt="People dine at white-clothed tables in a cozy restaurant with large windows. Bread and dishes fill a counter by a brick oven, where staff serve and prepare food." src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" decoding="async" data-nimg="responsive" class="block  lazyloaded" style="position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%;background-size:cover;background-position:0% 0%;filter:blur(20px);background-image:url(&quot;data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==&quot;)">
                                 <noscript><img alt="People dine at white-clothed tables in a cozy restaurant with large windows. Bread and dishes fill a counter by a brick oven, where staff serve and prepare food." sizes="(min-width: 1001px) 600px, (min-width: 768px) 700px, 100vw" srcSet="https://content.sfstandard.com/wp-content/uploads/2024/06/pfg_zunicafe_100523-0022.jpg?w=640&amp;q=75 640w, https://content.sfstandard.com/wp-content/uploads/2024/06/pfg_zunicafe_100523-0022.jpg?w=750&amp;q=75 750w, https://content.sfstandard.com/wp-content/uploads/2024/06/pfg_zunicafe_100523-0022.jpg?w=768&amp;q=75 768w, https://content.sfstandard.com/wp-content/uploads/2024/06/pfg_zunicafe_100523-0022.jpg?w=828&amp;q=75 828w, https://content.sfstandard.com/wp-content/uploads/2024/06/pfg_zunicafe_100523-0022.jpg?w=1024&amp;q=75 1024w, https://content.sfstandard.com/wp-content/uploads/2024/06/pfg_zunicafe_100523-0022.jpg?w=1080&amp;q=75 1080w, https://content.sfstandard.com/wp-content/uploads/2024/06/pfg_zunicafe_100523-0022.jpg?w=1200&amp;q=75 1200w, https://content.sfstandard.com/wp-content/uploads/2024/06/pfg_zunicafe_100523-0022.jpg?w=1920&amp;q=75 1920w, https://content.sfstandard.com/wp-content/uploads/2024/06/pfg_zunicafe_100523-0022.jpg?w=2048&amp;q=75 2048w, https://content.sfstandard.com/wp-content/uploads/2024/06/pfg_zunicafe_100523-0022.jpg?w=3840&amp;q=75 3840w" src="https://content.sfstandard.com/wp-content/uploads/2024/06/pfg_zunicafe_100523-0022.jpg?w=3840&amp;q=75" decoding="async" data-nimg="responsive" style="position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%" class="block  lazyloaded" loading="lazy"/></noscript>
                              </span>
                           </div>
                           <button class="hidden md:block absolute bottom-sm right-sm drop-shadow-[0_0_5px_rgba(0,0,0,1)]" aria-label="Expand image">
                              <svg class="text-white can-hover:hover:text-highlight motion-safe:transition-colors" width="12" height="12" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                 <path d="M16.5 10.6667V12.5C16.5 13.9001 16.5 14.6002 16.2275 15.135C15.9878 15.6054 15.6054 15.9878 15.135 16.2275C14.6002 16.5 13.9001 16.5 12.5 16.5H10.6667M7.33333 1.5H5.5C4.09987 1.5 3.3998 1.5 2.86502 1.77248C2.39462 2.01217 2.01217 2.39462 1.77248 2.86502C1.5 3.3998 1.5 4.09987 1.5 5.5V7.33333M11.5 6.5L16.5 1.5M16.5 1.5H11.5M16.5 1.5V6.5M6.5 11.5L1.5 16.5M1.5 16.5H6.5M1.5 16.5L1.5 11.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                              </svg>
                           </button>
                        </div>
                        <figcaption class="basis-full mt-[10px] text-xs text-gray-550 font-inter leading-[18px]">The wood-fired oven and bread station at Zuni Cafe in San Francisco, Wednesday, October 5, 2023. | <span class="sr-only">Source: </span>Jason Henry for The Standard</figcaption>
                     </figure>
                     <div class="floats">
                        <div class="article-body undefined text-left" id="">
                           <p>In fact, she says getting rid of surcharges in favor of a traditional tipping model could result in better service. “I think the upside is that you’re going to have to provide better service if you want to get tipped for the work,” she said. “You’re not going to automatically get that tip. You’re going to get what you worked for.”&nbsp;&nbsp;</p>
                        </div>
                        <div class="article-body undefined text-left" id="">
                           <p>Within the industry—and even within the same restaurant—there’s no broad agreement on how SB 478 will impact tips. Melissa Lang, who also works at Blue Whale restaurant in the Marina, came back to the hospitality industry several years ago after working in tech prior to the pandemic. Lang, who handles private event bookings for the restaurant, said eliminating automatic gratuities for large groups could lead to worse service if workers aren’t guaranteed fair compensation. “I can’t even imagine how bad service could be,” she said. “That’s a daunting thought.”</p>
                        </div>
                        <div class="article-body undefined text-left" id="">
                           <p>She said she’s watched many of her colleagues either leave San Francisco for more affordable markets or abandon the industry in favor of work with more consistent pay. With the additional uncertainty brought on by SB 478, she predicts even more people will choose to find other work.&nbsp;</p>
                        </div>
                        <div class="article-body undefined text-left" id="">
                           <p>“It’s pretty tough to look at that and not say, I’m not stupid, I can work in tech—or do almost anything else other than work in the service industry.”</p>
                        </div>
                        <div class="article-body undefined text-left" id="">
                           <p>Fausto Galicia has worked in San Francisco restaurants for 30 years, starting as a dishwasher at the legendary Postrio and working his way up the ranks to lead sommelier at Pabu. The restaurant charges a 4% surcharge to meet city healthcare requirements but leaves tips entirely up to guests.&nbsp;</p>
                        </div>
                        <div class="article-body undefined text-left" id="">
                           <p>Galicia worries confusion around restaurant pricing after July 1 could result in less money in his pocket. “I tried to read the articles, and it’s not really clear for me. So you can imagine for customers, it’s the same thing,” Galicia said. That confusion, he said, could only lead to one result: less money in his pocket. “Basically, I’d need to change to a different job.”&nbsp;</p>
                        </div>
                     </div>
                     <div class="StickySharer_container__T9VAP">
                        <div class="content-grid StickySharer_content__J2QbY">
                           <ul class="StickySharer_list__zDmJq">
                              <li class="StickySharer_copyLink__dUNN9">
                                 <button>
                                    <span class="sr-only">Copy link to this article</span>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="19" viewBox="0 0 18 19" fill="none">
                                       <path d="M7.50003 10.25C7.82212 10.6806 8.23305 11.0369 8.70494 11.2947C9.17684 11.5525 9.69866 11.7058 10.235 11.7443C10.7714 11.7827 11.3097 11.7053 11.8135 11.5173C12.3174 11.3294 12.7749 11.0353 13.155 10.655L15.405 8.405C16.0881 7.69775 16.4661 6.75049 16.4576 5.76725C16.449 4.78401 16.0546 3.84347 15.3593 3.14818C14.6641 2.4529 13.7235 2.05852 12.7403 2.04998C11.757 2.04143 10.8098 2.41941 10.1025 3.1025L8.81253 4.385M10.5 8.75C10.1779 8.31941 9.76701 7.96312 9.29512 7.7053C8.82322 7.44748 8.3014 7.29417 7.76504 7.25575C7.22868 7.21734 6.69034 7.29473 6.18652 7.48267C5.6827 7.67061 5.22519 7.9647 4.84503 8.345L2.59503 10.595C1.91194 11.3023 1.53396 12.2495 1.5425 13.2328C1.55104 14.216 1.94543 15.1565 2.64071 15.8518C3.33599 16.5471 4.27654 16.9415 5.25977 16.95C6.24301 16.9586 7.19027 16.5806 7.89753 15.8975L9.18003 14.615" stroke="currentColor" stroke-width="1.3" stroke-linecap="round" stroke-linejoin="round"></path>
                                    </svg>
                                 </button>
                                 <div class="StickySharer_copyLinkStatus__hO5_V null" role="status"></div>
                              </li>
                              <li>
                                 <a target="_blank" aria-label="Send as email" rel="noreferrer" href="mailto:?subject=Restaurant workers fear they could pay highest cost in junk-fee battle&amp;body=From The San Francisco Standard%0A%0ARestaurant workers fear they could pay highest cost in junk-fee battle%0A%0Ahttps://sfstandard.com/2024/06/24/service-fee-restaurants-san-francisco/?utm_source=email_sitebutton&amp;utm_medium=site_buttons&amp;utm_campaign=site_buttons">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="15" height="12.5" viewBox="0 0 18 15" fill="none">
                                       <path d="M1.5 3.75L7.62369 8.03658C8.11957 8.3837 8.36751 8.55726 8.6372 8.62448C8.87542 8.68386 9.12458 8.68386 9.3628 8.62448C9.63249 8.55726 9.88043 8.3837 10.3763 8.03658L16.5 3.75M5.1 13.5H12.9C14.1601 13.5 14.7902 13.5 15.2715 13.2548C15.6948 13.039 16.039 12.6948 16.2548 12.2715C16.5 11.7902 16.5 11.1601 16.5 9.9V5.1C16.5 3.83988 16.5 3.20982 16.2548 2.72852C16.039 2.30516 15.6948 1.96095 15.2715 1.74524C14.7902 1.5 14.1601 1.5 12.9 1.5H5.1C3.83988 1.5 3.20982 1.5 2.72852 1.74524C2.30516 1.96095 1.96095 2.30516 1.74524 2.72852C1.5 3.20982 1.5 3.83988 1.5 5.1V9.9C1.5 11.1601 1.5 11.7902 1.74524 12.2715C1.96095 12.6948 2.30516 13.039 2.72852 13.2548C3.20982 13.5 3.83988 13.5 5.1 13.5Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                    </svg>
                                 </a>
                              </li>
                              <li>
                                 <a target="_blank" aria-label="Share on Twitter" rel="noreferrer" href="https://twitter.com/intent/tweet?text=Restaurant%20workers%20fear%20they%20could%20pay%20highest%20cost%20in%20junk-fee%20battle&amp;via=sfstandard&amp;url=https://sfstandard.com/2024/06/24/service-fee-restaurants-san-francisco/?utm_source=twitter_sitebutton&amp;utm_medium=site_buttons&amp;utm_campaign=site_buttons">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="17" viewBox="0 0 18 17" fill="none">
                                       <path d="M10.5909 7.19834L16.8157 0H15.3406L9.93558 6.25022L5.61857 0H0.639404L7.16757 9.45144L0.639404 17H2.11459L7.82248 10.3996L12.3816 17H17.3607L10.5909 7.19834ZM8.57039 9.53471L7.90895 8.59356L2.64612 1.10473H4.91191L9.15908 7.14845L9.82052 8.0896L15.3413 15.9455H13.0755L8.57039 9.53471Z" fill="currentColor"></path>
                                    </svg>
                                 </a>
                              </li>
                              <li>
                                 <a target="_blank" aria-label="Share on Facebook" rel="noreferrer" href="https://www.facebook.com/sharer.php?u=https://sfstandard.com/2024/06/24/service-fee-restaurants-san-francisco/?utm_source=facebook_sitebutton&amp;utm_medium=site_buttons&amp;utm_campaign=site_buttons">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="8" height="15" viewBox="0 0 8 15" fill="none">
                                       <path d="M6.39293 2.49002H7.75V0.105022C7.09294 0.0340327 6.43274 -0.00101571 5.77214 2.2398e-05C3.80871 2.2398e-05 2.46607 1.24502 2.46607 3.52502V5.49001H0.25V8.16001H2.46607V15H5.12247V8.16001H7.33133L7.66338 5.49001H5.12247V3.78752C5.12247 3.00002 5.32459 2.49002 6.39293 2.49002Z" fill="currentColor"></path>
                                    </svg>
                                 </a>
                              </li>
                              <li>
                                 <a target="_blank" aria-label="Share on LinkedIn" rel="noreferrer" href="https://www.linkedin.com/cws/share?url=https://sfstandard.com/2024/06/24/service-fee-restaurants-san-francisco/?utm_source=linkedin_sitebutton&amp;utm_medium=site_buttons&amp;utm_campaign=site_buttons&amp;title=Restaurant workers fear they could pay highest cost in junk-fee battle">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="15" height="14.0625" viewBox="0 0 16 15" fill="none">
                                       <path d="M15.5 1.10294V13.8971C15.5 14.1896 15.3838 14.4701 15.177 14.677C14.9701 14.8838 14.6896 15 14.3971 15H1.60294C1.31042 15 1.02989 14.8838 0.823044 14.677C0.616202 14.4701 0.5 14.1896 0.5 13.8971V1.10294C0.5 0.810423 0.616202 0.529886 0.823044 0.323044C1.02989 0.116202 1.31042 0 1.60294 0L14.3971 0C14.6896 0 14.9701 0.116202 15.177 0.323044C15.3838 0.529886 15.5 0.810423 15.5 1.10294ZM4.91176 5.73529H2.70588V12.7941H4.91176V5.73529ZM5.11029 3.30882C5.11146 3.14197 5.07974 2.97651 5.01696 2.82191C4.95418 2.66731 4.86156 2.52659 4.7444 2.40779C4.62723 2.28898 4.48781 2.19442 4.3341 2.1295C4.18038 2.06458 4.01539 2.03057 3.84853 2.02941H3.80882C3.4695 2.02941 3.14408 2.16421 2.90414 2.40414C2.66421 2.64408 2.52941 2.9695 2.52941 3.30882C2.52941 3.64814 2.66421 3.97357 2.90414 4.2135C3.14408 4.45344 3.4695 4.58824 3.80882 4.58824C3.97569 4.59234 4.14173 4.56353 4.29746 4.50344C4.45319 4.44335 4.59555 4.35317 4.71642 4.23804C4.83728 4.12291 4.93427 3.9851 5.00186 3.83247C5.06944 3.67985 5.10629 3.5154 5.11029 3.34853V3.30882ZM13.2941 8.50588C13.2941 6.38382 11.9441 5.55882 10.6029 5.55882C10.1638 5.53684 9.72659 5.63037 9.33489 5.83009C8.94319 6.02981 8.6107 6.32874 8.37059 6.69706H8.30882V5.73529H6.23529V12.7941H8.44118V9.03971C8.40929 8.65519 8.53041 8.27362 8.77823 7.97789C9.02605 7.68217 9.38054 7.49616 9.76471 7.46029H9.84853C10.55 7.46029 11.0706 7.90147 11.0706 9.01324V12.7941H13.2765L13.2941 8.50588Z" fill="currentColor"></path>
                                    </svg>
                                 </a>
                              </li>
                           </ul>
                        </div>
                     </div>
                  </div>
                  <div class="content-grid mb-lg">
                     <p class="AuthorReach_container__2RgQp">
                        Lauren Saria<!-- --> can be reached at<!-- --> <a href="mailto:<EMAIL>"><EMAIL></a><br>
                     </p>
                     <div></div>
                     <div class="Tags_container__7AePs">
                        <h2 class="Tags_heading__Sd3vC">Filed Under</h2>
                        <div class="Tags_list__LELKL"><a href="/tag/food/">Food</a><a href="/food-drink/">Food &amp; Drink</a><a href="/tag/restaurants/">Restaurants</a></div>
                     </div>
                  </div>
                  <aside class="StoryDiscovery_StoryDiscovery__FT1Sv">
                     <div class="StoryDiscovery_StoryDiscoveryContainer__EiY6W">
                        <div class="StoryDiscovery_StoryDiscoveryReadMore__xBvu_">
                           <hr class="bg-warm-gray-700 mb-xs md:mb-sm">
                           <h2 class="StoryDiscovery_StoryDiscoveryReadMoreTitle__m0dv1">Read More</h2>
                           <ul class="flex flex-col">
                              <li class="ReadMore_ReadMoreArticleContainer__PGlJS">
                                 <a href="https://sfstandard.com/2024/06/18/wine-bars-san-francisco-trend-reservations-food/?itm_source=parsely-api">
                                    <h3 class="article-title">A new dining trend in San Francisco? Casual wine bars with serious food</h3>
                                    <span class="hidden xl:block text-heading-m text-text-secondary mt-1 font-inter">A growing number of San Francisco wine bars cater to diners who don’t want to commit to a restaurant meal. It’s the best kind of situationship. </span>
                                 </a>
                                 <div class="aspect-normal">
                                    <span style="box-sizing:border-box;display:block;overflow:hidden;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;position:relative">
                                       <span style="box-sizing:border-box;display:block;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;padding-top:65.625%"></span><img alt="An overhead view of a bar loaded with small plates of food and glasses of wine." src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" decoding="async" data-nimg="responsive" class="object-cover lazyloaded" style="position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%">
                                       <noscript><img alt="An overhead view of a bar loaded with small plates of food and glasses of wine." sizes="100vw" srcSet="https://content.sfstandard.com/wp-content/uploads/2024/06/20240612_noncommittalwinebars_-0539.jpg?w=640&amp;q=75 640w, https://content.sfstandard.com/wp-content/uploads/2024/06/20240612_noncommittalwinebars_-0539.jpg?w=750&amp;q=75 750w, https://content.sfstandard.com/wp-content/uploads/2024/06/20240612_noncommittalwinebars_-0539.jpg?w=828&amp;q=75 828w, https://content.sfstandard.com/wp-content/uploads/2024/06/20240612_noncommittalwinebars_-0539.jpg?w=1080&amp;q=75 1080w, https://content.sfstandard.com/wp-content/uploads/2024/06/20240612_noncommittalwinebars_-0539.jpg?w=1200&amp;q=75 1200w, https://content.sfstandard.com/wp-content/uploads/2024/06/20240612_noncommittalwinebars_-0539.jpg?w=1920&amp;q=75 1920w, https://content.sfstandard.com/wp-content/uploads/2024/06/20240612_noncommittalwinebars_-0539.jpg?w=2048&amp;q=75 2048w, https://content.sfstandard.com/wp-content/uploads/2024/06/20240612_noncommittalwinebars_-0539.jpg?w=3840&amp;q=75 3840w" src="https://content.sfstandard.com/wp-content/uploads/2024/06/20240612_noncommittalwinebars_-0539.jpg?w=3840&amp;q=75" decoding="async" data-nimg="responsive" style="position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%" class="object-cover lazyloaded" loading="lazy"/></noscript>
                                    </span>
                                 </div>
                              </li>
                              <li class="ReadMore_ReadMoreArticleContainer__PGlJS">
                                 <hr class="bg-warm-gray-300 my-md" aria-hidden="true">
                                 <a href="https://sfstandard.com/2024/06/20/rintaros-bento-boxes-are-back-and-still-sfs-most-exquisite-takeout/?itm_source=parsely-api">
                                    <h3 class="article-title">SF’s $100 bento box is the best takeout in the city</h3>
                                    <span class="hidden xl:block text-heading-m text-text-secondary mt-1 font-inter">Rintaro’s takeout bento boxes are back—and still exquisite. Plus, LOJ is already creating a buzz in the Marina and a perfect cocktail for Pride.</span>
                                 </a>
                                 <div class="aspect-normal">
                                    <span style="box-sizing:border-box;display:block;overflow:hidden;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;position:relative">
                                       <span style="box-sizing:border-box;display:block;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;padding-top:65.625%"></span><img alt="Rinataro's bento box for two people" src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" decoding="async" data-nimg="responsive" class="object-cover lazyloaded" style="position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%">
                                       <noscript><img alt="Rinataro&#x27;s bento box for two people" sizes="100vw" srcSet="https://content.sfstandard.com/wp-content/uploads/2024/06/inlined_20240615_allthingsconsumedjune20.jpg?w=640&amp;q=75 640w, https://content.sfstandard.com/wp-content/uploads/2024/06/inlined_20240615_allthingsconsumedjune20.jpg?w=750&amp;q=75 750w, https://content.sfstandard.com/wp-content/uploads/2024/06/inlined_20240615_allthingsconsumedjune20.jpg?w=828&amp;q=75 828w, https://content.sfstandard.com/wp-content/uploads/2024/06/inlined_20240615_allthingsconsumedjune20.jpg?w=1080&amp;q=75 1080w, https://content.sfstandard.com/wp-content/uploads/2024/06/inlined_20240615_allthingsconsumedjune20.jpg?w=1200&amp;q=75 1200w, https://content.sfstandard.com/wp-content/uploads/2024/06/inlined_20240615_allthingsconsumedjune20.jpg?w=1920&amp;q=75 1920w, https://content.sfstandard.com/wp-content/uploads/2024/06/inlined_20240615_allthingsconsumedjune20.jpg?w=2048&amp;q=75 2048w, https://content.sfstandard.com/wp-content/uploads/2024/06/inlined_20240615_allthingsconsumedjune20.jpg?w=3840&amp;q=75 3840w" src="https://content.sfstandard.com/wp-content/uploads/2024/06/inlined_20240615_allthingsconsumedjune20.jpg?w=3840&amp;q=75" decoding="async" data-nimg="responsive" style="position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%" class="object-cover lazyloaded" loading="lazy"/></noscript>
                                    </span>
                                 </div>
                              </li>
                              <li class="ReadMore_ReadMoreArticleContainer__PGlJS">
                                 <hr class="bg-warm-gray-300 my-md" aria-hidden="true">
                                 <a href="https://sfstandard.com/2024/06/19/best-classic-martini-san-francisco-restaurant-bar/?itm_source=parsely-api">
                                    <h3 class="article-title">In defense of a real martini—in a real martini glass</h3>
                                    <span class="hidden xl:block text-heading-m text-text-secondary mt-1 font-inter">Most martinis are served in coupes or Nick and Nora glasses. But there’s still a handful of spotsserving a classic martini in a classic martini glass.</span>
                                 </a>
                                 <div class="aspect-normal">
                                    <span style="box-sizing:border-box;display:block;overflow:hidden;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;position:relative">
                                       <span style="box-sizing:border-box;display:block;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;padding-top:65.625%"></span><img alt="A martini glass with two olives skewered on a stick is placed on a dark square napkin on a polished wooden bar top, with a blurry background of bar lights." src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" decoding="async" data-nimg="responsive" class="object-cover lazyloaded" style="position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%">
                                       <noscript><img alt="A martini glass with two olives skewered on a stick is placed on a dark square napkin on a polished wooden bar top, with a blurry background of bar lights." sizes="100vw" srcSet="https://content.sfstandard.com/wp-content/uploads/2024/06/featured-20240617-classicmartinis-me-15_b8fbc3.jpg?w=640&amp;q=75 640w, https://content.sfstandard.com/wp-content/uploads/2024/06/featured-20240617-classicmartinis-me-15_b8fbc3.jpg?w=750&amp;q=75 750w, https://content.sfstandard.com/wp-content/uploads/2024/06/featured-20240617-classicmartinis-me-15_b8fbc3.jpg?w=828&amp;q=75 828w, https://content.sfstandard.com/wp-content/uploads/2024/06/featured-20240617-classicmartinis-me-15_b8fbc3.jpg?w=1080&amp;q=75 1080w, https://content.sfstandard.com/wp-content/uploads/2024/06/featured-20240617-classicmartinis-me-15_b8fbc3.jpg?w=1200&amp;q=75 1200w, https://content.sfstandard.com/wp-content/uploads/2024/06/featured-20240617-classicmartinis-me-15_b8fbc3.jpg?w=1920&amp;q=75 1920w, https://content.sfstandard.com/wp-content/uploads/2024/06/featured-20240617-classicmartinis-me-15_b8fbc3.jpg?w=2048&amp;q=75 2048w, https://content.sfstandard.com/wp-content/uploads/2024/06/featured-20240617-classicmartinis-me-15_b8fbc3.jpg?w=3840&amp;q=75 3840w" src="https://content.sfstandard.com/wp-content/uploads/2024/06/featured-20240617-classicmartinis-me-15_b8fbc3.jpg?w=3840&amp;q=75" decoding="async" data-nimg="responsive" style="position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%" class="object-cover lazyloaded" loading="lazy"/></noscript>
                                    </span>
                                 </div>
                              </li>
                              <li class="ReadMore_ReadMoreArticleContainer__PGlJS">
                                 <hr class="bg-warm-gray-300 my-md" aria-hidden="true">
                                 <a href="https://sfstandard.com/2024/06/09/san-francisco-has-reached-peak-bagel-its-a-blessing-and-a-curse/?itm_source=parsely-api">
                                    <h3 class="article-title">San Francisco has reached peak bagel. It’s a blessing and a curse</h3>
                                    <span class="hidden xl:block text-heading-m text-text-secondary mt-1 font-inter">The good news is it’s easier than ever to get a good bagel. The bad news? They might lose some of their allure.</span>
                                 </a>
                                 <div class="aspect-normal">
                                    <span style="box-sizing: border-box; display: block; overflow: hidden; width: initial; height: initial; background: none; opacity: 1; border: 0px; margin: 0px; padding: 0px; position: relative;">
                                       <span style="box-sizing: border-box; display: block; width: initial; height: initial; background: none; opacity: 1; border: 0px; margin: 0px; padding: 65.625% 0px 0px;"></span><img alt="A close up of a pile of bagels of various flavor." src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" decoding="async" data-nimg="responsive" class="object-cover lazyloaded" style="position: absolute; inset: 0px; box-sizing: border-box; padding: 0px; border: none; margin: auto; display: block; width: 0px; height: 0px; min-width: 100%; max-width: 100%; min-height: 100%; max-height: 100%;">
                                       <noscript></noscript>
                                    </span>
                                 </div>
                              </li>
                              <li class="ReadMore_ReadMoreArticleContainer__PGlJS">
                                 <hr class="bg-warm-gray-300 my-md" aria-hidden="true">
                                 <a href="https://sfstandard.com/2024/06/11/chef-anthony-strong-restaurant-success/?itm_source=parsely-api">
                                    <h3 class="article-title">The pasta chef upending the restaurant biz—and making money while he’s at it</h3>
                                    <span class="hidden xl:block text-heading-m text-text-secondary mt-1 font-inter">With his second Pasta Supply Co now open, one chef has learned a thing or two about the business. And he has some advice.</span>
                                 </a>
                                 <div class="aspect-normal">
                                    <span style="box-sizing: border-box; display: block; overflow: hidden; width: initial; height: initial; background: none; opacity: 1; border: 0px; margin: 0px; padding: 0px; position: relative;">
                                       <span style="box-sizing: border-box; display: block; width: initial; height: initial; background: none; opacity: 1; border: 0px; margin: 0px; padding: 65.625% 0px 0px;"></span><img alt="A man with tattoos and glasses, wearing a cap and a white uniform, leans on a glass counter filled with pasta in a well-lit store with shelves of products behind him." src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" decoding="async" data-nimg="responsive" class="object-cover lazyloaded" style="position: absolute; inset: 0px; box-sizing: border-box; padding: 0px; border: none; margin: auto; display: block; width: 0px; height: 0px; min-width: 100%; max-width: 100%; min-height: 100%; max-height: 100%;">
                                       <noscript></noscript>
                                    </span>
                                 </div>
                              </li>
                           </ul>
                        </div>
                        <div class="StoryDiscovery_StoryDiscoveryPopular__KG5Wr">
                           <h2 class="StoryDiscovery_StoryDiscoveryPopularTitle__WDR56">Popular Today</h2>
                           <ol class="MostPopular_MostPopularContainer__gPcmJ">
                              <li class="MostPopular_MostPopularArticleContainer__n0o_W">
                                 <div class="aspect-normal md:hidden">
                                    <span style="box-sizing:border-box;display:block;overflow:hidden;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;position:relative">
                                       <span style="box-sizing:border-box;display:block;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;padding-top:65.625%"></span><img alt="The image shows a small two-story white house with a terracotta tiled roof, large front window, blue garage door, and steps leading to a blue front door. Surrounding greenery includes a small tree and bushes." src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" decoding="async" data-nimg="responsive" class="object-cover lazyloaded" style="position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%">
                                       <noscript><img alt="The image shows a small two-story white house with a terracotta tiled roof, large front window, blue garage door, and steps leading to a blue front door. Surrounding greenery includes a small tree and bushes." sizes="100vw" srcSet="https://content.sfstandard.com/wp-content/uploads/2024/06/mg_7017.jpg?w=640&amp;q=75 640w, https://content.sfstandard.com/wp-content/uploads/2024/06/mg_7017.jpg?w=750&amp;q=75 750w, https://content.sfstandard.com/wp-content/uploads/2024/06/mg_7017.jpg?w=828&amp;q=75 828w, https://content.sfstandard.com/wp-content/uploads/2024/06/mg_7017.jpg?w=1080&amp;q=75 1080w, https://content.sfstandard.com/wp-content/uploads/2024/06/mg_7017.jpg?w=1200&amp;q=75 1200w, https://content.sfstandard.com/wp-content/uploads/2024/06/mg_7017.jpg?w=1920&amp;q=75 1920w, https://content.sfstandard.com/wp-content/uploads/2024/06/mg_7017.jpg?w=2048&amp;q=75 2048w, https://content.sfstandard.com/wp-content/uploads/2024/06/mg_7017.jpg?w=3840&amp;q=75 3840w" src="https://content.sfstandard.com/wp-content/uploads/2024/06/mg_7017.jpg?w=3840&amp;q=75" decoding="async" data-nimg="responsive" style="position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%" class="object-cover lazyloaded" loading="lazy"/></noscript>
                                    </span>
                                 </div>
                                 <div class="MostPopular_MostPopularArticleTitleContainer__zCCkY">
                                    <span class="font-ivar-headline text-headline-ivar-3 h-min border-b-warm-gray-300 border-b-[1px]">1</span>
                                    <a href="/2024/06/21/betrayal-the-family-feud-behind-russian-hills-488k-home-sale/">
                                       <h3 class="text-heading-m">The family feud behind bizarre $488K Russian Hill home sale</h3>
                                    </a>
                                 </div>
                              </li>
                              <hr class="hidden md:block bg-warm-gray-500">
                              <li class="MostPopular_MostPopularArticleContainer__n0o_W">
                                 <div class="aspect-normal md:hidden">
                                    <span style="box-sizing:border-box;display:block;overflow:hidden;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;position:relative">
                                       <span style="box-sizing:border-box;display:block;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;padding-top:65.625%"></span><img alt="A person wearing a helmet with a mounted camera and headset sits in a black glider cockpit marked with &quot;TL39.&quot; The background shows a hilly, scenic sunset." src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" decoding="async" data-nimg="responsive" class="object-cover lazyloaded" style="position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%">
                                       <noscript><img alt="A person wearing a helmet with a mounted camera and headset sits in a black glider cockpit marked with &quot;TL39.&quot; The background shows a hilly, scenic sunset." sizes="100vw" srcSet="https://content.sfstandard.com/wp-content/uploads/2024/06/9b424573-cf65-4069-b0a7-28f9a964ac88.jpg?w=640&amp;q=75 640w, https://content.sfstandard.com/wp-content/uploads/2024/06/9b424573-cf65-4069-b0a7-28f9a964ac88.jpg?w=750&amp;q=75 750w, https://content.sfstandard.com/wp-content/uploads/2024/06/9b424573-cf65-4069-b0a7-28f9a964ac88.jpg?w=828&amp;q=75 828w, https://content.sfstandard.com/wp-content/uploads/2024/06/9b424573-cf65-4069-b0a7-28f9a964ac88.jpg?w=1080&amp;q=75 1080w, https://content.sfstandard.com/wp-content/uploads/2024/06/9b424573-cf65-4069-b0a7-28f9a964ac88.jpg?w=1200&amp;q=75 1200w, https://content.sfstandard.com/wp-content/uploads/2024/06/9b424573-cf65-4069-b0a7-28f9a964ac88.jpg?w=1920&amp;q=75 1920w, https://content.sfstandard.com/wp-content/uploads/2024/06/9b424573-cf65-4069-b0a7-28f9a964ac88.jpg?w=2048&amp;q=75 2048w, https://content.sfstandard.com/wp-content/uploads/2024/06/9b424573-cf65-4069-b0a7-28f9a964ac88.jpg?w=3840&amp;q=75 3840w" src="https://content.sfstandard.com/wp-content/uploads/2024/06/9b424573-cf65-4069-b0a7-28f9a964ac88.jpg?w=3840&amp;q=75" decoding="async" data-nimg="responsive" style="position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%" class="object-cover lazyloaded" loading="lazy"/></noscript>
                                    </span>
                                 </div>
                                 <div class="MostPopular_MostPopularArticleTitleContainer__zCCkY">
                                    <span class="font-ivar-headline text-headline-ivar-3 h-min border-b-warm-gray-300 border-b-[1px]">2</span>
                                    <a href="/2024/06/15/pivotal-flying-cars-black-fly-tim-lum/">
                                       <h3 class="text-heading-m">Silicon Valley is finally making flying cars—and this guy bought one of the first</h3>
                                    </a>
                                 </div>
                              </li>
                              <hr class="hidden md:block bg-warm-gray-500">
                              <li class="MostPopular_MostPopularArticleContainer__n0o_W">
                                 <div class="aspect-normal md:hidden">
                                    <span style="box-sizing:border-box;display:block;overflow:hidden;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;position:relative">
                                       <span style="box-sizing:border-box;display:block;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;padding-top:65.625%"></span><img alt="A woman in a magenta blazer and light-colored top is standing at a podium with a microphone, smiling. There is text in the blurred background." src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" decoding="async" data-nimg="responsive" class="object-cover lazyloaded" style="position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%">
                                       <noscript><img alt="A woman in a magenta blazer and light-colored top is standing at a podium with a microphone, smiling. There is text in the blurred background." sizes="100vw" srcSet="https://content.sfstandard.com/wp-content/uploads/2023/01/OaklandInauguration01092023-06.jpg?w=640&amp;q=75 640w, https://content.sfstandard.com/wp-content/uploads/2023/01/OaklandInauguration01092023-06.jpg?w=750&amp;q=75 750w, https://content.sfstandard.com/wp-content/uploads/2023/01/OaklandInauguration01092023-06.jpg?w=828&amp;q=75 828w, https://content.sfstandard.com/wp-content/uploads/2023/01/OaklandInauguration01092023-06.jpg?w=1080&amp;q=75 1080w, https://content.sfstandard.com/wp-content/uploads/2023/01/OaklandInauguration01092023-06.jpg?w=1200&amp;q=75 1200w, https://content.sfstandard.com/wp-content/uploads/2023/01/OaklandInauguration01092023-06.jpg?w=1920&amp;q=75 1920w, https://content.sfstandard.com/wp-content/uploads/2023/01/OaklandInauguration01092023-06.jpg?w=2048&amp;q=75 2048w, https://content.sfstandard.com/wp-content/uploads/2023/01/OaklandInauguration01092023-06.jpg?w=3840&amp;q=75 3840w" src="https://content.sfstandard.com/wp-content/uploads/2023/01/OaklandInauguration01092023-06.jpg?w=3840&amp;q=75" decoding="async" data-nimg="responsive" style="position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%" class="object-cover lazyloaded" loading="lazy"/></noscript>
                                    </span>
                                 </div>
                                 <div class="MostPopular_MostPopularArticleTitleContainer__zCCkY">
                                    <span class="font-ivar-headline text-headline-ivar-3 h-min border-b-warm-gray-300 border-b-[1px]">3</span>
                                    <a href="/2024/06/24/oakland-mayor-sheng-thao-refuses-resign/">
                                       <h3 class="text-heading-m">Oakland Mayor Sheng Thao refuses to resign after FBI raids. Her attorney just quit</h3>
                                    </a>
                                 </div>
                              </li>
                              <hr class="hidden md:block bg-warm-gray-500">
                              <li class="MostPopular_MostPopularArticleContainer__n0o_W">
                                 <div class="aspect-normal md:hidden">
                                    <span style="box-sizing:border-box;display:block;overflow:hidden;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;position:relative">
                                       <span style="box-sizing:border-box;display:block;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;padding-top:65.625%"></span><img alt="The image features three people. On the left, a man in a dark suit is speaking. In the middle, a woman in a blue blazer holds a microphone. On the right, a man in glasses and a suit is talking." src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" decoding="async" data-nimg="responsive" class="object-cover lazyloaded" style="position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%">
                                       <noscript><img alt="The image features three people. On the left, a man in a dark suit is speaking. In the middle, a woman in a blue blazer holds a microphone. On the right, a man in glasses and a suit is talking." sizes="100vw" srcSet="https://content.sfstandard.com/wp-content/uploads/2024/06/featured_20240624neighborsmayor.jpeg?w=640&amp;q=75 640w, https://content.sfstandard.com/wp-content/uploads/2024/06/featured_20240624neighborsmayor.jpeg?w=750&amp;q=75 750w, https://content.sfstandard.com/wp-content/uploads/2024/06/featured_20240624neighborsmayor.jpeg?w=828&amp;q=75 828w, https://content.sfstandard.com/wp-content/uploads/2024/06/featured_20240624neighborsmayor.jpeg?w=1080&amp;q=75 1080w, https://content.sfstandard.com/wp-content/uploads/2024/06/featured_20240624neighborsmayor.jpeg?w=1200&amp;q=75 1200w, https://content.sfstandard.com/wp-content/uploads/2024/06/featured_20240624neighborsmayor.jpeg?w=1920&amp;q=75 1920w, https://content.sfstandard.com/wp-content/uploads/2024/06/featured_20240624neighborsmayor.jpeg?w=2048&amp;q=75 2048w, https://content.sfstandard.com/wp-content/uploads/2024/06/featured_20240624neighborsmayor.jpeg?w=3840&amp;q=75 3840w" src="https://content.sfstandard.com/wp-content/uploads/2024/06/featured_20240624neighborsmayor.jpeg?w=3840&amp;q=75" decoding="async" data-nimg="responsive" style="position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%" class="object-cover lazyloaded" loading="lazy"/></noscript>
                                    </span>
                                 </div>
                                 <div class="MostPopular_MostPopularArticleTitleContainer__zCCkY">
                                    <span class="font-ivar-headline text-headline-ivar-3 h-min border-b-warm-gray-300 border-b-[1px]">4</span>
                                    <a href="/2024/06/24/san-franciscos-most-powerful-political-group-endorses-farrell-lurie-over-breed/">
                                       <h3 class="text-heading-m">San Francisco’s most powerful political group endorses Farrell, Lurie over Breed</h3>
                                    </a>
                                 </div>
                              </li>
                              <hr class="hidden md:block bg-warm-gray-500">
                              <li class="MostPopular_MostPopularArticleContainer__n0o_W">
                                 <div class="aspect-normal md:hidden">
                                    <span style="box-sizing:border-box;display:block;overflow:hidden;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;position:relative">
                                       <span style="box-sizing:border-box;display:block;width:initial;height:initial;background:none;opacity:1;border:0;margin:0;padding:0;padding-top:65.625%"></span><img alt="The image shows a conductor passionately leading musicians, with flames and a fiery red backdrop intensifying the scene. Two violinists are visible in the foreground." src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" decoding="async" data-nimg="responsive" class="object-cover lazyloaded" style="position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%">
                                       <noscript><img alt="The image shows a conductor passionately leading musicians, with flames and a fiery red backdrop intensifying the scene. Two violinists are visible in the foreground." sizes="100vw" srcSet="https://content.sfstandard.com/wp-content/uploads/2024/06/sfstandard_salonen.jpg?w=640&amp;q=75 640w, https://content.sfstandard.com/wp-content/uploads/2024/06/sfstandard_salonen.jpg?w=750&amp;q=75 750w, https://content.sfstandard.com/wp-content/uploads/2024/06/sfstandard_salonen.jpg?w=828&amp;q=75 828w, https://content.sfstandard.com/wp-content/uploads/2024/06/sfstandard_salonen.jpg?w=1080&amp;q=75 1080w, https://content.sfstandard.com/wp-content/uploads/2024/06/sfstandard_salonen.jpg?w=1200&amp;q=75 1200w, https://content.sfstandard.com/wp-content/uploads/2024/06/sfstandard_salonen.jpg?w=1920&amp;q=75 1920w, https://content.sfstandard.com/wp-content/uploads/2024/06/sfstandard_salonen.jpg?w=2048&amp;q=75 2048w, https://content.sfstandard.com/wp-content/uploads/2024/06/sfstandard_salonen.jpg?w=3840&amp;q=75 3840w" src="https://content.sfstandard.com/wp-content/uploads/2024/06/sfstandard_salonen.jpg?w=3840&amp;q=75" decoding="async" data-nimg="responsive" style="position:absolute;top:0;left:0;bottom:0;right:0;box-sizing:border-box;padding:0;border:none;margin:auto;display:block;width:0;height:0;min-width:100%;max-width:100%;min-height:100%;max-height:100%" class="object-cover lazyloaded" loading="lazy"/></noscript>
                                    </span>
                                 </div>
                                 <div class="MostPopular_MostPopularArticleTitleContainer__zCCkY">
                                    <span class="font-ivar-headline text-headline-ivar-3 h-min border-b-warm-gray-300 border-b-[1px]">5</span>
                                    <a href="/opinion/2024/06/20/san-francisco-symphony-troubled/">
                                       <h3 class="text-heading-m">Opinion: Hear that? It’s the sound of the San Francisco Symphony setting itself on fire</h3>
                                    </a>
                                 </div>
                              </li>
                           </ol>
                           <div class="border-b-highlight border-b-[6px]"></div>
                           <div class="pt-lg md:pt-xl" role="separator"></div>
                           <div class="CtaBox_ContainerNoBorder__aKVTc"><a class="CtaBox_Icon__OonAr m-auto" href="https://lifeinsevensongs.com"><img src="https://content.sfstandard.com/wp-content/uploads/2024/06/liss-homepagepromo-4x.jpg?resize=690px,1016px&amp;quality=100" width="345" height="508" alt="Life in Seven Songs show art - Intimate biographies of some of the world’s most fascinating people, told through the songs that made them who they are" class="CtaBox_Icon__OonAr"></a></div>
                        </div>
                     </div>
                  </aside>
               </div>
            </main>
            <footer aria-hidden="false" class="Footer_container__w3fri">
               <div class="Footer_content__1w4vB">
                  <div class="Footer_main__92joB">
                     <div>
                        <svg xmlns="http://www.w3.org/2000/svg" width="36" height="35.02702702702703" viewBox="0 0 37 36" fill="none">
                           <g clip-path="url(#clip0_1189_707)">
                              <mask id="mask0_1189_707" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="37" height="36">
                                 <path d="M36.5 0H0.5V36H36.5V0Z" fill="white"></path>
                              </mask>
                              <g mask="url(#mask0_1189_707)">
                                 <path d="M36.5 0H0.5V36H36.5V0Z" fill="#FFEB2D"></path>
                                 <path d="M18.925 28.125C23.6801 28.125 26.6061 25.346 26.6061 21.8519C26.6061 17.78 24.1019 16.4868 19.2345 15.8814C16.1396 15.4413 15.4925 14.781 15.4925 13.3777C15.4925 12.0571 16.5054 11.1491 18.5029 11.1491C20.5007 11.1491 21.5698 11.9745 21.8513 13.763H26.0435C25.6776 9.85597 23.0328 7.875 18.5029 7.875C14.0576 7.875 11.1877 10.3787 11.1877 13.7353C11.1877 17.5323 13.2979 19.0455 18.4467 19.7058C21.401 20.1459 22.245 20.6964 22.245 22.2647C22.245 23.8055 20.9228 24.8785 18.925 24.8785C15.9428 24.8785 15.1829 23.4201 14.9862 21.6594H10.625C10.8782 25.5939 13.4104 28.125 18.925 28.125Z" fill="#1C1917"></path>
                              </g>
                           </g>
                           <defs>
                              <clipPath id="clip0_1189_707">
                                 <rect width="36" height="36" fill="white" transform="translate(0.5)"></rect>
                              </clipPath>
                           </defs>
                        </svg>
                        <nav aria-labelledby="social-links-title-:R15p8m:">
                           <h2 class="sr-only" id="social-links-title-:R15p8m:">Social Links</h2>
                           <ul class="flex items-center Footer_socialLinks__oSgxb">
                              <li class="mx-3.5 first:ml-0 last:mr-0">
                                 <a class="can-hover:hover:text-marigold" target="_blank" rel="noreferrer" aria-label="The San Francisco Standard on Instagram" href="https://www.instagram.com/sfstandard/">
                                    <svg width="13" height="13" viewBox="0 0 13 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                                       <path d="M9.971 2.249C9.81673 2.249 9.66593 2.29475 9.53765 2.38045C9.40938 2.46616 9.30941 2.58798 9.25037 2.73051C9.19134 2.87303 9.17589 3.02987 9.20599 3.18117C9.23608 3.33248 9.31037 3.47146 9.41946 3.58054C9.52854 3.68963 9.66752 3.76392 9.81883 3.79401C9.97013 3.82411 10.127 3.80866 10.2695 3.74963C10.412 3.69059 10.5338 3.59062 10.6195 3.46234C10.7053 3.33407 10.751 3.18327 10.751 3.029C10.751 2.82213 10.6688 2.62373 10.5225 2.47746C10.3763 2.33118 10.1779 2.249 9.971 2.249ZM12.961 3.822C12.9484 3.28269 12.8474 2.74911 12.662 2.2425C12.4967 1.80904 12.2395 1.41653 11.908 1.092C11.5861 0.758827 11.1927 0.503215 10.7575 0.3445C10.2522 0.153505 9.71805 0.0501865 9.178 0.039C8.489 -3.63216e-08 8.268 0 6.5 0C4.732 0 4.511 -3.63216e-08 3.822 0.039C3.28195 0.0501865 2.74777 0.153505 2.2425 0.3445C1.80809 0.504822 1.41501 0.760214 1.092 1.092C0.758827 1.41387 0.503215 1.80729 0.3445 2.2425C0.153505 2.74777 0.0501865 3.28195 0.039 3.822C-3.63216e-08 4.511 0 4.732 0 6.5C0 8.268 -3.63216e-08 8.489 0.039 9.178C0.0501865 9.71805 0.153505 10.2522 0.3445 10.7575C0.503215 11.1927 0.758827 11.5861 1.092 11.908C1.41501 12.2398 1.80809 12.4952 2.2425 12.6555C2.74777 12.8465 3.28195 12.9498 3.822 12.961C4.511 13 4.732 13 6.5 13C8.268 13 8.489 13 9.178 12.961C9.71805 12.9498 10.2522 12.8465 10.7575 12.6555C11.1927 12.4968 11.5861 12.2412 11.908 11.908C12.241 11.5847 12.4984 11.1918 12.662 10.7575C12.8474 10.2509 12.9484 9.71731 12.961 9.178C12.961 8.489 13 8.268 13 6.5C13 4.732 13 4.511 12.961 3.822ZM11.791 9.1C11.7863 9.5126 11.7115 9.92141 11.57 10.309C11.4662 10.5919 11.2995 10.8475 11.0825 11.0565C10.8717 11.2713 10.6166 11.4377 10.335 11.544C9.94741 11.6855 9.5386 11.7603 9.126 11.765C8.476 11.7975 8.2355 11.804 6.526 11.804C4.8165 11.804 4.576 11.804 3.926 11.765C3.49758 11.773 3.07099 11.7071 2.665 11.57C2.39576 11.4582 2.15238 11.2923 1.95 11.0825C1.73426 10.8737 1.56965 10.6179 1.469 10.335C1.31031 9.94185 1.22229 9.52376 1.209 9.1C1.209 8.45 1.17 8.2095 1.17 6.5C1.17 4.7905 1.17 4.55 1.209 3.9C1.21191 3.47819 1.28892 3.06016 1.4365 2.665C1.55093 2.39064 1.72657 2.14608 1.95 1.95C2.14748 1.72651 2.39154 1.54901 2.665 1.43C3.06121 1.28703 3.4788 1.2123 3.9 1.209C4.55 1.209 4.7905 1.17 6.5 1.17C8.2095 1.17 8.45 1.17 9.1 1.209C9.5126 1.21373 9.92141 1.28846 10.309 1.43C10.6044 1.53962 10.8695 1.71785 11.0825 1.95C11.2955 2.14967 11.4619 2.39378 11.57 2.665C11.7145 3.06081 11.7892 3.47866 11.791 3.9C11.8235 4.55 11.83 4.7905 11.83 6.5C11.83 8.2095 11.8235 8.45 11.791 9.1ZM6.5 3.1655C5.84078 3.16678 5.19673 3.36344 4.64923 3.73062C4.10173 4.0978 3.67535 4.61903 3.42396 5.22844C3.17258 5.83785 3.10747 6.5081 3.23687 7.1545C3.36626 7.8009 3.68435 8.39445 4.15095 8.86014C4.61755 9.32582 5.21171 9.64276 5.85836 9.7709C6.50501 9.89903 7.17513 9.83262 7.78405 9.58005C8.39298 9.32748 8.91337 8.90009 9.27948 8.35187C9.6456 7.80366 9.841 7.15922 9.841 6.5C9.84186 6.06132 9.75598 5.62679 9.58831 5.22142C9.42063 4.81605 9.17446 4.44783 8.86397 4.13793C8.55347 3.82804 8.18477 3.58259 7.77907 3.4157C7.37337 3.24882 6.93868 3.16379 6.5 3.1655ZM6.5 8.6645C6.0719 8.6645 5.65342 8.53755 5.29747 8.29972C4.94152 8.06188 4.66409 7.72383 4.50026 7.32832C4.33644 6.93281 4.29357 6.4976 4.37709 6.07773C4.46061 5.65786 4.66676 5.27218 4.96947 4.96947C5.27218 4.66676 5.65786 4.46061 6.07773 4.37709C6.4976 4.29357 6.93281 4.33644 7.32832 4.50026C7.72383 4.66409 8.06188 4.94152 8.29972 5.29747C8.53755 5.65342 8.6645 6.0719 8.6645 6.5C8.6645 6.78425 8.60851 7.06571 8.49974 7.32832C8.39096 7.59093 8.23152 7.82954 8.03053 8.03053C7.82954 8.23152 7.59093 8.39096 7.32832 8.49974C7.06571 8.60851 6.78425 8.6645 6.5 8.6645Z" fill="currentColor"></path>
                                    </svg>
                                 </a>
                              </li>
                              <li class="mx-3.5 first:ml-0 last:mr-0">
                                 <a class="can-hover:hover:text-marigold" target="_blank" rel="noreferrer" aria-label="The San Francisco Standard on Twitter" href="https://twitter.com/sfstandard">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="13.764705882352942" height="13" viewBox="0 0 18 17" fill="none">
                                       <path d="M10.5909 7.19834L16.8157 0H15.3406L9.93558 6.25022L5.61857 0H0.639404L7.16757 9.45144L0.639404 17H2.11459L7.82248 10.3996L12.3816 17H17.3607L10.5909 7.19834ZM8.57039 9.53471L7.90895 8.59356L2.64612 1.10473H4.91191L9.15908 7.14845L9.82052 8.0896L15.3413 15.9455H13.0755L8.57039 9.53471Z" fill="currentColor"></path>
                                    </svg>
                                 </a>
                              </li>
                              <li class="mx-3.5 first:ml-0 last:mr-0">
                                 <a class="can-hover:hover:text-marigold" target="_blank" rel="noreferrer" aria-label="The San Francisco Standard on Facebook" href="https://www.facebook.com/SFStandard">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="6.933333333333334" height="13" viewBox="0 0 8 15" fill="none">
                                       <path d="M6.39293 2.49002H7.75V0.105022C7.09294 0.0340327 6.43274 -0.00101571 5.77214 2.2398e-05C3.80871 2.2398e-05 2.46607 1.24502 2.46607 3.52502V5.49001H0.25V8.16001H2.46607V15H5.12247V8.16001H7.33133L7.66338 5.49001H5.12247V3.78752C5.12247 3.00002 5.32459 2.49002 6.39293 2.49002Z" fill="currentColor"></path>
                                    </svg>
                                 </a>
                              </li>
                              <li class="mx-3.5 first:ml-0 last:mr-0">
                                 <a class="can-hover:hover:text-marigold" target="_blank" rel="noreferrer" aria-label="The San Francisco Standard on TikTok" href="https://www.tiktok.com/@sfstandard">
                                    <svg width="11.15121512151215" height="13" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 2859 3333" shape-rendering="geometricPrecision" text-rendering="geometricPrecision" image-rendering="optimizeQuality" fill-rule="evenodd" clip-rule="evenodd">
                                       <path d="M2081 0c55 473 319 755 778 785v532c-266 26-499-61-770-225v995c0 1264-1378 1659-1932 753-356-583-138-1606 1004-1647v561c-87 14-180 36-265 65-254 86-398 247-358 531 77 544 1075 705 992-358V1h551z" fill="currentColor"></path>
                                    </svg>
                                 </a>
                              </li>
                              <li class="mx-3.5 first:ml-0 last:mr-0">
                                 <a class="can-hover:hover:text-marigold" target="_blank" rel="noreferrer" aria-label="The San Francisco Standard on Threads" href="https://www.threads.net/@sfstandard">
                                    <svg width="11.414" height="13" version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 878 1000" xml:space="preserve">
                                       <g>
                                          <path d="M446.7,1000h-0.3c-149.2-1-263.9-50.2-341-146.2C36.9,768.3,1.5,649.4,0.3,500.4v-0.7c1.2-149.1,36.6-267.9,105.2-353.4 C182.5,50.2,297.3,1,446.4,0h0.3h0.3c114.4,0.8,210.1,30.2,284.4,87.4c69.9,53.8,119.1,130.4,146.2,227.8l-85,23.7 c-46-165-162.4-249.3-346-250.6c-121.2,0.9-212.9,39-272.5,113.2C118.4,271,89.6,371.4,88.5,500c1.1,128.6,29.9,229,85.7,298.5 c59.6,74.3,151.3,112.4,272.5,113.2c109.3-0.8,181.6-26.3,241.7-85.2c68.6-67.2,67.4-149.7,45.4-199.9 c-12.9-29.6-36.4-54.2-68.1-72.9c-8,56.3-25.9,101.9-53.5,136.3c-36.9,45.9-89.2,71-155.4,74.6c-50.1,2.7-98.4-9.1-135.8-33.4 c-44.3-28.7-70.2-72.5-73-123.5c-2.7-49.6,17-95.2,55.4-128.4c36.7-31.7,88.3-50.3,149.3-53.8c44.9-2.5,87-0.5,125.8,5.9 c-5.2-30.9-15.6-55.5-31.2-73.2c-21.4-24.4-54.5-36.8-98.3-37.1c-0.4,0-0.8,0-1.2,0c-35.2,0-83,9.7-113.4,55L261.2,327 c40.8-60.6,107-94,186.6-94c0.6,0,1.2,0,1.8,0c133.1,0.8,212.4,82.3,220.3,224.5c4.5,1.9,9,3.9,13.4,5.9 c62.1,29.2,107.5,73.4,131.4,127.9c33.2,75.9,36.3,199.6-64.5,298.3C673.1,965,579.6,999.1,447,1000L446.7,1000L446.7,1000z M488.5,512.9c-10.1,0-20.3,0.3-30.8,0.9c-76.5,4.3-124.2,39.4-121.5,89.3c2.8,52.3,60.5,76.6,116,73.6 c51-2.7,117.4-22.6,128.6-154.6C552.6,516,521.7,512.9,488.5,512.9z" fill="currentColor"></path>
                                       </g>
                                    </svg>
                                 </a>
                              </li>
                              <li class="mx-3.5 first:ml-0 last:mr-0">
                                 <a class="can-hover:hover:text-marigold" target="_blank" rel="noreferrer" aria-label="The San Francisco Standard on LinkedIn" href="https://www.linkedin.com/company/sfstandard">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="13.866666666666667" height="13" viewBox="0 0 16 15" fill="none">
                                       <path d="M15.5 1.10294V13.8971C15.5 14.1896 15.3838 14.4701 15.177 14.677C14.9701 14.8838 14.6896 15 14.3971 15H1.60294C1.31042 15 1.02989 14.8838 0.823044 14.677C0.616202 14.4701 0.5 14.1896 0.5 13.8971V1.10294C0.5 0.810423 0.616202 0.529886 0.823044 0.323044C1.02989 0.116202 1.31042 0 1.60294 0L14.3971 0C14.6896 0 14.9701 0.116202 15.177 0.323044C15.3838 0.529886 15.5 0.810423 15.5 1.10294ZM4.91176 5.73529H2.70588V12.7941H4.91176V5.73529ZM5.11029 3.30882C5.11146 3.14197 5.07974 2.97651 5.01696 2.82191C4.95418 2.66731 4.86156 2.52659 4.7444 2.40779C4.62723 2.28898 4.48781 2.19442 4.3341 2.1295C4.18038 2.06458 4.01539 2.03057 3.84853 2.02941H3.80882C3.4695 2.02941 3.14408 2.16421 2.90414 2.40414C2.66421 2.64408 2.52941 2.9695 2.52941 3.30882C2.52941 3.64814 2.66421 3.97357 2.90414 4.2135C3.14408 4.45344 3.4695 4.58824 3.80882 4.58824C3.97569 4.59234 4.14173 4.56353 4.29746 4.50344C4.45319 4.44335 4.59555 4.35317 4.71642 4.23804C4.83728 4.12291 4.93427 3.9851 5.00186 3.83247C5.06944 3.67985 5.10629 3.5154 5.11029 3.34853V3.30882ZM13.2941 8.50588C13.2941 6.38382 11.9441 5.55882 10.6029 5.55882C10.1638 5.53684 9.72659 5.63037 9.33489 5.83009C8.94319 6.02981 8.6107 6.32874 8.37059 6.69706H8.30882V5.73529H6.23529V12.7941H8.44118V9.03971C8.40929 8.65519 8.53041 8.27362 8.77823 7.97789C9.02605 7.68217 9.38054 7.49616 9.76471 7.46029H9.84853C10.55 7.46029 11.0706 7.90147 11.0706 9.01324V12.7941H13.2765L13.2941 8.50588Z" fill="currentColor"></path>
                                    </svg>
                                 </a>
                              </li>
                              <li class="mx-3.5 first:ml-0 last:mr-0">
                                 <a class="can-hover:hover:text-marigold" target="_blank" rel="noreferrer" aria-label="The San Francisco Standard on Apple News" href="https://apple.news/TDHP6_LSbQJGxQ_S0TndMnw?subscribe=1">
                                    <svg version="1.1" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 361 361" xml:space="preserve" width="13" height="13">
                                       <path d="M359.99652,112.58435c0-4.29785,0.00348-8.59747-0.02289-12.89532c-0.02283-3.62109-0.065-7.24042-0.16168-10.86151 c-0.21448-7.88733-0.67847-15.8432-2.08112-23.64435c-1.42365-7.9137-3.74548-15.27893-7.40674-22.46838 c-3.5979-7.06635-8.29962-13.53162-13.90826-19.13904c-5.60693-5.60565-12.07507-10.30426-19.14264-13.90253 c-7.19403-3.6615-14.56567-5.98535-22.48566-7.40741c-7.80048-1.401-15.75562-1.86505-23.64398-2.07776 c-3.62073-0.09845-7.24152-0.14062-10.86401-0.16168C255.98029,0,251.68109,0.00171,247.38013,0.00171L197.99182,0h-36.01611 l-49.36902,0.00348c-4.29742,0-8.59668-0.00348-12.8941,0.02289c-3.62079,0.02283-7.23981,0.065-10.86053,0.16168 c-7.8866,0.21448-15.8418,0.67853-23.64221,2.0813C57.29688,3.69318,49.93231,6.0152,42.74347,9.67676 c-7.06573,3.59821-13.5304,8.30035-19.13733,13.90955C18.00098,29.19373,13.3028,35.66248,9.7049,42.73065 c-3.66119,7.1947-5.9848,14.56696-7.40674,22.48767c-1.40088,7.80121-1.86493,15.75702-2.07758,23.64612 c-0.09845,3.62109-0.14062,7.24219-0.16168,10.86505c-0.02637,4.29956-0.02466,8.59918-0.02466,12.90057l-0.00171,49.39276 L0,198.00964l0.00348,49.37347c0,4.29785-0.00348,8.59747,0.02289,12.89532c0.02283,3.62109,0.065,7.24042,0.16168,10.86151 c0.21448,7.88733,0.67847,15.84314,2.08112,23.64435c1.42365,7.91364,3.74548,15.27887,7.40674,22.46832 c3.5979,7.06641,8.29962,13.53168,13.90826,19.1391c5.60693,5.60565,12.07507,10.30426,19.14264,13.90253 c7.19403,3.6615,14.56567,5.98535,22.48566,7.40741c7.80048,1.401,15.75562,1.86505,23.64398,2.07776 c3.62073,0.09845,7.24152,0.14062,10.86401,0.16168c4.29926,0.02637,8.59845,0.02466,12.89941,0.02466l49.38831,0.00171 L198.05682,360l49.36896-0.00354c4.29749,0,8.59668,0.00354,12.89417-0.02283c3.62079-0.02289,7.23975-0.06506,10.86053-0.16174 c7.8866-0.21442,15.84174-0.67853,23.64221-2.08124c7.91296-1.42383,15.27753-3.74591,22.46631-7.40741 c7.0658-3.59827,13.53046-8.30042,19.13733-13.90961c5.60516-5.60742,10.30341-12.07617,13.90131-19.14429 c3.66119-7.19476,5.9848-14.56702,7.40674-22.48773c1.40088-7.80115,1.86487-15.75702,2.07758-23.64606 c0.09839-3.62109,0.14062-7.24219,0.16168-10.86505c0.02637-4.29962,0.0246-8.59924,0.0246-12.90057L360,197.97711v-36.01935 L359.99652,112.58435z M192.35278,76.875h62.67316l8.56824,0.00031c0.74597,0,1.49194-0.00037,2.23792,0.00415 c0.62836,0.00391,1.25659,0.01111,1.88483,0.02808c1.36823,0.03699,2.74854,0.11761,4.10162,0.3609 c1.37402,0.24695,2.65289,0.65021,3.90118,1.28601c1.22614,0.62463,2.34808,1.44043,3.32098,2.41382 c0.97296,0.97339,1.78851,2.09613,2.41284,3.32312c0.63513,1.24829,1.03802,2.52704,1.28485,3.90088 c0.24335,1.35437,0.32404,2.73572,0.36096,4.10529c0.01703,0.62848,0.02429,1.25702,0.02808,1.88568 c0.00452,0.74634,0.00421,1.49268,0.00421,2.23895l0.00024,8.57245v62.66766c0,1.80176-2.42157,2.39001-3.24384,0.78693 c-19.6405-38.29102-50.03455-68.68787-88.32208-88.33002C189.96301,79.29681,190.55121,76.875,192.35278,76.875z M167.67975,283.125 h-62.67316l-8.5683-0.00031c-0.74591,0-1.49188,0.00031-2.23785-0.00421c-0.62836-0.00385-1.25659-0.01111-1.88483-0.02802 c-1.36829-0.03705-2.7486-0.11768-4.10168-0.3609c-1.37402-0.24701-2.65283-0.65021-3.90112-1.28601 c-1.2262-0.62469-2.34808-1.44049-3.32098-2.41382c-0.97296-0.97345-1.78851-2.09619-2.4129-3.32312 c-0.63507-1.24829-1.03796-2.52704-1.28485-3.90094c-0.24329-1.35437-0.32397-2.73572-0.3609-4.10529 c-0.01703-0.62842-0.02429-1.25696-0.02814-1.88568c-0.00446-0.74628-0.00415-1.49255-0.00415-2.23895l-0.00031-8.57245v-62.66766 c0-1.8017,2.42157-2.38995,3.2439-0.78687c19.64044,38.29095,50.03455,68.68781,88.32208,88.33008 C170.06958,280.70325,169.48132,283.125,167.67975,283.125z M283.13177,263.58539c0,0.74585,0.00037,1.49225-0.00427,2.23804 c-0.00385,0.62878-0.01117,1.25659-0.02789,1.88489c-0.03705,1.36841-0.11786,2.74878-0.36096,4.10217 c-0.24689,1.37396-0.65002,2.65302-1.28601,3.90192c-0.62451,1.22577-1.44019,2.34766-2.41339,3.32086 c-0.97351,0.97308-2.09607,1.78857-3.323,2.41321c-1.24811,0.63513-2.52673,1.03772-3.90057,1.28467 c-1.35406,0.24347-2.73547,0.32404-4.10486,0.36102c-0.62842,0.01715-1.2569,0.02435-1.88538,0.02808 c-0.74628,0.00458-1.49268,0.0047-2.23877,0.0047H237.8374c-5.24506-0.00012-7.9292-0.97321-11.87524-4.91925L81.81934,134.0498 c-3.89667-3.89722-4.91876-6.61902-4.91876-11.87616l0.00012-25.75909c-0.00006-0.74573-0.00031-1.49225,0.00427-2.23804 c0.00391-0.62878,0.01117-1.25653,0.02789-1.88483c0.03711-1.36847,0.11786-2.74878,0.36096-4.10217 c0.24689-1.37402,0.65002-2.65308,1.28607-3.90186c0.62439-1.22577,1.44006-2.34778,2.41339-3.32092 c0.97351-0.97308,2.09607-1.78857,3.323-2.41321c1.24805-0.63513,2.52661-1.03772,3.90057-1.28473 c1.354-0.24347,2.73535-0.32404,4.1048-0.36102c0.62842-0.01715,1.2569-0.02435,1.88544-0.02802 c0.74628-0.00464,1.49261-0.0047,2.23871-0.0047h25.74927c5.24506,0.00006,7.9292,0.97314,11.87524,4.91925l144.14282,144.15582 c3.89673,3.89734,4.91882,6.61902,4.91882,11.87622L283.13177,263.58539z" fill="currentColor"></path>
                                    </svg>
                                 </a>
                              </li>
                           </ul>
                        </nav>
                     </div>
                     <nav class="Footer_nav__mlfaR" aria-labelledby="footer-nav-title">
                        <h2 class="sr-only" id="footer-nav-title">Footer Navigation</h2>
                        <ul class="Footer_navList__uBStr">
                           <li class="Footer_navItem__j3_nN">
                              <ul class="Footer_navSublist__s8p00">
                                 <li class="Footer_navItem__j3_nN"><a href="/about/">About Us</a></li>
                                 <li class="Footer_navItem__j3_nN"><a href="/staff/">Staff</a></li>
                                 <li class="Footer_navItem__j3_nN"><a href="https://jobs.sfstandard.com/">Jobs</a></li>
                              </ul>
                           </li>
                           <li class="Footer_navItem__j3_nN">
                              <ul class="Footer_navSublist__s8p00">
                                 <li class="Footer_navItem__j3_nN"><a href="/newsletters/">Newsletters</a></li>
                                 <li class="Footer_navItem__j3_nN"><a href="/archive/">Archive</a></li>
                                 <li class="Footer_navItem__j3_nN"><a href="/rss-feeds/">RSS Feeds</a></li>
                              </ul>
                           </li>
                           <li class="Footer_navItem__j3_nN">
                              <ul class="Footer_navSublist__s8p00">
                                 <li class="Footer_navItem__j3_nN"><a href="/contact-us/">Contact Us</a></li>
                                 <li class="Footer_navItem__j3_nN"><a href="/tips/">Tips</a></li>
                                 <li class="Footer_navItem__j3_nN"><a href="/advertising/">Advertising Inquiries</a></li>
                              </ul>
                           </li>
                           <li class="Footer_navItem__j3_nN">
                              <ul class="Footer_navSublist__s8p00">
                                 <li class="Footer_navItem__j3_nN"><a href="/ethics-standards/">Standards &amp; Ethics</a></li>
                                 <li class="Footer_navItem__j3_nN"><a href="/ask-the-standard/">Ask The Standard</a></li>
                              </ul>
                           </li>
                        </ul>
                     </nav>
                  </div>
                  <div class="Footer_legal__C8gOU">
                     <div>© The San Francisco Standard. All Rights Reserved.</div>
                     <nav class="Footer_legalNav__TrpBh" aria-labelledby="footer-privacy-title">
                        <h2 class="sr-only" id="footer-privacy-title">Legal Information</h2>
                        <ul>
                           <li class="Footer_legalNavItem__qYsTi"><a href="/terms-of-use/">Terms of Use</a></li>
                           <li class="Footer_legalNavItem__qYsTi"><a href="/privacy-policy/">Privacy Policy</a></li>
                           <li class="Footer_legalNavItem__qYsTi"><a href="/privacy-policy/#california-resident-rights">California Resident Rights</a></li>
                           <li class="Footer_legalNavItem__qYsTi">
                              <button class="Footer_privacyOptions__Hr_rl" type="button">
                                 Your Privacy Choices
                                 <svg version="1.1" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 30 14" xml:space="preserve" width="30" height="14">
                                    <g>
                                       <g id="final---dec.11-2020_1_">
                                          <g id="_x30_208-our-toggle_2_" transform="translate(-1275.000000, -200.000000)">
                                             <g id="Final-Copy-2_2_" transform="translate(1275.000000, 200.000000)">
                                                <path style="fill-rule:evenodd;clip-rule:evenodd;fill:#FFFFFF" d="M7.4,12.8h6.8l3.1-11.6H7.4C4.2,1.2,1.6,3.8,1.6,7S4.2,12.8,7.4,12.8z"></path>
                                             </g>
                                          </g>
                                       </g>
                                       <g id="final---dec.11-2020">
                                          <g id="_x30_208-our-toggle" transform="translate(-1275.000000, -200.000000)">
                                             <g id="Final-Copy-2" transform="translate(1275.000000, 200.000000)">
                                                <path style="fill-rule:evenodd;clip-rule:evenodd;fill:#0066FF" d="M22.6,0H7.4c-3.9,0-7,3.1-7,7s3.1,7,7,7h15.2c3.9,0,7-3.1,7-7S26.4,0,22.6,0z M1.6,7c0-3.2,2.6-5.8,5.8-5.8 h9.9l-3.1,11.6H7.4C4.2,12.8,1.6,10.2,1.6,7z"></path>
                                                <path id="x" style="fill:#FFFFFF" d="M24.6,4c0.2,0.2,0.2,0.6,0,0.8l0,0L22.5,7l2.2,2.2c0.2,0.2,0.2,0.6,0,0.8c-0.2,0.2-0.6,0.2-0.8,0 l0,0l-2.2-2.2L19.5,10c-0.2,0.2-0.6,0.2-0.8,0c-0.2-0.2-0.2-0.6,0-0.8l0,0L20.8,7l-2.2-2.2c-0.2-0.2-0.2-0.6,0-0.8 c0.2-0.2,0.6-0.2,0.8,0l0,0l2.2,2.2L23.8,4C24,3.8,24.4,3.8,24.6,4z"></path>
                                                <path id="y" style="fill:#0066FF" d="M12.7,4.1c0.2,0.2,0.3,0.6,0.1,0.8l0,0L8.6,9.8C8.5,9.9,8.4,10,8.3,10c-0.2,0.1-0.5,0.1-0.7-0.1l0,0 L5.4,7.7c-0.2-0.2-0.2-0.6,0-0.8c0.2-0.2,0.6-0.2,0.8,0l0,0L8,8.6l3.8-4.5C12,3.9,12.4,3.9,12.7,4.1z"></path>
                                             </g>
                                          </g>
                                       </g>
                                    </g>
                                 </svg>
                              </button>
                           </li>
                        </ul>
                     </nav>
                  </div>
               </div>
            </footer>
         </div>
         <div style="display: inline;">
            <div class="Flyouts_FlyoutBackdrop___ykZa" data-visible="false" aria-hidden="true"></div>
            <div class="Flyouts_FlyoutParentContainer__GAGW9" data-visible="false" id="stay-on-top-flyout" role="dialog" aria-label="Newsletter sign-up" aria-hidden="true">
               <div class="relative w-full">
                  <button class="Flyouts_FlyoutContainerCloseIcon__AwrtZ" aria-label="Close" tabindex="-1">
                     <svg width="14" height="13" viewBox="0 0 14 13" fill="none" xmlns="http://www.w3.org/2000/svg" class="stroke-warm-gray-700">
                        <path d="M13 0.5L1 12.5M1 0.5L13 12.5" stroke="#A8A29E" stroke-linecap="round" stroke-linejoin="round" class="stroke-warm-gray-700"></path>
                     </svg>
                  </button>
                  <div class="Flyouts_FlyoutContainerArticle__YxUFh">
                     <form>
                        <h1 class="sr-only" id="stay-on-top">Stay on top of what’s happening in your city with news delivered directly to your inbox.</h1>
                        <div class="Flyouts_SubscribeContentFlyoutContainer____q4Q">
                           <h2 class="Flyouts_SubscribeContentFlyoutContainerTitle__Dm8eQ">Stay on top of what’s happening in your city with news delivered directly to your inbox.</h2>
                           <div class="Flyouts_SubscribeContentFlyoutContainerColumn__KlhZ1">
                              <div class="NewsletterShowcaseCard_NewsletterShowcaseCardContainer__ym1Z2">
                                 <div class="NewsletterShowcaseCard_NewsletterShowcaseCardContent__arn4J">
                                    <h1 class="NewsletterShowcaseCard_NewsletterShowcaseCardHeading__MJp5t">The Daily</h1>
                                    <p class="NewsletterShowcaseCard_NewsletterShowcaseCardDescription__Oe6Bt">A rundown of the stories we’ve covered in the past 24 hours, delivered first thing Monday through Saturday</p>
                                 </div>
                              </div>
                              <div class="Flyouts_SubscribeContentFlyoutContainerEmail__KV_8T">
                                 <div class="Flyouts_SubscribeContentFlyoutContainerEmailFieldContainer__Hf8NI"><input id="subscribeEmail" name="email" type="email" autocomplete="email" placeholder="Email" class="text-warm-gray-900" aria-invalid="false" tabindex="-1"><button class="" type="submit" tabindex="-1">Sign Up</button></div>
                                 <p class="Flyouts_SubscribeContentFlyoutContainerDisclaimer__j_5Bp">By clicking Sign Up you confirm you have read and agree to our <a href="https://sfstandard.com/terms-of-use/" class="underline" tabindex="-1">Terms of Use</a> and acknowledge our <a href="https://sfstandard.com/privacy-policy/" class="underline" tabindex="-1">Privacy Policy</a>.&nbsp;This site is protected by reCAPTCHA and the Google <a href="https://policies.google.com/privacy" class="underline" target="_blank" rel="noopener noreferrer" tabindex="-1">Privacy Policy</a> and <a href="https://policies.google.com/terms" class="underline" target="_blank" rel="noopener noreferrer" tabindex="-1">Terms of Service</a> apply.</p>
                              </div>
                           </div>
                        </div>
                     </form>
                  </div>
                  <div class="Flyouts_SubscribeOthersContentPreferencesUpdatedContainer__zJrrj" data-visible="false" data-invalid="false" role="presentation" aria-hidden="true" aria-live="polite">
                     <h4 class="Flyouts_SubscribeOthersContentPreferencesUpdatedContainerParagraph__ABnGb">Your preferences have been saved</h4>
                  </div>
               </div>
            </div>
         </div>
      </div>
      <script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"type":"Post","url":"/2024/06/24/service-fee-restaurants-san-francisco/","updatedAt":"2024-06-24T11:59:54","slug":"service-fee-restaurants-san-francisco","date":"2024-06-24T10:00:00","databaseId":285407,"title":"Restaurant workers fear they could pay highest cost in junk-fee battle","cleanTitle":"Restaurant workers fear they could pay highest cost in junk-fee battle","byline":{"profiles":[{"id":281091,"email":"<EMAIL>","url":"https://sfstandard.com/author/lauren-saria/","title":"Lauren Saria","social":[{"network":"twitter","url":"https://twitter.com/lhsaria"},{"network":"instagram","url":"https://www.instagram.com/laurensaria/"},{"network":"linkedin","url":"https://www.linkedin.com/in/lauren-saria/"}],"titleImage":{"node":{"alt":"","src":"https://content.sfstandard.com/wp-content/uploads/2024/06/sfs-staffportrait-laurensaria.jpg"}},"disableProfilePage":false}]},"contributors":[],"excerpt":"Diners hate them. Restaurant owners say they need them. But in the battle over service fees, it’s servers and cooks on the front lines. ","blocks":[{"innerBlocks":[],"name":"core/paragraph","postId":285407,"blockType":[],"originalContent":"\u003cp\u003eIn an industry known for its sky-high turnover rates, Noel Madayag is an outlier. He’s been a cook at \u003ca href=\"https://www.cassavasf.com/\"\u003eCassava\u003c/a\u003e since the business opened 12 years ago. He likes working at the upscale restaurant, which has made \u003ca href=\"https://www.bizjournals.com/sanfrancisco/news/2023/03/03/yuka-ioroi-cassava-north-beach-restaurant.html\"\u003eheadlines\u003c/a\u003e for paying all staff at least $20 an hour and providing full health benefits including a 401k with a 5% employer match.\u0026nbsp;\u003c/p\u003e","saveContent":"\u003cp\u003eIn an industry known for its sky-high turnover rates, Noel Madayag is an outlier. He’s been a cook at \u003ca href=\"https://www.cassavasf.com/\"\u003eCassava\u003c/a\u003e since the business opened 12 years ago. He likes working at the upscale restaurant, which has made \u003ca href=\"https://www.bizjournals.com/sanfrancisco/news/2023/03/03/yuka-ioroi-cassava-north-beach-restaurant.html\"\u003eheadlines\u003c/a\u003e for paying all staff at least $20 an hour and providing full health benefits including a 401k with a 5% employer match.\u0026nbsp;\u003c/p\u003e","order":0,"get_parent":{},"attributes":{"content":"In an industry known for its sky-high turnover rates, Noel Madayag is an outlier. He’s been a cook at \u003ca href=\"https://www.cassavasf.com/\"\u003eCassava\u003c/a\u003e since the business opened 12 years ago. He likes working at the upscale restaurant, which has made \u003ca href=\"https://www.bizjournals.com/sanfrancisco/news/2023/03/03/yuka-ioroi-cassava-north-beach-restaurant.html\"\u003eheadlines\u003c/a\u003e for paying all staff at least $20 an hour and providing full health benefits including a 401k with a 5% employer match.\u0026nbsp;","dropCap":false,"sfsBlockId":"caa9c431-e7d2-49c1-9ff1-b65135235d2f","anchor":"","links":[]},"attributesType":[],"dynamicContent":null},{"innerBlocks":[],"name":"core/paragraph","postId":285407,"blockType":[],"originalContent":"\u003cp\u003eBut as of last week, Madayag is preparing for a job hunt. “I have been working on my resume,” he said. “I need something to supplement [my income].”\u0026nbsp;\u003c/p\u003e","saveContent":"\u003cp\u003eBut as of last week, Madayag is preparing for a job hunt. “I have been working on my resume,” he said. “I need something to supplement [my income].”\u0026nbsp;\u003c/p\u003e","order":1,"get_parent":{},"attributes":{"content":"But as of last week, Madayag is preparing for a job hunt. “I have been working on my resume,” he said. “I need something to supplement [my income].”\u0026nbsp;","dropCap":false,"sfsBlockId":"093e480f-4c30-4f60-b632-c7281f5c1334","anchor":"","links":[]},"attributesType":[],"dynamicContent":null},{"innerBlocks":[],"name":"core/paragraph","postId":285407,"blockType":[],"originalContent":"\u003cp\u003eMadayag is just one of many San Francisco restaurant industry workers bracing for the impact of \u003ca href=\"https://leginfo.legislature.ca.gov/faces/billNavClient.xhtml?bill_id=202320240SB478\"\u003eSB 478\u003c/a\u003e, a California law aimed at eliminating \u003ca href=\"https://www.nytimes.com/interactive/2024/02/23/business/what-is-drip-pricing.html\"\u003edrip pricing\u003c/a\u003e and junk fees. It’s set to go into effect on July 1 and would require restaurants to \u003ca href=\"https://sfstandard.com/2024/05/02/restaurant-san-francisco-surcharges-service-fees-rob-bonta/\"\u003estop using mandatory surcharges\u003c/a\u003e, including the 20% service fee that allows Cassava to provide its staff with health insurance and retirement funds.\u0026nbsp;\u003c/p\u003e","saveContent":"\u003cp\u003eMadayag is just one of many San Francisco restaurant industry workers bracing for the impact of \u003ca href=\"https://leginfo.legislature.ca.gov/faces/billNavClient.xhtml?bill_id=202320240SB478\"\u003eSB 478\u003c/a\u003e, a California law aimed at eliminating \u003ca href=\"https://www.nytimes.com/interactive/2024/02/23/business/what-is-drip-pricing.html\"\u003edrip pricing\u003c/a\u003e and junk fees. It’s set to go into effect on July 1 and would require restaurants to \u003ca href=\"https://sfstandard.com/2024/05/02/restaurant-san-francisco-surcharges-service-fees-rob-bonta/\"\u003estop using mandatory surcharges\u003c/a\u003e, including the 20% service fee that allows Cassava to provide its staff with health insurance and retirement funds.\u0026nbsp;\u003c/p\u003e","order":2,"get_parent":{},"attributes":{"content":"Madayag is just one of many San Francisco restaurant industry workers bracing for the impact of \u003ca href=\"https://leginfo.legislature.ca.gov/faces/billNavClient.xhtml?bill_id=202320240SB478\"\u003eSB 478\u003c/a\u003e, a California law aimed at eliminating \u003ca href=\"https://www.nytimes.com/interactive/2024/02/23/business/what-is-drip-pricing.html\"\u003edrip pricing\u003c/a\u003e and junk fees. It’s set to go into effect on July 1 and would require restaurants to \u003ca href=\"https://sfstandard.com/2024/05/02/restaurant-san-francisco-surcharges-service-fees-rob-bonta/\"\u003estop using mandatory surcharges\u003c/a\u003e, including the 20% service fee that allows Cassava to provide its staff with health insurance and retirement funds.\u0026nbsp;","dropCap":false,"sfsBlockId":"e21e6443-a197-4942-b24f-68c54b8655b6","anchor":"","links":[{"post_id":272208,"url":"https://sfstandard.com/2024/05/02/restaurant-san-francisco-surcharges-service-fees-rob-bonta/","title":"‘Disturbing news’: San Francisco restaurants dread new state law banning service fees "}]},"attributesType":[],"dynamicContent":null},{"innerBlocks":[],"name":"core/paragraph","postId":285407,"blockType":[],"originalContent":"\u003cp\u003eResponding to a deafening roar from restaurateurs who fear the unintended consequences of the law's passage, state legislators are hustling to pass a \u003ca href=\"https://sfstandard.com/2024/06/06/san-francisco-haney-wiener-sb1528/\"\u003ecarveout for restaurants\u003c/a\u003e this week. The revised bill has already received assembly approval, but if it fails to emerge out of the state senate or get signed into law by Governor Newsom next week, most restaurant owners say they'll be forced to raise menu prices.\u0026nbsp;\u003c/p\u003e","saveContent":"\u003cp\u003eResponding to a deafening roar from restaurateurs who fear the unintended consequences of the law\u0026#8217;s passage, state legislators are hustling to pass a \u003ca href=\"https://sfstandard.com/2024/06/06/san-francisco-haney-wiener-sb1528/\"\u003ecarveout for restaurants\u003c/a\u003e this week. The revised bill has already received assembly approval, but if it fails to emerge out of the state senate or get signed into law by Governor Newsom next week, most restaurant owners say they\u0026#8217;ll be forced to raise menu prices.\u0026nbsp;\u003c/p\u003e","order":3,"get_parent":{},"attributes":{"content":"Responding to a deafening roar from restaurateurs who fear the unintended consequences of the law\u0026#8217;s passage, state legislators are hustling to pass a \u003ca href=\"https://sfstandard.com/2024/06/06/san-francisco-haney-wiener-sb1528/\"\u003ecarveout for restaurants\u003c/a\u003e this week. The revised bill has already received assembly approval, but if it fails to emerge out of the state senate or get signed into law by Governor Newsom next week, most restaurant owners say they\u0026#8217;ll be forced to raise menu prices.\u0026nbsp;","dropCap":false,"sfsBlockId":"7a1cb835-cff6-4896-9ec0-772af2efa929","anchor":"","links":[{"post_id":281549,"url":"https://sfstandard.com/2024/06/06/san-francisco-haney-wiener-sb1528/","title":"\u0026#8216;This should have never happened\u0026#8217;:  Lawmakers want to let restaurants keep charging fees"}]},"attributesType":[],"dynamicContent":null},{"innerBlocks":[],"name":"core/image","postId":285407,"blockType":[],"originalContent":"\u003cfigure class=\"wp-block-image size-large\"\u003e\u003cimg src=\"https://content.sfstandard.com/wp-content/uploads/2024/06/20240328_allthingsconsumed-me-19.jpg?w=2500\" alt=\"A man holding a wine bottle explains something to three seated men in a modern restaurant, with food and drinks on the table and greenery in the foreground.\" class=\"wp-image-285659\"/\u003e\u003cfigcaption class=\"wp-element-caption\"\u003ePatrons dine at the Anchovy Bar in the Fillmore in San Francisco on Monday, March 25, 2024. \u003c/figcaption\u003e\u003c/figure\u003e","saveContent":"\u003cfigure class=\"wp-block-image size-large\"\u003e\u003cimg src=\"https://content.sfstandard.com/wp-content/uploads/2024/06/20240328_allthingsconsumed-me-19.jpg?w=2500\" alt=\"A man holding a wine bottle explains something to three seated men in a modern restaurant, with food and drinks on the table and greenery in the foreground.\" class=\"wp-image-285659\"/\u003e\u003cfigcaption class=\"wp-element-caption\"\u003ePatrons dine at the Anchovy Bar in the Fillmore in San Francisco on Monday, March 25, 2024. \u003c/figcaption\u003e\u003c/figure\u003e","order":4,"get_parent":{},"attributes":{"url":"https://content.sfstandard.com/wp-content/uploads/2024/06/20240328_allthingsconsumed-me-19.jpg?w=2500","alt":"A man holding a wine bottle explains something to three seated men in a modern restaurant, with food and drinks on the table and greenery in the foreground.","caption":"Patrons dine at the Anchovy Bar in the Fillmore in San Francisco on Monday, March 25, 2024.","title":"","href":"","rel":"","linkClass":"","linkTarget":"","sfsBlockId":"763a39d4-bc86-43ee-b3f1-96bb1666c12c","anchor":"","credit":"Morgan Ellis/The Standard","disableExpansion":false,"orientationOnMobile":"original","id":285659,"sizeSlug":"large","linkDestination":"none","description":"Patrons dine at the Anchovy Bar in the Fillmore in San Francisco on Monday, March 25, 2024. A highlight on their menu is the Anchovy Bar tower for two, a two-tier platter featuring a variety of seafood components, including a half dozen fresh oysters, bay shrimp aguachile, scallop tartare and a squid conserva.","width":3000,"height":2001,"content":null},"attributesType":[],"dynamicContent":"\n\u003cfigure class=\"wp-block-image size-large\"\u003e\u003cimg src=\"https://content.sfstandard.com/wp-content/uploads/2024/06/20240328_allthingsconsumed-me-19.jpg?w=2500\" alt=\"A man holding a wine bottle explains something to three seated men in a modern restaurant, with food and drinks on the table and greenery in the foreground.\" class=\"wp-image-285659\"/\u003e\u003cfigcaption class=\"wp-element-caption\"\u003ePatrons dine at the Anchovy Bar in the Fillmore in San Francisco on Monday, March 25, 2024. \u003c/figcaption\u003e\u003c/figure\u003e\n"},{"innerBlocks":[],"name":"core/paragraph","postId":285407,"blockType":[],"originalContent":"\u003cp\u003eIf that comes to pass, workers like Madayag fear that higher prices will deter diners and cause restaurants to reduce their hours. If that happens, Madayag says he’d have to pick up a second job to support his family. That’s if he can find one.\u0026nbsp;\u003c/p\u003e","saveContent":"\u003cp\u003eIf that comes to pass, workers like Madayag fear that higher prices will deter diners and cause restaurants to reduce their hours. If that happens, Madayag says he’d have to pick up a second job to support his family. That’s if he can find one.\u0026nbsp;\u003c/p\u003e","order":5,"get_parent":{},"attributes":{"content":"If that comes to pass, workers like Madayag fear that higher prices will deter diners and cause restaurants to reduce their hours. If that happens, Madayag says he’d have to pick up a second job to support his family. That’s if he can find one.\u0026nbsp;","dropCap":false,"sfsBlockId":"7e73e320-5068-4bf5-b919-7ceece907a48","anchor":"","links":[]},"attributesType":[],"dynamicContent":null},{"innerBlocks":[],"name":"core/paragraph","postId":285407,"blockType":[],"originalContent":"\u003cp\u003e“It's not easy finding a second job, especially in this industry, given \u003ca href=\"https://www.sfchronicle.com/food/restaurants/article/sales-revenue-san-francisco-18659409.php\"\u003eit's slowing down\u003c/a\u003e,” he said.\u003c/p\u003e","saveContent":"\u003cp\u003e“It\u0026#8217;s not easy finding a second job, especially in this industry, given \u003ca href=\"https://www.sfchronicle.com/food/restaurants/article/sales-revenue-san-francisco-18659409.php\"\u003eit\u0026#8217;s slowing down\u003c/a\u003e,” he said.\u003c/p\u003e","order":6,"get_parent":{},"attributes":{"content":"“It\u0026#8217;s not easy finding a second job, especially in this industry, given \u003ca href=\"https://www.sfchronicle.com/food/restaurants/article/sales-revenue-san-francisco-18659409.php\"\u003eit\u0026#8217;s slowing down\u003c/a\u003e,” he said.","dropCap":false,"sfsBlockId":"f0f87ee8-f073-4a39-97d4-7e4a770f066d","anchor":"","links":[]},"attributesType":[],"dynamicContent":null},{"innerBlocks":[],"name":"core/paragraph","postId":285407,"blockType":[],"originalContent":"\u003cp\u003eSB 478 doesn’t just apply to restaurants; it also requires airlines, hotels and rental car companies to provide all-in pricing. But the change will be especially disruptive for San Francisco restaurants because of a 16-year-old city ordinance requiring most employers to \u003ca href=\"https://www.sf.gov/information/section-overview-hcso-administrative-guidance\"\u003eset aside funds for workers’ health insurance\u003c/a\u003e.\u0026nbsp;\u003c/p\u003e","saveContent":"\u003cp\u003eSB 478 doesn’t just apply to restaurants; it also requires airlines, hotels and rental car companies to provide all-in pricing. But the change will be especially disruptive for San Francisco restaurants because of a 16-year-old city ordinance requiring most employers to \u003ca href=\"https://www.sf.gov/information/section-overview-hcso-administrative-guidance\"\u003eset aside funds for workers’ health insurance\u003c/a\u003e.\u0026nbsp;\u003c/p\u003e","order":7,"get_parent":{},"attributes":{"content":"SB 478 doesn’t just apply to restaurants; it also requires airlines, hotels and rental car companies to provide all-in pricing. But the change will be especially disruptive for San Francisco restaurants because of a 16-year-old city ordinance requiring most employers to \u003ca href=\"https://www.sf.gov/information/section-overview-hcso-administrative-guidance\"\u003eset aside funds for workers’ health insurance\u003c/a\u003e.\u0026nbsp;","dropCap":false,"sfsBlockId":"3e3a7096-1256-47d8-86d3-4e1a9814eed4","anchor":"","links":[]},"attributesType":[],"dynamicContent":null},{"innerBlocks":[],"name":"core/paragraph","postId":285407,"blockType":[],"originalContent":"\u003cp\u003eThe majority of San Francisco restaurants accomplish this by implementing a \u003ca href=\"https://www.sf.gov/information/section-k-health-surcharges-hcso-administrative-guidance\"\u003ehealthcare surcharge\u003c/a\u003e, typically 5-8%. Diners, however, have consistently expressed \u003ca href=\"https://www.sfchronicle.com/restaurants/article/SF-restaurant-bill-surcharges-still-give-some-13254864.php\"\u003efrustration\u003c/a\u003e over their use. It’s not clear that the money really helps workers, critics say, pointing out that dozens of restaurants have been accused of \u003ca href=\"https://www.sfchronicle.com/restaurants/article/Restaurateurs-settle-over-health-surcharge-4499854.php\"\u003emisusing funds\u003c/a\u003e that were supposed to go to worker healthcare.\u0026nbsp;\u003c/p\u003e","saveContent":"\u003cp\u003eThe majority of San Francisco restaurants accomplish this by implementing a \u003ca href=\"https://www.sf.gov/information/section-k-health-surcharges-hcso-administrative-guidance\"\u003ehealthcare surcharge\u003c/a\u003e, typically 5-8%. Diners, however, have consistently expressed \u003ca href=\"https://www.sfchronicle.com/restaurants/article/SF-restaurant-bill-surcharges-still-give-some-13254864.php\"\u003efrustration\u003c/a\u003e over their use. It’s not clear that the money really helps workers, critics say, pointing out that dozens of restaurants have been accused of \u003ca href=\"https://www.sfchronicle.com/restaurants/article/Restaurateurs-settle-over-health-surcharge-4499854.php\"\u003emisusing funds\u003c/a\u003e that were supposed to go to worker healthcare.\u0026nbsp;\u003c/p\u003e","order":8,"get_parent":{},"attributes":{"content":"The majority of San Francisco restaurants accomplish this by implementing a \u003ca href=\"https://www.sf.gov/information/section-k-health-surcharges-hcso-administrative-guidance\"\u003ehealthcare surcharge\u003c/a\u003e, typically 5-8%. Diners, however, have consistently expressed \u003ca href=\"https://www.sfchronicle.com/restaurants/article/SF-restaurant-bill-surcharges-still-give-some-13254864.php\"\u003efrustration\u003c/a\u003e over their use. It’s not clear that the money really helps workers, critics say, pointing out that dozens of restaurants have been accused of \u003ca href=\"https://www.sfchronicle.com/restaurants/article/Restaurateurs-settle-over-health-surcharge-4499854.php\"\u003emisusing funds\u003c/a\u003e that were supposed to go to worker healthcare.\u0026nbsp;","dropCap":false,"sfsBlockId":"c7e7b214-a7df-4cc2-bf2b-55ef427f0024","anchor":"","links":[]},"attributesType":[],"dynamicContent":null},{"innerBlocks":[],"name":"core/paragraph","postId":285407,"blockType":[],"originalContent":"\u003cp\u003eKelsey Bigelow, general manager at \u003ca href=\"https://www.mestizasf.com/\"\u003eMestiza\u003c/a\u003e in SoMa, said she benefits from the 5% surcharge the restaurant applies to diners’ bills. The money generated from the surcharge goes into one of Mesitza’s general ledger accounts and then gets dispersed monthly to workers, including herself.\u0026nbsp;\u003c/p\u003e","saveContent":"\u003cp\u003eKelsey Bigelow, general manager at \u003ca href=\"https://www.mestizasf.com/\"\u003eMestiza\u003c/a\u003e in SoMa, said she benefits from the 5% surcharge the restaurant applies to diners’ bills. The money generated from the surcharge goes into one of Mesitza’s general ledger accounts and then gets dispersed monthly to workers, including herself.\u0026nbsp;\u003c/p\u003e","order":9,"get_parent":{},"attributes":{"content":"Kelsey Bigelow, general manager at \u003ca href=\"https://www.mestizasf.com/\"\u003eMestiza\u003c/a\u003e in SoMa, said she benefits from the 5% surcharge the restaurant applies to diners’ bills. The money generated from the surcharge goes into one of Mesitza’s general ledger accounts and then gets dispersed monthly to workers, including herself.\u0026nbsp;","dropCap":false,"sfsBlockId":"18928b77-47a1-4d5e-87fc-4f4168cd7a1e","anchor":"","links":[]},"attributesType":[],"dynamicContent":null},{"innerBlocks":[],"name":"core/paragraph","postId":285407,"blockType":[],"originalContent":"\u003cp\u003eRight now, Mestiza covers about 75% of the $700 she pays monthly for health insurance. But Bigelow said the revenue generated by the restaurant’s health care surcharge doesn’t fully cover what Mestiza pays out every month to workers. If the restaurant drops the fee and raises menu prices instead, she worries that the gap could get even larger. Combined with the \u003ca href=\"https://restaurant.org/research-and-media/research/economists-notebook/economic-indicators/food-costs/\"\u003erising cost of ingredients\u003c/a\u003e and \u003ca href=\"https://www.sf.gov/information/minimum-wage-ordinance\"\u003eminimum wage increase\u003c/a\u003e also set to go into effect on July 1, Bigelow worries about her employer’s continued ability to provide livable wages and benefits for workers.\u0026nbsp;\u003c/p\u003e","saveContent":"\u003cp\u003eRight now, Mestiza covers about 75% of the $700 she pays monthly for health insurance. But Bigelow said the revenue generated by the restaurant’s health care surcharge doesn’t fully cover what Mestiza pays out every month to workers. If the restaurant drops the fee and raises menu prices instead, she worries that the gap could get even larger. Combined with the \u003ca href=\"https://restaurant.org/research-and-media/research/economists-notebook/economic-indicators/food-costs/\"\u003erising cost of ingredients\u003c/a\u003e and \u003ca href=\"https://www.sf.gov/information/minimum-wage-ordinance\"\u003eminimum wage increase\u003c/a\u003e also set to go into effect on July 1, Bigelow worries about her employer’s continued ability to provide livable wages and benefits for workers.\u0026nbsp;\u003c/p\u003e","order":10,"get_parent":{},"attributes":{"content":"Right now, Mestiza covers about 75% of the $700 she pays monthly for health insurance. But Bigelow said the revenue generated by the restaurant’s health care surcharge doesn’t fully cover what Mestiza pays out every month to workers. If the restaurant drops the fee and raises menu prices instead, she worries that the gap could get even larger. Combined with the \u003ca href=\"https://restaurant.org/research-and-media/research/economists-notebook/economic-indicators/food-costs/\"\u003erising cost of ingredients\u003c/a\u003e and \u003ca href=\"https://www.sf.gov/information/minimum-wage-ordinance\"\u003eminimum wage increase\u003c/a\u003e also set to go into effect on July 1, Bigelow worries about her employer’s continued ability to provide livable wages and benefits for workers.\u0026nbsp;","dropCap":false,"sfsBlockId":"19a257cf-37db-4ca7-a0b7-8da308eeb0da","anchor":"","links":[]},"attributesType":[],"dynamicContent":null},{"innerBlocks":[],"name":"core/paragraph","postId":285407,"blockType":[],"originalContent":"\u003cp\u003e“[The surcharge] is definitely a helping hand for the employer, but also for the employee,” Bigelow said. “I know some people think that it just lines the owner's pockets. But I feel like, if it's clearly stated, I have no problem helping any of the places that I patronize knowing that it actually does transfer over into the employees benefit.”\u0026nbsp;\u003c/p\u003e","saveContent":"\u003cp\u003e“[The surcharge] is definitely a helping hand for the employer, but also for the employee,” Bigelow said. “I know some people think that it just lines the owner\u0026#8217;s pockets. But I feel like, if it\u0026#8217;s clearly stated, I have no problem helping any of the places that I patronize knowing that it actually does transfer over into the employees benefit.”\u0026nbsp;\u003c/p\u003e","order":11,"get_parent":{},"attributes":{"content":"“[The surcharge] is definitely a helping hand for the employer, but also for the employee,” Bigelow said. “I know some people think that it just lines the owner\u0026#8217;s pockets. But I feel like, if it\u0026#8217;s clearly stated, I have no problem helping any of the places that I patronize knowing that it actually does transfer over into the employees benefit.”\u0026nbsp;","dropCap":false,"sfsBlockId":"7319e139-eb0f-4b57-9f52-52b98aff1e3c","anchor":"","links":[]},"attributesType":[],"dynamicContent":null},{"innerBlocks":[],"name":"core/paragraph","postId":285407,"blockType":[],"originalContent":"\u003cp\u003eSB 478 has also reignited the heated restaurant industry debate over tips, or rather, efforts to eradicate them. For the past decade or so, restaurants big and small—most famously, Danny Meyer’s New York-based \u003ca href=\"https://www.ushg.com/\"\u003eUnion Square Hospitality Group\u003c/a\u003e—have tried to \u003ca href=\"https://www.nytimes.com/2015/10/15/dining/danny-meyer-restaurants-no-tips.html\"\u003eeliminate tipping\u003c/a\u003e, which has been shown to exacerbate social inequities in the industry. But switching to a no-tipping model has proven \u003ca href=\"https://www.nytimes.com/2020/07/20/dining/danny-meyer-no-tips.html\"\u003edifficult\u003c/a\u003e for even the most successful restaurateurs, leaving most restaurant workers' wages at the discretion of diners.\u0026nbsp;\u003c/p\u003e","saveContent":"\u003cp\u003eSB 478 has also reignited the heated restaurant industry debate over tips, or rather, efforts to eradicate them. For the past decade or so, restaurants big and small—most famously, Danny Meyer’s New York-based \u003ca href=\"https://www.ushg.com/\"\u003eUnion Square Hospitality Group\u003c/a\u003e—have tried to \u003ca href=\"https://www.nytimes.com/2015/10/15/dining/danny-meyer-restaurants-no-tips.html\"\u003eeliminate tipping\u003c/a\u003e, which has been shown to exacerbate social inequities in the industry. But switching to a no-tipping model has proven \u003ca href=\"https://www.nytimes.com/2020/07/20/dining/danny-meyer-no-tips.html\"\u003edifficult\u003c/a\u003e for even the most successful restaurateurs, leaving most restaurant workers\u0026#8217; wages at the discretion of diners.\u0026nbsp;\u003c/p\u003e","order":12,"get_parent":{},"attributes":{"content":"SB 478 has also reignited the heated restaurant industry debate over tips, or rather, efforts to eradicate them. For the past decade or so, restaurants big and small—most famously, Danny Meyer’s New York-based \u003ca href=\"https://www.ushg.com/\"\u003eUnion Square Hospitality Group\u003c/a\u003e—have tried to \u003ca href=\"https://www.nytimes.com/2015/10/15/dining/danny-meyer-restaurants-no-tips.html\"\u003eeliminate tipping\u003c/a\u003e, which has been shown to exacerbate social inequities in the industry. But switching to a no-tipping model has proven \u003ca href=\"https://www.nytimes.com/2020/07/20/dining/danny-meyer-no-tips.html\"\u003edifficult\u003c/a\u003e for even the most successful restaurateurs, leaving most restaurant workers\u0026#8217; wages at the discretion of diners.\u0026nbsp;","dropCap":false,"sfsBlockId":"b5b68887-cf63-47db-97a7-5be6ff4b1002","anchor":"","links":[]},"attributesType":[],"dynamicContent":null},{"innerBlocks":[],"name":"core/paragraph","postId":285407,"blockType":[],"originalContent":"\u003cp\u003e\u003c/p\u003e","saveContent":"\u003cp\u003e\u003c/p\u003e","order":13,"get_parent":{},"attributes":{"content":"","dropCap":false,"sfsBlockId":"9cbfc36d-fe7b-4ff0-bcd4-be422cea0a10","anchor":"","links":[]},"attributesType":[],"dynamicContent":null},{"innerBlocks":[],"name":"core/heading","postId":285407,"blockType":[],"originalContent":"\u003ch2 class=\"wp-block-heading\" id=\"h-i-m-not-stupid-i-can-work-in-tech\"\u003e'I’m not stupid, I can work in tech'\u003c/h2\u003e","saveContent":"\u003ch2 class=\"wp-block-heading\" id=\"h-i-m-not-stupid-i-can-work-in-tech\"\u003e\u0026#8216;I’m not stupid, I can work in tech\u0026#8217;\u003c/h2\u003e","order":14,"get_parent":{},"attributes":{"content":"\u0026#8216;I’m not stupid, I can work in tech\u0026#8217;","level":2,"sfsBlockId":"ecdfd472-92ad-4373-a848-9ff79119dd09","anchor":"h-i-m-not-stupid-i-can-work-in-tech"},"attributesType":[],"dynamicContent":"\n\u003ch2 class=\"wp-block-heading\" id=\"h-i-m-not-stupid-i-can-work-in-tech\"\u003e\u0026#8216;I’m not stupid, I can work in tech\u0026#8217;\u003c/h2\u003e\n"},{"innerBlocks":[],"name":"core/paragraph","postId":285407,"blockType":[],"originalContent":"\u003cp\u003eOne major sticking point for workers: SB 478 would make it illegal for restaurants to put an automatic tip on bills for large parties, a practice common at restaurants of all sizes. Kelsey Atchinson, who’s been in the service industry for 15 years and is currently a server at \u003ca href=\"http://www.bluewhalerestaurant-lounge.com/\"\u003eBlue Whale\u003c/a\u003e restaurant in the Marina, wasn’t aware the law would impact so-called “automatic gratuities” for large groups. The news gave her pause, but she said she believes customers understand that a 20% tip is standard practice. “It could definitely affect things, but probably just a small percentage,” she said.\u0026nbsp;\u003c/p\u003e","saveContent":"\u003cp\u003eOne major sticking point for workers: SB 478 would make it illegal for restaurants to put an automatic tip on bills for large parties, a practice common at restaurants of all sizes. Kelsey Atchinson, who’s been in the service industry for 15 years and is currently a server at \u003ca href=\"http://www.bluewhalerestaurant-lounge.com/\"\u003eBlue Whale\u003c/a\u003e restaurant in the Marina, wasn’t aware the law would impact so-called “automatic gratuities” for large groups. The news gave her pause, but she said she believes customers understand that a 20% tip is standard practice. “It could definitely affect things, but probably just a small percentage,” she said.\u0026nbsp;\u003c/p\u003e","order":15,"get_parent":{},"attributes":{"content":"One major sticking point for workers: SB 478 would make it illegal for restaurants to put an automatic tip on bills for large parties, a practice common at restaurants of all sizes. Kelsey Atchinson, who’s been in the service industry for 15 years and is currently a server at \u003ca href=\"http://www.bluewhalerestaurant-lounge.com/\"\u003eBlue Whale\u003c/a\u003e restaurant in the Marina, wasn’t aware the law would impact so-called “automatic gratuities” for large groups. The news gave her pause, but she said she believes customers understand that a 20% tip is standard practice. “It could definitely affect things, but probably just a small percentage,” she said.\u0026nbsp;","dropCap":false,"sfsBlockId":"b06cfae3-fcb2-4c23-9554-1004d68df6ee","anchor":"","links":[]},"attributesType":[],"dynamicContent":null},{"innerBlocks":[],"name":"core/image","postId":285407,"blockType":[],"originalContent":"\u003cfigure class=\"wp-block-image size-large\"\u003e\u003cimg src=\"https://content.sfstandard.com/wp-content/uploads/2024/06/pfg_zunicafe_100523-0022.jpg?w=2500\" alt=\"People dine at white-clothed tables in a cozy restaurant with large windows. Bread and dishes fill a counter by a brick oven, where staff serve and prepare food.\" class=\"wp-image-285660\"/\u003e\u003cfigcaption class=\"wp-element-caption\"\u003eThe wood-fired oven and bread station at Zuni Cafe in San Francisco, Wednesday, October 5, 2023.\u003c/figcaption\u003e\u003c/figure\u003e","saveContent":"\u003cfigure class=\"wp-block-image size-large\"\u003e\u003cimg src=\"https://content.sfstandard.com/wp-content/uploads/2024/06/pfg_zunicafe_100523-0022.jpg?w=2500\" alt=\"People dine at white-clothed tables in a cozy restaurant with large windows. Bread and dishes fill a counter by a brick oven, where staff serve and prepare food.\" class=\"wp-image-285660\"/\u003e\u003cfigcaption class=\"wp-element-caption\"\u003eThe wood-fired oven and bread station at Zuni Cafe in San Francisco, Wednesday, October 5, 2023.\u003c/figcaption\u003e\u003c/figure\u003e","order":16,"get_parent":{},"attributes":{"url":"https://content.sfstandard.com/wp-content/uploads/2024/06/pfg_zunicafe_100523-0022.jpg?w=2500","alt":"People dine at white-clothed tables in a cozy restaurant with large windows. Bread and dishes fill a counter by a brick oven, where staff serve and prepare food.","caption":"The wood-fired oven and bread station at Zuni Cafe in San Francisco, Wednesday, October 5, 2023.","title":"","href":"","rel":"","linkClass":"","linkTarget":"","sfsBlockId":"828e9af9-9de1-40d6-a6f9-42661e4c154b","anchor":"","credit":"Jason Henry for The Standard","disableExpansion":false,"orientationOnMobile":"original","id":285660,"sizeSlug":"large","linkDestination":"none","description":"The wood-fired oven and bread station at Zuni Caf in San Francisco, Wednesday, October 5, 2023.","width":3000,"height":2000,"content":null},"attributesType":[],"dynamicContent":"\n\u003cfigure class=\"wp-block-image size-large\"\u003e\u003cimg src=\"https://content.sfstandard.com/wp-content/uploads/2024/06/pfg_zunicafe_100523-0022.jpg?w=2500\" alt=\"People dine at white-clothed tables in a cozy restaurant with large windows. Bread and dishes fill a counter by a brick oven, where staff serve and prepare food.\" class=\"wp-image-285660\"/\u003e\u003cfigcaption class=\"wp-element-caption\"\u003eThe wood-fired oven and bread station at Zuni Cafe in San Francisco, Wednesday, October 5, 2023.\u003c/figcaption\u003e\u003c/figure\u003e\n"},{"innerBlocks":[],"name":"core/paragraph","postId":285407,"blockType":[],"originalContent":"\u003cp\u003eIn fact, she says getting rid of surcharges in favor of a traditional tipping model could result in better service. “I think the upside is that you're going to have to provide better service if you want to get tipped for the work,” she said. “You're not going to automatically get that tip. You're going to get what you worked for.”\u0026nbsp;\u0026nbsp;\u003c/p\u003e","saveContent":"\u003cp\u003eIn fact, she says getting rid of surcharges in favor of a traditional tipping model could result in better service. “I think the upside is that you\u0026#8217;re going to have to provide better service if you want to get tipped for the work,” she said. “You\u0026#8217;re not going to automatically get that tip. You\u0026#8217;re going to get what you worked for.”\u0026nbsp;\u0026nbsp;\u003c/p\u003e","order":17,"get_parent":{},"attributes":{"content":"In fact, she says getting rid of surcharges in favor of a traditional tipping model could result in better service. “I think the upside is that you\u0026#8217;re going to have to provide better service if you want to get tipped for the work,” she said. “You\u0026#8217;re not going to automatically get that tip. You\u0026#8217;re going to get what you worked for.”\u0026nbsp;\u0026nbsp;","dropCap":false,"sfsBlockId":"785ade0b-4dd1-4f45-b577-a58e70b780d2","anchor":"","links":[]},"attributesType":[],"dynamicContent":null},{"innerBlocks":[],"name":"core/paragraph","postId":285407,"blockType":[],"originalContent":"\u003cp\u003eWithin the industry—and even within the same restaurant—there’s no broad agreement on how SB 478 will impact tips. Melissa Lang, who also works at Blue Whale restaurant in the Marina, came back to the hospitality industry several years ago after working in tech prior to the pandemic. Lang, who handles private event bookings for the restaurant, said eliminating automatic gratuities for large groups could lead to worse service if workers aren’t guaranteed fair compensation. “I can't even imagine how bad service could be,” she said. “That's a daunting thought.”\u003c/p\u003e","saveContent":"\u003cp\u003eWithin the industry—and even within the same restaurant—there’s no broad agreement on how SB 478 will impact tips. Melissa Lang, who also works at Blue Whale restaurant in the Marina, came back to the hospitality industry several years ago after working in tech prior to the pandemic. Lang, who handles private event bookings for the restaurant, said eliminating automatic gratuities for large groups could lead to worse service if workers aren’t guaranteed fair compensation. “I can\u0026#8217;t even imagine how bad service could be,” she said. “That\u0026#8217;s a daunting thought.”\u003c/p\u003e","order":18,"get_parent":{},"attributes":{"content":"Within the industry—and even within the same restaurant—there’s no broad agreement on how SB 478 will impact tips. Melissa Lang, who also works at Blue Whale restaurant in the Marina, came back to the hospitality industry several years ago after working in tech prior to the pandemic. Lang, who handles private event bookings for the restaurant, said eliminating automatic gratuities for large groups could lead to worse service if workers aren’t guaranteed fair compensation. “I can\u0026#8217;t even imagine how bad service could be,” she said. “That\u0026#8217;s a daunting thought.”","dropCap":false,"sfsBlockId":"8557e7da-ed83-4a77-80a2-5f5788f71e7d","anchor":"","links":[]},"attributesType":[],"dynamicContent":null},{"innerBlocks":[],"name":"core/paragraph","postId":285407,"blockType":[],"originalContent":"\u003cp\u003eShe said she’s watched many of her colleagues either leave San Francisco for more affordable markets or abandon the industry in favor of work with more consistent pay. With the additional uncertainty brought on by SB 478, she predicts even more people will choose to find other work.\u0026nbsp;\u003c/p\u003e","saveContent":"\u003cp\u003eShe said she’s watched many of her colleagues either leave San Francisco for more affordable markets or abandon the industry in favor of work with more consistent pay. With the additional uncertainty brought on by SB 478, she predicts even more people will choose to find other work.\u0026nbsp;\u003c/p\u003e","order":19,"get_parent":{},"attributes":{"content":"She said she’s watched many of her colleagues either leave San Francisco for more affordable markets or abandon the industry in favor of work with more consistent pay. With the additional uncertainty brought on by SB 478, she predicts even more people will choose to find other work.\u0026nbsp;","dropCap":false,"sfsBlockId":"44b7bcdc-6e79-4046-ac82-c5e3e5fd402d","anchor":"","links":[]},"attributesType":[],"dynamicContent":null},{"innerBlocks":[],"name":"core/paragraph","postId":285407,"blockType":[],"originalContent":"\u003cp\u003e“It’s pretty tough to look at that and not say, I’m not stupid, I can work in tech—or do almost anything else other than work in the service industry.”\u003c/p\u003e","saveContent":"\u003cp\u003e“It’s pretty tough to look at that and not say, I’m not stupid, I can work in tech—or do almost anything else other than work in the service industry.”\u003c/p\u003e","order":20,"get_parent":{},"attributes":{"content":"“It’s pretty tough to look at that and not say, I’m not stupid, I can work in tech—or do almost anything else other than work in the service industry.”","dropCap":false,"sfsBlockId":"a3e99c34-7159-45eb-9a58-d18607dc7123","anchor":"","links":[]},"attributesType":[],"dynamicContent":null},{"innerBlocks":[],"name":"core/paragraph","postId":285407,"blockType":[],"originalContent":"\u003cp\u003eFausto Galicia has worked in San Francisco restaurants for 30 years, starting as a dishwasher at the legendary Postrio and working his way up the ranks to lead sommelier at Pabu. The restaurant charges a 4% surcharge to meet city healthcare requirements but leaves tips entirely up to guests.\u0026nbsp;\u003c/p\u003e","saveContent":"\u003cp\u003eFausto Galicia has worked in San Francisco restaurants for 30 years, starting as a dishwasher at the legendary Postrio and working his way up the ranks to lead sommelier at Pabu. The restaurant charges a 4% surcharge to meet city healthcare requirements but leaves tips entirely up to guests.\u0026nbsp;\u003c/p\u003e","order":21,"get_parent":{},"attributes":{"content":"Fausto Galicia has worked in San Francisco restaurants for 30 years, starting as a dishwasher at the legendary Postrio and working his way up the ranks to lead sommelier at Pabu. The restaurant charges a 4% surcharge to meet city healthcare requirements but leaves tips entirely up to guests.\u0026nbsp;","dropCap":false,"sfsBlockId":"b984dce8-5c84-4649-984e-c9ec7a1b4fed","anchor":"","links":[]},"attributesType":[],"dynamicContent":null},{"innerBlocks":[],"name":"core/paragraph","postId":285407,"blockType":[],"originalContent":"\u003cp\u003eGalicia worries confusion around restaurant pricing after July 1 could result in less money in his pocket. “I tried to read the articles, and it's not really clear for me. So you can imagine for customers, it's the same thing,” Galicia said. That confusion, he said, could only lead to one result: less money in his pocket. “Basically, I'd need to change to a different job.”\u0026nbsp;\u003c/p\u003e","saveContent":"\u003cp\u003eGalicia worries confusion around restaurant pricing after July 1 could result in less money in his pocket. “I tried to read the articles, and it\u0026#8217;s not really clear for me. So you can imagine for customers, it\u0026#8217;s the same thing,” Galicia said. That confusion, he said, could only lead to one result: less money in his pocket. “Basically, I\u0026#8217;d need to change to a different job.”\u0026nbsp;\u003c/p\u003e","order":22,"get_parent":{},"attributes":{"content":"Galicia worries confusion around restaurant pricing after July 1 could result in less money in his pocket. “I tried to read the articles, and it\u0026#8217;s not really clear for me. So you can imagine for customers, it\u0026#8217;s the same thing,” Galicia said. That confusion, he said, could only lead to one result: less money in his pocket. “Basically, I\u0026#8217;d need to change to a different job.”\u0026nbsp;","dropCap":false,"sfsBlockId":"7256d2f5-8284-4e69-9eb8-ab54f6fa324c","anchor":"","links":[]},"attributesType":[],"dynamicContent":null}],"content":"\n\u003cp\u003eIn an industry known for its sky-high turnover rates, Noel Madayag is an outlier. He’s been a cook at \u003ca href=\"https://www.cassavasf.com/\"\u003eCassava\u003c/a\u003e since the business opened 12 years ago. He likes working at the upscale restaurant, which has made \u003ca href=\"https://www.bizjournals.com/sanfrancisco/news/2023/03/03/yuka-ioroi-cassava-north-beach-restaurant.html\"\u003eheadlines\u003c/a\u003e for paying all staff at least $20 an hour and providing full health benefits including a 401k with a 5% employer match.\u0026nbsp;\u003c/p\u003e\n\n\n\n\u003cp\u003eBut as of last week, Madayag is preparing for a job hunt. “I have been working on my resume,” he said. “I need something to supplement [my income].”\u0026nbsp;\u003c/p\u003e\n\n\n\n\u003cp\u003eMadayag is just one of many San Francisco restaurant industry workers bracing for the impact of \u003ca href=\"https://leginfo.legislature.ca.gov/faces/billNavClient.xhtml?bill_id=202320240SB478\"\u003eSB 478\u003c/a\u003e, a California law aimed at eliminating \u003ca href=\"https://www.nytimes.com/interactive/2024/02/23/business/what-is-drip-pricing.html\"\u003edrip pricing\u003c/a\u003e and junk fees. It’s set to go into effect on July 1 and would require restaurants to \u003ca href=\"https://sfstandard.com/2024/05/02/restaurant-san-francisco-surcharges-service-fees-rob-bonta/\"\u003estop using mandatory surcharges\u003c/a\u003e, including the 20% service fee that allows Cassava to provide its staff with health insurance and retirement funds.\u0026nbsp;\u003c/p\u003e\n\n\n\n\u003cp\u003eResponding to a deafening roar from restaurateurs who fear the unintended consequences of the law\u0026#8217;s passage, state legislators are hustling to pass a \u003ca href=\"https://sfstandard.com/2024/06/06/san-francisco-haney-wiener-sb1528/\"\u003ecarveout for restaurants\u003c/a\u003e this week. The revised bill has already received assembly approval, but if it fails to emerge out of the state senate or get signed into law by Governor Newsom next week, most restaurant owners say they\u0026#8217;ll be forced to raise menu prices.\u0026nbsp;\u003c/p\u003e\n\n\n\n\u003cfigure class=\"wp-block-image size-large\"\u003e\u003cimg loading=\"lazy\" decoding=\"async\" width=\"3000\" height=\"2001\" src=\"https://content.sfstandard.com/wp-content/uploads/2024/06/20240328_allthingsconsumed-me-19.jpg?w=2500\" alt=\"A man holding a wine bottle explains something to three seated men in a modern restaurant, with food and drinks on the table and greenery in the foreground.\" class=\"wp-image-285659\" srcset=\"https://content.sfstandard.com/wp-content/uploads/2024/06/20240328_allthingsconsumed-me-19.jpg 3000w, https://content.sfstandard.com/wp-content/uploads/2024/06/20240328_allthingsconsumed-me-19.jpg?resize=300,200 300w, https://content.sfstandard.com/wp-content/uploads/2024/06/20240328_allthingsconsumed-me-19.jpg?resize=768,512 768w, https://content.sfstandard.com/wp-content/uploads/2024/06/20240328_allthingsconsumed-me-19.jpg?resize=2500,1668 2500w, https://content.sfstandard.com/wp-content/uploads/2024/06/20240328_allthingsconsumed-me-19.jpg?resize=1536,1025 1536w, https://content.sfstandard.com/wp-content/uploads/2024/06/20240328_allthingsconsumed-me-19.jpg?resize=2048,1366 2048w\" sizes=\"(max-width: 3000px) 100vw, 3000px\" /\u003e\u003cfigcaption class=\"wp-element-caption\"\u003ePatrons dine at the Anchovy Bar in the Fillmore in San Francisco on Monday, March 25, 2024. \u003c/figcaption\u003e\u003c/figure\u003e\n\n\n\n\u003cp\u003eIf that comes to pass, workers like Madayag fear that higher prices will deter diners and cause restaurants to reduce their hours. If that happens, Madayag says he’d have to pick up a second job to support his family. That’s if he can find one.\u0026nbsp;\u003c/p\u003e\n\n\n\n\u003cp\u003e“It\u0026#8217;s not easy finding a second job, especially in this industry, given \u003ca href=\"https://www.sfchronicle.com/food/restaurants/article/sales-revenue-san-francisco-18659409.php\"\u003eit\u0026#8217;s slowing down\u003c/a\u003e,” he said.\u003c/p\u003e\n\n\n\n\u003cp\u003eSB 478 doesn’t just apply to restaurants; it also requires airlines, hotels and rental car companies to provide all-in pricing. But the change will be especially disruptive for San Francisco restaurants because of a 16-year-old city ordinance requiring most employers to \u003ca href=\"https://www.sf.gov/information/section-overview-hcso-administrative-guidance\"\u003eset aside funds for workers’ health insurance\u003c/a\u003e.\u0026nbsp;\u003c/p\u003e\n\n\n\n\u003cp\u003eThe majority of San Francisco restaurants accomplish this by implementing a \u003ca href=\"https://www.sf.gov/information/section-k-health-surcharges-hcso-administrative-guidance\"\u003ehealthcare surcharge\u003c/a\u003e, typically 5-8%. Diners, however, have consistently expressed \u003ca href=\"https://www.sfchronicle.com/restaurants/article/SF-restaurant-bill-surcharges-still-give-some-13254864.php\"\u003efrustration\u003c/a\u003e over their use. It’s not clear that the money really helps workers, critics say, pointing out that dozens of restaurants have been accused of \u003ca href=\"https://www.sfchronicle.com/restaurants/article/Restaurateurs-settle-over-health-surcharge-4499854.php\"\u003emisusing funds\u003c/a\u003e that were supposed to go to worker healthcare.\u0026nbsp;\u003c/p\u003e\n\n\n\n\u003cp\u003eKelsey Bigelow, general manager at \u003ca href=\"https://www.mestizasf.com/\"\u003eMestiza\u003c/a\u003e in SoMa, said she benefits from the 5% surcharge the restaurant applies to diners’ bills. The money generated from the surcharge goes into one of Mesitza’s general ledger accounts and then gets dispersed monthly to workers, including herself.\u0026nbsp;\u003c/p\u003e\n\n\n\n\u003cp\u003eRight now, Mestiza covers about 75% of the $700 she pays monthly for health insurance. But Bigelow said the revenue generated by the restaurant’s health care surcharge doesn’t fully cover what Mestiza pays out every month to workers. If the restaurant drops the fee and raises menu prices instead, she worries that the gap could get even larger. Combined with the \u003ca href=\"https://restaurant.org/research-and-media/research/economists-notebook/economic-indicators/food-costs/\"\u003erising cost of ingredients\u003c/a\u003e and \u003ca href=\"https://www.sf.gov/information/minimum-wage-ordinance\"\u003eminimum wage increase\u003c/a\u003e also set to go into effect on July 1, Bigelow worries about her employer’s continued ability to provide livable wages and benefits for workers.\u0026nbsp;\u003c/p\u003e\n\n\n\n\u003cp\u003e“[The surcharge] is definitely a helping hand for the employer, but also for the employee,” Bigelow said. “I know some people think that it just lines the owner\u0026#8217;s pockets. But I feel like, if it\u0026#8217;s clearly stated, I have no problem helping any of the places that I patronize knowing that it actually does transfer over into the employees benefit.”\u0026nbsp;\u003c/p\u003e\n\n\n\n\u003cp\u003eSB 478 has also reignited the heated restaurant industry debate over tips, or rather, efforts to eradicate them. For the past decade or so, restaurants big and small—most famously, Danny Meyer’s New York-based \u003ca href=\"https://www.ushg.com/\"\u003eUnion Square Hospitality Group\u003c/a\u003e—have tried to \u003ca href=\"https://www.nytimes.com/2015/10/15/dining/danny-meyer-restaurants-no-tips.html\"\u003eeliminate tipping\u003c/a\u003e, which has been shown to exacerbate social inequities in the industry. But switching to a no-tipping model has proven \u003ca href=\"https://www.nytimes.com/2020/07/20/dining/danny-meyer-no-tips.html\"\u003edifficult\u003c/a\u003e for even the most successful restaurateurs, leaving most restaurant workers\u0026#8217; wages at the discretion of diners.\u0026nbsp;\u003c/p\u003e\n\n\n\n\u003cp\u003e\u003c/p\u003e\n\n\n\n\u003ch2 class=\"wp-block-heading\" id=\"h-i-m-not-stupid-i-can-work-in-tech\"\u003e\u0026#8216;I’m not stupid, I can work in tech\u0026#8217;\u003c/h2\u003e\n\n\n\n\u003cp\u003eOne major sticking point for workers: SB 478 would make it illegal for restaurants to put an automatic tip on bills for large parties, a practice common at restaurants of all sizes. Kelsey Atchinson, who’s been in the service industry for 15 years and is currently a server at \u003ca href=\"http://www.bluewhalerestaurant-lounge.com/\"\u003eBlue Whale\u003c/a\u003e restaurant in the Marina, wasn’t aware the law would impact so-called “automatic gratuities” for large groups. The news gave her pause, but she said she believes customers understand that a 20% tip is standard practice. “It could definitely affect things, but probably just a small percentage,” she said.\u0026nbsp;\u003c/p\u003e\n\n\n\n\u003cfigure class=\"wp-block-image size-large\"\u003e\u003cimg loading=\"lazy\" decoding=\"async\" width=\"3000\" height=\"2000\" src=\"https://content.sfstandard.com/wp-content/uploads/2024/06/pfg_zunicafe_100523-0022.jpg?w=2500\" alt=\"People dine at white-clothed tables in a cozy restaurant with large windows. Bread and dishes fill a counter by a brick oven, where staff serve and prepare food.\" class=\"wp-image-285660\" srcset=\"https://content.sfstandard.com/wp-content/uploads/2024/06/pfg_zunicafe_100523-0022.jpg 3000w, https://content.sfstandard.com/wp-content/uploads/2024/06/pfg_zunicafe_100523-0022.jpg?resize=300,200 300w, https://content.sfstandard.com/wp-content/uploads/2024/06/pfg_zunicafe_100523-0022.jpg?resize=768,512 768w, https://content.sfstandard.com/wp-content/uploads/2024/06/pfg_zunicafe_100523-0022.jpg?resize=2500,1667 2500w, https://content.sfstandard.com/wp-content/uploads/2024/06/pfg_zunicafe_100523-0022.jpg?resize=1536,1024 1536w, https://content.sfstandard.com/wp-content/uploads/2024/06/pfg_zunicafe_100523-0022.jpg?resize=2048,1365 2048w\" sizes=\"(max-width: 3000px) 100vw, 3000px\" /\u003e\u003cfigcaption class=\"wp-element-caption\"\u003eThe wood-fired oven and bread station at Zuni Cafe in San Francisco, Wednesday, October 5, 2023.\u003c/figcaption\u003e\u003c/figure\u003e\n\n\n\n\u003cp\u003eIn fact, she says getting rid of surcharges in favor of a traditional tipping model could result in better service. “I think the upside is that you\u0026#8217;re going to have to provide better service if you want to get tipped for the work,” she said. “You\u0026#8217;re not going to automatically get that tip. You\u0026#8217;re going to get what you worked for.”\u0026nbsp;\u0026nbsp;\u003c/p\u003e\n\n\n\n\u003cp\u003eWithin the industry—and even within the same restaurant—there’s no broad agreement on how SB 478 will impact tips. Melissa Lang, who also works at Blue Whale restaurant in the Marina, came back to the hospitality industry several years ago after working in tech prior to the pandemic. Lang, who handles private event bookings for the restaurant, said eliminating automatic gratuities for large groups could lead to worse service if workers aren’t guaranteed fair compensation. “I can\u0026#8217;t even imagine how bad service could be,” she said. “That\u0026#8217;s a daunting thought.”\u003c/p\u003e\n\n\n\n\u003cp\u003eShe said she’s watched many of her colleagues either leave San Francisco for more affordable markets or abandon the industry in favor of work with more consistent pay. With the additional uncertainty brought on by SB 478, she predicts even more people will choose to find other work.\u0026nbsp;\u003c/p\u003e\n\n\n\n\u003cp\u003e“It’s pretty tough to look at that and not say, I’m not stupid, I can work in tech—or do almost anything else other than work in the service industry.”\u003c/p\u003e\n\n\n\n\u003cp\u003eFausto Galicia has worked in San Francisco restaurants for 30 years, starting as a dishwasher at the legendary Postrio and working his way up the ranks to lead sommelier at Pabu. The restaurant charges a 4% surcharge to meet city healthcare requirements but leaves tips entirely up to guests.\u0026nbsp;\u003c/p\u003e\n\n\n\n\u003cp\u003eGalicia worries confusion around restaurant pricing after July 1 could result in less money in his pocket. “I tried to read the articles, and it\u0026#8217;s not really clear for me. So you can imagine for customers, it\u0026#8217;s the same thing,” Galicia said. That confusion, he said, could only lead to one result: less money in his pocket. “Basically, I\u0026#8217;d need to change to a different job.”\u0026nbsp;\u003c/p\u003e\n","featuredImageOrientationOnMobile":"original","titleImage":{"caption":"","credit":"AI illustration by Clark Miller for The Standard; photo by Unsplash","alt":"An illustration of a large glass tip jar breaking into pieces as people dressed as chefs and servers stand around and watch.","height":1365,"width":2048,"src":"https://content.sfstandard.com/wp-content/uploads/2024/06/featured_20240621-junkfeeban.jpg","focalPoint":null},"articleImage":{"caption":"","credit":"AI illustration by Clark Miller for The Standard; photo by Unsplash","alt":"An illustration of a large glass tip jar breaking into pieces as people dressed as chefs and servers stand around and watch.","height":1365,"width":2048,"src":"https://content.sfstandard.com/wp-content/uploads/2024/06/featured_20240621-junkfeeban.jpg","focalPoint":null},"articleFeaturedVideo":{"hasFeaturedVideo":false,"src":"","poster":""},"focalPoints":{"website":null,"article":null,"facebook":null,"twitter":null,"mobile":null},"postSources":[],"categories":[{"id":5945,"title":"Food \u0026 Drink","url":"/food-drink/"}],"tags":[{"name":"Food","slug":"food","hidden":false},{"name":"Restaurants","slug":"restaurants","hidden":false}],"meta":{"preloadImageSizes":"","preloadImageSrc":"","preloadScript":"","hideImage":false,"primaryCategory":{"id":5945,"title":"Food \u0026 Drink","url":"/food-drink/"},"style":"style1","headlinePlacement":"bottom","isSponsored":false,"sponsoredUrl":null,"sponsoredImage":null,"showLastUpdated":false,"video":"","videoCaption":"","videoCredit":"","additionalCredits":"","dek":"Diners hate them. Restaurant owners say they need them. But in the battle over service fees, it’s servers and cooks on the front lines. ","subLabel":null,"authorShortBio":null,"kicker":"none"},"authorTwitter":"https://twitter.com/lhsaria","disableProfilePage":false,"id":285407,"publishedAt":"2024-06-24T10:00:00","authors":[{"id":281091,"email":"<EMAIL>","url":"https://sfstandard.com/author/lauren-saria/","title":"Lauren Saria","social":[{"network":"twitter","url":"https://twitter.com/lhsaria"},{"network":"instagram","url":"https://www.instagram.com/laurensaria/"},{"network":"linkedin","url":"https://www.linkedin.com/in/lauren-saria/"}],"titleImage":{"node":{"alt":"","src":"https://content.sfstandard.com/wp-content/uploads/2024/06/sfs-staffportrait-laurensaria.jpg"}},"disableProfilePage":false}],"mostPopular":[{"cleanTitle":"The family feud behind bizarre $488K Russian Hill home sale","authors":[{"id":281433,"email":"<EMAIL>","url":"https://sfstandard.com/author/sam-mondros/","title":"Sam Mondros","social":[{"network":"twitter","url":"https://twitter.com/Sam_Mondros"},{"network":"linkedin","url":"https://www.linkedin.com/in/sam-mondros-4335b41b5/"}],"titleImage":{"node":{"alt":"","src":"https://content.sfstandard.com/wp-content/uploads/2024/06/sfs-staffportrait-sammondros.jpg"}},"disableProfilePage":false}],"blocks":[],"categories":[{"id":5947,"title":"Life","url":"/life/"}],"contributors":[],"excerpt":"The feud behind the $488K sale of a classic Russian Hill home.","id":285194,"meta":{},"publishedAt":"2024-06-21T15:01:59","seo":{"title":"The family feud behind bizarre $488K Russian Hill home sale"},"slug":"betrayal-the-family-feud-behind-russian-hills-488k-home-sale","tags":[{"name":"Apartment Rentals","slug":"apartment-rentals"},{"name":"Economy","slug":"economy"},{"name":"Housing \u0026 Development","slug":"housing-development-2"},{"name":"Marina","slug":"marina"},{"name":"Real Estate","slug":"real-estate"},{"name":"Russian Hill","slug":"russian-hill"}],"title":"The family feud behind bizarre $488K Russian Hill home sale","titleImage":{"src":"https://content.sfstandard.com/wp-content/uploads/2024/06/mg_7017.jpg","alt":"The image shows a small two-story white house with a terracotta tiled roof, large front window, blue garage door, and steps leading to a blue front door. Surrounding greenery includes a small tree and bushes.","width":6240,"height":4160},"type":"Post","updatedAt":"2024-06-22T06:07:47","url":"/2024/06/21/betrayal-the-family-feud-behind-russian-hills-488k-home-sale/"},{"cleanTitle":"Silicon Valley is finally making flying cars—and this guy bought one of the first","authors":[{"id":281433,"email":"<EMAIL>","url":"https://sfstandard.com/author/sam-mondros/","title":"Sam Mondros","social":[{"network":"twitter","url":"https://twitter.com/Sam_Mondros"},{"network":"linkedin","url":"https://www.linkedin.com/in/sam-mondros-4335b41b5/"}],"titleImage":{"node":{"alt":"","src":"https://content.sfstandard.com/wp-content/uploads/2024/06/sfs-staffportrait-sammondros.jpg"}},"disableProfilePage":false}],"blocks":[],"categories":[{"id":5947,"title":"Life","url":"/life/"}],"contributors":[],"excerpt":"The flying car is here—sort of. ","id":283515,"meta":{},"publishedAt":"2024-06-15T10:00:00","seo":{"title":"Silicon Valley is finally making flying cars—and this guy bought one of the first"},"slug":"pivotal-flying-cars-black-fly-tim-lum","tags":[{"name":"Drones","slug":"drones"},{"name":"Silicon Valley","slug":"silicon-valley"},{"name":"Tech","slug":"tech"}],"title":"Silicon Valley is finally making flying cars—and this guy bought one of the first","titleImage":{"src":"https://content.sfstandard.com/wp-content/uploads/2024/06/9b424573-cf65-4069-b0a7-28f9a964ac88.jpg","alt":"A person wearing a helmet with a mounted camera and headset sits in a black glider cockpit marked with \"TL39.\" The background shows a hilly, scenic sunset.","width":1600,"height":1200},"type":"Post","updatedAt":"2024-06-18T16:55:18","url":"/2024/06/15/pivotal-flying-cars-black-fly-tim-lum/"},{"cleanTitle":"Oakland Mayor Sheng Thao refuses to resign after FBI raids. Her attorney just quit","authors":[{"id":180272,"email":"<EMAIL>","url":"https://sfstandard.com/author/george-kelly/","title":"George Kelly","social":[{"network":"url","url":"https://www.georgekelly.com/"},{"network":"twitter","url":"https://twitter.com/allaboutgeorge"},{"network":"linkedin","url":"https://www.linkedin.com/in/allaboutgeorge"}],"titleImage":{"node":{"alt":"","src":"https://content.sfstandard.com/wp-content/uploads/2023/07/george-kelly.jpg"}},"disableProfilePage":false}],"blocks":[],"categories":[{"id":5944,"title":"News","url":"/news/"}],"contributors":[],"excerpt":"Mayor Sheng Thao spoke publicly for the first time since the FBI raid on her home.","id":285665,"meta":{},"publishedAt":"2024-06-24T11:00:05","seo":{"title":"Oakland Mayor Sheng Thao refuses to resign after FBI raids. Her attorney just quit"},"slug":"oakland-mayor-sheng-thao-refuses-resign","tags":[{"name":"Oakland","slug":"oakland"},{"name":"Politics","slug":"politics"},{"name":"Sheng Thao","slug":"sheng-thao"}],"title":"Oakland Mayor Sheng Thao refuses to resign after FBI raids. Her attorney just quit","titleImage":{"src":"https://content.sfstandard.com/wp-content/uploads/2023/01/OaklandInauguration01092023-06.jpg","alt":"A woman in a magenta blazer and light-colored top is standing at a podium with a microphone, smiling. There is text in the blurred background.","width":1800,"height":1200},"type":"Post","updatedAt":"2024-06-24T16:20:57","url":"/2024/06/24/oakland-mayor-sheng-thao-refuses-resign/"},{"cleanTitle":"San Francisco’s most powerful political group endorses Farrell, Lurie over Breed","authors":[{"id":179502,"email":"<EMAIL>","url":"https://sfstandard.com/author/josh-koehn/","title":"Josh Koehn","social":[{"network":"twitter","url":"https://twitter.com/Josh_Koehn"},{"network":"instagram","url":"https://www.instagram.com/koehn.josh/"},{"network":"linkedin","url":"https://www.linkedin.com/in/josh-koehn-0bb96423/"}],"titleImage":{"node":{"alt":"","src":"https://content.sfstandard.com/wp-content/uploads/2022/08/Josh_Koehn.jpg"}},"disableProfilePage":false}],"blocks":[],"categories":[{"id":5943,"title":"Politics \u0026 Policy","url":"/politics-policy/"}],"contributors":[{"heading":null,"profiles":[{"id":245982,"email":"<EMAIL>","url":"https://sfstandard.com/author/gabriel-greschler/","title":"Gabe Greschler","social":[{"network":"twitter","url":"https://twitter.com/ggreschler"}],"titleImage":{"node":{"alt":"","src":"https://content.sfstandard.com/wp-content/uploads/2024/02/gabe-greschler.jpg"}},"disableProfilePage":false}]}],"excerpt":"After spending millions to reshape San Francisco politics, a moderate group is backing two challengers to Mayor London Breed.","id":285709,"meta":{},"publishedAt":"2024-06-24T12:16:53","seo":{"title":"San Francisco’s most powerful political group endorses Farrell, Lurie over Breed"},"slug":"san-franciscos-most-powerful-political-group-endorses-farrell-lurie-over-breed","tags":[{"name":"Aaron Peskin","slug":"aaron-peskin"},{"name":"Chesa Boudin","slug":"chesa-boudin"},{"name":"Daniel Lurie","slug":"daniel-lurie"},{"name":"Elections 2024","slug":"elections-2024"},{"name":"London Breed","slug":"london-breed"},{"name":"Mark Farrell","slug":"mark-farrell"},{"name":"Mayor of San Francisco","slug":"sf-mayor"}],"title":"San Francisco’s most powerful political group endorses Farrell, Lurie over Breed","titleImage":{"src":"https://content.sfstandard.com/wp-content/uploads/2024/06/featured_20240624neighborsmayor.jpeg","alt":"The image features three people. On the left, a man in a dark suit is speaking. In the middle, a woman in a blue blazer holds a microphone. On the right, a man in glasses and a suit is talking.","width":2550,"height":1700},"type":"Post","updatedAt":"2024-06-24T18:30:42","url":"/2024/06/24/san-franciscos-most-powerful-political-group-endorses-farrell-lurie-over-breed/"},{"cleanTitle":"Hear that? It’s the sound of the San Francisco Symphony setting itself on fire","authors":[{"id":255120,"email":"<EMAIL>","url":"https://sfstandard.com/author/adam-lashinsky/","title":"Adam Lashinsky","social":[{"network":"twitter","url":"https://twitter.com/adamlashinsky"}],"titleImage":{"node":{"alt":"","src":"https://content.sfstandard.com/wp-content/uploads/2024/03/adam-lashinsky.jpg"}},"disableProfilePage":false}],"blocks":[],"categories":[],"contributors":[],"excerpt":"A music director fleeing town, labor friction with artists, and confusion over what it stands for: A shocking portrait of an artistic organization adrift.","id":284766,"meta":{},"publishedAt":"2024-06-20T06:00:00","seo":{"title":"Opinion: Hear that? It’s the sound of the San Francisco Symphony setting itself on fire"},"slug":"san-francisco-symphony-troubled","tags":[],"title":"Opinion: Hear that? It’s the sound of the San Francisco Symphony setting itself on fire","titleImage":{"src":"https://content.sfstandard.com/wp-content/uploads/2024/06/sfstandard_salonen.jpg","alt":"The image shows a conductor passionately leading musicians, with flames and a fiery red backdrop intensifying the scene. Two violinists are visible in the foreground.","width":2048,"height":1365},"type":"Opinion","updatedAt":"2024-06-20T11:20:55","url":"/opinion/2024/06/20/san-francisco-symphony-troubled/"}],"readMore":[{"author":"Lauren Saria","authors":["Lauren Saria"],"full_content_word_count":0,"image_url":"https://content.sfstandard.com/wp-content/uploads/2024/06/featured_20240621-junkfeeban.jpg","metadata":"{\"image\":{\"src\":\"https://content.sfstandard.com/wp-content/uploads/2024/06/featured_20240621-junkfeeban.jpg\",\"alt\":\"An illustration of a large glass tip jar breaking into pieces as people dressed as chefs and servers stand around and watch.\",\"width\":2048,\"height\":1365},\"excerpt\":\"Diners hate them. Restaurant owners say they need them. But in the battle over service fees, it’s servers and cooks on the front lines. \",\"postSources\":[],\"type\":\"Post\",\"cleanTitle\":\"Restaurant workers fear they could pay highest cost in junk-fee battle\",\"id\":285407}","pub_date":"2024-06-24T17:00:00","section":"Food \u0026 Drink","tags":["Food","Restaurants","parsely_smart:entity:Fee","parsely_smart:entity:Gratuity","parsely_smart:iab:Business","parsely_smart:iab:Dining Out","parsely_smart:iab:Food \u0026 Drink","parsely_smart:iab:Health \u0026 Fitness"],"thumb_url_medium":"https://images.parsely.com/zqpox_bJsmbRI2sdeVzt7XxglrY=/85x85/smart/https%3A//content.sfstandard.com/wp-content/uploads/2024/06/featured_20240621-junkfeeban.jpg","title":"Restaurant workers fear they could pay highest cost in junk-fee battle","url":"https://sfstandard.com/2024/06/24/service-fee-restaurants-san-francisco/?itm_source=parsely-api"},{"author":"Becky Duffet","authors":["Becky Duffet"],"full_content_word_count":0,"image_url":"https://content.sfstandard.com/wp-content/uploads/2024/06/20240615_farmersmarketchefspicks_-0350.jpg","metadata":"{\"image\":{\"src\":\"https://content.sfstandard.com/wp-content/uploads/2024/06/20240615_farmersmarketchefspicks_-0350.jpg\",\"alt\":\"Chef Nico Pena of Octavia holding Brentwood corn\",\"width\":2300,\"height\":1533,\"crop\":\"0px,0px,2299px,1533px\"},\"excerpt\":\"Produce is ripe for the picking. But how to get the best of it? We asked SF\u0026#8217;s culinary elite.\",\"postSources\":[],\"type\":\"Post\",\"cleanTitle\":\"The produce is peaking: 12 SF chefs on what to buy at the farmer’s market right now\",\"id\":284931}","pub_date":"2024-06-22T13:00:00","section":"Food \u0026 Drink","tags":["Chefs","Restaurants"],"thumb_url_medium":"https://images.parsely.com/_Kifohxm_XIvp3vhSg-vmTnj8XI=/85x85/smart/https%3A//content.sfstandard.com/wp-content/uploads/2024/06/20240615_farmersmarketchefspicks_-0350.jpg","title":"The produce is peaking: 12 SF chefs on what to buy at the farmer’s market right now","url":"https://sfstandard.com/2024/06/22/summer-is-here-twelve-top-chefs-on-how-to-shop-the-citys-most-famous-market-like-a-boss/?itm_source=parsely-api"},{"author":"Lauren Saria","authors":["Lauren Saria","Sara Deseran","Astrid Kane"],"full_content_word_count":0,"image_url":"https://content.sfstandard.com/wp-content/uploads/2024/06/inlined_20240615_allthingsconsumedjune20.jpg","metadata":"{\"image\":{\"src\":\"https://content.sfstandard.com/wp-content/uploads/2024/06/inlined_20240615_allthingsconsumedjune20.jpg\",\"alt\":\"Rinataro's bento box for two people\",\"width\":3000,\"height\":2001},\"excerpt\":\"Rintaro’s takeout bento boxes are back—and still exquisite. Plus, LOJ is already creating a buzz in the Marina and a perfect cocktail for Pride.\",\"postSources\":[],\"type\":\"Post\",\"cleanTitle\":\"SF’s $100 bento box is the best takeout in the city\",\"id\":284807}","pub_date":"2024-06-20T19:00:00","section":"Food \u0026 Drink","tags":["Bars","Cocktails","Food","Restaurants","parsely_smart:iab:American Cuisine","parsely_smart:iab:Cooking, Baking \u0026 Desserts","parsely_smart:iab:Dining Out","parsely_smart:iab:Food \u0026 Drink"],"thumb_url_medium":"https://images.parsely.com/Ev3bVAuWkRp0fSrPCtu0QqICjRI=/85x85/smart/https%3A//content.sfstandard.com/wp-content/uploads/2024/06/inlined_20240615_allthingsconsumedjune20.jpg","title":"SF’s $100 bento box is the best takeout in the city","url":"https://sfstandard.com/2024/06/20/rintaros-bento-boxes-are-back-and-still-sfs-most-exquisite-takeout/?itm_source=parsely-api"},{"author":"Lauren Saria","authors":["Lauren Saria"],"full_content_word_count":0,"image_url":"https://content.sfstandard.com/wp-content/uploads/2024/06/featured-20240617-classicmartinis-me-15_b8fbc3.jpg","metadata":"{\"image\":{\"src\":\"https://content.sfstandard.com/wp-content/uploads/2024/06/featured-20240617-classicmartinis-me-15_b8fbc3.jpg\",\"alt\":\"A martini glass with two olives skewered on a stick is placed on a dark square napkin on a polished wooden bar top, with a blurry background of bar lights.\",\"width\":3000,\"height\":2001},\"excerpt\":\"Most martinis are served in coupes or Nick and Nora glasses. But there\u0026#8217;s still a handful of spotsserving a classic martini in a classic martini glass.\",\"postSources\":[],\"type\":\"Post\",\"cleanTitle\":\"In defense of a real martini—in a real martini glass\",\"id\":284427}","pub_date":"2024-06-19T18:00:00","section":"Food \u0026 Drink","tags":["Bars","Cocktails","Restaurants","parsely_smart:entity:Bartender","parsely_smart:entity:Cocktail","parsely_smart:entity:Cosmopolitan (cocktail)","parsely_smart:entity:Craft cocktail movement","parsely_smart:entity:Drink","parsely_smart:entity:Martini (cocktail)","parsely_smart:iab:Alcohol","parsely_smart:iab:Food \u0026 Drink"],"thumb_url_medium":"https://images.parsely.com/BRJ5-i0vyJujSTRZ8TbrcHjPOak=/85x85/smart/https%3A//content.sfstandard.com/wp-content/uploads/2024/06/featured-20240617-classicmartinis-me-15_b8fbc3.jpg","title":"In defense of a real martini—in a real martini glass","url":"https://sfstandard.com/2024/06/19/best-classic-martini-san-francisco-restaurant-bar/?itm_source=parsely-api"},{"author":"Lauren Saria","authors":["Lauren Saria"],"full_content_word_count":0,"image_url":"https://content.sfstandard.com/wp-content/uploads/2024/06/20240612_noncommittalwinebars_-0539.jpg","metadata":"{\"image\":{\"src\":\"https://content.sfstandard.com/wp-content/uploads/2024/06/20240612_noncommittalwinebars_-0539.jpg\",\"alt\":\"An overhead view of a bar loaded with small plates of food and glasses of wine.\",\"width\":3000,\"height\":2001},\"excerpt\":\"A growing number of San Francisco wine bars cater to diners who don’t want to commit to a restaurant meal. It\u0026#8217;s the best kind of situationship. \",\"postSources\":[],\"type\":\"Post\",\"cleanTitle\":\"A new dining trend in San Francisco? Casual wine bars with serious food\",\"id\":283540}","pub_date":"2024-06-18T18:00:00","section":"Food \u0026 Drink","tags":["Bars","Food","Restaurants","Wine","parsely_smart:entity:Menu","parsely_smart:entity:Restaurant","parsely_smart:iab:Alcohol","parsely_smart:iab:Dining Out","parsely_smart:iab:Food \u0026 Drink"],"thumb_url_medium":"https://images.parsely.com/B6n3UCjmhosxIvr8sQgBOZeKH3w=/85x85/smart/https%3A//content.sfstandard.com/wp-content/uploads/2024/06/20240612_noncommittalwinebars_-0539.jpg","title":"A new dining trend in San Francisco? Casual wine bars with serious food","url":"https://sfstandard.com/2024/06/18/wine-bars-san-francisco-trend-reservations-food/?itm_source=parsely-api"},{"author":"Lauren Saria","authors":["Lauren Saria","Sara Deseran","Astrid Kane"],"full_content_word_count":0,"image_url":"https://content.sfstandard.com/wp-content/uploads/2024/06/inline-01-20240607-allthingsconsumedjune13-tv0120-cropped.jpg","metadata":"{\"image\":{\"src\":\"https://content.sfstandard.com/wp-content/uploads/2024/06/inline-01-20240607-allthingsconsumedjune13-tv0120-cropped.jpg\",\"alt\":\"The Kuy Teav Phnom Penh noodle soup (left) and Mee Kola bowl sit on a table in Lunette Cambodia\",\"width\":5810,\"height\":3875},\"excerpt\":\"The new Cambodian spot has locals and tourists lining up. Plus: A love letter to a great neighborhood restaurant and a natural wine bar that serves endless summer.\",\"postSources\":[],\"type\":\"Post\",\"cleanTitle\":\"Newly opened Lunette is already the star of the Ferry Building\",\"id\":282999}","pub_date":"2024-06-13T17:00:00","section":"Food \u0026 Drink","tags":["All Things Consumed","Cocktails","Food","Restaurants","parsely_smart:entity:Cambodian cuisine","parsely_smart:entity:Cutlet","parsely_smart:entity:Fish sauce","parsely_smart:entity:Kroeung","parsely_smart:entity:Kuyteav","parsely_smart:entity:Lunch","parsely_smart:entity:Thai cuisine","parsely_smart:iab:Chinese Cuisine","parsely_smart:iab:Cooking, Baking \u0026 Desserts","parsely_smart:iab:Dining Out","parsely_smart:iab:Food \u0026 Drink"],"thumb_url_medium":"https://images.parsely.com/-7EsXQ7Lo-ykyZm6Nvsdc1pz_nk=/85x85/smart/https%3A//content.sfstandard.com/wp-content/uploads/2024/06/inline-01-20240607-allthingsconsumedjune13-tv0120-cropped.jpg","title":"Newly opened Lunette is already the star of the Ferry Building","url":"https://sfstandard.com/2024/06/13/lunette-new-cambodian-restaurant-san-francisco/?itm_source=parsely-api"},{"author":"Sara Deseran","authors":["Sara Deseran"],"full_content_word_count":0,"image_url":"https://content.sfstandard.com/wp-content/uploads/2024/06/20240605_pastasupplyco-me-54.jpg","metadata":"{\"image\":{\"src\":\"https://content.sfstandard.com/wp-content/uploads/2024/06/20240605_pastasupplyco-me-54.jpg\",\"alt\":\"A man with tattoos and glasses, wearing a cap and a white uniform, leans on a glass counter filled with pasta in a well-lit store with shelves of products behind him.\",\"width\":2380,\"height\":1586},\"excerpt\":\"With his second Pasta Supply Co now open, one chef has learned a thing or two about the business. And he has some advice.\",\"postSources\":[],\"type\":\"Post\",\"cleanTitle\":\"The pasta chef upending the restaurant biz—and making money while he’s at it\",\"id\":282396}","pub_date":"2024-06-11T17:00:00","section":"Food \u0026 Drink","tags":["Chefs","Restaurants","parsely_smart:iab:Business","parsely_smart:iab:Dining Out","parsely_smart:iab:Food \u0026 Drink"],"thumb_url_medium":"https://images.parsely.com/57pPWPtBz4jQfYYx12tshqC4EXQ=/85x85/smart/https%3A//content.sfstandard.com/wp-content/uploads/2024/06/20240605_pastasupplyco-me-54.jpg","title":"The pasta chef upending the restaurant biz—and making money while he’s at it","url":"https://sfstandard.com/2024/06/11/chef-anthony-strong-restaurant-success/?itm_source=parsely-api"},{"author":"Lauren Saria","authors":["Lauren Saria"],"full_content_word_count":0,"image_url":"https://content.sfstandard.com/wp-content/uploads/2024/06/featured-adahliacole-wisesons-june2024-20.jpg","metadata":"{\"image\":{\"src\":\"https://content.sfstandard.com/wp-content/uploads/2024/06/featured-adahliacole-wisesons-june2024-20.jpg\",\"alt\":\"A close up of a pile of bagels of various flavor.\",\"width\":8256,\"height\":5504},\"excerpt\":\"The good news is it\u0026#8217;s easier than ever to get a good bagel. The bad news? They might lose some of their allure.\",\"postSources\":[],\"type\":\"Post\",\"cleanTitle\":\"San Francisco has reached peak bagel. It’s a blessing and a curse\",\"id\":281850}","pub_date":"2024-06-09T15:00:00","section":"Food \u0026 Drink","tags":["Bakeries","Food","Restaurants","parsely_smart:iab:Business","parsely_smart:iab:Cooking, Baking \u0026 Desserts","parsely_smart:iab:Food \u0026 Drink","parsely_smart:iab:Science","parsely_smart:iab:Shopping","parsely_smart:iab:Weather"],"thumb_url_medium":"https://images.parsely.com/nWzv2qWMG01DNc0Cxh2FqX9Rkdc=/85x85/smart/https%3A//content.sfstandard.com/wp-content/uploads/2024/06/featured-adahliacole-wisesons-june2024-20.jpg","title":"San Francisco has reached peak bagel. It’s a blessing and a curse","url":"https://sfstandard.com/2024/06/09/san-francisco-has-reached-peak-bagel-its-a-blessing-and-a-curse/?itm_source=parsely-api"},{"author":"Han Li","authors":["Han Li"],"full_content_word_count":0,"image_url":"https://content.sfstandard.com/wp-content/uploads/2024/06/20240603_koreatowninsf_-0243-1.jpg","metadata":"{\"image\":{\"src\":\"https://content.sfstandard.com/wp-content/uploads/2024/06/20240603_koreatowninsf_-0243-1.jpg\",\"alt\":\"A man is laughing while standing in a brightly lit, colorful restaurant with cartoon-inspired wall art, wearing a black T-shirt and maroon pants.\",\"width\":3000,\"height\":2001,\"crop\":\"0px,0px,3000px,2000px\"},\"excerpt\":\"Within walking distance on Post, Geary and Fillmore streets, dozens of Korean businesses are concentrated in the area.\",\"postSources\":[],\"type\":\"Post\",\"cleanTitle\":\"A Koreatown is quietly forming in an unlikely location: Japantown\",\"id\":281407}","pub_date":"2024-06-06T19:00:00","section":"Food \u0026 Drink","tags":["Asian American","Fillmore","Japantown"],"thumb_url_medium":"https://images.parsely.com/RmfapFgb2cfLOAoPl2nQNK2R2hY=/85x85/smart/https%3A//content.sfstandard.com/wp-content/uploads/2024/06/20240603_koreatowninsf_-0243-1.jpg","title":"A Koreatown is quietly forming in an unlikely location: Japantown","url":"https://sfstandard.com/2024/06/06/san-francisco-koreatown-japantown-fillmore/?itm_source=parsely-api"},{"author":"Lauren Saria","authors":["Lauren Saria","Sara Deseran","Astrid Kane"],"full_content_word_count":0,"image_url":"https://content.sfstandard.com/wp-content/uploads/2024/06/20240605_allthingsconsumed-elenas-me-21.jpg","metadata":"{\"image\":{\"src\":\"https://content.sfstandard.com/wp-content/uploads/2024/06/20240605_allthingsconsumed-elenas-me-21.jpg\",\"alt\":\"A cast iron skillet with steak, onions, and peppers sits on a wooden table. In the background: a plate of rice and beans, salsa, and toritllas.\",\"width\":2477,\"height\":1652},\"excerpt\":\"The folks from Original Joe\u0026#8217;s get spicy. Plus: Tartine isn\u0026#8217;t just for daytime now and the best Burmese curry in SoMa. \",\"postSources\":[],\"type\":\"Post\",\"cleanTitle\":\"A hot new Mexican restaurant brings rare sizzle to the west side\",\"id\":281243}","pub_date":"2024-06-06T17:00:00","section":"Food \u0026 Drink","tags":["All Things Consumed","Chefs","Restaurants","parsely_smart:entity:Negroni","parsely_smart:entity:Streamline Moderne","parsely_smart:iab:Alcohol","parsely_smart:iab:Arts \u0026 Entertainment","parsely_smart:iab:Food \u0026 Drink"],"thumb_url_medium":"https://images.parsely.com/_aVc0-1ZtbSZoZSnRyxwjot_E_w=/85x85/smart/https%3A//content.sfstandard.com/wp-content/uploads/2024/06/20240605_allthingsconsumed-elenas-me-21.jpg","title":"A hot new Mexican restaurant brings rare sizzle to the west side","url":"https://sfstandard.com/2024/06/06/elenas-brings-the-sizzle-to-west-portal/?itm_source=parsely-api"}],"pageId":"article-285407","seo":{"metaDesc":"San Francisco's restaurant workers worry \"junk fee ban\" could mean lower wages and career changes.","metaKeywords":"","canonical":"https://sfstandard.com/2024/06/24/service-fee-restaurants-san-francisco/","metaRobotsNofollow":"follow","metaRobotsNoindex":"index","opengraphAuthor":"","opengraphDescription":"Diners hate them. Owners say they need them. But in the raging battle over restaurant service fees, it’s servers and cooks who are on the front lines.","opengraphImage":{"uri":"https://content.sfstandard.com/wp-content/uploads/2024/06/featured_20240621-junkfeeban.jpg?resize=1200%2C630","mimeType":"image/jpeg","mediaDetails":{"width":1200,"height":630}},"opengraphModifiedTime":"2024-06-24T18:59:54+00:00","opengraphPublishedTime":"2024-06-24T17:00:00+00:00","opengraphPublisher":"https://www.facebook.com/SFStandard","opengraphSiteName":"The San Francisco Standard","opengraphTitle":"Restaurant workers fear they could pay highest cost in junk-fee battle","opengraphType":"article","opengraphUrl":"https://sfstandard.com/2024/06/24/service-fee-restaurants-san-francisco/","readingTime":6,"title":"California junk fee ban: Restaurant workers brace for impact","twitterDescription":"Diners hate them. Owners say they need them. But in the raging battle over restaurant service fees, it’s servers and cooks who are on the front lines.","twitterTitle":"Restaurant workers fear they could pay highest cost in junk-fee battle","twitterImage":{"uri":"https://content.sfstandard.com/wp-content/uploads/2024/06/featured_20240621-junkfeeban.jpg?fit=2048%2C2048","mimeType":"image/jpeg","mediaDetails":{"width":2048,"height":2048}},"schema":{"raw":"{\"@context\":\"https://schema.org\",\"@graph\":[{\"@type\":\"Article\",\"@id\":\"https://sfstandard.com/2024/06/24/service-fee-restaurants-san-francisco/#article\",\"isPartOf\":{\"@id\":\"https://sfstandard.com/2024/06/24/service-fee-restaurants-san-francisco/\"},\"author\":[{\"@id\":\"https://sfstandard.com/?post_type=profile\u0026#038;p=281091\"}],\"headline\":\"Restaurant workers fear they could pay highest cost in junk-fee battle\",\"datePublished\":\"2024-06-24T17:00:00+00:00\",\"dateModified\":\"2024-06-24T18:59:54+00:00\",\"mainEntityOfPage\":{\"@id\":\"https://sfstandard.com/2024/06/24/service-fee-restaurants-san-francisco/\"},\"wordCount\":1184,\"publisher\":{\"@id\":\"https://sfstandard.com/#organization\"},\"image\":{\"@id\":\"https://sfstandard.com/2024/06/24/service-fee-restaurants-san-francisco/#primaryimage\"},\"thumbnailUrl\":\"https://content.sfstandard.com/wp-content/uploads/2024/06/featured_20240621-junkfeeban.jpg\",\"keywords\":[\"Food\",\"Restaurants\"],\"articleSection\":[\"Food \u0026amp; Drink\"],\"inLanguage\":\"en-US\",\"url\":\"https://sfstandard.com/2024/06/24/service-fee-restaurants-san-francisco/\",\"copyrightYear\":\"2024\",\"copyrightHolder\":{\"@id\":\"https://sfstandard.com/#organization\"}},{\"@type\":\"WebPage\",\"@id\":\"https://sfstandard.com/2024/06/24/service-fee-restaurants-san-francisco/\",\"url\":\"https://sfstandard.com/2024/06/24/service-fee-restaurants-san-francisco/\",\"name\":\"California junk fee ban: Restaurant workers brace for impact\",\"isPartOf\":{\"@id\":\"https://sfstandard.com/#website\"},\"primaryImageOfPage\":{\"@id\":\"https://sfstandard.com/2024/06/24/service-fee-restaurants-san-francisco/#primaryimage\"},\"image\":{\"@id\":\"https://sfstandard.com/2024/06/24/service-fee-restaurants-san-francisco/#primaryimage\"},\"thumbnailUrl\":\"https://content.sfstandard.com/wp-content/uploads/2024/06/featured_20240621-junkfeeban.jpg\",\"datePublished\":\"2024-06-24T17:00:00+00:00\",\"dateModified\":\"2024-06-24T18:59:54+00:00\",\"description\":\"San Francisco's restaurant workers worry \\\"junk fee ban\\\" could mean lower wages and career changes.\",\"inLanguage\":\"en-US\",\"potentialAction\":[{\"@type\":\"ReadAction\",\"target\":[\"https://sfstandard.com/2024/06/24/service-fee-restaurants-san-francisco/\"]}]},{\"@type\":\"ImageObject\",\"inLanguage\":\"en-US\",\"@id\":\"https://sfstandard.com/2024/06/24/service-fee-restaurants-san-francisco/#primaryimage\",\"url\":\"https://content.sfstandard.com/wp-content/uploads/2024/06/featured_20240621-junkfeeban.jpg\",\"contentUrl\":\"https://content.sfstandard.com/wp-content/uploads/2024/06/featured_20240621-junkfeeban.jpg\",\"width\":2048,\"height\":1365,\"caption\":\"California's ban on \\\"junk fees\\\" goes into effect in just one week. Unless a hail mary effort to pass a carveout for restaurants succeeds, many San Francisco restaurant owners will likely raise prices. But its the service workers including servers and cooks who worry they'll suffer most due to the transition away from mandatory service fees.\"},{\"@type\":\"WebSite\",\"@id\":\"https://sfstandard.com/#website\",\"url\":\"https://sfstandard.com/\",\"name\":\"The San Francisco Standard\",\"description\":\"The San Francisco Bay Area\u0026#039;s essential source for daily news, politics, business, food, tech, culture and more\",\"publisher\":{\"@id\":\"https://sfstandard.com/#organization\"},\"potentialAction\":[{\"@type\":\"SearchAction\",\"target\":{\"@type\":\"EntryPoint\",\"urlTemplate\":\"https://sfstandard.com/?s={search_term_string}\"},\"query-input\":\"required name=search_term_string\"}],\"inLanguage\":\"en-US\"},{\"@type\":\"Organization\",\"@id\":\"https://sfstandard.com/#organization\",\"name\":\"The San Francisco Standard\",\"url\":\"https://sfstandard.com/\",\"logo\":{\"@type\":\"ImageObject\",\"inLanguage\":\"en-US\",\"@id\":\"https://sfstandard.com/#/schema/logo/image/\",\"url\":\"https://content.sfstandard.com/wp-content/uploads/2024/02/coloryellow-cropdefault-transparentfalse2x.png\",\"contentUrl\":\"https://content.sfstandard.com/wp-content/uploads/2024/02/coloryellow-cropdefault-transparentfalse2x.png\",\"width\":512,\"height\":512,\"caption\":\"The San Francisco Standard\"},\"image\":{\"@id\":\"https://sfstandard.com/#/schema/logo/image/\"},\"sameAs\":[\"https://www.facebook.com/SFStandard\",\"https://x.com/sfstandard\",\"https://www.instagram.com/sfstandard/\",\"https://www.linkedin.com/company/sfstandard/\",\"https://www.youtube.com/c/SFStandard\",\"https://www.tiktok.com/@sfstandard\",\"https://www.threads.net/@sfstandard\"],\"publishingPrinciples\":\"https://sfstandard.com/about/\",\"ownershipFundingInfo\":\"https://sfstandard.com/about/\",\"actionableFeedbackPolicy\":\"https://sfstandard.com/ethics-standards/\",\"correctionsPolicy\":\"https://sfstandard.com/ethics-standards/\",\"ethicsPolicy\":\"https://sfstandard.com/ethics-standards/\"},{\"@type\":\"Person\",\"@id\":\"https://sfstandard.com/?post_type=profile\u0026#038;p=281091\",\"name\":\"Lauren Saria\",\"url\":\"https://sfstandard.com/author/lauren-saria/\",\"description\":\"Lauren Saria is the deputy food editor at The Standard and has more than a decade of experience covering food, drink, and the restaurant industry. She was previously the site lead for Eater SF and has worked as the food editor at The Arizona Republic and Phoenix New Times. An alumna of the Walter Cronkite\u0026hellip;\",\"image\":{\"@type\":\"ImageObject\",\"inLanguage\":\"en-US\",\"@id\":\"https://sfstandard.com/#/schema/person/image/\",\"url\":\"https://content.sfstandard.com/wp-content/uploads/2024/06/sfs-staffportrait-laurensaria.jpg\",\"contentUrl\":\"https://content.sfstandard.com/wp-content/uploads/2024/06/sfs-staffportrait-laurensaria.jpg\",\"width\":4651,\"height\":3101,\"caption\":\"Deputy Food Editor Lauren Saria poses for a portrait at the San Francisco Standard newsroom in San Francisco on Thursday, Jun. 6, 2024.\"}}]}"},"authorTwitter":"https://twitter.com/lhsaria"},"pageParameters":{"page_type":"article","article_id":285407,"content_title":"Restaurant workers fear they could pay highest cost in junk-fee battle","tags":["Food","Restaurants"],"section":["Food \u0026 Drink"],"authors":["Lauren Saria"],"primary_section":"Food \u0026 Drink","publish_date":"2024-06-24","publish_time":"10:00","page_url":"https://sfstandard.com/2024/06/24/service-fee-restaurants-san-francisco/"},"globalData":{"viewer":null,"flags":[{"id":"pagesRedesign","value":true},{"id":"userRegistration","value":false}],"menus":[{"id":"categories","items":[{"label":"News","order":1,"target":null,"title":null,"url":"/news/","childItems":[]},{"label":"Politics \u0026 Policy","order":2,"target":null,"title":null,"url":"/politics-policy/","childItems":[]},{"label":"Business","order":3,"target":null,"title":null,"url":"/business/","childItems":[]},{"label":"Opinion","order":4,"target":null,"title":null,"url":"/opinion","childItems":[]},{"label":"Life","order":5,"target":null,"title":null,"url":"/life/","childItems":[]},{"label":"Food \u0026 Drink","order":6,"target":null,"title":null,"url":"/food-drink/","childItems":[]},{"label":"Arts \u0026 Entertainment","order":7,"target":null,"title":null,"url":"/arts-entertainment/","childItems":[]}]},{"id":"footer","items":[{"label":"Column 1","order":1,"target":null,"title":null,"url":"/","childItems":[{"label":"About Us","order":2,"target":null,"title":null,"url":"/about/","childItems":[]},{"label":"Staff","order":3,"target":null,"title":null,"url":"/staff/","childItems":[]},{"label":"Jobs","order":4,"target":null,"title":null,"url":"https://jobs.sfstandard.com/","childItems":[]}]},{"label":"Column 2","order":5,"target":null,"title":null,"url":"/","childItems":[{"label":"Newsletters","order":6,"target":null,"title":null,"url":"/newsletters/","childItems":[]},{"label":"Archive","order":7,"target":null,"title":null,"url":"/archive/","childItems":[]},{"label":"RSS Feeds","order":8,"target":null,"title":null,"url":"/rss-feeds/","childItems":[]}]},{"label":"Column 3","order":9,"target":null,"title":null,"url":"/","childItems":[{"label":"Contact Us","order":10,"target":null,"title":null,"url":"/contact-us/","childItems":[]},{"label":"Tips","order":11,"target":null,"title":null,"url":"/tips/","childItems":[]},{"label":"Advertising Inquiries","order":12,"target":null,"title":null,"url":"/advertising/","childItems":[]}]},{"label":"Column 4","order":13,"target":null,"title":null,"url":"","childItems":[{"label":"Standards \u0026 Ethics","order":14,"target":null,"title":null,"url":"/ethics-standards/","childItems":[]},{"label":"Ask The Standard","order":15,"target":null,"title":null,"url":"/ask-the-standard/","childItems":[]}]}]},{"id":"footer-privacy","items":[{"label":"Terms of Use","order":1,"target":null,"title":null,"url":"/terms-of-use/","childItems":[]},{"label":"Privacy Policy","order":2,"target":null,"title":null,"url":"/privacy-policy/","childItems":[]},{"label":"California Resident Rights","order":3,"target":null,"title":null,"url":"/privacy-policy/#california-resident-rights","childItems":[]}]},{"id":"sidebar","items":[{"label":"News","order":1,"target":null,"title":null,"url":"/news/","childItems":[]},{"label":"Politics \u0026 Policy","order":2,"target":null,"title":null,"url":"/politics-policy/","childItems":[]},{"label":"Business","order":3,"target":null,"title":null,"url":"/business/","childItems":[]},{"label":"Opinion","order":4,"target":null,"title":null,"url":"/opinion","childItems":[]},{"label":"Life","order":5,"target":null,"title":null,"url":"/life/","childItems":[]},{"label":"Food \u0026 Drink","order":6,"target":null,"title":null,"url":"/food-drink/","childItems":[]},{"label":"Arts \u0026 Entertainment","order":7,"target":null,"title":null,"url":"/arts-entertainment/","childItems":[]},{"label":"Newsletters","order":8,"target":null,"title":null,"url":"/newsletters","childItems":[]},{"label":"Podcast","order":9,"target":null,"title":null,"url":"https://www.lifeinsevensongs.com/","childItems":[]}]}],"events":[{"endDate":"2024-06-17","startDate":"2024-06-17","title":"Mayoral Debate Watch Party","eventLocation":"290 Division Street, 3rd Floor","eventSubtitle":"Join The Standard to watch the mayoral debate live","eventType":"Live Event","eventUrl":"https://events.sfstandard.com/mayoral-debate","modalCtaButtonText":"","modalDisplayEndDate":"","modalDisplayEndTime":"","modalDisplaySections":[],"modalDisplayStartDate":"","modalImage":"//assets.swoogo.com/uploads/full/4019004-666252c6b4e4d.png","modalImageAltText":"","modalTitle":"*/event-name/*","newsletterPromoButtonText":"","newsletterPromoDescription":"*/event-description/*","newsletterPromoHeading":"Upcoming Event","newsletterPromoImage":"//assets.swoogo.com/uploads/full/4019004-666252c6b4e4d.png","startTime":"19:00:00","endTime":"21:00:00","index":87547.58665342994},{"endDate":"","startDate":"2024-03-13","title":"Policing in San Francisco: Where do we go after the March 5 election?","eventLocation":"Manny's, 3092 16th St, San Francisco, CA 94103","eventSubtitle":"","eventType":"Live at Manny's","eventUrl":"https://events.sfstandard.com/policing-where-do-we-go-after-march-5","modalCtaButtonText":"Get Tickets","modalDisplayEndDate":"2024-03-13","modalDisplayEndTime":"17:30:00","modalDisplaySections":["News","Politics \u0026 Policy","Opinion"],"modalDisplayStartDate":"2024-03-01","modalImage":"//assets.swoogo.com/uploads/medium/3631861-65e264387b2bf.jpeg","modalImageAltText":"Black-and-white portraits of the four men who will be participating in the event","modalTitle":"Policing in SF: Where do we go after the March 5 election?","newsletterPromoButtonText":"Get Tickets","newsletterPromoDescription":"What, if anything, will change in wake of the election? Will we see further attempts to take such measures directly to voters? This panel will be moderated by San Francisco Standard reporter Josh Koehn.","newsletterPromoHeading":"Upcoming Event","newsletterPromoImage":"//assets.swoogo.com/uploads/full/3631863-65e2647770b59.jpeg","startTime":"19:00:00","endTime":"20:00:00","index":85345.66731249056},{"endDate":"","startDate":"2023-11-29","title":"Can More Cops Keep SF Safe?","eventLocation":"Manny's, 3092 16th St, San Francisco, CA 94103","eventSubtitle":"","eventType":"","eventUrl":"https://events.sfstandard.com/mannys-can-more-cops-keep-sf-safe","modalCtaButtonText":"Get Tickets","modalDisplayEndDate":"2023-11-29","modalDisplayEndTime":"17:00:00","modalDisplaySections":["News","Politics \u0026 Policy"],"modalDisplayStartDate":"2023-11-21","modalImage":"//assets.swoogo.com/uploads/full/3272642-655d29c998be1.jpeg","modalImageAltText":"A conversation featuring District 6 Supervisor Matt Dorsey and the San Francisco Standard's Editor-in-Chief Julie Makinen and Senior Reporter Michael Barba","modalTitle":"Can More Cops Keep SF Safe?","newsletterPromoButtonText":"Get Tickets","newsletterPromoDescription":"Would more police officers help San Francisco solve its problems? If so, how should the city pay for them? What should they focus on, if hired? Join District 6 Supervisor Matt Dorsey and the San Francisco Standard's Editor-in-Chief Julie Makinen and Senior Reporter Michael Barba for a lively conversation about this hotly debated topic.","newsletterPromoHeading":"Upcoming Event","newsletterPromoImage":"//assets.swoogo.com/uploads/full/3272645-655d2a12432b6.jpeg","startTime":"18:30:00","endTime":"20:00:00","index":14490.438973624343},{"endDate":"","startDate":"2023-10-25","title":"Is the City Ready?","eventLocation":"","eventSubtitle":"APEC puts a global spotlight on San Francisco","eventType":"","eventUrl":"https://events.sfstandard.com/apec-is-the-city-ready","modalCtaButtonText":"Get Tickets","modalDisplayEndDate":"2023-10-25","modalDisplayEndTime":"18:00:00","modalDisplaySections":["News","Politics \u0026 Policy","Business"],"modalDisplayStartDate":"2023-10-19","modalImage":"//assets.swoogo.com/uploads/full/3076071-6531b1ac5ac5c.jpeg","modalImageAltText":"A live event featuring The Standard reporters Han Li, Josh Koehn, and Jonah Owen Lamb and editor-in-chief Julie Makinen","modalTitle":"Is the City Ready?","newsletterPromoButtonText":"Get Tickets","newsletterPromoDescription":"In mid-November, San Francisco will host the Asia-Pacific Economic Cooperation summit, bringing together President Joe Biden with the leaders of up to 20 other global economies. What are the potential economic upsides? How will local residents be affected, and how can they get involved? And how much is this all going to cost? Join The Standard for a lively discussion and Q\u0026A.","newsletterPromoHeading":"Upcoming Event","newsletterPromoImage":"//assets.swoogo.com/uploads/full/3076269-6531b1f472572.jpeg","startTime":"18:30:00","endTime":"20:00:00","index":30908.127896664482},{"endDate":"","startDate":"2023-10-12","title":"Wine \u0026 Popcorn","eventLocation":"Birba, 458 Grove Street","eventSubtitle":"Join us for a drink on Thursday evening","eventType":"Networking","eventUrl":"https://events.sfstandard.com/birba-wine-bar","modalCtaButtonText":"RSVP","modalDisplayEndDate":"2023-10-12","modalDisplayEndTime":"15:00:00","modalDisplaySections":[],"modalDisplayStartDate":"2023-10-11","modalImage":"//assets.swoogo.com/uploads/full/3022684-652048fc52e64.png","modalImageAltText":"Join us for a drink on Thursday evening","modalTitle":"Wine \u0026 Popcorn","newsletterPromoButtonText":"RSVP","newsletterPromoDescription":"We've teamed up with Birba Wine Bar to present a special treat: an exclusive pour of rosé wine paired with truffled popcorn. RSVP now and join The Standard's CEO—and fellow readers—on Thursday evening.","newsletterPromoHeading":"Upcoming Event","newsletterPromoImage":"//assets.swoogo.com/uploads/full/3042192-65273b4a2d2f1.jpg","startTime":"17:00:00","endTime":"20:00:00","index":55156.146325421316},{"endDate":"","startDate":"2023-10-12","title":"Truffles \u0026 Tannins","eventLocation":"Birba, 458 Grove Street","eventSubtitle":"Join us for a drink at this happening wine bar","eventType":"Networking","eventUrl":"https://events.sfstandard.com/birba","modalCtaButtonText":"RSVP","modalDisplayEndDate":"2023-10-10","modalDisplayEndTime":"02:00:00","modalDisplaySections":[],"modalDisplayStartDate":"2023-10-06","modalImage":"//assets.swoogo.com/uploads/full/3022684-652048fc52e64.png","modalImageAltText":"Join us for a drink at this happening wine bar","modalTitle":"Truffles \u0026 Tannins","newsletterPromoButtonText":"RSVP","newsletterPromoDescription":"Exclusively for San Francisco Standard readers, we've teamed up with Birba Wine Bar to present a special treat: an exclusive pour of rosé wine paired with truffled popcorn. RSVP now and join us—and fellow readers—next Thursday evening.","newsletterPromoHeading":"Upcoming Event","newsletterPromoImage":"//assets.swoogo.com/uploads/full/3023623-6520989c855a3.jpg","startTime":"17:00:00","endTime":"20:00:00","index":66065.17336564031},{"endDate":"","startDate":"2023-09-28","title":"The Standard at Sunset","eventLocation":"The LINE San Francisco, 970 Market St, San Francisco, CA","eventSubtitle":"Rooftop cocktails and music by DJ Miles Medina","eventType":"Networking","eventUrl":"https://events.sfstandard.com/the-standard-at-sunset","modalCtaButtonText":"Join Us","modalDisplayEndDate":"2023-09-19","modalDisplayEndTime":"19:00:00","modalDisplaySections":["News","Life","Food \u0026 Drink","Arts \u0026 Entertainment","Video"],"modalDisplayStartDate":"2023-09-13","modalImage":"//assets.swoogo.com/uploads/medium/2903847-65033e7bd85d0.png","modalImageAltText":"Rooftop cocktails and music by DJ Miles Medina at the Line Hotel in San Francisco","modalTitle":"The Standard at Sunset","newsletterPromoButtonText":"Join Us","newsletterPromoDescription":"The Standard and the LINE San Francisco invite you to admire the views at Rise Over Run, the hotel’s rooftop bar. Join us for cocktails, music by DJ Miles Medina, and a chance to win free tickets to the Portola Music Festival.","newsletterPromoHeading":"Upcoming Event","newsletterPromoImage":"//assets.swoogo.com/uploads/full/2904600-650359811905c.jpeg","startTime":"19:00:00","endTime":"22:00:00","index":13245.117942842333}]},"_sentryTraceData":"f78728fa6cb44d3fa7a42d32b499b868-a74d2ee9ada0979f-0","_sentryBaggage":"sentry-environment=production,sentry-release=5515QAup-wFM4UtsvvF4J,sentry-public_key=642f02aaa96c4e679673d2642c3c2782,sentry-trace_id=f78728fa6cb44d3fa7a42d32b499b868,sentry-sample_rate=0.001,sentry-transaction=%2F%5B...slug%5D,sentry-sampled=false"},"__N_SSP":true},"page":"/[...slug]","query":{"slug":["2024","06","24","service-fee-restaurants-san-francisco"]},"buildId":"5515QAup-wFM4UtsvvF4J","runtimeConfig":{"NEXT_PUBLIC_GRAPHQL_ENDPOINT":"https://content.sfstandard.com/graphql","NEXT_PUBLIC_NEWSLETTER_POPUP_DELAY":"25000","NEXT_PUBLIC_PROMOTIONAL_CAMPAIGN_POPUP_DELAY":"500","NEXT_PUBLIC_SENTRY_DSN":"https://<EMAIL>/4504205221232640","NEXT_PUBLIC_SERVER_URL":"https://sfstandard.com","NEXT_PUBLIC_PUSHLY_API_KEY":"UZPyIYSDIaWyssyBVys87gSmL3qaHC7TSXwz","NEXT_PUBLIC_WORDPRESS_URL":"https://content.sfstandard.com","NEXT_PUBLIC_GTM_ID":"GTM-MM9HQBT","NEXT_PUBLIC_APP_ENVIRONMENT":"production","NEXT_PUBLIC_CSP_REPORT_URI":"https://o4504205219004416.ingest.sentry.io/api/4504205221232640/security/?sentry_key=642f02aaa96c4e679673d2642c3c2782","NEXT_PUBLIC_GRAPHQL_URL":"https://content.sfstandard.com/graphql","NEXT_PUBLIC_WEBSITE_URL":"https://sfstandard.com","NEXT_PUBLIC_PARSELY_API_KEY":"sfstandard.com","NEXT_PUBLIC_SAILTHRU_CUSTOMER_ID":"bce3e9bfed9c608363bcbe5503a2f08f","NEXT_PUBLIC_RECAPTCHA_SITE_KEY":"6LeCOagoAAAAALG9RfX6P6jxPCsEfQq7nIxqVnpI"},"isFallback":false,"gssp":true,"customServer":true,"scriptLoader":[{"id":"ketch","strategy":"afterInteractive","children":"!function(){window.semaphore = window.semaphore || [],window.ketch=function(){window.semaphore.push(arguments)};var e=new URLSearchParams(document.location.search),o=e.has('property')?e.get('property'):'website_smart_tag',n=document.createElement('script');n.type='text/javascript',n.src='https://global.ketchcdn.com/web/v3/config/sfstandard/'.concat(o,'/boot.js'),n.defer=n.async=!0,document.getElementsByTagName('head')[0].appendChild(n)}();"},{"id":"google-tag-manager","strategy":"afterInteractive","children":"window.addEventListener('load', () =\u003e setTimeout(() =\u003e {\n        (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':\n        new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],\n        j=d.createElement(s),dl=l!='dataLayer'?'\u0026l='+l:'';j.async=true;j.src=\n        'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);\n        })(window,document,'script','dataLayer','GTM-MM9HQBT');}, 100));\n      "},{"id":"ketch-manual-scripts","strategy":"afterInteractive","children":"\n            window.sfsApiKeys = {\n              sailthru: 'bce3e9bfed9c608363bcbe5503a2f08f',\n              pushly: 'UZPyIYSDIaWyssyBVys87gSmL3qaHC7TSXwz',\n            };\n\n            ketch('on', 'consent', (consent) =\u003e {\n              if (consent.purposes['analytics']) {\n                setupSailthru();\n                setupPushly();\n              }\n\n              function setupSailthru() {\n                if (!window.sfsApiKeys.sailthru) return;\n\n                const script = document.createElement('script');\n                script.id = 'sailthru-cfg';\n                script.src = 'https://ak.sail-horizon.com/spm/spm.v1.min.js';\n                document.head.appendChild(script);\n\n                var sailthruInitInterval = setInterval(() =\u003e {\n                  const script = document.querySelector('#sailthru-cfg');\n\n                  if (!script) return;\n\n                  script.addEventListener('load', () =\u003e {\n                    Sailthru.init({customerId: window.sfsApiKeys.sailthru});\n                  });\n\n                  clearInterval(sailthruInitInterval);\n                }, 100);\n              }\n\n              function setupPushly() {\n                if (!window.sfsApiKeys.pushly) return;\n\n                const script = document.createElement('script');\n                script.id = 'pushly-sdk';\n                script.src = `https://cdn.p-n.io/pushly-sdk.min.js?domain_key=${encodeURIComponent(\n                  window.sfsApiKeys.pushly,\n                )}`;\n                document.head.appendChild(script);\n\n                window.PushlySDK = window.PushlySDK || [];\n                function pushly() {\n                  window.PushlySDK.push(arguments);\n                }\n\n                window.pushly = pushly;\n\n                pushly('load', {\n                  domainKey: 'UZPyIYSDIaWyssyBVys87gSmL3qaHC7TSXwz',\n                  sw: '/assets/pushly/pushly-sdk-worker.js',\n                });\n              }\n            });\n          "},{"defer":true,"id":"recaptcha","src":"https://www.google.com/recaptcha/api.js?render=6LeCOagoAAAAALG9RfX6P6jxPCsEfQq7nIxqVnpI","strategy":"afterInteractive"},{"id":"zephr","strategy":"afterInteractive","src":"/zephr-browser/1.7.1/zephr-browser.umd.js"}]}</script>
      <div id="portal"></div>
      <script id="ketch" data-nscript="afterInteractive">!function(){window.semaphore = window.semaphore || [],window.ketch=function(){window.semaphore.push(arguments)};var e=new URLSearchParams(document.location.search),o=e.has('property')?e.get('property'):'website_smart_tag',n=document.createElement('script');n.type='text/javascript',n.src='https://global.ketchcdn.com/web/v3/config/sfstandard/'.concat(o,'/boot.js'),n.defer=n.async=!0,document.getElementsByTagName('head')[0].appendChild(n)}();</script><script id="google-tag-manager" data-nscript="afterInteractive">window.addEventListener('load', () => setTimeout(() => {
         (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
         new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
         j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
         'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
         })(window,document,'script','dataLayer','GTM-MM9HQBT');}, 100));
      </script><script id="ketch-manual-scripts" data-nscript="afterInteractive">
         window.sfsApiKeys = {
           sailthru: 'bce3e9bfed9c608363bcbe5503a2f08f',
           pushly: 'UZPyIYSDIaWyssyBVys87gSmL3qaHC7TSXwz',
         };
         
         ketch('on', 'consent', (consent) => {
           if (consent.purposes['analytics']) {
             setupSailthru();
             setupPushly();
           }
         
           function setupSailthru() {
             if (!window.sfsApiKeys.sailthru) return;
         
             const script = document.createElement('script');
             script.id = 'sailthru-cfg';
             script.src = 'https://ak.sail-horizon.com/spm/spm.v1.min.js';
             document.head.appendChild(script);
         
             var sailthruInitInterval = setInterval(() => {
               const script = document.querySelector('#sailthru-cfg');
         
               if (!script) return;
         
               script.addEventListener('load', () => {
                 Sailthru.init({customerId: window.sfsApiKeys.sailthru});
               });
         
               clearInterval(sailthruInitInterval);
             }, 100);
           }
         
           function setupPushly() {
             if (!window.sfsApiKeys.pushly) return;
         
             const script = document.createElement('script');
             script.id = 'pushly-sdk';
             script.src = `https://cdn.p-n.io/pushly-sdk.min.js?domain_key=${encodeURIComponent(
               window.sfsApiKeys.pushly,
             )}`;
             document.head.appendChild(script);
         
             window.PushlySDK = window.PushlySDK || [];
             function pushly() {
               window.PushlySDK.push(arguments);
             }
         
             window.pushly = pushly;
         
             pushly('load', {
               domainKey: 'UZPyIYSDIaWyssyBVys87gSmL3qaHC7TSXwz',
               sw: '/assets/pushly/pushly-sdk-worker.js',
             });
           }
         });
      </script><script src="https://www.google.com/recaptcha/api.js?render=6LeCOagoAAAAALG9RfX6P6jxPCsEfQq7nIxqVnpI" defer="true" id="recaptcha" data-nscript="afterInteractive"></script><script src="/zephr-browser/1.7.1/zephr-browser.umd.js" id="zephr" data-nscript="afterInteractive"></script>
      <next-route-announcer>
         <p aria-live="assertive" id="__next-route-announcer__" role="alert" style="border: 0px; clip: rect(0px, 0px, 0px, 0px); height: 1px; margin: -1px; overflow: hidden; padding: 0px; position: absolute; width: 1px; white-space: nowrap; overflow-wrap: normal;"></p>
      </next-route-announcer>
      <div>
         <div class="grecaptcha-badge" data-style="bottomright" style="width: 256px; height: 60px; display: block; transition: right 0.3s ease 0s; position: fixed; bottom: 14px; right: -186px; box-shadow: gray 0px 0px 5px; border-radius: 2px; overflow: hidden;">
            <div class="grecaptcha-logo"><iframe title="reCAPTCHA" width="256" height="60" role="presentation" name="a-d9xxn7xoqn1b" frameborder="0" scrolling="no" sandbox="allow-forms allow-popups allow-same-origin allow-scripts allow-top-navigation allow-modals allow-popups-to-escape-sandbox allow-storage-access-by-user-activation" src="https://www.google.com/recaptcha/api2/anchor?ar=1&amp;k=6LeCOagoAAAAALG9RfX6P6jxPCsEfQq7nIxqVnpI&amp;co=aHR0cHM6Ly9zZnN0YW5kYXJkLmNvbTo0NDM.&amp;hl=en&amp;v=KXX4ARWFlYTftefkdODAYWZh&amp;size=invisible&amp;cb=y7zyknc2jzdb"></iframe></div>
            <div class="grecaptcha-error"></div>
            <textarea id="g-recaptcha-response-100000" name="g-recaptcha-response" class="g-recaptcha-response" style="width: 250px; height: 40px; border: 1px solid rgb(193, 193, 193); margin: 10px 25px; padding: 0px; resize: none; display: none;"></textarea>
         </div>
         <iframe style="display: none;"></iframe>
      </div>
      <script type="text/javascript" id="" src="//tru.am/scripts/custom/sfstandard.js"></script>
   </body>
</html>