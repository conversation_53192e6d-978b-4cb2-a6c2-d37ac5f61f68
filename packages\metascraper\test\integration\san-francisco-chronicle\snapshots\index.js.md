# Snapshot report for `test/integration/san-francisco-chronicle/index.js`

The actual snapshot is saved in `index.js.snap`.

Generated by [AVA](https://avajs.dev).

## san-francisco-chronicle

> Snapshot 1

    {
      audio: null,
      author: '<PERSON>',
      date: '2016-04-25T23:38:36.000Z',
      description: '[...] it’s natural to think that San Francisco’s new Nasdaq Entrepreneurial Center,…',
      image: 'https://s.hdnux.com/photos/45/53/13/9876166/6/rawImage.jpg',
      lang: 'en',
      logo: 'https://www.sfchronicle.com/sites/premiumsfgate/apple-touch-icon-196x196.png',
      publisher: 'SFGATE',
      title: 'Nasdaq center in SF offers free classes for entrepreneurs',
      url: 'https://www.sfchronicle.com/business/article/Nasdaq-center-in-SF-offers-free-classes-for-7338290.php',
      video: null,
    }
