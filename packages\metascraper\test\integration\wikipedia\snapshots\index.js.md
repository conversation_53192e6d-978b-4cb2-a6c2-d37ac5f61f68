# Snapshot report for `test/integration/wikipedia/index.js`

The actual snapshot is saved in `index.js.snap`.

Generated by [A<PERSON>](https://avajs.dev).

## wikipedia

> Snapshot 1

    {
      audio: 'https://upload.wikimedia.org/wikipedia/en/d/d2/<PERSON>_<PERSON>_-_Like_a_Rolling_Stone.ogg',
      author: null,
      date: '2016-10-13T12:00:00.000Z',
      description: 'This article is about the musician. For his debut album, see <PERSON> (album).',
      image: 'https://upload.wikimedia.org/wikipedia/commons/thumb/0/02/<PERSON>_<PERSON>_-_Azkena_Rock_Festival_2010_2.jpg/1200px-<PERSON>_<PERSON>_-_Azkena_Rock_Festival_2010_2.jpg',
      lang: 'en',
      logo: 'https://en.wikipedia.org/static/apple-touch/wikipedia.png',
      publisher: 'Wikipedia',
      title: '<PERSON> - <PERSON>',
      url: 'https://en.wikipedia.org/wiki/<PERSON>_<PERSON>',
      video: null,
    }
