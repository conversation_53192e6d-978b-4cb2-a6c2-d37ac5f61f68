# Snapshot report for `test/integration/audiense/index.js`

The actual snapshot is saved in `index.js.snap`.

Generated by [AVA](https://avajs.dev).

## audiense

> Snapshot 1

    {
      audio: null,
      author: '<PERSON> - Marketing Manager',
      date: '2017-09-06T00:00:00.000Z',
      description: 'Looking at the data & audience insights behind the crime drama channels will be able to create tailored tv shows with proven traction ensuring high ratings',
      image: 'https://resources.audiense.com/hs-fs/hubfs/Audiense-299.jpg?width=60&name=Audiense-299.jpg',
      lang: 'en',
      logo: 'https://resources.audiense.com/hubfs/favicon-1.png',
      publisher: 'audiense_logotype_black',
      title: '<PERSON> Peaks 2017: Using the data to create the next big TV sensation',
      url: 'https://resources.audiense.com/en/blog/twin-peaks-2017-using-the-social-data-audience-consumer-insights-to-create-the-next-big-tv-sensation',
      video: null,
    }
