# Snapshot report for `test/integration/bukvy/index.js`

The actual snapshot is saved in `index.js.snap`.

Generated by [AVA](https://avajs.dev).

## bukvy

> Snapshot 1

    {
      audio: null,
      author: 'Буквы',
      date: '2019-01-06T07:43:59.000Z',
      description: 'Президент Петр Порошенко заявил, что государство будет уважать религиозный выбор и вероисповедание каждого гражданина страны. Об этом он заявил в обращении после торжественной церемонии вручения в Стамбуле Томоса об автокефалии Православной церкви Украины. “Я как Президент гарантирую от имени государства, что Украина будет уважать религиозный выбор и свободу вероисповедания каждого гражданина”, – подчеркнул он. Петр […]',
      image: 'https://bykvu.com/ru/kd_image_generate/141935.jpg',
      lang: null,
      logo: 'https://bykvu.com/wp-content/themes/bykvu/img/android-icon-192x192.png',
      publisher: 'Буквы',
      title: 'Украина будет уважать религиозный выбор каждого гражданина, – Порошенко',
      url: 'https://bykvu.com/ru/bukvy/107312-ukraina-budet-uvazhat-religioznyj-vybor-kazhdogo-grazhdanina-poroshenko/',
      video: null,
    }
