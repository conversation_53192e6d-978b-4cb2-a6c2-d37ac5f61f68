# Snapshot report for `test/integration/forbes/index.js`

The actual snapshot is saved in `index.js.snap`.

Generated by [AVA](https://avajs.dev).

## forbes

> Snapshot 1

    {
      audio: null,
      author: '<PERSON>',
      date: '2015-09-30T15:12:00.000Z',
      description: 'HackerRank, a fast-growing company that runs coding contests to identify top software engineers, has hired Facebook and Google veteran <PERSON> to be its chief operating officer.',
      image: 'https://imageio.forbes.com/blogs-images/georgeanders/files/2015/09/HackerRank-1940x827.jpg?format=jpg&width=1200',
      lang: 'en',
      logo: 'https://i.forbesimg.com/48X48-F.png',
      publisher: 'Forbes',
      title: '<PERSON> <PERSON>eteran <PERSON> Joins HackerRank As COO',
      url: 'https://www.forbes.com/sites/georgeanders/2015/09/30/facebook-veteran-grady-burnett-joins-hackerrank-as-coo/',
      video: null,
    }
