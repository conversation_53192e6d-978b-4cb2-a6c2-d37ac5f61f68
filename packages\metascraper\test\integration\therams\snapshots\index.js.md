# Snapshot report for `test/integration/therams/index.js`

The actual snapshot is saved in `index.js.snap`.

Generated by [AVA](https://avajs.dev).

## therams

> Snapshot 1

    {
      audio: null,
      author: '<PERSON><PERSON> <PERSON>',
      date: '2024-04-22T22:44:32.799Z',
      description: 'Rams wide receiver <PERSON> is ready to move past the injuries impacting his previou two seasons as he shifts his tattention fully toward 2024.',
      image: 'https://static.clubs.nfl.com/image/upload/t_editorial_landscape_12_desktop/rams/jzucxygx5mpy7zd87dur',
      lang: 'en',
      logo: 'https://static.clubs.nfl.com/rams/uuxy85au1yxxtunegpzz',
      publisher: 'Los Angeles Rams',
      title: '<PERSON> focused on being present, best version of himself as he prepares for 2024 season',
      url: 'https://www.therams.com/news/cooper-kupp-being-present-best-version-of-himself-prepares-2024-season',
      video: null,
    }
