<!doctype html>
<html lang="en">

<head>
  <script defer src="https://cdn.optimizely.com/js/16180790160.js"></script>
  <title data-rh="true">🍾🚀 webpack 3: Official Release!! 🚀🍾 | by <PERSON> | webpack | Medium</title>
  <meta data-rh="true" charset="utf-8" />
  <meta data-rh="true" name="viewport" content="width=device-width,minimum-scale=1,initial-scale=1" />
  <meta data-rh="true" name="theme-color" content="#000000" />
  <meta data-rh="true" name="twitter:app:name:iphone" content="Medium" />
  <meta data-rh="true" name="twitter:app:id:iphone" content="828256236" />
  <meta data-rh="true" property="al:ios:app_name" content="Medium" />
  <meta data-rh="true" property="al:ios:app_store_id" content="828256236" />
  <meta data-rh="true" property="al:android:package" content="com.medium.reader" />
  <meta data-rh="true" property="fb:app_id" content="542599432471018" />
  <meta data-rh="true" property="og:site_name" content="Medium" />
  <meta data-rh="true" property="og:type" content="article" />
  <meta data-rh="true" property="article:published_time" content="2017-06-21T16:57:08.802Z" />
  <meta data-rh="true" name="title"
    content="🍾🚀 webpack 3: Official Release!! 🚀🍾 | by Sean T. Larkin | webpack | Medium" />
  <meta data-rh="true" property="og:title" content="🍾🚀 webpack 3: Official Release!! 🚀🍾" />
  <meta data-rh="true" property="twitter:title" content="🍾🚀 webpack 3: Official Release!! 🚀🍾" />
  <meta data-rh="true" name="twitter:site" content="@webpack" />
  <meta data-rh="true" name="twitter:app:url:iphone" content="medium://p/15fd2dd8f07b" />
  <meta data-rh="true" property="al:android:url" content="medium://p/15fd2dd8f07b" />
  <meta data-rh="true" property="al:ios:url" content="medium://p/15fd2dd8f07b" />
  <meta data-rh="true" property="al:android:app_name" content="Medium" />
  <meta data-rh="true" name="description"
    content="After we released webpack v2, we made some promises to the community. We promised that we would deliver the features you voted for. Today, we’ve fulfilled these promises." />
  <meta data-rh="true" property="og:description" content="Now with Scope Hoisting, “magic comments”, and more!" />
  <meta data-rh="true" property="twitter:description" content="Now with Scope Hoisting, “magic comments”, and more!" />
  <meta data-rh="true" property="og:url" content="https://medium.com/webpack/webpack-3-official-release-15fd2dd8f07b" />
  <meta data-rh="true" property="al:web:url"
    content="https://medium.com/webpack/webpack-3-official-release-15fd2dd8f07b" />
  <meta data-rh="true" property="og:image" content="https://miro.medium.com/max/1200/1*Ac4K68j43uSbvHnKZKfXPw.jpeg" />
  <meta data-rh="true" name="twitter:image:src"
    content="https://miro.medium.com/max/1200/1*Ac4K68j43uSbvHnKZKfXPw.jpeg" />
  <meta data-rh="true" name="twitter:card" content="summary_large_image" />
  <meta data-rh="true" property="article:author" content="https://medium.com/@TheLarkInn" />
  <meta data-rh="true" name="twitter:creator" content="@TheLarkInn" />
  <meta data-rh="true" name="author" content="Sean T. Larkin" />
  <meta data-rh="true" name="robots" content="index,follow,max-image-preview:large" />
  <meta data-rh="true" name="referrer" content="unsafe-url" />
  <meta data-rh="true" name="twitter:label1" value="Reading time" />
  <meta data-rh="true" name="twitter:data1" value="3 min read" />
  <meta data-rh="true" name="parsely-post-id" content="15fd2dd8f07b" />
  <link data-rh="true" rel="search" type="application/opensearchdescription+xml" title="Medium" href="/osd.xml" />
  <link data-rh="true" rel="apple-touch-icon" sizes="152x152"
    href="https://miro.medium.com/fit/c/152/152/1*sHhtYhaCe2Uc3IU0IgKwIQ.png" />
  <link data-rh="true" rel="apple-touch-icon" sizes="120x120"
    href="https://miro.medium.com/fit/c/120/120/1*sHhtYhaCe2Uc3IU0IgKwIQ.png" />
  <link data-rh="true" rel="apple-touch-icon" sizes="76x76"
    href="https://miro.medium.com/fit/c/76/76/1*sHhtYhaCe2Uc3IU0IgKwIQ.png" />
  <link data-rh="true" rel="apple-touch-icon" sizes="60x60"
    href="https://miro.medium.com/fit/c/60/60/1*sHhtYhaCe2Uc3IU0IgKwIQ.png" />
  <link data-rh="true" rel="mask-icon" href="https://cdn-static-1.medium.com/_/fp/icons/Medium-Avatar-500x500.svg"
    color="#171717" />
  <link data-rh="true" id="glyph_preload_link" rel="preload" as="style" type="text/css"
    href="https://glyph.medium.com/css/unbound.css" />
  <link data-rh="true" id="glyph_link" rel="stylesheet" type="text/css"
    href="https://glyph.medium.com/css/unbound.css" />
  <link data-rh="true" rel="author" href="https://medium.com/@TheLarkInn" />
  <link data-rh="true" rel="canonical" href="https://medium.com/webpack/webpack-3-official-release-15fd2dd8f07b" />
  <link data-rh="true" rel="alternate" href="android-app://com.medium.reader/https/medium.com/p/15fd2dd8f07b" />
  <link data-rh="true" rel="icon" href="https://miro.medium.com/1*m-R_BkNf1Qjr1YbyOIJY2w.png" />
  <script data-rh="true"
    type="application/ld+json">{"@context":"http:\u002F\u002Fschema.org","@type":"NewsArticle","image":["https:\u002F\u002Fmiro.medium.com\u002Fmax\u002F1200\u002F1*Ac4K68j43uSbvHnKZKfXPw.jpeg"],"url":"https:\u002F\u002Fmedium.com\u002Fwebpack\u002Fwebpack-3-official-release-15fd2dd8f07b","dateCreated":"2017-06-19T17:23:28.613Z","datePublished":"2017-06-19T17:23:28.613Z","dateModified":"2018-06-05T03:29:05.031Z","headline":"🍾🚀 webpack 3: Official Release!! 🚀🍾 - webpack - Medium","name":"🍾🚀 webpack 3: Official Release!! 🚀🍾 - webpack - Medium","description":"After we released webpack v2, we made some promises to the community. We promised that we would deliver the features you voted for. Today, we’ve fulfilled these promises.","identifier":"15fd2dd8f07b","keywords":["Lite:true","Tag:JavaScript","Tag:Webpack","Tag:Tech","Tag:Technology","Tag:Web Development","Publication:webpack","Elevated:false","LockedPostSource:LOCKED_POST_SOURCE_NONE","LayerCake:0"],"author":{"@type":"Person","name":"Sean T. Larkin","url":"https:\u002F\u002Fmedium.com\u002F@TheLarkInn"},"creator":["Sean T. Larkin"],"publisher":{"@type":"Organization","name":"webpack","url":"https:\u002F\u002Fmedium.com\u002Fwebpack","logo":{"@type":"ImageObject","width":154,"height":60,"url":"https:\u002F\u002Fmiro.medium.com\u002Fmax\u002F308\u002F1*gdoQ1_5OID90wf1eLTFvWw.png"}},"mainEntityOfPage":"https:\u002F\u002Fmedium.com\u002Fwebpack\u002Fwebpack-3-official-release-15fd2dd8f07b"}</script>
  <link rel="preload" href="https://cdn.optimizely.com/js/16180790160.js" as="script">
  <style type="text/css" data-fela-rehydration="555" data-fela-type="STATIC">
    html {
      box-sizing: border-box
    }

    *,
    *:before,
    *:after {
      box-sizing: inherit
    }

    body {
      margin: 0;
      padding: 0;
      text-rendering: optimizeLegibility;
      -webkit-font-smoothing: antialiased;
      color: rgba(0, 0, 0, 0.8);
      position: relative;
      min-height: 100vh
    }

    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    dl,
    dd,
    ol,
    ul,
    menu,
    figure,
    blockquote,
    p,
    pre,
    form {
      margin: 0
    }

    menu,
    ol,
    ul {
      padding: 0;
      list-style: none;
      list-style-image: none
    }

    main {
      display: block
    }

    a {
      color: inherit;
      text-decoration: none
    }

    a,
    button,
    input {
      -webkit-tap-highlight-color: transparent
    }

    img,
    svg {
      vertical-align: middle
    }

    button {
      background: transparent;
      overflow: visible
    }

    button,
    input,
    optgroup,
    select,
    textarea {
      margin: 0
    }

    :root {
      --reach-tabs: 1;
      --reach-menu-button: 1
    }
  </style>
  <style type="text/css" data-fela-rehydration="555" data-fela-type="RULE">
    .a {
      font-family: medium-content-sans-serif-font, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif
    }

    .b {
      font-weight: 400
    }

    .c {
      background-color: rgba(255, 255, 255, 1)
    }

    .l {
      height: 100vh
    }

    .m {
      width: 100vw
    }

    .n {
      display: flex
    }

    .o {
      align-items: center
    }

    .p {
      justify-content: center
    }

    .q {
      height: 25px
    }

    .r {
      fill: rgba(41, 41, 41, 1)
    }

    .s {
      display: block
    }

    .t {
      position: absolute
    }

    .u {
      top: 0
    }

    .v {
      left: 0
    }

    .w {
      right: 0
    }

    .x {
      z-index: 500
    }

    .y {
      box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.05)
    }

    .ah {
      max-width: 1192px
    }

    .ai {
      min-width: 0
    }

    .aj {
      width: 100%
    }

    .ak {
      height: 65px
    }

    .an {
      flex: 1 0 auto
    }

    .ao {
      fill: rgba(25, 25, 25, 1)
    }

    .ap {
      flex: 0 0 auto
    }

    .aq {
      visibility: hidden
    }

    .ar {
      margin-left: 16px
    }

    .as {
      color: rgba(132, 133, 133, 1)
    }

    .at {
      fill: rgba(132, 133, 133, 1)
    }

    .au {
      font-size: inherit
    }

    .av {
      border: inherit
    }

    .aw {
      font-family: inherit
    }

    .ax {
      letter-spacing: inherit
    }

    .ay {
      font-weight: inherit
    }

    .az {
      padding: 0
    }

    .ba {
      margin: 0
    }

    .be:disabled {
      cursor: default
    }

    .bf:disabled {
      color: rgba(163, 208, 162, 0.5)
    }

    .bg:disabled {
      fill: rgba(163, 208, 162, 0.5)
    }

    .bh {
      border-top: 1px solid rgba(230, 230, 230, 1)
    }

    .bj {
      height: 54px
    }

    .bk {
      overflow: hidden
    }

    .bl {
      margin-right: 40px
    }

    .bm {
      height: 36px
    }

    .bn {
      width: 93px
    }

    .bo {
      overflow: auto
    }

    .bp {
      flex: 0 1 auto
    }

    .bq {
      list-style-type: none
    }

    .br {
      line-height: 40px
    }

    .bs {
      white-space: nowrap
    }

    .bt {
      overflow-x: auto
    }

    .bu {
      align-items: flex-start
    }

    .bv {
      margin-top: 20px
    }

    .bw {
      padding-top: 20px
    }

    .bx {
      height: 80px
    }

    .by {
      height: 20px
    }

    .bz {
      margin-right: 15px
    }

    .ca {
      margin-left: 15px
    }

    .cb:first-child {
      margin-left: 0
    }

    .cc {
      min-width: 1px
    }

    .cd {
      background-color: rgba(168, 168, 168, 1)
    }

    .ce {
      font-family: sohne, "Helvetica Neue", Helvetica, Arial, sans-serif
    }

    .cf {
      font-size: 13px
    }

    .cg {
      line-height: 20px
    }

    .ch {
      color: rgba(117, 117, 117, 1)
    }

    .ci {
      text-transform: uppercase
    }

    .cj {
      letter-spacing: 1px
    }

    .ck {
      color: inherit
    }

    .cl {
      fill: inherit
    }

    .co:disabled {
      color: rgba(117, 117, 117, 1)
    }

    .cp:disabled {
      fill: rgba(117, 117, 117, 1)
    }

    .cq {
      margin-bottom: 0px
    }

    .cr {
      margin-top: 0px
    }

    .cs {
      height: 119px
    }

    .cv {
      padding-left: 24px
    }

    .cw {
      padding-right: 24px
    }

    .cx {
      margin-left: auto
    }

    .cy {
      margin-right: auto
    }

    .cz {
      max-width: 728px
    }

    .da {
      box-sizing: border-box
    }

    .db {
      background: rgba(255, 255, 255, 1)
    }

    .dc {
      border: 1px solid rgba(230, 230, 230, 1)
    }

    .dd {
      border-radius: 4px
    }

    .de {
      box-shadow: 0 1px 4px rgba(230, 230, 230, 1)
    }

    .df {
      max-height: 100vh
    }

    .dg {
      overflow-y: auto
    }

    .dh {
      top: calc(100vh + 100px)
    }

    .di {
      bottom: calc(100vh + 100px)
    }

    .dj {
      width: 10px
    }

    .dk {
      pointer-events: none
    }

    .dl {
      word-break: break-word
    }

    .dm {
      word-wrap: break-word
    }

    .dn:after {
      display: block
    }

    .do:after {
      content: ""
    }

    .dp:after {
      clear: both
    }

    .dq {
      clear: both
    }

    .dz {
      max-width: 4896px
    }

    .ef {
      padding-bottom: 5px
    }

    .eg {
      padding-top: 5px
    }

    .ei {
      cursor: zoom-in
    }

    .ej {
      position: relative
    }

    .ek {
      z-index: auto
    }

    .em {
      opacity: 0
    }

    .en {
      transition: opacity 100ms 400ms
    }

    .eo {
      height: 100%
    }

    .ep {
      will-change: transform
    }

    .eq {
      transform: translateZ(0)
    }

    .er {
      margin: auto
    }

    .es {
      background-color: rgba(242, 242, 242, 1)
    }

    .et {
      padding-bottom: 66.66666666666667%
    }

    .eu {
      height: 0
    }

    .ev {
      filter: blur(20px)
    }

    .ew {
      transform: scale(1.1)
    }

    .ex {
      visibility: visible
    }

    .ey {
      margin-top: 10px
    }

    .ez {
      text-align: center
    }

    .fc {
      font-size: 14px
    }

    .fd {
      max-width: 680px
    }

    .fe {
      line-height: 1.23
    }

    .ff {
      letter-spacing: 0
    }

    .fg {
      font-style: normal
    }

    .fh {
      font-family: fell, Georgia, Cambria, "Times New Roman", Times, serif
    }

    .gc {
      margin-bottom: -0.27em
    }

    .gd {
      color: rgba(41, 41, 41, 1)
    }

    .ge {
      line-height: 1.394
    }

    .gu {
      margin-bottom: -0.42em
    }

    .gv {
      margin-top: 32px
    }

    .gw {
      justify-content: space-between
    }

    .ha {
      border-radius: 50%
    }

    .hb {
      height: 48px
    }

    .hc {
      width: 48px
    }

    .hd {
      margin-left: 12px
    }

    .he {
      margin-bottom: 2px
    }

    .hg {
      max-height: 20px
    }

    .hh {
      text-overflow: ellipsis
    }

    .hi {
      display: -webkit-box
    }

    .hj {
      -webkit-line-clamp: 1
    }

    .hk {
      -webkit-box-orient: vertical
    }

    .hn {
      margin-left: 8px
    }

    .ho {
      color: rgba(255, 255, 255, 1)
    }

    .hp {
      padding: 0px 8px 1px
    }

    .hq {
      fill: rgba(255, 255, 255, 1)
    }

    .hr {
      background: rgba(132, 133, 133, 1)
    }

    .hs {
      border-color: rgba(132, 133, 133, 1)
    }

    .hv:disabled {
      cursor: inherit
    }

    .hw:disabled {
      opacity: 0.3
    }

    .hx:disabled:hover {
      background: rgba(132, 133, 133, 1)
    }

    .hy:disabled:hover {
      border-color: rgba(132, 133, 133, 1)
    }

    .hz {
      border-width: 1px
    }

    .ia {
      border-style: solid
    }

    .ib {
      display: inline-block
    }

    .ic {
      text-decoration: none
    }

    .id {
      align-items: flex-end
    }

    .il {
      padding-right: 6px
    }

    .im {
      fill: rgba(117, 117, 117, 1)
    }

    .in {
      margin-right: 8px
    }

    .io {
      line-height: 1.58
    }

    .ip {
      letter-spacing: -0.004em
    }

    .iq {
      font-family: charter, Georgia, Cambria, "Times New Roman", Times, serif
    }

    .jj {
      margin-bottom: -0.46em
    }

    .jk {
      font-weight: 700
    }

    .jl {
      font-style: italic
    }

    .jm {
      padding: 2px 4px
    }

    .jn {
      font-size: 75%
    }

    .jo>strong {
      font-family: inherit
    }

    .jp {
      font-family: Menlo, Monaco, "Courier New", Courier, monospace
    }

    .jq {
      margin-bottom: 14px
    }

    .jr {
      padding-top: 24px
    }

    .js {
      padding-bottom: 10px
    }

    .jt {
      background-color: rgba(8, 8, 8, 1)
    }

    .ju {
      height: 3px
    }

    .jv {
      width: 3px
    }

    .jw {
      margin-right: 20px
    }

    .jx {
      line-height: 1.12
    }

    .jy {
      letter-spacing: -0.022em
    }

    .jz {
      font-weight: 500
    }

    .ks {
      margin-bottom: -0.28em
    }

    .ky {
      text-decoration: underline
    }

    .kz {
      line-height: 1.18
    }

    .lh {
      margin-bottom: -0.31em
    }

    .ln {
      padding-bottom: 37%
    }

    .lo {
      padding: 20px
    }

    .lp {
      background: rgba(242, 242, 242, 1)
    }

    .lq {
      font-size: 16px
    }

    .lr {
      margin-top: -0.09em
    }

    .ls {
      margin-bottom: -0.09em
    }

    .lt {
      white-space: pre-wrap
    }

    .lu {
      padding-bottom: NaN%
    }

    .lv {
      list-style-type: disc
    }

    .lw {
      margin-left: 30px
    }

    .lx {
      padding-left: 0px
    }

    .md {
      will-change: opacity
    }

    .me {
      position: fixed
    }

    .mf {
      width: 188px
    }

    .mg {
      left: 50%
    }

    .mh {
      transform: translateX(406px)
    }

    .mi {
      top: calc(65px + 54px + 14px)
    }

    .ml {
      will-change: opacity, transform
    }

    .mm {
      transform: translateY(159px)
    }

    .mo {
      width: 131px
    }

    .mp {
      flex-direction: column
    }

    .mq {
      padding-bottom: 28px
    }

    .mr {
      border-bottom: 1px solid rgba(230, 230, 230, 1)
    }

    .ms {
      padding-bottom: 20px
    }

    .mt {
      padding-top: 2px
    }

    .mu {
      max-height: 120px
    }

    .mv {
      -webkit-line-clamp: 6
    }

    .mw {
      padding: 7px 16px 9px
    }

    .mx {
      flex-direction: row
    }

    .my {
      padding-top: 28px
    }

    .mz {
      margin-bottom: 19px
    }

    .na {
      margin-left: -3px
    }

    .ng {
      outline: 0
    }

    .nh {
      border: 0
    }

    .ni {
      user-select: none
    }

    .nj {
      cursor: pointer
    }

    .nk>svg {
      pointer-events: none
    }

    .nm {
      -webkit-user-select: none
    }

    .nw button {
      text-align: left
    }

    .nx {
      opacity: 0.4
    }

    .ny {
      cursor: not-allowed
    }

    .nz {
      padding-right: 9px
    }

    .oi {
      margin-top: 40px
    }

    .oj {
      flex-wrap: wrap
    }

    .ok {
      margin-top: 25px
    }

    .ol {
      margin-bottom: 8px
    }

    .om {
      line-height: 22px
    }

    .on {
      border-radius: 3px
    }

    .oo {
      padding: 5px 10px
    }

    .op {
      max-width: 155px
    }

    .ot {
      top: 1px
    }

    .ph {
      margin-left: -1px
    }

    .pi {
      margin-left: -4px
    }

    .pq {
      padding-right: 8px
    }

    .pr {
      padding-top: 32px
    }

    .ps {
      margin-bottom: 25px
    }

    .pu {
      margin-bottom: 32px
    }

    .pv {
      min-height: 80px
    }

    .qa {
      width: 80px
    }

    .qb {
      padding-left: 102px
    }

    .qd {
      line-height: 18px
    }

    .qe {
      letter-spacing: 0.077em
    }

    .qf {
      margin-bottom: 6px
    }

    .qg {
      font-size: 22px
    }

    .qh {
      line-height: 28px
    }

    .qi {
      padding: 4px 12px 6px
    }

    .qj {
      max-width: 555px
    }

    .qk {
      max-width: 450px
    }

    .ql {
      line-height: 24px
    }

    .qm {
      display: none
    }

    .qo {
      max-width: 550px
    }

    .qp {
      margin-top: 5px
    }

    .qq {
      height: 40px
    }

    .qr {
      width: 40px
    }

    .qs {
      font-size: 12px
    }

    .qt {
      line-height: 16px
    }

    .qu {
      letter-spacing: 0.083em
    }

    .qv {
      padding-top: 8px
    }

    .qw {
      margin-bottom: 40px
    }

    .qx {
      margin-top: 24px
    }

    .qy {
      padding-bottom: 16px
    }

    .qz {
      margin-bottom: 24px
    }

    .sj {
      flex-grow: 0
    }

    .sk {
      padding-bottom: 24px
    }

    .sl {
      max-width: 500px
    }

    .sp {
      padding-bottom: 8px
    }

    .tc {
      padding-bottom: 100%
    }

    .tn {
      padding: 60px 0
    }

    .to {
      background-color: rgba(0, 0, 0, 0.9)
    }

    .tq {
      padding-bottom: 48px
    }

    .tr {
      border-bottom: 1px solid rgba(255, 255, 255, 0.54)
    }

    .ts {
      margin: 0 -12px
    }

    .tt {
      margin: 0 12px
    }

    .tu {
      flex: 1 1 0
    }

    .tx:disabled {
      color: rgba(255, 255, 255, 0.7)
    }

    .ty:disabled {
      fill: rgba(255, 255, 255, 0.7)
    }

    .tz {
      font-size: 20px
    }

    .ua {
      color: rgba(255, 255, 255, 0.98)
    }

    .ub {
      color: rgba(255, 255, 255, 0.7)
    }

    .uc {
      height: 22px
    }

    .ud {
      width: 200px
    }

    .uj {
      margin-right: 16px
    }

    .bb:hover {
      cursor: pointer
    }

    .bc:hover {
      color: rgba(113, 114, 114, 1)
    }

    .bd:hover {
      fill: rgba(113, 114, 114, 1)
    }

    .cm:hover {
      color: rgba(25, 25, 25, 1)
    }

    .cn:hover {
      fill: rgba(25, 25, 25, 1)
    }

    .hm:hover {
      text-decoration: underline
    }

    .ht:hover {
      background: rgba(113, 114, 114, 1)
    }

    .hu:hover {
      border-color: rgba(113, 114, 114, 1)
    }

    .no:hover {
      fill: rgba(117, 117, 117, 1)
    }

    .tv:hover {
      color: rgba(255, 255, 255, 0.99)
    }

    .tw:hover {
      fill: rgba(255, 255, 255, 0.99)
    }

    .el:focus {
      transform: scale(1.01)
    }

    .nn:focus {
      fill: rgba(117, 117, 117, 1)
    }

    .nl:active {
      border-style: none
    }
  </style>
  <style type="text/css" data-fela-rehydration="555" data-fela-type="RULE" media="all and (min-width: 1080px)">
    .d {
      display: none
    }

    .ag {
      margin: 0 64px
    }

    .dy {
      max-width: 1192px
    }

    .ee {
      margin-top: 32px
    }

    .fy {
      font-size: 48px
    }

    .fz {
      margin-top: 0.55em
    }

    .ga {
      line-height: 60px
    }

    .gb {
      letter-spacing: -0.011em
    }

    .gr {
      font-size: 22px
    }

    .gs {
      margin-top: 0.92em
    }

    .gt {
      line-height: 28px
    }

    .ik {
      margin-left: 30px
    }

    .jf {
      font-size: 21px
    }

    .jg {
      margin-top: 2em
    }

    .jh {
      line-height: 32px
    }

    .ji {
      letter-spacing: -0.003em
    }

    .ko {
      font-size: 30px
    }

    .kp {
      margin-top: 1.95em
    }

    .kq {
      line-height: 36px
    }

    .kr {
      letter-spacing: 0
    }

    .kx {
      margin-top: 0.86em
    }

    .lg {
      margin-top: 1.72em
    }

    .lm {
      margin-top: 56px
    }

    .mc {
      margin-top: 1.05em
    }

    .nf {
      margin-right: 5px
    }

    .nv {
      margin-top: 5px
    }

    .oh {
      padding-left: 6px
    }

    .ov {
      display: inline-block
    }

    .pa {
      margin-left: 7px
    }

    .pb {
      margin-top: 8px
    }

    .pg {
      width: 25px
    }

    .po {
      padding-left: 7px
    }

    .pp {
      top: 3px
    }

    .ro {
      width: calc(100% + 32px)
    }

    .rp {
      margin-left: -16px
    }

    .rq {
      margin-right: -16px
    }

    .sf {
      padding-left: 16px
    }

    .sg {
      padding-right: 16px
    }

    .sh {
      flex-basis: 25%
    }

    .si {
      max-width: 25%
    }

    .sy {
      font-size: 16px
    }

    .sz {
      line-height: 20px
    }

    .tl {
      min-width: 70px
    }

    .tm {
      min-height: 70px
    }
  </style>
  <style type="text/css" data-fela-rehydration="555" data-fela-type="RULE" media="all and (max-width: 1079.98px)">
    .e {
      display: none
    }

    .fa {
      margin-left: auto
    }

    .fb {
      text-align: center
    }

    .ij {
      margin-left: 30px
    }

    .ne {
      margin-right: 5px
    }

    .nu {
      margin-top: 5px
    }

    .og {
      padding-left: 6px
    }

    .ou {
      display: inline-block
    }

    .oy {
      margin-left: 7px
    }

    .oz {
      margin-top: 8px
    }

    .pf {
      width: 25px
    }

    .pm {
      padding-left: 7px
    }

    .pn {
      top: 3px
    }
  </style>
  <style type="text/css" data-fela-rehydration="555" data-fela-type="RULE" media="all and (max-width: 903.98px)">
    .f {
      display: none
    }

    .ii {
      margin-left: 30px
    }

    .nd {
      margin-right: 5px
    }

    .nt {
      margin-top: 5px
    }

    .oe {
      padding-left: 6px
    }

    .of {
      top: 3px
    }

    .os {
      display: inline-block
    }

    .ow {
      margin-left: 7px
    }

    .ox {
      margin-top: 8px
    }

    .pe {
      width: 15px
    }

    .pl {
      padding-left: 3px
    }

    .so {
      margin-right: 16px
    }
  </style>
  <style type="text/css" data-fela-rehydration="555" data-fela-type="RULE" media="all and (max-width: 727.98px)">
    .g {
      display: none
    }

    .al {
      height: 56px
    }

    .am {
      display: flex
    }

    .bi {
      display: block
    }

    .ct {
      margin-bottom: 0px
    }

    .cu {
      height: 110px
    }

    .gy {
      margin-top: 32px
    }

    .gz {
      flex-direction: column-reverse
    }

    .ig {
      margin-bottom: 30px
    }

    .ih {
      margin-left: 0px
    }

    .nc {
      margin-left: 8px
    }

    .nr {
      margin-top: 2px
    }

    .ns {
      margin-right: 8px
    }

    .oc {
      padding-left: 6px
    }

    .od {
      top: 3px
    }

    .or {
      display: inline-block
    }

    .pd {
      width: 15px
    }

    .pk {
      padding-left: 3px
    }

    .pt {
      padding-top: 0
    }

    .pw {
      margin-bottom: 24px
    }

    .px {
      align-items: center
    }

    .py {
      width: 102px
    }

    .pz {
      position: relative
    }

    .qc {
      padding-left: 0
    }

    .qn {
      margin-top: 24px
    }

    .ra {
      padding-bottom: 12px
    }

    .rb {
      margin-top: 16px
    }

    .sn {
      margin-right: 16px
    }

    .ta {
      margin-left: 16px
    }

    .tb {
      margin-right: 0px
    }

    .tp {
      padding: 32px 0
    }

    .ue {
      width: 140px
    }

    .uf {
      margin-bottom: 16px
    }

    .ug {
      margin-top: 30px
    }

    .uh {
      width: 100%
    }

    .ui {
      flex-direction: row
    }
  </style>
  <style type="text/css" data-fela-rehydration="555" data-fela-type="RULE" media="all and (max-width: 551.98px)">
    .h {
      display: none
    }

    .ab {
      margin: 0 24px
    }

    .dr {
      margin: 0
    }

    .ds {
      max-width: 100%
    }

    .ea {
      margin-top: 24px
    }

    .fi {
      font-size: 34px
    }

    .fj {
      margin-top: 0.56em
    }

    .fk {
      line-height: 42px
    }

    .fl {
      letter-spacing: -0.016em
    }

    .gf {
      font-size: 18px
    }

    .gg {
      margin-top: 0.79em
    }

    .gh {
      line-height: 24px
    }

    .gx {
      margin-top: 32px
    }

    .hf {
      margin-bottom: 0px
    }

    .ie {
      margin-bottom: 30px
    }

    .if {
      margin-left: 0px
    }

    .ir {
      margin-top: 1.56em
    }

    .is {
      line-height: 28px
    }

    .it {
      letter-spacing: -0.003em
    }

    .ka {
      font-size: 22px
    }

    .kb {
      margin-top: 1.2em
    }

    .kc {
      letter-spacing: 0
    }

    .kt {
      margin-top: 0.67em
    }

    .la {
      font-size: 20px
    }

    .lb {
      margin-top: 1.23em
    }

    .li {
      margin-top: 40px
    }

    .ly {
      margin-top: 1.34em
    }

    .nb {
      margin-left: 8px
    }

    .np {
      margin-top: 2px
    }

    .nq {
      margin-right: 8px
    }

    .oa {
      padding-left: 6px
    }

    .ob {
      top: 3px
    }

    .oq {
      display: inline-block
    }

    .pc {
      width: 15px
    }

    .pj {
      padding-left: 3px
    }

    .rc {
      width: calc(100% + 24px)
    }

    .rd {
      margin-left: -12px
    }

    .re {
      margin-right: -12px
    }

    .rr {
      padding-left: 12px
    }

    .rs {
      padding-right: 12px
    }

    .rt {
      flex-basis: 100%
    }

    .sm {
      margin-right: 16px
    }

    .sq {
      font-size: 16px
    }

    .sr {
      line-height: 20px
    }

    .td {
      min-width: 48px
    }

    .te {
      min-height: 48px
    }
  </style>
  <style type="text/css" data-fela-rehydration="555" data-fela-type="RULE"
    media="all and (min-width: 904px) and (max-width: 1079.98px)">
    .i {
      display: none
    }

    .af {
      margin: 0 64px
    }

    .dx {
      max-width: 1192px
    }

    .ed {
      margin-top: 32px
    }

    .fu {
      font-size: 48px
    }

    .fv {
      margin-top: 0.55em
    }

    .fw {
      line-height: 60px
    }

    .fx {
      letter-spacing: -0.011em
    }

    .go {
      font-size: 22px
    }

    .gp {
      margin-top: 0.92em
    }

    .gq {
      line-height: 28px
    }

    .jb {
      font-size: 21px
    }

    .jc {
      margin-top: 2em
    }

    .jd {
      line-height: 32px
    }

    .je {
      letter-spacing: -0.003em
    }

    .kk {
      font-size: 30px
    }

    .kl {
      margin-top: 1.95em
    }

    .km {
      line-height: 36px
    }

    .kn {
      letter-spacing: 0
    }

    .kw {
      margin-top: 0.86em
    }

    .lf {
      margin-top: 1.72em
    }

    .ll {
      margin-top: 56px
    }

    .mb {
      margin-top: 1.05em
    }

    .rl {
      width: calc(100% + 32px)
    }

    .rm {
      margin-left: -16px
    }

    .rn {
      margin-right: -16px
    }

    .sb {
      padding-left: 16px
    }

    .sc {
      padding-right: 16px
    }

    .sd {
      flex-basis: 25%
    }

    .se {
      max-width: 25%
    }

    .sw {
      font-size: 16px
    }

    .sx {
      line-height: 20px
    }

    .tj {
      min-width: 70px
    }

    .tk {
      min-height: 70px
    }
  </style>
  <style type="text/css" data-fela-rehydration="555" data-fela-type="RULE"
    media="all and (min-width: 728px) and (max-width: 903.98px)">
    .j {
      display: none
    }

    .ae {
      margin: 0 48px
    }

    .dv {
      margin: 0
    }

    .dw {
      max-width: 100%
    }

    .ec {
      margin-top: 32px
    }

    .fq {
      font-size: 48px
    }

    .fr {
      margin-top: 0.55em
    }

    .fs {
      line-height: 60px
    }

    .ft {
      letter-spacing: -0.011em
    }

    .gl {
      font-size: 22px
    }

    .gm {
      margin-top: 0.92em
    }

    .gn {
      line-height: 28px
    }

    .ix {
      font-size: 21px
    }

    .iy {
      margin-top: 2em
    }

    .iz {
      line-height: 32px
    }

    .ja {
      letter-spacing: -0.003em
    }

    .kg {
      font-size: 30px
    }

    .kh {
      margin-top: 1.95em
    }

    .ki {
      line-height: 36px
    }

    .kj {
      letter-spacing: 0
    }

    .kv {
      margin-top: 0.86em
    }

    .le {
      margin-top: 1.72em
    }

    .lk {
      margin-top: 56px
    }

    .ma {
      margin-top: 1.05em
    }

    .ri {
      width: calc(100% + 28px)
    }

    .rj {
      margin-left: -14px
    }

    .rk {
      margin-right: -14px
    }

    .rx {
      padding-left: 14px
    }

    .ry {
      padding-right: 14px
    }

    .rz {
      flex-basis: 50%
    }

    .sa {
      max-width: 50%
    }

    .su {
      font-size: 16px
    }

    .sv {
      line-height: 20px
    }

    .th {
      min-width: 48px
    }

    .ti {
      min-height: 48px
    }
  </style>
  <style type="text/css" data-fela-rehydration="555" data-fela-type="RULE"
    media="all and (min-width: 552px) and (max-width: 727.98px)">
    .k {
      display: none
    }

    .ac {
      margin: 0 24px
    }

    .dt {
      margin: 0
    }

    .du {
      max-width: 100%
    }

    .eb {
      margin-top: 24px
    }

    .fm {
      font-size: 34px
    }

    .fn {
      margin-top: 0.56em
    }

    .fo {
      line-height: 42px
    }

    .fp {
      letter-spacing: -0.016em
    }

    .gi {
      font-size: 18px
    }

    .gj {
      margin-top: 0.79em
    }

    .gk {
      line-height: 24px
    }

    .iu {
      margin-top: 1.56em
    }

    .iv {
      line-height: 28px
    }

    .iw {
      letter-spacing: -0.003em
    }

    .kd {
      font-size: 22px
    }

    .ke {
      margin-top: 1.2em
    }

    .kf {
      letter-spacing: 0
    }

    .ku {
      margin-top: 0.67em
    }

    .lc {
      font-size: 20px
    }

    .ld {
      margin-top: 1.23em
    }

    .lj {
      margin-top: 40px
    }

    .lz {
      margin-top: 1.34em
    }

    .rf {
      width: calc(100% + 24px)
    }

    .rg {
      margin-left: -12px
    }

    .rh {
      margin-right: -12px
    }

    .ru {
      padding-left: 12px
    }

    .rv {
      padding-right: 12px
    }

    .rw {
      flex-basis: 100%
    }

    .ss {
      font-size: 16px
    }

    .st {
      line-height: 20px
    }

    .tf {
      min-width: 48px
    }

    .tg {
      min-height: 48px
    }
  </style>
  <style type="text/css" data-fela-rehydration="555" data-fela-type="RULE" media="print">
    .z {
      display: none
    }
  </style>
  <style type="text/css" data-fela-rehydration="555" data-fela-type="RULE"
    media="(prefers-reduced-motion: no-preference)">
    .eh {
      transition: transform 300ms cubic-bezier(0.2, 0, 0.2, 1)
    }

    .mj {
      transition: opacity 200ms
    }
  </style>
  <style type="text/css" data-fela-rehydration="555" data-fela-type="RULE"
    media="(orientation: landscape) and (max-width: 903.98px)">
    .hl {
      max-height: none
    }
  </style>
  <style type="text/css" data-fela-rehydration="555" data-fela-type="RULE" media="all and (max-width: 1230px)">
    .mk {
      display: none
    }
  </style>
  <style type="text/css" data-fela-rehydration="555" data-fela-type="RULE" media="all and (max-width: 1198px)">
    .mn {
      display: none
    }
  </style>
</head>

<body>
  <div id="root">
    <div class="a b c">
      <div class="d e f g h i j k"></div>
      <script>document.domain = document.domain;</script>
      <div>
        <script>if (window.self !== window.top) window.location = "about:blank"</script>
      </div>
      <script>window.PARSELY = window.PARSELY || { autotrack: false }</script>
      <div class="s">
        <nav class="s t u v w c x y z">
          <div>
            <div class="s c">
              <div class="n p">
                <div class="ab ac ae af ag ah ai aj">
                  <div class="ak n o al am">
                    <div class="n o an x"><a rel="noopener"
                        href="/?source=post_page-----15fd2dd8f07b--------------------------------"><svg
                          viewBox="0 0 1043.63 592.71" class="q ao">
                          <g data-name="Layer 2">
                            <g data-name="Layer 1">
                              <path
                                d="M588.67 296.36c0 163.67-131.78 296.35-294.33 296.35S0 460 0 296.36 131.78 0 294.34 0s294.33 132.69 294.33 296.36M911.56 296.36c0 154.06-65.89 279-147.17 279s-147.17-124.94-147.17-279 65.88-279 147.16-279 147.17 124.9 147.17 279M1043.63 296.36c0 138-23.17 249.94-51.76 249.94s-51.75-111.91-51.75-249.94 23.17-249.94 51.75-249.94 51.76 111.9 51.76 249.94">
                              </path>
                            </g>
                          </g>
                        </svg></a></div>
                    <div class="s ap x">
                      <div class="n o">
                        <div class="n o g">
                          <div class="aq" id="lo-post-page-navbar-sign-in-link">
                            <div class="ar s"><span><a
                                  href="https://medium.com/m/signin?operation=login&amp;redirect=https%3A%2F%2Fmedium.com%2Fwebpack%2Fwebpack-3-official-release-15fd2dd8f07b&amp;source=post_page-----15fd2dd8f07b---------------------nav_reg-----------"
                                  class="as at au av aw ax ay az ba bb bc bd be bf bg" rel="noopener">Sign in</a></span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="bh s c bi">
              <div class="n p">
                <div class="ab ac ae af ag ah ai aj">
                  <div class="bj bk n o">
                    <div class="bl s ap"><a
                        href="/webpack?source=post_page-----15fd2dd8f07b--------------------------------"
                        rel="noopener">
                        <div class="bm bn s"><img alt="webpack" class=""
                            src="https://miro.medium.com/max/186/1*gdoQ1_5OID90wf1eLTFvWw.png" width="93" height="36" />
                        </div>
                      </a></div>
                    <div class="bo s bp">
                      <ul class="bq ba br bs bt n bu g bv bw bx">
                        <li class="n o by bz ca cb"><span class="ce b cf cg ch ci cj"><a
                              href="https://medium.com/webpack/announcements/home?source=post_page-----15fd2dd8f07b--------------------------------"
                              class="ck cl au av aw ax ay az ba bb cm cn be co cp"
                              rel="noopener">Announcements</a></span></li>
                        <li class="n o by bz ca cb"><span class="ce b cf cg ch ci cj"><a
                              href="https://medium.com/webpack/gsoc/home?source=post_page-----15fd2dd8f07b--------------------------------"
                              class="ck cl au av aw ax ay az ba bb cm cn be co cp" rel="noopener">Google Summer of
                              Code</a></span></li>
                        <li class="n o by bz ca cb"><span class="ce b cf cg ch ci cj"><a
                              href="https://medium.com/webpack/contributors-guide/home?source=post_page-----15fd2dd8f07b--------------------------------"
                              class="ck cl au av aw ax ay az ba bb cm cn be co cp" rel="noopener">Contributors
                              Guide</a></span></li>
                        <li class="n o by bz ca cb"><span class="ce b cf cg ch ci cj"><a
                              href="https://medium.com/webpack/tips-and-tricks/home?source=post_page-----15fd2dd8f07b--------------------------------"
                              class="ck cl au av aw ax ay az ba bb cm cn be co cp" rel="noopener">Tips and
                              Tricks</a></span></li>
                        <li class="n o by bz ca cb"><span class="ce b cf cg ch ci cj"><a
                              href="https://medium.com/webpack/about?source=post_page-----15fd2dd8f07b--------------------------------"
                              class="ck cl au av aw ax ay az ba bb cm cn be co cp" rel="noopener">About Us</a></span>
                        </li><span class="by cc cd"></span>
                        <li class="n o by bz ca cb"><span class="ce b cf cg ch ci cj"><a
                              href="http://webpack.js.org/?source=post_page-----15fd2dd8f07b--------------------------------"
                              class="ck cl au av aw ax ay az ba bb cm cn be co cp" rel="noopener nofollow">Official
                              Documentation</a></span></li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </nav>
        <div class="cq cr cs s ct cu"></div>
        <article>
          <section class="cv cw cx cy aj cz da s"></section><span class="s"></span>
          <div>
            <div class="t v dh di dj dk"></div>
            <section class="dl dm dn do dp">
              <div class="dq">
                <div class="n p">
                  <div class="dr ds dt du dv dw af dx ag dy ai aj">
                    <figure class="ea eb ec ed ee dq ef eg paragraph-image">
                      <div role="button" tabindex="0" class="eh ei ej ek aj el">
                        <div class="cx cy dz">
                          <div class="er s ej es">
                            <div class="et eu s">
                              <div class="em en t u v eo aj bk ep eq"><img alt="Image for post"
                                  class="t u v eo aj ev ew ex"
                                  src="https://miro.medium.com/max/60/1*Ac4K68j43uSbvHnKZKfXPw.jpeg?q=20" width="4896"
                                  height="3264" /></div><img alt="Image for post" class="em en t u v eo aj c"
                                width="4896" height="3264" /><noscript><img alt="Image for post" class="t u v eo aj"
                                  src="https://miro.medium.com/max/9792/1*Ac4K68j43uSbvHnKZKfXPw.jpeg" width="4896"
                                  height="3264"
                                  srcSet="https://miro.medium.com/max/552/1*Ac4K68j43uSbvHnKZKfXPw.jpeg 276w, https://miro.medium.com/max/1104/1*Ac4K68j43uSbvHnKZKfXPw.jpeg 552w, https://miro.medium.com/max/1280/1*Ac4K68j43uSbvHnKZKfXPw.jpeg 640w, https://miro.medium.com/max/1456/1*Ac4K68j43uSbvHnKZKfXPw.jpeg 728w, https://miro.medium.com/max/1632/1*Ac4K68j43uSbvHnKZKfXPw.jpeg 816w, https://miro.medium.com/max/1808/1*Ac4K68j43uSbvHnKZKfXPw.jpeg 904w, https://miro.medium.com/max/1984/1*Ac4K68j43uSbvHnKZKfXPw.jpeg 992w, https://miro.medium.com/max/2000/1*Ac4K68j43uSbvHnKZKfXPw.jpeg 1000w"
                                  sizes="1000px" /></noscript>
                            </div>
                          </div>
                        </div>
                      </div>
                      <figcaption class="ey ez cz cx cy fa fb ce b fc cg ch">It’s finally here. And it’s beautiful.
                      </figcaption>
                    </figure>
                  </div>
                </div>
              </div>
              <div class="n p">
                <div class="ab ac ae af ag fd ai aj">
                  <div class="">
                    <h1 id="ea51"
                      class="fe ff fg fh b fi fj fk fl fm fn fo fp fq fr fs ft fu fv fw fx fy fz ga gb gc gd">🍾🚀
                      webpack 3: Official Release!! 🚀🍾</h1>
                  </div>
                  <div class="">
                    <h2 id="8fe8" class="ge ff fg ce b gf gg gh gi gj gk gl gm gn go gp gq gr gs gt gu ch">Scope
                      Hoisting, “magic comments”, and more!</h2>
                    <div class="gv">
                      <div class="n gw gx gy gz">
                        <div class="o n">
                          <div><a rel="noopener"
                              href="/@TheLarkInn?source=post_page-----15fd2dd8f07b--------------------------------"><img
                                alt="Sean T. Larkin" class="s ha hb hc"
                                src="https://miro.medium.com/fit/c/96/96/1*AtcF5LLmMnXgwTpBMeos-w.jpeg" width="48"
                                height="48" /></a></div>
                          <div class="hd aj s">
                            <div class="n">
                              <div style="flex:1"><span class="ce b fc cg gd">
                                  <div class="he n o hf"><span class="ce b fc cg bk hg hh hi hj hk hl gd"><a
                                        class="ck cl au av aw ax ay az ba bb hm be co cp" rel="noopener"
                                        href="/@TheLarkInn?source=post_page-----15fd2dd8f07b--------------------------------">Sean
                                        T. Larkin</a></span>
                                    <div class="hn s ap h"><span><button
                                          class="ce b cf cg ho hp hq hr hs ht hu bb hv hw hx hy dd hz ia da ib ic">Follow</button></span>
                                    </div>
                                  </div>
                                </span></div>
                            </div><span class="ce b fc cg ch"><span class="ce b fc cg bk hg hh hi hj hk hl ch">
                                <div><a class="ck cl au av aw ax ay az ba bb hm be co cp" rel="noopener"
                                    href="/webpack/webpack-3-official-release-15fd2dd8f07b?source=post_page-----15fd2dd8f07b--------------------------------">Jun
                                    19, 2017</a> <!-- -->·
                                  <!-- -->
                                  <!-- -->3
                                  <!-- --> min read
                                </div>
                              </span></span>
                          </div>
                        </div>
                        <div class="n id ie if ig ih ii ij ik z">
                          <div class="n o">
                            <div class="il s ap"><button class="ck cl au av aw ax ay az ba bb cm cn be co cp"
                                aria-label="Share on twitter"><svg width="29" height="29" class="im">
                                  <path
                                    d="M22.05 7.54a4.47 4.47 0 0 0-3.3-1.46 4.53 4.53 0 0 0-4.53 4.53c0 .35.04.7.08 1.05A12.9 12.9 0 0 1 5 6.89a5.1 5.1 0 0 0-.65 2.26c.03 1.6.83 2.99 2.02 3.79a4.3 4.3 0 0 1-2.02-.57v.08a4.55 4.55 0 0 0 3.63 4.44c-.4.08-.8.13-1.21.16l-.81-.08a4.54 4.54 0 0 0 4.2 3.15 9.56 9.56 0 0 1-5.66 1.94l-1.05-.08c2 1.27 4.38 2.02 6.94 2.02 8.3 0 12.86-6.9 12.84-12.85.02-.24 0-.43 0-.65a8.68 8.68 0 0 0 2.26-2.34c-.82.38-1.7.62-2.6.72a4.37 4.37 0 0 0 1.95-2.51c-.84.53-1.81.9-2.83 1.13z">
                                  </path>
                                </svg></button></div>
                            <div class="il s ap"><button class="ck cl au av aw ax ay az ba bb cm cn be co cp"
                                aria-label="Share on linkedin"><svg width="29" height="29" viewBox="0 0 29 29"
                                  fill="none" class="im">
                                  <path
                                    d="M5 6.36C5 5.61 5.63 5 6.4 5h16.2c.77 0 1.4.61 1.4 1.36v16.28c0 .75-.63 1.36-1.4 1.36H6.4c-.77 0-1.4-.6-1.4-1.36V6.36z">
                                  </path>
                                  <path fill-rule="evenodd" clip-rule="evenodd"
                                    d="M10.76 20.9v-8.57H7.89v8.58h2.87zm-1.44-9.75c1 0 1.63-.65 1.63-1.48-.02-.84-.62-1.48-1.6-1.48-.99 0-1.63.64-1.63 1.48 0 .83.62 1.48 1.59 1.48h.01zM12.35 20.9h2.87v-4.79c0-.25.02-.5.1-.7.2-.5.67-1.04 1.46-1.04 1.04 0 1.46.8 1.46 1.95v4.59h2.87v-4.92c0-2.64-1.42-3.87-3.3-3.87-1.55 0-2.23.86-2.61 1.45h.02v-1.24h-2.87c.04.8 0 8.58 0 8.58z"
                                    fill="#fff"></path>
                                </svg></button></div>
                            <div class="il s ap"><button class="ck cl au av aw ax ay az ba bb cm cn be co cp"
                                aria-label="Share on facebook"><svg width="29" height="29" class="im">
                                  <path
                                    d="M23.2 5H5.8a.8.8 0 0 0-.8.8V23.2c0 .*********.8h9.3v-7.13h-2.38V13.9h2.38v-2.38c0-2.45 1.55-3.66 3.74-3.66 1.05 0 1.95.08 2.2.11v2.57h-1.5c-1.2 0-1.48.57-1.48 1.4v1.96h2.97l-.6 2.97h-2.37l.05 7.12h5.1a.8.8 0 0 0 .79-.8V5.8a.8.8 0 0 0-.8-.79">
                                  </path>
                                </svg></button></div>
                            <div class="in s">
                              <div class="im"><span><a
                                    href="https://medium.com/m/signin?actionUrl=https%3A%2F%2Fmedium.com%2F_%2Fbookmark%2Fp%2F15fd2dd8f07b&amp;operation=register&amp;redirect=https%3A%2F%2Fmedium.com%2Fwebpack%2Fwebpack-3-official-release-15fd2dd8f07b&amp;source=post_actions_header--------------------------bookmark_preview-----------"
                                    class="ck cl au av aw ax ay az ba bb cm cn be co cp" rel="noopener"><svg width="25"
                                      height="25" viewBox="0 0 25 25">
                                      <path
                                        d="M19 6a2 2 0 0 0-2-2H8a2 2 0 0 0-2 2v14.66h.01c.01.1.05.2.12.28a.5.5 0 0 0 .7.03l5.67-4.12 5.66 4.13a.5.5 0 0 0 .71-.03.5.5 0 0 0 .12-.29H19V6zm-6.84 9.97L7 19.64V6a1 1 0 0 1 1-1h9a1 1 0 0 1 1 1v13.64l-5.16-3.67a.49.49 0 0 0-.68 0z"
                                        fill-rule="evenodd"></path>
                                    </svg></a></span></div>
                            </div>
                            <div class="s an"></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <p id="927b"
                    class="io ip fg iq b gf ir is it gi iu iv iw ix iy iz ja jb jc jd je jf jg jh ji jj dl gd">After we
                    released webpack v2, we made some promises to the community. We promised that we would deliver the
                    features you voted for. Moreover, we promised to deliver them in a <strong class="iq jk"><em
                        class="jl">faster</em></strong>, <strong class="iq jk"><em class="jl">more stable</em></strong>
                    release cycle.</p>
                  <p id="efac"
                    class="io ip fg iq b gf ir is it gi iu iv iw ix iy iz ja jb jc jd je jf jg jh ji jj dl gd">No more
                    year-long betas, no breaking changes between release candidates. We promised to do you right by<em
                      class="jl"> </em><strong class="iq jk"><em class="jl">you,</em></strong><em class="jl"> the
                      community that makes webpack thrive.</em></p>
                  <p id="ca2d"
                    class="io ip fg iq b gf ir is it gi iu iv iw ix iy iz ja jb jc jd je jf jg jh ji jj dl gd">The
                    webpack team is proud to announce that today we have released webpack 3.0.0!!! You can download or
                    upgrade to it today!!</p>
                  <p id="549b"
                    class="io ip fg iq b gf ir is it gi iu iv iw ix iy iz ja jb jc jd je jf jg jh ji jj dl gd"><code
                      class="es jm jn jo jp b">npm install webpack@3.0.0 --save-dev</code></p>
                  <p id="69df"
                    class="io ip fg iq b gf ir is it gi iu iv iw ix iy iz ja jb jc jd je jf jg jh ji jj dl gd">or with
                  </p>
                  <p id="4ef7"
                    class="io ip fg iq b gf ir is it gi iu iv iw ix iy iz ja jb jc jd je jf jg jh ji jj dl gd"><code
                      class="es jm jn jo jp b">yarn add webpack@3.0.0 --dev</code></p>
                </div>
              </div>
            </section>
            <div class="n p gv jq jr js" role="separator"><span class="jt ha ib ju jv jw"></span><span
                class="jt ha ib ju jv jw"></span><span class="jt ha ib ju jv"></span></div>
            <section class="dl dm dn do dp">
              <div class="n p">
                <div class="ab ac ae af ag fd ai aj">
                  <p id="17f8"
                    class="io ip fg iq b gf ir is it gi iu iv iw ix iy iz ja jb jc jd je jf jg jh ji jj dl gd">Migrating
                    from webpack 2 to 3, should involve <strong class="iq jk"><em class="jl">no effort beyond running
                        the upgrade commands in your terminal.</em></strong> We marked this as a Major change because of
                    internal breaking changes that could affect some plugins.</p>
                  <p id="2878"
                    class="io ip fg iq b gf ir is it gi iu iv iw ix iy iz ja jb jc jd je jf jg jh ji jj dl gd"><strong
                      class="iq jk"><em class="jl">So far we’ve seen 98% of users upgrade with no breaking functionality
                        at all!!!</em></strong></p>
                  <h1 id="3750"
                    class="jx jy fg ce jz ka kb is kc kd ke iv kf kg kh ki kj kk kl km kn ko kp kq kr ks gd">What’s new?
                  </h1>
                  <p id="3c03"
                    class="io ip fg iq b gf kt is it gi ku iv iw ix kv iz ja jb kw jd je jf kx jh ji jj dl gd">As we
                    mentioned, we aimed to deliver the features that you <a href="https://webpack.js.org/vote"
                      class="ck ky" rel="noopener nofollow">voted for</a>! Because of the overwhelming GitHub
                    contributions, <a href="http://opencollective.com/webpack" class="ck ky"
                      rel="noopener nofollow">support from our backers and sponsors</a>, we have been able to hit each
                    one. 😍</p>
                  <h2 id="c11e"
                    class="kz jy fg ce jz la lb gh kc lc ld gk kf gl le gn kj go lf gq kn gr lg gt kr lh gd">🔬 Scope
                    Hoisting 🔬</h2>
                  <p id="0014"
                    class="io ip fg iq b gf kt is it gi ku iv iw ix kv iz ja jb kw jd je jf kx jh ji jj dl gd">Scope
                    Hoisting is the flagship feature of webpack 3. One of webpack’s trade-offs when bundling was that
                    each module in your bundle would be wrapped in individual function closures. These wrapper functions
                    made it slower for your JavaScript to execute in the browser. In comparison, tools like Closure
                    Compiler and RollupJS ‘hoist’ or concatenate the scope of all your modules into one closure and
                    allow for your code to have a faster execution time in the browser.</p>
                  <figure class="li lj lk ll lm dq">
                    <div class="er s ej">
                      <div class="ln eu s"></div>
                    </div>
                  </figure>
                  <p id="4965"
                    class="io ip fg iq b gf ir is it gi iu iv iw ix iy iz ja jb jc jd je jf jg jh ji jj dl gd">As of
                    today, with webpack 3, you can <strong class="iq jk">now add the following plugin to your
                      configuration to enable scope hoisting:</strong></p>
                  <pre
                    class="li lj lk ll lm lo lp bt"><span id="7078" class="gd kz jy fg jp b lq lr ls s lt">module.exports = {  <br/>  plugins: [<br/>    new webpack.optimize.ModuleConcatenationPlugin()<br/>  ]<br/>};</span></pre>
                  <p id="95b8"
                    class="io ip fg iq b gf ir is it gi iu iv iw ix iy iz ja jb jc jd je jf jg jh ji jj dl gd">Scope
                    Hoisting is specifically a feature made possible by ECMAScript Module syntax. Because of this
                    webpack may fallback to normal bundling based on what kind of modules you are using, and <a
                      class="ck ky" rel="noopener"
                      href="/webpack/webpack-freelancing-log-book-week-5-7-4764be3266f5">other conditions</a>.</p>
                  <p id="40b6"
                    class="io ip fg iq b gf ir is it gi iu iv iw ix iy iz ja jb jc jd je jf jg jh ji jj dl gd">To stay
                    informed on what triggers these fallbacks, we’ve added a <code
                      class="es jm jn jo jp b">--display-optimization-bailout</code> cli flag that will tell you what
                    caused the fallback.</p>
                  <figure class="li lj lk ll lm dq">
                    <div class="er s ej">
                      <div class="ln eu s"></div>
                    </div>
                  </figure>
                  <p id="f203"
                    class="io ip fg iq b gf ir is it gi iu iv iw ix iy iz ja jb jc jd je jf jg jh ji jj dl gd">Because
                    scope hoisting will remove function wrappers around your modules, you may see some small size
                    improvements. However, the significant improvement will be how fast the JavaScript loads in the
                    browser. If you have awesome before and after comparisons, feel free to respond with some data as
                    we’d be honored to share it!</p>
                  <h2 id="ea65"
                    class="kz jy fg ce jz la lb gh kc lc ld gk kf gl le gn kj go lf gq kn gr lg gt kr lh gd">🔮 ”Magic
                    Comments” 🔮</h2>
                  <p id="b50c"
                    class="io ip fg iq b gf kt is it gi ku iv iw ix kv iz ja jb kw jd je jf kx jh ji jj dl gd">When we
                    introduced in webpack 2 the ability to use the dynamic import syntax ( <code
                      class="es jm jn jo jp b">import()</code> ), users expressed their concerns that they could not
                    create named chunks like they were able to with <code
                      class="es jm jn jo jp b">require.ensure</code>.</p>
                  <p id="b465"
                    class="io ip fg iq b gf ir is it gi iu iv iw ix iy iz ja jb jc jd je jf jg jh ji jj dl gd">We have
                    now introduced what the community has coined “magic comments”, the ability to pass chunk name, <a
                      class="ck ky" rel="noopener"
                      href="/webpack/how-to-use-webpacks-new-magic-comment-feature-with-react-universal-component-ssr-a38fd3e296a">and
                      more</a> as an inline comment to your <code class="es jm jn jo jp b">import()</code> statements.
                  </p>
                  <figure class="li lj lk ll lm dq">
                    <div class="er s ej">
                      <div class="lu eu s"></div>
                    </div>
                    <figcaption class="ey ez cz cx cy fa fb ce b fc cg ch">By using comments, we are able to stay true
                      to the load specification, and still give you the great chunk naming features you love.
                    </figcaption>
                  </figure>
                  <p id="f813"
                    class="io ip fg iq b gf ir is it gi iu iv iw ix iy iz ja jb jc jd je jf jg jh ji jj dl gd">Although
                    these are technically features we released in v2.4 and v2.6, we worked to stabilize and fix bugs for
                    these features that have landed in v3. This now allows the dynamic import syntax to have the same
                    flexibility as <code class="es jm jn jo jp b">require.ensure</code>.</p>
                  <figure class="li lj lk ll lm dq">
                    <div class="er s ej">
                      <div class="ln eu s"></div>
                    </div>
                  </figure>
                  <p id="a2af"
                    class="io ip fg iq b gf ir is it gi iu iv iw ix iy iz ja jb jc jd je jf jg jh ji jj dl gd">To learn
                    more information, see our <a href="https://webpack.js.org/guides/code-splitting-async" class="ck ky"
                      rel="noopener nofollow">newest documentation guide on code-splitting</a> that highlights these
                    features!!!</p>
                  <h1 id="5371"
                    class="jx jy fg ce jz ka kb is kc kd ke iv kf kg kh ki kj kk kl km kn ko kp kq kr ks gd">😍 What’s
                    next? 😍</h1>
                  <p id="4f06"
                    class="io ip fg iq b gf kt is it gi ku iv iw ix kv iz ja jb kw jd je jf kx jh ji jj dl gd">We have
                    quite a few features and enhancements that we are hoping to bring you!!! But to take control of the
                    ones we should be working one, stop by our<a href="http://webpack.js.org/vote" class="ck ky"
                      rel="noopener nofollow"><em class="jl"> vote page, and upvote the features you would like to
                        see!</em></a></p>
                  <p id="92e2"
                    class="io ip fg iq b gf ir is it gi iu iv iw ix iy iz ja jb jc jd je jf jg jh ji jj dl gd">Here are
                    some of those things we are hoping to bring you still:</p>
                  <ul class="">
                    <li id="b1a0"
                      class="io ip fg iq b gf ir is it gi iu iv iw ix iy iz ja jb jc jd je jf jg jh ji jj lv lw lx gd">
                      Better Build Caching</li>
                    <li id="e784"
                      class="io ip fg iq b gf ly is it gi lz iv iw ix ma iz ja jb mb jd je jf mc jh ji jj lv lw lx gd">
                      Faster initial and incremental builds</li>
                    <li id="9ec9"
                      class="io ip fg iq b gf ly is it gi lz iv iw ix ma iz ja jb mb jd je jf mc jh ji jj lv lw lx gd">
                      Better TypeScript experience</li>
                    <li id="98e6"
                      class="io ip fg iq b gf ly is it gi lz iv iw ix ma iz ja jb mb jd je jf mc jh ji jj lv lw lx gd">
                      Revamped Long term caching</li>
                    <li id="8e9d"
                      class="io ip fg iq b gf ly is it gi lz iv iw ix ma iz ja jb mb jd je jf mc jh ji jj lv lw lx gd">
                      WASM Module Support</li>
                    <li id="8948"
                      class="io ip fg iq b gf ly is it gi lz iv iw ix ma iz ja jb mb jd je jf mc jh ji jj lv lw lx gd">
                      Improve User Experience</li>
                  </ul>
                  <h1 id="f87b"
                    class="jx jy fg ce jz ka kb is kc kd ke iv kf kg kh ki kj kk kl km kn ko kp kq kr ks gd">🙇Thank you
                    🙇</h1>
                  <p id="48ea"
                    class="io ip fg iq b gf kt is it gi ku iv iw ix kv iz ja jb kw jd je jf kx jh ji jj dl gd">All of
                    our users, contributors, documentation writers, bloggers, sponsors, backers, and maintainers are all
                    shareholders in helping us ensure webpack is successful for years to come.</p>
                  <p id="9f50"
                    class="io ip fg iq b gf ir is it gi iu iv iw ix iy iz ja jb jc jd je jf jg jh ji jj dl gd">For this,
                    we thank you all. It is not possible without you and we can’t wait to share what is in store for the
                    future!!</p>
                </div>
              </div>
            </section>
            <div class="n p gv jq jr js" role="separator"><span class="jt ha ib ju jv jw"></span><span
                class="jt ha ib ju jv jw"></span><span class="jt ha ib ju jv"></span></div>
            <section class="dl dm dn do dp">
              <div class="n p">
                <div class="ab ac ae af ag fd ai aj">
                  <p id="d2ed"
                    class="io ip fg iq b gf ir is it gi iu iv iw ix iy iz ja jb jc jd je jf jg jh ji jj dl gd"><em
                      class="jl">No time to help contribute? Want to give back in other ways? </em><strong
                      class="iq jk"><em class="jl">Become a Backer or Sponsor to webpack by donating to our
                      </em></strong><a href="http://opencollective.com/webpack" class="ck ky"
                      rel="noopener nofollow"><strong class="iq jk"><em class="jl">open
                          collective</em></strong></a><strong class="iq jk"><em class="jl">.</em></strong><em
                      class="jl"> Open Collective not only helps support the Core Team, but also supports contributors
                      who have spent significant time improving our organization on their free time! ❤</em></p>
                </div>
              </div>
            </section>
          </div>
        </article>
        <div class="em dk me ml aj mm u mj mn" data-test-id="post-sidebar">
          <div class="n p">
            <div class="ab ac ae af ag ah ai aj">
              <div class="mo n mp">
                <div class="dk">
                  <div>
                    <div class="mq mr s"><a
                        href="/webpack?source=post_sidebar--------------------------post_sidebar-----------"
                        class="ck cl au av aw ax ay az ba bb cm cn be co cp" rel="noopener">
                        <h2 class="ce jz lq cg ff gd dl">webpack</h2>
                      </a>
                      <div class="ms mt s">
                        <p class="ce b fc cg bk mu hh hi mv hk hl ch">The official Medium publication for the webpack
                          open source…</p>
                      </div>
                      <div class="ib" aria-hidden="false" aria-describedby="collectionFollowPopover"
                        aria-labelledby="collectionFollowPopover"><span><button
                            class="ce b fc cg ho mw hq hr hs ht hu bb hv hw hx hy dd hz ia da ib ic">
                            <div class="n mx">Follow</div>
                          </button></span></div>
                    </div>
                    <div class="my mz na n">
                      <div class="n o">
                        <div class="s ej nb nc nd ne nf"><span><a
                              href="https://medium.com/m/signin?actionUrl=https%3A%2F%2Fmedium.com%2F_%2Fvote%2Fwebpack%2F15fd2dd8f07b&amp;operation=register&amp;redirect=https%3A%2F%2Fmedium.com%2Fwebpack%2Fwebpack-3-official-release-15fd2dd8f07b&amp;source=post_sidebar-----15fd2dd8f07b---------------------clap_sidebar-----------"
                              class="ck cl au av aw ax ay az ba bb cm cn be co cp" rel="noopener">
                              <div class="az ng nh ni nj nk nl nm r nn no"><svg width="29" height="29"
                                  aria-label="clap">
                                  <g fill-rule="evenodd">
                                    <path
                                      d="M13.74 1l.76 2.97.76-2.97zM16.82 4.78l1.84-2.56-1.43-.47zM10.38 2.22l1.84 2.56-.41-3.03zM22.38 22.62a5.11 5.11 0 0 1-3.16 1.61l.49-.45c2.88-2.89 3.45-5.98 1.69-9.21l-1.1-1.94-.96-2.02c-.31-.67-.23-1.18.25-1.55a.84.84 0 0 1 .66-.16c.34.05.66.28.88.6l2.85 5.02c1.18 1.97 1.38 5.12-1.6 8.1M9.1 22.1l-5.02-5.02a1 1 0 0 1 .7-1.7 1 1 0 0 1 .72.3l2.6 2.6a.44.44 0 0 0 .63-.62L6.1 15.04l-1.75-1.75a1 1 0 1 1 1.41-1.41l4.15 4.15a.44.44 0 0 0 .63 0 .44.44 0 0 0 0-.62L6.4 11.26l-1.18-1.18a1 1 0 0 1 0-1.4 1.02 1.02 0 0 1 1.41 0l1.18 1.16L11.96 14a.44.44 0 0 0 .62 0 .44.44 0 0 0 0-.63L8.43 9.22a.99.99 0 0 1-.3-.7.99.99 0 0 1 .3-.7 1 1 0 0 1 1.41 0l7 6.98a.44.44 0 0 0 .7-.5l-1.35-2.85c-.31-.68-.23-1.19.25-1.56a.85.85 0 0 1 .66-.16c.34.06.66.28.88.6L20.63 15c1.57 2.88 1.07 5.54-1.55 8.16a5.62 5.62 0 0 1-5.06 1.65 9.35 9.35 0 0 1-4.93-2.72zM13 6.98l2.56 2.56c-.5.6-.56 1.41-.15 2.28l.26.56-4.25-4.25a.98.98 0 0 1-.12-.45 1 1 0 0 1 .29-.7 1.02 1.02 0 0 1 1.41 0zm8.89 2.06c-.38-.56-.9-.92-1.49-1.01a1.74 1.74 0 0 0-1.34.33c-.38.29-.61.65-.71 1.06a2.1 2.1 0 0 0-1.1-.56 1.78 1.78 0 0 0-.99.13l-2.64-2.64a1.88 1.88 0 0 0-2.65 0 1.86 1.86 0 0 0-.48.85 1.89 1.89 0 0 0-2.67-.01 1.87 1.87 0 0 0-.5.9c-.76-.75-2-.75-2.7-.04a1.88 1.88 0 0 0 0 2.66c-.3.12-.61.29-.87.55a1.88 1.88 0 0 0 0 2.66l.62.62a1.88 1.88 0 0 0-.9 3.16l5.01 5.02c1.6 1.6 3.52 2.64 5.4 2.96a7.16 7.16 0 0 0 1.18.1c1.03 0 2-.25 2.9-.7A5.9 5.9 0 0 0 23 23.24c3.34-3.34 3.08-6.93 1.74-9.17l-2.87-5.04z">
                                    </path>
                                  </g>
                                </svg></div>
                            </a></span></div>
                        <div class="s np nq nr ns nt nu nv">
                          <div class="nw">
                            <p class="ce b fc cg ch"><button class="ck cl au av aw ax ay az ba bb cm cn be co cp">4K
                                <!-- -->
                              </button></p>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="mz s"><button class="nj nh az">
                        <div class="nz n o mx"><svg width="25" height="25" class="r" aria-label="responses">
                            <path
                              d="M19.07 21.12a6.33 6.33 0 0 1-3.53-1.1 7.8 7.8 0 0 1-.7-.52c-.77.21-1.57.32-2.38.32-4.67 0-8.46-3.5-8.46-7.8C4 7.7 7.79 4.2 12.46 4.2c4.66 0 8.46 3.5 8.46 7.8 0 2.06-.85 3.99-2.4 5.45a6.28 6.28 0 0 0 1.14 2.59c.***********.06.7a.69.69 0 0 1-.62.38h-.03zm0-1v.5l.03-.5h-.03zm-3.92-1.64l.21.2a6.09 6.09 0 0 0 3.24 1.54 7.14 7.14 0 0 1-.83-1.84 5.15 5.15 0 0 1-.16-.75 2.4 2.4 0 0 1-.02-.29v-.23l.18-.15a6.6 6.6 0 0 0 2.3-4.96c0-3.82-3.4-6.93-7.6-6.93-4.19 0-7.6 3.11-7.6 6.93 0 3.83 3.41 6.94 7.6 6.94.83 0 1.64-.12 2.41-.35l.28-.08z"
                              fill-rule="evenodd"></path>
                          </svg>
                          <div class="s ej oa ob oc od oe of og oh">
                            <p class="ce b fc cg ch">51
                              <!-- -->
                            </p>
                          </div>
                        </div>
                      </button></div>
                    <div class="im"><span><a
                          href="https://medium.com/m/signin?actionUrl=https%3A%2F%2Fmedium.com%2F_%2Fbookmark%2Fp%2F15fd2dd8f07b&amp;operation=register&amp;redirect=https%3A%2F%2Fmedium.com%2Fwebpack%2Fwebpack-3-official-release-15fd2dd8f07b&amp;source=post_sidebar-----15fd2dd8f07b---------------------bookmark_sidebar-----------"
                          class="ck cl au av aw ax ay az ba bb cm cn be co cp" rel="noopener"><svg width="25"
                            height="25" viewBox="0 0 25 25">
                            <path
                              d="M19 6a2 2 0 0 0-2-2H8a2 2 0 0 0-2 2v14.66h.01c.01.1.05.2.12.28a.5.5 0 0 0 .7.03l5.67-4.12 5.66 4.13a.5.5 0 0 0 .71-.03.5.5 0 0 0 .12-.29H19V6zm-6.84 9.97L7 19.64V6a1 1 0 0 1 1-1h9a1 1 0 0 1 1 1v13.64l-5.16-3.67a.49.49 0 0 0-.68 0z"
                              fill-rule="evenodd"></path>
                          </svg></a></span></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="em dk md me mf mg mh mi mj mk"></div>
        <div>
          <div class="oi dq n mp p">
            <div class="n p">
              <div class="ab ac ae af ag fd ai aj">
                <div class="n oj"></div>
                <div class="n o oj"></div>
                <div class="ok s">
                  <ul class="az ba">
                    <li class="ib bq in ol"><a href="https://medium.com/webpack/tagged/javascript"
                        class="ce b cf om ch on oo ic s lp">JavaScript</a></li>
                    <li class="ib bq in ol"><a href="https://medium.com/webpack/tagged/webpack"
                        class="ce b cf om ch on oo ic s lp">Webpack</a></li>
                    <li class="ib bq in ol"><a href="https://medium.com/webpack/tagged/tech"
                        class="ce b cf om ch on oo ic s lp">Tech</a></li>
                    <li class="ib bq in ol"><a href="https://medium.com/webpack/tagged/technology"
                        class="ce b cf om ch on oo ic s lp">Technology</a></li>
                    <li class="ib bq in ol"><a href="https://medium.com/webpack/tagged/web-development"
                        class="ce b cf om ch on oo ic s lp">Web Development</a></li>
                  </ul>
                </div>
                <div class="ok n gw z">
                  <div class="n mx">
                    <div class="op s"><span class="s oq or os e d">
                        <div class="n o">
                          <div class="s ej nb nc nd ne nf"><span><a
                                href="https://medium.com/m/signin?actionUrl=https%3A%2F%2Fmedium.com%2F_%2Fvote%2Fwebpack%2F15fd2dd8f07b&amp;operation=register&amp;redirect=https%3A%2F%2Fmedium.com%2Fwebpack%2Fwebpack-3-official-release-15fd2dd8f07b&amp;source=post_actions_footer-----15fd2dd8f07b---------------------clap_footer-----------"
                                class="ck cl au av aw ax ay az ba bb cm cn be co cp" rel="noopener">
                                <div class="az ng nh ni nj nk nl nm r nn no"><svg width="25" height="25"
                                    viewBox="0 0 25 25" aria-label="clap">
                                    <g fill-rule="evenodd">
                                      <path
                                        d="M11.74 0l.76 2.97.76-2.97zM14.81 3.78l1.84-2.56-1.42-.47zM8.38 1.22l1.84 2.56L9.8.75zM20.38 21.62a5.11 5.11 0 0 1-3.16 1.61l.49-.45c2.88-2.89 3.45-5.98 1.69-9.21l-1.1-1.94-.96-2.02c-.31-.67-.23-1.18.25-1.55a.84.84 0 0 1 .66-.16c.34.05.66.28.88.6l2.85 5.02c1.18 1.97 1.38 5.12-1.6 8.1M7.1 21.1l-5.02-5.02a1 1 0 0 1 .7-1.7 1 1 0 0 1 .72.3l2.6 2.6a.44.44 0 0 0 .63-.62L4.1 14.04l-1.75-1.75a1 1 0 1 1 1.41-1.41l4.15 4.15a.44.44 0 0 0 .63 0 .44.44 0 0 0 0-.62L4.4 10.26 3.22 9.08a1 1 0 0 1 0-1.4 1.02 1.02 0 0 1 1.41 0l1.18 1.16L9.96 13a.44.44 0 0 0 .62 0 .44.44 0 0 0 0-.63L6.43 8.22a.99.99 0 0 1-.3-.7.99.99 0 0 1 .3-.7 1 1 0 0 1 1.41 0l7 6.98a.44.44 0 0 0 .7-.5l-1.35-2.85c-.31-.68-.23-1.19.25-1.56a.85.85 0 0 1 .66-.16c.34.06.66.28.88.6L18.63 14c1.57 2.88 1.07 5.54-1.55 8.16a5.62 5.62 0 0 1-5.06 1.65 9.35 9.35 0 0 1-4.93-2.72zM11 5.98l2.56 2.56c-.5.6-.56 1.41-.15 2.28l.26.56-4.25-4.25a.98.98 0 0 1-.12-.45 1 1 0 0 1 .29-.7 1.02 1.02 0 0 1 1.41 0zm8.89 2.06c-.38-.56-.9-.92-1.49-1.01a1.74 1.74 0 0 0-1.34.33c-.38.29-.61.65-.71 1.06a2.1 2.1 0 0 0-1.1-.56 1.78 1.78 0 0 0-.99.13l-2.64-2.64a1.88 1.88 0 0 0-2.65 0 1.86 1.86 0 0 0-.48.85 1.89 1.89 0 0 0-2.67-.01 1.87 1.87 0 0 0-.5.9c-.76-.75-2-.75-2.7-.04a1.88 1.88 0 0 0 0 2.66c-.3.12-.61.29-.87.55a1.88 1.88 0 0 0 0 2.66l.62.62a1.88 1.88 0 0 0-.9 3.16l5.01 5.02c1.6 1.6 3.52 2.64 5.4 2.96a7.16 7.16 0 0 0 1.18.1c1.03 0 2-.25 2.9-.7A5.9 5.9 0 0 0 21 22.24c3.34-3.34 3.08-6.93 1.74-9.17l-2.87-5.04z">
                                      </path>
                                    </g>
                                  </svg></div>
                              </a></span></div>
                          <div class="s np nq nr ns nt nu nv">
                            <div class="ej ot nw">
                              <p class="ce b fc cg gd"><button
                                  class="ck cl au av aw ax ay az ba bb cm cn be co cp">4K<span class="s h g f ou ov"> 
                                    <!-- -->claps
                                  </span></button><span class="s h g f ou ov"></span></p>
                            </div>
                          </div>
                        </div>
                      </span><span class="s h g f ou ov">
                        <div class="n bu">
                          <div class="s ej nb nc"><span><a
                                href="https://medium.com/m/signin?actionUrl=https%3A%2F%2Fmedium.com%2F_%2Fvote%2Fwebpack%2F15fd2dd8f07b&amp;operation=register&amp;redirect=https%3A%2F%2Fmedium.com%2Fwebpack%2Fwebpack-3-official-release-15fd2dd8f07b&amp;source=post_actions_footer-----15fd2dd8f07b---------------------clap_footer-----------"
                                class="ck cl au av aw ax ay az ba bb cm cn be co cp" rel="noopener">
                                <div class="az ng nh ni nj nk nl nm r nn no"><svg width="33" height="33"
                                    viewBox="0 0 33 33" aria-label="clap">
                                    <path
                                      d="M28.86 17.34l-3.64-6.4c-.3-.43-.71-.73-1.16-.8a1.12 1.12 0 0 0-.9.21c-.62.5-.73 1.18-.32 2.06l1.22 2.6 1.4 2.45c2.23 4.09 1.51 8-2.15 11.66a9.6 9.6 0 0 1-.8.71 6.53 6.53 0 0 0 4.3-2.1c3.82-3.82 3.57-7.87 2.05-10.39zm-6.25 11.08c3.35-3.35 4-6.78 1.98-10.47L21.2 12c-.3-.43-.71-.72-1.16-.8a1.12 1.12 0 0 0-.9.22c-.62.49-.74 1.18-.32 2.06l1.72 3.63a.5.5 0 0 1-.81.57l-8.91-8.9a1.33 1.33 0 0 0-1.89 1.88l5.3 5.3a.5.5 0 0 1-.71.7l-5.3-5.3-1.49-1.49c-.5-.5-1.38-.5-1.88 0a1.34 1.34 0 0 0 0 1.89l1.49 1.5 5.3 5.28a.5.5 0 0 1-.36.86.5.5 0 0 1-.36-.15l-5.29-5.29a1.34 1.34 0 0 0-1.88 0 1.34 1.34 0 0 0 0 1.89l2.23 2.23L9.3 21.4a.5.5 0 0 1-.36.85.5.5 0 0 1-.35-.14l-3.32-3.33a1.33 1.33 0 0 0-1.89 0 1.32 1.32 0 0 0-.39.95c0 .35.14.69.4.94l6.39 6.4c3.53 3.53 8.86 5.3 12.82 1.35zM12.73 9.26l5.68 5.68-.49-1.04c-.52-1.1-.43-2.13.22-2.89l-3.3-3.3a1.34 1.34 0 0 0-1.88 0 1.33 1.33 0 0 0-.4.94c0 .22.07.42.17.61zm14.79 19.18a7.46 7.46 0 0 1-6.41 2.31 7.92 7.92 0 0 1-3.67.9c-3.05 0-6.12-1.63-8.36-3.88l-6.4-6.4A2.31 2.31 0 0 1 2 19.72a2.33 2.33 0 0 1 1.92-2.3l-.87-.87a2.34 2.34 0 0 1 0-3.3 2.33 2.33 0 0 1 1.24-.64l-.14-.14a2.34 2.34 0 0 1 0-3.3 2.39 2.39 0 0 1 3.3 0l.14.14a2.33 2.33 0 0 1 3.95-1.24l.09.09c.09-.42.29-.83.62-1.16a2.34 2.34 0 0 1 3.3 0l3.38 3.39a2.17 2.17 0 0 1 1.27-.17c.54.08 1.03.35 1.45.76.1-.55.41-1.03.9-1.42a2.12 2.12 0 0 1 1.67-.4 2.8 2.8 0 0 1 1.85 1.25l3.65 6.43c1.7 2.83 2.03 7.37-2.2 11.6zM13.22.48l-1.92.89 2.37 2.83-.45-3.72zm8.48.88L19.78.5l-.44 3.7 2.36-2.84zM16.5 3.3L15.48 0h2.04L16.5 3.3z"
                                      fill-rule="evenodd"></path>
                                  </svg></div>
                              </a></span></div>
                          <div class="s np nq nr ns ow ox oy oz pa pb">
                            <div class="ej ot nw">
                              <p class="ce b fc cg gd"><button
                                  class="ck cl au av aw ax ay az ba bb cm cn be co cp">4K<span class="s h g f ou ov"> 
                                    <!-- -->claps
                                  </span></button><span class="s h g f ou ov"></span></p>
                            </div>
                          </div>
                        </div>
                      </span></div>
                    <div class="s pc pd pe pf pg"></div><button class="nj nh az">
                      <div class="nz n o mx"><span class="ph s h g f ou ov"><svg width="33" height="33"
                            viewBox="0 0 33 33" fill="none" class="r" aria-label="responses">
                            <path clip-rule="evenodd"
                              d="M24.28 25.5l.32-.29c2.11-1.94 3.4-4.61 3.4-7.56C28 11.83 22.92 7 16.5 7S5 11.83 5 17.65s5.08 10.66 11.5 10.66c1.22 0 2.4-.18 3.5-.5l.5-.15.41.33a8.86 8.86 0 0 0 4.68 2.1 7.34 7.34 0 0 1-1.3-4.15v-.43zm1 .45c0 1.5.46 2.62 1.69 **********.01.75-.38.75a9.69 9.69 0 0 1-6.31-2.37c-1.2.35-2.46.54-3.78.54C9.6 29.3 4 24.09 4 17.65 4 11.22 9.6 6 16.5 6S29 11.22 29 17.65c0 3.25-1.42 6.18-3.72 8.3z">
                            </path>
                          </svg></span><span class="pi s oq or os e d"><svg width="25" height="25" class="r"
                            aria-label="responses">
                            <path
                              d="M19.07 21.12a6.33 6.33 0 0 1-3.53-1.1 7.8 7.8 0 0 1-.7-.52c-.77.21-1.57.32-2.38.32-4.67 0-8.46-3.5-8.46-7.8C4 7.7 7.79 4.2 12.46 4.2c4.66 0 8.46 3.5 8.46 7.8 0 2.06-.85 3.99-2.4 5.45a6.28 6.28 0 0 0 1.14 2.59c.***********.06.7a.69.69 0 0 1-.62.38h-.03zm0-1v.5l.03-.5h-.03zm-3.92-1.64l.21.2a6.09 6.09 0 0 0 3.24 1.54 7.14 7.14 0 0 1-.83-1.84 5.15 5.15 0 0 1-.16-.75 2.4 2.4 0 0 1-.02-.29v-.23l.18-.15a6.6 6.6 0 0 0 2.3-4.96c0-3.82-3.4-6.93-7.6-6.93-4.19 0-7.6 3.11-7.6 6.93 0 3.83 3.41 6.94 7.6 6.94.83 0 1.64-.12 2.41-.35l.28-.08z"
                              fill-rule="evenodd"></path>
                          </svg></span>
                        <div class="s ej pj ob pk od pl of pm pn po pp">
                          <p class="ce b fc cg gd">51
                            <!-- --> <span class="s h g f ou ov">response
                              <!-- -->s
                            </span>
                          </p>
                        </div>
                      </div>
                    </button>
                  </div>
                  <div class="n o">
                    <div class="il s ap"><button class="ck cl au av aw ax ay az ba bb cm cn be co cp"
                        aria-label="Share on twitter"><svg width="29" height="29" class="im">
                          <path
                            d="M22.05 7.54a4.47 4.47 0 0 0-3.3-1.46 4.53 4.53 0 0 0-4.53 4.53c0 .35.04.7.08 1.05A12.9 12.9 0 0 1 5 6.89a5.1 5.1 0 0 0-.65 2.26c.03 1.6.83 2.99 2.02 3.79a4.3 4.3 0 0 1-2.02-.57v.08a4.55 4.55 0 0 0 3.63 4.44c-.4.08-.8.13-1.21.16l-.81-.08a4.54 4.54 0 0 0 4.2 3.15 9.56 9.56 0 0 1-5.66 1.94l-1.05-.08c2 1.27 4.38 2.02 6.94 2.02 8.3 0 12.86-6.9 12.84-12.85.02-.24 0-.43 0-.65a8.68 8.68 0 0 0 2.26-2.34c-.82.38-1.7.62-2.6.72a4.37 4.37 0 0 0 1.95-2.51c-.84.53-1.81.9-2.83 1.13z">
                          </path>
                        </svg></button></div>
                    <div class="il s ap"><button class="ck cl au av aw ax ay az ba bb cm cn be co cp"
                        aria-label="Share on linkedin"><svg width="29" height="29" viewBox="0 0 29 29" fill="none"
                          class="im">
                          <path
                            d="M5 6.36C5 5.61 5.63 5 6.4 5h16.2c.77 0 1.4.61 1.4 1.36v16.28c0 .75-.63 1.36-1.4 1.36H6.4c-.77 0-1.4-.6-1.4-1.36V6.36z">
                          </path>
                          <path fill-rule="evenodd" clip-rule="evenodd"
                            d="M10.76 20.9v-8.57H7.89v8.58h2.87zm-1.44-9.75c1 0 1.63-.65 1.63-1.48-.02-.84-.62-1.48-1.6-1.48-.99 0-1.63.64-1.63 1.48 0 .83.62 1.48 1.59 1.48h.01zM12.35 20.9h2.87v-4.79c0-.25.02-.5.1-.7.2-.5.67-1.04 1.46-1.04 1.04 0 1.46.8 1.46 1.95v4.59h2.87v-4.92c0-2.64-1.42-3.87-3.3-3.87-1.55 0-2.23.86-2.61 1.45h.02v-1.24h-2.87c.04.8 0 8.58 0 8.58z"
                            fill="#fff"></path>
                        </svg></button></div>
                    <div class="il s ap"><button class="ck cl au av aw ax ay az ba bb cm cn be co cp"
                        aria-label="Share on facebook"><svg width="29" height="29" class="im">
                          <path
                            d="M23.2 5H5.8a.8.8 0 0 0-.8.8V23.2c0 .*********.8h9.3v-7.13h-2.38V13.9h2.38v-2.38c0-2.45 1.55-3.66 3.74-3.66 1.05 0 1.95.08 2.2.11v2.57h-1.5c-1.2 0-1.48.57-1.48 1.4v1.96h2.97l-.6 2.97h-2.37l.05 7.12h5.1a.8.8 0 0 0 .79-.8V5.8a.8.8 0 0 0-.8-.79">
                          </path>
                        </svg></button></div>
                    <div class="pq s ap">
                      <div class="im"><span><a
                            href="https://medium.com/m/signin?actionUrl=https%3A%2F%2Fmedium.com%2F_%2Fbookmark%2Fp%2F15fd2dd8f07b&amp;operation=register&amp;redirect=https%3A%2F%2Fmedium.com%2Fwebpack%2Fwebpack-3-official-release-15fd2dd8f07b&amp;source=post_actions_footer--------------------------bookmark_footer-----------"
                            class="ck cl au av aw ax ay az ba bb cm cn be co cp" rel="noopener"><svg width="25"
                              height="25" viewBox="0 0 25 25">
                              <path
                                d="M19 6a2 2 0 0 0-2-2H8a2 2 0 0 0-2 2v14.66h.01c.01.1.05.2.12.28a.5.5 0 0 0 .7.03l5.67-4.12 5.66 4.13a.5.5 0 0 0 .71-.03.5.5 0 0 0 .12-.29H19V6zm-6.84 9.97L7 19.64V6a1 1 0 0 1 1-1h9a1 1 0 0 1 1 1v13.64l-5.16-3.67a.49.49 0 0 0-.68 0z"
                                fill-rule="evenodd"></path>
                            </svg></a></span></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div>
              <div class="n p">
                <div class="ab ac ae af ag fd ai aj">
                  <div class="pr bh ps ok s pt z">
                    <div class="s g">
                      <div class="pu pv s ej"><span class="s pw am px">
                          <div class="s t py pz"><a rel="noopener"
                              href="/@TheLarkInn?source=follow_footer-------------------------------------"><img
                                alt="Sean T. Larkin" class="s ha bx qa"
                                src="https://miro.medium.com/fit/c/160/160/1*AtcF5LLmMnXgwTpBMeos-w.jpeg" width="80"
                                height="80" /></a></div><span class="s">
                            <div class="qb s qc">
                              <p class="ce b cf qd qe ch ci">Written by</p>
                            </div>
                            <div class="qb qf n qc">
                              <div class="aj n o gw">
                                <h2 class="ce jz qg qh ff gd"><a class="ck cl au av aw ax ay az ba bb cm cn be co cp"
                                    rel="noopener"
                                    href="/@TheLarkInn?source=follow_footer-------------------------------------">Sean
                                    T. Larkin</a></h2>
                                <div class="s g"><span><button
                                      class="ce b fc cg ho qi hq hr hs ht hu bb hv hw hx hy dd hz ia da ib ic">Follow</button></span>
                                </div>
                              </div>
                            </div>
                          </span>
                        </span>
                        <div class="qb qj s qc bi">
                          <div class="qk s">
                            <p class="ce b lq ql ch">@Webpack Core team &amp; AngularCLI team. Program Manager
                              @Microsoft @EdgeDevTools. Web Performance, JavaScripter, Woodworker, 🐓 Farmer, and
                              Gardener!</p>
                          </div>
                          <div class="qm qn bi"><span><button
                                class="ce b fc cg ho qi hq hr hs ht hu bb hv hw hx hy dd hz ia da ib ic">Follow</button></span>
                          </div>
                        </div>
                      </div>
                      <div class="pr s"></div>
                      <div class="pu pv s ej"><span class="s pw am px">
                          <div class="s t py pz"><a
                              href="/webpack?source=follow_footer-------------------------------------"
                              rel="noopener"><img alt="webpack" class="dd qa bx"
                                src="https://miro.medium.com/fit/c/160/160/1*Wx82vEGrMfW4AdSLodZXgQ.png" width="80"
                                height="80" /></a></div><span class="s">
                            <div class="qb qf n qc">
                              <div class="aj n o gw">
                                <h2 class="ce jz qg qh ff gd"><a
                                    href="/webpack?source=follow_footer-------------------------------------"
                                    class="ck cl au av aw ax ay az ba bb cm cn be co cp" rel="noopener">webpack</a></h2>
                                <div class="s g">
                                  <div class="ib" aria-hidden="false" aria-describedby="collectionFollowPopover"
                                    aria-labelledby="collectionFollowPopover"><span><button
                                        class="ce b fc cg ho qi hq hr hs ht hu bb hv hw hx hy dd hz ia da ib ic">
                                        <div class="n mx">Follow</div>
                                      </button></span></div>
                                </div>
                              </div>
                            </div>
                          </span>
                        </span>
                        <div class="qb qo s qc bi">
                          <div class="qk s">
                            <p class="ce b lq ql ch">The official Medium publication for the webpack open source
                              project!</p>
                          </div>
                          <div class="qm qn bi">
                            <div class="ib" aria-hidden="false" aria-describedby="collectionFollowPopover"
                              aria-labelledby="collectionFollowPopover"><span><button
                                  class="ce b fc cg ho qi hq hr hs ht hu bb hv hw hx hy dd hz ia da ib ic">
                                  <div class="n mx">Follow</div>
                                </button></span></div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="qm bi">
                      <div class="jr s">
                        <div class="n mx">
                          <div class="qp s"><a rel="noopener"
                              href="/@TheLarkInn?source=follow_footer-------------------------------------"><img
                                alt="Sean T. Larkin" class="s ha qq qr"
                                src="https://miro.medium.com/fit/c/80/80/1*AtcF5LLmMnXgwTpBMeos-w.jpeg" width="40"
                                height="40" /></a></div>
                          <div class="hd s">
                            <p class="ce b qs qt qu ch ci">Written by</p>
                            <div class="n mx">
                              <h2 class="ce jz lq cg ff gd"><a class="ck cl au av aw ax ay az ba bb cm cn be co cp"
                                  rel="noopener"
                                  href="/@TheLarkInn?source=follow_footer-------------------------------------">Sean T.
                                  Larkin</a></h2>
                              <div class="hd s"><span><button
                                    class="ce b cf cg ho hp hq hr hs ht hu bb hv hw hx hy dd hz ia da ib ic">Follow</button></span>
                              </div>
                            </div>
                            <div class="qv s">
                              <p class="ce b fc cg ch">@Webpack Core team &amp; AngularCLI team. Program Manager
                                @Microsoft @EdgeDevTools. Web Performance, JavaScripter, Woodworker, 🐓 Farmer, and
                                Gardener!</p>
                            </div>
                          </div>
                        </div>
                        <div class="jr s">
                          <div class="n mx"><a href="/webpack?source=follow_footer-------------------------------------"
                              rel="noopener"><img alt="webpack" class="dd qr qq"
                                src="https://miro.medium.com/fit/c/80/80/1*Wx82vEGrMfW4AdSLodZXgQ.png" width="40"
                                height="40" /></a>
                            <div class="hd s">
                              <div class="n mx">
                                <h2 class="ce jz lq cg ff gd"><a
                                    href="/webpack?source=follow_footer-------------------------------------"
                                    class="ck cl au av aw ax ay az ba bb cm cn be co cp" rel="noopener">webpack</a></h2>
                                <div class="hd s">
                                  <div class="ib" aria-hidden="false" aria-describedby="collectionFollowPopover"
                                    aria-labelledby="collectionFollowPopover"><span><button
                                        class="ce b cf cg ho hp hq hr hs ht hu bb hv hw hx hy dd hz ia da ib ic">
                                        <div class="n mx">Follow</div>
                                      </button></span></div>
                                </div>
                              </div>
                              <div class="qv s">
                                <p class="ce b fc cg ch">The official Medium publication for the webpack open source
                                  project!</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="s db z">
                <div class="n p">
                  <div class="ab ac ae af ag ah ai aj">
                    <div class="qw qx s">
                      <div class="qy mr qz qx s ra rb">
                        <h2 class="ce jz la gh kc lc gk kf gl gn kj go gq kn gr gt kr gd">More From Medium</h2>
                      </div>
                      <div class="bu n mx oj rc rd re rf rg rh ri rj rk rl rm rn ro rp rq">
                        <div class="rr rs rt ds ru rv rw du rx ry rz sa sb sc sd se sf sg sh si sj">
                          <div class="sk sl s">
                            <div class="aj eo">
                              <div class="n gw">
                                <div class="s bp sm sn so">
                                  <div class="sp s">
                                    <h2 class="ce jz sq sr kc ss st kf su sv kj sw sx kn sy sz kr gd"><a rel="noopener"
                                        href="/webpack/better-tree-shaking-with-deep-scope-analysis-a0b788c0ce77?source=post_internal_links---------0----------------------------">Better
                                        tree shaking with deep scope analysis</a></h2>
                                  </div>
                                  <div class="o n">
                                    <div></div>
                                    <div class="aj s">
                                      <div class="n">
                                        <div style="flex:1"><span class="ce b fc cg gd">
                                            <div class="cq n o hf"><span class="ce b cf cg gd"><a
                                                  href="https://okcdz.medium.com/?source=post_internal_links---------0----------------------------"
                                                  class="ck cl au av aw ax ay az ba bb hm be co cp"
                                                  rel="noopener">Vincent Chan</a><span>
                                                  <!-- -->in
                                                  <!-- --> <a
                                                    href="/webpack?source=post_internal_links---------0----------------------------"
                                                    class="ck cl au av aw ax ay az ba bb hm be co cp"
                                                    rel="noopener">webpack</a>
                                                </span></span></div>
                                          </span></div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div class="hd in s ta tb"><a class="ck cl au av aw ax ay az ba bb cm cn be co cp s"
                                    rel="noopener"
                                    href="/webpack/better-tree-shaking-with-deep-scope-analysis-a0b788c0ce77?source=post_internal_links---------0----------------------------">
                                    <div class="er s ej es">
                                      <div class="tc eu s">
                                        <div class="em en t u v eo aj bk ep eq"><img class="t u v eo aj ev ew ex"
                                            src="https://miro.medium.com/max/60/1*LF_LYj06_VWwk-q6WEH0-w.png?q=20"
                                            width="70" height="70" role="presentation" /></div><img
                                          class="em en td te tf tg th ti tj tk tl tm c" width="70" height="70"
                                          role="presentation" /><noscript><img class="td te tf tg th ti tj tk tl tm"
                                            src="https://miro.medium.com/fit/c/140/140/1*LF_LYj06_VWwk-q6WEH0-w.png"
                                            width="70" height="70"
                                            srcSet="https://miro.medium.com/fit/c/96/140/1*LF_LYj06_VWwk-q6WEH0-w.png 48w, https://miro.medium.com/fit/c/140/140/1*LF_LYj06_VWwk-q6WEH0-w.png 70w"
                                            sizes="70px" role="presentation" /></noscript>
                                      </div>
                                    </div>
                                  </a></div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div class="rr rs rt ds ru rv rw du rx ry rz sa sb sc sd se sf sg sh si sj">
                          <div class="sk sl s">
                            <div class="aj eo">
                              <div class="n gw">
                                <div class="s bp sm sn so">
                                  <div class="sp s">
                                    <h2 class="ce jz sq sr kc ss st kf su sv kj sw sx kn sy sz kr gd"><a rel="noopener"
                                        href="/swlh/intro-to-recoil-d689a77c5f04?source=post_internal_links---------1----------------------------">Intro
                                        to Recoil</a></h2>
                                  </div>
                                  <div class="o n">
                                    <div></div>
                                    <div class="aj s">
                                      <div class="n">
                                        <div style="flex:1"><span class="ce b fc cg gd">
                                            <div class="cq n o hf"><span class="ce b cf cg gd"><a
                                                  class="ck cl au av aw ax ay az ba bb hm be co cp" rel="noopener"
                                                  href="/@TRomesh?source=post_internal_links---------1----------------------------">Tharaka
                                                  Romesh</a><span>
                                                  <!-- -->in
                                                  <!-- --> <a
                                                    href="/swlh?source=post_internal_links---------1----------------------------"
                                                    class="ck cl au av aw ax ay az ba bb hm be co cp" rel="noopener">The
                                                    Startup</a>
                                                </span></span></div>
                                          </span></div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div class="hd in s ta tb"><a class="ck cl au av aw ax ay az ba bb cm cn be co cp s"
                                    rel="noopener"
                                    href="/swlh/intro-to-recoil-d689a77c5f04?source=post_internal_links---------1----------------------------">
                                    <div class="er s ej es">
                                      <div class="tc eu s">
                                        <div class="em en t u v eo aj bk ep eq"><img class="t u v eo aj ev ew ex"
                                            src="https://miro.medium.com/max/60/1*wNaUljI2sG7HeDKQMJwqHQ.png?q=20"
                                            width="70" height="70" role="presentation" /></div><img
                                          class="em en td te tf tg th ti tj tk tl tm c" width="70" height="70"
                                          role="presentation" /><noscript><img class="td te tf tg th ti tj tk tl tm"
                                            src="https://miro.medium.com/fit/c/140/140/1*wNaUljI2sG7HeDKQMJwqHQ.png"
                                            width="70" height="70"
                                            srcSet="https://miro.medium.com/fit/c/96/140/1*wNaUljI2sG7HeDKQMJwqHQ.png 48w, https://miro.medium.com/fit/c/140/140/1*wNaUljI2sG7HeDKQMJwqHQ.png 70w"
                                            sizes="70px" role="presentation" /></noscript>
                                      </div>
                                    </div>
                                  </a></div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div class="rr rs rt ds ru rv rw du rx ry rz sa sb sc sd se sf sg sh si sj">
                          <div class="sk sl s">
                            <div class="aj eo">
                              <div class="n gw">
                                <div class="s bp sm sn so">
                                  <div class="sp s">
                                    <h2 class="ce jz sq sr kc ss st kf su sv kj sw sx kn sy sz kr gd"><a rel="noopener"
                                        href="/swlh/what-if-chrome-os-was-a-phone-operating-system-212d813b2798?source=post_internal_links---------2----------------------------">What
                                        If Chrome OS Was a Phone Operating System?</a></h2>
                                  </div>
                                  <div class="o n">
                                    <div></div>
                                    <div class="aj s">
                                      <div class="n">
                                        <div style="flex:1"><span class="ce b fc cg gd">
                                            <div class="cq n o hf"><span class="ce b cf cg gd"><a
                                                  href="https://omarzahran.medium.com/?source=post_internal_links---------2----------------------------"
                                                  class="ck cl au av aw ax ay az ba bb hm be co cp" rel="noopener">Omar
                                                  Zahran</a><span>
                                                  <!-- -->in
                                                  <!-- --> <a
                                                    href="/swlh?source=post_internal_links---------2----------------------------"
                                                    class="ck cl au av aw ax ay az ba bb hm be co cp" rel="noopener">The
                                                    Startup</a>
                                                </span></span></div>
                                          </span></div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div class="hd in s ta tb"><a class="ck cl au av aw ax ay az ba bb cm cn be co cp s"
                                    rel="noopener"
                                    href="/swlh/what-if-chrome-os-was-a-phone-operating-system-212d813b2798?source=post_internal_links---------2----------------------------">
                                    <div class="er s ej es">
                                      <div class="tc eu s">
                                        <div class="em en t u v eo aj bk ep eq"><img class="t u v eo aj ev ew ex"
                                            src="https://miro.medium.com/max/60/0*fvLeveZAwD-r-UXc?q=20" width="70"
                                            height="70" role="presentation" /></div><img
                                          class="em en td te tf tg th ti tj tk tl tm c" width="70" height="70"
                                          role="presentation" /><noscript><img class="td te tf tg th ti tj tk tl tm"
                                            src="https://miro.medium.com/fit/c/140/140/0*fvLeveZAwD-r-UXc" width="70"
                                            height="70"
                                            srcSet="https://miro.medium.com/fit/c/96/140/0*fvLeveZAwD-r-UXc 48w, https://miro.medium.com/fit/c/140/140/0*fvLeveZAwD-r-UXc 70w"
                                            sizes="70px" role="presentation" /></noscript>
                                      </div>
                                    </div>
                                  </a></div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div class="rr rs rt ds ru rv rw du rx ry rz sa sb sc sd se sf sg sh si sj">
                          <div class="sk sl s">
                            <div class="aj eo">
                              <div class="n gw">
                                <div class="s bp sm sn so">
                                  <div class="sp s">
                                    <h2 class="ce jz sq sr kc ss st kf su sv kj sw sx kn sy sz kr gd"><a rel="noopener"
                                        href="/the-guild/this-is-how-i-build-babel-plug-ins-b0a13dcd0352?source=post_internal_links---------3----------------------------">This
                                        is how I build Babel plug-ins</a></h2>
                                  </div>
                                  <div class="o n">
                                    <div></div>
                                    <div class="aj s">
                                      <div class="n">
                                        <div style="flex:1"><span class="ce b fc cg gd">
                                            <div class="cq n o hf"><span class="ce b cf cg gd"><a
                                                  href="https://eytanmanor.medium.com/?source=post_internal_links---------3----------------------------"
                                                  class="ck cl au av aw ax ay az ba bb hm be co cp" rel="noopener">Eytan
                                                  Manor</a><span>
                                                  <!-- -->in
                                                  <!-- --> <a
                                                    href="/the-guild?source=post_internal_links---------3----------------------------"
                                                    class="ck cl au av aw ax ay az ba bb hm be co cp" rel="noopener">The
                                                    Guild</a>
                                                </span></span></div>
                                          </span></div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div class="hd in s ta tb"><a class="ck cl au av aw ax ay az ba bb cm cn be co cp s"
                                    rel="noopener"
                                    href="/the-guild/this-is-how-i-build-babel-plug-ins-b0a13dcd0352?source=post_internal_links---------3----------------------------">
                                    <div class="er s ej es">
                                      <div class="tc eu s">
                                        <div class="em en t u v eo aj bk ep eq"><img class="t u v eo aj ev ew ex"
                                            src="https://miro.medium.com/max/60/1*cW9IvHMO5fBRNbjw2NW44Q.png?q=20"
                                            width="70" height="70" role="presentation" /></div><img
                                          class="em en td te tf tg th ti tj tk tl tm c" width="70" height="70"
                                          role="presentation" /><noscript><img class="td te tf tg th ti tj tk tl tm"
                                            src="https://miro.medium.com/fit/c/140/140/1*cW9IvHMO5fBRNbjw2NW44Q.png"
                                            width="70" height="70"
                                            srcSet="https://miro.medium.com/fit/c/96/140/1*cW9IvHMO5fBRNbjw2NW44Q.png 48w, https://miro.medium.com/fit/c/140/140/1*cW9IvHMO5fBRNbjw2NW44Q.png 70w"
                                            sizes="70px" role="presentation" /></noscript>
                                      </div>
                                    </div>
                                  </a></div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div class="rr rs rt ds ru rv rw du rx ry rz sa sb sc sd se sf sg sh si sj">
                          <div class="sk sl s">
                            <div class="aj eo">
                              <div class="n gw">
                                <div class="s bp sm sn so">
                                  <div class="sp s">
                                    <h2 class="ce jz sq sr kc ss st kf su sv kj sw sx kn sy sz kr gd"><a
                                        href="https://premeena.medium.com/webpack-react-optimised-from-scratch-da8f75024ba4?source=post_internal_links---------4----------------------------"
                                        rel="noopener">Webpack + React Optimised from scratch</a></h2>
                                  </div>
                                  <div class="o n">
                                    <div></div>
                                    <div class="aj s">
                                      <div class="n">
                                        <div style="flex:1"><span class="ce b fc cg gd">
                                            <div class="cq n o hf"><span class="ce b cf cg gd"><a
                                                  href="https://premeena.medium.com/?source=post_internal_links---------4----------------------------"
                                                  class="ck cl au av aw ax ay az ba bb hm be co cp"
                                                  rel="noopener">Shailesh Jha</a></span></div>
                                          </span></div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div class="hd in s ta tb"><a
                                    href="https://premeena.medium.com/webpack-react-optimised-from-scratch-da8f75024ba4?source=post_internal_links---------4----------------------------"
                                    class="ck cl au av aw ax ay az ba bb cm cn be co cp s" rel="noopener">
                                    <div class="er s ej es">
                                      <div class="tc eu s">
                                        <div class="em en t u v eo aj bk ep eq"><img class="t u v eo aj ev ew ex"
                                            src="https://miro.medium.com/max/60/0*nBNPobrQPLNmVp_a?q=20" width="70"
                                            height="70" role="presentation" /></div><img
                                          class="em en td te tf tg th ti tj tk tl tm c" width="70" height="70"
                                          role="presentation" /><noscript><img class="td te tf tg th ti tj tk tl tm"
                                            src="https://miro.medium.com/fit/c/140/140/0*nBNPobrQPLNmVp_a" width="70"
                                            height="70"
                                            srcSet="https://miro.medium.com/fit/c/96/140/0*nBNPobrQPLNmVp_a 48w, https://miro.medium.com/fit/c/140/140/0*nBNPobrQPLNmVp_a 70w"
                                            sizes="70px" role="presentation" /></noscript>
                                      </div>
                                    </div>
                                  </a></div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div class="rr rs rt ds ru rv rw du rx ry rz sa sb sc sd se sf sg sh si sj">
                          <div class="sk sl s">
                            <div class="aj eo">
                              <div class="n gw">
                                <div class="s bp sm sn so">
                                  <div class="sp s">
                                    <h2 class="ce jz sq sr kc ss st kf su sv kj sw sx kn sy sz kr gd"><a rel="noopener"
                                        href="/dooboolab/how-to-update-relay-cache-d04ee074eb98?source=post_internal_links---------5----------------------------">How
                                        to Update Relay Cache</a></h2>
                                  </div>
                                  <div class="o n">
                                    <div></div>
                                    <div class="aj s">
                                      <div class="n">
                                        <div style="flex:1"><span class="ce b fc cg gd">
                                            <div class="cq n o hf"><span class="ce b cf cg gd"><a
                                                  href="https://0916dhkim.medium.com/?source=post_internal_links---------5----------------------------"
                                                  class="ck cl au av aw ax ay az ba bb hm be co cp"
                                                  rel="noopener">Donghyeon Kim</a><span>
                                                  <!-- -->in
                                                  <!-- --> <a
                                                    href="/dooboolab?source=post_internal_links---------5----------------------------"
                                                    class="ck cl au av aw ax ay az ba bb hm be co cp"
                                                    rel="noopener">dooboolab</a>
                                                </span></span></div>
                                          </span></div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div class="hd in s ta tb"><a class="ck cl au av aw ax ay az ba bb cm cn be co cp s"
                                    rel="noopener"
                                    href="/dooboolab/how-to-update-relay-cache-d04ee074eb98?source=post_internal_links---------5----------------------------">
                                    <div class="er s ej es">
                                      <div class="tc eu s">
                                        <div class="em en t u v eo aj bk ep eq"><img class="t u v eo aj ev ew ex"
                                            src="https://miro.medium.com/max/60/0*R7W2uXa4tMUvJrqS?q=20" width="70"
                                            height="70" role="presentation" /></div><img
                                          class="em en td te tf tg th ti tj tk tl tm c" width="70" height="70"
                                          role="presentation" /><noscript><img class="td te tf tg th ti tj tk tl tm"
                                            src="https://miro.medium.com/fit/c/140/140/0*R7W2uXa4tMUvJrqS" width="70"
                                            height="70"
                                            srcSet="https://miro.medium.com/fit/c/96/140/0*R7W2uXa4tMUvJrqS 48w, https://miro.medium.com/fit/c/140/140/0*R7W2uXa4tMUvJrqS 70w"
                                            sizes="70px" role="presentation" /></noscript>
                                      </div>
                                    </div>
                                  </a></div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div class="rr rs rt ds ru rv rw du rx ry rz sa sb sc sd se sf sg sh si sj">
                          <div class="sk sl s">
                            <div class="aj eo">
                              <div class="n gw">
                                <div class="s bp sm sn so">
                                  <div class="sp s">
                                    <h2 class="ce jz sq sr kc ss st kf su sv kj sw sx kn sy sz kr gd"><a
                                        href="https://toastui.medium.com/improving-the-reactivity-system-feat-toast-ui-grid-26ffc025864d?source=post_internal_links---------6----------------------------"
                                        rel="noopener">Improving the Reactivity System (feat. TOAST UI Grid)</a></h2>
                                  </div>
                                  <div class="o n">
                                    <div></div>
                                    <div class="aj s">
                                      <div class="n">
                                        <div style="flex:1"><span class="ce b fc cg gd">
                                            <div class="cq n o hf"><span class="ce b cf cg gd"><a
                                                  href="https://toastui.medium.com/?source=post_internal_links---------6----------------------------"
                                                  class="ck cl au av aw ax ay az ba bb hm be co cp" rel="noopener">TOAST
                                                  UI</a></span></div>
                                          </span></div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div class="hd in s ta tb"><a
                                    href="https://toastui.medium.com/improving-the-reactivity-system-feat-toast-ui-grid-26ffc025864d?source=post_internal_links---------6----------------------------"
                                    class="ck cl au av aw ax ay az ba bb cm cn be co cp s" rel="noopener">
                                    <div class="er s ej es">
                                      <div class="tc eu s">
                                        <div class="em en t u v eo aj bk ep eq"><img class="t u v eo aj ev ew ex"
                                            src="https://miro.medium.com/max/60/1*9p8FLpoYHEOc_8bdG0J08A.png?q=20"
                                            width="70" height="70" role="presentation" /></div><img
                                          class="em en td te tf tg th ti tj tk tl tm c" width="70" height="70"
                                          role="presentation" /><noscript><img class="td te tf tg th ti tj tk tl tm"
                                            src="https://miro.medium.com/fit/c/140/140/1*9p8FLpoYHEOc_8bdG0J08A.png"
                                            width="70" height="70"
                                            srcSet="https://miro.medium.com/fit/c/96/140/1*9p8FLpoYHEOc_8bdG0J08A.png 48w, https://miro.medium.com/fit/c/140/140/1*9p8FLpoYHEOc_8bdG0J08A.png 70w"
                                            sizes="70px" role="presentation" /></noscript>
                                      </div>
                                    </div>
                                  </a></div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div class="rr rs rt ds ru rv rw du rx ry rz sa sb sc sd se sf sg sh si sj">
                          <div class="sk sl s">
                            <div class="aj eo">
                              <div class="n gw">
                                <div class="s bp sm sn so">
                                  <div class="sp s">
                                    <h2 class="ce jz sq sr kc ss st kf su sv kj sw sx kn sy sz kr gd"><a
                                        href="https://levelup.gitconnected.com/understanding-micro-frontends-webpack5-configurations-step-by-step-4dd2f7d81dcb?source=post_internal_links---------7----------------------------"
                                        rel="noopener nofollow">Understanding Micro-Frontends Webpack 5 Configurations
                                        Step by Step</a></h2>
                                  </div>
                                  <div class="o n">
                                    <div></div>
                                    <div class="aj s">
                                      <div class="n">
                                        <div style="flex:1"><span class="ce b fc cg gd">
                                            <div class="cq n o hf"><span class="ce b cf cg gd"><a
                                                  href="https://ranyel.medium.com/?source=post_internal_links---------7----------------------------"
                                                  class="ck cl au av aw ax ay az ba bb hm be co cp" rel="noopener">Rany
                                                  ElHousieny</a><span>
                                                  <!-- -->in
                                                  <!-- --> <a
                                                    href="https://levelup.gitconnected.com/?source=post_internal_links---------7----------------------------"
                                                    class="ck cl au av aw ax ay az ba bb hm be co cp"
                                                    rel="noopener nofollow">Level Up Coding</a>
                                                </span></span></div>
                                          </span></div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div class="hd in s ta tb"><a
                                    href="https://levelup.gitconnected.com/understanding-micro-frontends-webpack5-configurations-step-by-step-4dd2f7d81dcb?source=post_internal_links---------7----------------------------"
                                    class="ck cl au av aw ax ay az ba bb cm cn be co cp s" rel="noopener nofollow">
                                    <div class="er s ej es">
                                      <div class="tc eu s">
                                        <div class="em en t u v eo aj bk ep eq"><img class="t u v eo aj ev ew ex"
                                            src="https://miro.medium.com/freeze/max/60/1*6js53mDPNElWcqe_NTDOIQ.gif?q=20"
                                            width="70" height="70" role="presentation" /></div><img
                                          class="em en td te tf tg th ti tj tk tl tm c" width="70" height="70"
                                          role="presentation" /><noscript><img class="td te tf tg th ti tj tk tl tm"
                                            src="https://miro.medium.com/fit/c/140/140/1*6js53mDPNElWcqe_NTDOIQ.gif"
                                            width="70" height="70"
                                            srcSet="https://miro.medium.com/fit/c/96/140/1*6js53mDPNElWcqe_NTDOIQ.gif 48w, https://miro.medium.com/fit/c/140/140/1*6js53mDPNElWcqe_NTDOIQ.gif 70w"
                                            sizes="70px" role="presentation" /></noscript>
                                      </div>
                                    </div>
                                  </a></div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="tn s to tp">
          <div class="n p">
            <div class="ab ac ae af ag ah ai aj">
              <div class="tq tr pu n gw g">
                <div class="ts n gw">
                  <div class="tt s tu">
                    <div class="sp s"><a
                        href="https://medium.com/about?autoplay=1&amp;source=post_page-----15fd2dd8f07b--------------------------------"
                        class="ck cl au av aw ax ay az ba bb tv tw be tx ty" rel="noopener">
                        <h2 class="ce jz tz ql ff ua">Learn more.</h2>
                      </a></div>
                    <p class="ce b fc cg ub">Medium
                      <!-- --> is an open platform where 170 million readers come to find insightful and dynamic
                      thinking. Here, expert and undiscovered voices alike dive into the heart of any topic and bring
                      new ideas to the surface.
                      <!-- --> <a
                        href="https://medium.com/about?autoplay=1&amp;source=post_page-----15fd2dd8f07b--------------------------------"
                        class="ck cl au av aw ax ay az ba bb be tx ty ky" rel="noopener">Learn more</a>
                    </p>
                  </div>
                  <div class="tt s tu">
                    <div class="sp s"><a
                        href="https://medium.com/topics?source=post_page-----15fd2dd8f07b--------------------------------"
                        class="ck cl au av aw ax ay az ba bb tv tw be tx ty" rel="noopener">
                        <h2 class="ce jz tz ql ff ua">Make
                          <!-- -->Medium
                          <!-- --> yours.
                        </h2>
                      </a></div>
                    <p class="ce b fc cg ub">Follow the writers, publications, and topics that matter to you, and you’ll
                      see them on your homepage and in your inbox.
                      <!-- --> <a
                        href="https://medium.com/topics?source=post_page-----15fd2dd8f07b--------------------------------"
                        class="ck cl au av aw ax ay az ba bb be tx ty ky" rel="noopener">Explore</a>
                    </p>
                  </div>
                  <div class="tt s tu">
                    <div class="sp s"><a
                        href="https://about.medium.com/creators/?source=post_page-----15fd2dd8f07b--------------------------------"
                        class="ck cl au av aw ax ay az ba bb tv tw be tx ty" rel="noopener">
                        <h2 class="ce jz tz ql ff ua">Share your thinking.</h2>
                      </a></div>
                    <p class="ce b fc cg ub">If you have a story to tell, knowledge to share, or a perspective to offer
                      — welcome home. It’s easy and free to post your thinking on any topic.
                      <!-- --> <a
                        href="https://about.medium.com/creators/?source=post_page-----15fd2dd8f07b--------------------------------"
                        class="ck cl au av aw ax ay az ba bb be tx ty ky" rel="noopener">Write on
                        <!-- -->Medium
                      </a>
                    </p>
                  </div>
                </div>
              </div>
              <div class="n mp">
                <div class="n o gw"><a class="ck cl au av aw ax ay az ba bb tv tw be tx ty" rel="noopener"
                    href="/?source=post_page-----15fd2dd8f07b--------------------------------"><svg
                      viewBox="0 0 3940 610" class="hq uc">
                      <path
                        d="M594.79 308.2c0 163.76-131.85 296.52-294.5 296.52S5.8 472 5.8 308.2 137.65 11.69 300.29 11.69s294.5 132.75 294.5 296.51M917.86 308.2c0 154.16-65.93 279.12-147.25 279.12s-147.25-125-147.25-279.12S689.29 29.08 770.61 29.08s147.25 125 147.25 279.12M1050 308.2c0 138.12-23.19 250.08-51.79 250.08s-51.79-112-51.79-250.08 23.19-250.08 51.8-250.08S1050 170.09 1050 308.2M1862.77 37.4l.82-.18v-6.35h-167.48l-155.51 365.5-155.51-365.5h-180.48v6.35l.81.18c30.57 6.9 46.09 17.19 46.09 54.3v434.45c0 37.11-15.58 47.4-46.15 54.3l-.81.18V587H1327v-6.35l-.81-.18c-30.57-6.9-46.09-17.19-46.09-54.3V116.9L1479.87 587h11.33l205.59-483.21V536.9c-2.62 29.31-18 38.36-45.68 44.61l-.82.19v6.3h213.3v-6.3l-.82-.19c-27.71-6.25-43.46-15.3-46.08-44.61l-.14-445.2h.14c0-37.11 15.52-47.4 46.08-54.3m97.43 287.8c3.49-78.06 31.52-134.4 78.56-135.37 14.51.24 26.68 5 36.14 14.16 20.1 19.51 29.55 60.28 28.09 121.21zm-2.11 22h250v-1.05c-.71-59.69-18-106.12-51.34-138-28.82-27.55-71.49-42.71-116.31-42.71h-1c-23.26 0-51.79 5.64-72.09 15.86-23.11 10.7-43.49 26.7-60.45 47.7-27.3 33.83-43.84 79.55-47.86 130.93-.13 1.54-.24 3.08-.35 4.62s-.18 2.92-.25 4.39a332.64 332.64 0 0 0-.36 21.69C1860.79 507 1923.65 600 2035.3 600c98 0 155.07-71.64 169.3-167.8l-7.19-2.53c-25 51.68-69.9 83-121 79.18-69.76-5.22-123.2-75.95-118.35-161.63m532.69 157.68c-8.2 19.45-25.31 30.15-48.24 30.15s-43.89-15.74-58.78-44.34c-16-30.7-24.42-74.1-24.42-125.51 0-107 33.28-176.21 84.79-176.21 21.57 0 38.55 10.7 46.65 29.37zm165.84 76.28c-30.57-7.23-46.09-18-46.09-57V5.28L2424.77 60v6.7l1.14-.09c25.62-2.07 43 1.47 53.09 10.79 7.9 7.3 11.75 18.5 11.75 34.26v71.14c-18.31-11.69-40.09-17.38-66.52-17.38-53.6 0-102.59 22.57-137.92 63.56-36.83 42.72-56.3 101.1-56.3 168.81C2230 518.72 2289.53 600 2378.13 600c51.83 0 93.53-28.4 112.62-76.3V588h166.65v-6.66zm159.29-505.33c0-37.76-28.47-66.24-66.24-66.24-37.59 0-67 29.1-67 66.24s29.44 66.24 67 66.24c37.77 0 66.24-28.48 66.24-66.24m43.84 505.33c-30.57-7.23-46.09-18-46.09-57h-.13V166.65l-166.66 47.85v6.5l1 .09c36.06 3.21 45.93 15.63 45.93 57.77V588h166.8v-6.66zm427.05 0c-30.57-7.23-46.09-18-46.09-57V166.65L3082 212.92v6.52l.94.1c29.48 3.1 38 16.23 38 58.56v226c-9.83 19.45-28.27 31-50.61 31.78-36.23 0-56.18-24.47-56.18-68.9V166.66l-166.66 47.85V221l1 .09c36.06 3.2 45.94 15.62 45.94 57.77v191.27a214.48 214.48 0 0 0 3.47 39.82l3 13.05c14.11 50.56 51.08 77 109 77 49.06 0 92.06-30.37 111-77.89v66h166.66v-6.66zM3934.2 588v-6.67l-.81-.19c-33.17-7.65-46.09-22.07-46.09-51.43v-243.2c0-75.83-42.59-121.09-113.93-121.09-52 0-95.85 30.05-112.73 76.86-13.41-49.6-52-76.86-109.06-76.86-50.12 0-89.4 26.45-106.25 71.13v-69.87l-166.66 45.89v6.54l1 .09c35.63 3.16 45.93 15.94 45.93 57V588h155.5v-6.66l-.82-.2c-26.46-6.22-35-17.56-35-46.66V255.72c7-16.35 21.11-35.72 49-35.72 34.64 0 52.2 24 52.2 71.28V588h155.54v-6.66l-.82-.2c-26.46-6.22-35-17.56-35-46.66v-248a160.45 160.45 0 0 0-2.2-27.68c7.42-17.77 22.34-38.8 51.37-38.8 35.13 0 52.2 23.31 52.2 71.28V588z">
                      </path>
                    </svg></a>
                  <div class="qv ud n gw ue am">
                    <p class="ce b lq ql ua"><a
                        href="https://medium.com/about?autoplay=1&amp;source=post_page-----15fd2dd8f07b--------------------------------"
                        class="ck cl au av aw ax ay az ba bb hm be tx ty" rel="noopener">About</a></p>
                    <p class="ce b lq ql ua"><a
                        href="https://help.medium.com/hc/en-us?source=post_page-----15fd2dd8f07b--------------------------------"
                        class="ck cl au av aw ax ay az ba bb hm be tx ty" rel="noopener">Help</a></p>
                    <p class="ce b lq ql ua"><a
                        href="https://policy.medium.com/medium-terms-of-service-9db0094a1e0f?source=post_page-----15fd2dd8f07b--------------------------------"
                        class="ck cl au av aw ax ay az ba bb hm be tx ty" rel="noopener">Legal</a></p>
                  </div>
                </div>
                <div class="qm uf ug am">
                  <p class="ce b lq ql ub">Get the Medium app</p>
                </div>
                <div class="qm uf uh am ui">
                  <div class="uj s"><a
                      href="https://itunes.apple.com/app/medium-everyones-stories/id828256236?pt=698524&amp;mt=8&amp;ct=post_page&amp;source=post_page-----15fd2dd8f07b--------------------------------"
                      class="ck cl au av aw ax ay az ba bb tv tw be tx ty" rel="noopener nofollow"><img
                        alt="A button that says &#x27;Download on the App Store&#x27;, and if clicked it will lead you to the iOS App store"
                        class="" src="https://miro.medium.com/max/270/1*Crl55Tm6yDNMoucPo1tvDg.png" width="135"
                        height="41" /></a></div>
                  <div class="s"><a
                      href="https://play.google.com/store/apps/details?id=com.medium.reader&amp;source=post_page-----15fd2dd8f07b--------------------------------"
                      class="ck cl au av aw ax ay az ba bb tv tw be tx ty" rel="noopener nofollow"><img
                        alt="A button that says &#x27;Get it on, Google Play&#x27;, and if clicked it will lead you to the Google Play store"
                        class="" src="https://miro.medium.com/max/270/1*W_RAPQ62h0em559zluJLdQ.png" width="135"
                        height="41" /></a></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <script>window.__BUILD_ID__ = "main-********-183757-c310e81f10"</script>
  <script>window.__GRAPHQL_URI__ = "https://medium.com/_/graphql"</script>
  <script>window.__PRELOADED_STATE__ = { "auroraPage": { "isAuroraPageEnabled": false }, "bookReader": { "assets": {}, "reader": { "currentAsset": null, "settingsPanelIsOpen": false, "settings": { "fontFamily": "FELL", "fontScale": "M", "publisherStyling": true, "textAlignment": "START", "theme": "White", "lineSpacing": 0, "wordSpacing": 0, "letterSpacing": 0 }, "internalNavCounter": 0 } }, "cache": { "experimentGroupSet": true, "group": "control", "tags": [], "serverVariantState": "" }, "client": { "isUs": false, "isNativeMedium": false, "isSafariMobile": false, "isSafari": false, "routingEntity": { "type": "DEFAULT", "explicit": false }, "supportsWebp": false }, "config": { "nodeEnv": "production", "version": "main-********-183757-c310e81f10", "isTaggedVersion": false, "target": "production", "productName": "Medium", "publicUrl": "https:\u002F\u002Fcdn-client.medium.com\u002Flite", "authDomain": "medium.com", "authGoogleClientId": "************-k1k6qe060s2tp2a2jam4ljdcms00sttg.apps.googleusercontent.com", "favicon": "production", "glyphUrl": "https:\u002F\u002Fglyph.medium.com", "branchKey": "key_live_ofxXr2qTrrU9NqURK8ZwEhknBxiI6KBm", "lightStep": { "name": "lite-web", "host": "lightstep.medium.systems", "token": "ce5be895bef60919541332990ac9fef2", "appVersion": "main-********-183757-c310e81f10" }, "algolia": { "appId": "MQ57UUUQZ2", "apiKeySearch": "********************************", "indexPrefix": "medium_", "host": "-dsn.algolia.net" }, "recaptchaKey": "6Lfc37IUAAAAAKGGtC6rLS13R1Hrw_BqADfS1LRk", "recaptcha3Key": "6Lf8R9wUAAAAABMI_85Wb8melS7Zj6ziuf99Yot5", "datadog": { "clientToken": "pub853ea8d17ad6821d9f8f11861d23dfed", "context": { "deployment": { "target": "production", "tag": "main-********-183757-c310e81f10", "commit": "c310e81f1083a2494066966a3d5a2eb55e2d87ae" } }, "datacenter": "us" }, "isAmp": false, "googleAnalyticsCode": "UA-24232453-2", "signInWallCustomDomainCollectionIds": ["3a8144eabfe3", "336d898217ee", "61061eb0c96b", "138adf9c44c", "819cc2aaeee0"], "mediumOwnedAndOperatedCollectionIds": ["544c7006046e", "bcc38c8f6edf", "444d13b52878", "8d6b8a439e32", "92d2092dc598", "1285ba81cada", "cb8577c9149e", "8ccfed20cbb2", "ae2a65f35510", "3f6ecf56618", "7b6769f2748b", "fc8964313712", "ef8e90590e66", "191186aaafa0", "d944778ce714", "bdc4052bbdba", "88d9857e584e", "9dc80918cc93", "8a9336e5bb4", "cef6983b292", "54c98c43354d", "193b68bd4fba", "b7e45b22fec3", "55760f21cdc5"], "tierOneDomains": ["medium.com", "thebolditalic.com", "arcdigital.media", "towardsdatascience.com", "uxdesign.cc", "codeburst.io", "psiloveyou.xyz", "writingcooperative.com", "entrepreneurshandbook.co", "prototypr.io", "betterhumans.coach.me", "theascent.pub"], "topicsToFollow": ["d61cf867d93f", "8a146bc21b28", "1eca0103fff3", "4d562ee63426", "aef1078a3ef5", "e15e46793f8d", "6158eb913466", "55f1c20aba7a", "3d18b94f6858", "4861fee224fd", "63c6f1f93ee", "1d98b3a9a871", "decb52b64abf", "ae5d4995e225", "830cded25262"], "defaultImages": { "avatar": { "imageId": "1*dmbNkD5D-u45r44go_cf0g.png", "height": 150, "width": 150 }, "orgLogo": { "imageId": "1*OMF3fSqH8t4xBJ9-6oZDZw.png", "height": 106, "width": 545 }, "postLogo": { "imageId": "1*kFrc4tBFM_tCis-2Ic87WA.png", "height": 810, "width": 1440 }, "postPreviewImage": { "imageId": "1*hn4v1tCaJy7cWMyb0bpNpQ.png", "height": 386, "width": 579 } }, "performanceTags": [], "collectionStructuredData": { "8d6b8a439e32": { "name": "Elemental", "data": { "@type": "NewsMediaOrganization", "ethicsPolicy": "https:\u002F\u002Fhelp.medium.com\u002Fhc\u002Fen-us\u002Farticles\u002F360043290473", "logo": { "@type": "ImageObject", "url": "https:\u002F\u002Fcdn-images-1.medium.com\u002Fmax\u002F980\u002F1*<EMAIL>", "width": 980, "height": 159 } } }, "3f6ecf56618": { "name": "Forge", "data": { "@type": "NewsMediaOrganization", "ethicsPolicy": "https:\u002F\u002Fhelp.medium.com\u002Fhc\u002Fen-us\u002Farticles\u002F360043290473", "logo": { "@type": "ImageObject", "url": "https:\u002F\u002Fcdn-images-1.medium.com\u002Fmax\u002F596\u002F1*<EMAIL>", "width": 596, "height": 183 } } }, "ae2a65f35510": { "name": "GEN", "data": { "@type": "NewsMediaOrganization", "ethicsPolicy": "https:\u002F\u002Fhelp.medium.com\u002Fhc\u002Fen-us\u002Farticles\u002F360043290473", "logo": { "@type": "ImageObject", "url": "https:\u002F\u002Fmiro.medium.com\u002Fmax\u002F264\u002F1*RdVZMdvfV3YiZTw6mX7yWA.png", "width": 264, "height": 140 } } }, "88d9857e584e": { "name": "LEVEL", "data": { "@type": "NewsMediaOrganization", "ethicsPolicy": "https:\u002F\u002Fhelp.medium.com\u002Fhc\u002Fen-us\u002Farticles\u002F360043290473", "logo": { "@type": "ImageObject", "url": "https:\u002F\u002Fmiro.medium.com\u002Fmax\u002F540\u002F1*JqYMhNX6KNNb2UlqGqO2WQ.png", "width": 540, "height": 108 } } }, "7b6769f2748b": { "name": "Marker", "data": { "@type": "NewsMediaOrganization", "ethicsPolicy": "https:\u002F\u002Fhelp.medium.com\u002Fhc\u002Fen-us\u002Farticles\u002F360043290473", "logo": { "@type": "ImageObject", "url": "https:\u002F\u002Fcdn-images-1.medium.com\u002Fmax\u002F383\u002F1*<EMAIL>", "width": 383, "height": 92 } } }, "444d13b52878": { "name": "OneZero", "data": { "@type": "NewsMediaOrganization", "ethicsPolicy": "https:\u002F\u002Fhelp.medium.com\u002Fhc\u002Fen-us\u002Farticles\u002F360043290473", "logo": { "@type": "ImageObject", "url": "https:\u002F\u002Fmiro.medium.com\u002Fmax\u002F540\u002F1*cw32fIqCbRWzwJaoQw6BUg.png", "width": 540, "height": 123 } } }, "8ccfed20cbb2": { "name": "Zora", "data": { "@type": "NewsMediaOrganization", "ethicsPolicy": "https:\u002F\u002Fhelp.medium.com\u002Fhc\u002Fen-us\u002Farticles\u002F360043290473", "logo": { "@type": "ImageObject", "url": "https:\u002F\u002Fmiro.medium.com\u002Fmax\u002F540\u002F1*tZUQqRcCCZDXjjiZ4bDvgQ.png", "width": 540, "height": 106 } } } }, "embeddedPostIds": { "coronavirus": "cd3010f9d81f" }, "sharedCdcMessaging": { "COVID_APPLICABLE_TAG_SLUGS": [], "COVID_APPLICABLE_TOPIC_NAMES": [], "COVID_APPLICABLE_TOPIC_NAMES_FOR_TOPIC_PAGE": [], "COVID_MESSAGES": { "tierA": { "text": "For more information on the novel coronavirus and Covid-19, visit cdc.gov.", "markups": [{ "start": 66, "end": 73, "href": "https:\u002F\u002Fwww.cdc.gov\u002Fcoronavirus\u002F2019-nCoV" }] }, "tierB": { "text": "Anyone can publish on Medium per our Policies, but we don’t fact-check every story. For more info about the coronavirus, see cdc.gov.", "markups": [{ "start": 37, "end": 45, "href": "https:\u002F\u002Fhelp.medium.com\u002Fhc\u002Fen-us\u002Fcategories\u002F201931128-Policies-Safety" }, { "start": 125, "end": 132, "href": "https:\u002F\u002Fwww.cdc.gov\u002Fcoronavirus\u002F2019-nCoV" }] }, "paywall": { "text": "This article has been made free for everyone, thanks to Medium Members. For more information on the novel coronavirus and Covid-19, visit cdc.gov.", "markups": [{ "start": 56, "end": 70, "href": "https:\u002F\u002Fmedium.com\u002Fmembership" }, { "start": 138, "end": 145, "href": "https:\u002F\u002Fwww.cdc.gov\u002Fcoronavirus\u002F2019-nCoV" }] }, "unbound": { "text": "This article is free for everyone, thanks to Medium Members. For more information on the novel coronavirus and Covid-19, visit cdc.gov.", "markups": [{ "start": 45, "end": 59, "href": "https:\u002F\u002Fmedium.com\u002Fmembership" }, { "start": 127, "end": 134, "href": "https:\u002F\u002Fwww.cdc.gov\u002Fcoronavirus\u002F2019-nCoV" }] } }, "COVID_BANNER_POST_ID_OVERRIDE_WHITELIST": ["3b31a67bff4a"] }, "sharedVoteMessaging": { "TAGS": ["politics", "election-2020", "government", "us-politics", "election", "2020-presidential-race", "trump", "donald-trump", "democrats", "republicans", "congress", "republican-party", "democratic-party", "biden", "joe-biden", "maga"], "TOPICS": ["politics", "election"], "MESSAGE": { "text": "Find out more about the U.S. election results here.", "markups": [{ "start": 46, "end": 50, "href": "https:\u002F\u002Fcookpolitical.com\u002F2020-national-popular-vote-tracker" }] }, "EXCLUDE_POSTS": ["397ef29e3ca5"] }, "embedPostRules": [], "recircOptions": { "v1": { "limit": 3 }, "v2": { "limit": 8 } }, "braintreeClientKey": "production_zjkj96jm_m56f8fqpf7ngnrd4", "paypalClientId": "AXj1G4fotC2GE8KzWX9mSxCH1wmPE3nJglf4Z2ig_amnhvlMVX87otaq58niAg9iuLktVNF_1WCMnN7v", "stripePublishableKey": "pk_live_7FReX44VnNIInZwrIIx6ghjl", "errorTracking": "none" }, "debug": { "requestId": "c8480dfc-20f5-4dba-896d-3e7d67c5bf42", "branchDeployConfig": null, "originalSpanCarrier": { "ot-tracer-spanid": "315a56c039ddc84c", "ot-tracer-traceid": "340f33c73b648faf", "ot-tracer-sampled": "true" } }, "multiVote": { "clapsPerPost": {} }, "navigation": { "branch": { "show": null, "hasRendered": null, "blockedByCTA": false }, "hideGoogleOneTap": false, "hasRenderedGoogleOneTap": null, "hasRenderedAlternateUserBanner": null, "currentLocation": "https:\u002F\u002Fmedium.com\u002Fwebpack\u002Fwebpack-3-official-release-15fd2dd8f07b", "host": "medium.com", "hostname": "medium.com", "referrer": "", "susiModal": { "step": null, "operation": "register" }, "postRead": false, "responsesOpen": false }, "session": { "user": { "id": "lo_54464edd69c3" }, "xsrf": "", "isSpoofed": false }, "tracing": {} }</script>
  <script>window.__APOLLO_STATE__ = { "ROOT_QUERY": { "__typename": "Query", "viewer": null, "variantFlags": [{ "__typename": "VariantFlag", "name": "allow_access", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "allow_signup", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "allow_test_auth", "valueType": { "__typename": "VariantFlagString", "value": "disallow" } }, { "__typename": "VariantFlag", "name": "android_enable_lock_responses", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "assign_default_topic_to_posts", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "available_annual_plan", "valueType": { "__typename": "VariantFlagString", "value": "2c754bcc2995" } }, { "__typename": "VariantFlag", "name": "available_monthly_plan", "valueType": { "__typename": "VariantFlagString", "value": "60e220181034" } }, { "__typename": "VariantFlag", "name": "bane_add_user", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "bane_verify_domain", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "branch_seo_metadata", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "browsable_stream_config_bucket", "valueType": { "__typename": "VariantFlagString", "value": "curated-topics" } }, { "__typename": "VariantFlag", "name": "coronavirus_topic_recirc", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "covid_19_cdc_banner", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "default_seo_post_titles", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "disable_android_subscription_activity_carousel", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "disable_ios_resume_reading_toast", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "disable_ios_subscription_activity_carousel", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "disable_mobile_featured_chunk", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "disable_post_recommended_from_friends_provider", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_android_local_currency", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_annual_renewal_reminder_email", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_app_flirty_thirty", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_apple_parse_expires_at", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_apple_sign_in", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_apple_webhook", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_apple_webhook_renewal_failure", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_aurora_about_page_routing", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_aurora_general_admission", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_aurora_nav", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_aurora_profile_page", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_aurora_pub_follower_page", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_aurora_recirc", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_aurora_sticky_nav", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_aurora_tag_page_routing", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_author_autotier", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_author_cards", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_author_cards_byline", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_automated_mission_control_triggers", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_braintree_apple_pay", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_braintree_client", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_braintree_integration", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_braintree_paypal", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_braintree_trial_membership", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_braintree_webhook", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_branch_io", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_branch_text_me_the_app", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_branding", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_branding_fonts", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_cleansweep_double_writes", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_client_error_tracking", "valueType": { "__typename": "VariantFlagString", "value": "none" } }, { "__typename": "VariantFlag", "name": "enable_confirm_sign_in", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_cta_meter", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_curation_priority_queue_experiment", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_custom_domain_v2_settings", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_dedicated_series_tab_api_ios", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_digest_feature_logging", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_digest_tagline", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_earn_redirect", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_edit_alt_text", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_email_sign_in_captcha", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_embedding_based_diversification", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_end_of_post_cleanup", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_evhead_com_to_ev_medium_com_redirect", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_expanded_feature_chunk_pool", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_filter_by_resend_rules", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_filter_expire_processor", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_footer_app_buttons", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_fyf_authors_and_collections", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_fyf_rex_sourcing", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_global_susi_modal", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_google_one_tap", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_google_webhook", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_google_webhook_subscription_cancelled", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_highlander_member_digest", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_hightower_user_minimum_guarantee", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_homepage_who_to_follow_module", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_homepage_write_button", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_ios_post_stats", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_json_logs_trained_ranker", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_kbfd_rex", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_kbfd_rex_app_highlights", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_lite_homepage", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_lite_homepage_feed", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_lite_notifications", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_lite_pay_page", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_lite_post", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_lite_post_cd", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_lite_pub_homepage_for_selected_domains", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_lite_publish_to_profile", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_lite_server_upstream_deadlines", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_lite_stories", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_lite_threaded_responses", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_lite_topics", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_lite_unread_notification_count_mutation", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_lock_responses", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_login_code_flow", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_marketing_emails", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_media_resource_try_catch", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_medium2_kbfd", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_membership_remove_section_a", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_miro_on_kubernetes", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_mission_control", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_ml_rank_modules", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_ml_rank_rex_anno", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_mute", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_new_checkout_flow", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_new_collaborative_filtering_data", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_new_login_flow", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_new_three_dot_menu", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_parsely", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_patronus_on_kubernetes", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_popularity_feature", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_post_import", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_post_page_nav_stickiness_removal", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_post_settings_screen", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_primary_topic_for_mobile", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_profile_design_reminder", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_profile_page_seo_titles", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_publish_to_email_for_publication_posts", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_receipt_notes", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_responses_2", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_responses_all", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_responses_edit_and_delete", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_responses_moderation", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_rex_follow_feed_cache", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_rito_upstream_deadlines", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_rtr_channel", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_s3_sites", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_save_to_medium", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_signup_friction", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_starspace", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_starspace_ranker_starspace", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_stripegate", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_tick_landing_page", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_tipalti_onboarding", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_trending_posts_diversification", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_tribute_landing_page", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_triton_predictions", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_trumpland_landing_page", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_twitter_auth_suggestions", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_unfiltered_cf", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "enable_untruncated_author_post_as_email", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "glyph_font_set", "valueType": { "__typename": "VariantFlagString", "value": "m2-unbound" } }, { "__typename": "VariantFlag", "name": "google_sign_in_android", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "ios_enable_generic_home_modules", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "ios_enable_home_post_menu", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "ios_enable_iceland_paywall", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "ios_enable_lock_responses", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "ios_iceland_nux", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "ios_pub_follow_email_opt_in", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "is_not_medium_subscriber", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "kill_fastrak", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "kill_stripe_express", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "limit_post_referrers", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "limit_user_follows", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "make_nav_sticky", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "new_transition_page", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "provider_for_credit_card_form", "valueType": { "__typename": "VariantFlagString", "value": "BRAINTREE" } }, { "__typename": "VariantFlag", "name": "pub_sidebar", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "redefine_average_post_reading_time", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "remove_post_post_similarity", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "retrained_ranker", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "sign_up_with_email_button", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "signin_services", "valueType": { "__typename": "VariantFlagString", "value": "twitter,facebook,google,email,google-fastidv,google-one-tap,apple" } }, { "__typename": "VariantFlag", "name": "signup_services", "valueType": { "__typename": "VariantFlagString", "value": "twitter,facebook,google,email,google-fastidv,google-one-tap,apple" } }, { "__typename": "VariantFlag", "name": "skip_sign_in_recaptcha", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "suppress_apple_missing_expires_date_alert", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }, { "__typename": "VariantFlag", "name": "use_new_admin_topic_backend", "valueType": { "__typename": "VariantFlagBoolean", "value": true } }], "meterPost({\"postId\":\"15fd2dd8f07b\",\"postMeteringOptions\":{\"referrer\":\"\"}})": { "__ref": "MeteringInfo:{}" }, "postResult({\"id\":\"15fd2dd8f07b\"})": { "__ref": "Post:15fd2dd8f07b" } }, "MeteringInfo:{}": { "__typename": "MeteringInfo", "postIds": [], "maxUnlockCount": 3, "unlocksRemaining": 3 }, "ImageMetadata:": { "id": "", "__typename": "ImageMetadata" }, "ImageMetadata:1*gdoQ1_5OID90wf1eLTFvWw.png": { "id": "1*gdoQ1_5OID90wf1eLTFvWw.png", "__typename": "ImageMetadata", "originalWidth": 3916, "originalHeight": 1524 }, "User:cccc522e775a": { "id": "cccc522e775a", "__typename": "User" }, "ImageMetadata:1*Wx82vEGrMfW4AdSLodZXgQ.png": { "id": "1*Wx82vEGrMfW4AdSLodZXgQ.png", "__typename": "ImageMetadata" }, "Collection:b2f039622545": { "id": "b2f039622545", "__typename": "Collection", "domain": null, "googleAnalyticsId": null, "slug": "webpack", "colorBehavior": "ACCENT_COLOR", "isAuroraVisible": false, "favicon": { "__ref": "ImageMetadata:" }, "name": "webpack", "colorPalette": { "__typename": "ColorPalette", "highlightSpectrum": { "__typename": "ColorSpectrum", "backgroundColor": "#FFFFFFFF", "colorPoints": [{ "__typename": "ColorPoint", "color": "#FFF4F2F2", "point": 0 }, { "__typename": "ColorPoint", "color": "#FFF2F0F0", "point": 0.1 }, { "__typename": "ColorPoint", "color": "#FFF0EEEE", "point": 0.2 }, { "__typename": "ColorPoint", "color": "#FFEEECEC", "point": 0.3 }, { "__typename": "ColorPoint", "color": "#FFECEBEA", "point": 0.4 }, { "__typename": "ColorPoint", "color": "#FFEAE9E8", "point": 0.5 }, { "__typename": "ColorPoint", "color": "#FFE8E7E7", "point": 0.6 }, { "__typename": "ColorPoint", "color": "#FFE6E5E5", "point": 0.7 }, { "__typename": "ColorPoint", "color": "#FFE4E3E3", "point": 0.8 }, { "__typename": "ColorPoint", "color": "#FFE2E1E1", "point": 0.9 }, { "__typename": "ColorPoint", "color": "#FFE0DFDF", "point": 1 }] }, "defaultBackgroundSpectrum": { "__typename": "ColorSpectrum", "backgroundColor": "#FFFFFFFF", "colorPoints": [{ "__typename": "ColorPoint", "color": "#FF848585", "point": 0 }, { "__typename": "ColorPoint", "color": "#FF7B7B7B", "point": 0.1 }, { "__typename": "ColorPoint", "color": "#FF717272", "point": 0.2 }, { "__typename": "ColorPoint", "color": "#FF686868", "point": 0.3 }, { "__typename": "ColorPoint", "color": "#FF5E5E5E", "point": 0.4 }, { "__typename": "ColorPoint", "color": "#FF545454", "point": 0.5 }, { "__typename": "ColorPoint", "color": "#FF494A4A", "point": 0.6 }, { "__typename": "ColorPoint", "color": "#FF3F3F3F", "point": 0.7 }, { "__typename": "ColorPoint", "color": "#FF333333", "point": 0.8 }, { "__typename": "ColorPoint", "color": "#FF272727", "point": 0.9 }, { "__typename": "ColorPoint", "color": "#FF1A1A1A", "point": 1 }] }, "tintBackgroundSpectrum": { "__typename": "ColorSpectrum", "backgroundColor": "#FFFFFFFF", "colorPoints": [{ "__typename": "ColorPoint", "color": "#FFFFFFFF", "point": 0 }, { "__typename": "ColorPoint", "color": "#FFECECEC", "point": 0.1 }, { "__typename": "ColorPoint", "color": "#FFD9D9D9", "point": 0.2 }, { "__typename": "ColorPoint", "color": "#FFC5C6C6", "point": 0.3 }, { "__typename": "ColorPoint", "color": "#FFB1B1B1", "point": 0.4 }, { "__typename": "ColorPoint", "color": "#FF9C9D9D", "point": 0.5 }, { "__typename": "ColorPoint", "color": "#FF868787", "point": 0.6 }, { "__typename": "ColorPoint", "color": "#FF6F7071", "point": 0.7 }, { "__typename": "ColorPoint", "color": "#FF575959", "point": 0.8 }, { "__typename": "ColorPoint", "color": "#FF3D3F3F", "point": 0.9 }, { "__typename": "ColorPoint", "color": "#FF202122", "point": 1 }] } }, "customStyleSheet": null, "tagline": "The official Medium publication for the webpack open source…", "isAuroraEligible": false, "viewerIsEditor": false, "logo": { "__ref": "ImageMetadata:1*gdoQ1_5OID90wf1eLTFvWw.png" }, "navItems": [{ "__typename": "NavItem", "title": "Announcements", "url": "https:\u002F\u002Fmedium.com\u002Fwebpack\u002Fannouncements\u002Fhome", "type": "TOPIC_PAGE" }, { "__typename": "NavItem", "title": "Google Summer of Code", "url": "https:\u002F\u002Fmedium.com\u002Fwebpack\u002Fgsoc\u002Fhome", "type": "TOPIC_PAGE" }, { "__typename": "NavItem", "title": "Contributors Guide", "url": "https:\u002F\u002Fmedium.com\u002Fwebpack\u002Fcontributors-guide\u002Fhome", "type": "TOPIC_PAGE" }, { "__typename": "NavItem", "title": "Tips and Tricks", "url": "https:\u002F\u002Fmedium.com\u002Fwebpack\u002Ftips-and-tricks\u002Fhome", "type": "TOPIC_PAGE" }, { "__typename": "NavItem", "title": "About Us", "url": "https:\u002F\u002Fmedium.com\u002Fwebpack\u002Fabout", "type": "ABOUT_PAGE_NAV_ITEM" }, { "__typename": "NavItem", "title": "Official Documentation", "url": "http:\u002F\u002Fwebpack.js.org", "type": "EXTERNAL_LINK_NAV_ITEM" }], "creator": { "__ref": "User:cccc522e775a" }, "subscriberCount": 16306, "avatar": { "__ref": "ImageMetadata:1*Wx82vEGrMfW4AdSLodZXgQ.png" }, "isEnrolledInHightower": false, "newsletterV3": null, "viewerIsFollowing": false, "viewerIsSubscribedToLetters": false, "canToggleEmail": false, "isUserSubscribedToCollectionEmails": false, "viewerIsMuting": false, "viewerCanEditOwnPosts": false, "viewerCanEditPosts": false, "description": "The official Medium publication for the webpack open source project!", "ampEnabled": false, "twitterUsername": "webpack", "facebookPageId": null }, "User:393110b0b9e4": { "id": "393110b0b9e4", "__typename": "User", "isFollowing": null, "viewerIsUser": false, "isSuspended": false, "name": "Sean T. Larkin", "hasCompletedProfile": false, "bio": "@Webpack Core team & AngularCLI team. Program Manager @Microsoft @EdgeDevTools. Web Performance, JavaScripter, Woodworker, 🐓 Farmer, and Gardener!", "imageId": "1*AtcF5LLmMnXgwTpBMeos-w.jpeg", "username": "TheLarkInn", "customStyleSheet": null, "customDomainState": null, "isAuroraVisible": true, "createdAt": 0, "mediumMemberAt": 0, "lastPostCreatedAt": 0, "socialStats": { "__typename": "SocialStats", "followerCount": 8583 }, "hasSubdomain": false, "isBlocking": null, "isMuting": null, "allowNotes": true, "newsletterV3": null, "twitterScreenName": "TheLarkInn", "isPartnerProgramEnrolled": false }, "Paragraph:aca3888b20f_0": { "id": "aca3888b20f_0", "__typename": "Paragraph", "name": "821e", "text": "It’s finally here. And it’s beautiful.", "type": "IMG", "href": null, "layout": "OUTSET_CENTER", "metadata": { "__ref": "ImageMetadata:1*Ac4K68j43uSbvHnKZKfXPw.jpeg" }, "hasDropCap": null, "iframe": null, "mixtapeMetadata": null, "markups": [], "dropCapImage": null }, "Paragraph:aca3888b20f_1": { "id": "aca3888b20f_1", "__typename": "Paragraph", "name": "ea51", "text": "🍾🚀 webpack 3: Official Release!! 🚀🍾", "type": "H3", "href": null, "layout": null, "metadata": null, "hasDropCap": null, "iframe": null, "mixtapeMetadata": null, "markups": [], "dropCapImage": null }, "Paragraph:aca3888b20f_2": { "id": "aca3888b20f_2", "__typename": "Paragraph", "name": "8fe8", "text": "Scope Hoisting, “magic comments”, and more!", "type": "H4", "href": null, "layout": null, "metadata": null, "hasDropCap": null, "iframe": null, "mixtapeMetadata": null, "markups": [], "dropCapImage": null }, "Paragraph:aca3888b20f_3": { "id": "aca3888b20f_3", "__typename": "Paragraph", "name": "927b", "text": "After we released webpack v2, we made some promises to the community. We promised that we would deliver the features you voted for. Moreover, we promised to deliver them in a faster, more stable release cycle.", "type": "P", "href": null, "layout": null, "metadata": null, "hasDropCap": null, "iframe": null, "mixtapeMetadata": null, "markups": [{ "__typename": "Markup", "start": 175, "end": 181, "type": "STRONG", "href": null, "anchorType": null, "userId": null, "linkMetadata": null }, { "__typename": "Markup", "start": 183, "end": 194, "type": "STRONG", "href": null, "anchorType": null, "userId": null, "linkMetadata": null }, { "__typename": "Markup", "start": 175, "end": 181, "type": "EM", "href": null, "anchorType": null, "userId": null, "linkMetadata": null }, { "__typename": "Markup", "start": 183, "end": 194, "type": "EM", "href": null, "anchorType": null, "userId": null, "linkMetadata": null }], "dropCapImage": null }, "Paragraph:aca3888b20f_4": { "id": "aca3888b20f_4", "__typename": "Paragraph", "name": "efac", "text": "No more year-long betas, no breaking changes between release candidates. We promised to do you right by you, the community that makes webpack thrive.", "type": "P", "href": null, "layout": null, "metadata": null, "hasDropCap": null, "iframe": null, "mixtapeMetadata": null, "markups": [{ "__typename": "Markup", "start": 104, "end": 108, "type": "STRONG", "href": null, "anchorType": null, "userId": null, "linkMetadata": null }, { "__typename": "Markup", "start": 103, "end": 149, "type": "EM", "href": null, "anchorType": null, "userId": null, "linkMetadata": null }], "dropCapImage": null }, "Paragraph:aca3888b20f_5": { "id": "aca3888b20f_5", "__typename": "Paragraph", "name": "ca2d", "text": "The webpack team is proud to announce that today we have released webpack 3.0.0!!! You can download or upgrade to it today!!", "type": "P", "href": null, "layout": null, "metadata": null, "hasDropCap": null, "iframe": null, "mixtapeMetadata": null, "markups": [], "dropCapImage": null }, "Paragraph:aca3888b20f_6": { "id": "aca3888b20f_6", "__typename": "Paragraph", "name": "549b", "text": "npm install webpack@3.0.0 --save-dev", "type": "P", "href": null, "layout": null, "metadata": null, "hasDropCap": null, "iframe": null, "mixtapeMetadata": null, "markups": [{ "__typename": "Markup", "start": 0, "end": 36, "type": "CODE", "href": null, "anchorType": null, "userId": null, "linkMetadata": null }], "dropCapImage": null }, "Paragraph:aca3888b20f_7": { "id": "aca3888b20f_7", "__typename": "Paragraph", "name": "69df", "text": "or with", "type": "P", "href": null, "layout": null, "metadata": null, "hasDropCap": null, "iframe": null, "mixtapeMetadata": null, "markups": [], "dropCapImage": null }, "Paragraph:aca3888b20f_8": { "id": "aca3888b20f_8", "__typename": "Paragraph", "name": "4ef7", "text": "yarn add webpack@3.0.0 --dev", "type": "P", "href": null, "layout": null, "metadata": null, "hasDropCap": null, "iframe": null, "mixtapeMetadata": null, "markups": [{ "__typename": "Markup", "start": 0, "end": 28, "type": "CODE", "href": null, "anchorType": null, "userId": null, "linkMetadata": null }], "dropCapImage": null }, "Paragraph:aca3888b20f_9": { "id": "aca3888b20f_9", "__typename": "Paragraph", "name": "17f8", "text": "Migrating from webpack 2 to 3, should involve no effort beyond running the upgrade commands in your terminal. We marked this as a Major change because of internal breaking changes that could affect some plugins.", "type": "P", "href": null, "layout": null, "metadata": null, "hasDropCap": null, "iframe": null, "mixtapeMetadata": null, "markups": [{ "__typename": "Markup", "start": 46, "end": 109, "type": "STRONG", "href": null, "anchorType": null, "userId": null, "linkMetadata": null }, { "__typename": "Markup", "start": 46, "end": 109, "type": "EM", "href": null, "anchorType": null, "userId": null, "linkMetadata": null }], "dropCapImage": null }, "Paragraph:aca3888b20f_10": { "id": "aca3888b20f_10", "__typename": "Paragraph", "name": "2878", "text": "So far we’ve seen 98% of users upgrade with no breaking functionality at all!!!", "type": "P", "href": null, "layout": null, "metadata": null, "hasDropCap": null, "iframe": null, "mixtapeMetadata": null, "markups": [{ "__typename": "Markup", "start": 0, "end": 79, "type": "STRONG", "href": null, "anchorType": null, "userId": null, "linkMetadata": null }, { "__typename": "Markup", "start": 0, "end": 79, "type": "EM", "href": null, "anchorType": null, "userId": null, "linkMetadata": null }], "dropCapImage": null }, "Paragraph:aca3888b20f_11": { "id": "aca3888b20f_11", "__typename": "Paragraph", "name": "3750", "text": "What’s new?", "type": "H3", "href": null, "layout": null, "metadata": null, "hasDropCap": null, "iframe": null, "mixtapeMetadata": null, "markups": [], "dropCapImage": null }, "Paragraph:aca3888b20f_12": { "id": "aca3888b20f_12", "__typename": "Paragraph", "name": "3c03", "text": "As we mentioned, we aimed to deliver the features that you voted for! Because of the overwhelming GitHub contributions, support from our backers and sponsors, we have been able to hit each one. 😍", "type": "P", "href": null, "layout": null, "metadata": null, "hasDropCap": null, "iframe": null, "mixtapeMetadata": null, "markups": [{ "__typename": "Markup", "start": 59, "end": 68, "type": "A", "href": "https:\u002F\u002Fwebpack.js.org\u002Fvote", "anchorType": "LINK", "userId": null, "linkMetadata": null }, { "__typename": "Markup", "start": 120, "end": 157, "type": "A", "href": "http:\u002F\u002Fopencollective.com\u002Fwebpack", "anchorType": "LINK", "userId": null, "linkMetadata": null }], "dropCapImage": null }, "Paragraph:aca3888b20f_13": { "id": "aca3888b20f_13", "__typename": "Paragraph", "name": "c11e", "text": "🔬 Scope Hoisting 🔬", "type": "H4", "href": null, "layout": null, "metadata": null, "hasDropCap": null, "iframe": null, "mixtapeMetadata": null, "markups": [], "dropCapImage": null }, "Paragraph:aca3888b20f_14": { "id": "aca3888b20f_14", "__typename": "Paragraph", "name": "0014", "text": "Scope Hoisting is the flagship feature of webpack 3. One of webpack’s trade-offs when bundling was that each module in your bundle would be wrapped in individual function closures. These wrapper functions made it slower for your JavaScript to execute in the browser. In comparison, tools like Closure Compiler and RollupJS ‘hoist’ or concatenate the scope of all your modules into one closure and allow for your code to have a faster execution time in the browser.", "type": "P", "href": null, "layout": null, "metadata": null, "hasDropCap": null, "iframe": null, "mixtapeMetadata": null, "markups": [], "dropCapImage": null }, "Paragraph:aca3888b20f_15": { "id": "aca3888b20f_15", "__typename": "Paragraph", "name": "d5a2", "text": "", "type": "IFRAME", "href": null, "layout": "INSET_CENTER", "metadata": null, "hasDropCap": null, "iframe": { "__typename": "Iframe", "mediaResource": { "__ref": "MediaResource:4533845503a873853b93e6aaf0833c57" } }, "mixtapeMetadata": null, "markups": [], "dropCapImage": null }, "Paragraph:aca3888b20f_16": { "id": "aca3888b20f_16", "__typename": "Paragraph", "name": "4965", "text": "As of today, with webpack 3, you can now add the following plugin to your configuration to enable scope hoisting:", "type": "P", "href": null, "layout": null, "metadata": null, "hasDropCap": null, "iframe": null, "mixtapeMetadata": null, "markups": [{ "__typename": "Markup", "start": 37, "end": 113, "type": "STRONG", "href": null, "anchorType": null, "userId": null, "linkMetadata": null }], "dropCapImage": null }, "Paragraph:aca3888b20f_17": { "id": "aca3888b20f_17", "__typename": "Paragraph", "name": "7078", "text": "module.exports = {  \n  plugins: [\n    new webpack.optimize.ModuleConcatenationPlugin()\n  ]\n};", "type": "PRE", "href": null, "layout": null, "metadata": null, "hasDropCap": null, "iframe": null, "mixtapeMetadata": null, "markups": [], "dropCapImage": null }, "Paragraph:aca3888b20f_18": { "id": "aca3888b20f_18", "__typename": "Paragraph", "name": "95b8", "text": "Scope Hoisting is specifically a feature made possible by ECMAScript Module syntax. Because of this webpack may fallback to normal bundling based on what kind of modules you are using, and other conditions.", "type": "P", "href": null, "layout": null, "metadata": null, "hasDropCap": null, "iframe": null, "mixtapeMetadata": null, "markups": [{ "__typename": "Markup", "start": 189, "end": 205, "type": "A", "href": "https:\u002F\u002Fmedium.com\u002Fwebpack\u002Fwebpack-freelancing-log-book-week-5-7-4764be3266f5", "anchorType": "LINK", "userId": "", "linkMetadata": null }], "dropCapImage": null }, "Paragraph:aca3888b20f_19": { "id": "aca3888b20f_19", "__typename": "Paragraph", "name": "40b6", "text": "To stay informed on what triggers these fallbacks, we’ve added a --display-optimization-bailout cli flag that will tell you what caused the fallback.", "type": "P", "href": null, "layout": null, "metadata": null, "hasDropCap": null, "iframe": null, "mixtapeMetadata": null, "markups": [{ "__typename": "Markup", "start": 65, "end": 95, "type": "CODE", "href": "", "anchorType": "LINK", "userId": "", "linkMetadata": null }], "dropCapImage": null }, "Paragraph:aca3888b20f_20": { "id": "aca3888b20f_20", "__typename": "Paragraph", "name": "6a0e", "text": "", "type": "IFRAME", "href": null, "layout": "INSET_CENTER", "metadata": null, "hasDropCap": null, "iframe": { "__typename": "Iframe", "mediaResource": { "__ref": "MediaResource:6663aed6525e9200886db81c9415337c" } }, "mixtapeMetadata": null, "markups": [], "dropCapImage": null }, "Paragraph:aca3888b20f_21": { "id": "aca3888b20f_21", "__typename": "Paragraph", "name": "f203", "text": "Because scope hoisting will remove function wrappers around your modules, you may see some small size improvements. However, the significant improvement will be how fast the JavaScript loads in the browser. If you have awesome before and after comparisons, feel free to respond with some data as we’d be honored to share it!", "type": "P", "href": null, "layout": null, "metadata": null, "hasDropCap": null, "iframe": null, "mixtapeMetadata": null, "markups": [], "dropCapImage": null }, "Paragraph:aca3888b20f_22": { "id": "aca3888b20f_22", "__typename": "Paragraph", "name": "ea65", "text": "🔮 ”Magic Comments” 🔮", "type": "H4", "href": null, "layout": null, "metadata": null, "hasDropCap": null, "iframe": null, "mixtapeMetadata": null, "markups": [], "dropCapImage": null }, "Paragraph:aca3888b20f_23": { "id": "aca3888b20f_23", "__typename": "Paragraph", "name": "b50c", "text": "When we introduced in webpack 2 the ability to use the dynamic import syntax ( import() ), users expressed their concerns that they could not create named chunks like they were able to with require.ensure.", "type": "P", "href": null, "layout": null, "metadata": null, "hasDropCap": null, "iframe": null, "mixtapeMetadata": null, "markups": [{ "__typename": "Markup", "start": 79, "end": 87, "type": "CODE", "href": null, "anchorType": null, "userId": null, "linkMetadata": null }, { "__typename": "Markup", "start": 190, "end": 204, "type": "CODE", "href": null, "anchorType": null, "userId": null, "linkMetadata": null }], "dropCapImage": null }, "Paragraph:aca3888b20f_24": { "id": "aca3888b20f_24", "__typename": "Paragraph", "name": "b465", "text": "We have now introduced what the community has coined “magic comments”, the ability to pass chunk name, and more as an inline comment to your import() statements.", "type": "P", "href": null, "layout": null, "metadata": null, "hasDropCap": null, "iframe": null, "mixtapeMetadata": null, "markups": [{ "__typename": "Markup", "start": 141, "end": 149, "type": "CODE", "href": null, "anchorType": null, "userId": null, "linkMetadata": null }, { "__typename": "Markup", "start": 103, "end": 111, "type": "A", "href": "https:\u002F\u002Fmedium.com\u002Fwebpack\u002Fhow-to-use-webpacks-new-magic-comment-feature-with-react-universal-component-ssr-a38fd3e296a", "anchorType": "LINK", "userId": null, "linkMetadata": null }], "dropCapImage": null }, "Paragraph:aca3888b20f_25": { "id": "aca3888b20f_25", "__typename": "Paragraph", "name": "1049", "text": "By using comments, we are able to stay true to the load specification, and still give you the great chunk naming features you love.", "type": "IFRAME", "href": null, "layout": "INSET_CENTER", "metadata": null, "hasDropCap": null, "iframe": { "__typename": "Iframe", "mediaResource": { "__ref": "MediaResource:1de84d98f7926e813eccd95ddac64e1b" } }, "mixtapeMetadata": null, "markups": [], "dropCapImage": null }, "Paragraph:aca3888b20f_26": { "id": "aca3888b20f_26", "__typename": "Paragraph", "name": "f813", "text": "Although these are technically features we released in v2.4 and v2.6, we worked to stabilize and fix bugs for these features that have landed in v3. This now allows the dynamic import syntax to have the same flexibility as require.ensure.", "type": "P", "href": null, "layout": null, "metadata": null, "hasDropCap": null, "iframe": null, "mixtapeMetadata": null, "markups": [{ "__typename": "Markup", "start": 223, "end": 237, "type": "CODE", "href": null, "anchorType": null, "userId": null, "linkMetadata": null }], "dropCapImage": null }, "Paragraph:aca3888b20f_27": { "id": "aca3888b20f_27", "__typename": "Paragraph", "name": "e1b1", "text": "", "type": "IFRAME", "href": null, "layout": "INSET_CENTER", "metadata": null, "hasDropCap": null, "iframe": { "__typename": "Iframe", "mediaResource": { "__ref": "MediaResource:fd3c12141eb0e7363d3e33feb528480c" } }, "mixtapeMetadata": null, "markups": [], "dropCapImage": null }, "Paragraph:aca3888b20f_28": { "id": "aca3888b20f_28", "__typename": "Paragraph", "name": "a2af", "text": "To learn more information, see our newest documentation guide on code-splitting that highlights these features!!!", "type": "P", "href": null, "layout": null, "metadata": null, "hasDropCap": null, "iframe": null, "mixtapeMetadata": null, "markups": [{ "__typename": "Markup", "start": 35, "end": 79, "type": "A", "href": "https:\u002F\u002Fwebpack.js.org\u002Fguides\u002Fcode-splitting-async", "anchorType": "LINK", "userId": "", "linkMetadata": null }], "dropCapImage": null }, "Paragraph:aca3888b20f_29": { "id": "aca3888b20f_29", "__typename": "Paragraph", "name": "5371", "text": "😍 What’s next? 😍", "type": "H3", "href": null, "layout": null, "metadata": null, "hasDropCap": null, "iframe": null, "mixtapeMetadata": null, "markups": [], "dropCapImage": null }, "Paragraph:aca3888b20f_30": { "id": "aca3888b20f_30", "__typename": "Paragraph", "name": "4f06", "text": "We have quite a few features and enhancements that we are hoping to bring you!!! But to take control of the ones we should be working one, stop by our vote page, and upvote the features you would like to see!", "type": "P", "href": null, "layout": null, "metadata": null, "hasDropCap": null, "iframe": null, "mixtapeMetadata": null, "markups": [{ "__typename": "Markup", "start": 150, "end": 208, "type": "A", "href": "http:\u002F\u002Fwebpack.js.org\u002Fvote", "anchorType": "LINK", "userId": "", "linkMetadata": null }, { "__typename": "Markup", "start": 150, "end": 208, "type": "EM", "href": "", "anchorType": "LINK", "userId": "", "linkMetadata": null }], "dropCapImage": null }, "Paragraph:aca3888b20f_31": { "id": "aca3888b20f_31", "__typename": "Paragraph", "name": "92e2", "text": "Here are some of those things we are hoping to bring you still:", "type": "P", "href": null, "layout": null, "metadata": null, "hasDropCap": null, "iframe": null, "mixtapeMetadata": null, "markups": [], "dropCapImage": null }, "Paragraph:aca3888b20f_32": { "id": "aca3888b20f_32", "__typename": "Paragraph", "name": "b1a0", "text": "Better Build Caching", "type": "ULI", "href": null, "layout": null, "metadata": null, "hasDropCap": null, "iframe": null, "mixtapeMetadata": null, "markups": [], "dropCapImage": null }, "Paragraph:aca3888b20f_33": { "id": "aca3888b20f_33", "__typename": "Paragraph", "name": "e784", "text": "Faster initial and incremental builds", "type": "ULI", "href": null, "layout": null, "metadata": null, "hasDropCap": null, "iframe": null, "mixtapeMetadata": null, "markups": [], "dropCapImage": null }, "Paragraph:aca3888b20f_34": { "id": "aca3888b20f_34", "__typename": "Paragraph", "name": "9ec9", "text": "Better TypeScript experience", "type": "ULI", "href": null, "layout": null, "metadata": null, "hasDropCap": null, "iframe": null, "mixtapeMetadata": null, "markups": [], "dropCapImage": null }, "Paragraph:aca3888b20f_35": { "id": "aca3888b20f_35", "__typename": "Paragraph", "name": "98e6", "text": "Revamped Long term caching", "type": "ULI", "href": null, "layout": null, "metadata": null, "hasDropCap": null, "iframe": null, "mixtapeMetadata": null, "markups": [], "dropCapImage": null }, "Paragraph:aca3888b20f_36": { "id": "aca3888b20f_36", "__typename": "Paragraph", "name": "8e9d", "text": "WASM Module Support", "type": "ULI", "href": null, "layout": null, "metadata": null, "hasDropCap": null, "iframe": null, "mixtapeMetadata": null, "markups": [], "dropCapImage": null }, "Paragraph:aca3888b20f_37": { "id": "aca3888b20f_37", "__typename": "Paragraph", "name": "8948", "text": "Improve User Experience", "type": "ULI", "href": null, "layout": null, "metadata": null, "hasDropCap": null, "iframe": null, "mixtapeMetadata": null, "markups": [], "dropCapImage": null }, "Paragraph:aca3888b20f_38": { "id": "aca3888b20f_38", "__typename": "Paragraph", "name": "f87b", "text": "🙇Thank you 🙇", "type": "H3", "href": null, "layout": null, "metadata": null, "hasDropCap": null, "iframe": null, "mixtapeMetadata": null, "markups": [], "dropCapImage": null }, "Paragraph:aca3888b20f_39": { "id": "aca3888b20f_39", "__typename": "Paragraph", "name": "48ea", "text": "All of our users, contributors, documentation writers, bloggers, sponsors, backers, and maintainers are all shareholders in helping us ensure webpack is successful for years to come.", "type": "P", "href": null, "layout": null, "metadata": null, "hasDropCap": null, "iframe": null, "mixtapeMetadata": null, "markups": [], "dropCapImage": null }, "Paragraph:aca3888b20f_40": { "id": "aca3888b20f_40", "__typename": "Paragraph", "name": "9f50", "text": "For this, we thank you all. It is not possible without you and we can’t wait to share what is in store for the future!!", "type": "P", "href": null, "layout": null, "metadata": null, "hasDropCap": null, "iframe": null, "mixtapeMetadata": null, "markups": [], "dropCapImage": null }, "Paragraph:aca3888b20f_41": { "id": "aca3888b20f_41", "__typename": "Paragraph", "name": "d2ed", "text": "No time to help contribute? Want to give back in other ways? Become a Backer or Sponsor to webpack by donating to our open collective. Open Collective not only helps support the Core Team, but also supports contributors who have spent significant time improving our organization on their free time! ❤", "type": "P", "href": null, "layout": null, "metadata": null, "hasDropCap": null, "iframe": null, "mixtapeMetadata": null, "markups": [{ "__typename": "Markup", "start": 118, "end": 133, "type": "A", "href": "http:\u002F\u002Fopencollective.com\u002Fwebpack", "anchorType": "LINK", "userId": "", "linkMetadata": null }, { "__typename": "Markup", "start": 61, "end": 134, "type": "STRONG", "href": "", "anchorType": "LINK", "userId": "", "linkMetadata": null }, { "__typename": "Markup", "start": 0, "end": 300, "type": "EM", "href": "", "anchorType": "LINK", "userId": "", "linkMetadata": null }], "dropCapImage": null }, "ImageMetadata:1*Ac4K68j43uSbvHnKZKfXPw.jpeg": { "id": "1*Ac4K68j43uSbvHnKZKfXPw.jpeg", "__typename": "ImageMetadata", "originalHeight": 3264, "originalWidth": 4896, "focusPercentX": null, "focusPercentY": null, "alt": null }, "MediaResource:4533845503a873853b93e6aaf0833c57": { "id": "4533845503a873853b93e6aaf0833c57", "__typename": "MediaResource", "iframeSrc": "https:\u002F\u002Fcdn.embedly.com\u002Fwidgets\u002Fmedia.html?type=text%2Fhtml&key=d04bfffea46d4aeda930ec88cc64b87c&schema=twitter&url=https%3A\u002F\u002Ftwitter.com\u002Ftizmagik\u002Fstatus\u002F876128847682523138&image=https%3A\u002F\u002Fi.embed.ly\u002F1\u002Fimage%3Furl%3Dhttps%253A%252F%252Fpbs.twimg.com%252Fmedia%252FDCih0NiWAAAAtZl.jpg%253Alarge%26key%3D4fce0568f2ce49e8b54624ef71a8a5bd", "iframeHeight": 185, "iframeWidth": 500, "title": "Jeremy Gayed 🤓 on Twitter" }, "MediaResource:6663aed6525e9200886db81c9415337c": { "id": "6663aed6525e9200886db81c9415337c", "__typename": "MediaResource", "iframeSrc": "https:\u002F\u002Fcdn.embedly.com\u002Fwidgets\u002Fmedia.html?type=text%2Fhtml&key=a19fcc184b9711e1b4764040d3dc5c07&schema=twitter&url=https%3A\u002F\u002Ftwitter.com\u002Fjeremenichelli\u002Fstatus\u002F876527176606265344&image=https%3A\u002F\u002Fi.embed.ly\u002F1\u002Fimage%3Furl%3Dhttps%253A%252F%252Fpbs.twimg.com%252Fprofile_images%252F838369206907387904%252FPNdDKe77_400x400.jpg%26key%3Da19fcc184b9711e1b4764040d3dc5c07", "iframeHeight": 185, "iframeWidth": 500, "title": "Jeremias Menichelli on Twitter" }, "MediaResource:1de84d98f7926e813eccd95ddac64e1b": { "id": "1de84d98f7926e813eccd95ddac64e1b", "__typename": "MediaResource", "iframeSrc": "", "iframeHeight": 0, "iframeWidth": 0, "title": "Chunk Name" }, "MediaResource:fd3c12141eb0e7363d3e33feb528480c": { "id": "fd3c12141eb0e7363d3e33feb528480c", "__typename": "MediaResource", "iframeSrc": "https:\u002F\u002Fcdn.embedly.com\u002Fwidgets\u002Fmedia.html?type=text%2Fhtml&key=a19fcc184b9711e1b4764040d3dc5c07&schema=twitter&url=https%3A\u002F\u002Ftwitter.com\u002Fadamrackis\u002Fstatus\u002F872602076056088576&image=https%3A\u002F\u002Fi.embed.ly\u002F1\u002Fimage%3Furl%3Dhttps%253A%252F%252Fpbs.twimg.com%252Fmedia%252FDBwao-MVwAAnydG.jpg%253Alarge%26key%3Da19fcc184b9711e1b4764040d3dc5c07", "iframeHeight": 185, "iframeWidth": 500, "title": "Adam Rackis on Twitter" }, "Tag:javascript": { "id": "javascript", "__typename": "Tag", "displayTitle": "JavaScript" }, "Tag:webpack": { "id": "webpack", "__typename": "Tag", "displayTitle": "Webpack" }, "Tag:tech": { "id": "tech", "__typename": "Tag", "displayTitle": "Tech" }, "Tag:technology": { "id": "technology", "__typename": "Tag", "displayTitle": "Technology" }, "Tag:web-development": { "id": "web-development", "__typename": "Tag", "displayTitle": "Web Development" }, "ImageMetadata:1*LF_LYj06_VWwk-q6WEH0-w.png": { "id": "1*LF_LYj06_VWwk-q6WEH0-w.png", "__typename": "ImageMetadata", "focusPercentX": null, "focusPercentY": null }, "User:a5aabd9ad00a": { "id": "a5aabd9ad00a", "__typename": "User", "name": "Vincent Chan", "username": "okcdz", "bio": "C++\u002FRust\u002FTypeScript, Engineer", "isFollowing": null, "imageId": "1*RYAoNSK9gttwIZqtsuPuCw.png", "mediumMemberAt": 0, "customDomainState": { "__typename": "CustomDomainState", "live": { "__typename": "CustomDomain", "domain": "okcdz.medium.com" } }, "hasSubdomain": true }, "Post:a0b788c0ce77": { "id": "a0b788c0ce77", "__typename": "Post", "title": "Better tree shaking with deep scope analysis", "mediumUrl": "https:\u002F\u002Fmedium.com\u002Fwebpack\u002Fbetter-tree-shaking-with-deep-scope-analysis-a0b788c0ce77", "previewImage": { "__ref": "ImageMetadata:1*LF_LYj06_VWwk-q6WEH0-w.png" }, "isPublished": true, "firstPublishedAt": 1534213613492, "readingTime": 5.058490566037736, "statusForCollection": "APPROVED", "isLocked": false, "isShortform": false, "visibility": "PUBLIC", "collection": { "__ref": "Collection:b2f039622545" }, "creator": { "__ref": "User:a5aabd9ad00a" }, "previewContent": { "__typename": "PreviewContent", "isFullContent": false } }, "ImageMetadata:1*wNaUljI2sG7HeDKQMJwqHQ.png": { "id": "1*wNaUljI2sG7HeDKQMJwqHQ.png", "__typename": "ImageMetadata", "focusPercentX": null, "focusPercentY": null }, "Collection:f5af2b715248": { "id": "f5af2b715248", "__typename": "Collection", "name": "The Startup", "slug": "swlh", "domain": null }, "User:6ee1d21716ab": { "id": "6ee1d21716ab", "__typename": "User", "name": "Tharaka Romesh", "username": "TRomesh", "bio": "Senior Software Engineer @99XTechnology", "isFollowing": null, "imageId": "1*-cOqqabGfysUpQ9aH93wmw.jpeg", "mediumMemberAt": 0, "customDomainState": null, "hasSubdomain": false }, "Post:d689a77c5f04": { "id": "d689a77c5f04", "__typename": "Post", "title": "Intro to Recoil", "mediumUrl": "https:\u002F\u002Fmedium.com\u002Fswlh\u002Fintro-to-recoil-d689a77c5f04", "previewImage": { "__ref": "ImageMetadata:1*wNaUljI2sG7HeDKQMJwqHQ.png" }, "isPublished": true, "firstPublishedAt": 1589920781581, "readingTime": 3.3320754716981136, "statusForCollection": "APPROVED", "isLocked": true, "isShortform": false, "visibility": "LOCKED", "collection": { "__ref": "Collection:f5af2b715248" }, "creator": { "__ref": "User:6ee1d21716ab" }, "previewContent": { "__typename": "PreviewContent", "isFullContent": false } }, "ImageMetadata:0*fvLeveZAwD-r-UXc": { "id": "0*fvLeveZAwD-r-UXc", "__typename": "ImageMetadata", "focusPercentX": null, "focusPercentY": null }, "User:74d56154f1f9": { "id": "74d56154f1f9", "__typename": "User", "name": "Omar Zahran", "username": "omarzahran", "bio": "Tech enthusiast, writer, and lover of food", "isFollowing": null, "imageId": "1*GGxQ2sFS3ITdV2cyBmWJNA.jpeg", "mediumMemberAt": 1578146987997, "customDomainState": { "__typename": "CustomDomainState", "live": { "__typename": "CustomDomain", "domain": "omarzahran.medium.com" } }, "hasSubdomain": true }, "Post:212d813b2798": { "id": "212d813b2798", "__typename": "Post", "title": "What If Chrome OS Was a Phone Operating System?", "mediumUrl": "https:\u002F\u002Fmedium.com\u002Fswlh\u002Fwhat-if-chrome-os-was-a-phone-operating-system-212d813b2798", "previewImage": { "__ref": "ImageMetadata:0*fvLeveZAwD-r-UXc" }, "isPublished": true, "firstPublishedAt": 1590248494744, "readingTime": 6.6169811320754715, "statusForCollection": "APPROVED", "isLocked": true, "isShortform": false, "visibility": "LOCKED", "collection": { "__ref": "Collection:f5af2b715248" }, "creator": { "__ref": "User:74d56154f1f9" }, "previewContent": { "__typename": "PreviewContent", "isFullContent": false } }, "ImageMetadata:1*cW9IvHMO5fBRNbjw2NW44Q.png": { "id": "1*cW9IvHMO5fBRNbjw2NW44Q.png", "__typename": "ImageMetadata", "focusPercentX": null, "focusPercentY": null }, "Collection:4737331fbdc0": { "id": "4737331fbdc0", "__typename": "Collection", "name": "The Guild", "slug": "the-guild", "domain": null }, "User:5a6962cd709e": { "id": "5a6962cd709e", "__typename": "User", "name": "Eytan Manor", "username": "eytanmanor", "bio": "Eytan is a JavaScript artist who comes from the land of the Promise(). His hobbies are eating, sleeping; and open-source… He loves open-source.", "isFollowing": null, "imageId": "1*xzrkykaDv7Ohk6OXzkOKFw.png", "mediumMemberAt": 0, "customDomainState": { "__typename": "CustomDomainState", "live": { "__typename": "CustomDomain", "domain": "eytanmanor.medium.com" } }, "hasSubdomain": true }, "Post:b0a13dcd0352": { "id": "b0a13dcd0352", "__typename": "Post", "title": "This is how I build Babel plug-ins", "mediumUrl": "https:\u002F\u002Fmedium.com\u002Fthe-guild\u002Fthis-is-how-i-build-babel-plug-ins-b0a13dcd0352", "previewImage": { "__ref": "ImageMetadata:1*cW9IvHMO5fBRNbjw2NW44Q.png" }, "isPublished": true, "firstPublishedAt": 1538994729668, "readingTime": 6.0698113207547175, "statusForCollection": "APPROVED", "isLocked": false, "isShortform": false, "visibility": "PUBLIC", "collection": { "__ref": "Collection:4737331fbdc0" }, "creator": { "__ref": "User:5a6962cd709e" }, "previewContent": { "__typename": "PreviewContent", "isFullContent": false } }, "ImageMetadata:0*nBNPobrQPLNmVp_a": { "id": "0*nBNPobrQPLNmVp_a", "__typename": "ImageMetadata", "focusPercentX": null, "focusPercentY": null }, "User:6895d5e78577": { "id": "6895d5e78577", "__typename": "User", "name": "Shailesh Jha", "username": "premeena", "bio": "Consultant | Full Stack Developer", "isFollowing": null, "imageId": "1*TFFMDq1asFHASUA9Pic-9Q.jpeg", "mediumMemberAt": 0, "customDomainState": { "__typename": "CustomDomainState", "live": { "__typename": "CustomDomain", "domain": "premeena.medium.com" } }, "hasSubdomain": true }, "Post:da8f75024ba4": { "id": "da8f75024ba4", "__typename": "Post", "title": "Webpack + React Optimised from scratch", "mediumUrl": "https:\u002F\u002Fpremeena.medium.com\u002Fwebpack-react-optimised-from-scratch-da8f75024ba4", "previewImage": { "__ref": "ImageMetadata:0*nBNPobrQPLNmVp_a" }, "isPublished": true, "firstPublishedAt": 1615095720033, "readingTime": 8.477358490566038, "statusForCollection": null, "isLocked": false, "isShortform": false, "visibility": "PUBLIC", "collection": null, "creator": { "__ref": "User:6895d5e78577" }, "previewContent": { "__typename": "PreviewContent", "isFullContent": false } }, "ImageMetadata:0*R7W2uXa4tMUvJrqS": { "id": "0*R7W2uXa4tMUvJrqS", "__typename": "ImageMetadata", "focusPercentX": null, "focusPercentY": null }, "Collection:be0b0399bbce": { "id": "be0b0399bbce", "__typename": "Collection", "name": "dooboolab", "slug": "dooboolab", "domain": null }, "User:3c26c3f84101": { "id": "3c26c3f84101", "__typename": "User", "name": "Donghyeon Kim", "username": "0916dhkim", "bio": "", "isFollowing": null, "imageId": "", "mediumMemberAt": 0, "customDomainState": { "__typename": "CustomDomainState", "live": { "__typename": "CustomDomain", "domain": "0916dhkim.medium.com" } }, "hasSubdomain": true }, "Post:d04ee074eb98": { "id": "d04ee074eb98", "__typename": "Post", "title": "How to Update Relay Cache", "mediumUrl": "https:\u002F\u002Fmedium.com\u002Fdooboolab\u002Fhow-to-update-relay-cache-d04ee074eb98", "previewImage": { "__ref": "ImageMetadata:0*R7W2uXa4tMUvJrqS" }, "isPublished": true, "firstPublishedAt": 1612132378295, "readingTime": 2.9871069182389935, "statusForCollection": "APPROVED", "isLocked": false, "isShortform": false, "visibility": "PUBLIC", "collection": { "__ref": "Collection:be0b0399bbce" }, "creator": { "__ref": "User:3c26c3f84101" }, "previewContent": { "__typename": "PreviewContent", "isFullContent": false } }, "ImageMetadata:1*9p8FLpoYHEOc_8bdG0J08A.png": { "id": "1*9p8FLpoYHEOc_8bdG0J08A.png", "__typename": "ImageMetadata", "focusPercentX": null, "focusPercentY": null }, "User:792f462bb0e7": { "id": "792f462bb0e7", "__typename": "User", "name": "TOAST UI", "username": "toastui", "bio": "JavaScript UI Library Open Source by http:\u002F\u002Fui.toast.com", "isFollowing": null, "imageId": "1*L3JIVv_0_gEInsRriDrt5Q.png", "mediumMemberAt": 0, "customDomainState": { "__typename": "CustomDomainState", "live": { "__typename": "CustomDomain", "domain": "toastui.medium.com" } }, "hasSubdomain": true }, "Post:26ffc025864d": { "id": "26ffc025864d", "__typename": "Post", "title": "Improving the Reactivity System (feat. TOAST UI Grid)", "mediumUrl": "https:\u002F\u002Ftoastui.medium.com\u002Fimproving-the-reactivity-system-feat-toast-ui-grid-26ffc025864d", "previewImage": { "__ref": "ImageMetadata:1*9p8FLpoYHEOc_8bdG0J08A.png" }, "isPublished": true, "firstPublishedAt": 1578972835689, "readingTime": 12.666352201257862, "statusForCollection": null, "isLocked": true, "isShortform": false, "visibility": "LOCKED", "collection": null, "creator": { "__ref": "User:792f462bb0e7" }, "previewContent": { "__typename": "PreviewContent", "isFullContent": false } }, "ImageMetadata:1*6js53mDPNElWcqe_NTDOIQ.gif": { "id": "1*6js53mDPNElWcqe_NTDOIQ.gif", "__typename": "ImageMetadata", "focusPercentX": null, "focusPercentY": null }, "Collection:5517fd7b58a6": { "id": "5517fd7b58a6", "__typename": "Collection", "name": "Level Up Coding", "slug": "gitconnected", "domain": "levelup.gitconnected.com" }, "User:2fe57836127": { "id": "2fe57836127", "__typename": "User", "name": "Rany ElHousieny", "username": "ranyel", "bio": "https:\u002F\u002Frany.tk Commercial software development manager offering 25+ years’ of technical experience. Certified Solutions Architect", "isFollowing": null, "imageId": "2*-MWSVEjycNfqU98EZSVVZg.jpeg", "mediumMemberAt": 1603377049000, "customDomainState": { "__typename": "CustomDomainState", "live": { "__typename": "CustomDomain", "domain": "ranyel.medium.com" } }, "hasSubdomain": true }, "Post:4dd2f7d81dcb": { "id": "4dd2f7d81dcb", "__typename": "Post", "title": "Understanding Micro-Frontends Webpack 5 Configurations Step by Step", "mediumUrl": "https:\u002F\u002Flevelup.gitconnected.com\u002Funderstanding-micro-frontends-webpack5-configurations-step-by-step-4dd2f7d81dcb", "previewImage": { "__ref": "ImageMetadata:1*6js53mDPNElWcqe_NTDOIQ.gif" }, "isPublished": true, "firstPublishedAt": 1614879438472, "readingTime": 7.944339622641509, "statusForCollection": "APPROVED", "isLocked": false, "isShortform": false, "visibility": "PUBLIC", "collection": { "__ref": "Collection:5517fd7b58a6" }, "creator": { "__ref": "User:2fe57836127" }, "previewContent": { "__typename": "PreviewContent", "isFullContent": false } }, "Post:15fd2dd8f07b": { "id": "15fd2dd8f07b", "__typename": "Post", "canonicalUrl": "", "collection": { "__ref": "Collection:b2f039622545" }, "content({})": { "__typename": "PostContent", "isLockedPreviewOnly": false, "validatedShareKey": "", "isCacheableContent": false, "bodyModel": { "__typename": "RichText", "paragraphs": [{ "__ref": "Paragraph:aca3888b20f_0" }, { "__ref": "Paragraph:aca3888b20f_1" }, { "__ref": "Paragraph:aca3888b20f_2" }, { "__ref": "Paragraph:aca3888b20f_3" }, { "__ref": "Paragraph:aca3888b20f_4" }, { "__ref": "Paragraph:aca3888b20f_5" }, { "__ref": "Paragraph:aca3888b20f_6" }, { "__ref": "Paragraph:aca3888b20f_7" }, { "__ref": "Paragraph:aca3888b20f_8" }, { "__ref": "Paragraph:aca3888b20f_9" }, { "__ref": "Paragraph:aca3888b20f_10" }, { "__ref": "Paragraph:aca3888b20f_11" }, { "__ref": "Paragraph:aca3888b20f_12" }, { "__ref": "Paragraph:aca3888b20f_13" }, { "__ref": "Paragraph:aca3888b20f_14" }, { "__ref": "Paragraph:aca3888b20f_15" }, { "__ref": "Paragraph:aca3888b20f_16" }, { "__ref": "Paragraph:aca3888b20f_17" }, { "__ref": "Paragraph:aca3888b20f_18" }, { "__ref": "Paragraph:aca3888b20f_19" }, { "__ref": "Paragraph:aca3888b20f_20" }, { "__ref": "Paragraph:aca3888b20f_21" }, { "__ref": "Paragraph:aca3888b20f_22" }, { "__ref": "Paragraph:aca3888b20f_23" }, { "__ref": "Paragraph:aca3888b20f_24" }, { "__ref": "Paragraph:aca3888b20f_25" }, { "__ref": "Paragraph:aca3888b20f_26" }, { "__ref": "Paragraph:aca3888b20f_27" }, { "__ref": "Paragraph:aca3888b20f_28" }, { "__ref": "Paragraph:aca3888b20f_29" }, { "__ref": "Paragraph:aca3888b20f_30" }, { "__ref": "Paragraph:aca3888b20f_31" }, { "__ref": "Paragraph:aca3888b20f_32" }, { "__ref": "Paragraph:aca3888b20f_33" }, { "__ref": "Paragraph:aca3888b20f_34" }, { "__ref": "Paragraph:aca3888b20f_35" }, { "__ref": "Paragraph:aca3888b20f_36" }, { "__ref": "Paragraph:aca3888b20f_37" }, { "__ref": "Paragraph:aca3888b20f_38" }, { "__ref": "Paragraph:aca3888b20f_39" }, { "__ref": "Paragraph:aca3888b20f_40" }, { "__ref": "Paragraph:aca3888b20f_41" }], "sections": [{ "__typename": "Section", "name": "33b6", "startIndex": 0, "textLayout": "FLOW", "imageLayout": "NONE", "backgroundImage": null, "videoLayout": "NO_VIDEO", "backgroundVideo": null }, { "__typename": "Section", "name": "33f3", "startIndex": 9, "textLayout": null, "imageLayout": null, "backgroundImage": null, "videoLayout": null, "backgroundVideo": null }, { "__typename": "Section", "name": "f349", "startIndex": 41, "textLayout": null, "imageLayout": null, "backgroundImage": null, "videoLayout": null, "backgroundVideo": null }] } }, "creator": { "__ref": "User:393110b0b9e4" }, "customStyleSheet": null, "firstPublishedAt": 1497893008613, "isLocked": false, "isPublished": true, "isShortform": false, "layerCake": 0, "primaryTopic": null, "title": "🍾🚀 webpack 3: Official Release!! 🚀🍾", "readCreatorPostsCount": 0, "mediumUrl": "https:\u002F\u002Fmedium.com\u002Fwebpack\u002Fwebpack-3-official-release-15fd2dd8f07b", "isLimitedState": false, "visibility": "PUBLIC", "license": "ALL_RIGHTS_RESERVED", "allowResponses": true, "newsletterId": "", "sequence": null, "tags": [{ "__ref": "Tag:javascript" }, { "__ref": "Tag:webpack" }, { "__ref": "Tag:tech" }, { "__ref": "Tag:technology" }, { "__ref": "Tag:web-development" }], "topics": [], "viewerClapCount": 0, "showSubscribeToProfilePromo": false, "showSubscribeToCollectionNewsletterV3Promo": false, "inResponseToPostResult": null, "isNewsletter": false, "socialTitle": "", "socialDek": "", "metaDescription": "After we released webpack v2, we made some promises to the community. We promised that we would deliver the features you voted for. Today, we’ve fulfilled these promises.", "latestPublishedAt": 1498064228802, "readingTime": 2.909433962264151, "previewContent": { "__typename": "PreviewContent", "subtitle": "Now with Scope Hoisting, “magic comments”, and more!" }, "previewImage": { "__ref": "ImageMetadata:1*Ac4K68j43uSbvHnKZKfXPw.jpeg" }, "creatorPartnerProgramEnrollmentStatus": "PERMISSION_DENIED", "clapCount": 4034, "lockedSource": "LOCKED_POST_SOURCE_NONE", "isSuspended": false, "pendingCollection": null, "statusForCollection": "APPROVED", "pinnedAt": 0, "pinnedByCreatorAt": 0, "curationEligibleAt": 0, "responseDistribution": "NOT_DISTRIBUTED", "shareKey": null, "internalLinks({\"paging\":{\"limit\":8}})": { "__typename": "InternalLinksConnection", "items": [{ "__ref": "Post:a0b788c0ce77" }, { "__ref": "Post:d689a77c5f04" }, { "__ref": "Post:212d813b2798" }, { "__ref": "Post:b0a13dcd0352" }, { "__ref": "Post:da8f75024ba4" }, { "__ref": "Post:d04ee074eb98" }, { "__ref": "Post:26ffc025864d" }, { "__ref": "Post:4dd2f7d81dcb" }] }, "collaborators": [], "translationSourcePost": null, "inResponseToMediaResource": null, "isDistributionAlertDismissed": false, "audioVersionUrl": "", "seoTitle": "", "updatedAt": 1528169345031, "shortformType": "SHORTFORM_TYPE_LINK", "structuredData": "", "seoDescription": "", "postResponses": { "__typename": "PostResponses", "count": 51 }, "latestPublishedVersion": "aca3888b20f", "isPublishToEmail": false, "readingList": "READING_LIST_NONE", "voterCount": 1841, "recommenders": [] } }</script>
  <script src="https://cdn-client.medium.com/lite/static/js/manifest.1fd15351.js"></script>
  <script src="https://cdn-client.medium.com/lite/static/js/5566.011c6c68.js"></script>
  <script src="https://cdn-client.medium.com/lite/static/js/main.4f933583.js"></script>
  <script src="https://cdn-client.medium.com/lite/static/js/5573.159bf40f.chunk.js"></script>
  <script src="https://cdn-client.medium.com/lite/static/js/instrumentation.2774f137.chunk.js"></script>
  <script src="https://cdn-client.medium.com/lite/static/js/reporting.0e714607.chunk.js"></script>
  <script src="https://cdn-client.medium.com/lite/static/js/1752.a348f767.chunk.js"></script>
  <script src="https://cdn-client.medium.com/lite/static/js/1491.c08ce3ca.chunk.js"></script>
  <script src="https://cdn-client.medium.com/lite/static/js/4964.fb36722e.chunk.js"></script>
  <script src="https://cdn-client.medium.com/lite/static/js/8342.6aa0b45e.chunk.js"></script>
  <script src="https://cdn-client.medium.com/lite/static/js/9692.3fc1c18a.chunk.js"></script>
  <script src="https://cdn-client.medium.com/lite/static/js/4586.7c07e0df.chunk.js"></script>
  <script src="https://cdn-client.medium.com/lite/static/js/5064.ee9cac75.chunk.js"></script>
  <script src="https://cdn-client.medium.com/lite/static/js/9355.c6c38ae6.chunk.js"></script>
  <script src="https://cdn-client.medium.com/lite/static/js/2846.bbadae04.chunk.js"></script>
  <script src="https://cdn-client.medium.com/lite/static/js/7012.f1ee6871.chunk.js"></script>
  <script src="https://cdn-client.medium.com/lite/static/js/6230.b596e4e9.chunk.js"></script>
  <script src="https://cdn-client.medium.com/lite/static/js/9972.a4eec407.chunk.js"></script>
  <script src="https://cdn-client.medium.com/lite/static/js/5127.810d9eac.chunk.js"></script>
  <script src="https://cdn-client.medium.com/lite/static/js/8580.84adae83.chunk.js"></script>
  <script src="https://cdn-client.medium.com/lite/static/js/8751.8bd4ce88.chunk.js"></script>
  <script src="https://cdn-client.medium.com/lite/static/js/2955.e3a25a19.chunk.js"></script>
  <script src="https://cdn-client.medium.com/lite/static/js/7131.4059cf64.chunk.js"></script>
  <script src="https://cdn-client.medium.com/lite/static/js/8127.58f0a64c.chunk.js"></script>
  <script src="https://cdn-client.medium.com/lite/static/js/6371.4672dd1f.chunk.js"></script>
  <script src="https://cdn-client.medium.com/lite/static/js/304.7fb69e54.chunk.js"></script>
  <script src="https://cdn-client.medium.com/lite/static/js/2874.661d5cbc.chunk.js"></script>
  <script src="https://cdn-client.medium.com/lite/static/js/2514.2c8bf092.chunk.js"></script>
  <script src="https://cdn-client.medium.com/lite/static/js/3874.19af3a09.chunk.js"></script>
  <script src="https://cdn-client.medium.com/lite/static/js/2558.1ac5c4fd.chunk.js"></script>
  <script src="https://cdn-client.medium.com/lite/static/js/8286.962b84f6.chunk.js"></script>
  <script src="https://cdn-client.medium.com/lite/static/js/5025.250e5ada.chunk.js"></script>
  <script src="https://cdn-client.medium.com/lite/static/js/Post.4a47de6d.chunk.js"></script>
  <script>window.main();</script>
</body>

</html>
