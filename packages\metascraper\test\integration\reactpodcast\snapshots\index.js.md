# Snapshot report for `test/integration/reactpodcast/index.js`

The actual snapshot is saved in `index.js.snap`.

Generated by [AVA](https://avajs.dev).

## reactpodcast

> Snapshot 1

    {
      audio: 'https://cdn.simplecast.com/audio/bdb43d4d-bd1d-4fbc-bd60-40f1e3299aa3/episodes/48e2fc0d-0af2-468c-9eaf-e6ba4d4f852e/audio/2f892c8b-ecc9-4905-ae9d-4283ffb1054e/default_tc.mp3',
      author: null,
      description: `<PERSON> is building a full-stack framework for React and GraphQL developers.␊
      In this episode we talk about RedwoodJS, a framework that’s bringing full-stack to Jamstack. <PERSON> is a co-founder of GitHub, creator of Jekyll (the OG static site generator), TOML, and Semantic Versioning. If you’ve been hunting for a full-stack React and GraphQL solution, and envy the integration of frameworks like Rails and Laravel, listen up, because RedwoodJS might just be for for you.`,
      image: null,
      lang: 'en',
      logo: 'https://image.simplecastcdn.com/images/7672009f-32a0-470f-81b8-77a9748d560e/82cc2a41-1a7b-4c91-b6f0-a461c50830ed/simplecast-logo-32.jpg',
      publisher: 'React Podcast',
      title: '117: Tom Preston-Werner on RedwoodJS | React Podcast',
      url: 'https://reactpodcast.com/episodes/117',
      video: null,
    }
