
























<!DOCTYPE html>
<!--[if lt IE 7]>  <html lang="en" class="no-js lt-ie9 lt-ie8 lt-ie7" xmlns="http://www.w3.org/1999/xhtml" xmlns:fb="http://ogp.me/ns/fb#"> <![endif]-->
<!--[if IE 7]> <html lang="en" class="no-js lt-ie10 lt-ie9 lt-ie8" xmlns="http://www.w3.org/1999/xhtml" xmlns:fb="http://ogp.me/ns/fb#"> <![endif]-->
<!--[if IE 8]> <html lang="en" class="no-js lt-ie10 lt-ie9" xmlns="http://www.w3.org/1999/xhtml" xmlns:fb="http://ogp.me/ns/fb#"> <![endif]-->
<!--[if IE 9]> <html lang="en" class="no-js lt-ie10" xmlns="http://www.w3.org/1999/xhtml" xmlns:fb="http://ogp.me/ns/fb#"> <![endif]-->
<!--[if gt IE 9]><!--> <html lang="en" class="no-js" xmlns="http://www.w3.org/1999/xhtml" xmlns:fb="http://ogp.me/ns/fb#"> <!--<![endif]-->











<head>

  <!-- metas -->
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
  <title>20 Ways to Measure the Success of Your Growing Cloud Investment | CIO</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">










  <link rel="canonical" href="http://www.cio.com/article/2929788/cloud-computing/20-ways-to-measure-the-success-of-your-growing-cloud-investment.html" />





  <meta itemprop="url" content="http://www.cio.com/article/2929788/cloud-computing/20-ways-to-measure-the-success-of-your-growing-cloud-investment.html" />
  <meta name="description" content="If IT is going to better align themselves with business operations, they must be able to objectively quantify the value of the cloud. " />
  <meta name="news_keywords" content="cloud investment,cost savings" />


      <meta name="DC.date.issued" content="2015-06-03T07:06-05:00" />
      <meta name="date" content="2015-06-03" />









      <meta property="og:title" content="20 Ways to Measure the Success of Your Growing Cloud Investment">







      <meta property="og:image" content="http://images.techhive.com/images/article/2015/06/msftone_cloudperspective4-100588855-primary.idge.png" />
      <meta itemprop="image" content="http://images.techhive.com/images/article/2015/06/msftone_cloudperspective4-100588855-primary.idge.png">
      <meta itemprop="thumbnailURl" content="http://images.techhive.com/images/article/2015/06/msftone_cloudperspective4-100588855-primary.idge.png" />
      <link rel="image_src" href="http://images.techhive.com/images/article/2015/06/msftone_cloudperspective4-100588855-primary.idge.png" />
      <meta name="twitter:image" content="http://images.techhive.com/images/article/2015/06/msftone_cloudperspective4-100588855-primary.idge.png" />





      <meta property="og:type" content="article" />









      <meta name="author" content="David Spark">





        <meta name="robots" content="NOFOLLOW" />




  <meta property="og:site_name" content="CIO" />
  <meta property="fb:app_id" content="129182073794488" />


      <meta property="og:url" content="http://www.cio.com/article/2929788/cloud-computing/20-ways-to-measure-the-success-of-your-growing-cloud-investment.html">
      <meta name="twitter:url" content="http://www.cio.com/article/2929788/cloud-computing/20-ways-to-measure-the-success-of-your-growing-cloud-investment.html">











    <!-- Add to article pages only -->
      <meta name="twitter:card" content="summary_large_image">


  <meta name="twitter:card" content="summary">
  <meta name="twitter:site" content="@CIOonline">

    <meta property="og:description" content="If IT is going to better align themselves with business operations, they must be able to objectively quantify the value of the cloud. ">
    <meta name="twitter:description" content="If IT is going to better align themselves with business operations, they must be able to objectively quantify the value of the cloud. ">

















  <meta name="rating" content="General">

  <meta name="robots" content="NOODP,NOYDIR" />


    <meta name="google-site-verification" content="kgXSqlEuYT_m_uwWCByBpKmiaVLisiAi9bPjzyelGTw" />
















  <!-- Sailthru -->

    <meta name="sailthru.tags" content="cloud-computing,david-spark,empowering-business,news,long"/>

    <meta name="sailthru.image.thumb" content="http://core3.staticworld.net/images/article/2015/06/msftone_cloudperspective4-100588855-small.idge.png"/>

    <meta name="sailthru.image.full" content="http://core2.staticworld.net/images/article/2015/06/msftone_cloudperspective4-100588855-primary.idge.png"/>



  <!-- pagination -->


  <link rel="next" href="/article/2929788/cloud-computing/20-ways-to-measure-the-success-of-your-growing-cloud-investment.html?page=2" />


  <!-- css -->
  <link rel="stylesheet" href="/www.idge/css/normalize.css?v=20160525110859" />
  <link rel="stylesheet" href="/www.idge/css/prettify.css?v=20160525110859" />

    <link rel="stylesheet" href="/www.idge.ans/js/select2-3.5.0/select2.css" />



    <link rel="stylesheet" href="/www.idge/css/sitewrapper.css?v=20160525110859" />




        <link rel="stylesheet" href="/www.idge/css/article.css?v=20160525110859" />



    <link rel="stylesheet" href="/www.idge/css/print.css?v=20160525110859" media="print" />



    <link rel="stylesheet" href="/www.idge.cio/css/sitewrapper.css?v=20160525110859" />

    <link rel="stylesheet" href="/www.idge.cio/css/article.css?v=20160525110859" />



  <link rel="stylesheet" href="/www.idge/css/webfonts/ss-social.css?v=20160525110859" />
  <link rel="stylesheet" href="/www.idge/css/webfonts/ss-standard.css?v=20160525110859" />
  <!--[if lte IE 8]>
    <link href="/www.idge/css/webfonts/ss-ie8.css" rel="stylesheet" />
  <![endif]-->



    <link href="/www/css/ecom.css?v=20160525110859" rel="stylesheet" >




    <script type="text/javascript" src="http://fonts.staticworld.net/sqc0dqh.js"></script>
    <script type="text/javascript">try{Typekit.load();}catch(e){}</script>


  <!--  fav and touch icons -->
  <link rel="shortcut icon" type="image/x-icon" href="http://idge.staticworld.net/cio/favicon.ico"/>
  <link rel="apple-touch-icon-precomposed" sizes="144x144" href="http://idge.staticworld.net/cio/CIO_logo_144x144.png" />
  <link rel="apple-touch-icon-precomposed" sizes="114x114" href="http://idge.staticworld.net/cio/CIO_logo_114x114.png" />
  <link rel="apple-touch-icon-precomposed" sizes="72x72" href="http://idge.staticworld.net/cio/CIO_logo_72x72.png" />
  <link rel="apple-touch-icon-precomposed" sizes="57x57" href="http://idge.staticworld.net/cio/CIO_logo_57x57.png" />
  <link rel="apple-touch-icon" href="http://idge.staticworld.net/cio/CIO_logo_300x300.png" />

  <!-- js -->
  <script type="text/javascript">var _sf_startpt=(new Date()).getTime();</script>

  <script src="/www.idge/js/jquery/jquery-1.10.2.min.js"></script>

  <script src="/www/js/init_device.js?v=20160525110859"></script>

  <!-- google_dfp needs jquery defined first -->
















<script type="text/javascript" src="/www/js/ads/gpt_includes.js?v=20160525110859"></script>


    <script src="/www/js/ads/narf_prebid.js?v=20160525110859"></script>




<script type="text/javascript">
  // Set up ad related variables
  try {
    IDG.GPT.unitName = "/8456/IDG.US_E_CIO.com";
  }
  catch (exception) {
    console.log ("google_dfp can't use IDG.GPT "+ exception);
  }


  try {
    IDG.GPT.unitName = IDG.GPT.unitName + "/" + "cloud-computing_section";
  }
  catch (exception) {
    console.log ("google_dfp can't use IDG.GPT "+ exception);
  }
  // global variables
  var global_ShowSuper = true;
  var global_ShowHero = false;

  //XFP global targeting, more targeting thm_pre
  var url = window.location.href;
  if(url.indexOf("?")>1){
    url=url.split('?')[0];
  }


  try {
    IDG.GPT.addTarget("URL", encodeURIComponent(url));
  }
  catch (exception) {
    console.log ("google_dfp can't use IDG.GPT "+ exception);
  }
  try {IDG.GPT.addTarget("zone", 'article-blog/cloud-computing');}  catch (exception) {console.log ("google_dfp can't use IDG.GPT "+ exception);}
  try{IDG.GPT.addTarget("blogId", '1469');} catch (exception) {console.log ("google_dfp can't use IDG.GPT "+ exception);}try{IDG.GPT.addTarget("articleId", '2929788');}  catch (exception) {console.log ("google_dfp can't use IDG.GPT "+ exception);}try{IDG.GPT.addTarget("type", 'news');}  catch (exception) {console.log ("google_dfp can't use IDG.GPT "+ exception);}try{IDG.GPT.addTarget("typeId", '2');} catch (exception) {console.log ("google_dfp can't use IDG.GPT "+ exception);}try{IDG.GPT.addTarget("manufacturer", 'hp');}  catch (exception) {console.log ("google_dfp can't use IDG.GPT "+ exception);}try{IDG.GPT.addTarget("templateType", 'article-default');} catch (exception) {console.log ("google_dfp can't use IDG.GPT "+ exception);}

  try {IDG.GPT.addTarget("categoryIds", [3255]);} catch (exception) {console.log ("google_dfp can't use IDG.GPT "+ exception);}
  try{IDG.GPT.addTarget("categorySlugs", ['cloud-computing']);} catch (exception) {console.log ("google_dfp can't use IDG.GPT "+ exception);}
  try {
    if (null != IDG.GPT.getQsVal("env")) {
      IDG.GPT.addTarget("env", IDG.GPT.getQsVal("env").replace(/\W/g, "") );
    }
  }
  catch (exception) {
    console.log ("google_dfp can't use IDG.GPT "+ exception);
  }

    try {
      IDG.GPT.addTarget("author", 'David Spark');
    }
    catch (exception) {
      console.log ("google_dfp can't use IDG.GPT "+ exception);
    }





  try {
    IDG.GPT.addTarget("page_type", '');
  }
  catch (exception) {
    console.log ("google_dfp can't use IDG.GPT "+ exception);
  }


    try {
      IDG.GPT.addTarget("blog_name", 'Empowering Business');
    }
    catch (exception) {
      console.log ("google_dfp can't use IDG.GPT "+ exception);
    }



    try {
      IDG.GPT.addTarget("sponsored", 'true');
    }
    catch (exception) {
      console.log ("google_dfp can't use IDG.GPT "+ exception);
    }


    try {
      IDG.GPT.addTarget("insiderContent", 'false');
    }
    catch (exception) {
      console.log ("google_dfp can't use IDG.GPT "+ exception);
    }



</script>


<script type="text/javascript" src="/www/js/ads/proximic.js?v=20160525110859"></script>
<script type="text/javascript">
   var currentUrl = encodeURIComponent(window.location.href);
   (function (u) {
    var f = "scr";
    var e = (u ? ("url=" + u) : "");
    document.write("<"+f+"ipt type='text/javascript' src='http://t.zqtk.net/c.js?m=1&s=idg_js&"+e.replace(/\'/g, "")+"'></"+f+"ipt>");
    })((typeof (currentUrl) === "undefined") ? "" : currentUrl);
</script>


<script type="text/javascript">




</script>

<script src="/www.idge/js/thm_pre.js?v=20160525110859"></script>


  <script>
    var isMobile = (typeof IDG.DEVICE !== 'undefined') ? IDG.DEVICE.isMobile() : false;








    IDG.GPT.addBiddingParams("yieldbotPsn", (isMobile ? "dda9" : "c1ed"));
    IDG.GPT.addBiddingParams("appnexusPlacementId", (isMobile ? "6451281" : "6352906"));
  </script>
  <script type="text/javascript" src="/www/js/ads/prebid_launcher.js?v=20160525110859"></script>

<script type="text/javascript" src="/www/js/ads/gpt_launcher.js?v=20160525110859"></script>



  <script src="/www/js/swfobject.js?v=20160525110859"></script>

    <script src="/www.idge.ans/js/select2-3.5.0/select2.js"></script>






    <script>
       $(document).ready(function() {
         $("select#country").select2({
            placeholder: "country",
            minimumResultsForSearch: Infinity,
            allowClear: true
          });
          $("#jobPosition").select2({
            placeholder: "position",
            minimumResultsForSearch: Infinity,
            allowClear: true
          });
          $("#jobFunction").select2({
            placeholder: "function",
            minimumResultsForSearch: Infinity,
            allowClear: true
          });
          $("#companySize").select2({
            placeholder: "company size",
            minimumResultsForSearch: Infinity,
            allowClear: true
          });
          $("#industry").select2({
            placeholder: "industry",
            minimumResultsForSearch: Infinity,
            allowClear: true
          });
       });
     </script>













<script>

var brandCode = "cio";
var brandName="CIO";
var regDebugLog=false;
var regApiUrl=" http://regdev.idge.int/api/ ";
var tokenPrefix='www';
var tokenSuffix='.com/insider/token?';
var brandDomain=".cio.com";
var insiderContentType="article";
var notEmptyArticle=false;
var notEmptyMediaResource=false;
var isInsiderPremium=false;
var isResourceInsiderPremium=false;


  regApiUrl="http://reg.idgenterprise.com/api/";



  regDebugLog=false;



  tokenPrefix="http://www";



  tokenSuffix="/insider/token";





  notEmptyArticle=true;








</script>

<script src="/www/js/utils/hashes.js?v=20160525110859"></script>
<script src="/www/js/utils/alc.js?v=20160525110859"></script>
<script src="/www/js/insider/insider_reg_api.js?v=20160525110859"></script>
<script src="/www/js/insider/jquery.maskedinput-1.4.min.js"></script>

<script>
$(document).ready(function(){
  if(typeof(IDG.insiderReg.readCookie('nsdr')) !== 'undefined'){
    $('#welcome-message').show();
    IDG.insiderReg.personalize();
  }else{
    $('.signin-register').show();
  }
});

</script>




  <!-- All JavaScript at the bottom, except for Modernizr which enables HTML5 elements & feature detects -->
  <script src="/www.idge/js/mule/modernizr.js?v=20160525110859"></script>
  <script src="/www.idge/js/mule/respond.min.js?v=20160525110859"></script>











<!-- Begin Eloqua Tracking -->
<script type="text/javascript" src="/www/js/analytics/eloqua/elqCfg.js"></script>
<script type="text/javascript" src="/www/js/analytics/eloqua/elqImg.js"></script>
<script type="text/javascript" src="/www/js/analytics/eloqua/elqFCS.js"></script>
<script type="text/javascript" src="/www/js/analytics/eloqua/elqScr.js"></script>
<script type="text/javascript" src="/www/js/analytics/eloqua/elqIDG.js"></script>



















<script type="text/javascript">
  IDG = window.IDG || {};
  IDG.PageInfo = IDG.PageInfo || {};
  IDG.PageInfo = function() {
    return {
      eloqua_type: "blogpost",
      eloqua_topic: "cloudcomputing"
    };
  }();
  var elqCustomerGUID = "";
  if (this.GetElqCustomerGUID && typeof GetElqCustomerGUID === "function") {
          elqCustomerGUID = GetElqCustomerGUID();
  }
  IDG.Eloqua.invoke_flash();
</script>
<!-- End Eloqua Tracking -->



  <script type="text/javascript">
    var g_arrModules = new Array();
  </script>






  <script src='https://www.google.com/recaptcha/api.js'></script>











































































    <meta name="parsely-title" content="20 Ways to Measure the Success of Your Growing Cloud Investment" />
    <meta name="parsely-link" content="http://www.cio.com/article/2929788/cloud-computing/20-ways-to-measure-the-success-of-your-growing-cloud-investment.html" />
    <meta name="parsely-type" content="NewsArticle" />
    <meta name="parsely-image-url" content="http://images.techhive.com/images/article/2015/06/msftone_cloudperspective4-100588855-orig.png" />
    <meta name="parsely-pub-date" content="2015-06-03T14:06:00Z" />
    <meta name="parsely-section" content="Cloud Computing" />
    <meta name="parsely-author" content="David Spark" />
    <meta name="parsely-tags" content="blog: Empowering Business,type: news,source: cio,original" />





  <script src="http://a.postrelease.com/serve/load.js?async=true"></script>


















  <script src="/www/js/analytics/mouse_down.js?v=20160525110859"></script>

  <script>
  dataLayer=[{
         'primaryCategory': 'Cloud Computing',
         'articleType': 'News',
         'articleId': '2929788',
         'blogName':'Empowering Business',

          'brandpost':'true',


          'parselyEnabled':'true',
          'parselyDomain':'cio.com',
        'ga_enabled':'true',

        'viglinksId':'43a59949636e2ec4b588715c676d1993'
  }];
  </script>














  <!-- Google Tag Manager -->
  <noscript><iframe src="//www.googletagmanager.com/ns.html?GTM-NCMJGX"
  height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
  <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
  new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
  j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
  '//www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
  })(window,document,'script','dataLayer','GTM-NCMJGX');</script>
  <!-- End Google Tag Manager -->


</head>



  <body id="article2929788" class="article blog1469 cio">














<!-- BEGIN PAGE HEADER -->
<header id="banner" >

    <div class="ad">





























        <div id="topleaderboard" class="">
        </div>
        <script type="text/javascript">
          IDG.GPT.addDisplayedAd("topleaderboard", "true");
          $('#topleaderboard').responsiveAd({screenSize:'971 1115', scriptTags: []});
        </script>





    </div>



      <script>
      if(!$thm.deviceWidthAtLeast($thm.deviceBreakpoints.tablet)){
      $('.main-header').insertBefore($('#banner .ad'));
      }
    </script>


    <div class="topics-wrap">
        <section class="topics">
            <nav id="scrollable">






























    <!-- blx4 #877 blox4.raw_link_list  -->








































  <ul>

    <li class="header">Trending<span class="colon">:</span></li>



        <li><a href="/article/3023380/leadership-management/read-cios-january-2016-digital-magazine-state-of-the-cio.html" >State of the CIO 2016</a></li>



        <li><a href="http://www.cio.com/category/cio100/" target="_blank" rel="nofollow">CIO 100</a></li>



        <li><a href="/category/careers-staffing" >Careers</a></li>



        <li><a href="/category/consumer-technology" >Consumer Electronics</a></li>



        <li><a href="/category/it-strategy" >IT Strategy</a></li>



        <li><a href="/category/security" >Security</a></li>



        <li><a href="/category/mobile" >Mobile</a></li>



        <li><a href="/resources/" >Resources/White Papers</a></li>



        <li><a href="/category/cio-role/" >CIO Role</a></li>


  </ul>





            </nav>

            <div class="fading-gradient"></div>


            <nav class="tools">
              <ul class="user">

                        <li class="search-icon"><a href="#search" id="search-btn"><i class="ss-icon">Search</i></a></li>

                   </ul>
                   <ul class="social">















  <li><a href="https://twitter.com/CIOonline" itemprop="sameAs" rel="nofollow" target="_blank" onclick="brandFollowTrack('Twitter')"><i class="ss-icon ss-social-circle ss-twitter"></i></a></li>



  <li><a href="https://www.linkedin.com/company/cio-online" itemprop="sameAs" rel="nofollow" target="_blank" onclick="brandFollowTrack('LinkedIn')"><i class="ss-icon ss-social-circle brand ss-linkedin"></i></a></li>



  <li><a href="https://www.facebook.com/CIOfacebook" itemprop="sameAs" rel="nofollow" target="_blank" onclick="brandFollowTrack('Facebook')"><i class="ss-icon ss-social-circle brand ss-facebook"></i></a></li>



  <li><a href="https://plus.google.com/+CIOOnline" itemprop="sameAs" rel="publisher" target="_blank" onclick="brandFollowTrack('Google+')"><i class="ss-icon ss-social-circle brand ss-googleplus"></i></a></li>



  <li><a href="https://www.youtube.com/user/CIOtv" itemprop="sameAs" rel="nofollow" target="_blank" onclick="brandFollowTrack('YouTube')"><i class="ss-icon ss-social-circle brand ss-youtube"></i></a></li>





  <li><a href="/about/rss/" target="_blank"><i class="ss-icon ss-social-circle ss-rss"></i></a></li>




  <li class="more-social"><i class="ss-icon ss-ellipsis"></i></li>

                   </ul>
               </nav>

        </section>
      </div>


           <div id="sticky-anchor"></div>

      <section id="sticky-head" class="main">
          <div class="wrapper">
              <div class="masthead">

                  <button><i class="ss-icon ss-rows"></i></button>

                  <a href="/"><span class="ir logo">cio</span></a>

                <!--
                <img class="print-only" src="/www.idge.cio/images/cio-logo-print.png">
                -->

                <img class="print-only" src="http://idge.staticworld.net/cio/cio-logo-print.png">

              </div>
              <div id="reading-tools-wrapper">


                  <nav class="tools">

                      <ul class="user">






                      </ul>
                  </nav>
              </div>


                <div class="insider-signin">

                  <span class="signin-register">
                    <a href="/learn-about-insider/"><span class="insider"></span></a> <a href="javascript://" onclick="IDG.insiderReg.registerLinkEvent('insider-reg-signin')" class="js-open-modal signin" data-modal-id="insider-popup">Sign In</a> <span class="divider">|</span> <a href="javascript://" onclick="IDG.insiderReg.registerLinkEvent('insider-reg-form-short')" class="js-open-modal register" data-modal-id="insider-popup">Register</a>
                  </span>

                  <div id="welcome-message" class="insider-list">
                        <div id="insider-selector">
                            <div id="insider-welcome" class="label"></div>

                            <div class="stories">

                              <div class="personalization">Hi<span id="person-first-name"></span>! Here are the latest Insider stories.</div>



















































































  <ul>














































<li><a  href="/article/3075494/leadership-management/its-new-imperative-partnering-to-shape-the-future.html">IT’s new imperative: Partnering to shape the future</a></li>

















































<li><a  href="/article/3074532/networking/tips-for-adding-ipv6-to-ipv4-networks.html">Tips for adding IPv6 to IPv4 networks</a></li>

















































<li><a  href="/article/3074931/application-development/powerapps-first-look-create-mobile-apps-without-coding.html">PowerApps first look: Create mobile apps without coding</a></li>

















































<li><a  href="/article/3074328/security/by-the-numbers-cyber-attack-costs-compared.html">By the numbers: Cyberattack costs compared</a></li>




  </ul>



                  <div class="sign-out-link">
                    <a class="more-insider" href="/insider/">More Insider</a>
                    <a class="sign-out" href="javascript://" onclick="IDG.insiderReg.logout()">Sign Out</a>
                  </div>
                </div>

                        </div>
                    </div>

                </div>
                <script>
                  $('.signin-register').hide();
                  $('#welcome-message').hide();
                </script>



          </div>
      </section>
      <section class="tools-expand  ">







              <form class="search" id="search-form" action="/search">
                  <label for="search-box">Search for</label>
            <input type="text" name="query" id="banner-search-term" value="" />
                  <input type="submit" id="banner-search-submit" value="Go" />
                  <input type="hidden" name="contentType" value="article,resource" />
                  <div class="search-suggest">Suggestions for you</div>
              </form>


          <div class="user">
              <form>
                  <label for="login-email">Insider email</label> <input id="login-email" type="text" />
              </form>
          </div>

      </section>





        <div id="sticky-anchor-nav"></div>


      <nav id="sticky-nav" class="main">
        <ul class="topnav">





                  <li><a href="/category/big-data/">Big Data</a>





              </li>





                  <li><a href="/category/cio100/">CIO 100 Symposium and Awards</a>





              </li>





                  <li><a href="/category/careers-staffing/">Careers/Staffing</a>





              </li>





                  <li><a href="/category/cloud-computing/">Cloud Computing</a>





              </li>





                  <li><a href="/category/consumer-electronics/">Consumer Technology</a>





              </li>





                  <li><a href="/category/developer/">Developers</a>





              </li>





                  <li><a href="/category/hardware/">Hardware</a>





              </li>





                  <li><a href="/category/healthcare/">Healthcare</a>





              </li>





                  <li><a href="/category/it-industry/">IT Industry</a>





              </li>





                  <li><a href="/category/it-strategy/" class="hasChildren">IT Strategy<span class="ss-icon ss-navigatedown"></span></a>





                    <ul class="subnav">
                        <li><a href="/category/it-strategy/">All IT Strategy</a></li>

                        <li><a href="/category/cio-role/">CIO Role</a></li>

                        <li><a href="/category/cmo-role/">CMO Role</a></li>

                        <li><a href="/category/innovation/">Innovation</a></li>

                        <li><a href="/category/leadership-management/">Leadership and Management</a></li>

                        <li><a href="/category/outsourcing/">Outsourcing</a></li>

                    </ul>

              </li>





                  <li><a href="/category/infrastructure/" class="hasChildren">Infrastructure<span class="ss-icon ss-navigatedown"></span></a>





                    <ul class="subnav">
                        <li><a href="/category/infrastructure/">All Infrastructure</a></li>

                        <li><a href="/category/data-center/">Data Center</a></li>

                        <li><a href="/category/networking/">Networking</a></li>

                        <li><a href="/category/infrastructure-storage/">Storage</a></li>

                        <li><a href="/category/virtualization/">Virtualization</a></li>

                    </ul>

              </li>





                  <li><a href="/category/insider-threats/">Insider Threats</a>





              </li>





                  <li><a href="/category/internet/" class="hasChildren">Internet<span class="ss-icon ss-navigatedown"></span></a>





                    <ul class="subnav">
                        <li><a href="/category/internet/">All Internet</a></li>

                        <li><a href="/category/marketing/">Marketing</a></li>

                    </ul>

              </li>





                  <li><a href="/category/mobile/" class="hasChildren">Mobile<span class="ss-icon ss-navigatedown"></span></a>





                    <ul class="subnav">
                        <li><a href="/category/mobile/">All Mobile</a></li>

                        <li><a href="/category/mobile-apps/">Mobile Apps</a></li>

                        <li><a href="/category/mobile-device-management/">Mobile Management</a></li>

                        <li><a href="/category/smartphones/">Smartphones</a></li>

                        <li><a href="/category/tablets/">Tablets</a></li>

                        <li><a href="/category/wearable-technology/">Wearables</a></li>

                    </ul>

              </li>





                  <li><a href="/category/operating-systems/" class="hasChildren">Operating Systems<span class="ss-icon ss-navigatedown"></span></a>





                    <ul class="subnav">
                        <li><a href="/category/operating-systems/">All Operating Systems</a></li>

                        <li><a href="/category/linux/">Linux</a></li>

                        <li><a href="/category/windows/">Windows</a></li>

                    </ul>

              </li>





                  <li><a href="/category/security/" class="hasChildren">Security<span class="ss-icon ss-navigatedown"></span></a>





                    <ul class="subnav">
                        <li><a href="/category/security/">All Security</a></li>

                        <li><a href="/category/cyber-attacks-espionage/">Cybersecurity</a></li>

                        <li><a href="/category/disaster-recovery/">Disaster Recovery</a></li>

                        <li><a href="/category/malware/">Malware</a></li>

                        <li><a href="/category/privacy/">Privacy</a></li>

                        <li><a href="/category/regulation/">Regulation</a></li>

                    </ul>

              </li>





                  <li><a href="/category/software/">Software</a>





              </li>

      </ul>

        <ul class="secondary">












<li><a href="/about/contactus.html">Contact Us</a></li>
<li>
  <a href="/about/subscription-services.html" class="hasChildren" >
    Magazine
    <span class="ss-icon ss-navigatedown"></span>
  </a>
  <ul class="subnav">
    <li><a href="/about/subscription-services.html">Subscription Services</a></li>
    <li><a href="/magazine">Archive</a></li>
  </ul>
</li>
<li><a href="/news/">News</a></li>
<li><a href="/opinion/">Opinion</a></li>
<li><a href="/resources/">Resources</a></li>
<li><a href="/slideshows/">Slideshows</a></li>
<li><a href="/video/">Video</a></li>
<li>
  <a href="#" class="hasChildren">More <span class="ss-icon ss-navigatedown"></span></a>
  <ul class="subnav">

    <li><a href="http://council.cio.com/" target="_blank" rel="nofollow">CIO Executive Council</a></li>
    <li><a href="http://www.ereg.me/IDGEvents" target="_blank" rel="nofollow">CIO Events</a></li>
    <li><a href="http://itjobs.cio.com/" target="_blank" rel="nofollow">IT Jobs</a></li>
    <li><a href="/newsletters/signup.html">Newsletters</a></li>
    <li><a href="/about/rss.html">RSS</a></li>
  </ul>
</li>
<li><a href="/blogs/">Blogs</a></li>


        </ul>
    </nav>



    <script>
      if(!$thm.deviceWidthAtLeast($thm.deviceBreakpoints.tablet)){
      $('div.insider-signin').prependTo('section.topics nav.tools');
      }
    </script>


    <script>
    $(document).ready(function(){
      if($thm.deviceWidthAtLeast($thm.deviceBreakpoints.tablet)){
        $("nav ul li a.hasChildren").attr("href", "#").addClass('notMobile');
        $('nav#sticky-nav ul.secondary').addClass('topnav');
      }
      if(!$thm.deviceWidthAtLeast($thm.deviceBreakpoints.tablet)){
        $("nav#sticky-nav > ul").switchClass("topnav", "primary");
      }
    });
    </script>



      <script>
      if(!$thm.deviceWidthAtLeast($thm.deviceBreakpoints.tablet)){
      $('#sticky-anchor, section.main, section.topics, section.tools-expand, #sticky-anchor-nav, nav.main').insertBefore($('#banner .ad'));
      $(document).ready(function(){
        $('#banner section.tools-expand').addClass('top-margin');
      });
      }
    </script>




</header>



<script>
$(document).ready(function(){
  $('body').addClass('insider-plus');

  var headerHeight = $('header#banner').height(); // height for desktop on load not fixed
  $('header#banner nav#sticky-nav.main.no-stick').css("top", headerHeight);

  if(!$thm.deviceWidthAtLeast($thm.deviceBreakpoints.tablet)){
    var stickyHeaderHeight = $('section.topics').height() + $('#sticky-head.main').height();
    stickyHeaderHeight = stickyHeaderHeight - 8;
  }

  // Listen for orientation changes - mobile devices have a problem resizing fixed position elements on
  // orientation change from portrait to landscape so scrolling to current position of header
  var supportsOrientationChange = "onorientationchange" in window,
    orientationEvent = supportsOrientationChange ? "orientationchange" : "resize";

  if($thm.deviceWidthAtLeast($thm.deviceBreakpoints.tablet)){
    window.addEventListener(orientationEvent, function() {
      var top = $('section#sticky-head.main').offset().top;
      if (top < 170) {
        top = 0; // so we don't miss the top leaderboard or ticker on change
      } else {
        top = top - 50; //acknowledging "shorter" top on landscape than portrait
      }
      if (window.orientation == 90 || window.orientation == -90) {
        window.scrollTo(0, top);
      }
    }, false);
  }

  if(!$thm.deviceWidthAtLeast($thm.deviceBreakpoints.tablet)){
    window.addEventListener(orientationEvent, function() {
      var top = $('section#sticky-head.main').offset().top;
      if (top < 50) {  // top is fixed on mobile so this is when user is at very top
        top = 5; // scroll 5 pixels from 0 to resize fixed header from portrait
      } else {
        top = top - 40;
      }
      if (window.orientation == 90 || window.orientation == -90) {
        window.scrollTo(0, top);
      }
    }, false);
  }

  //nexus 7 is sized mobile in portrait and tablet in landscape
  var ua = navigator.userAgent.toLowerCase();
  var isAndroid = ua.indexOf("android") > -1;
  window.addEventListener(orientationEvent, function() {
      if(isAndroid && (screen.width == 600 || screen.width == 960)) {
        location.reload(true); // refreshing page for changes to header
      }
  }, false);

  if ($thm.deviceWidthAtLeast($thm.deviceBreakpoints.desktop) && !$('html').hasClass('touch')){
    $("ul.social li.more-social").mouseenter(function() {
      $('ul.social > li').addClass('display-icon');
    });
    $("ul.social").mouseleave(function() {
      $('ul.social > li').removeClass('display-icon');
    });
  };

  if($thm.deviceWidthAtLeast($thm.deviceBreakpoints.tablet) && $('html').hasClass('touch')){
    $("ul.social li.more-social").click(function() {
      $('ul.social > li').toggleClass('display-icon');
    });
  };

});

function sticky_relocate() {
  var headerHeight = $('header#banner').height(); // height for desktop
  //$('header#banner nav#sticky-nav.main').css("top", headerHeight);

  var winHeight = $(window).height();
  // desktop
  if ( $thm.deviceWidthAtLeast($thm.deviceBreakpoints.wide ) ){
    winHeight = winHeight - 40;
    if (window.orientation == 90 || window.orientation == -90) {
      winHeight = winHeight + 50; // landscape tablet 1024
    }
  }// tablet
  else if ( $thm.deviceWidthAtLeast($thm.deviceBreakpoints.tablet) && !$thm.deviceWidthAtLeast($thm.deviceBreakpoints.wide) ) {
    winHeight = winHeight + 40;
  } else { // mobile
    winHeight = winHeight + 10;
  }

  $('#banner nav#sticky-nav.main').css("height", winHeight); // flyout nav height

  // tablet and desktop
  if($thm.deviceWidthAtLeast($thm.deviceBreakpoints.tablet)){
      var window_top = $(window).scrollTop();
      var div_top = $('#sticky-anchor').offset().top;
      if (window_top > div_top) {
          $('#sticky-head').addClass('stick');
          $('#page-wrapper').addClass('top-pad');
          $('#banner .ad').addClass('top-margin');
      } else {
          $('#sticky-head').removeClass('stick');
          $('#page-wrapper').removeClass('top-pad');
          $('#banner .ad').removeClass('top-margin');
      }
  }
  // mobile
  if(!$thm.deviceWidthAtLeast($thm.deviceBreakpoints.tablet)){
    $('section.topics').addClass('stick-topics');
    $('#sticky-head').addClass('stick-head');
    $('#page-wrapper').addClass('top-pad');
    $('#banner .ad').addClass('top-margin');
    $('#banner section.tools-expand').addClass('search');

    var window_top = $(window).scrollTop();
    var div_top = $('#sticky-anchor').offset().top + 30;
      if (window_top > div_top) {
        $('#sticky-head').addClass('stick');
        $('.topics.stick-topics, #search-form').addClass('remove');
        $('li.search-icon').fadeOut("fast");
      } else {
        $('#sticky-head').removeClass('stick');
        $('.topics.stick-topics, #search-form').removeClass('remove');
        $('li.search-icon').fadeIn("fast");
      }
  }// end mobile

  // tablet and desktop flyout nav
  if($thm.deviceWidthAtLeast($thm.deviceBreakpoints.tablet)){
      if ($('#sticky-head').hasClass('stick')) {
        var stickyHeaderHeight = $('section.main').height(); // height of the fixed header for tablet and desktop
        stickyHeaderHeight = 52; // setting fixed height - same across all sites
      $('header#banner nav#sticky-nav.main').css("top", stickyHeaderHeight);
          $('#sticky-nav').addClass('stick');
          $('#sticky-nav').removeClass('no-stick');
      } else {
        stickyHeaderHeight = $('header#banner').height();
        $('header#banner nav#sticky-nav.main').css("top", stickyHeaderHeight);
          $('#sticky-nav').removeClass('stick');
          $('#sticky-nav').addClass('no-stick');
      }

  }// end tablet/desktop


  // mobile flyout nav
  if(!$thm.deviceWidthAtLeast($thm.deviceBreakpoints.tablet)){

      var stickyHeaderHeight = $('section.topics').height() + $('#sticky-head.main.stick-head').height(); // height of the fixed header for mobile
      stickyHeaderHeight = stickyHeaderHeight - 8;  // 8 seems appropriate
      $('header#banner nav#sticky-nav.main').css("top", stickyHeaderHeight);
        $('#sticky-nav').addClass('stick');

        if ($('#sticky-head').hasClass('stick')) {
          $('#sticky-head.stick').siblings('#sticky-nav').addClass('nav-stick');
          $('#sticky-head').siblings('#sticky-nav').removeClass('no-stick');
        } else {
          $('#sticky-head').siblings('#sticky-nav').removeClass('nav-stick');
          $('#sticky-head').siblings('#sticky-nav').addClass('no-stick');
        }

  }// end mobile


} // end sticky_relocate

$(function () {
    $(window).scroll(sticky_relocate);
    sticky_relocate();
});
</script>



  <div id="insider-popup" class="modal-box ">

    <div class="insider-modal-wrapper">

      <div class="modal-header-title">
        <div class="modal-banner-insider"><span class="insider"></span></div>
          <div class="modal-close close">×</div>
        </div>

        <div class="modal-body">


            <script>
$(document).ready(function(){
  $('a[data-link]').click(function() {

      //get this link's dataLink value
      var dataLink = $(this).attr('data-link');

      //select the div with the same value
      var toKeep = 'div[data-link="'+dataLink+'"]';

      //select data-link divs that are not the above div
      $('div[data-link]').not(toKeep).hide();

      if($(this).hasClass('link-transition')){
        $(toKeep).fadeIn(800);
      } else {
        $(toKeep).show();
      }

      //prevent location change
      return false;
  });


});
</script>




        </div>

        <div class="modal-footer">
          <div class="modal-close close-btn">Close</div>
        </div>
    </div>

  </div>

  <script>
    $(document).ready(function(){

      var appendthis =  ("<div class='modal-overlay modal-close'></div>");
      if($thm.deviceWidthAtLeast($thm.deviceBreakpoints.tablet)){
        $('.modal-box').css({
          //top: ($(window).height() - $(".modal-box").outerHeight()) / 2,
          top: 20,
          left: 0
        });
      }


      if(!$thm.deviceWidthAtLeast($thm.deviceBreakpoints.tablet)){
        $('.modal-box').height($(document).height());
        $('.modal-box').css('top','0');
      }

      var userAg = navigator.userAgent;

      $('a[data-modal-id]').click(function(e) {
        e.preventDefault();
        $('body').append(appendthis);
        $('.modal-overlay').fadeTo(500, 0.9);
        $('.modal-overlay').height($(document).height());
        var modalBox = $(this).attr('data-modal-id');

        if ($(".modal-box").hasClass('triggered') || (userAg.indexOf("Firefox")!=-1)) {
          $('#'+modalBox).fadeIn($(this).data()); // triggered by something other than onclick of link such as scroll to point in window
        } else {
          if(!$thm.deviceWidthAtLeast($thm.deviceBreakpoints.tablet)){
            $('#'+modalBox).toggle('slide', {direction: 'up'}, 800);
          } else {
              $('#'+modalBox).toggle('slide', {direction: 'left'}, 600);
          }
        }

        if(!$thm.deviceWidthAtLeast($thm.deviceBreakpoints.tablet)){
          $("html, body").animate({
                  scrollTop: 0
              }, 700);


              $('a.btn.continue-on').click(function() {
                $('html, body').animate({
                  scrollTop: 0
                }, 500);
              });

              return false;

        }
        if (navigator.userAgent.match(/(iPad)/)) {
          $("html, body").animate({
                  scrollTop: 0
              }, 500);
        }
      });


      $(".modal-close, .modal-overlay").click(function() {
        $('.select2-drop').css('display','none');
        $(".modal-overlay").fadeOut(500, function() {
          $(".modal-overlay").remove();
        });
        if ($(".modal-box").hasClass('triggered') || (userAg.indexOf("Firefox")!=-1)) {
          $(".modal-box").fadeOut(500);
        } else {
          if(!$thm.deviceWidthAtLeast($thm.deviceBreakpoints.tablet)){
            $('.modal-box').toggle('slide', {direction: 'up'}, 800);
          } else {
            $('.modal-box').toggle('slide', {direction: 'left'}, 600);
          }
        }
      });

    });
  </script>


<!-- END PAGE HEADER -->




    <div id="page-wrapper" class="page-wrapper">
















<div id="skinAdTarget"></div>













































      <section role="main">

        <article itemscope itemtype="http://schema.org/BlogPosting"  class="blog" >





















      <div class="ticker-ad ad">





























        <div id="ticker" class="">
        </div>
        <script type="text/javascript">
          IDG.GPT.addDisplayedAd("ticker", "true");
          $('#ticker').responsiveAd({screenSize:'971 1115', scriptTags: []});
        </script>





      </div>






<!-- Events Header -->





















































<!-- //end Events Header -->

















































<header>










  <nav class="breadcrumbs horiz">
    <ul itemscope itemtype="http://data-vocabulary.org/Breadcrumb">
      <li><a href="/" itemprop="url"><span itemprop="title">Home</span></a></li>



          <li><a href="/category/cloud-computing/" itemprop="url"><span itemprop="title">Cloud Computing</span></a></li>



    </ul>
  </nav>



    <div class="blog-byline vcard author with-image" itemscope itemtype="http://schema.org/Person" itemprop="author">

      <div class="brand sponsored">

























  <a href="/blog/empowering-business/">
    <img  class="originalImage  imgId100577596 " src="http://core1.staticworld.net/images/article/2015/04/msftlogo_black-100577596-orig.jpg" width="34" width="34" alt="Empowering Business" itemprop="image" />
  </a>






        <div class="blog-branding-text">

            <span class="brandpost">BrandPost</span> Sponsored by Microsoft <span class="divider">|</span> <a class="learn-more" href="#">Learn More</a>

          <div class="blog-title"><a href="/blog/empowering-business/">Empowering Business</a></div>


        </div>
      </div>

        <div class="about">
          <div class="about-title">About <span class="divider">|</span> <a href="/blog/empowering-business/index.rss"><i class="ss-icon ss-social ss-rss"></i></a></div>
          <p>Enterprises are leveraging cloud services to improve efficiency, increase agility and unlock new opportunities. </p>
        </div>

    </div>







      <div class="category">sponsored</div>




  <h1 itemprop="headline">20 Ways to Measure the Success of Your Growing Cloud Investment</h1>

</header>













<section class="epo" id="drr-top-ad">










































<div class="ad">

































        <div id="topimu" class="adunit">
        </div>
        <script type="text/javascript">
          IDG.GPT.addDisplayedAd("topimu", "true");
          $('#topimu').responsiveAd({screenSize:'971 1115', scriptTags: []});
        </script>







</div>



</section>















      <figure itemprop="image" itemscope itemtype="http://schema.org/ImageObject" class="hero-img">
      <meta itemprop="representativeOfPage" content="1">

          <img src="http://images.techhive.com/images/article/2015/06/msftone_cloudperspective4-100588855-primary.idge.png" alt="msftone cloudperspective4" itemprop="contentUrl" />
          <figcaption>


          </figcaption>
      </figure>




















































<div id="sharer" class="sidecar">
<ul id="share-tool">
  <li class="close-btn"> <i class="ss-icon ss-delete"></i> </li>

  <li class="sosh">
    <a href="https://twitter.com/intent/tweet?url=http%3A%2F%2Fwww.cio.com%2Farticle%2F2929788%2Fcloud-computing%2F20-ways-to-measure-the-success-of-your-growing-cloud-investment.html&via=CIOonline&text=20+Ways+to+Measure+the+Success+of+Your+Growing+Cloud+Investment" target="_blank" onclick="sharingTrack('Twitter-vert', 'twshare')">
      <img class="sidecar-icon" src="http://idge.staticworld.net/images/twitter.svg" />
    </a>
  </li>
  <li class="sosh">
    <a href="https://www.facebook.com/sharer/sharer.php?u=http%3A%2F%2Fwww.cio.com%2Farticle%2F2929788%2Fcloud-computing%2F20-ways-to-measure-the-success-of-your-growing-cloud-investment.html" target="_blank" onclick="sharingTrack('Facebook-vert', 'fblike')">
      <img class="sidecar-icon" src="http://idge.staticworld.net/images/facebook.svg" />
    </a>
  </li>
  <li class="sosh">
    <a href="http://www.linkedin.com/shareArticle?url=http%3A%2F%2Fwww.cio.com%2Farticle%2F2929788%2Fcloud-computing%2F20-ways-to-measure-the-success-of-your-growing-cloud-investment.html&title=20+Ways+to+Measure+the+Success+of+Your+Growing+Cloud+Investment" target="_blank" onclick="sharingTrack('LinkedIn-vert')">
      <img class="sidecar-icon" src="http://idge.staticworld.net/images/linkedin.svg" />
    </a>
  </li>
  <li class="sosh">
    <a href="https://plus.google.com/share?url=http%3A%2F%2Fwww.cio.com%2Farticle%2F2929788%2Fcloud-computing%2F20-ways-to-measure-the-success-of-your-growing-cloud-investment.html" target="_blank" onclick="sharingTrack('GooglePlus-vert', 'googplus')">
      <img class="sidecar-icon" src="http://idge.staticworld.net/images/googleplus.svg" />
    </a>
  </li>
  <li class="sosh hide">
    <a href="http://reddit.com/submit?url=http%3A%2F%2Fwww.cio.com%2Farticle%2F2929788%2Fcloud-computing%2F20-ways-to-measure-the-success-of-your-growing-cloud-investment.html&title=20+Ways+to+Measure+the+Success+of+Your+Growing+Cloud+Investment" target="_blank" onclick="sharingTrack('Reddit-vert')">
      <img class="sidecar-icon" src="http://idge.staticworld.net/images/reddit.svg" />
    </a>
  </li>
  <li class="sosh hide">
    <a href="http://www.stumbleupon.com/submit?url=http%3A%2F%2Fwww.cio.com%2Farticle%2F2929788%2Fcloud-computing%2F20-ways-to-measure-the-success-of-your-growing-cloud-investment.html" target="_blank" onclick="sharingTrack('ShareStumbleupPrint-vert')">
      <img class="sidecar-icon" src="http://idge.staticworld.net/images/stumbleupon.svg" />
    </a>
  </li>
  <li class="sosh hide">
    <a href="#email" id="email-icon">
      <img class="sidecar-icon" src="http://idge.staticworld.net/images/mail.svg" />
    </a>
  </li>
  <li class="sosh hide print">
    <a href="javascript:window.print();">
      <img class="sidecar-icon" src="http://idge.staticworld.net/images/print.svg" />
    </a>
  </li>
  <li class="more">
    <div class="more-icon" onclick="document.getElementById('share-tool').className = 'expand';"></div>
  </li>

</ul>
</div>


<script type="text/javascript">
  function encodeQueryData(params) {
    var ret = [];
    for (var paramKey in params) {
      ret.push(encodeURIComponent(paramKey) + "=" + encodeURIComponent(params[paramKey]));
    }
    return ret.join("&");
  }
  $(document).ready(function() {
    $(document).on("click","#email-icon",function(event) {
      event.preventDefault();
      sharingTrack('Email-vert');
      $('#emailModal').fadeIn(800);
    });

    $(document).on("click","#emailModal .close-btn",function(event) {
      event.preventDefault();
            $('#emailModal').hide();
            $("#emailModal .eml-friend").show();
            $(".eml-friend-success").hide();
            $(".eml-friend-error").hide();
      $('#emailModal').fadeOut(200);
      $('#email-to').val('');
      $('#email-from').val('');
      $('#name').val('');
      $('#personalization').val('');
      $('#eml-from-address-message').html("");
      $('#eml-to-address-message').html("");
      $('#eml-friend-captcha-message').html("");
      Recaptcha.reload();
    });

    $(document).on("submit","#emailModal form",function(event) {
      event.preventDefault();
      var $form = $(this);
      var action = $form.attr('action');
      var formData = $form.serialize();
      var emailFrom = $('#email-from').val();
      var emailTo = $('#email-to').val();
      var uresponse=$('#recaptcha_response_field').val()
      $('#eml-from-address-message').html("");
      $('#eml-to-address-message').html("");
      $('#eml-friend-captcha-message').html("");
      if (isValidEmailAddress(emailFrom) && isValidEmailAddress(emailTo) && uresponse !="") {
        // eloqua
        var eloquaParam = {
          AssetCountforCurrentCampaign : "1",
          AssetName : "",
          AssetTopic : "",
          AssetType : "",
          BuyingCycle : "",
          C_Address1 : "",
          C_Address2 : "",
          C_BusPhone : "",
          C_City : "",
          C_Company_Size1 : "",
          C_Country : "",
          C_EmailAddress : "",
          C_FirstName : "",
          C_Industry1 : "",
          C_Job_Role1 : "",
          C_LastName : "",
          C_State_Prov : "",
          C_Zip_Postal : "",
          ClientName : "",
          ProgramName : "",
          brand : "",
          elqFormName : "CentralRegistrationMasterForm",
          formId : "3062313",
          elqSiteId : 1856,
          elqCustomerGUID : elqCustomerGUID,
          elqCookieWrite : 0,
          friend_email : emailFrom,
          friend_article_title : "20 Ways to Measure the Success of Your Growing Cloud Investment",
          friend_taxo : "Cloud Computing",
          friend_source : "CIO",
          friend_article_url : "http://www.cio.com/article/2929788/cloud-computing/20-ways-to-measure-the-success-of-your-growing-cloud-investment.html",
          device_platform : navigator.userAgent
        };
        $.ajax("http://now.eloqua.com/e/f2.aspx", {
          type: 'GET',
          data: eloquaParam,
          success: function(data, textStatus, xhr) {
          },
          error: function(xhr, textStatus, errorThrown) {
          }
        });
        // eloqua
        var eloquaParamMini = {
          elqCustomerGUID : elqCustomerGUID,
          friend_taxo : "Cloud Computing",
          friend_source : "CIO",
          device_platform : navigator.userAgent
        };
        formData += "&" +encodeQueryData(eloquaParamMini);

        // email
        $.ajax(action, {
          type: 'POST',
          data: formData,
          success: function(data, textStatus, xhr) {
            if(data=='no-match') {
              Recaptcha.reload();
              $('#eml-friend-captcha-message').html("Invalid recaptcha - please re-enter the text below.");
            }
            else {
              $("#emailModal .eml-friend").hide();
              $(".eml-friend-success").fadeIn(800);
            }
          },
          error: function(xhr, textStatus, errorThrown) {
            $("#emailModal .eml-friend").hide();
            $(".eml-friend-error").fadeIn(800);
          }
        });
      }
      else {
        if(uresponse==""){
          $('#eml-friend-captcha-message').html("Please type the text below.");
        }
        if(!isValidEmailAddress(emailTo)){
          $('#eml-to-address-message').html("Please enter a valid email address.");
        }
        if(!isValidEmailAddress(emailFrom)){
          $('#eml-from-address-message').html("Please enter a valid email address.");
        }
      }
    });
  });

</script>


<script type="text/javascript">
 var RecaptchaOptions = {
    theme : 'clean'
 };
 </script>
<div class="modal eml-friend-wrapper" id="emailModal" style="display:none;">
  <form class="eml-friend" action="/eloquaemail/article?eloqua=true&id=2929788">
    <input type="hidden" name="title" value="20 Ways to Measure the Success of Your Growing Cloud Investment" />
    <input type="hidden" name="url" value="http://www.cio.com/article/2929788/cloud-computing/20-ways-to-measure-the-success-of-your-growing-cloud-investment.html" />
    <a href="#" class="close-btn"><i class="ss-icon ss-delete"></i></a>
    <h3>Email a friend</h3>

    <div class="input-row">
      <div id="eml-to-address-message" class="error-msg"></div>
      <h4 class="input-label">To</h4>
      <input id="email-to" value="" placeholder="Email address (required)" type="email" name="recipients" tabindex="1">
      <p class="addl-text">Use commas to separate multiple email addresses</p>
    </div>
    <hr />
    <div class="input-row">
      <div id="eml-from-address-message" class="error-msg"></div>
      <h4 class="input-label">From</h4>
      <input id="name" value="" placeholder="Name (optional)" type="text" name="name" tabindex="2">
    </div>
    <div class="input-row">
      <input id="email-from" value="" placeholder="Email address (required)" type="email" name="sender" tabindex="3">
    </div>
    <div class="input-row">
      <textarea name="message" placeholder="Check out this article I found on CIO." id="personalization" tabindex="4"></textarea>
    </div>
    <hr />
    <div class="input-row">
      <div id="eml-friend-captcha-message" class="error-msg"></div>
    </div>
    <script type="text/javascript"
         src="http://www.google.com/recaptcha/api/challenge?k=6LdPbfsSAAAAAB89GpzHsyYe-AGzZXYy9K_4KAnF">
      </script>
      <noscript>
         <iframe src="http://www.google.com/recaptcha/api/noscript?k=6LdPbfsSAAAAAB89GpzHsyYe-AGzZXYy9K_4KAnF"
             height="300" width="500" frameborder="0"></iframe><br>
         <textarea id="recaptcha_challenge_field" name="recaptcha_challenge_field" rows="3" cols="40">
         </textarea>
         <input id="recaptcha_response_field" type="hidden" name="recaptcha_response_field"
             value="manual_challenge">
     </noscript>
    <hr />
    <div class="submit">
      <input type="submit" value="Send" class="input-submit" tabindex="5">
      <a href="/about/privacy.html" class="submit-privacy" target="_blank">Privacy Policy</a>
    </div>
  </form>

  <div class="eml-ty eml-friend-success" style="display:none;">
    <a href="#" class="close-btn"><i class="ss-icon ss-delete"></i></a>
    <h3>Thank you</h3>
    <p class="msg-sent">Your message has been sent.</p>
  </div>
  <div class="eml-friend-error" style="display:none;">
    <a href="#" class="close-btn"><i class="ss-icon ss-delete"></i></a>
    <h3>Sorry</h3>
    <p class="msg-sent">There was an error emailing this page.</p>
  </div>
</div>




  <div class="comment-count-mobile">














<div id="comment-bubble-idge">
  <a href="#comments" class="comment comments-bubble-cta" onclick="sharingTrack('Comment-vert', 'click')">
    <span id="comment-count" class="comment-count-bubble"></span><span id="comment-text" class="comment-text-bubble"> Comments</span>
  </a>
</div>
  </div>







  <div class="byline vcard author with-image">
    <div class="byline-wrapper">
      <p class="name" itemscope itemtype="http://schema.org/Person" itemprop="author">























<a href="/author/David-Spark/" rel="author" title="David Spark"><img  class="bylineImage  imgId100571280 " src="http://core3.staticworld.net/images/article/2015/03/davidspark_headshot-100571280-byline.jpg" width="34" width="34" alt="David Spark" itemprop="image" /></a>





















By <span class="fn" itemprop="name"><a rel="author" itemprop="url" href="/author/David-Spark/"><span itemprop="name">David Spark</span></a></span>
      </p>
























  <div class="meta">


        <a href="#follow" class="follow_btn">Follow</a>

      <ul >

        <!-- Always display RSS feed on hover: IDGMPM-6374 -->
        <li><a href="/author/David-Spark/index.rss"><i class="ss-icon ss-social ss-rss"></i></a></li>


        <li><a href="https://twitter.com/@dspark" rel="nofollow" onclick="followTrack()" target="_blank"><i class="ss-icon ss-social ss-twitter"></i></a></li>







        <li><a href="https://www.linkedin.com/in/davidspark" rel="nofollow" onclick="followTrack()" target="_blank"><i class="ss-icon ss-social ss-linkedin"></i></a></li>


      </ul>

  </div>



      <p class="dateline">



        <span itemprop="datePublished" content="2015-06-03T07:06-0700">Jun 3, 2015 7:06 AM
           PT
        </span>

      </p>
    </div>
  </div>












        <section class="bodee">















































    <div class="apart-alt tags">
    <span class="related">RELATED TOPICS</span>
    <ul itemprop="keywords">

      <li><a href="/category/cloud-computing">Cloud Computing</a></li>










    </ul>
  </div>



<div class="comment-count-main">














<div id="comment-bubble-idge">
  <a href="#comments" class="comment comments-bubble-cta" onclick="sharingTrack('Comment-vert', 'click')">
    <span id="comment-count" class="comment-count-bubble"></span><span id="comment-text" class="comment-text-bubble"> Comments</span>
  </a>
</div>
</div>





















<script type="text/javascript">

$(document).ready(function() {
  $('.articleBloxAd').filter( ":visible" ).each(function(index, item) {
    var id = $(item).attr('id');
    var divClass = $(item).attr('class');
    var adString = IDG.GPT.getLazyAdCode();
    $(item).replaceWith("<div id=\"" + id + "\" class=\"lazyload_blox_ad " + divClass + "\">" + adString + "</div>");
  });
  try {
    $("div.lazyload_blox_ad").lazyLoadAd({
          threshold    : 500,         // You can set threshold on how close to the edge ad should come before it is loaded. Default is 0 (when it is visible).
          forceLoad    : false,       // Ad is loaded even if not visible. Default is false.
          onLoad       : false,       // Callback function on call ad loading
          onComplete   : false,       // Callback function when load is loaded
          timeout      : 1500,        // Timeout ad load
          debug        : false,       // For debug use : draw colors border depends on load status
          xray         : false        // For debug use : display a complete page view with ad placements
    }) ;
  }
  catch (exception){
    console.log("error loading lazyload_ad " + exception);
  }
});

</script>









































<script type="text/javascript">

$(function() {
  var debug = false;
  var topImuHeight = 250;
  try {
    topImuHeight = getTallestNoScroll();
  }
  catch (e) {
    console.log(e);
  }
  var setInt = setInterval(checkHeight, 100);
  var count = 0;

  function checkHeight() {
    if ((topImuHeight > 0 && count > 10) || count > 50) {
      if (!topImuHeight > 0 ) {
        try {
          topImuHeight = getTallestWithScroll();
        }
        catch (e) {
          console.log(e);
        }
      }
      if (! topImuHeight > 0 || topImuHeight > 1200) {
        if (debug) {
          console.log("topimuheight is out of range so manually set to 250");
        }
        topImuHeight = 250;
      }
      clearInterval(setInt);
      executeDRR(topImuHeight);
    }
    else {
      if (debug) {
        console.log("have to check height again and counter is: " + count);
      }
      try {
        topImuHeight = getTallestNoScroll();
      }
      catch (e) {
        console.log(e);
      }
      count++;
    }
  }

  function getTallestNoScroll() {
      var bodyOffset = 0;
      var htmlHeight = 0;
      var htmlOffset = 0;

      if ($('#topimu div iframe').length) {
      var body = document.getElementById('topimu').getElementsByTagName('iframe')[0].contentWindow.document.body;
        var html = document.getElementById('topimu').getElementsByTagName('iframe')[0].contentWindow.document.documentElement;

        if (body != null) {
          bodyOffset = body.offsetHeight;
        }
        if (html != null) {
          htmlHeight = html.clientHeight;
          htmlOffset = html.offsetHeight;
        }
        if (debug) {
          console.log("in getTallestNoScroll");
          if (body != null && body.offsetHeight != null) { console.log("offsetHeight is " + body.offsetHeight); }
        if (body != null && body.scrollHeight != null) { console.log("scrollHeight is " + body.scrollHeight); }
        if (html != null && html.clientHeight != null) { console.log("htmlClientHeight is " + html.clientHeight); }
        if (html != null && html.scrollHeight != null) { console.log("htmlScrollHeight is " + html.scrollHeight); }
        if (html != null && html.offsetHeight != null) { console.log("htmlOffsetHeight is " + html.offsetHeight); }
        }
      }
      return Math.max(bodyOffset, htmlHeight, htmlOffset);
  }

  function getTallestWithScroll() {
      var bodyOffset = 0;
      var bodyScroll = 0;
      var htmlHeight = 0;
      var htmlOffset = 0;
      var htmlScroll = 0;

      if ($('#topimu div iframe').length) {
      var body = document.getElementById('topimu').getElementsByTagName('iframe')[0].contentWindow.document.body;
        var html = document.getElementById('topimu').getElementsByTagName('iframe')[0].contentWindow.document.documentElement;
        if (body != null) {
          bodyOffset = body.offsetHeight;
          bodyScroll = body.scrollHeight;
        }
        if (html != null) {
          htmlHeight = html.clientHeight;
          htmlOffset = html.offsetHeight;
          htmlScroll = html.scrollHeight;
        }
        if (debug) {
          if (body != null && body.offsetHeight != null) { console.log("offsetHeight is " + body.offsetHeight); }
          if (body != null && body.scrollHeight != null) { console.log("scrollHeight is " + body.scrollHeight); }
        if (html != null && html.clientHeight != null) { console.log("htmlClientHeight is " + html.clientHeight); }
        if (html != null && html.scrollHeight != null) { console.log("htmlScrollHeight is " + html.scrollHeight); }
        if (html != null && html.offsetHeight != null) { console.log("htmlOffsetHeight is " + html.offsetHeight); }
        }
      }
    return Math.max(bodyOffset, bodyScroll, htmlHeight, htmlOffset, htmlScroll);
  }
});

function executeDRR(imuHeight) {
    var debug = false;
    var topImuHeight = (imuHeight != null && imuHeight > 0) ? imuHeight : 250;
    var socialPages = ['facebook', 'twitter', 'reddit', 'tumblr'];

    // Set the calls to module content for right rail
    var articleDRRModuleList = ["module.resources", "most.popular.articles", "module.category.articles", "module.epo","module.answers.promo"];
    var resourceLibraryUrl = "";
    var moduleUrls = [];
    for (var i=0; i<articleDRRModuleList.length; i++) {
      var articleDRRModuleName = articleDRRModuleList[i];
      if (articleDRRModuleName == "module.epo") {
        articleDRRModuleName = getEpoParams();
      }
      if (articleDRRModuleName == "module.resources") {
        if (true) {
          continue;
        }
        articleDRRModuleName = "module.resources&pageSize=2&catId=3255";
        if (resourceLibraryUrl != null) {
          articleDRRModuleName += "&titleLink=" + resourceLibraryUrl;
        }
      }
      if (articleDRRModuleName == "module.category.articles") {
        if (true) {
          articleDRRModuleName = "module.category.articles&catId=3255";
        }
      }
      if (articleDRRModuleName.indexOf("module.resources")>=0) {
        moduleUrls.push("/napi/tile?def=" + articleDRRModuleName);
      }
      else if (articleDRRModuleName == "module.answers.promo") {
        moduleUrls.push("/napi/tile?def=module.answers.promo&catId=3255");
      }
      else {
        moduleUrls.push("/napi/tile?def=" + articleDRRModuleName + "&excludeCurrentArticle=true&excludeIds=2929788");
      }
    }

    var leftModuleUrl = "/napi/tile?def=insider.item";
    var leftModuleElementId = "drr-left";

    // Constants
    var interModuleHeight = 400;
    var grafHeight = 25;
    var moduleHeightBuffer = 350;
    var adHeightBuffer = 350;
    var mobileBreak = 768; // Width at which design becomes one column at top of article
    var leftPixelWindow = 700; // Need at least this much space to place left rail module near end of article
    var rightPixelWindow = 300;

    if (debug) {
      var topImu = $("#topimu").height(); // not using this one anymore
      console.log("old topImu height would have used this " + topImu);
    }

    var tagHeight = $(".tags").height();
    var cumulativeHeight = 0;
    var loopCounter = 0;
    var placementTarget = topImuHeight + moduleHeightBuffer + grafHeight + interModuleHeight;

    if ($(window).width() > mobileBreak) {
      // Desktop view
      if ($("figure.hero-img").height()) {
        cumulativeHeight += $("figure.hero-img").height();
      }
    }
    else {
      // Mobile View
      if ($("figure.hero-img").height()) {
        placementTarget += $("figure.hero-img").height();
      }
      // Add heights of all elements up through read these next (which is placed after eigth p tag)
      var firstModIndex = $("#drr-container > p:eq(7)").index();
      $("#drr-container").children().slice(0, firstModIndex).each(function() {
        placementTarget += $(this).height();
      });
      // Define first mobile ad here so imu counter shows imu1 first imu2 second, etc.
      var firstMobileAdHtml = getLazyLoadAdHtml();
    }

    var modules = [];
    var placementIndex = [];

    var moduleCounter = 0;
    var originalModuleUrlLength = moduleUrls.length;

    // Ordering should be [ad, module, module, ad, ad, ad, module, ad, ad, ad, module] per CSOB-445, IDGMPM-6788
    var adPositions = new Array(0,3,4,5,7,8,9);

    // Place Right-rail div containers
    $("#drr-container").children().each(function(index,value) {
      //ignore any hidden elements in the body, like the mobile-only "read this next" module
      if ($(this).is(':visible')) {
        if (debug) {
          console.log($(this));
        }
        if (cumulativeHeight >= placementTarget) {
          console.log("cumulatievHeight >= placementTarget and cumulativeHeight is " + cumulativeHeight + " and placementTarget is " + placementTarget);
          var placementDiff = 0;
          if ($.inArray(loopCounter, adPositions) != -1) {
            IDG.GPT.addExtIMU();
            if (true) {
              var adDivString = getLazyLoadAdHtml();
            } else {
              IDG.GPT.IMUCounter = IDG.GPT.IMUCounter + 1;
              var slotName = IDG.GPT.getIMUSlotName();
              IDG.GPT.defineGoogleTagSlot(slotName ,[[320,50],[300,250],[300,50]]);
              var adString = "<div id='" + slotName + "'></div><script>$('#" + slotName + "').responsiveAd({screenSize:'971 1115', scriptTags: []});if (Object.keys(IDG.GPT.companions).length > 0) {IDG.GPT.refreshAd('" + slotName + "');}<\/script>";
              var adDivString = "<div class='apart ad'>" + adString + "</div>";
            }

            placementDiff = applyInsert($(this), adDivString);
            if (debug) {
              console.log("Just placed an ad and the placementDiff is: " + placementDiff);
            }
            placementTarget = cumulativeHeight + placementDiff + interModuleHeight + adHeightBuffer;
          }
          else {
            var moduleDivString = "";
            var elementId = "drr-mod-"+moduleCounter;
            moduleDivString = "<div id=\"" + elementId + "\" />";
            modules.push(elementId);

            placementDiff = applyInsert($(this), moduleDivString);
            if (debug) {
              console.log("Just placed a module and the placementDiff is: " + placementDiff);
            }
            placementTarget = cumulativeHeight + placementDiff + interModuleHeight + moduleHeightBuffer;
            moduleCounter++;
          }
          loopCounter++;
        }
        // Avoid placing elements too soon due to non-large figures inflating the cumulative height
        if ($(this).is("figure") && !$(this).is("figure.large")) {
          cumulativeHeight += grafHeight;
        }
        else {
          cumulativeHeight += $(this).height() + grafHeight;
        }
      }
    });

    // clone Related Stories module to come in after eighth para in article body for mobile breakpoint display
    var $relatedStories = $('.related-promo-wrapper');
    if ($relatedStories.length) {
      var $relatedStoriesClone = $relatedStories.clone();
      $relatedStoriesClone.insertAfter( "#drr-container > p:eq(7)");
    }

    // For mobile only, place ad after second paragraph.
    if (firstMobileAdHtml) {
      $(firstMobileAdHtml).insertAfter("#drr-container > p:eq(1)");
    }

    var $insiderPromo = $('.insider-promo-wrapper');
    if ($insiderPromo.length) {
      var $insiderPromoClone = $insiderPromo.clone();
      $insiderPromoClone.insertAfter( "#drr-container > p:eq(1)");
    }

    //place left side element
    cumulativeHeight = 0;
    var leftPlacementTarget = tagHeight < 100 ? 100 : tagHeight;
    var leftPlacementLookaheadStart = null;
    var leftIntervalHeight = 650;
    var leftPlacementIndex = null;
    var $leftPlacementElement;

    // Only place left module if not a sponsored article and not on greenbot
    if (!true && true) {
      $("#drr-container").children().each(function(index,value) {
        if (debug) {
          console.log("leftRailProcessor iterate. index "+ index);
          console.log($(this));
        }
        //ignore any hidden elements in the body, like the mobile-only "read this next" module
        if($(this).is(':visible')) {
          if (cumulativeHeight >= leftPlacementTarget) {
            if (debug) {
              console.log("congratulations... we've passed the initial start point");
            }
            if (leftPlacementIndex == null) {
              //it's not good enough to not be a left avoid - it also shouldn't be a <p> with an immediately preceding small or medium image left avoid.
              if (!isLeftAvoid($(this)) && noPrevFigures($(this)) ) {
                leftPlacementIndex = $(this).index();
                $leftPlacementElement = $(this);
                leftPlacementLookaheadStart = cumulativeHeight;
                if (debug) {
                  console.log("is not a left avoid and no prev figures. ########## set placementIndex ("+leftPlacementIndex+") and lookaheadStart ("+leftPlacementLookaheadStart+") ##########");
                }
              } else {
                if (debug) {
                  console.log("is a left avoid or has previous figures. continue");
                }
              }
            } else {
              if (debug) {
                console.log("#### leftPlacementIndex already set to "+leftPlacementIndex+". looking ahead...");
              }
              //not null; has been set
              if ((cumulativeHeight - leftPlacementLookaheadStart) > leftIntervalHeight) {
                if (debug) {
                  console.log("###### THRESHOLD REACHED. LOOKAHEAD COMPLETE. END ###### (cumulativeHeight - leftPlacementLookaheadStart) ("+(cumulativeHeight-leftPlacementLookaheadStart)+") > leftIntervalHeight ("+leftIntervalHeight+").");
                }
                return false;
              } else {
                if (debug) {
                  console.log("threshold not reached: (cumulativeHeight - leftPlacementLookaheadStart) ("+(cumulativeHeight-leftPlacementLookaheadStart)+") < leftIntervalHeight ("+leftIntervalHeight+")");
                }
                if (isLeftAvoid($(this))) {
                  if (debug) {
                    console.log("This element is left avoid. #RESET, CONTINUE#");
                  }
                  leftPlacementIndex = null;
                  leftPlacementLookaheadStart = null;
                }
              }
            }
          }
          //we shouldn't be counting small or medium figures towards height since their heights are reflected in the following <p> tags
          if (!(isLeftAvoid($(this)) && ($(this).hasClass('small') || $(this).hasClass('inline-small') || $(this).hasClass('medium') || $(this).hasClass('inline-medium') || $(this).hasClass('apart') ))) {
            cumulativeHeight += $(this).height() + grafHeight;
          }
          if (debug) {
            console.log("-------------------- set cumulativeHeight("+cumulativeHeight+") ---------------");
            console.log("");
          }
        }
      });
    }

    if (leftPlacementIndex != null && elementNotNearEnd($leftPlacementElement, leftPixelWindow)) {
      if (debug) {
        console.log(" insert into index "+leftPlacementIndex);
      }
      $("#drr-container").children().eq(leftPlacementIndex).before("<div id=\""+leftModuleElementId+"\" />");
    }

    IDG.GPT.trackOmniture();

    // Add Right rail module content
    for (var i=0; i<modules.length; i++) {
      if (moduleUrls[i] !== undefined) {
        $.get( moduleUrls[i]+"&divId="+modules[i], function( data ) {
          var placementId = $(data).attr("data-placement-id");
           $( "#"+placementId ).html( data );
        });
      }
    }


    // Add Left rail module content, if not on a collection/package page
    if (leftPlacementIndex != null && elementNotNearEnd($leftPlacementElement, leftPixelWindow)) {
      $.get( leftModuleUrl+"&divId="+leftModuleElementId, function( data ) {
        var placementId = $(data).attr("data-placement-id");
        $( "#"+placementId ).html( data );
      });
    }




    //this needs to run once all of the modules are placed to make sure that any modules placed after page load are slated to be lazy-loaded
    $("div.lazyload_ad_article").lazyLoadAd({
          threshold    : 500,         // You can set threshold on how close to the edge ad should come before it is loaded. Default is 0 (when it is visible).
          forceLoad    : false,       // Ad is loaded even if not visible. Default is false.
          onLoad       : false,       // Callback function on call ad loading
          onComplete   : false,       // Callback function when load is loaded
          timeout      : 1500,        // Timeout ad load
          debug        : false,       // For debug use : draw colors border depends on load status
          xray         : false      // For debug use : display a complete page view with ad placements
    }) ;

    /*
    * Increments imu counter and generates a 'name' based on count like imu2, imu3, etc.
    * Returns the html and code script needed for the lazy load ad js.
    */
    function getLazyLoadAdHtml() {
      var adString = IDG.GPT.getLazyAdCode();
      return "<div class=\"apart ad lazyload_ad_article\">" + adString + "</div>";
    }

    function getEpoParams() {
      var parts = document.referrer.replace(/^https?:\/\//, '').split('/');
      var defaultCatId = 3576;
      var defaultTypeId = 6;
      var epoParams = "module.epo";

      parts.shift();

      // From HOMEPAGE; show default typeId articles
      if (parts.join('/') == "" && document.referrer.indexOf(document.domain)) {


        epoParams += "&typeId=" + defaultTypeId + "&referrer=home";

      }
      // From ARTICLE: Show articles w referrer catId
      else if (document.referrer != undefined && document.referrer.indexOf('article') >= 0) {
        var a = document.createElement('a');
        a.href = document.referrer;
        var uriParts = a.pathname.split('/');
        a = '';
        if (typeof uriParts[3] == 'undefined') {
          epoParams += "&typeId=" + defaultTypeId + "&referrer=home"; // default is 'home' behavior
        }
        else {
          var refCatSlug = uriParts[3];
          epoParams += "&catSlug=" + refCatSlug + "&referrer=article";
        }
      }
      // From SEARCH: Show article with catId same as current article
      else if (document.referrer.indexOf("google") >= 0 || document.referrer.indexOf("yahoo") >= 0 || document.referrer.indexOf("bing") >= 0) {
        var categories = [3255];
        if (categories instanceof Array && categories.length > 0) {
          var primaryCatId = categories[0];
          epoParams += "&catId=" + primaryCatId + "&referrer=search";
        }
        else {
          epoParams += "&typeId=" + defaultTypeId + "&referrer=home"; // default is 'home' behavior
        }
      }
      // Default is to show like coming from homepage
      else {


        epoParams += "&typeId=" + defaultTypeId + "&referrer=home";
         // default is 'home' behavior
      }
      return epoParams;
    }

    /**
     * @param jqo Original jquery object target
     * @param divString The div to be inserted.
     * @return Difference in height between original placement target and final target.
     * Checks first 6 elements for an allowable placement (600 pixel window).
     * If none, check nearby for elements that are not right avoids.
     * If none, place element before current target.
     */
    function applyInsert(jqo, divString) {
      if (debug) {
        console.log("applyInsert at top and jqo index is: " + jqo.index());
      }

      for (var i=0; i<=6; i++) {
        $thisElement = jqo.nextAll().andSelf().slice(i, i+1);
        if (debug) {
          console.log("Checking first six and i is: " + i + " and this element index is " + $thisElement.index() );
        }
        if ($thisElement.index() < 0) {
          break;
        }
        if (allowPlacement($thisElement)) {
          return addElement(jqo, $thisElement, divString);
        }
      }
      // If got here, no good place to put it.. just put it before one that is not right avoid!
      var $allowElement = null;
      if (($allowElement = findNearbyAllow(jqo)) != null) {
        return addElement(jqo, $allowElement, divString);
      }
      else {
        // nothing good so put in first spot that is not rightReject and not followed by reject.
        if (debug) {
          console.log("No nearby allows so just place in first spot that is not rightReject.");
        }
        var numElements = jqo.nextAll().length;
        var startIndex = jqo.index();
        for (var i=startIndex; i<=numElements; i++) {
          var $element = $("#drr-container").children().eq(i);
          // This element is eligible when not null, not in placement index, and not a rightReject nor is next element
          if ($element != null && (placementIndex == null || placementIndex.indexOf(i) == -1)) {
            if (!isRightReject($element) && !isRightReject($element.next())) {
              return addElement(jqo, $element, divString);
            }
          }
        }
        if (debug) {
          console.log("Not going to place element: return 0.");
        }
        return 0;
      }
    }

    /**
     * @param jqo Original jquery object
     * @param allowElement Element that is good placement for module/ad
     * @param divString The div to be inserted before the good element
     * @return placementHeightDiff Diff in height between original placement target and current target.
     *
     * If element is not too close to the end the insert the div before allowable element.
     * Add element index to placementIndex to keep track of which elements already have placements
     */
    function addElement(jqo, allowElement, divString) {
      var offset = allowElement.index() - jqo.index();
      if (debug) {
        console.log("addElement: jqo index is " + jqo.index() + " allowElement index is " + allowElement.index());
      }

      if (elementNotNearEnd(allowElement, rightPixelWindow)) {
        allowElement.before(divString);
        if (debug) {
          console.log("addElement: Adding " + allowElement.index() + " to placementIndex.");
        }
        placementIndex.push(allowElement.index());
        if (offset == 0) {
          return 0;
        }
        else {
          return getHeightDifference(jqo,allowElement);
        }

      }
      else {
        if (debug) {
          console.log("addElement: Near the end so do NOT add.");
        }
        return 0;
      }
    }

    function getHeightDifference(jqo,allowElement) {
      var offset = allowElement.index() - jqo.index();
      var height = 0;
      var children = null;
      if (offset > 0) {
        children = $("#drr-container").children().slice(jqo.index(), allowElement.index() );
      }
      else {
        children = $("#drr-container").children().slice(allowElement.index(), jqo.index());

      }
      if (children != null) {
        children.each(function(i) {
          if (debug) {
            console.log("About to add this element's height to heigh diff offset");
            console.log($(this));
          }
            height += $(this).height() + grafHeight;
        });
      }
      if (offset < 0) {
        height *= -1;
      }
      if (debug) {
        console.log("getHeightDifference: offset was " + offset + " and height diff is : " + height);
      }

      return height;
    }

    /**
     * Return true if next 550 pixels do not include a right avoid; false otherwise.
     */
    function allowPlacement(jqo) {
      $testElement = jqo;
      var height = 0;
      while (height < 550) {
        if ($testElement != null && !isRightAvoid($testElement)) {
          if (debug) {
            console.log("allowPlacement: this element height is " + $testElement.height() + " and index is: " + $testElement.index());
          }
          height += $testElement.height() + grafHeight;
        }
        else {
          return false;
        }
        $testElement = $testElement.next();
      }
      return true;
    }

    /**
     * Look ahead and back for element that is not rightAvoid
     * Return the index of the closest allowable element or null if none found.
     */
    function findNearbyAllow(jqo) {
      if (debug) {
        console.log("In nearby allow so could not find 600 px window.");
      }
      // Check current element first
      if (!isRightAvoid(jqo) && !isRightAvoid(jqo.next())) {
        return jqo;
      }
      for (var i=1; i<=2; i++) {
        if (!isRightAvoid(jqo.nextAll().slice(i-1, i)) && !isRightAvoid(jqo.nextAll().slice(i, i+1)) ) {
          return jqo.nextAll().slice(i-1, i);
        }
        if (!isRightAvoid(jqo.prevAll().slice(i-1, i)) && !isRightAvoid(jqo.prevAll().slice(i, i+1))  ) {
          return jqo.prevAll().slice(i, i+1);
        }
      }
      return null;
    }

    /**
    * Return true if this is avoid element or already has module/ad placed near it
    */
    function isRightAvoid(jqo) {
      if (placementIndex == null || placementIndex.indexOf(parseInt(jqo.index())) == -1) {
        if (jqo.is("pre") && jqo.height() > 300) {
          if (debug) {
            console.log("isRightAvoid: found pre. return true");
          }
          return true;
        }
        if (jqo.is("figure") && jqo.hasClass('large')) {
          if (debug) {
            console.log("isRightAvoid: found figure.large return true");
          }
          return true;
        }
        if (jqo.is("figure") && jqo.hasClass('medium') && jqo.hasClass('inline')) {
          if (debug) {
            console.log("isRightAvoid: found figure has class medium and inline.");
          }
          return true;
        }

        if (jqo.is('div') && jqo.hasClass('table-wrapper')) {
          if (debug) {
            console.log("isRightAvoid: found div with class table-wrapper");
          }
          return true;
        }
        if (jqo.is('aside')) {
          if (jqo.hasClass('sidebar') && !jqo.hasClass('medium')) {
            if (debug) {
              console.log("isRightAvoid: found aside with class sidebar, without class medium");
            }
            return true;
          }
          if (jqo.hasClass('statsTable')) {
            if (debug) {
              console.log("isRightAvoid: found aside with class statsTable");
            }
            return true;
          }
        }
        if (jqo.hasClass('download-asset')) {
          if (debug) {
            console.log("isRightAvoid: found class download-asset return true");
          }
          return true;
        }
        if (jqo.hasClass('tableLarge')) {
          if (debug) {
            console.log("isRightAvoid: found class tableLarge return true");
          }
          return true;
        }
        if (jqo.hasClass('reject')) {
          if (debug) {
            console.log("isRightAvoid: found class reject. return true");
          }
          return true;
        }
        if (jqo.is('table') && jqo.hasClass('scorecard')) {
          if (debug) {
            console.log("isRightAvoid: found div with class scorecard");
          }
          return true;
        }
        if (jqo.hasClass('product-list') || jqo.hasClass('fullwidth')) {
          if (debug) {
            console.log("isRightAvoid: found product list or fullwidth product sidebar");
          }
          return true;
        }
      }
      return false;
    }

    // Return true if element has class 'reject': will not place drr modules/ads next to these elements
    function isRightReject(jqo) {
      console.log("in isRightReject");
      if (jqo != null) {
        if (jqo.hasClass("reject")) {
          if (debug) {
            console.log("isRightReject: found 'reject' class");
          }
          return true;
        }
        return false;
      }
      return false;
    }

    // Returns true if height of all elements after this one is more than 500; false otherwise
    function elementNotNearEnd(element, pixelWindow) {
      if (pixelWindow == null) {
        pixelWindow = 500;
      }
      if (element == null) {
        return false;
      }
      var remainingHeight = 0;
      var children = $("#drr-container").children().slice(element.index());
      if (children == null) {
        return false;
      }
      children.each(function(i){
         remainingHeight += $(this).height();
      });
      if ( remainingHeight > pixelWindow) {
        return true;
      }
      else {
        if (debug) {
          console.log("Element too close to end. Remaining height is: " + remainingHeight + " and window is " + pixelWindow);
        }
        return false;
      }
    }

    /**
    * Return true if need to avoid this element when placing left module.
    */
    function isLeftAvoid(jqo) {
      if (jqo.is("figure")) {
        if (debug) {
          console.log("isLeftAvoid: found figure. return true");
        }
        return true;
      }
      if (jqo.is("aside.pullquote")) {
        if (debug) {
          console.log("isLeftAvoid: found pullquote. return true");
        }
        return true;
      }
      if (jqo.is("pre")) {
        if (debug) {
          console.log("isLeftAvoid: found pre. return true");
        }
        return true;
      }
      if (jqo.is("div.gist")) {
        if (debug) {
          console.log("isLeftAvoid: found github code block. return true");
        }
        return true;
      }

      if (jqo.is("aside") && jqo.hasClass("sidebar") && jqo.hasClass("medium")) {
        if (debug) {
          console.log("isLeftAvoid: found medium sidebar. return true");
        }
        return true;
      }

      if (jqo.hasClass("statsTable")) {
        if (debug) {
          console.log("isLeftAvoid: found class statsTable. return true");
        }
        return true;
      }

      if (jqo.hasClass("product-sidebar") && jqo.not(".fullwidth").length > 0) {
        if (debug) {
          console.log("isLeftAvoid: found class product-sidebar. return true");
        }
        return true;
      }
      return false;
    }

    /**
     * return true if there are no figures before the target placement that might bleed down into placement element
     */
    function noPrevFigures($originalTarget) {
      var targetIndex = $originalTarget.index();
      var numElementsLookBack = 5;
      var figureIndex = null;
      var figureHeight = null;
      var startIndex = targetIndex - numElementsLookBack < 0 ? 0 : targetIndex - numElementsLookBack;

      $("#drr-container").children().slice(startIndex, targetIndex).each(function(index, value) {
        if ($(this).is(':visible')) {
          if (($(this).is("figure") && !$(this).hasClass("large")) || $(this).hasClass("statsTable") || $(this).hasClass("product-sidebar")) {
            figureIndex = $(this).index();
            figureHeight = $(this).height();
            if (debug) {
              console.log("noPrevFigures: Found a figure and it's index is: " + figureIndex + " it's height is: " + figureHeight);
            }
          }
        }
      });
      if (figureIndex != null) {
        startIndex = figureIndex+1;
        var heightDiff = 0;
        $("#drr-container").children().slice(startIndex, targetIndex).each(function(index, value) {
          if ($(this).is(':visible')) {
            heightDiff += $(this).height();
          }
        });
        if ( heightDiff < figureHeight) {
          if (debug) {
            console.log("noPrevFigures: figureHeight is: " + figureHeight + " and heightDiff is " + heightDiff + " so return false.");
          }
          return false;
        }
      }
      return true;
    }

  }
</script>












<div id="drr-container" itemprop="articleBody">



























    <div class="lazyload_ad">
    <code type="text/javascript">
      <!--
      document.write('<div id="gpt-pin" class="">');
      IDG.GPT.addDisplayedAd("gpt-pin", "true");
      IDG.GPT.addLazyloadedAd("gpt-pin", "true");
      document.write('</div>');


          IDG.GPT.displayGoogleTagSlot('gpt-pin');



      if (Object.keys(IDG.GPT.companions).length > 0) {
        IDG.GPT.refreshAd('gpt-pin');
      }
      //-->
    </code>
    </div>





  <p>While it’s well known that the cloud helps organizations “do more with less,” the follow up question of “how much?” is far less known. IT rarely asks that question, yet the business does ask, and the business wants answers. If IT is going to better align themselves with business operations, they must be able to objectively quantify the value of the cloud.</p><p>Looking to the wisdom of the crowd, we reached out to experts and asked, “How do you measure the value the cloud brings to your business?” As the responses came in we discovered there’s far more than one or even 20 different ways to measure the success of the cloud. A myriad of technical and business dynamics can be a definition of success. More importantly, what may work for one organization may not be appropriate for another. As you look through the following advice, pick and choose what’s right for your organization. Note: Please don’t skip tip number one. It’s appropriate for all businesses.</p><h3>1: Define what ROI means for your business</h3><p>“You need to define what ROI means for your organization first, before you identify your KPIs [key performance indicators],” advises Jeff Frankel (<a href="http://twitter.com/docstarsoftware" rel="nofollow">@docstarsoftware</a>), executive vice president and principal at <a href="http://docstar.com/" rel="nofollow">docSTAR</a>.</p><p>“The KPIs that are used to measure the success of a cloud initiative should similarly be the ones used to measure impact on the overall success of the company itself,” adds Matt Podowitz (<a href="http://twitter.com/mpodowitz" rel="nofollow">@mpodowitz</a>), senior director at <a href="http://www.thepinehillgroup.com/" rel="nofollow">Pine Hill Group</a>.</p><p>“Before implementing the cloud projects, have business/IT alignment up front to define what the business values are,” says Terence Ngai (<a href="http://twitter.com/terencecngai" rel="nofollow">@TerenceCNgai</a>), head of cloud go-to-market and solution delivery at <a href="http://hp.com/" rel="nofollow">HP</a>.</p><p>“Company strategy and employee goals should inform which tools and technology you choose – not the other way around,” says Clara Liang (<a href="https://twitter.com/claraliang" rel="nofollow">@claraliang</a>), Chief Product Officer of <a href="http://jive.com/" rel="nofollow">Jive</a>.</p><p>Podowitz agrees: “Measuring cloud success using technology rather than business KPIs diminishes the cloud effort to nothing more than a technology project, not a technology-enabled transformation of the business capable of creating real value.”</p><p>“The only KPIs for the success of moving to the cloud are how well it achieved your reason for moving to the cloud in the first place,” concludes Nicko van Someren (<a href="http://twitter.com/good_technology" rel="nofollow">@good_technology</a>), CTO, at <a href="http://good.com/" rel="nofollow">Good Technology</a>.</p><h3>2: Obviously, Compare costs</h3><p>“The value of the cloud is around overall cost cutting,” says Michael Weiss (<a href="http://twitter.com/oildex" rel="nofollow">@Oildex</a>), VP, software engineering at <a href="http://oildex.com/" rel="nofollow">Oildex</a>.</p><p>“Compare previous expenses around on-premise datacenter management to the expenses of cloud hosting,” suggests Kevin Fishner (<a href="http://twitter.com/kfishner" rel="nofollow">@KFishner</a>), director of customer success, at <a href="https://hashicorp.com" rel="nofollow">HashiCorp</a>.</p><p>For best cost analysis, M.J. Johnson (<a href="https://twitter.com/threeeyedtoad" rel="nofollow">@threeeyedtoad</a>), director, product marketing, at <a href="http://akamai.com/" rel="nofollow">Akamai Technologies</a>, suggests measuring “reduced hardware and software costs, reduced resources required to maintain infrastructure, plus eliminated on-premise and co-located data centers including leased lines for connectivity.”</p><p>“The KPI that measures success in the cloud is more related to cost avoidance. If we add 100 users to an on premise application, what will that cost us in terms of hardware, infrastructure, licenses and help desk costs over a defined timeframe,” adds Dean Wiech (<a href="http://twitter.com/dwiech" rel="nofollow">@dwiech</a>), managing director for <a href="http://tools4ever.com/" rel="nofollow">Tools4ever</a>.</p><h3>3: Don’t just focus on cost savings</h3><p>“Many companies make the mistake of only measuring hard cost savings associated with cloud, which frequently leads to misaligned expectations during the implementation phase,” says Charles Moore (<a href="http://twitter.com/delphix" rel="nofollow">@delphix</a>), product marketing, at <a href="http://delphix.com/" rel="nofollow">Delphix</a>.</p><p>Keep that in mind as you read the rest of the advice, which addresses mostly non-cost-based ways to measure the success of your cloud initiative.</p><h3>4: Measure revenue impact</h3><p>“The closer an organization can tie a cloud project to revenue, the better,” says Eric Shapiro (<a href="http://twitter.com/arctouch" rel="nofollow">@ArcTouch</a>), CEO and co-founder of <a href="http://www.arctouch.com/" rel="nofollow">ArcTouch</a>.</p><p>“Cloud technologies allow you to more granularly align tech operations KPIs to your current revenue and business KPIs,” adds Scott Maurice (<a href="http://twitter.com/scottjmaurice" rel="nofollow">@scottjmaurice</a>), managing partner at <a href="http://availpartners.net/" rel="nofollow">vail Partners</a>. “You no longer have to take yearly budget and estimate costs; you can directly assign a technology cost metric to a specific revenue-generating application and by extension, specific revenue.”</p><h3>5: Focus on customer KPIs</h3><p>“Deployment of cloud, or any technology for that matter, should involve tight assessment of what it means for end customers of the business,” says Greg Johnsen (<a href="https://twitter.com/gregjohnsen" rel="nofollow">@gregjohnsen</a>), CMO of <a href="http://www.gtnexus.com/" rel="nofollow">GT Nexus</a>, who suggests measuring the on-time delivery of goods to customers and the number of days you’re holding inventory.</p><p>Dave Davis (<a href="http://twitter.com/DaveDavis" rel="nofollow">@DaveDavis</a>), managing director at <a href="http://redflymarketing.com/" rel="nofollow">Redfly Marketing</a>, adds “We've found that customer churn rate, average revenue per customer, free to premium up-sell rate, and what we call ‘time to slip away rate’ are what's worth focusing on from a customer perspective and a financial perspective.”</p><p>Additional customer focused KPIs “would include an increase in transaction completion, reduction in cart abandonment, and an increase of spinoff sales for the B2C world,” suggests Steve Prentice (<a href="http://twitter.com/StevenPrentice" rel="nofollow">@stevenprentice</a>), senior writer, at <a href="http://www.cloudtweaks.com/" rel="nofollow">CloudTweaks</a>.</p><h3>6: Calculate ‘time to daily use’</h3><p>“We like to focus particularly on a KPI we call ‘time to daily use’ which measures how long a cloud product takes to become indispensable to a customer. The shorter this time, the less chance a user will slip away,” says Redfly Marketing’s Davis.</p><h3>7: Survey user satisfaction</h3><p>“One of the easiest and probably most underutilized ways [to measure the impact of the cloud] is to conduct employee satisfaction surveys across line-of-business (LOB) apps,” says Todd Schwartz (<a href="http://twitter.com/GetSkyKick" rel="nofollow">@GetSkyKick</a>), co-CEO and co-founder of <a href="http://skykick.com/" rel="nofollow">SkyKick</a>.</p><p>“KPIs based on surveys not only demonstrate that a process is shorter and cheaper, but that the users are also happier with the new cloud-based set up,” says Max Dufour (<a href="http://twitter.com/maxdufour" rel="nofollow">@maxdufour</a>), partner at <a href="http://harmeda.com/" rel="nofollow">Harmeda</a>.</p><p>Schwartz’s experience confirms Dufour’s theory: “What we hear from our partners about their customers is that SaaS-based apps are easier to use, make employees happier, and provide more value to end users.”</p><h3>8: Gauge market responsiveness</h3><p>“The KPIs for the cloud are the same they should have always been for IT in the past,”says Jeff Kaplan (<a href="https://twitter.com/thinkstrategies" rel="nofollow">@thinkstrategies</a>), managing director of <a href="http://thinkstrategies.com/" rel="nofollow">THINKstrategies</a>. “They are greater agility and responsiveness to market demands, and ability to identify and pursue new market opportunities.”</p><p>Akamai’s Johnson concurs: “The benefit resulting from instant-on cloud-based capabilities can be measured in time to market, newfound agility to deploy new applications and enter new markets, and increased customer acquisition/decreased customer attrition.”</p><p>“Cloud deployed in a global supply chain should enable a manufacturer to know the true cost to serve a market and foster better decisions based on these insights,” says GT Nexus’ Johnsen.</p><p>For example, Alastair Mitchell (<a href="http://twitter.com/alimitchell" rel="nofollow">@alimitchell</a>), co-founder and president, <a href="http://huddle.com/" rel="nofollow">Huddle</a>, has “worked with pharmaceutical companies who’ve managed to dramatically reduce the time to market of new drugs through improved cloud-based collaboration during clinical trials and with universities who’ve been able to connect research teams who are dispersed across the globe.”</p><p>“Cloud also enables organizations to move fast and deploy quickly without having to involve finance or purchasing or to think about deployment factors (space, networking, setup, etc.),” adds Gerardo Dada (<a href="http://twitter.com/gerardodada" rel="nofollow">@gerardodada</a>), VP, product marketing and strategy at <a href="http://solarwinds.com/" rel="nofollow">SolarWinds</a>.</p><h3>9: Evaluate speed <em>and</em> control</h3><p>While speed was a common theme we saw across many KPIs, it had to be balanced with the ability to actually conduct business.</p><p>“Success for IT in the ‘cloud’ is a complex mix between allowing speed (pace of new applications, operate at speed of the business, new APIs, new microservices) while still providing IT control (governance around data access and quality, security, identity, uptime SLAs, etc.),” says Chris Purpura (<a href="http://twitter.com/chrispurpura" rel="nofollow">@ChrisPurpura</a>), VP of digital enterprise strategy at <a href="http://twitter.com/mulesoft" rel="nofollow">MuleSoft</a>.</p><h3>10: Determine the opportunity cost</h3><p>“A less obvious KPI for measuring cloud success is opportunity cost, or the cost of the best alternative foregone,” says Ray Bordogna (<a href="http://twitter.com/RayBordogna" rel="nofollow">@RayBordogna</a>), partner and chief strategy officer at <a href="http://liquidhub.com/" rel="nofollow">LiquidHub</a>.</p><p>For example, Steve Herrod (<a href="http://twitter.com/herrod" rel="nofollow">@herrod</a>), managing director, <a href="https://www.generalcatalyst.com/" rel="nofollow">General Catalyst Partners</a>, suggests you ask yourself, “How many employee-hours go toward the non-differentiated application and infrastructure work can be replaced by IaaS or SaaS offerings?”</p><p>In addition, the cloud affords companies to rapidly experiment. Without the cloud, the experiment opportunity is lost.</p><p>“Leveraging cloud-oriented assets can enable a business to quickly design, implement and test several choices in a timely, cost-effective manner,” continued Bordogna.</p><h3>11: Don’t overlook continuity of business operations</h3><p>The ability of the cloud to be always on and operational, without the excessive need for in-house technical staff, is a value add that should not be ignored.</p><p>“If a company suffers a server failure, security breach or problem due to simple human error, companies that deploy cloud-empowered data protection solutions are assured that their business remains operating smoothly. It is the continuance of operations KPI that showcases the success of the cloud,” explains Dave LeClair (<a href="https://twitter.com/LeClairTech" rel="nofollow">@LeClairTech</a>), VP, product marketing, at <a href="http://www.unitrends.com/" rel="nofollow">Unitrends</a>.</p><p>This continuance extends beyond not only just keeping the lights on, but also to ongoing product deployment.</p><p>As MJ DiBerardino (<a href="http://twitter.com/cloudnexa" rel="nofollow">@cloudnexa</a>), CTO of <a href="http://cloudnexa.com/" rel="nofollow">Cloudnexa</a>, notes, “When you are developing a new product, service or platform, the cloud allows you to maintain a continuous deployment model for your development operations.”</p><h3>12: How does the cloud foster innovation?</h3><p>“The biggest distraction for cloud adoption is the continuous focus on reduced costs on IT infrastructure. True value is gained from business gain agility with the new found ability to rapidly innovate,” says Larry Carvalho (<a href="http://twitter.com/robustcloud" rel="nofollow">@robustcloud</a>), research manager and lead analyst – PaaS for <a href="http://idc.com/" rel="nofollow">IDC</a>.</p><p>Jason Dover (<a href="http://twitter.com/jaysdover" rel="nofollow">@jaysdover</a>), director of product line management, <a href="http://kemptechnologies.com/" rel="nofollow">KEMP Technologies</a>, refers to this KPI as “time to innovation (TTI).”</p><p>Shortening the TTI requires improving the ease and speed of collaboration.</p><p>“Measure connected worker engagement, the resources required to maintain engagement, and retention as it relates to delivering the business goals,” suggests Eric Hanson (<a href="https://twitter.com/fuze" rel="nofollow">@fuze</a>), VP of strategic initiatives at <a href="http://fuze.com/" rel="nofollow">Fuze</a>.</p><h3>13: Can you identify and solve a specific problem?</h3><p>“The cloud allows IT professionals to focus on the actual business problem rather than focus on the intermediate technical disciplines,” says Dan Carney (<a href="https://twitter.com/llnw" rel="nofollow">@llnw</a>), VP, operations at <a href="http://limelight.com/" rel="nofollow">Limelight</a>. “This is very healthy as it drives the IT staff [who understands the technology] to engage in business discussions about how the user [who does not necessarily understand or care about the technology] perceives the service.”</p><p>In one example, Honeywell, a high-tech company, outfitted its sales staff with a low-tech solution: phonebook-sized catalogs tied together with shower curtain rings. It wasn’t a pretty sight, and it definitely didn’t instill confidence in potential buyers. With the help of ArcTouch, they solved the problem by producing an easy to access catalog application that was connected to Salesforce data.</p><p>“With the new solution, Honeywell staffers could instantly capture new business leads, share meeting outcomes, define next steps, and set reminders,” says ArcTouch’s Shapiro. “At a single two-day trade event, the app helped Honeywell capture more than $1 million in new business leads, which more than paid for the solution we developed.”</p><h3>14: Calculate mean time to solving problems</h3><p>“The big KPI for cloud infrastructure for internal development is average time between bug report and solution deployment,” says Jeffrey Bolden, managing partner at <a href="http://BlueLotusSIDC.com" rel="nofollow">Blue Lotus SIDC</a>. “Most companies that move toward a cloud culture see reductions of 30 percent to 80 percent in this key metric of IT responsiveness.”</p><p>“Time spent on a key process or cycle is another good KPI,” adds Harmeda’s Dufour.</p><p>This could be the time spent on back-office tasks such as the time handling and processing invoices, suggests GT Nexus’ Johnsen.</p><h3>15: Compute the time to develop apps</h3><p>Neal Bradbury (<a href="http://twitter.com/intronisinc" rel="nofollow">@IntronisInc</a>), co-founder and VP of channel development at <a href="http://intronis.com/" rel="nofollow">Intronis</a> recommends measuring “provisioning speed, or how quickly a business is able to set up a working application in the cloud.”</p><p>“This KPI measurement includes the time to prepare the infrastructure/VMs for dev, QA, staging, and production, which is often a major delay in enterprise shops,” adds Bernard Sanders (<a href="http://twitter.com/BernardCBolt" rel="nofollow">@BernardCBolt</a>), CTO of <a href="http://www.cloudboltsoftware.com/" rel="nofollow">CloudBolt Software</a>.</p><p>You need to also include your ability to not have to seek third party support, and instead use the development resources and tools of your choice.</p><p>“This is important because skill sets vary and most enterprises don’t have the mobile skills they need,” notes Sravish Sridhar (<a href="https://twitter.com/sravish" rel="nofollow">@sravish</a>), founder and CEO of <a href="http://kinvey.com/" rel="nofollow">Kinvey</a>.</p><p>“Sure, you need to ask yourself if you’re creating something that will enrich the experience for your customers, but more importantly, are you doing this quickly enough to realize the benefit?” asks Michael Henry (<a href="http://twitter.com/executionmgmt" rel="nofollow">@executionmgmt</a>), CIO at <a href="http://digitalrealty.com/" rel="nofollow">Digital Realty</a>.</p><h3>16: Measure velocity, not agility</h3><p>“Agility is difficult to measure, but velocity is not,” argues Ariel Tseitlin (<a href="http://twitter.com/atseitlin" rel="nofollow">@atseitlin</a>), partner at <a href="http://scalevp.com/" rel="nofollow">Scale Venture Partners</a>.</p><p>While tip #8, gauge market responsiveness, is valuable, it is still difficult to measure. Other types of speed, which many experts recommend, are far easier.</p>











</div>




    <div class="apart-alt tags">
    <span class="related">RELATED TOPICS</span>
    <ul>

      <li><a href="/category/cloud-computing">Cloud Computing</a></li>











    </ul>

  </div>































































<section class="pagination">



      <span class="page-numbers">

          <a href="/article/2929788/cloud-computing/20-ways-to-measure-the-success-of-your-growing-cloud-investment.html" class="page-link current">1</a>

          <a href="/article/2929788/cloud-computing/20-ways-to-measure-the-success-of-your-growing-cloud-investment.html?page=2" class="page-link ">2</a>

      </span>
      <span class="current-page">
        Page 1
      </span>


    <a href="/article/2929788/cloud-computing/20-ways-to-measure-the-success-of-your-growing-cloud-investment.html?page=2" class="page-link next" rel="next">Next <i class="ss-icon ss-navigateright"></i></a>

</section>
<script type="text/javascript">
$(document).ready(function(){
  $('.pagination .page-link').click(function(e){
    oClickTrack("Article Detail:Pagination");
  });
});
</script>








































    <!-- blx4 #1170 blox4.html  -->



































  <div class="article-intercept">
  <i class="ss-icon ss-lightbulb"></i> <a href="http://www.cio.com/article/3022833/cio-role/state-of-the-cio-2016-its-complicated.html">Download the State of the CIO 2016 report</a>
  </div>











    <a href="#comments" class="btn comments-cta"><i class="ss-icon ss-chat"></i>View <span id="comment-cta-txt">Comments</span></a>






<script src="/www/js/video/embedder.js?v=20160525110859"></script>


























        </section><!-- /.bodee -->





























<div id="content-recommender" class="nolinks norewrite">
  <div class="head">You Might Like</div>
  <div id="rcjsload_eab3cb"></div>
</div>

<script type="text/javascript">
(function() {
function ad_block_test(e,o){if("undefined"!=typeof document.body){var t="0.1.2-dev",o=o?o:"sponsorText",n=document.createElement("DIV");n.id=o,n.style.position="absolute",n.style.left="-999px",n.appendChild(document.createTextNode("&nbsp;")),document.body.appendChild(n),setTimeout(function(){if(n){var o=0==n.clientHeight;try{}catch(d){console&&console.log&&console.log("ad-block-test error",d)}e(o,t),document.body.removeChild(n)}},175)}}
ad_block_test(function(is_blocked){
var widget_id = 30203;
if (is_blocked === true) {
widget_id = 30279;
}
var referer="";try{if(referer=document.referrer,"undefined"==typeof referer)throw"undefined"}catch(exception){referer=document.location.href,(""==referer||"undefined"==typeof referer)&&(referer=document.URL)}referer=referer.substr(0,700);
var rcel = document.createElement("script");
rcel.id = 'rc_' + Math.floor(Math.random() * 1000);
rcel.type = 'text/javascript';
rcel.src = "http://trends.revcontent.com/serve.js.php?w="+widget_id+"&t="+rcel.id+"&c="+(new Date()).getTime()+"&width="+(window.outerWidth || document.documentElement.clientWidth) +"&referer="+referer + '&is_blocked=' + is_blocked;
rcel.async = true;
var rcds = document.getElementById("rcjsload_eab3cb"); rcds.appendChild(rcel);
});
})();
</script>



































<script src='http://cdn.gigya.com/JS/socialize.js?apiKey=3_wuZtWVv8usH9xnCrfq71nES_SNy20jW3VPrfXCBaEm7IuRNU_y027GOAxzJ_emKE' type='text/javascript'></script>


<script>
  function disableCommentBox() {
    var nodeList = document.querySelectorAll("textarea.gig-composebox-textarea");
    for (var i = 0, length = nodeList.length; i < length; i++) {
      nodeList[i].innerHTML = "Please sign in before writing your comment";
    }
    var sbutton = document.getElementById("submitButton");
    sbutton.style="display: inherit;";

  }

  function disableGlogin() {
    var nodeList = document.querySelectorAll(".gig-composebox-login");
    for (var i = 0, length = nodeList.length; i < length; i++){
      nodeList[i].style="display: none;";
    }
  }

</script>

<section id="comments" class="bodee">


  <div class="comments-hed clearfix">
    <div class="head">Join the discussion</div>
    <div class="subhead"><span class="firstToComment"><em>Be the first to comment on this article.</em> </span><a href="/about/comment-policy.html">Our Commenting Policies</a></div>
  </div>
  <div class="comments-body">
    <div id='commentsDiv'></div>
  </div>





































    <div class="lazyload_ad">
      <code type="text/javascript">
      <!--
        IDG.GPT.IMUCounter = IDG.GPT.IMUCounter + 1;
        document.write('<div id="' + IDG.GPT.getIMUSlotName() + '">');
        IDG.GPT.defineGoogleTagSlot(IDG.GPT.getIMUSlotName(), [300,250], false);
        document.write('</div>');
        $('#' + IDG.GPT.getIMUSlotName()).responsiveAd({screenSize:'971 1115', scriptTags: []});
        if (Object.keys(IDG.GPT.companions).length > 0) {
          IDG.GPT.refreshAd(IDG.GPT.getIMUSlotName());
        }
      //-->
      </code>
    </div>









</section>

<script type='text/javascript'>

function onSiteLoginHandler(event) {
    // Site Login button clicked
    NarfUser.showLogin();
}

function GigyaLogouthandle(eventObj) {
  logout();
}

function commentsLoadedHandler(e) {
  gigya.comments.getComments({
    categoryID: 'cio',
    streamID: '2929788',
    callback: function(response) {
      if (typeof response.commentCount !== 'undefined' && response.commentCount > 0) {
            $('.firstToComment').hide();
      }
    }
  });
}

function commentSubmittedHandler(eventObj) {
  var aid = eventObj.streamID;
  var brand = "cio";
  $thm.logPlEvent({"b":brand,"e":"comment","t":"article","id":aid});
}

function commentVotedHandler(e) {
  var voteType = e.vote;
  if(voteType == 'pos') $thm.logPlEvent({"b":"cio","e":"upcomment","t":"article","id":"2929788"});
  else if(voteType == 'neg') $thm.logPlEvent({"b":"cio","e":"downcomment","t":"article","id":"2929788"});
}

function gigyaErrorHandler(e) {
  console.log(">> Gigya initialization failed <<");
  console.log("errorCode:    " + e.errorCode);
  console.log("errorMessage: " + e.errorMessage);
  console.log("errorDetails: " + e.errorDetails);
  console.log("category: cio");
  console.log("gigyaKey: 3_wuZtWVv8usH9xnCrfq71nES_SNy20jW3VPrfXCBaEm7IuRNU_y027GOAxzJ_emKE");
  console.log(">> -- <<");
}
  gigya.socialize.addEventHandlers({
    onLogout:GigyaLogouthandle
  });

  var params = {
    categoryID: 'cio',
    streamID: '2929788',
    containerID: 'commentsDiv',
    useSiteLogin: true,
    onSiteLoginClicked: onSiteLoginHandler,
    cid:'',
    width: '100%',
    enabledShareProviders: 'facebook,twitter,yahoo,linkedin',
    showLoginBar: true,
    version:2,
    onLoad:commentsLoadedHandler,
    onCommentSubmitted:commentSubmittedHandler,
    onCommentVoted:commentVotedHandler,
    onError: gigyaErrorHandler
  }

 gigya.comments.showCommentsUI(params);

</script>
























<section id="funnel">
    <section class="popular-brand-cols">













































































<section class="popular-col">































































































































































    <div class="promo with-eyebrow with-image">
        <div class="eyebrow">Popular On CIO.com</div>

          <div class="sized-img"><a  href="/article/3029225/mobile-apps/3-reasons-to-buy-fitbits-new-alta-band-and-2-reasons-not-to.html"><img  class="lazy carousel.idgeImage  imgId********* " data-original="http://core4.staticworld.net/images/article/2016/02/fibit-alta-*********-carousel.idge.jpeg" alt="fibit alta" itemprop="image" /></a></div>





      <div class="hed featured"><a  href="/article/3029225/mobile-apps/3-reasons-to-buy-fitbits-new-alta-band-and-2-reasons-not-to.html">3 reasons to buy Fitbit&#039;s new Alta band, and 2 reasons not to</a></div>

        <p>Fitbit Alta is an updated version of the company's popular Flex wristband with some valuable new...</p>

















































































    <ul>
      <li class="clearfix">

            <a  href="/article/3023349/linux/best-linux-distros-of-2016-something-for-everyone.html"><img  class="lazy carousel.idgeImage  imgId********* " data-original="http://core4.staticworld.net/images/article/2016/01/linux-slide-*********-carousel.idge.jpg" alt="linux slide" itemprop="image" /></a>



<a class="cn" href="/contributor-network/signup.html">
<span class="contributor-nw">
  <i class="ss-icon ss-connection"></i> <span class="company">IDG</span> Contributor Network
</span>
</a>





          <div class="hed"><a  href="/article/3023349/linux/best-linux-distros-of-2016-something-for-everyone.html">Best Linux distros of 2016: Something for everyone</a></div>
      </li>
















































































      <li class="clearfix">

            <a  href="/article/3016790/mobile-apps/battle-of-the-fitness-bands-garmin-vivosmart-hr-vs-fitbit-charge-hr.html"><img  class="lazy carousel.idgeImage  imgId********* " data-original="http://core4.staticworld.net/images/article/2015/12/garmin-vivosmart-hr-fitbit-charge-hr-*********-carousel.idge.jpg" alt="garmin vivosmart hr fitbit charge hr" itemprop="image" /></a>





          <div class="hed"><a  href="/article/3016790/mobile-apps/battle-of-the-fitness-bands-garmin-vivosmart-hr-vs-fitbit-charge-hr.html">Battle of the (fitness) bands: Garmin vivosmart HR vs. Fitbit Charge HR</a></div>
      </li>
    </ul>
    </div><!-- /.promo -->
    <div class="promo newsletter with-eyebrow">






























    <!-- blx4 #787 blox4.html  -->









































        <div class="hed four-dot">Newsletters</div> <div class="desc">Sign up and receive the latest news, reviews and trends on your favorite technology topics.</div> <p>Get our Daily News newsletter</p> <form action="/newsletters/signup.html"> <input type="email" name="email" placeholder="Enter your email address"> <input type="hidden" name="list" value="cio_insider" id="cio_insider" /><button class="btn" type="submit">Go</button> </form>








    </div><!--  ./promo newsletter -->
















































































    <div class="promo with-image">

          <div class="sized-img"><a  href="/article/3043511/health/fitbit-blaze-smart-fitness-watch-hands-on-review.html"><img  class="lazy carousel.idgeImage  imgId100636783 " data-original="http://core5.staticworld.net/images/article/2016/01/fitbit-blaze-smartwatches-smartwatch-ces-2016-100636783-carousel.idge.jpg" alt="fitbit blaze smartwatches smartwatch ces 2016" itemprop="image" /></a></div>





      <div class="hed"><a  href="/article/3043511/health/fitbit-blaze-smart-fitness-watch-hands-on-review.html">4 things you won&#039;t like about Fitbit Blaze — and 3 you will</a></div>

         <p>Fitbit's latest fitness tracker doesn't have any unique new features that set it apart, and as such,...</p>

    </div>















































































    <div class="promo with-image">

          <div class="sized-img"><a  href="/article/3019677/mobile-apps/3-ways-fitbit-missed-the-mark-with-its-new-blaze-fitness-watch.html"><img  class="lazy carousel.idgeImage  imgId100636783 " data-original="http://core5.staticworld.net/images/article/2016/01/fitbit-blaze-smartwatches-smartwatch-ces-2016-100636783-carousel.idge.jpg" alt="fitbit blaze smartwatches smartwatch ces 2016" itemprop="image" /></a></div>





      <div class="hed"><a  href="/article/3019677/mobile-apps/3-ways-fitbit-missed-the-mark-with-its-new-blaze-fitness-watch.html">3 ways Fitbit missed the mark with its new Blaze fitness watch (updated)</a></div>

         <p>Fitbit's latest fitness band, Blaze, has a color screen and attractive, interchangeable bands and...</p>

    </div>
















</section>


        <section class="brand-col">































































































































  <div class="promo brandposts with-eyebrow" >
  <div class="eyebrow-wrapper"><div class="eyebrow">BrandPosts</div><a href="#" class="learn-more">Learn more</a></div>
  <ul class="sponsored">




























<li class="clearfix  with-image" >

  <div class="wrap">
    <a  href="/article/3072650/cloud-computing/cloudwashing-what-it-is-and-how-to-avoid-it.html"><img  class="lazy carousel.idgeImage  imgId100662056 " data-original="http://core1.staticworld.net/images/article/2016/05/business-team-meeting-discussion-working-concept-000091202093_medium-100662056-carousel.idge.jpg" alt="business team meeting discussion working concept 000091202093 medium" itemprop="image" /></a>
  </div>


















<div class="blog-branding-text"><span class="sponsored-by">Sponsored by</span> PLEX</div>

  <div class="title"><a  href="/article/3072650/cloud-computing/cloudwashing-what-it-is-and-how-to-avoid-it.html">Cloudwashing: What it is and How to avoid it</a></div>
</li>































<li class="clearfix " >


















<div class="blog-branding-text"><span class="sponsored-by">Sponsored by</span> PC Connection</div>

  <div class="title"><a  href="/article/3066781/cloud-computing/the-state-of-the-cloud.html">The State of the Cloud</a></div>
</li>































<li class="clearfix  with-image" >

  <div class="wrap">
    <a  href="/article/3063124/innovation/keys-to-innovative-disruption-digital-vision-tech-roadmap.html"><img  class="lazy carousel.idgeImage  imgId100658596 " data-original="http://core0.staticworld.net/images/article/2016/04/istock_000046190638_large-100658596-carousel.idge.jpg" alt="istock 000046190638 large" itemprop="image" /></a>
  </div>


















<div class="blog-branding-text"><span class="sponsored-by">Sponsored by</span> KPMG</div>

  <div class="title"><a  href="/article/3063124/innovation/keys-to-innovative-disruption-digital-vision-tech-roadmap.html">Keys to Innovative Disruption? Digital Vision, Tech Roadmap</a></div>
</li>































<li class="clearfix  with-image" >

  <div class="wrap">
    <a  href="/article/3041046/cloud-computing/disaster-recovery-rules-to-live-by.html"><img  class="lazy carousel.idgeImage  imgId100648673 " data-original="http://core1.staticworld.net/images/article/2016/03/ca5d1d8765e649f58b073e633f705026-100648673-carousel.idge.jpg" alt="ca5d1d8765e649f58b073e633f705026" itemprop="image" /></a>
  </div>


















<div class="blog-branding-text"><span class="sponsored-by">Sponsored by</span> Acronis</div>

  <div class="title"><a  href="/article/3041046/cloud-computing/disaster-recovery-rules-to-live-by.html">Disaster Recovery: Rules to Live By</a></div>
</li>




  </ul>
  </div>





























<div class="ad">



    <div class="lazyload_ad">
      <code type="text/javascript">
      <!--
        IDG.GPT.IMUCounter = IDG.GPT.IMUCounter + 1;
        document.write('<div id="' + IDG.GPT.getIMUSlotName() + '">');
        IDG.GPT.defineGoogleTagSlot(IDG.GPT.getIMUSlotName(), IDG.GPT.slots["topimu"], false);
        document.write('</div>');
        $('#' + IDG.GPT.getIMUSlotName()).responsiveAd({screenSize:'971 1115', scriptTags: []});
        if (Object.keys(IDG.GPT.companions).length > 0) {
          IDG.GPT.refreshAd(IDG.GPT.getIMUSlotName());
        }
      //-->
      </code>
    </div>






</div>







        </section>
    </section>

    <section class="featured-col">
































    <!-- blx4 #777 blox4.link_list  -->















































<div class="promo with-eyebrow with-image">
  <div class="eyebrow">Featured Stories</div>

    <div class="sized-img"><a  href="/article/3075442/government/state-department-argues-against-cyber-arms-treaty.html"><img  class="lazy medium.idgeImage  imgId100645764 " data-original="http://core4.staticworld.net/images/article/2016/02/cybersecurity_denial-100645764-medium.idge.jpg" alt="cybersecurity denial" itemprop="image" /></a></div>


  <div class="hed featured"><a  href="/article/3075442/government/state-department-argues-against-cyber-arms-treaty.html">State Department argues against cyber treaty</a></div>
  <p>Senior State Department official says cyber is fundamentally different than any sort of conventional...</p>
</div>





















































































































































  <div class="promo with-image">

    <div class="sized-img">
        <a  href="/article/3075423/it-strategy/it-wants-but-struggles-to-operationalize-big-data.html"><img  class="lazy carousel.idgeImage  imgId100638626 " data-original="http://core4.staticworld.net/images/article/2016/01/data_analytics_trends-100638626-carousel.idge.jpg" alt="data analytics trends" itemprop="image" /></a>
      </div>



    <div class="hed">
      <a  href="/article/3075423/it-strategy/it-wants-but-struggles-to-operationalize-big-data.html">IT still struggles to operationalize big data </a>
    </div>
    <p>Big data leaders at large companies are bullish on the capabilities of big data analytics, but are...</p>
  </div>






















































  <div class="promo with-image">

    <div class="sized-img">
        <a  href="/article/3075500/big-data/cio-quick-takes-how-to-make-big-data-count.html"><img  class="lazy carousel.idgeImage  imgId100663176 " data-original="http://core3.staticworld.net/images/article/2016/05/cio-quick-big-data-100663176-carousel.idge.jpg" alt="cio quick big data" itemprop="image" /></a>
      </div>



    <div class="hed">
      <a  href="/article/3075500/big-data/cio-quick-takes-how-to-make-big-data-count.html">CIO Quick Takes: How to make big data count</a>
    </div>
    <p>Sure ... data is big these days, but what are companies doing with all that newly-mined information? To...</p>
  </div>






















































  <div class="promo with-image">

    <div class="sized-img">
        <a  href="/article/3075498/health/setting-up-a-workstation-for-a-healthier-you.html"><img  class="lazy carousel.idgeImage  imgId100663156 " data-original="http://core2.staticworld.net/images/article/2016/05/ergotronprimary-100663156-carousel.idge.jpg" alt="ergotronprimary" itemprop="image" /></a>
      </div>



    <div class="hed">
      <a  href="/article/3075498/health/setting-up-a-workstation-for-a-healthier-you.html">Setting up a workstation for a healthier you</a>
    </div>
    <p>There are plenty of gadgets you can buy to make spending every day at your desk or in a cube. But how...</p>
  </div>








    </section>
</section>




























    <div class="lazyload_ad">
    <code type="text/javascript">
      <!--
      document.write('<div id="ciu" class="">');
      IDG.GPT.addDisplayedAd("ciu", "true");
      IDG.GPT.addLazyloadedAd("ciu", "true");
      document.write('</div>');



          $('#ciu').responsiveAd({screenSize:'971 1115', scriptTags: []});


      if (Object.keys(IDG.GPT.companions).length > 0) {
        IDG.GPT.refreshAd('ciu');
      }
      //-->
    </code>
    </div>









    <section id="resources-sponsored-links">


  <section class="sponsored-links">
    <div class="head">Sponsored Links</div>









































































  <ul class="first">































































<li>
  <a rel="nofollow" target="_blank" href="http://pubads.g.doubleclick.net/gampad/clk?id=772722416&iu=/8456/IDG.US_E_CIO.com" onclick="moduleTrack('Sponsored Links','resources-sponsored-links component')">
    Are you ready for the next evolution in enterprise IT? Learn more
  </a>
</li>




  </ul>


  </section>
</section>




        </article>

      </section><!-- /role=main -->

    </div><!-- /#page-wrapper -->













<footer>
    <section class="brand" itemscope itemtype="http://schema.org/Organization">

      <link itemprop="url" href="http://www.cio.com">
        <a href="/"><span class="logo">CIO</span></a>
    <span class="tagline"> </span>

        <span class="follow">
      <label>Follow us</label>
              <ul>















  <li><a href="https://twitter.com/CIOonline" itemprop="sameAs" rel="nofollow" target="_blank" onclick="brandFollowTrack('Twitter')"><i class="ss-icon ss-social-circle ss-twitter"></i></a></li>



  <li><a href="https://www.linkedin.com/company/cio-online" itemprop="sameAs" rel="nofollow" target="_blank" onclick="brandFollowTrack('LinkedIn')"><i class="ss-icon ss-social-circle brand ss-linkedin"></i></a></li>



  <li><a href="https://www.facebook.com/CIOfacebook" itemprop="sameAs" rel="nofollow" target="_blank" onclick="brandFollowTrack('Facebook')"><i class="ss-icon ss-social-circle brand ss-facebook"></i></a></li>



  <li><a href="https://plus.google.com/+CIOOnline" itemprop="sameAs" rel="publisher" target="_blank" onclick="brandFollowTrack('Google+')"><i class="ss-icon ss-social-circle brand ss-googleplus"></i></a></li>



  <li><a href="https://www.youtube.com/user/CIOtv" itemprop="sameAs" rel="nofollow" target="_blank" onclick="brandFollowTrack('YouTube')"><i class="ss-icon ss-social-circle brand ss-youtube"></i></a></li>





  <li><a href="/about/rss/" target="_blank"><i class="ss-icon ss-social-circle ss-rss"></i></a></li>



              </ul>
        </span>

    </section>

    <section class="topics">




        <nav id="ft2">
            <ul>












<li><a href="/feature">Feature</a></li>
<li><a href="/news">News</a></li>
<li><a href="/news-analysis">News Analysis</a></li>
<li><a href="/opinion">Opinion</a></li>
<li><a href="/slideshows">Slideshows</a></li>
<li><a href="/video">Videos</a></li>

            </ul>
        </nav>



        <nav id="ft2a">
                <ul>












<li><a href="/newsletters/signup.html">Sign up for Newsletters</a></li>
<li><a href="/insider">Sign up for Insider</a></li>

                </ul>
            </nav>


    </section>


    <section class="about">
    <div class="wrapper">
          <nav class="tertiary" id="ft3">
              <ul>

















<li><a href="/about/about.html"  >About CIO</a>

<li><a href="/about/adchoices.html"  >Ad Choices</a>

<li><a href="http://www.idgenterprise.com/reach/cio/" target="_blank" rel="nofollow" >Advertising</a>

<li><a href="http://careers.idg.com/" target="_blank" rel="nofollow" >Careers at IDG</a>

<li><a href="/about/contactus.html"  >Contact Us</a>

<li><a href="/about/privacy.html"  >Privacy Policy</a>

<li><a href="/about/tos.html"  >Terms of Service</a>

<li><a href="/about/sitemap.html"  >Site Map</a>

<li><a href="/about/subscription-services.html"  >Subscription Services</a>




              </ul>
          </nav>
    </div>
  </section>

    <section class="copyright">
        <div class="wrapper">

          <p>Copyright &copy; 1994 - 2016 CXO Media Inc. a subsidiary of IDG Enterprise</p>
      <div class="network">
        <div id="network-selector">
                <div class="label">Explore the IDG Network <i class="ss-icon tick">descend</i></div>
                <ul>



















    <li><a href="http://www.cio.com" target="_blank" rel="nofollow">CIO</a></li>

    <li><a href="http://www.computerworld.com" target="_blank" rel="nofollow">Computerworld</a></li>

    <li><a href="http://www.csoonline.com" target="_blank" rel="nofollow">CSO</a></li>

    <li><a href="http://www.greenbot.com" target="_blank" rel="nofollow">Greenbot</a></li>

    <li><a href="http://www.idc.com" target="_blank" rel="nofollow">IDC</a></li>

    <li><a href="http://www.idg.com" target="_blank" rel="nofollow">IDG</a></li>

    <li><a href="http://www.idganswers.com" target="_blank" rel="nofollow">IDG Answers</a></li>

    <li><a href="http://www.idgconnect.com" target="_blank" rel="nofollow">IDG Connect</a></li>

    <li><a href="http://www.idgknowledgehub.com" target="_blank" rel="nofollow">IDG Knowledge Hub</a></li>

    <li><a href="http://www.idgtechnetwork.com" target="_blank" rel="nofollow">IDG TechNetwork</a></li>

    <li><a href="http://www.idg.tv" target="_blank" rel="nofollow">IDG.TV</a></li>

    <li><a href="http://www.idgventures.com" target="_blank" rel="nofollow">IDG Ventures</a></li>

    <li><a href="http://www.infoworld.com" target="_blank" rel="nofollow">Infoworld</a></li>

    <li><a href="http://www.itnews.com" target="_blank" rel="nofollow">IT News</a></li>

    <li><a href="http://www.itwhitepapers.com" target="_blank" rel="nofollow">ITwhitepapers</a></li>

    <li><a href="http://www.itworld.com" target="_blank" rel="nofollow">ITworld</a></li>

    <li><a href="http://www.javaworld.com" target="_blank" rel="nofollow">JavaWorld</a></li>

    <li><a href="http://www.linuxworld.com" target="_blank" rel="nofollow">LinuxWorld</a></li>

    <li><a href="http://www.macworld.com" target="_blank" rel="nofollow">Macworld</a></li>

    <li><a href="http://www.networkworld.com" target="_blank" rel="nofollow">Network World</a></li>

    <li><a href="http://www.pcworld.com" target="_blank" rel="nofollow">PC World</a></li>

    <li><a href="http://www.techhive.com" target="_blank" rel="nofollow">TechHive</a></li>


                </ul>
        </div><!-- /#network-selector -->
      </div><!-- /.network -->
    </div><!-- /.wrapper -->
    </section>
</footer>

<script src="/www/js/jquery/jquery-ui.js?v=20160525110859"></script>
<script src="/www/js/jquery/jquery.dfp.min.js?v=20160525110859"></script>

<script src="/www.idge/js/FTScroller_plugins.js?v=20160525110859"></script>
<script src="/www.idge/js/mule/shortstack_nav.js?v=20160525110859"></script>
<script src="/www.idge/js/jquery/jt-scrollable.min.js?v=20160525110859"></script>
<script src="/www/js/jquery/jquery.timeago.js?v=20160525110859"></script>
<script>
$(document).ready(function(){
  $(".timeago").timeago();
});
</script>






























        <!-- Begin welcome ad overlay - gpt-overlay position  -->
        <div id="superadunit" class="hidden">
          <div class="ad">
            <a href="javascript:unhide('superadunit');" id="superstitial-text">This ad will close in 20 seconds. Continue to site &raquo;</a>
            <div id="gpt-overlay" class="">
              <script type="text/javascript">
                IDG.GPT.addDisplayedAd("gpt-overlay", "true");
                IDG.GPT.displayGoogleTagSlot('gpt-overlay');
              </script>
            </div>
          </div>
        </div>
        <!--  End welcome ad overlay - gpt-overlay position -->






































        <div id="catfish" class="">
        </div>
        <script type="text/javascript">
          IDG.GPT.addDisplayedAd("catfish", "true");
          $('#catfish').responsiveAd({screenSize:'971 1115', scriptTags: []});
        </script>































        <!--  Begin gpt-skin/gpt-pin/inread -->
        <div id="gpt-skin" class="">
        </div>
        <script type="text/javascript">
          IDG.GPT.addDisplayedAd("gpt-skin", "true");
          IDG.GPT.displayGoogleTagSlot('gpt-skin');
        </script>
        <!--  End gpt-skin/gpt-pin/inread -->









































        <div id="mobilewelcomead" class="">
        </div>
        <script type="text/javascript">
          IDG.GPT.addDisplayedAd("mobilewelcomead", "true");
          $('#mobilewelcomead').responsiveAd({screenSize:'971 1115', scriptTags: []});
        </script>







<script src="/www/js/analytics/tracking.js?v=20160525110859"></script>
<script src="/www/js/autocomplete.js?v=20160525110859"></script>

<script type="text/javascript">
$(document).ready(function(){
  var listItems = $('#scrollable ul li');
  var listWidth = 0;
  for (var i=0;i < listItems.length; i++) {
    listWidth = listWidth + $(listItems[i]).outerWidth();
  }

  $('#scrollable ul').width(listWidth+100); //adding an extra 100px of buffer


  //we need to disable this for IE10 for now - link clicks don't work!
  var trackPointerEvents = window.navigator.msPointerEnabled;

  if (!trackPointerEvents) {
    //set up the FT Scroller for the topics
    var scroller = new FTScroller(document.getElementById('scrollable'), {
      scrollingY: false,
      bouncing: false,
      updateOnWindowResize: true,
      scrollbars: false
    });
  }

});
</script>


  <script src="/www.idge/js/social_sidecar.js?v=20160525110859"></script>
  <script src="/www/js/carousel-extend.js?v=20160525110859"></script>
  <script src="/www.idge/js/jquery/plugins/jquery.colorbox-min.js?v=20160525110859"></script>
  <script src="/www.idge/js/article.js?v=20160525110859"></script>
  <script src="/www/js/prettify.js?v=20160525110859"></script>
  <script src="/www.idge/js/jquery/responsive-tables.js?v=20160525110859"></script>
  <script src="/www.idge/js/jquery/jquery.tablesorter.min.js?v=20160525110859"></script>
  <script>
  $(document).ready(function() {
    $("table.tablesorter").tablesorter({
      widgets: ['zebra']
    });

    $("table.tablesorter tbody tr").hover(function() {
      $(this).toggleClass("selected");
    });

    $("table.tablesorter thead tr th").each(function(){
      if ($(this).find('.ss-icon').length < 1) {
        $(this).append('<i class="ss-icon"></i>');
      }
    });

  });
  </script>


<script src="/www/js/jquery/jquery.lazyload.min.js?v=20160525110859"></script>

<script src="/www.idge/js/global.js?v=************"></script>

<script src="/www/js/webfonts/ss-social.js?v=20160525110859"></script>
<script src="/www/js/webfonts/ss-standard.js?v=20160525110859"></script>
<script src="/www/js/analytics/brandAnalytics.js"></script>



































<script type='text/javascript'>var _sf_startpt=(new Date()).getTime()</script>

<script type="text/javascript">
  var _sf_async_config={};

  _sf_async_config.uid = 29363;

  _sf_async_config.path = "/article/2929788/cloud-computing/20-ways-to-measure-the-success-of-your-growing-cloud-investment.html";
  _sf_async_config.title = "20 Ways to Measure the Success of Your Growing Cloud Investment | CIO";
  _sf_async_config.domain = "cio.com";
  if(window.location.href.indexOf("video")&&true) {
    _sf_async_config.playerdomain= _sf_async_config.domain.replace("www.","");
  }

  _sf_async_config.useCanonical = true;



      _sf_async_config.sections = "cloud-computing";
      _sf_async_config.authors="David Spark";



  (function(){
    function loadChartbeat() {
      window._sf_endpt=(new Date()).getTime();
      var e = document.createElement("script");
      e.setAttribute("language", "javascript");
      e.setAttribute("type", "text/javascript");
      e.setAttribute("src", (("https:" == document.location.protocol) ?
        "https://a248.e.akamai.net/chartbeat.download.akamai.com/102508/" : "http://static.chartbeat.com/") +
        ((window.location.href.indexOf("video")) ? "js/chartbeat_video.js" : "js/chartbeat.js"));
      document.body.appendChild(e);
    }
    var oldonload = window.onload;
    window.onload = (typeof window.onload != "function") ?
   loadChartbeat : function() { oldonload(); loadChartbeat(); };
  })();
</script>












<!-- Begin BlueKai Tag -->
<iframe name="__bkframe" height="0" width="0" frameborder="0" src="javascript:void(0)"></iframe>
<script type="text/javascript" src="http://tags.bkrtx.com/js/bk-coretag.js"></script>
<script type="text/javascript" src="/www/js/jquery/jquery.cookie.js"></script>
<!-- CryptoJS -->
<script src="http://crypto-js.googlecode.com/svn/tags/3.1.2/build/rollups/md5.js"></script>
<script type="text/javascript" src="/www/js/analytics/idg_bk_coreapi.js?v=20160525110859"></script>

<script type="text/javascript">
var blueKaiId=14337;
var blueKaiPort=10;
var brandCode = "cio";
var regUrl="http://regdev.idge.int/api/"
var daysToRefresh=30;
var bk_debug_log=false;



   regUrl="http://reg.idgenterprise.com/api/";



  daysToRefresh=30;







if ( typeof(s) != 'undefined' ) {
  bk_addPageCtx("pageType", (typeof(s.pageType)!='undefined')?s.pageType:'');
}


//CMS-Specific
bk_addPageCtx("cmscatids", "[3255]");


bk_addPageCtx("cmpy", "[]");


bk_addPageCtx('tid', '2');




// Add the list name and email address hash params to bluekai tracking
// If the user came from a newsletter clickthru, also set the isnl pagectx
params = getUrlParams('phint');
if(Object.keys(params).length > 0)
  {
  bk_addPageCtx("idg_eid", params['idg_eid']);
  bk_addPageCtx("newt", params['newt']);
  bk_addPageCtx("isnl", "1");
  }

  try{
    IDG.bkCoreApi.start();
  }
  catch(e){
    //alert("Triggering catch")
    bk_doJSTag(blueKaiId ,blueKaiPort);
    if (window.console && window.console.log){
      window.console.log("Error Occured While Processing Profile Siubmission")
    }
  }

</script>



<!-- End BlueKai Tag -->













<!-- START Nielsen Online SiteCensus? V6.0 -->
<!-- COPYRIGHT 2010 Nielsen Online -->
<script type="text/javascript">
    (function () {
        var d = new Image(1, 1);
        d.onerror = d.onload = function () {
            d.onerror = d.onload = null;
        };
        d.src = ["http://secure-us.imrworldwide.com/cgi-bin/m?ci=us-203426h&cg=0&cc=1&si=", escape(window.location.href), "&rp=", escape(document.referrer), "&ts=compact&rnd=", (new Date()).getTime()].join();
    })();
</script>


<!-- END Nielsen Online SiteCensus? V6.0 -->

<script type="text/javascript" src="/www/js/ads/jquery.lazyload-ad.js?v=20160525110859"></script>

<script type="text/javascript">
// -- Load Lazy Advertisement placement as deferred
$("div.lazyload_ad").lazyLoadAd({
        threshold    : 500,         // You can set threshold on how close to the edge ad should come before it is loaded. Default is 0 (when it is visible).
        forceLoad    : false,       // Ad is loaded even if not visible. Default is false.
        onLoad       : false,       // Callback function on call ad loading
        onComplete   : false,       // Callback function when load is loaded
        timeout      : 1500,        // Timeout ad load
        debug        : false,       // For debug use : draw colors border depends on load status
        xray         : false        // For debug use : display a complete page view with ad placements
}) ;
</script>



<script type='text/javascript'>
g_arrModules[''] = true;
g_arrModules['Article Detail:Top:Breadcrumb'] = true;
g_arrModules['Article Detail:Social Sharing Vertical'] = true;
g_arrModules['Article Detail:Pagination'] = true;
g_arrModules[''] = true;

$(window).load(function() {
  var commentsHeight = $(".gig-comments-comments").height();
  var commentNumber = $(".gig-comments-count").html();



  if (commentsHeight < 400) {
    $('.gig-comments-comments').height('auto').addClass('open');
      $(".gig-comments-more").css("display","none");
  }

  if (commentsHeight > 400) {
    $(".gig-comments-more").css("display","inline-block");
    $(".gig-comments-more").html("View All " + commentNumber);
    $('.gig-comments-comments').css("height","400px");

    $(".gig-comments-more").click(function() {
        $('.gig-comments-comments').height('auto').addClass('open');
        $(".gig-comments-more").css("display","none");
    });
  }

  $(".gig-comment-replyLink").click(function() {
      $('.gig-comments-comments').height('auto').addClass('open');
      $(".gig-comments-more").css("display","none");
  });
  $(".gig-composebox-textarea").click(function() {
    $('.gig-comments-comments').height('auto').addClass('open');
      $(".gig-comments-more").css("display","none");
  });

  //behavior for comments call-to-action - added per IDGMPM-8760
  if (commentNumber != "0 Comments") {
    $("#comment-cta-txt").text(commentNumber);
  }
  var commentCount = parseInt(commentNumber);
  if (commentCount>0) {
    $(".comment-count-bubble").text(commentCount);
  }
  else {
    $(".comment-count-bubble").text(""); //no number if zero per IDGMPM-9441 comments
  }
  if (commentCount==1) {
    $(".comment-text-bubble").text("Comment");
  }
  else {
    $(".comment-text-bubble").text("Comments");
  }

  var reposition = 0;
  if ($("body").hasClass("insider-plus")) reposition = 50; //for fixed header on IDGE Insider sites
  $(".comments-cta, .comments-bubble-cta").click(function(e) {
    e.preventDefault();
    $(".gig-comments-more").click(); //emulate clicking Gigya comments-more button
    $('html,body').animate({
        scrollTop: $("#comments").offset().top - reposition
    }, 600);
  });
});

g_arrModules['Article Detail:Funnel:Popular Articles'] = true;
g_arrModules[''] = true;
g_arrModules['Article Detail:Funnel:Sponsored Blog Posts'] = true;
g_arrModules['Article Detail:Funnel:Homepage Top Story'] = true;
g_arrModules['Article Detail:Funnel:Funnel Crawl'] = true;

    $('#resources-sponsored-links').addClass('show');

</script>















<script language="JavaScript" type="text/javascript">
  var omniture_account="cxociocom";

  var omniture_trackingServer = "idgenterprise.d1.sc.omtrdc.net";

  g_sOmnitureReportSuite = "cxociocom";


$(document).ready(function(){

  if(!!$('.sharing-tools').length || !!$('#ss-share').length || (!!$('#sharer').length && !!!$('#sharer.sidecar').length)){
    Socialite.load();
    Socialite.setup({
      twitter: {
        lang      : 'en',
            onclick   : function(e) {socialTrack("Article","Twitter","Click");},
            ontweet   : function(e) {
              socialTrack("Article","Twitter","Tweet");
              $thm.logPlEvent({"b":"cio","e":"twshare","t":"article","id":"2929788"});
          },
            onretweet : function(e) {socialTrack("Article","Twitter","Retweet");},
            onfavorite: function(e) {socialTrack("Article","Twitter","Favorite");},
            onfollow  : function(e) {socialTrack("Article","Twitter","Follow");followTrack();}
        },
        facebook: {
          lang  : 'en_US',
          appId : '*************68',
          onlike  : function(url){
            socialTrack("Article","Facebook","Like");
            $thm.logPlEvent({"b":"cio","e":"fblike","t":"article","id":"2929788"});
          },
          onunlike: function(url){socialTrack("Article","Facebook","Unlike");},
          xfbml : true,
          version :  'v2.1'
        },
        googleplus: {
            lang  : 'en-US',
            callback: function(el, e) {
              socialTrack("Article","Googleplus","Click");
              $thm.logPlEvent({"b":"cio","e":"googplus","t":"article","id":"2929788"});
            }
        }
    });

    //$('.sharing-tools').show();
    $('#ss-share').show();

  }

  $('a.pinterest-pinit').click(function(e){
    socialTrack('Article','Pinterest','share');
  });
  $('li.redditShare').click(function(e) {
    socialTrack('Article','Reddit','share');
  });

  $('li.stumbleUponShare').click(function(e) {
    socialTrack('Article','StumbleUpon','share');
  });

  $('.banner-tw').click(function(e){
    brandFollowTrack("twitter");

  });
  $('.banner-fb').click(function(e){
    brandFollowTrack("facebook");
  });
  $('.mediaresource #download-asset').click(function(e){
    Analytics.logAssetDownload($(this).data('id'));
  });

  if (typeof(FB)!='undefined') {
    FB.Event.subscribe('edge.create', function(response){
      s = s_gi(g_sOmnitureReportSuite);
      s.linkTrackEvents = s.events = "event36";
      s.prop48="facebook share";
      s.linkTrackVars = "prop48,events";
      s.tl(true, 'o', 'Social Media share');
    });
  }

  if (typeof(digMag) != 'undefined'){
    if(typeof(digLogin) != 'undefined'){
      DigAnalytics.logSignIn(digLogin);
    }
  }


});

var Analytics = new Object();
Analytics.logArticleComment = function(){
  s = s_gi(g_sOmnitureReportSuite);
  s.linkTrackVars="prop25,eVar25,events";
  s.linkTrackEvents="event19";
  s.events="event19";
  s.prop25 = 'Article Comment';
  s.tl(this,'o','Article Comments');
};
Analytics.logSocialSignin = function(provider){
  s = s_gi(g_sOmnitureReportSuite);

  s.linkTrackEvents = s.events = "event32";
  s.prop19=provider;
  s.linkTrackVars = "prop19,events";
  s.tl(true, 'o', provider);
};
Analytics.logInternalSignin = function(){
  s = s_gi(g_sOmnitureReportSuite);
  s.linkTrackEvents = s.events = "event32";
  s.prop19="Internal Login";
  s.linkTrackVars = "prop19,events";
  s.tl(true, 'o', 'Login');
};
Analytics.logAssetDownload = function(assetname){
  s = s_gi(g_sOmnitureReportSuite);
  s.linkTrackVars="prop52,eVar37,events";
  s.linkTrackEvents="event49";
  s.pagename = 'filetype:'+s.pagename;
  s.events="event49";
  s.prop52 = 'asset:'+assetname;
  eVar37= s.prop52;
  s.tl(this,'o','Asset Download');
}

/**
 * Trigger Omniture and Google Analytics tracking code
 * loc - Home, Footer, Header, Article
 * network - Facebook, Twitter, LinkedIn, Googleplus
 * socialAction - Like, Unlike, Share, Follow, Tweet, Retweet, Favorite, Click
 */
function socialTrack(loc,network,socialAction) {
  //Omniture
  var trackString = network + ":" + loc + ":" + socialAction;
  console.log("socialTrack() "+trackString);
  s.prop48 = trackString;
  s.eVar27 = "D=c48";
  s.event36 = trackString;
  s.linkTrackEvents=s.events="event36";
  s.linkTrackVars = "prop48,eVar27,events";
  s.tl(true, 'o', 'Social media');
}

function linkedInTrack() {
  socialTrack("Article","LinkedIn","Share");
};

function followTrack() {
  console.log("followTrack()");
  s = s_gi(g_sOmnitureReportSuite);
  s.linkTrackEvents = s.events = "event37";
  s.prop49="social media";
  s.linkTrackVars = "prop49,events";
  s.tl(true, 'o', 'Social media follow');
}
function brandFollowTrack(network){
  console.log("brandFollowTrack()");
  s = s_gi(g_sOmnitureReportSuite);
  s.eVar16="followed on:"+network;
  s.linkTrackEvents = s.events = "event37";
  s.prop49="social media";
  s.linkTrackVars = "prop49,eVar16,events";
  s.tl(true, 'o', 'Social media brand follow');
    if (typeof IDG.Eloqua != "undefined") {
        IDG.Eloqua.invoke_flash("social-" + network);
  }
}
function sharingTrack(type, pixelLogString) {
  s = s_gi(g_sOmnitureReportSuite);
  s.eVar16="Sharing on: " + type;
  s.linkTrackEvents = s.events = "event37";
  s.prop49="social media sharing";
  s.linkTrackVars = "prop49,eVar16,events";
  s.tl(true, 'o', 'Social Media Sharing');
    if (typeof IDG.Eloqua != "undefined") {
        IDG.Eloqua.invoke_flash("social-" + type);
  }
    // Record Social Event in pixel logger
    if (pixelLogString !== undefined) {
      $thm.logPlEvent({"b":"cio","e":pixelLogString,"t":"article","id":"2929788"});
    }
}
function moduleTrack(moduleName,loc) {
  console.log("moduleTrack()");
  s = s_gi(g_sOmnitureReportSuite);
  s.eVar42=moduleName;
  s.eVar44=loc;
  s.linkTrackEvents=s.events='event31';
  s.list3=s.prop42+","+s.eVar42;
  s.tl(true, 'o', 'Module click');
}
function trackSlideshow(articleId, slideTitle, slideNumber) {
  s = s_gi(g_sOmnitureReportSuite);
  s.prop15="page number:" + slideNumber;
  s.eVar15="D=c15";
  s.tl(true, 'o', 'Track Slideshow: articleId:' + articleId + ' title:' + slideTitle + " Slide No.:" + slideNumber);
}
function oClickTrack(key) {
  // test for empty key and don't log if empty
  if (typeof(key) != 'undefined' && key.length > 0){
    s = s_gi(g_sOmnitureReportSuite);

    s.linkTrackVars="eVar42,events";
      s.events="event49";
      s.linkTrackEvents="event49";

    s.eVar42 = key;
    s.list3=s.prop42+","+s.eVar42;
    s.tl(true,'o','module click');
  }
};
</script>
<!-- SiteCatalyst code version: H.26.2.
Copyright 1996-2013 Adobe, Inc. All Rights Reserved
More info available at http://www.omniture.com -->

<script language="JavaScript" type="text/javascript" src="/www.idge/js/analytics/s_code.js?v=20160525110859"></script>
<script language="JavaScript" type="text/javascript" id="omniVars"><!--
g_sOmnitureReportSuite = typeof g_sOmnitureReportSuite != 'undefined' && g_sOmnitureReportSuite ? g_sOmnitureReportSuite : "cxociocom";

/* You may give each page an identifying name, server, and channel on
the next lines. */
//BEGIN OMNITURE VARS
s.pageName="CIO:article:news:Cloud Computing:2929788:20 Ways to Measure the Success of Your Growing Cloud Investment";

s.pageType="";
if (typeof omnitureChannel != 'undefined'){
s.channel=omnitureChannel;
}
s.prop1="source:cio";s.eVar1="D=c1";
s.prop2="content type:news";s.eVar2="D=c2";
s.prop3="display type:article:news";s.eVar3="D=c3";
s.prop4="358 days";s.eVar4="D=c4";
s.prop5="blog";
s.prop6="";
s.prop7="blogs:empowering business";s.eVar7="D=c7";
s.prop9="category:cloud computing";s.eVar9="D=c9";
s.prop11="published:03-Jun-15";s.eVar11="D=c11";
s.prop12="aid:2929788";s.eVar12="D=c12";
s.prop13="";s.eVar13="D=c13";
s.prop14="author:david spark";s.eVar14="D=c14";
s.prop15="page number:1";s.eVar15="D=c15";
s.prop17="uri:/article/2929788/cloud-computing/20-ways-to-measure-the-success-of-your-growing-cloud-investment.html";s.eVar17="D=c17";
s.prop18="";s.eVar18="D=c18";
s.prop24=getLoginStatusForOmniture();s.eVar24="D=c24";
s.prop28=getTrackingToken('tk') ? "tokens:"+getTrackingToken('tk') : "";s.eVar28="D=c28";

s.prop37="rpn:cio:news:cio:article:news:cloud computing:2929788:20 ways to measure the success of your growing cloud investment";

s.eVar37="D=c37";
s.prop39="";s.eVar39="D=c39";
s.prop40=" sponsored";
s.prop41=IDG.GPT.getDisplayedAds(',') + ',' + IDG.GPT.getOtherAds(',');
s.prop42=IDG.GPT.getModules(',');s.eVar42="D=c42";


s.prop53="3255";s.eVar53="D=c53";
s.prop54="cloud computing";s.eVar30="D=c54";
s.eVar33=s.prop55="3255";s.eVar33="D=c55";
s.prop56="";s.eVar40="D=c56";
s.prop61="";s.eVar10="D=c61";
s.prop74="cloud";s.eVar58="D=c74";
s.prop75="cloud";s.eVar60="D=c75";
s.prop58="cloud:cloud";s.eVar59="D=c58";
//module tracking
var sModules = '';
for(var i in g_arrModules){
  if(g_arrModules[i] && i != ''){
    sModules += i+',';
  }
}
sModules = sModules.replace(/,$/,'');
s.prop42 = sModules;
s.eVar42="D=c42";
//now set events
s.events="";


  //article view
  s.events=s.apl(s.events,'event33',',');
  s.eVar41="03-Jun-15";

s.list3=s.prop42+","+s.eVar42;
//force all values to lower case for reporting consistency
for(var p in s){
  if(s[p] && p.match(/^(prop|ceVar)\d+$/)){
    //console.log(p+": "+s[p]);
    s[p] = s[p].toLowerCase();
    //console.log(p+": "+s[p]);
  }
}
//END OMNITURE VARS

/* Conversion Variables */
s.campaign=getTrackingToken('tk') ? "tokens:"+getTrackingToken('tk') : "";

/************* DO NOT ALTER ANYTHING BELOW THIS LINE ! **************/
var s_code=s.t();if(s_code)document.write(s_code)//--></script>
<script language="JavaScript" type="text/javascript"><!--
if(navigator.appVersion.indexOf('MSIE')>=0)document.write(unescape('%3C')+'\!-'+'-')
//--></script><noscript><img src="http://idgenterprise.d1.sc.omtrdc.net/b/ss/cxociocom/1/H.25--NS/0"
height="1" width="1" border="0" alt="" /></noscript><!--/DO NOT REMOVE/-->
<!-- End SiteCatalyst code version: H.26.2. -->















<script type="text/javascript">
$thm.logPlEvent({"b":"cio","e":"view","t":"article","id":"2929788"});
</script>



    <div id="loginModal"></div>
    <div id="logoffModal"></div>


















  <script type="text/javascript">

  (function(){

    function loadHorizon(){
      var s = document.createElement('script');
      s.type = 'text/javascript';
      s.async = true;
      s.src = location.protocol + '//ak.sail-horizon.com/horizon/v1.js';
      var x = document.getElementsByTagName('script')[0];
      x.parentNode.insertBefore(s, x);
    }

    loadHorizon();
    var oldOnLoad = window.onload;
    window.onload = function(){
              if (typeof oldOnLoad === 'function'){
                  oldOnLoad();
            }

        Sailthru.setup({
              domain: 'horizon.cio.com',
                useStoredTags: false
        });
    };
    if(console!=undefined){
      console.log('SAIL-23 fired Horizon');
    }

  })();
  </script>

  </body>
</html>
