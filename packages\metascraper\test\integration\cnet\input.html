<!DOCTYPE html>
<html lang="en" xmlns:og="http://opengraphprotocol.org/schema/"
  xmlns:fb="http://ogp.me/ns/fb#" class=" preampjs fusejs"
  data-triggered="true">

<head>
  <!-- <PERSON><PERSON> loves you! -->
  <!-- running tag: master.a28c66-prod-fly -->

  <meta content="text/html; charset=utf-8" http-equiv="Content-Type">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta content="en_US" http-equiv="Content-Language">
  <meta name="referrer" content="no-referrer-when-downgrade">

  <meta name="theme-color" content="#B80000">

  <meta name="author" content="Scott Stein">



  <title>Pebble 2, Pebble Time 2, Pebble Core: Pebble's fitness hat trick adds
    heart rate, GPS, Spotify, and 3G - CNET</title>


  <meta name="description"
    content="Smartwatches with heart rate support, plus a stand-alone puck called Core that acts as a 3G-connected smart button: <PERSON>eb<PERSON> is trying for a two-device approach in fitness wearables.">




  <meta property="og:site_name" content="CNET">
  <meta property="og:title"
    content="Pebble 2 watch adds heart rate, and the Spotify-enabled 3G GPS Pebble Core isn't a watch at all">
  <meta property="og:description"
    content="Pebble's latest wearables are trying harder than ever to nail fitness better, but one is aiming at being a new type of smart button.">
  <meta property="og:image"
    content="https://www.cnet.com/a/img/o7o1R0SgMd_Zf4mL0U0ICpR5x2o=/1200x630/2016/05/23/ee9a228a-d1d9-40b3-b401-4ba0e07e2fce/pebblecore3.jpg">
  <meta property="og:type" content="article">
  <meta property="og:url"
    content="https://www.cnet.com/tech/mobile/pebble-2-pebble-time-2-pebble-core-announced-coming-this-year-and-2017/">

  <link rel="apple-touch-icon" sizes="57x57"
    href="https://www.cnet.com/a/fly/bundles/cnetcss/images/core/icon/apple-touch-icon-57.png">
  <link rel="apple-touch-icon" sizes="72x72"
    href="https://www.cnet.com/a/fly/bundles/cnetcss/images/core/icon/apple-touch-icon-72.png">
  <link rel="apple-touch-icon" sizes="60x60"
    href="https://www.cnet.com/a/fly/bundles/cnetcss/images/core/icon/apple-touch-icon-60.png">
  <link rel="apple-touch-icon" sizes="76x76"
    href="https://www.cnet.com/a/fly/bundles/cnetcss/images/core/icon/apple-touch-icon-76.png">
  <link rel="apple-touch-icon" sizes="114x114"
    href="https://www.cnet.com/a/fly/bundles/cnetcss/images/core/icon/apple-touch-icon-114.png">
  <link rel="apple-touch-icon" sizes="120x120"
    href="https://www.cnet.com/a/fly/bundles/cnetcss/images/core/icon/apple-touch-icon-120.png">
  <link rel="apple-touch-icon" sizes="144x144"
    href="https://www.cnet.com/a/fly/bundles/cnetcss/images/core/icon/apple-touch-icon-144.png">
  <link rel="apple-touch-icon" sizes="152x152"
    href="https://www.cnet.com/a/fly/bundles/cnetcss/images/core/icon/apple-touch-icon-152.png">
  <link rel="mask-icon"
    href="https://www.cnet.com/a/fly/bundles/cnetcss/images/core/icon/safari_pinned_tab.svg"
    color="#B80000">

  <link rel="icon" type="image/png"
    href="https://www.cnet.com/a/fly/bundles/cnetcss/images/core/icon/favicon-16.png"
    sizes="16x16">
  <link rel="icon" type="image/png"
    href="https://www.cnet.com/a/fly/bundles/cnetcss/images/core/icon/favicon-32.png"
    sizes="32x32">
  <link rel="icon" type="image/png"
    href="https://www.cnet.com/a/fly/bundles/cnetcss/images/core/icon/favicon-48.png"
    sizes="48x48">
  <link rel="icon" type="image/png"
    href="https://www.cnet.com/a/fly/bundles/cnetcss/images/core/icon/favicon-96.png"
    sizes="96x96">
  <link rel="icon" type="image/png"
    href="https://www.cnet.com/a/fly/bundles/cnetcss/images/core/icon/favicon-128.png"
    sizes="128x128">
  <link rel="icon" type="image/png"
    href="https://www.cnet.com/a/fly/bundles/cnetcss/images/core/icon/favicon-196.png"
    sizes="196x196">

  <meta name="msapplication-task"
    content="name=Home; action-uri=https://www.cnet.com/; icon-uri=https://www.cnet.com/favicon.ico">
  <meta name="msapplication-task"
    content="name=Downloads; action-uri=http://download.cnet.com/; icon-uri=http://download.cnet.com/favicon.ico">
  <meta name="msapplication-task"
    content="name=News; action-uri=https://www.cnet.com/news/; icon-uri=https://www.cnet.com/favicon.ico">
  <meta name="msapplication-task"
    content="name=Reviews; action-uri=https://www.cnet.com/reviews/; icon-uri=https://www.cnet.com/favicon.ico">
  <meta name="msapplication-task"
    content="name=Videos; action-uri=https://www.cnet.com/videos/; icon-uri=https://www.cnet.com/favicon.ico">
  <meta name="msapplication-task"
    content="name=Forums; action-uri=http://forums.cnet.com/; icon-uri=https://www.cnet.com/favicon.ico">

  <meta name="msapplication-TileImage"
    content="https://www.cnet.com/a/fly/bundles/cnetcss/images/core/icon/mstile-144.png">
  <meta name="msapplication-square70x70logo"
    content="https://www.cnet.com/a/fly/bundles/cnetcss/images/core/icon/mstile-70.png">
  <meta name="msapplication-square150x150logo"
    content="https://www.cnet.com/a/fly/bundles/cnetcss/images/core/icon/mstile-150.png">
  <meta name="msapplication-wide310x150logo"
    content="https://www.cnet.com/a/fly/bundles/cnetcss/images/core/icon/mstile-310x150.png">
  <meta name="msapplication-square310x310logo"
    content="https://www.cnet.com/a/fly/bundles/cnetcss/images/core/icon/mstile-310.png">
  <link rel="canonical"
    href="https://www.cnet.com/tech/mobile/pebble-2-pebble-time-2-pebble-core-announced-coming-this-year-and-2017/">



  <link rel="alternate" hreflang="x-default"
    href="https://www.cnet.com/tech/mobile/pebble-2-pebble-time-2-pebble-core-announced-coming-this-year-and-2017/">

  <link rel="alternate" hreflang="es-us"
    href="https://www.cnet.com/es/tech/mobile/pebble-2-pebble-time-2-pebble-core-nuevos-monitores-actividad/">
  <link rel="alternate" hreflang="es-ar"
    href="https://www.cnet.com/es/tech/mobile/pebble-2-pebble-time-2-pebble-core-nuevos-monitores-actividad/">
  <link rel="alternate" hreflang="es-bo"
    href="https://www.cnet.com/es/tech/mobile/pebble-2-pebble-time-2-pebble-core-nuevos-monitores-actividad/">
  <link rel="alternate" hreflang="es-cl"
    href="https://www.cnet.com/es/tech/mobile/pebble-2-pebble-time-2-pebble-core-nuevos-monitores-actividad/">
  <link rel="alternate" hreflang="es-co"
    href="https://www.cnet.com/es/tech/mobile/pebble-2-pebble-time-2-pebble-core-nuevos-monitores-actividad/">
  <link rel="alternate" hreflang="es-cr"
    href="https://www.cnet.com/es/tech/mobile/pebble-2-pebble-time-2-pebble-core-nuevos-monitores-actividad/">
  <link rel="alternate" hreflang="es-cu"
    href="https://www.cnet.com/es/tech/mobile/pebble-2-pebble-time-2-pebble-core-nuevos-monitores-actividad/">
  <link rel="alternate" hreflang="es-do"
    href="https://www.cnet.com/es/tech/mobile/pebble-2-pebble-time-2-pebble-core-nuevos-monitores-actividad/">
  <link rel="alternate" hreflang="es-ec"
    href="https://www.cnet.com/es/tech/mobile/pebble-2-pebble-time-2-pebble-core-nuevos-monitores-actividad/">
  <link rel="alternate" hreflang="es-sv"
    href="https://www.cnet.com/es/tech/mobile/pebble-2-pebble-time-2-pebble-core-nuevos-monitores-actividad/">
  <link rel="alternate" hreflang="es-gt"
    href="https://www.cnet.com/es/tech/mobile/pebble-2-pebble-time-2-pebble-core-nuevos-monitores-actividad/">
  <link rel="alternate" hreflang="es-hn"
    href="https://www.cnet.com/es/tech/mobile/pebble-2-pebble-time-2-pebble-core-nuevos-monitores-actividad/">
  <link rel="alternate" hreflang="es-mx"
    href="https://www.cnet.com/es/tech/mobile/pebble-2-pebble-time-2-pebble-core-nuevos-monitores-actividad/">
  <link rel="alternate" hreflang="es-ni"
    href="https://www.cnet.com/es/tech/mobile/pebble-2-pebble-time-2-pebble-core-nuevos-monitores-actividad/">
  <link rel="alternate" hreflang="es-pa"
    href="https://www.cnet.com/es/tech/mobile/pebble-2-pebble-time-2-pebble-core-nuevos-monitores-actividad/">
  <link rel="alternate" hreflang="es-py"
    href="https://www.cnet.com/es/tech/mobile/pebble-2-pebble-time-2-pebble-core-nuevos-monitores-actividad/">
  <link rel="alternate" hreflang="es-pe"
    href="https://www.cnet.com/es/tech/mobile/pebble-2-pebble-time-2-pebble-core-nuevos-monitores-actividad/">
  <link rel="alternate" hreflang="es-pr"
    href="https://www.cnet.com/es/tech/mobile/pebble-2-pebble-time-2-pebble-core-nuevos-monitores-actividad/">
  <link rel="alternate" hreflang="es-es"
    href="https://www.cnet.com/es/tech/mobile/pebble-2-pebble-time-2-pebble-core-nuevos-monitores-actividad/">
  <link rel="alternate" hreflang="es-uy"
    href="https://www.cnet.com/es/tech/mobile/pebble-2-pebble-time-2-pebble-core-nuevos-monitores-actividad/">
  <link rel="alternate" hreflang="es-ve"
    href="https://www.cnet.com/es/tech/mobile/pebble-2-pebble-time-2-pebble-core-nuevos-monitores-actividad/">
  <link rel="alternate" hreflang="en-us"
    href="https://www.cnet.com/tech/mobile/pebble-2-pebble-time-2-pebble-core-announced-coming-this-year-and-2017/">

  <link rel="search" type="application/opensearchdescription+xml"
    title="CNET Search" href="https://www.cnet.com/opensearch.xml">

  <meta name="facebook-domain-verification"
    content="ac1izblyrxd6ql7y6vlex7yeuie0us">
  <meta property="fb:app_id" content="***********">
  <meta property="fb:admins" content="***************">
  <meta property="article:publisher" content="https://www.facebook.com/cnet">
  <meta property="article:author"
    content="https://www.facebook.com/profile.php?id=*********">

  <meta property="fb:pages" content="**********">
  <meta content="********" name="twitter:account_id">
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:url"
    content="https://www.cnet.com/tech/mobile/pebble-2-pebble-time-2-pebble-core-announced-coming-this-year-and-2017/">
  <meta name="twitter:title"
    content="Pebble 2 watch adds heart rate, and the Spotify-enabled 3G GPS Pebble Core isn't a watch at all">
  <meta name="twitter:description"
    content="Pebble's latest wearables are trying harder than ever to nail fitness better, but one is aiming at being a new type of smart button.">
  <meta name="twitter:image"
    content="https://www.cnet.com/a/img/o7o1R0SgMd_Zf4mL0U0ICpR5x2o=/1200x630/2016/05/23/ee9a228a-d1d9-40b3-b401-4ba0e07e2fce/pebblecore3.jpg">
  <meta name="twitter:site" content="@CNET">
  <meta name="twitter:creator" content="@jetscott">

  <meta content="I1kHyfzmmG1fEVjq8GBUgkfCHc6PNtxce1_VyUuJhws"
    name="google-site-verification">
  <meta content="wNWLoFvk5wsdZQ-u75uCNiaHNa7FLFmNtH2t7dZbXmQ"
    name="google-site-verification">
  <meta content="47524839a64b83c951afca9ef6c838e0" name="p:domain_verify">
  <meta name="msapplication-TileColor" content="#ffffff">
  <meta name="msapplication-tooltip"
    content="Product reviews and prices, software downloads, and tech news - CNET">
  <meta name="msapplication-starturl" content="https://www.cnet.com/">
  <meta name="msapplication-window" content="width=1024;height=768">
  <meta name="msapplication-navbutton-color" content="#424244">


  <meta name="viewport" content="width=device-width">



  <meta name="news_keywords"
    content="wearable tech, pebble technology, apple, gps, samsung">


  <link rel="amphtml"
    href="https://www.cnet.com/google-amp/news/pebble-2-pebble-time-2-pebble-core-announced-coming-this-year-and-2017/">

  <meta property="article:opinion" content="false">
  <meta property="article:content_tier" content="free">
  <meta property="og:type" content="article">
  <meta name="robots" content="max-image-preview:large">

  <link rel="preload" as="image"
    href="https://www.cnet.com/a/img/nkHnBz5tlKgLwecmn-8bs-qiWjI=/1092x614/2016/05/23/b2257bbb-1b75-4390-b5c1-303f2589b427/pebble.jpg">



  <link rel="preload"
    href="https://www.cnet.com/a/fly/bundles/cnetcss/fonts/Proxima%20Nova/Regular.woff2"
    as="font" type="font/woff2" crossorigin="">
  <link rel="preload"
    href="https://www.cnet.com/a/fly/bundles/cnetcss/fonts/Proxima%20Nova/Bold.woff2"
    as="font" type="font/woff2" crossorigin="">
  <link rel="preload"
    href="https://www.cnet.com/a/fly/bundles/cnetcss/fonts/Proxima%20Nova/Extrabold.woff2"
    as="font" type="font/woff2" crossorigin="">
  <link rel="stylesheet"
    href="https://www.cnet.com/a/fly/css/core/main.desktop-5e929ece75-rev.css">
  <link rel="stylesheet"
    href="https://www.cnet.com/a/fly/css/core/header.mobile-c52cdff92d-rev.css">

  <link rel="stylesheet"
    href="https://www.cnet.com/a/fly/css/common/articleReview.desktop-d9522332f0-rev.css">





















  <iframe src="javascript:void(0)" title=""
    style="width: 0px; height: 0px; border: 0px; display: none;"></iframe>
  <script type="application/ld+json">
    {
      "@context": "https:\/\/schema.org",
      "@type": "NewsArticle",
      "mainEntityOfPage": {
        "@type": "WebPage",
        "@id": "https:\/\/www.cnet.com\/tech\/mobile\/pebble-2-pebble-time-2-pebble-core-announced-coming-this-year-and-2017\/"
      },
      "url": "https:\/\/www.cnet.com\/tech\/mobile\/pebble-2-pebble-time-2-pebble-core-announced-coming-this-year-and-2017\/",
      "headline": "Pebble 2, Pebble Time 2, Pebble Core: Pebble's fitness hat trick adds heart rate, GPS, Spotify, and 3G",
      "description": "Smartwatches with heart rate support, plus a stand-alone puck called Core that acts as a 3G-connected smart button: Pebble is trying for a two-device approach in fitness wearables.",
      "keywords": "Smartwatches, Fitness, Spotify",
      "image": {
        "@type": "ImageObject",
        "url": "https:\/\/www.cnet.com\/a\/img\/PV0KB7YLf6b4iiqlMZJC5W9AVbs=\/1200x675\/2016\/05\/23\/ee9a228a-d1d9-40b3-b401-4ba0e07e2fce\/pebblecore3.jpg",
        "width": "1200",
        "height": "675"
      },
      "author": {
        "@type": "Person",
        "name": "Scott Stein",
        "url": "https:\/\/www.cnet.com\/profiles\/scottstein8\/"
      },
      "datePublished": "2016-05-24T07:00:00-0700",
      "dateModified": "2016-05-24T07:00:00-0700",
      "publisher": {
        "@type": "Organization",
        "name": "CNET",
        "logo": {
          "@type": "ImageObject",
          "url": "https:\/\/www.cnet.com\/a\/fly\/bundles\/cnetcss\/images\/core\/redball\/logo_310_white.png",
          "width": "310",
          "height": "310"
        }
      },
      "articleBody": "Editors' note, December 7, 2016: Pebble has announced that its assets have been acquired by Fitbit and it will no longer release new products. While the Pebble 2 and other Pebble watches will continue to work, no new software updates or features will be released. Pebble also warned that down the road functionality or service quality may be reduced. Kickstarter backers of the Pebble Time 2 and Pebble Core will receive a full refund for their purchase. Pebble's back with more smartwatches...and something else.Good news if you were a fan of Pebble's formula for smartwatches: There are two new watches on their way this September. They're more affordable, look better and have added heart rate tracking. The Pebble 2 and Pebble Time 2 hit Kickstarter today. But there's another thing coming early next year, called Pebble Core, that's not a watch at all.It's a box. A clip-on. A keychain. A smart button. A Spotify music player. A GPS and even 3G-enabled fitness tracker. An emergency gadget-slash-Pebble accessory. A programmable Android mini-computer.And it doesn't even have a screen.I got a chance to look at both at Pebble's headquarters in Redwood City, California, while talking with CEO Eric Migicovsky about why he's decided to move beyond the wrist for the first time.Pebble Core: A little do-it-all smart button box-slash-music player-slash-fitness thingMigicovsky has believed in fitness on smartwatches since the Pebble first launched. The campaign video for the Kickstarter campaign of the first Pebble Watch in 2012 focused on running, cycling and swim tracking. Migicovsky is a fitness addict. He's also casual. He met with me wearing a t-shirt, cargo shorts and flip-flops.Pebble watches run their own operating system, and have their own unique advantages compared to other watches in the ever-more-crowded wearable world. They have always-on reflective displays, week-plus battery life and better water resistance. But they're also less feature-rich than Android Wear, Apple Watch and Samsung's Gear S2. For instance, they can't directly play music.The Core feels like a bundle of smartwatch features all put into a separate kinda-sorta accessory. It has 4GB of storage and dedicated Spotify support to download music playlists (or, even stream over cellular wireless connection once a 3G SIM card is popped in), it has GPS and it has two touch surfaces that act as buttons, which can be programmed for anything from hailing an Uber to sending an emergency text SOS. Or, opening a smart lock. \"In some ways it's a computer on your keychain,\" says Migicovsky.It's small, it's plastic. And it looks more like a pebble than anything Pebble's made before.\"I love running with Pebble, but at the same time, I end up taking my phone because I like to listen to Spotify when I run and I like GPS for tracking,\" explains Migicovsky. \"And we looked at it and we looked at integrating it all into a watch. And we decided to actually create a secondary device, something that clips on to your waist belt or you can put in your pocket.\"\"We built it because we got feedback from our users that said they didn't want to run with their phone,\" says Migicovsky. In that sense, Pebble Core is almost like part of a high-end 3G GPS-enabled smartwatch like the Samsung Gear S2 3G, peeled off and turned into its own device.I couldn't try Pebble Core. It was just a screenless box I held for a few minutes. I was told it runs Android, but I couldn't tell how, or in what way. But Migicovsky proudly promises the Pebble Core is \"hackable\", meaning you can install apps and program its buttons in a variety of ways. (\"You can write apps in JavaScript for it,\" he explains, \"they could store it on here.\") There's even a mounting input on the back, if you take off the magnetic loop that normally clips onto a belt or keyring. Pebble Core could be a super-smart version of a Tile thing-finder, or simply a music player (it has a headphone jack and Bluetooth) and GPS-connected fitness tracker. It connects with a Pebble watch, or it can work alone. And at just $69 for its Kickstarter price (about \u00a350 or AU$100 converted) it may well be the wireless-headphone-friendly, Spotify-capable iPod Shuffle replacement that many runners have long dreamed of. Battery life lasts for up to 9 hours of music playback with GPS enabled, or for days on standby. And the Core charges wirelessly using the popular Qi standard, unlike Pebble's watches that use their own custom charge cables. Migicovsky sees a future where many little wearable devices work together all over your body, not just on the wrist: \"I see a future where at some point you're gonna have multiple wearables that all talk to each other, that connect through the Internet.\" Pebble Core is clearly aiming to take a first bite of it. Pebble watches with heart rate are closer to Fitbit than everThe watches are coming in September and fit a far clearer need. They're both distinct, affordably priced improvements to existing Pebble watches. And they both come closer to being full-fledged fitness trackers.Most notably, they both have optical heart rate tracking via a little bump on the back where continuous heart rate readings are measured on-wrist once every 10 minutes, or continuously during exercise.The Pebble 2 is a reboot of the very first black and white plastic Pebble, redone with a Gorilla Glass-covered black and white display and a slim, clean look. It has a microphone, just like the Pebble Time, for voice responses to messages. It's 30-meter water resistant. It costs just $129. (That converts to about \u00a390 or AU$180.)Pebble Time 2 gets rid of any distinction between the plastic Pebble Time and its better all-steel model. This is basically the Time Steel with heart rate tracking and a 50 percent larger color screen, at a lower $199 price (that converts to about \u00a3135 or AU$275). Both watches are compatible with the same magnetic chargers, and Pebble's embryonic smart strap system, which allows bands to attach to the rear charge port to access data and power. One year later, and smart straps still seem more like a promise than a reality, with only a few Kickstarter-type concepts in existence. Migicovsky still says Pebble doesn't plan to make any itself. \"Our smart strap strategy is very much about enabling and helping the community get these to market. We're a small company, we're focused on making the watches and the Core. That's the main focus for us. We see that the community is already starting to build these great accessories.\"Other than a larger, color display and better battery -- the Pebble 2 gets around 7 days of battery life, the larger Pebble Time 2 should get 10, according to Migicovsky -- these work the same. I'd probably go for the smaller, more affordable Pebble 2. It reminds me of a new-age Casio watch. I think it's the best-looking option.Much like last year, early Kickstarter adopters get discounts on both watches: $99 for Pebble 2, $169 for Pebble Time 2. Despite last year's somewhat controversial Kickstarter-first launch of Pebble Time, the same thing's happening again this year. According to Migicovsky, it's to satisfy the Kickstarter community that supported Pebble in the first place.Pebble's OS is also getting an update with some useful additions:                    Quick-peek notifications now pop below the watch face much like they do on Android Wear or Apple Watch, instead of taking over the whole screen.Actions, which are basically programmable quick functions that can do specific tasks faster. Checking weather, calling an Uber, or doing one-function things can be assigned to the Pebble buttons, or pulled out of a list-like menu.Migicovsky quotes the refrain that repeats throughout wearable tech: It's still too slow, and \"3 to 5 seconds\" is the goal for any interaction. \"It still takes too many clicks to navigate through the app list and dive all the way into an app. And when in reality, you've got your phone. If you're gonna spend more than 5 or 10 seconds on your watch, then you might as well take out your phone.\"How much more room is there on the wrist...or anywhere else?Shipments of wearable tech keep rising, but Pebble's position in smartwatches seems to be in flux. Recent wearable market estimates that include fitness trackers don't even have Pebble in the top 5.With Apple, Google, Samsung, Fitbit and a ton of other traditional watch companies like Fossil heading into the mix, Pebble might have a rough time gaining ground. But maybe there's a future in devices off-wrist.Or, maybe, Pebble Core hints at a future beyond the current Pebble watch. Music playback. GPS. Android. A more flexible, programmable design.Doesn't sound like a bad idea for Pebble 3.This article also appears in Spanish. Read: Pebble 2, Pebble Time 2, Pebble Core: Pebble lanza tres nuevos monitores de actividad",
      "speakable": {
        "@type": "SpeakableSpecification",
        "cssSelector": [".speakableText", ".article-dek", ".speakableTextP1",
          ".speakableTextP2"
        ]
      }
    }

  </script>
















  <script type="application/ld+json">
    {
      "@context": "https:\/\/schema.org",
      "@type": "BreadcrumbList",
      "itemListElement": [{
        "@type": "ListItem",
        "position": 1,
        "item": {
          "@id": "https:\/\/www.cnet.com\/",
          "name": "CNET"
        }
      }, {
        "@type": "ListItem",
        "position": 2,
        "item": {
          "@id": "https:\/\/www.cnet.com\/tech\/",
          "name": "Tech"
        }
      }, {
        "@type": "ListItem",
        "position": 3,
        "item": {
          "@id": "https:\/\/www.cnet.com\/tech\/mobile\/",
          "name": "Mobile"
        }
      }, {
        "@type": "ListItem",
        "position": 4,
        "item": {
          "@id": "https:\/\/www.cnet.com\/tech\/mobile\/pebble-2-pebble-time-2-pebble-core-announced-coming-this-year-and-2017\/",
          "name": "Pebble 2, Pebble Time 2, Pebble Core: Pebble's fitness hat trick adds heart rate, GPS, Spotify, and 3G"
        }
      }]
    }

  </script>


  <script src="https://cdn.cookielaw.org/scripttemplates/otSDKStub.js"
    type="text/javascript" charset="UTF-8"
    data-domain-script="d7b19758-74b7-4244-8f94-61299f58ea38" async=""></script>

  <script type="application/javascript">
    (function (a) {
      var w = window,
        b = 'cbsoptanon',
        q = 'cmd',
        r = 'config';
      w[b] = w[b] ? w[b] : {};
      w[b][q] = w[b][q] ? w[b][q] : [];
      w[b][r] = w[b][r] ? w[b][r] : [];

      a.forEach(function (z) {
        w[b][z] = w[b][z] || function () {
          var c = arguments;
          w[b][q].push(function () {
            w[b][z].apply(w[b], c);
          })
        }
      });
    })(["onIframesReady", "onFormsReady", "onScriptsReady", "onAdsReady"]);

    //Add configs
    window.cbsoptanon.config.push({
      enableServices: false,
      setNpaOnConsentChange: true,
      euMaxAttempts: 25,
      countryCodeMaxAttempts: 25,
      oneTrustTimeout: 700
    });

    //Load iframes as soon as ready
    window.cbsoptanon.cmd.push(function (_cbsoptanon) {
      console.log('Loading iframes');
      _cbsoptanon.tags.load('IFRAME');
    });

  </script>

  <script src="https://www.cnet.com/a/privacy/optanon/optanon-v1.1.0.js"
    id="bb-cbsoptanon" type="application/javascript" async=""></script>








  <script type="application/javascript">
    //grab push notification payload stored from ua-push-worker in indexedDB
    if ("indexedDB" in window) {
      var inDb;
      var inDbRequest = indexedDB.open("notifications", 1);

      inDbRequest.onupgradeneeded = function () {
        let db = inDbRequest.result;
        if (!db.objectStoreNames.contains('items')) {
          let notificationItems = db.createObjectStore('items', {
            autoIncrement: true
          });
        }
      };

      inDbRequest.onsuccess = function (event) {
        inDb = event.target.result;
        if (inDb.objectStoreNames.contains('items')) {
          //get the payload and save it in sessionstorage
          inDb.transaction(["items"], 'readwrite').objectStore("items")
            .getAll().onsuccess = function (event) {

              if (event.target.result) {
                var notifIds = event.target.result;

                try {
                  var lastNotifId = notifIds[notifIds.length - 1];
                  var decodedLastNotifId = atob(lastNotifId);
                  var notIdObj = JSON.parse(decodedLastNotifId);
                  window.CnetPageVars.notificationId = notIdObj.push_id;
                } catch (err) {
                  return false;
                }

                if (typeof (Storage) !== 'undefined' && decodedLastNotifId &&
                  notIdObj.push_id) {
                  sessionStorage.setItem('v76_message_id', notIdObj.push_id);
                }
                //then clear the db table
                inDb.transaction(["items"], 'readwrite').objectStore("items")
                  .clear().onsuccess = function (event) {};
              }
            };
        }
      };
    }

    if (typeof (Storage) !== 'undefined') {}

  </script>

  <script type="application/javascript">
    window.CnetPageVars = {
      ads: {
        data: {
          "gpt": {
            "targeting": {
              "section": "reviews",
              "pid": "pebble 2 pebble time 2 pebble core announced coming this year and 2017,pebble 2,pebble time,pebble watch,philips spotify,samsung gear s2,samsung gear,samsung gear s2 3g,google android wear,garmin approach",
              "topic": "tech,mobile,smartwatches,wearable-tech,gps,storage,monitors",
              "ptopic": "smartwatches",
              "tag": "fitness,spotify,wearable-tech,samsung,fitbit-inc,kickstarter,apple-watch,apple-ipod-shuffle,google-android-wear,uber",
              "collection": "",
              "edition": "us",
              "test": "||",
              "mfr": ",pebble-technology,apple,samsung,fitbit-inc,spotify,google,uber,fossil-inc",
              "prodtype": "headphones,phone,running,android,cycle,microphones,battery,clip-on,strap,computer",
              "device": "desktop",
              "ptype": "article",
              "cid": "pebble-2-pebble-time-2-pebble-core-announced-coming-this-year-and-2017,dfb49c4e-2dd0-4182-890c-37897d913be9",
              "env": "prod"
            },
            "adConfig": {
              "regions": [{
                "region": "uk",
                "countries": ["be", "dk", "fi", "de", "is", "it", "lu",
                  "nl", "no", "es", "se", "gb", "ie", "za", "bh", "kw",
                  "om", "qa", "sa", "ae", "iq", "eg", "fr"
                ]
              }, {
                "region": "au",
                "countries": ["au", "nz"]
              }, {
                "region": "asia",
                "countries": ["sg", "my", "th", "id", "ph", "hk", "vn",
                  "in", "tw"
                ]
              }],
              "network": {
                "dev": 22281868136,
                "qa": 22281868136,
                "prod": 22309610186
              },
              "method": "async",
              "adUnits": {
                "intromercial": [],
                "skin": [],
                "recirculation": [],
                "resourceCenterTop": [],
                "resourceCenterBottom": [],
                "mpuPlusTop": [],
                "marqueeTop": [],
                "mpuTop": [],
                "mpuBottom": [],
                "leaderPlusTop": [],
                "leaderTop": [],
                "buttonTop": [],
                "dynamicTextLinkTop": [],
                "mpuMiddle": [],
                "mpuPlusMiddle": [],
                "mpuMiddle2": [],
                "mpuPlusBottom": [],
                "leaderPlusMiddle": [],
                "leaderMiddle": [],
                "leaderMiddle2": [],
                "leaderMiddle3": [],
                "leaderMiddle4": [],
                "leaderBottom": [],
                "leaderPlusBottom": [],
                "leaderboardMiddle": [],
                "incontentAdTop": [],
                "incontentAdBottom": [],
                "nativeLeader": {
                  "customTargeting": {
                    "stc": "jeTMd4HjGdxg4dTceXGzahvk",
                    "strnativekey": "9ZUFuTwq2XugQH5BAj3iNPab"
                  }
                },
                "nativeLeaderIncrement": [],
                "dynamicShowcaseTop": [],
                "infoButtonsTop": [],
                "replayWideTop": {
                  "customTargeting": {
                    "stc": "jeTMd4HjGdxg4dTceXGzahvk",
                    "strnativekey": "9ZUFuTwq2XugQH5BAj3iNPab"
                  }
                },
                "replayWideBottom": [],
                "nativeThisweekoncnet": {
                  "customTargeting": {
                    "stc": "d320ba44",
                    "strnativekey": "2434f2df"
                  }
                },
                "nativeCompare": [],
                "overgifTop": [],
                "showcaseBottom": [],
                "showcaseTop": [],
                "videoThumbnailTop": [],
                "navAd": [],
                "navAdPlus": [],
                "navAdPlusLeader": [],
                "incontentAllTop": [],
                "incontentAdPlusTop": [],
                "incontentAdPlusBillboardTop": [],
                "incontentAdPlusMiddle": [],
                "incontentAdPlusBillboardMiddle": [],
                "incontentAdPlusMiddle2": [],
                "incontentAdPlusBillboardMiddle2": [],
                "incontentAdPlusMiddle3": [],
                "incontentAdPlusBillboardMiddle3": [],
                "incontentAdPlusBottom": [],
                "incontentAdPlusBillboardBottom": [],
                "flexIncontentAdPlusBillboardTop": [],
                "incontentAdBillboardTop": [],
                "premierBrandsTop": [],
                "premierBrandsBottom": [],
                "sponsoredLogoTop": [],
                "sponsoredLogoBottom": [],
                "sponsoredLogoLarge": [],
                "nativeMpuTop": [],
                "nativeMpuMiddle": {
                  "customTargeting": {
                    "stc": "PDm281QAgRa4ZbCUbg4onNo1",
                    "strnativekey": "AKVVxuLpfqUsBfc9PjwYoAC3"
                  }
                },
                "nativeMiddle": [],
                "nativeMiddle2": [],
                "nativeMpuBottom": [],
                "browseCars": [],
                "dealsTileTop": [],
                "dealsTileMiddle2": [],
                "dealsTileMiddle3": [],
                "dealsTileBottom": [],
                "ampBanner": {
                  "sizes": [
                    [320, 50]
                  ],
                  "position": "increment"
                },
                "ampBannerSticky": {
                  "sizes": [
                    [320, 50]
                  ],
                  "position": "increment"
                },
                "ampMpu": {
                  "sizes": [
                    [300, 250]
                  ],
                  "position": "increment"
                },
                "ampMpuPlus": {
                  "sizes": [
                    [300, 250],
                    [320, 480]
                  ],
                  "position": "increment"
                },
                "ampReplayWide": {
                  "sizes": [
                    [11, 11], "fluid"
                  ],
                  "position": "increment"
                },
                "ampSponsoredLogo": {
                  "sizes": [
                    [88, 31]
                  ],
                  "position": "increment"
                }
              },
              "mobile": {
                "adUnits": {
                  "hpto": [],
                  "mobileBannerTop": [],
                  "mobileBannerMpuHptoOverlay": [],
                  "mobileMpuBottom": [],
                  "mobileBanner": [],
                  "mobileReplayWide": [],
                  "mobileReplayWidePlus": [],
                  "mobileBannerPlus": [],
                  "mobileDealsTile": [],
                  "mobileMpu": [],
                  "mobileMpuNative": [],
                  "mobileOvergif": [],
                  "mobileShowcase": [],
                  "mobileNavAd": [],
                  "mobileNavAdPlus": [],
                  "mobileNavAdPlusBanner": [],
                  "mobileIncontentAll": [],
                  "mobileIncontentAdPlus": [],
                  "mobileIncontentAdPlusNewsbtm": [],
                  "mobileIncontentMpuPlus": [],
                  "mobileSponsoredLogo": [],
                  "mobileNativeBottom": [],
                  "mobileFacebook": [],
                  "mobileSponsoredLogoLarge": [],
                  "mobileBrowseCars": [],
                  "ampBanner": {
                    "sizes": [
                      [320, 50]
                    ],
                    "position": "increment"
                  },
                  "ampBannerSticky": {
                    "sizes": [
                      [320, 50]
                    ],
                    "position": "increment"
                  },
                  "ampMpu": {
                    "sizes": [
                      [300, 250]
                    ],
                    "position": "increment"
                  },
                  "ampMpuPlus": {
                    "sizes": [
                      [300, 250],
                      [320, 480]
                    ],
                    "position": "increment"
                  },
                  "ampReplayWide": {
                    "sizes": [
                      [11, 11], "fluid"
                    ],
                    "position": "increment"
                  },
                  "ampSponsoredLogo": {
                    "sizes": [
                      [88, 31]
                    ],
                    "position": "increment"
                  }
                }
              },
              "siteName": "cnet",
              "defaultRegion": "aw",
              "collapseEmptyDiv": false,
              "setRefreshUnfilledSlots": true,
              "setClearUnfilledSlots": false,
              "disableInitialLoad": false,
              "disableInitialLoadCompanions": false,
              "enableSingleRequest": true,
              "adOrder": [],
              "disableInitialLoadRefresh": "false",
              "enableHeaderBidding": false,
              "disableAdIncrement": false,
              "disableClearTargetingOnInit": false,
              "enableRepeatedAds": false,
              "enableYieldIntelligence": "false"
            },
            "containerId": "61718e4e621d3",
            "slotVars": {
              "network": "22309610186",
              "siteName": "cnet",
              "collapseEmptyDiv": "",
              "topics": "tech\/mobile\/smartwatches",
              "platform": "desktop"
            },
            "adType": "gpt"
          },
          "cookieName": "cnet_ad",
          "cookieServicePath": "\/ad\/ad-cookie\/",
          "session": 4,
          "subSession": 6
        }
      },
      assetsHosts: ["https:\/\/www.cnet.com\/a\/fly"],
      assetsVersion: {
        version: 'a28c66-fly'
      },
      auto: false,
      build: true,
      cmsEdit: {
        contentId: 'dfb49c4e-2dd0-4182-890c-37897d913be9',
        contentType: 'article'
      },
      device: 'desktop',
      deviceOriginal: 'desktop',
      disqus: {
        shortname: 'cnet-1',
        publicKey: 'ufGwgeeqlA0GFpKyNaNhEkmcPSqdHGlT5hwI5efgcclsQLIXaCAHU3PsuDiGNa7Z',
        sso: {
          name: 'CNET',
          authLoginUrl: '/disqus/sso/login/',
          authLogoutUrl: '/disqus/sso/logout/',
          button: 'https://www.cnet.com/a/fly/bundles/cnetcss/images/core/icon/favicon-32.png',
          icon: 'https://www.cnet.com/favicon.ico',
          loginUrl: 'https://www.cnet.com/?redirectUrl=/disqus/login/',
          logoutUrl: 'https://www.cnet.com/user/logout/',
          profileUrl: 'https://www.cnet.com/profiles/%7Busername%7D/'
        }
      },
      edition: {
        code: 'us'
      },
      environment: 'prod',
      pageType: 'article',
      personalization: {
        base: {
          project: 'i-cnet-prod',
          platform: 'web',
          environment: 'prod'
        },
        firebase: {
          config: {
            apiKey: 'AIzaSyBqfebhuwHJuZRko9qIT87K_EIlKGTfHbA',
            authDomain: 'i-cnet-engagement-prod.firebaseapp.com',
            projectId: 'i-cnet-engagement-prod',
            appId: '1:914013374524:web:4ae41512a7271cf2451b39',
          }
        },
        firestore: {
          collections: {
            registered: {
              users: 'r_users',
              interests_summary: 'r_interests_summary'
            },
            anonymous: {
              users: 'a_users',
              interests_summary: 'a_interests_summary'
            }
          },
          settings: {
            host: 'firestore.googleapis.com',
            ssl: true,
            experimentalForceLongPolling: true
          }
        },
        functions: {
          override: false,
          origin: ''
        },
        urbanAirship: {
          appKey: 'vfB_rNe6QmqG3FM6u3fQ_w',
          token: 'MTp2ZkJfck5lNlFtcUczRk02dTNmUV93OjF4amNnZWVGZFpKeHd3R1hSSzF6N2xaZkw5clQ1ME4tQWpHUFpDRGU2Ujg',
          vapidPublicKey: 'BLjW5aAAExzAsGPWMgbTHPM+L7ShRKV5kbcp7aCqB0VCql/4QhAKiEHVZaTMX1wDmp5OdFcnFVAJrR5rK1x173g='
        }
      },
      services: {
        "5g": {
          "enabled": false
        },
        "5g_phones": {
          "enabled": false
        },
        "500px": {
          "enabled": true
        },
        "ab_test": {
          "enabled": true
        },
        "ad_heavy": {
          "enabled": false
        },
        "allconnect_integration": {
          "enabled": false
        },
        "amazon_bidder": {
          "enabled": true
        },
        "amazon_native": {
          "enabled": true
        },
        "audiencescience": {
          "enabled": true
        },
        "bem": {
          "enabled": false
        },
        "bidbarrel": {
          "enabled": true
        },
        "capi": {
          "enabled": false
        },
        "censor_comments": {
          "enabled": false
        },
        "ces": {
          "enabled": false
        },
        "chatid": {
          "enabled": true
        },
        "chartbeat_headline": {
          "enabled": false
        },
        "chartbeat_tracking": {
          "enabled": true
        },
        "cohesion": {
          "enabled": true
        },
        "comscore": {
          "enabled": true
        },
        "content_discovery": {
          "enabled": true
        },
        "core_web_vital_tracking": {
          "enabled": true
        },
        "crazyegg": {
          "enabled": true
        },
        "digioh": {
          "enabled": true
        },
        "disqus": {
          "enabled": true
        },
        "dw": {
          "enabled": true
        },
        "dynamic_widget": {
          "enabled": false
        },
        "dynamic_widget_core": {
          "enabled": true
        },
        "effective_measure": {
          "enabled": true
        },
        "eyeota": {
          "enabled": true
        },
        "facebook": {
          "enabled": true
        },
        "facebookmarketing": {
          "enabled": true
        },
        "finance_source": {
          "enabled": true
        },
        "finance": {
          "enabled": false
        },
        "wellness": {
          "enabled": false
        },
        "home": {
          "enabled": false
        },
        "tech": {
          "enabled": true
        },
        "flickr": {
          "enabled": true
        },
        "gdpr": {
          "enabled": true
        },
        "gifv": {
          "enabled": true
        },
        "google_analytics": {
          "enabled": true
        },
        "googlecsa": {
          "enabled": true
        },
        "googleima": {
          "enabled": true
        },
        "googleremarketing": {
          "enabled": true
        },
        "gpt": {
          "enabled": true
        },
        "google_maps": {
          "enabled": true
        },
        "heartbeat": {
          "enabled": true
        },
        "hgg": {
          "enabled": false
        },
        "hgg_timeframe": {
          "enabled": false
        },
        "indexexchange": {
          "enabled": true
        },
        "imgur": {
          "enabled": true
        },
        "instagram": {
          "enabled": true
        },
        "jsredirects": {
          "enabled": true
        },
        "linkedin": {
          "enabled": true
        },
        "livestream": {
          "enabled": true
        },
        "microsoft_tag_page": {
          "enabled": false
        },
        "mpulse": {
          "enabled": true
        },
        "mwc": {
          "enabled": false
        },
        "newsletter_reengagement": {
          "enabled": true
        },
        "newsletter_reengagement_testa": {
          "enabled": true
        },
        "newsletter_reengagement_testb": {
          "enabled": true
        },
        "nielsen": {
          "enabled": true
        },
        "one_trust": {
          "enabled": true
        },
        "pinterest": {
          "enabled": true
        },
        "playbuzz": {
          "enabled": true
        },
        "precap_listicle": {
          "enabled": false
        },
        "precap_listicle_deals": {
          "enabled": false
        },
        "precap_listicle_versus": {
          "enabled": false
        },
        "qualtrics": {
          "enabled": true
        },
        "recaptcha": {
          "enabled": true
        },
        "reddit": {
          "enabled": true
        },
        "review_pagination": {
          "enabled": false
        },
        "scribd": {
          "enabled": true
        },
        "service_worker": {
          "enabled": true
        },
        "sitecatalyst": {
          "enabled": true
        },
        "skybox": {
          "enabled": true
        },
        "skybox_autoplay": {
          "enabled": false
        },
        "sonobi": {
          "enabled": true
        },
        "soundcloud": {
          "enabled": true
        },
        "stackla": {
          "enabled": true
        },
        "tealium": {
          "enabled": true
        },
        "tealium_collect": {
          "enabled": true
        },
        "trackonomics": {
          "enabled": true
        },
        "trueanthem": {
          "enabled": true
        },
        "twitterwidget": {
          "enabled": true
        },
        "urban_airship": {
          "enabled": true
        },
        "verite": {
          "enabled": true
        },
        "vertical": {
          "enabled": true
        },
        "vertical_galleries": {
          "enabled": false
        },
        "video": {
          "enabled": true
        },
        "viglink": {
          "enabled": true
        },
        "vimeo": {
          "enabled": true
        },
        "vpn": {
          "enabled": false
        },
        "wireless_plan_finder": {
          "enabled": false
        },
        "wireless_feature": {
          "enabled": false
        },
        "precap_as_widget": {
          "enabled": false
        },
        "yahoosyndication": {
          "enabled": true
        },
        "youtube": {
          "enabled": true
        }
      },
      tracking: {
        enabled: true
      },
      txId: '297057b1-98a0-4035-9881-c87035b19ad0',
      user: {
        urls: {
          checkEmail: "/user/authentication/email/",
          forgot: "/user/authentication/forgot/",
          interests: "/user/authentication/interests/",
          login: "/user/authentication/login/",
          newsletters: "/user/authentication/newsletters/",
          modal: "/user/authentication/",
          register: "/user/authentication/register/",
          userData: "/user/get-data/",
          username: "/user/authentication/username/"
        }
      },
      video: {
        playCount: 0,
        prepend: ""
      },
      tagIds: []
    };

  </script>

  <script type="application/javascript">
    window.utag_data = {
      "isEnabled": true,
      "articleAuthorId": ["ec8b2a32-8175-11e2-9d12-0018fe8a00b0"],
      "articleAuthorName": ["scott stein"],
      "articleId": "dfb49c4e-2dd0-4182-890c-37897d913be9",
      "articlePubDate": "2016-05-24 07:00:00-0700",
      "articleTitle": "pebble 2, pebble time 2, pebble core: pebble's fitness hat trick adds heart rate, gps, spotify, and 3g",
      "articleType": "magnet_article",
      "collectionId": [],
      "collectionName": [],
      "deviceType": "desktop",
      "isDev": "0",
      "pageType": "article",
      "siteEdition": "us",
      "siteHier": ["reviews", "tech", "mobile", "smartwatches"],
      "siteSection": "reviews",
      "siteType": "responsive web",
      "topicId": ["********-4277-4785-9963-d0a540c5f9c8"],
      "topicName": ["smartwatches"],
      "dwAccount": "cbsicnetglobalsite",
      "dwHost": "dw.cbsi.com",
      "siteId": 1,
      "reportSuiteId": "cbsicnetglobalsite",
      "bkSiteId": 3321,
      "limit": 4,
      "annotations": ["wearable-tech", "apple", "gps", "samsung", "pebble-2",
        "pebble-time", "storage", "fitbit-inc", "pebble-watch",
        "kickstarter"
      ],
      "brand": "cnet",
      "collectionSlug": [],
      "collectionThreadName": [],
      "contentId": "dfb49c4e-2dd0-4182-890c-37897d913be9",
      "contentType": "article",
      "hasHeroImage": "image",
      "interest": {
        "id": "be4e4302-f313-11e2-8262-0291187b029a_cnet_tag",
        "name": "wearable tech",
        "type": "specific"
      },
      "mfr": ["pebble-technology", "apple", "samsung", "apple", "samsung",
        "fitbit-inc", "spotify", "spotify", "google", "fitbit-inc", "uber",
        "uber", "fossil-inc"
      ],
      "tagSlugs": ["fitness", "spotify"],
      "topicSlugs": ["smartwatches"],
      "siteName": "cnet",
      "tealium": {
        "profile": "cnetglobalsite",
        "env": "prod"
      },
      "secondaryCollectionIds": [],
      "error": "prod",
      "soastaBucket": "",
      "soastaPageType": "article",
      "traffic_source": "cloud"
    };

    window.chsn_pageType = "article";
    window.chsn_post_id = "dfb49c4e-2dd0-4182-890c-37897d913be9";

  </script>

  <script type="application/javascript">
    if (typeof utag_data == "object" && typeof (Storage) !== 'undefined') {
      utag_data.message_id = sessionStorage.getItem('v76_message_id');
      utag_data.contact_id = localStorage.getItem('v77_contact_id');
      utag_data.channel_id = localStorage.getItem('v78_channel_id');
    }

  </script>

  <script type="application/javascript">
    ! function (t, e) {
      "object" == typeof exports && "undefined" != typeof module ? module
        .exports = e() : "function" == typeof define && define.amd ? define(e) :
        (t = t || self).uuidv4 = e()
    }(this, (function () {
      "use strict";
      var t = "undefined" != typeof crypto && crypto.getRandomValues &&
        crypto.getRandomValues.bind(crypto) || "undefined" !=
        typeof msCrypto && "function" == typeof msCrypto.getRandomValues &&
        msCrypto.getRandomValues.bind(msCrypto),
        e = new Uint8Array(16);

      function n() {
        if (!t) throw new Error(
          "crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported"
          );
        return t(e)
      }
      for (var o = [], r = 0; r < 256; ++r) o.push((r + 256).toString(16)
        .substr(1));
      return function (t, e, r) {
        "string" == typeof t && (e = "binary" === t ? new Uint8Array(16) :
          null, t = null);
        var u = (t = t || {}).random || (t.rng || n)();
        if (u[6] = 15 & u[6] | 64, u[8] = 63 & u[8] | 128, e) {
          for (var i = r || 0, d = 0; d < 16; ++d) e[i + d] = u[d];
          return e
        }
        return function (t, e) {
          var n = e || 0,
            r = o;
          return (r[t[n + 0]] + r[t[n + 1]] + r[t[n + 2]] + r[t[n +
            3]] + "-" + r[t[n + 4]] + r[t[n + 5]] + "-" + r[t[n +
            6]] + r[t[n + 7]] + "-" + r[t[n + 8]] + r[t[n + 9]] +
            "-" + r[t[n + 10]] + r[t[n + 11]] + r[t[n + 12]] + r[t[n +
              13]] + r[t[n + 14]] + r[t[n + 15]]).toLowerCase()
        }(u)
      }
    }));
    var initGuid = uuidv4();

    // Update for general use
    window.CnetPageVars.guid = initGuid;

    // Update for ads
    window.CnetPageVars.ads.data.gpt.targeting.vguid = initGuid;

    // Update for Tealium/tracking
    utag_data.pageViewGuid = initGuid;
    window.CnetPageVars.tracking.data = utag_data;

    window.chsn_ad_id = initGuid;

  </script>

  <script type="application/javascript">
    var CnetFunctions = {
      /** Creates a tag with the specified attributes and body, then injects it after the injection point element. */
      appendElement: function (tagName, injectionPoint, attributes, body) {
        var element = document.createElement(tagName);
        for (var attribute in attributes) {
          var value = attributes[attribute];
          if (false !== value && null != value && '' !== value) {
            element[attribute] = attributes[attribute];
          }
        }

        if (null != body) {
          element.innerHTML = body;
        }
        injectionPoint.parentNode.insertBefore(element, injectionPoint
          .nextSibling);
      },

      /** Need a helper function since the native method isn't available in IE. */
      endsWith: function (needle, haystack) {
        return haystack.indexOf(needle) === haystack.length - needle.length;
      },

      /**
          Execute a function as a callback once GDPR consent is granted.

          TODO -  See if we can update this method to prevent the stacking of callbacks. Specifically,
          if this is called on AJAX events, multiple instances of a single service call (like ad refresh)
          could be queued up and all fired at once when the user grants consent. Not currently an issue
          since we're assuming user consent by default, but that could change in the near future.
      */
      gdprConsentCallback: function (id, callback, callbackArgs, category,
        onReady) {
        if ((window.CnetPageVars.services.one_trust && window.CnetPageVars
            .services.one_trust.enabled)) {
          // One trust is enabled setup the functions
          onReady = onReady || 'Scripts';
          var opt = window.cbsoptanon['on' + onReady + 'Ready'];

          function oneTrustLog(message) {
            window.CnetFunctions.logWithLabel('%c One Trust ', message);
          }

          oneTrustLog("Added to " + onReady + " Queue: " + id + ' ' + (
            category == null ? '' : category));

          opt(function (_cbsoptanon, options) {
            //If we have categories we need to check if they are allowed
            if (category != null) {
              //Gets the current state of all categories
              var checkState = function () {
                _cbsoptanon.ot.getState(function (targeting,
                  performance, functional, social) {
                  var obj = {
                    targeting: targeting,
                    performance: performance,
                    functional: functional,
                    social: social
                  };

                  var cat = category.toLowerCase();

                  if (obj[cat]) {
                    oneTrustLog(cat + ' is allowed for ' + id);
                    window.CnetPageVars.tagIds[id] = true;
                    callback.apply(this, [callbackArgs,
                      _cbsoptanon, options
                    ]);
                  }
                });
              };

              checkState();

              _cbsoptanon.ot.addOnConsentChangedHandler(function () {
                if (window.CnetPageVars.tagIds[id] !== true) {
                  checkState();
                }
              });
            } else {
              //Just waiting for on ready fire callback
              oneTrustLog(onReady + " Ready: Loaded " + id);
              callback.apply(this, [callbackArgs, _cbsoptanon,
              options]);
            }
          });
        } else {
          window.CnetFunctions.log(
            "Service loading (GDPR consent already granted): " + id);
          callback.apply(this, callbackArgs);
        }
      },

      /** Get the value of a cookie by name. */
      getCookieValue: function (cookieName) {
        var cookieValue = window.document.cookie.match('(^|;)\\s*' +
          cookieName + '\\s*=\\s*([^;]+)');

        return cookieValue ? cookieValue.pop() : null;
      },

      /** Log to the console w/o exceptions thrown in IE. */
      log: function (message) {
        if (window.console) {
          window.console.log(message);
        }
      },

      /** Log with a label **/
      logWithLabel: function (label, message) {
        if (window.console) {
          window.console.log(label,
            'color:#000;border-radius:3px;background-color:hsl(161, 100%, 94%);',
            message);
        }
      },

      /** Push Cohesion Tagular beam event. */
      pushCohesionBeam(data) {
        if (window.CnetPageVars.services.cohesion && window.CnetPageVars
          .services.cohesion.enabled) {
          if ('function' === typeof window.tagular) {
            window.tagular('beam', data);
          }
        }
      },

      /** Trigger a custom event on the specified element. */
      triggerEvent: function (element, eventName, payload) {
        var event;
        payload = payload || {};

        if ('function' === typeof window.CustomEvent) {
          // Current W3C standard
          event = new CustomEvent(eventName, payload);
          element.dispatchEvent(event);
        } else if ('function' === typeof window.document.createEvent) {
          // Fallback for IE and really old browsers
          event = window.document.createEvent("Event");
          event.initEvent(eventName, true, true);
          event.eventName = eventName;
          element.dispatchEvent(event);
        }
      }
    };

  </script>
  <style type="text/css">
    .preampjs [data-preamp],
    .fusejs [data-fuse] {
      opacity: 0 !important
    }

  </style>
  <script>
  </script>
















  <script type="application/javascript">
    window.BOOMR_config = {
      Errors: {
        enabled: true,
        monitorGlobal: true, // onerror
        monitorNetwork: false, // XHRs
        monitorConsole: true, // window.console.error
        monitorEvents: false, // addEventListener
        monitorTimeout: false, // setTimeout, setInterval
        maxErrors: 10, // max errors sent per page
        onError: function (err) {
          var text = err.message || "";
          return text.indexOf(".cnet.com") !== -1 && //first party
            text.indexOf("Script error") === -1 && //not a script error
            text.indexOf("Load timeout for modules") === -
            1; //not a load timeout
        }
      }
    };

    (function () {
      if (window.BOOMR && window.BOOMR.version) {
        return;
      }
      var dom, doc, where, iframe = document.createElement("iframe"),
        win = window;

      function boomerangSaveLoadTime(e) {
        win.BOOMR_onload = (e && e.timeStamp) || new Date().getTime();
      }
      if (win.addEventListener) {
        win.addEventListener("load", boomerangSaveLoadTime, false);
      } else if (win.attachEvent) {
        win.attachEvent("onload", boomerangSaveLoadTime);
      }

      iframe.src = "javascript:void(0)";
      iframe.title = "";
      iframe.role = "presentation";
      (iframe.frameElement || iframe).style.cssText =
        "width:0;height:0;border:0;display:none;";
      where = document.getElementsByTagName("script")[0];
      where.parentNode.insertBefore(iframe, where);

      try {
        doc = iframe.contentWindow.document;
      } catch (e) {
        dom = document.domain;
        iframe.src = "javascript:var d=document.open();d.domain='" + dom +
          "';void(0);";
        doc = iframe.contentWindow.document;
      }
      doc.open()._l = function () {
        var js = this.createElement("script");
        if (dom) {
          this.domain = dom;
        }
        js.id = "boomr-if-as";
        js.src =
        "//c.go-mpulse.net/boomerang/38QDY-8CT77-8XNH2-VJQTD-EK4YX";
        BOOMR_lstart = new Date().getTime();
        this.body.appendChild(js);
      };
      doc.write('<body onload="document._l();">');
      doc.close();
    })();

  </script>





  <script type="application/javascript">
    var _sf_async_config = _sf_async_config || {};
    _sf_async_config.uid = 66142;
    _sf_async_config.domain = "cnet.com";
    _sf_async_config.flickerControl = false;
    _sf_async_config.useCanonical = true;
    _sf_async_config.autoDetect = false;
    _sf_async_config.sections = "reviews,tech";
    _sf_async_config.authors = "scott stein";
    _sf_async_config.contentType = "magnet_article";
    var _sf_startpt = (new Date()).getTime();
    window._cbq = (window._cbq || []);

  </script>



  <script type="application/javascript">
    (function () {
      if ('object' === typeof window.navigator.serviceWorker) {
        if (window.CnetPageVars.services.service_worker && window.CnetPageVars
          .services.service_worker.enabled) {
          // Register the global service worker here; others are registered by their respective managers.
          window.navigator.serviceWorker.register('/service-worker.js').then(
            function (registration) {
              window.console.info(
                'Registration of service worker /service-worker.js successful with scope:' +
                registration.scope);
            },
            function (e) {
              window.console.error(
                'Registration of service worker /service-worker.js failed:',
                e);
            });
        }

        window.knownServiceWorkers = {};
        window.knownServiceWorkers[window.location.origin +
          "/service-worker.js"] = true;
        window.knownServiceWorkers[window.location.origin +
          "/bundles/cnetjs/js/libs/ua-push-worker.js"] = true;

        try {
          // This is experimental but supported by Chrome, Firefox, Edge, and Opera
          window.navigator.serviceWorker.getRegistrations().then(function (
            registrations) {
            registrations.forEach(function (registration) {
              var statusObject = registration.active || registration
                .installing || registration.waiting;
              window.console.log('Found registered service worker:',
                registration);

              // Unregister any service worker that has not been explicitly allowed
              if (null != statusObject && !knownServiceWorkers[
                  statusObject.scriptURL]) {
                window.console.info('Unregistering service worker:',
                  registration);
                registration.unregister();
              }
            });
          });
        } catch (e) {
          // Safari doesn't support fetching all registered SWs, so only known registrations can be explicitly unregistered.
          Object.keys(knownServiceWorkers).forEach(function (url) {
            if (!knownServiceWorkers[url]) {
              window.navigator.serviceWorker.getRegistration(url).then(
                function (registration) {
                  window.console.info('Unregistering service worker:',
                    registration);
                  registration.unregister();
                }).catch(function (e) {
                window.console.error(
                  'An error occurred while unregistering a service worker:',
                  e);
              });
            }
          });
        }
      } else if ('object' === typeof window.console && 'function' ===
        typeof window.console.warn) {
        // Don't break IE by calling this without checking the console exists first.
        window.console.warn(
          'Service workers are not supported by this browser.');
      }
    })();

  </script>

  <script type="application/javascript">
    window.BidBarrel = window.BidBarrel || {};
    window.BidBarrel.queue = window.BidBarrel.queue || [];

  </script>



  <script type="application/javascript">
    (function () {
      ! function (t, e) {
        "object" == typeof exports && "undefined" != typeof module ? module
          .exports = e() : "function" == typeof define && define.amd ? define(
            e) : (t = t || self).uuidv4 = e()
      }(this, (function () {
        "use strict";
        var t = "undefined" != typeof crypto && crypto.getRandomValues &&
          crypto.getRandomValues.bind(crypto) || "undefined" !=
          typeof msCrypto && "function" == typeof msCrypto
          .getRandomValues && msCrypto.getRandomValues.bind(msCrypto),
          e = new Uint8Array(16);

        function n() {
          if (!t) throw new Error(
            "crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported"
            );
          return t(e)
        }
        for (var o = [], r = 0; r < 256; ++r) o.push((r + 256).toString(
          16).substr(1));
        return function (t, e, r) {
          "string" == typeof t && (e = "binary" === t ? new Uint8Array(
            16) : null, t = null);
          var u = (t = t || {}).random || (t.rng || n)();
          if (u[6] = 15 & u[6] | 64, u[8] = 63 & u[8] | 128, e) {
            for (var i = r || 0, d = 0; d < 16; ++d) e[i + d] = u[d];
            return e
          }
          return function (t, e) {
            var n = e || 0,
              r = o;
            return (r[t[n + 0]] + r[t[n + 1]] + r[t[n + 2]] + r[t[n +
                3]] + "-" + r[t[n + 4]] + r[t[n + 5]] + "-" + r[t[
                n + 6]] + r[t[n + 7]] + "-" + r[t[n + 8]] + r[t[n +
                9]] + "-" + r[t[n + 10]] + r[t[n + 11]] + r[t[n +
                12]] + r[t[n + 13]] + r[t[n + 14]] + r[t[n + 15]])
              .toLowerCase()
          }(u)
        }
      }));

      function injectClickId(target) {
        const clickId = uuidv4().replaceAll('-', '');

        target.setAttribute('href', target.href.replace("___COM_CLICK_ID___",
          clickId));

        window.CnetFunctions.pushCohesionBeam({
          '@type': 'redventures.ecommerce.v1.ProductClicked',
          'product': {
            'pageId': window ? .CnetPageVars ? .tracking ? .data ?
              .articleId ? window.CnetPageVars.tracking.data.articleId :
              ''
          },
          'correlationId': clickId
        });

        setTimeout(function () {
          target.setAttribute('href', target.href.replace(clickId,
            "___COM_CLICK_ID___"));
        }, 500);
      }

      document.addEventListener('DOMContentLoaded', function () {
        if (!window.leadsTracker) {
          let elements = document.querySelectorAll(
            'a[data-component=leadsTracker]');
          for (element of elements) {
            element.addEventListener('mousedown', event => {
              if (!window.leadsTracker) {
                let target = event.target.closest(
                  'a[data-component=leadsTracker]');
                if (target) {
                  injectClickId(target);
                }
              }
            });
          }
        }

        if (!window.externalLink) {
          let elements = document.querySelectorAll(
            'a[data-component=externalLink]');
          for (element of elements) {
            element.addEventListener('mousedown', event => {
              if (!window.externalLink) {
                let target = event.target.closest(
                  'a[data-component=externalLink]');
                if (target) {
                  injectClickId(target);
                }
              }
            });
          }
        }
      });
    })();

  </script>
  <script id="script_core_web_vitals_polyfill">
    ! function () {
      var e, t, n, i, r = {
          passive: !0,
          capture: !0
        },
        a = new Date,
        o = function () {
          i = [], t = -1, e = null, f(addEventListener)
        },
        c = function (i, r) {
          e || (e = r, t = i, n = new Date, f(removeEventListener), u())
        },
        u = function () {
          if (t >= 0 && t < n - a) {
            var r = {
              entryType: "first-input",
              name: e.type,
              target: e.target,
              cancelable: e.cancelable,
              startTime: e.timeStamp,
              processingStart: e.timeStamp + t
            };
            i.forEach((function (e) {
              e(r)
            })), i = []
          }
        },
        s = function (e) {
          if (e.cancelable) {
            var t = (e.timeStamp > 1e12 ? new Date : performance.now()) - e
              .timeStamp;
            "pointerdown" == e.type ? function (e, t) {
              var n = function () {
                  c(e, t), a()
                },
                i = function () {
                  a()
                },
                a = function () {
                  removeEventListener("pointerup", n, r), removeEventListener(
                    "pointercancel", i, r)
                };
              addEventListener("pointerup", n, r), addEventListener(
                "pointercancel", i, r)
            }(t, e) : c(t, e)
          }
        },
        f = function (e) {
          ["mousedown", "keydown", "touchstart", "pointerdown"].forEach((
            function (t) {
              return e(t, s, r)
            }))
        },
        p = "hidden" === document.visibilityState ? 0 : 1 / 0;
      addEventListener("visibilitychange", (function e(t) {
        "hidden" === document.visibilityState && (p = t.timeStamp,
          removeEventListener("visibilitychange", e, !0))
      }), !0);
      o(), self.webVitals = {
        firstInputPolyfill: function (e) {
          i.push(e), u()
        },
        resetFirstInputPolyfill: o,
        get firstHiddenTime() {
          return p
        }
      }
    }();

  </script>

  <script
    src="https://geolocation.onetrust.com/cookieconsentpub/v1/geo/location"
    async="" type="text/javascript"></script>
  <script type="text/javascript" charset="utf-8" async=""
    data-requirecontext="_" data-requiremodule="main.default"
    src="https://www.cnet.com/a/fly/a28c66-fly/js/main.default.js"></script>
  <script src="https://cdn.cookielaw.org/scripttemplates/6.7.0/otBannerSdk.js"
    async="" type="text/javascript"></script>
  <script type="text/javascript" charset="utf-8" async=""
    data-requirecontext="_"
    data-requiremodule="//tags.tiqcdn.com/utag/redventures/cnetglobalsite/prod/utag.js"
    src="//tags.tiqcdn.com/utag/redventures/cnetglobalsite/prod/utag.js">
  </script>
  <script type="text/javascript" charset="utf-8" async=""
    data-requirecontext="_"
    data-requiremodule="https://at.adtech.redventures.io/lib/dist/prod/bidbarrel-cnet-rv.min.js"
    src="https://at.adtech.redventures.io/lib/dist/prod/bidbarrel-cnet-rv.min.js">
  </script>
  <script type="text/javascript" charset="utf-8" async=""
    data-requirecontext="_" data-requiremodule="libs/mpulse"
    src="https://www.cnet.com/a/fly/js/libs/mpulse.js"></script>
  <script type="text/javascript" charset="utf-8" async=""
    data-requirecontext="_"
    data-requiremodule="pages/desktop/article_video_test"
    src="https://www.cnet.com/a/fly/js/pages/desktop/article_video_test-1228104946-rev.js">
  </script>
  <style type="text/css" id="onetrust-style">
    #onetrust-banner-sdk {
      -ms-text-size-adjust: 100%;
      -webkit-text-size-adjust: 100%
    }

    #onetrust-banner-sdk .onetrust-vendors-list-handler {
      cursor: pointer;
      color: #1f96db;
      font-size: inherit;
      font-weight: bold;
      text-decoration: none;
      margin-left: 5px
    }

    #onetrust-banner-sdk .onetrust-vendors-list-handler:hover {
      color: #1f96db
    }

    #onetrust-banner-sdk .ot-close-icon,
    #onetrust-pc-sdk .ot-close-icon {
      background-image: url("data:image/svg+xml;base64,PHN2ZyB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHg9IjBweCIgeT0iMHB4IiB3aWR0aD0iMzQ4LjMzM3B4IiBoZWlnaHQ9IjM0OC4zMzNweCIgdmlld0JveD0iMCAwIDM0OC4zMzMgMzQ4LjMzNCIgc3R5bGU9ImVuYWJsZS1iYWNrZ3JvdW5kOm5ldyAwIDAgMzQ4LjMzMyAzNDguMzM0OyIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+PGc+PHBhdGggZmlsbD0iIzU2NTY1NiIgZD0iTTMzNi41NTksNjguNjExTDIzMS4wMTYsMTc0LjE2NWwxMDUuNTQzLDEwNS41NDljMTUuNjk5LDE1LjcwNSwxNS42OTksNDEuMTQ1LDAsNTYuODVjLTcuODQ0LDcuODQ0LTE4LjEyOCwxMS43NjktMjguNDA3LDExLjc2OWMtMTAuMjk2LDAtMjAuNTgxLTMuOTE5LTI4LjQxOS0xMS43NjlMMTc0LjE2NywyMzEuMDAzTDY4LjYwOSwzMzYuNTYzYy03Ljg0Myw3Ljg0NC0xOC4xMjgsMTEuNzY5LTI4LjQxNiwxMS43NjljLTEwLjI4NSwwLTIwLjU2My0zLjkxOS0yOC40MTMtMTEuNzY5Yy0xNS42OTktMTUuNjk4LTE1LjY5OS00MS4xMzksMC01Ni44NWwxMDUuNTQtMTA1LjU0OUwxMS43NzQsNjguNjExYy0xNS42OTktMTUuNjk5LTE1LjY5OS00MS4xNDUsMC01Ni44NDRjMTUuNjk2LTE1LjY4Nyw0MS4xMjctMTUuNjg3LDU2LjgyOSwwbDEwNS41NjMsMTA1LjU1NEwyNzkuNzIxLDExLjc2N2MxNS43MDUtMTUuNjg3LDQxLjEzOS0xNS42ODcsNTYuODMyLDBDMzUyLjI1OCwyNy40NjYsMzUyLjI1OCw1Mi45MTIsMzM2LjU1OSw2OC42MTF6Ii8+PC9nPjwvc3ZnPg==");
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
      height: 12px;
      width: 12px
    }

    #onetrust-banner-sdk .powered-by-logo,
    #onetrust-banner-sdk .ot-pc-footer-logo a,
    #onetrust-pc-sdk .powered-by-logo,
    #onetrust-pc-sdk .ot-pc-footer-logo a {
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
      height: 25px;
      width: 152px;
      display: block
    }

    #onetrust-banner-sdk h3 *,
    #onetrust-banner-sdk h4 *,
    #onetrust-banner-sdk h6 *,
    #onetrust-banner-sdk button *,
    #onetrust-banner-sdk a[data-parent-id] *,
    #onetrust-pc-sdk h3 *,
    #onetrust-pc-sdk h4 *,
    #onetrust-pc-sdk h6 *,
    #onetrust-pc-sdk button *,
    #onetrust-pc-sdk a[data-parent-id] * {
      font-size: inherit;
      font-weight: inherit;
      color: inherit
    }

    #onetrust-banner-sdk .ot-hide,
    #onetrust-pc-sdk .ot-hide {
      display: none !important
    }

    #onetrust-pc-sdk .ot-sdk-row .ot-sdk-column {
      padding: 0
    }

    #onetrust-pc-sdk .ot-sdk-container {
      padding-right: 0
    }

    #onetrust-pc-sdk .ot-sdk-row {
      flex-direction: initial;
      width: 100%
    }

    #onetrust-pc-sdk [type="checkbox"]:checked,
    #onetrust-pc-sdk [type="checkbox"]:not(:checked) {
      pointer-events: initial
    }

    #onetrust-pc-sdk [type="checkbox"]:disabled+label::before,
    #onetrust-pc-sdk [type="checkbox"]:disabled+label:after,
    #onetrust-pc-sdk [type="checkbox"]:disabled+label {
      pointer-events: none;
      opacity: 0.7
    }

    #onetrust-pc-sdk #vendor-list-content {
      transform: translate3d(0, 0, 0)
    }

    #onetrust-pc-sdk li input[type="checkbox"] {
      z-index: 1
    }

    #onetrust-pc-sdk li .ot-checkbox label {
      z-index: 2
    }

    #onetrust-pc-sdk li .ot-checkbox input[type="checkbox"] {
      height: auto;
      width: auto
    }

    #onetrust-pc-sdk li .host-title a,
    #onetrust-pc-sdk li .ot-host-name a,
    #onetrust-pc-sdk li .accordion-text,
    #onetrust-pc-sdk li .ot-acc-txt {
      z-index: 2;
      position: relative
    }

    #onetrust-pc-sdk input {
      margin: 3px 0.1ex
    }

    #onetrust-pc-sdk .toggle-always-active {
      opacity: 0.6;
      cursor: default
    }

    #onetrust-pc-sdk .screen-reader-only,
    #onetrust-pc-sdk .ot-scrn-rdr {
      border: 0;
      clip: rect(0 0 0 0);
      height: 1px;
      margin: -1px;
      overflow: hidden;
      padding: 0;
      position: absolute;
      width: 1px
    }

    #onetrust-pc-sdk .pc-logo,
    #onetrust-pc-sdk .ot-pc-logo {
      height: 60px;
      width: 180px;
      background-position: center;
      background-size: contain;
      background-repeat: no-repeat
    }

    #onetrust-pc-sdk .ot-tooltip .ot-tooltiptext {
      visibility: hidden;
      width: 120px;
      background-color: #555;
      color: #fff;
      text-align: center;
      padding: 5px 0;
      border-radius: 6px;
      position: absolute;
      z-index: 1;
      bottom: 125%;
      left: 50%;
      margin-left: -60px;
      opacity: 0;
      transition: opacity 0.3s
    }

    #onetrust-pc-sdk .ot-tooltip .ot-tooltiptext::after {
      content: "";
      position: absolute;
      top: 100%;
      left: 50%;
      margin-left: -5px;
      border-width: 5px;
      border-style: solid;
      border-color: #555 transparent transparent transparent
    }

    #onetrust-pc-sdk .ot-tooltip:hover .ot-tooltiptext {
      visibility: visible;
      opacity: 1
    }

    #onetrust-pc-sdk .ot-tooltip {
      position: relative;
      display: inline-block;
      z-index: 3
    }

    #onetrust-pc-sdk .ot-tooltip svg {
      color: grey;
      height: 20px;
      width: 20px
    }

    #onetrust-pc-sdk.ot-fade-in,
    .onetrust-pc-dark-filter.ot-fade-in {
      animation-name: onetrust-fade-in;
      animation-duration: 400ms;
      animation-timing-function: ease-in-out
    }

    #onetrust-pc-sdk.ot-hide {
      display: none !important
    }

    .onetrust-pc-dark-filter.ot-hide {
      display: none !important
    }

    #ot-sdk-btn.ot-sdk-show-settings,
    #ot-sdk-btn.optanon-show-settings {
      color: #68b631;
      border: 1px solid #68b631;
      height: auto;
      white-space: normal;
      word-wrap: break-word;
      padding: 0.8em 2em;
      font-size: 0.8em;
      line-height: 1.2;
      cursor: pointer;
      -moz-transition: 0.1s ease;
      -o-transition: 0.1s ease;
      -webkit-transition: 1s ease;
      transition: 0.1s ease
    }

    #ot-sdk-btn.ot-sdk-show-settings:hover,
    #ot-sdk-btn.optanon-show-settings:hover {
      color: #fff;
      background-color: #68b631
    }

    #ot-sdk-btn.ot-sdk-show-settings:focus,
    #ot-sdk-btn.optanon-show-settings:focus {
      outline: none
    }

    .onetrust-pc-dark-filter {
      background: rgba(0, 0, 0, 0.5);
      z-index: 2147483646;
      width: 100%;
      height: 100%;
      overflow: hidden;
      position: fixed;
      top: 0;
      bottom: 0;
      left: 0
    }

    @keyframes onetrust-fade-in {
      0% {
        opacity: 0
      }

      100% {
        opacity: 1
      }
    }

    @media only screen and (min-width: 426px) and (max-width: 896px) and (orientation: landscape) {
      #onetrust-pc-sdk p {
        font-size: 0.75em
      }
    }

    #onetrust-banner-sdk,
    #onetrust-pc-sdk,
    #ot-sdk-cookie-policy {
      font-size: 16px
    }

    #onetrust-banner-sdk *,
    #onetrust-banner-sdk ::after,
    #onetrust-banner-sdk ::before,
    #onetrust-pc-sdk *,
    #onetrust-pc-sdk ::after,
    #onetrust-pc-sdk ::before,
    #ot-sdk-cookie-policy *,
    #ot-sdk-cookie-policy ::after,
    #ot-sdk-cookie-policy ::before {
      -webkit-box-sizing: content-box;
      -moz-box-sizing: content-box;
      box-sizing: content-box
    }

    #onetrust-banner-sdk div,
    #onetrust-banner-sdk span,
    #onetrust-banner-sdk h1,
    #onetrust-banner-sdk h2,
    #onetrust-banner-sdk h3,
    #onetrust-banner-sdk h4,
    #onetrust-banner-sdk h5,
    #onetrust-banner-sdk h6,
    #onetrust-banner-sdk p,
    #onetrust-banner-sdk img,
    #onetrust-banner-sdk svg,
    #onetrust-banner-sdk button,
    #onetrust-banner-sdk section,
    #onetrust-banner-sdk a,
    #onetrust-banner-sdk label,
    #onetrust-banner-sdk input,
    #onetrust-banner-sdk ul,
    #onetrust-banner-sdk li,
    #onetrust-banner-sdk nav,
    #onetrust-banner-sdk table,
    #onetrust-banner-sdk thead,
    #onetrust-banner-sdk tr,
    #onetrust-banner-sdk td,
    #onetrust-banner-sdk tbody,
    #onetrust-banner-sdk .ot-main-content,
    #onetrust-banner-sdk .ot-toggle,
    #onetrust-banner-sdk #ot-content,
    #onetrust-banner-sdk #ot-pc-content,
    #onetrust-banner-sdk .checkbox,
    #onetrust-pc-sdk div,
    #onetrust-pc-sdk span,
    #onetrust-pc-sdk h1,
    #onetrust-pc-sdk h2,
    #onetrust-pc-sdk h3,
    #onetrust-pc-sdk h4,
    #onetrust-pc-sdk h5,
    #onetrust-pc-sdk h6,
    #onetrust-pc-sdk p,
    #onetrust-pc-sdk img,
    #onetrust-pc-sdk svg,
    #onetrust-pc-sdk button,
    #onetrust-pc-sdk section,
    #onetrust-pc-sdk a,
    #onetrust-pc-sdk label,
    #onetrust-pc-sdk input,
    #onetrust-pc-sdk ul,
    #onetrust-pc-sdk li,
    #onetrust-pc-sdk nav,
    #onetrust-pc-sdk table,
    #onetrust-pc-sdk thead,
    #onetrust-pc-sdk tr,
    #onetrust-pc-sdk td,
    #onetrust-pc-sdk tbody,
    #onetrust-pc-sdk .ot-main-content,
    #onetrust-pc-sdk .ot-toggle,
    #onetrust-pc-sdk #ot-content,
    #onetrust-pc-sdk #ot-pc-content,
    #onetrust-pc-sdk .checkbox,
    #ot-sdk-cookie-policy div,
    #ot-sdk-cookie-policy span,
    #ot-sdk-cookie-policy h1,
    #ot-sdk-cookie-policy h2,
    #ot-sdk-cookie-policy h3,
    #ot-sdk-cookie-policy h4,
    #ot-sdk-cookie-policy h5,
    #ot-sdk-cookie-policy h6,
    #ot-sdk-cookie-policy p,
    #ot-sdk-cookie-policy img,
    #ot-sdk-cookie-policy svg,
    #ot-sdk-cookie-policy button,
    #ot-sdk-cookie-policy section,
    #ot-sdk-cookie-policy a,
    #ot-sdk-cookie-policy label,
    #ot-sdk-cookie-policy input,
    #ot-sdk-cookie-policy ul,
    #ot-sdk-cookie-policy li,
    #ot-sdk-cookie-policy nav,
    #ot-sdk-cookie-policy table,
    #ot-sdk-cookie-policy thead,
    #ot-sdk-cookie-policy tr,
    #ot-sdk-cookie-policy td,
    #ot-sdk-cookie-policy tbody,
    #ot-sdk-cookie-policy .ot-main-content,
    #ot-sdk-cookie-policy .ot-toggle,
    #ot-sdk-cookie-policy #ot-content,
    #ot-sdk-cookie-policy #ot-pc-content,
    #ot-sdk-cookie-policy .checkbox {
      font-family: inherit;
      font-weight: normal;
      -webkit-font-smoothing: auto;
      letter-spacing: normal;
      line-height: normal;
      padding: 0;
      margin: 0;
      height: auto;
      min-height: 0;
      max-height: none;
      width: auto;
      min-width: 0;
      max-width: none;
      border-radius: 0;
      border: none;
      clear: none;
      float: none;
      position: static;
      bottom: auto;
      left: auto;
      right: auto;
      top: auto;
      text-align: left;
      text-decoration: none;
      text-indent: 0;
      text-shadow: none;
      text-transform: none;
      white-space: normal;
      background: none;
      overflow: visible;
      vertical-align: baseline;
      visibility: visible;
      z-index: auto;
      box-shadow: none
    }

    #onetrust-banner-sdk label:before,
    #onetrust-banner-sdk label:after,
    #onetrust-banner-sdk .checkbox:after,
    #onetrust-banner-sdk .checkbox:before,
    #onetrust-pc-sdk label:before,
    #onetrust-pc-sdk label:after,
    #onetrust-pc-sdk .checkbox:after,
    #onetrust-pc-sdk .checkbox:before,
    #ot-sdk-cookie-policy label:before,
    #ot-sdk-cookie-policy label:after,
    #ot-sdk-cookie-policy .checkbox:after,
    #ot-sdk-cookie-policy .checkbox:before {
      content: "";
      content: none
    }

    #onetrust-banner-sdk .ot-sdk-container,
    #onetrust-pc-sdk .ot-sdk-container,
    #ot-sdk-cookie-policy .ot-sdk-container {
      position: relative;
      width: 100%;
      max-width: 100%;
      margin: 0 auto;
      padding: 0 20px;
      box-sizing: border-box
    }

    #onetrust-banner-sdk .ot-sdk-column,
    #onetrust-banner-sdk .ot-sdk-columns,
    #onetrust-pc-sdk .ot-sdk-column,
    #onetrust-pc-sdk .ot-sdk-columns,
    #ot-sdk-cookie-policy .ot-sdk-column,
    #ot-sdk-cookie-policy .ot-sdk-columns {
      width: 100%;
      float: left;
      box-sizing: border-box;
      padding: 0;
      display: initial
    }

    @media (min-width: 400px) {

      #onetrust-banner-sdk .ot-sdk-container,
      #onetrust-pc-sdk .ot-sdk-container,
      #ot-sdk-cookie-policy .ot-sdk-container {
        width: 90%;
        padding: 0
      }
    }

    @media (min-width: 550px) {

      #onetrust-banner-sdk .ot-sdk-container,
      #onetrust-pc-sdk .ot-sdk-container,
      #ot-sdk-cookie-policy .ot-sdk-container {
        width: 100%
      }

      #onetrust-banner-sdk .ot-sdk-column,
      #onetrust-banner-sdk .ot-sdk-columns,
      #onetrust-pc-sdk .ot-sdk-column,
      #onetrust-pc-sdk .ot-sdk-columns,
      #ot-sdk-cookie-policy .ot-sdk-column,
      #ot-sdk-cookie-policy .ot-sdk-columns {
        margin-left: 4%
      }

      #onetrust-banner-sdk .ot-sdk-column:first-child,
      #onetrust-banner-sdk .ot-sdk-columns:first-child,
      #onetrust-pc-sdk .ot-sdk-column:first-child,
      #onetrust-pc-sdk .ot-sdk-columns:first-child,
      #ot-sdk-cookie-policy .ot-sdk-column:first-child,
      #ot-sdk-cookie-policy .ot-sdk-columns:first-child {
        margin-left: 0
      }

      #onetrust-banner-sdk .ot-sdk-one.ot-sdk-column,
      #onetrust-banner-sdk .ot-sdk-one.ot-sdk-columns,
      #onetrust-pc-sdk .ot-sdk-one.ot-sdk-column,
      #onetrust-pc-sdk .ot-sdk-one.ot-sdk-columns,
      #ot-sdk-cookie-policy .ot-sdk-one.ot-sdk-column,
      #ot-sdk-cookie-policy .ot-sdk-one.ot-sdk-columns {
        width: 4.66666666667%
      }

      #onetrust-banner-sdk .ot-sdk-two.ot-sdk-columns,
      #onetrust-pc-sdk .ot-sdk-two.ot-sdk-columns,
      #ot-sdk-cookie-policy .ot-sdk-two.ot-sdk-columns {
        width: 13.3333333333%
      }

      #onetrust-banner-sdk .ot-sdk-three.ot-sdk-columns,
      #onetrust-pc-sdk .ot-sdk-three.ot-sdk-columns,
      #ot-sdk-cookie-policy .ot-sdk-three.ot-sdk-columns {
        width: 22%
      }

      #onetrust-banner-sdk .ot-sdk-four.ot-sdk-columns,
      #onetrust-pc-sdk .ot-sdk-four.ot-sdk-columns,
      #ot-sdk-cookie-policy .ot-sdk-four.ot-sdk-columns {
        width: 30.6666666667%
      }

      #onetrust-banner-sdk .ot-sdk-five.ot-sdk-columns,
      #onetrust-pc-sdk .ot-sdk-five.ot-sdk-columns,
      #ot-sdk-cookie-policy .ot-sdk-five.ot-sdk-columns {
        width: 39.3333333333%
      }

      #onetrust-banner-sdk .ot-sdk-six.ot-sdk-columns,
      #onetrust-pc-sdk .ot-sdk-six.ot-sdk-columns,
      #ot-sdk-cookie-policy .ot-sdk-six.ot-sdk-columns {
        width: 48%
      }

      #onetrust-banner-sdk .ot-sdk-seven.ot-sdk-columns,
      #onetrust-pc-sdk .ot-sdk-seven.ot-sdk-columns,
      #ot-sdk-cookie-policy .ot-sdk-seven.ot-sdk-columns {
        width: 56.6666666667%
      }

      #onetrust-banner-sdk .ot-sdk-eight.ot-sdk-columns,
      #onetrust-pc-sdk .ot-sdk-eight.ot-sdk-columns,
      #ot-sdk-cookie-policy .ot-sdk-eight.ot-sdk-columns {
        width: 65.3333333333%
      }

      #onetrust-banner-sdk .ot-sdk-nine.ot-sdk-columns,
      #onetrust-pc-sdk .ot-sdk-nine.ot-sdk-columns,
      #ot-sdk-cookie-policy .ot-sdk-nine.ot-sdk-columns {
        width: 74%
      }

      #onetrust-banner-sdk .ot-sdk-ten.ot-sdk-columns,
      #onetrust-pc-sdk .ot-sdk-ten.ot-sdk-columns,
      #ot-sdk-cookie-policy .ot-sdk-ten.ot-sdk-columns {
        width: 82.6666666667%
      }

      #onetrust-banner-sdk .ot-sdk-eleven.ot-sdk-columns,
      #onetrust-pc-sdk .ot-sdk-eleven.ot-sdk-columns,
      #ot-sdk-cookie-policy .ot-sdk-eleven.ot-sdk-columns {
        width: 91.3333333333%
      }

      #onetrust-banner-sdk .ot-sdk-twelve.ot-sdk-columns,
      #onetrust-pc-sdk .ot-sdk-twelve.ot-sdk-columns,
      #ot-sdk-cookie-policy .ot-sdk-twelve.ot-sdk-columns {
        width: 100%;
        margin-left: 0
      }

      #onetrust-banner-sdk .ot-sdk-one-third.ot-sdk-column,
      #onetrust-pc-sdk .ot-sdk-one-third.ot-sdk-column,
      #ot-sdk-cookie-policy .ot-sdk-one-third.ot-sdk-column {
        width: 30.6666666667%
      }

      #onetrust-banner-sdk .ot-sdk-two-thirds.ot-sdk-column,
      #onetrust-pc-sdk .ot-sdk-two-thirds.ot-sdk-column,
      #ot-sdk-cookie-policy .ot-sdk-two-thirds.ot-sdk-column {
        width: 65.3333333333%
      }

      #onetrust-banner-sdk .ot-sdk-one-half.ot-sdk-column,
      #onetrust-pc-sdk .ot-sdk-one-half.ot-sdk-column,
      #ot-sdk-cookie-policy .ot-sdk-one-half.ot-sdk-column {
        width: 48%
      }

      #onetrust-banner-sdk .ot-sdk-offset-by-one.ot-sdk-column,
      #onetrust-banner-sdk .ot-sdk-offset-by-one.ot-sdk-columns,
      #onetrust-pc-sdk .ot-sdk-offset-by-one.ot-sdk-column,
      #onetrust-pc-sdk .ot-sdk-offset-by-one.ot-sdk-columns,
      #ot-sdk-cookie-policy .ot-sdk-offset-by-one.ot-sdk-column,
      #ot-sdk-cookie-policy .ot-sdk-offset-by-one.ot-sdk-columns {
        margin-left: 8.66666666667%
      }

      #onetrust-banner-sdk .ot-sdk-offset-by-two.ot-sdk-column,
      #onetrust-banner-sdk .ot-sdk-offset-by-two.ot-sdk-columns,
      #onetrust-pc-sdk .ot-sdk-offset-by-two.ot-sdk-column,
      #onetrust-pc-sdk .ot-sdk-offset-by-two.ot-sdk-columns,
      #ot-sdk-cookie-policy .ot-sdk-offset-by-two.ot-sdk-column,
      #ot-sdk-cookie-policy .ot-sdk-offset-by-two.ot-sdk-columns {
        margin-left: 17.3333333333%
      }

      #onetrust-banner-sdk .ot-sdk-offset-by-three.ot-sdk-column,
      #onetrust-banner-sdk .ot-sdk-offset-by-three.ot-sdk-columns,
      #onetrust-pc-sdk .ot-sdk-offset-by-three.ot-sdk-column,
      #onetrust-pc-sdk .ot-sdk-offset-by-three.ot-sdk-columns,
      #ot-sdk-cookie-policy .ot-sdk-offset-by-three.ot-sdk-column,
      #ot-sdk-cookie-policy .ot-sdk-offset-by-three.ot-sdk-columns {
        margin-left: 26%
      }

      #onetrust-banner-sdk .ot-sdk-offset-by-four.ot-sdk-column,
      #onetrust-banner-sdk .ot-sdk-offset-by-four.ot-sdk-columns,
      #onetrust-pc-sdk .ot-sdk-offset-by-four.ot-sdk-column,
      #onetrust-pc-sdk .ot-sdk-offset-by-four.ot-sdk-columns,
      #ot-sdk-cookie-policy .ot-sdk-offset-by-four.ot-sdk-column,
      #ot-sdk-cookie-policy .ot-sdk-offset-by-four.ot-sdk-columns {
        margin-left: 34.6666666667%
      }

      #onetrust-banner-sdk .ot-sdk-offset-by-five.ot-sdk-column,
      #onetrust-banner-sdk .ot-sdk-offset-by-five.ot-sdk-columns,
      #onetrust-pc-sdk .ot-sdk-offset-by-five.ot-sdk-column,
      #onetrust-pc-sdk .ot-sdk-offset-by-five.ot-sdk-columns,
      #ot-sdk-cookie-policy .ot-sdk-offset-by-five.ot-sdk-column,
      #ot-sdk-cookie-policy .ot-sdk-offset-by-five.ot-sdk-columns {
        margin-left: 43.3333333333%
      }

      #onetrust-banner-sdk .ot-sdk-offset-by-six.ot-sdk-column,
      #onetrust-banner-sdk .ot-sdk-offset-by-six.ot-sdk-columns,
      #onetrust-pc-sdk .ot-sdk-offset-by-six.ot-sdk-column,
      #onetrust-pc-sdk .ot-sdk-offset-by-six.ot-sdk-columns,
      #ot-sdk-cookie-policy .ot-sdk-offset-by-six.ot-sdk-column,
      #ot-sdk-cookie-policy .ot-sdk-offset-by-six.ot-sdk-columns {
        margin-left: 52%
      }

      #onetrust-banner-sdk .ot-sdk-offset-by-seven.ot-sdk-column,
      #onetrust-banner-sdk .ot-sdk-offset-by-seven.ot-sdk-columns,
      #onetrust-pc-sdk .ot-sdk-offset-by-seven.ot-sdk-column,
      #onetrust-pc-sdk .ot-sdk-offset-by-seven.ot-sdk-columns,
      #ot-sdk-cookie-policy .ot-sdk-offset-by-seven.ot-sdk-column,
      #ot-sdk-cookie-policy .ot-sdk-offset-by-seven.ot-sdk-columns {
        margin-left: 60.6666666667%
      }

      #onetrust-banner-sdk .ot-sdk-offset-by-eight.ot-sdk-column,
      #onetrust-banner-sdk .ot-sdk-offset-by-eight.ot-sdk-columns,
      #onetrust-pc-sdk .ot-sdk-offset-by-eight.ot-sdk-column,
      #onetrust-pc-sdk .ot-sdk-offset-by-eight.ot-sdk-columns,
      #ot-sdk-cookie-policy .ot-sdk-offset-by-eight.ot-sdk-column,
      #ot-sdk-cookie-policy .ot-sdk-offset-by-eight.ot-sdk-columns {
        margin-left: 69.3333333333%
      }

      #onetrust-banner-sdk .ot-sdk-offset-by-nine.ot-sdk-column,
      #onetrust-banner-sdk .ot-sdk-offset-by-nine.ot-sdk-columns,
      #onetrust-pc-sdk .ot-sdk-offset-by-nine.ot-sdk-column,
      #onetrust-pc-sdk .ot-sdk-offset-by-nine.ot-sdk-columns,
      #ot-sdk-cookie-policy .ot-sdk-offset-by-nine.ot-sdk-column,
      #ot-sdk-cookie-policy .ot-sdk-offset-by-nine.ot-sdk-columns {
        margin-left: 78%
      }

      #onetrust-banner-sdk .ot-sdk-offset-by-ten.ot-sdk-column,
      #onetrust-banner-sdk .ot-sdk-offset-by-ten.ot-sdk-columns,
      #onetrust-pc-sdk .ot-sdk-offset-by-ten.ot-sdk-column,
      #onetrust-pc-sdk .ot-sdk-offset-by-ten.ot-sdk-columns,
      #ot-sdk-cookie-policy .ot-sdk-offset-by-ten.ot-sdk-column,
      #ot-sdk-cookie-policy .ot-sdk-offset-by-ten.ot-sdk-columns {
        margin-left: 86.6666666667%
      }

      #onetrust-banner-sdk .ot-sdk-offset-by-eleven.ot-sdk-column,
      #onetrust-banner-sdk .ot-sdk-offset-by-eleven.ot-sdk-columns,
      #onetrust-pc-sdk .ot-sdk-offset-by-eleven.ot-sdk-column,
      #onetrust-pc-sdk .ot-sdk-offset-by-eleven.ot-sdk-columns,
      #ot-sdk-cookie-policy .ot-sdk-offset-by-eleven.ot-sdk-column,
      #ot-sdk-cookie-policy .ot-sdk-offset-by-eleven.ot-sdk-columns {
        margin-left: 95.3333333333%
      }

      #onetrust-banner-sdk .ot-sdk-offset-by-one-third.ot-sdk-column,
      #onetrust-banner-sdk .ot-sdk-offset-by-one-third.ot-sdk-columns,
      #onetrust-pc-sdk .ot-sdk-offset-by-one-third.ot-sdk-column,
      #onetrust-pc-sdk .ot-sdk-offset-by-one-third.ot-sdk-columns,
      #ot-sdk-cookie-policy .ot-sdk-offset-by-one-third.ot-sdk-column,
      #ot-sdk-cookie-policy .ot-sdk-offset-by-one-third.ot-sdk-columns {
        margin-left: 34.6666666667%
      }

      #onetrust-banner-sdk .ot-sdk-offset-by-two-thirds.ot-sdk-column,
      #onetrust-banner-sdk .ot-sdk-offset-by-two-thirds.ot-sdk-columns,
      #onetrust-pc-sdk .ot-sdk-offset-by-two-thirds.ot-sdk-column,
      #onetrust-pc-sdk .ot-sdk-offset-by-two-thirds.ot-sdk-columns,
      #ot-sdk-cookie-policy .ot-sdk-offset-by-two-thirds.ot-sdk-column,
      #ot-sdk-cookie-policy .ot-sdk-offset-by-two-thirds.ot-sdk-columns {
        margin-left: 69.3333333333%
      }

      #onetrust-banner-sdk .ot-sdk-offset-by-one-half.ot-sdk-column,
      #onetrust-banner-sdk .ot-sdk-offset-by-one-half.ot-sdk-columns,
      #onetrust-pc-sdk .ot-sdk-offset-by-one-half.ot-sdk-column,
      #onetrust-pc-sdk .ot-sdk-offset-by-one-half.ot-sdk-columns,
      #ot-sdk-cookie-policy .ot-sdk-offset-by-one-half.ot-sdk-column,
      #ot-sdk-cookie-policy .ot-sdk-offset-by-one-half.ot-sdk-columns {
        margin-left: 52%
      }
    }

    #onetrust-banner-sdk h1,
    #onetrust-banner-sdk h2,
    #onetrust-banner-sdk h3,
    #onetrust-banner-sdk h4,
    #onetrust-banner-sdk h5,
    #onetrust-banner-sdk h6,
    #onetrust-pc-sdk h1,
    #onetrust-pc-sdk h2,
    #onetrust-pc-sdk h3,
    #onetrust-pc-sdk h4,
    #onetrust-pc-sdk h5,
    #onetrust-pc-sdk h6,
    #ot-sdk-cookie-policy h1,
    #ot-sdk-cookie-policy h2,
    #ot-sdk-cookie-policy h3,
    #ot-sdk-cookie-policy h4,
    #ot-sdk-cookie-policy h5,
    #ot-sdk-cookie-policy h6 {
      margin-top: 0;
      font-weight: 600;
      font-family: inherit
    }

    #onetrust-banner-sdk h1,
    #onetrust-pc-sdk h1,
    #ot-sdk-cookie-policy h1 {
      font-size: 1.5rem;
      line-height: 1.2
    }

    #onetrust-banner-sdk h2,
    #onetrust-pc-sdk h2,
    #ot-sdk-cookie-policy h2 {
      font-size: 1.5rem;
      line-height: 1.25
    }

    #onetrust-banner-sdk h3,
    #onetrust-pc-sdk h3,
    #ot-sdk-cookie-policy h3 {
      font-size: 1.5rem;
      line-height: 1.3
    }

    #onetrust-banner-sdk h4,
    #onetrust-pc-sdk h4,
    #ot-sdk-cookie-policy h4 {
      font-size: 1.5rem;
      line-height: 1.35
    }

    #onetrust-banner-sdk h5,
    #onetrust-pc-sdk h5,
    #ot-sdk-cookie-policy h5 {
      font-size: 1.5rem;
      line-height: 1.5
    }

    #onetrust-banner-sdk h6,
    #onetrust-pc-sdk h6,
    #ot-sdk-cookie-policy h6 {
      font-size: 1.5rem;
      line-height: 1.6
    }

    @media (min-width: 550px) {

      #onetrust-banner-sdk h1,
      #onetrust-pc-sdk h1,
      #ot-sdk-cookie-policy h1 {
        font-size: 1.5rem
      }

      #onetrust-banner-sdk h2,
      #onetrust-pc-sdk h2,
      #ot-sdk-cookie-policy h2 {
        font-size: 1.5rem
      }

      #onetrust-banner-sdk h3,
      #onetrust-pc-sdk h3,
      #ot-sdk-cookie-policy h3 {
        font-size: 1.5rem
      }

      #onetrust-banner-sdk h4,
      #onetrust-pc-sdk h4,
      #ot-sdk-cookie-policy h4 {
        font-size: 1.5rem
      }

      #onetrust-banner-sdk h5,
      #onetrust-pc-sdk h5,
      #ot-sdk-cookie-policy h5 {
        font-size: 1.5rem
      }

      #onetrust-banner-sdk h6,
      #onetrust-pc-sdk h6,
      #ot-sdk-cookie-policy h6 {
        font-size: 1.5rem
      }
    }

    #onetrust-banner-sdk p,
    #onetrust-pc-sdk p,
    #ot-sdk-cookie-policy p {
      margin: 0 0 1em 0;
      font-family: inherit;
      line-height: normal
    }

    #onetrust-banner-sdk a,
    #onetrust-pc-sdk a,
    #ot-sdk-cookie-policy a {
      color: #565656;
      text-decoration: underline
    }

    #onetrust-banner-sdk a:hover,
    #onetrust-pc-sdk a:hover,
    #ot-sdk-cookie-policy a:hover {
      color: #565656;
      text-decoration: none
    }

    #onetrust-banner-sdk .ot-sdk-button,
    #onetrust-banner-sdk button,
    #onetrust-pc-sdk .ot-sdk-button,
    #onetrust-pc-sdk button,
    #ot-sdk-cookie-policy .ot-sdk-button,
    #ot-sdk-cookie-policy button {
      margin-bottom: 1rem;
      font-family: inherit
    }

    #onetrust-banner-sdk .ot-sdk-button,
    #onetrust-banner-sdk button,
    #onetrust-banner-sdk input[type="submit"],
    #onetrust-banner-sdk input[type="reset"],
    #onetrust-banner-sdk input[type="button"],
    #onetrust-pc-sdk .ot-sdk-button,
    #onetrust-pc-sdk button,
    #onetrust-pc-sdk input[type="submit"],
    #onetrust-pc-sdk input[type="reset"],
    #onetrust-pc-sdk input[type="button"],
    #ot-sdk-cookie-policy .ot-sdk-button,
    #ot-sdk-cookie-policy button,
    #ot-sdk-cookie-policy input[type="submit"],
    #ot-sdk-cookie-policy input[type="reset"],
    #ot-sdk-cookie-policy input[type="button"] {
      display: inline-block;
      height: 38px;
      padding: 0 30px;
      color: #555;
      text-align: center;
      font-size: 0.9em;
      font-weight: 400;
      line-height: 38px;
      letter-spacing: 0.01em;
      text-decoration: none;
      white-space: nowrap;
      background-color: transparent;
      border-radius: 2px;
      border: 1px solid #bbb;
      cursor: pointer;
      box-sizing: border-box
    }

    #onetrust-banner-sdk .ot-sdk-button:hover,
    #onetrust-banner-sdk :not(.ot-leg-btn-container)>button:hover,
    #onetrust-banner-sdk input[type="submit"]:hover,
    #onetrust-banner-sdk input[type="reset"]:hover,
    #onetrust-banner-sdk input[type="button"]:hover,
    #onetrust-banner-sdk .ot-sdk-button:focus,
    #onetrust-banner-sdk :not(.ot-leg-btn-container)>button:focus,
    #onetrust-banner-sdk input[type="submit"]:focus,
    #onetrust-banner-sdk input[type="reset"]:focus,
    #onetrust-banner-sdk input[type="button"]:focus,
    #onetrust-pc-sdk .ot-sdk-button:hover,
    #onetrust-pc-sdk :not(.ot-leg-btn-container)>button:hover,
    #onetrust-pc-sdk input[type="submit"]:hover,
    #onetrust-pc-sdk input[type="reset"]:hover,
    #onetrust-pc-sdk input[type="button"]:hover,
    #onetrust-pc-sdk .ot-sdk-button:focus,
    #onetrust-pc-sdk :not(.ot-leg-btn-container)>button:focus,
    #onetrust-pc-sdk input[type="submit"]:focus,
    #onetrust-pc-sdk input[type="reset"]:focus,
    #onetrust-pc-sdk input[type="button"]:focus,
    #ot-sdk-cookie-policy .ot-sdk-button:hover,
    #ot-sdk-cookie-policy :not(.ot-leg-btn-container)>button:hover,
    #ot-sdk-cookie-policy input[type="submit"]:hover,
    #ot-sdk-cookie-policy input[type="reset"]:hover,
    #ot-sdk-cookie-policy input[type="button"]:hover,
    #ot-sdk-cookie-policy .ot-sdk-button:focus,
    #ot-sdk-cookie-policy :not(.ot-leg-btn-container)>button:focus,
    #ot-sdk-cookie-policy input[type="submit"]:focus,
    #ot-sdk-cookie-policy input[type="reset"]:focus,
    #ot-sdk-cookie-policy input[type="button"]:focus {
      color: #333;
      border-color: #888;
      opacity: 0.7
    }

    #onetrust-banner-sdk .ot-sdk-button.ot-sdk-button-primary,
    #onetrust-banner-sdk button.ot-sdk-button-primary,
    #onetrust-banner-sdk input[type="submit"].ot-sdk-button-primary,
    #onetrust-banner-sdk input[type="reset"].ot-sdk-button-primary,
    #onetrust-banner-sdk input[type="button"].ot-sdk-button-primary,
    #onetrust-pc-sdk .ot-sdk-button.ot-sdk-button-primary,
    #onetrust-pc-sdk button.ot-sdk-button-primary,
    #onetrust-pc-sdk input[type="submit"].ot-sdk-button-primary,
    #onetrust-pc-sdk input[type="reset"].ot-sdk-button-primary,
    #onetrust-pc-sdk input[type="button"].ot-sdk-button-primary,
    #ot-sdk-cookie-policy .ot-sdk-button.ot-sdk-button-primary,
    #ot-sdk-cookie-policy button.ot-sdk-button-primary,
    #ot-sdk-cookie-policy input[type="submit"].ot-sdk-button-primary,
    #ot-sdk-cookie-policy input[type="reset"].ot-sdk-button-primary,
    #ot-sdk-cookie-policy input[type="button"].ot-sdk-button-primary {
      color: #fff;
      background-color: #33c3f0;
      border-color: #33c3f0
    }

    #onetrust-banner-sdk .ot-sdk-button.ot-sdk-button-primary:hover,
    #onetrust-banner-sdk button.ot-sdk-button-primary:hover,
    #onetrust-banner-sdk input[type="submit"].ot-sdk-button-primary:hover,
    #onetrust-banner-sdk input[type="reset"].ot-sdk-button-primary:hover,
    #onetrust-banner-sdk input[type="button"].ot-sdk-button-primary:hover,
    #onetrust-banner-sdk .ot-sdk-button.ot-sdk-button-primary:focus,
    #onetrust-banner-sdk button.ot-sdk-button-primary:focus,
    #onetrust-banner-sdk input[type="submit"].ot-sdk-button-primary:focus,
    #onetrust-banner-sdk input[type="reset"].ot-sdk-button-primary:focus,
    #onetrust-banner-sdk input[type="button"].ot-sdk-button-primary:focus,
    #onetrust-pc-sdk .ot-sdk-button.ot-sdk-button-primary:hover,
    #onetrust-pc-sdk button.ot-sdk-button-primary:hover,
    #onetrust-pc-sdk input[type="submit"].ot-sdk-button-primary:hover,
    #onetrust-pc-sdk input[type="reset"].ot-sdk-button-primary:hover,
    #onetrust-pc-sdk input[type="button"].ot-sdk-button-primary:hover,
    #onetrust-pc-sdk .ot-sdk-button.ot-sdk-button-primary:focus,
    #onetrust-pc-sdk button.ot-sdk-button-primary:focus,
    #onetrust-pc-sdk input[type="submit"].ot-sdk-button-primary:focus,
    #onetrust-pc-sdk input[type="reset"].ot-sdk-button-primary:focus,
    #onetrust-pc-sdk input[type="button"].ot-sdk-button-primary:focus,
    #ot-sdk-cookie-policy .ot-sdk-button.ot-sdk-button-primary:hover,
    #ot-sdk-cookie-policy button.ot-sdk-button-primary:hover,
    #ot-sdk-cookie-policy input[type="submit"].ot-sdk-button-primary:hover,
    #ot-sdk-cookie-policy input[type="reset"].ot-sdk-button-primary:hover,
    #ot-sdk-cookie-policy input[type="button"].ot-sdk-button-primary:hover,
    #ot-sdk-cookie-policy .ot-sdk-button.ot-sdk-button-primary:focus,
    #ot-sdk-cookie-policy button.ot-sdk-button-primary:focus,
    #ot-sdk-cookie-policy input[type="submit"].ot-sdk-button-primary:focus,
    #ot-sdk-cookie-policy input[type="reset"].ot-sdk-button-primary:focus,
    #ot-sdk-cookie-policy input[type="button"].ot-sdk-button-primary:focus {
      color: #fff;
      background-color: #1eaedb;
      border-color: #1eaedb
    }

    #onetrust-banner-sdk input[type="email"],
    #onetrust-banner-sdk input[type="number"],
    #onetrust-banner-sdk input[type="search"],
    #onetrust-banner-sdk input[type="text"],
    #onetrust-banner-sdk input[type="tel"],
    #onetrust-banner-sdk input[type="url"],
    #onetrust-banner-sdk input[type="password"],
    #onetrust-banner-sdk textarea,
    #onetrust-banner-sdk select,
    #onetrust-pc-sdk input[type="email"],
    #onetrust-pc-sdk input[type="number"],
    #onetrust-pc-sdk input[type="search"],
    #onetrust-pc-sdk input[type="text"],
    #onetrust-pc-sdk input[type="tel"],
    #onetrust-pc-sdk input[type="url"],
    #onetrust-pc-sdk input[type="password"],
    #onetrust-pc-sdk textarea,
    #onetrust-pc-sdk select,
    #ot-sdk-cookie-policy input[type="email"],
    #ot-sdk-cookie-policy input[type="number"],
    #ot-sdk-cookie-policy input[type="search"],
    #ot-sdk-cookie-policy input[type="text"],
    #ot-sdk-cookie-policy input[type="tel"],
    #ot-sdk-cookie-policy input[type="url"],
    #ot-sdk-cookie-policy input[type="password"],
    #ot-sdk-cookie-policy textarea,
    #ot-sdk-cookie-policy select {
      height: 38px;
      padding: 6px 10px;
      background-color: #fff;
      border: 1px solid #d1d1d1;
      border-radius: 4px;
      box-shadow: none;
      box-sizing: border-box
    }

    #onetrust-banner-sdk input[type="email"],
    #onetrust-banner-sdk input[type="number"],
    #onetrust-banner-sdk input[type="search"],
    #onetrust-banner-sdk input[type="text"],
    #onetrust-banner-sdk input[type="tel"],
    #onetrust-banner-sdk input[type="url"],
    #onetrust-banner-sdk input[type="password"],
    #onetrust-banner-sdk textarea,
    #onetrust-pc-sdk input[type="email"],
    #onetrust-pc-sdk input[type="number"],
    #onetrust-pc-sdk input[type="search"],
    #onetrust-pc-sdk input[type="text"],
    #onetrust-pc-sdk input[type="tel"],
    #onetrust-pc-sdk input[type="url"],
    #onetrust-pc-sdk input[type="password"],
    #onetrust-pc-sdk textarea,
    #ot-sdk-cookie-policy input[type="email"],
    #ot-sdk-cookie-policy input[type="number"],
    #ot-sdk-cookie-policy input[type="search"],
    #ot-sdk-cookie-policy input[type="text"],
    #ot-sdk-cookie-policy input[type="tel"],
    #ot-sdk-cookie-policy input[type="url"],
    #ot-sdk-cookie-policy input[type="password"],
    #ot-sdk-cookie-policy textarea {
      -webkit-appearance: none;
      -moz-appearance: none;
      appearance: none
    }

    #onetrust-banner-sdk textarea,
    #onetrust-pc-sdk textarea,
    #ot-sdk-cookie-policy textarea {
      min-height: 65px;
      padding-top: 6px;
      padding-bottom: 6px
    }

    #onetrust-banner-sdk input[type="email"]:focus,
    #onetrust-banner-sdk input[type="number"]:focus,
    #onetrust-banner-sdk input[type="search"]:focus,
    #onetrust-banner-sdk input[type="text"]:focus,
    #onetrust-banner-sdk input[type="tel"]:focus,
    #onetrust-banner-sdk input[type="url"]:focus,
    #onetrust-banner-sdk input[type="password"]:focus,
    #onetrust-banner-sdk textarea:focus,
    #onetrust-banner-sdk select:focus,
    #onetrust-pc-sdk input[type="email"]:focus,
    #onetrust-pc-sdk input[type="number"]:focus,
    #onetrust-pc-sdk input[type="search"]:focus,
    #onetrust-pc-sdk input[type="text"]:focus,
    #onetrust-pc-sdk input[type="tel"]:focus,
    #onetrust-pc-sdk input[type="url"]:focus,
    #onetrust-pc-sdk input[type="password"]:focus,
    #onetrust-pc-sdk textarea:focus,
    #onetrust-pc-sdk select:focus,
    #ot-sdk-cookie-policy input[type="email"]:focus,
    #ot-sdk-cookie-policy input[type="number"]:focus,
    #ot-sdk-cookie-policy input[type="search"]:focus,
    #ot-sdk-cookie-policy input[type="text"]:focus,
    #ot-sdk-cookie-policy input[type="tel"]:focus,
    #ot-sdk-cookie-policy input[type="url"]:focus,
    #ot-sdk-cookie-policy input[type="password"]:focus,
    #ot-sdk-cookie-policy textarea:focus,
    #ot-sdk-cookie-policy select:focus {
      border: 1px solid #33c3f0;
      outline: 0
    }

    #onetrust-banner-sdk label,
    #onetrust-banner-sdk legend,
    #onetrust-pc-sdk label,
    #onetrust-pc-sdk legend,
    #ot-sdk-cookie-policy label,
    #ot-sdk-cookie-policy legend {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 600
    }

    #onetrust-banner-sdk fieldset,
    #onetrust-pc-sdk fieldset,
    #ot-sdk-cookie-policy fieldset {
      padding: 0;
      border-width: 0
    }

    #onetrust-banner-sdk input[type="checkbox"],
    #onetrust-banner-sdk input[type="radio"],
    #onetrust-pc-sdk input[type="checkbox"],
    #onetrust-pc-sdk input[type="radio"],
    #ot-sdk-cookie-policy input[type="checkbox"],
    #ot-sdk-cookie-policy input[type="radio"] {
      display: inline
    }

    #onetrust-banner-sdk label>.label-body,
    #onetrust-pc-sdk label>.label-body,
    #ot-sdk-cookie-policy label>.label-body {
      display: inline-block;
      margin-left: 0.5rem;
      font-weight: normal
    }

    #onetrust-banner-sdk ul,
    #onetrust-pc-sdk ul,
    #ot-sdk-cookie-policy ul {
      list-style: circle inside
    }

    #onetrust-banner-sdk ol,
    #onetrust-pc-sdk ol,
    #ot-sdk-cookie-policy ol {
      list-style: decimal inside
    }

    #onetrust-banner-sdk ol,
    #onetrust-banner-sdk ul,
    #onetrust-pc-sdk ol,
    #onetrust-pc-sdk ul,
    #ot-sdk-cookie-policy ol,
    #ot-sdk-cookie-policy ul {
      padding-left: 0;
      margin-top: 0
    }

    #onetrust-banner-sdk ul ul,
    #onetrust-banner-sdk ul ol,
    #onetrust-banner-sdk ol ol,
    #onetrust-banner-sdk ol ul,
    #onetrust-pc-sdk ul ul,
    #onetrust-pc-sdk ul ol,
    #onetrust-pc-sdk ol ol,
    #onetrust-pc-sdk ol ul,
    #ot-sdk-cookie-policy ul ul,
    #ot-sdk-cookie-policy ul ol,
    #ot-sdk-cookie-policy ol ol,
    #ot-sdk-cookie-policy ol ul {
      margin: 1.5rem 0 1.5rem 3rem;
      font-size: 90%
    }

    #onetrust-banner-sdk li,
    #onetrust-pc-sdk li,
    #ot-sdk-cookie-policy li {
      margin-bottom: 1rem
    }

    #onetrust-banner-sdk code,
    #onetrust-pc-sdk code,
    #ot-sdk-cookie-policy code {
      padding: 0.2rem 0.5rem;
      margin: 0 0.2rem;
      font-size: 90%;
      white-space: nowrap;
      background: #f1f1f1;
      border: 1px solid #e1e1e1;
      border-radius: 4px
    }

    #onetrust-banner-sdk pre>code,
    #onetrust-pc-sdk pre>code,
    #ot-sdk-cookie-policy pre>code {
      display: block;
      padding: 1rem 1.5rem;
      white-space: pre
    }

    #onetrust-banner-sdk th,
    #onetrust-banner-sdk td,
    #onetrust-pc-sdk th,
    #onetrust-pc-sdk td,
    #ot-sdk-cookie-policy th,
    #ot-sdk-cookie-policy td {
      padding: 12px 15px;
      text-align: left;
      border-bottom: 1px solid #e1e1e1
    }

    #onetrust-banner-sdk .ot-sdk-u-full-width,
    #onetrust-pc-sdk .ot-sdk-u-full-width,
    #ot-sdk-cookie-policy .ot-sdk-u-full-width {
      width: 100%;
      box-sizing: border-box
    }

    #onetrust-banner-sdk .ot-sdk-u-max-full-width,
    #onetrust-pc-sdk .ot-sdk-u-max-full-width,
    #ot-sdk-cookie-policy .ot-sdk-u-max-full-width {
      max-width: 100%;
      box-sizing: border-box
    }

    #onetrust-banner-sdk .ot-sdk-u-pull-right,
    #onetrust-pc-sdk .ot-sdk-u-pull-right,
    #ot-sdk-cookie-policy .ot-sdk-u-pull-right {
      float: right
    }

    #onetrust-banner-sdk .ot-sdk-u-pull-left,
    #onetrust-pc-sdk .ot-sdk-u-pull-left,
    #ot-sdk-cookie-policy .ot-sdk-u-pull-left {
      float: left
    }

    #onetrust-banner-sdk hr,
    #onetrust-pc-sdk hr,
    #ot-sdk-cookie-policy hr {
      margin-top: 3rem;
      margin-bottom: 3.5rem;
      border-width: 0;
      border-top: 1px solid #e1e1e1
    }

    #onetrust-banner-sdk .ot-sdk-container:after,
    #onetrust-banner-sdk .ot-sdk-row:after,
    #onetrust-banner-sdk .ot-sdk-u-cf,
    #onetrust-pc-sdk .ot-sdk-container:after,
    #onetrust-pc-sdk .ot-sdk-row:after,
    #onetrust-pc-sdk .ot-sdk-u-cf,
    #ot-sdk-cookie-policy .ot-sdk-container:after,
    #ot-sdk-cookie-policy .ot-sdk-row:after,
    #ot-sdk-cookie-policy .ot-sdk-u-cf {
      content: "";
      display: table;
      clear: both
    }

    #onetrust-banner-sdk .ot-sdk-row,
    #onetrust-pc-sdk .ot-sdk-row,
    #ot-sdk-cookie-policy .ot-sdk-row {
      margin: 0;
      max-width: none;
      display: block;
      margin: 0
    }

    #onetrust-banner-sdk .banner-option-input:focus+label {
      outline-color: -webkit-focus-ring-color;
      outline-style: auto;
      outline-width: 1px
    }

    #onetrust-banner-sdk {
      box-shadow: 0 0 18px rgba(0, 0, 0, .2)
    }

    #onetrust-banner-sdk.otFlat {
      position: fixed;
      z-index: 2147483645;
      bottom: 0;
      right: 0;
      left: 0;
      background-color: #fff;
      max-height: 90%;
      overflow-x: hidden;
      overflow-y: auto
    }

    #onetrust-banner-sdk>.ot-sdk-container {
      overflow: hidden
    }

    #onetrust-banner-sdk::-webkit-scrollbar {
      width: 11px
    }

    #onetrust-banner-sdk::-webkit-scrollbar-thumb {
      border-radius: 10px;
      background: #c1c1c1
    }

    #onetrust-banner-sdk {
      scrollbar-arrow-color: #c1c1c1;
      scrollbar-darkshadow-color: #c1c1c1;
      scrollbar-face-color: #c1c1c1;
      scrollbar-shadow-color: #c1c1c1
    }

    #onetrust-banner-sdk #onetrust-policy {
      margin: 1.25em 0 .625em 2em;
      overflow: hidden
    }

    #onetrust-banner-sdk #onetrust-policy-title {
      font-size: 1.2em;
      line-height: 1.3;
      margin-bottom: 10px
    }

    #onetrust-banner-sdk #onetrust-policy-text {
      clear: both;
      text-align: left;
      font-size: .88em;
      line-height: 1.4
    }

    #onetrust-banner-sdk #onetrust-policy-text * {
      font-size: inherit;
      line-height: inherit
    }

    #onetrust-banner-sdk #onetrust-policy-text a {
      font-weight: bold;
      margin-left: 5px
    }

    #onetrust-banner-sdk #onetrust-policy-title,
    #onetrust-banner-sdk #onetrust-policy-text {
      color: dimgray;
      float: left
    }

    #onetrust-banner-sdk #onetrust-button-group-parent {
      min-height: 1px;
      text-align: center
    }

    #onetrust-banner-sdk #onetrust-button-group {
      display: inline-block
    }

    #onetrust-banner-sdk #onetrust-accept-btn-handler,
    #onetrust-banner-sdk #onetrust-reject-all-handler,
    #onetrust-banner-sdk #onetrust-pc-btn-handler {
      background-color: #68b631;
      color: #fff;
      border-color: #68b631;
      margin-right: 1em;
      min-width: 125px;
      height: auto;
      white-space: normal;
      word-break: break-word;
      word-wrap: break-word;
      padding: 12px 10px;
      line-height: 1.2;
      font-size: .813em;
      font-weight: 600
    }

    #onetrust-banner-sdk #onetrust-pc-btn-handler.cookie-setting-link {
      background-color: #fff;
      border: none;
      color: #68b631;
      text-decoration: underline;
      padding-right: 0
    }

    #onetrust-banner-sdk #onetrust-close-btn-container {
      text-align: center
    }

    #onetrust-banner-sdk .onetrust-close-btn-ui {
      width: .8em;
      height: 18px;
      margin: 50% 0 0 50%;
      border: none
    }

    #onetrust-banner-sdk .onetrust-close-btn-ui.onetrust-lg {
      top: 50%;
      margin: auto;
      transform: translate(-50%, -50%);
      position: absolute;
      padding: 0
    }

    #onetrust-banner-sdk .banner_logo {
      display: none
    }

    #onetrust-banner-sdk .ot-b-addl-desc {
      clear: both;
      float: left;
      display: block
    }

    #onetrust-banner-sdk #banner-options {
      float: left;
      display: table;
      margin-right: 0;
      margin-left: 1em;
      width: calc(100% - 1em)
    }

    #onetrust-banner-sdk #banner-options label {
      margin: 0;
      display: inline-block
    }

    #onetrust-banner-sdk .banner-option {
      margin-bottom: 12px;
      border: none;
      float: left;
      padding: 0
    }

    #onetrust-banner-sdk .banner-option:not(:first-child) {
      padding: 0;
      border: none
    }

    #onetrust-banner-sdk .banner-option-input {
      position: absolute;
      cursor: pointer;
      width: auto;
      height: 20px;
      opacity: 0
    }

    #onetrust-banner-sdk .banner-option-header {
      margin-bottom: 6px;
      cursor: pointer;
      display: inline-block
    }

    #onetrust-banner-sdk .banner-option-header :first-child {
      font-size: .82em;
      line-height: 1.4;
      color: dimgray;
      font-weight: bold;
      float: left
    }

    #onetrust-banner-sdk .banner-option-header .ot-arrow-container {
      display: inline-block;
      border-top: 6px solid transparent;
      border-bottom: 6px solid transparent;
      border-left: 6px solid dimgray;
      margin-left: 10px;
      margin-top: 2px
    }

    #onetrust-banner-sdk .banner-option-details {
      display: none;
      font-size: .83em;
      line-height: 1.5;
      padding: 10px 0px 5px 10px;
      margin-right: 10px;
      height: 0px
    }

    #onetrust-banner-sdk .banner-option-details * {
      font-size: inherit;
      line-height: inherit;
      color: dimgray
    }

    #onetrust-banner-sdk .ot-arrow-container,
    #onetrust-banner-sdk .banner-option-details {
      transition: all 300ms ease-in 0s;
      -webkit-transition: all 300ms ease-in 0s;
      -moz-transition: all 300ms ease-in 0s;
      -o-transition: all 300ms ease-in 0s
    }

    #onetrust-banner-sdk .banner-option-input:checked~label .banner-option-header .ot-arrow-container {
      transform: rotate(90deg)
    }

    #onetrust-banner-sdk .banner-option-input:checked~.banner-option-details {
      height: auto;
      display: block
    }

    #onetrust-banner-sdk .ot-dpd-container {
      float: left
    }

    #onetrust-banner-sdk .ot-dpd-title {
      margin-bottom: 10px
    }

    #onetrust-banner-sdk .ot-dpd-title,
    #onetrust-banner-sdk .ot-dpd-desc {
      font-size: .88em;
      line-height: 1.4;
      color: dimgray
    }

    #onetrust-banner-sdk .ot-dpd-title *,
    #onetrust-banner-sdk .ot-dpd-desc * {
      font-size: inherit;
      line-height: inherit
    }

    #onetrust-banner-sdk.ot-iab-2 #onetrust-policy-text * {
      margin-bottom: 0
    }

    #onetrust-banner-sdk.ot-iab-2 .onetrust-vendors-list-handler {
      display: block;
      margin-left: 0;
      margin-top: 5px;
      clear: both
    }

    #onetrust-banner-sdk.ot-iab-2 #onetrust-button-group button {
      display: block
    }

    #onetrust-banner-sdk #onetrust-policy-text,
    #onetrust-banner-sdk .ot-dpd-desc,
    #onetrust-banner-sdk .ot-b-addl-desc {
      font-size: .813em;
      line-height: 1.5
    }

    #onetrust-banner-sdk .ot-dpd-desc {
      margin-bottom: 10px
    }

    #onetrust-banner-sdk .ot-dpd-desc>.ot-b-addl-desc {
      margin-top: 10px;
      margin-bottom: 10px;
      font-size: 1em
    }

    @media only screen and (max-width: 425px) {
      #onetrust-banner-sdk #onetrust-policy {
        margin-left: 0
      }

      #onetrust-banner-sdk .ot-hide-small {
        display: none
      }

      #onetrust-banner-sdk #onetrust-button-group {
        display: block
      }

      #onetrust-banner-sdk #onetrust-accept-btn-handler,
      #onetrust-banner-sdk #onetrust-reject-all-handler,
      #onetrust-banner-sdk #onetrust-pc-btn-handler {
        width: 100%
      }

      #onetrust-banner-sdk .onetrust-close-btn-ui {
        margin: 5px 0 0 0;
        float: right;
        padding: 0
      }

      #onetrust-banner-sdk #onetrust-close-btn-container-mobile,
      #onetrust-banner-sdk #onetrust-policy-title {
        display: inline;
        float: none
      }

      #onetrust-banner-sdk #banner-options {
        margin: 0;
        padding: 0;
        width: 100%
      }
    }

    @media only screen and (min-width: 426px)and (max-width: 896px) {
      #onetrust-banner-sdk #onetrust-policy {
        margin-left: 1em;
        margin-right: 1em
      }

      #onetrust-banner-sdk .onetrust-close-btn-ui.onetrust-lg {
        top: 25%;
        right: 2%
      }

      #onetrust-banner-sdk:not(.ot-iab-2) #onetrust-group-container {
        width: 95%
      }

      #onetrust-banner-sdk.ot-iab-2 #onetrust-group-container {
        width: 100%
      }

      #onetrust-banner-sdk #onetrust-button-group-parent {
        width: 100%;
        position: relative;
        margin-left: 0
      }

      #onetrust-banner-sdk .ot-hide-large {
        display: none
      }

      #onetrust-banner-sdk #onetrust-button-group button {
        display: inline-block
      }

      #onetrust-banner-sdk #onetrust-button-group {
        margin-right: 0;
        text-align: center
      }

      #onetrust-banner-sdk .has-reject-all-button #onetrust-pc-btn-handler {
        float: left
      }

      #onetrust-banner-sdk .has-reject-all-button #onetrust-reject-all-handler,
      #onetrust-banner-sdk .has-reject-all-button #onetrust-accept-btn-handler {
        float: right
      }

      #onetrust-banner-sdk .has-reject-all-button #onetrust-button-group {
        width: calc(100% - 2em);
        margin-right: 0
      }

      #onetrust-banner-sdk .has-reject-all-button #onetrust-pc-btn-handler.cookie-setting-link {
        padding-left: 0px;
        text-align: left
      }

      #onetrust-banner-sdk.ot-buttons-fw .ot-sdk-three button {
        width: 100%;
        text-align: center
      }

      #onetrust-banner-sdk.ot-buttons-fw #onetrust-button-group-parent button {
        float: none
      }

      #onetrust-banner-sdk.ot-buttons-fw #onetrust-pc-btn-handler.cookie-setting-link {
        text-align: center
      }
    }

    @media only screen and (min-width: 550px) {
      #onetrust-banner-sdk .banner-option:not(:first-child) {
        border-left: 1px solid #d8d8d8;
        padding-left: 25px
      }
    }

    @media only screen and (min-width: 425px)and (max-width: 550px) {

      #onetrust-banner-sdk.ot-iab-2 #onetrust-button-group,
      #onetrust-banner-sdk.ot-iab-2 #onetrust-policy,
      #onetrust-banner-sdk.ot-iab-2 .banner-option {
        width: 100%
      }
    }

    @media only screen and (min-width: 769px) {
      #onetrust-banner-sdk .ot-hide-large {
        display: none
      }

      #onetrust-banner-sdk #onetrust-button-group {
        margin-right: 30%
      }

      #onetrust-banner-sdk #banner-options {
        margin-left: 2em;
        margin-right: 5em;
        margin-bottom: 1.25em;
        width: calc(100% - 7em)
      }

      #onetrust-banner-sdk .banner-option {
        float: none;
        display: table-cell
      }
    }

    @media only screen and (min-width: 1024px) {
      #onetrust-banner-sdk #onetrust-policy {
        margin-left: 2em
      }

      #onetrust-banner-sdk.vertical-align-content #onetrust-button-group-parent {
        position: absolute;
        top: 50%;
        left: 60%;
        transform: translateY(-50%)
      }

      #onetrust-banner-sdk.ot-iab-2 #onetrust-policy-title {
        width: 50%
      }

      #onetrust-banner-sdk.ot-iab-2 #onetrust-policy-text,
      #onetrust-banner-sdk.ot-iab-2 :not(.ot-dpd-desc)>.ot-b-addl-desc {
        margin-bottom: 1em;
        width: 50%;
        border-right: 1px solid #d8d8d8;
        padding-right: 1rem
      }

      #onetrust-banner-sdk.ot-iab-2 #onetrust-policy-text {
        margin-bottom: 0;
        padding-bottom: 1em
      }

      #onetrust-banner-sdk.ot-iab-2 :not(.ot-dpd-desc)>.ot-b-addl-desc {
        margin-bottom: 0;
        padding-bottom: 1em
      }

      #onetrust-banner-sdk.ot-iab-2 .ot-dpd-container {
        width: 45%;
        padding-left: 1rem;
        display: inline-block;
        float: none
      }

      #onetrust-banner-sdk.ot-iab-2 .ot-dpd-title {
        line-height: 1.7
      }

      #onetrust-banner-sdk.ot-iab-2 #onetrust-button-group-parent {
        left: auto;
        right: 4%;
        margin-left: 0
      }

      #onetrust-banner-sdk.ot-iab-2 #onetrust-button-group button {
        display: block
      }

      #onetrust-banner-sdk:not(.ot-iab-2) #onetrust-button-group-parent {
        margin: auto;
        width: 36%
      }

      #onetrust-banner-sdk:not(.ot-iab-2) #onetrust-group-container {
        width: 60%
      }

      #onetrust-banner-sdk #onetrust-button-group {
        margin-right: auto
      }

      #onetrust-banner-sdk #onetrust-close-btn-container {
        float: right
      }

      #onetrust-banner-sdk #onetrust-accept-btn-handler,
      #onetrust-banner-sdk #onetrust-reject-all-handler,
      #onetrust-banner-sdk #onetrust-pc-btn-handler {
        margin-top: 1em
      }
    }

    @media only screen and (min-width: 890px) {
      #onetrust-banner-sdk.ot-buttons-fw:not(.ot-iab-2) #onetrust-button-group-parent {
        padding-left: 4%;
        margin-left: 0
      }

      #onetrust-banner-sdk.ot-buttons-fw:not(.ot-iab-2) #onetrust-button-group {
        margin-right: 0;
        margin-top: 1.25em;
        width: 100%
      }

      #onetrust-banner-sdk.ot-buttons-fw:not(.ot-iab-2) #onetrust-button-group button {
        width: 100%;
        margin-bottom: 5px;
        margin-top: 5px
      }

      #onetrust-banner-sdk.ot-buttons-fw:not(.ot-iab-2) #onetrust-button-group button:last-of-type {
        margin-bottom: 20px
      }
    }

    @media only screen and (min-width: 1280px) {
      #onetrust-banner-sdk:not(.ot-iab-2) #onetrust-group-container {
        width: 55%
      }

      #onetrust-banner-sdk:not(.ot-iab-2) #onetrust-button-group-parent {
        width: 44%;
        padding-left: 2%;
        padding-right: 2%
      }

      #onetrust-banner-sdk:not(.ot-iab-2).vertical-align-content #onetrust-button-group-parent {
        position: absolute;
        left: 55%
      }
    }

    #onetrust-consent-sdk #onetrust-banner-sdk {
      background-color: #000000;
    }

    #onetrust-consent-sdk #onetrust-policy-title,
    #onetrust-consent-sdk #onetrust-policy-text,
    #onetrust-consent-sdk .ot-b-addl-desc,
    #onetrust-consent-sdk .ot-dpd-desc,
    #onetrust-consent-sdk .ot-dpd-title,
    #onetrust-consent-sdk #onetrust-policy-text *:not(.onetrust-vendors-list-handler),
    #onetrust-consent-sdk .ot-dpd-desc *:not(.onetrust-vendors-list-handler),
    #onetrust-consent-sdk #onetrust-banner-sdk #banner-options * {
      color: #FFFFFF;
    }

    #onetrust-consent-sdk #onetrust-banner-sdk .banner-option-details {
      background-color: #E9E9E9;
    }

    #onetrust-consent-sdk #onetrust-accept-btn-handler,
    #onetrust-banner-sdk #onetrust-reject-all-handler {
      background-color: #FFFFFF;
      border-color: #FFFFFF;
      color: #000000;
    }

    #onetrust-consent-sdk #onetrust-pc-btn-handler.cookie-setting-link {
      border-color: #000000;
      background-color: #000000;
      color: #FFFFFF
    }

    #onetrust-consent-sdk #onetrust-pc-btn-handler {
      color: #6CC04A;
      border-color: #6CC04A;
      background-color: #FFFFFF;
    }

    @media only screen and (max-width: 896px) {
      #onetrust-banner-sdk #onetrust-group-container {
        width: 100% !important;
      }

      #onetrust-banner-sdk #onetrust-button-group {
        margin-bottom: 15px !important;
      }
    }

    @media only screen and (max-width: 896px) and (min-width: 426px) {
      #onetrust-banner-sdk #onetrust-button-group-parent {
        left: 50% !important;
        transform: translateX(-50%) !important;
        width: auto !important;
      }
    }

    @media only screen and (min-width: 896px) {
      #onetrust-banner-sdk #onetrust-group-container {
        width: 70% !important;
      }

      #onetrust-banner-sdk #onetrust-button-group-parent {
        position: absolute;
        top: 50%;
        left: 70% !important;
        transform: translateY(-50%);
        width: 25% !important;
      }
    }


    #onetrust-banner-sdk {
      font-family: arial, helvetica, sans-serif !important;
      font-size: 14px !important;
      font-weight: normal !important;
      text-shadow: none !important;
      text-transform: none !important;
      letter-spacing: normal !important;
    }

    #onetrust-banner-sdk p {
      margin: 0 !important;
      font-size: 13px !important;
    }

    #onetrust-banner-sdk #banner-options {
      margin: 0 !important;
      font-size: 13px !important;
    }

    #onetrust-pc-sdk .always-active {
      color: #999 !important;
      font-size: 0.75em !important;
      top: 9px !important;
    }

    #onetrust-banner-sdk #onetrust-group-container {
      margin-bottom: 10px !important;
    }

    #onetrust-banner-sdk #onetrust-button-group {
      display: flex !important;
      flex-direction: row-reverse !important;
    }

    #ot-sdk-btn {
      width: auto !important;
      z-index: 3999999 !important;
    }

    #ot-sdk-btn,
    #onetrust-banner-sdk #onetrust-pc-btn-handler,
    #onetrust-banner-sdk #onetrust-accept-btn-handler,
    #onetrust-banner-sdk #accept-recommended-btn-handler,
    #cookie-preferences .save-preference-btn-handler {
      margin: 0px !important;
      padding: 10px !important;
      border-radius: 7px !important;
      border: #bbb 1px solid !important;
      background: none !important;
      background-color: #fff !important;
      box-shadow: none !important;
      transition: all 0.2s !important;
      color: #000 !important;

      font-family: arial, helvetica, sans-serif !important;
      font-size: 14px !important;
      font-weight: normal !important;
      text-shadow: none !important;
      text-decoration: none !important;
      text-transform: none !important;
      letter-spacing: normal !important;
    }

    #onetrust-banner-sdk #onetrust-accept-btn-handler {
      margin-right: 10px !important;
    }

    #onetrust-banner-sdk #onetrust-pc-btn-handler {
      margin-right: 0 !important;
    }

    #ot-sdk-btn:hover,
    #onetrust-banner-sdk #onetrust-pc-btn-handler:hover,
    #onetrust-banner-sdk #onetrust-accept-btn-handler:hover,
    #onetrust-banner-sdk #accept-recommended-btn-handler:hover,
    #cookie-preferences .save-preference-btn-handler:hover {
      background-color: #bbb !important;
      opacity: 1 !important;
    }

    #ot-sdk-btn:focus,
    #onetrust-banner-sdk #onetrust-pc-btn-handler:focus,
    #onetrust-banner-sdk #onetrust-accept-btn-handler:focus,
    #onetrust-banner-sdk #accept-recommended-btn-handler:focus,
    #cookie-preferences .save-preference-btn-handler:focus {
      background-color: #fff !important;
      opacity: 1 !important;
    }



    /* fix for ISGPRIVACY-632 : lastfm black bkg overlay */
    #onetrust-consent-sdk .main-content:before {
      background-color: transparent !important;
    }

    #onetrust-pc-sdk #ot-pc-desc a {
      display: inline-block !important;
    }

    @keyframes ot-slide-in-left {
      from {
        -webkit-transform: translate3d(-100%, 0, 0);
        transform: translate3d(-100%, 0, 0)
      }

      to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
      }
    }

    @keyframes ot-slide-in-right {
      from {
        -webkit-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0)
      }

      to {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
      }
    }

    @keyframes ot-slide-out-left {
      from {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
      }

      to {
        -webkit-transform: translate3d(-100%, 0, 0);
        transform: translate3d(-100%, 0, 0)
      }
    }

    @keyframes ot-slide-out-right {
      from {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
      }

      to {
        -webkit-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0)
      }
    }

    #onetrust-pc-sdk.otPcPanel.ot-slide-out-right {
      -webkit-animation-name: ot-slide-out-right;
      animation-name: ot-slide-out-right
    }

    #onetrust-pc-sdk.otPcPanel.ot-slide-in-left {
      -webkit-animation-name: ot-slide-in-left;
      animation-name: ot-slide-in-left
    }

    #onetrust-pc-sdk.otPcPanel.ot-slide-in-right {
      -webkit-animation-name: ot-slide-in-right;
      animation-name: ot-slide-in-right
    }

    #onetrust-pc-sdk.otPcPanel.ot-slide-out-left {
      -webkit-animation-name: ot-slide-out-left;
      animation-name: ot-slide-out-left
    }

    @media(print),
    (prefers-reduced-motion) {
      .ot-animated {
        -webkit-animation: initial !important;
        animation: initial !important;
        -webkit-transition: none !important;
        transition: none !important
      }

      #onetrust-pc-sdk.otPcPanel.ot-slide-out-left {
        -webkit-transform: translate3d(-100%, 0, 0);
        transform: translate3d(-100%, 0, 0)
      }

      #onetrust-pc-sdk.otPcPanel.ot-slide-out-right {
        -webkit-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0)
      }
    }

    #onetrust-pc-sdk {
      position: fixed;
      z-index: 2147483647;
      bottom: 0;
      left: 0;
      background-color: #fff;
      max-width: 480px;
      min-width: 480px;
      height: 100%;
      -webkit-box-shadow: 0px 2px 10px -3px #999;
      -moz-box-shadow: 0px 2px 10px -3px #999;
      box-shadow: 0px 2px 10px -3px #999
    }

    #onetrust-pc-sdk.otPcPanel.right,
    #onetrust-pc-sdk.otPcPanel[dir=rtl] {
      right: 0;
      left: auto
    }

    #onetrust-pc-sdk.otPcPanel.right-rtl[dir=rtl] {
      left: 0;
      right: auto
    }

    #onetrust-pc-sdk.otPcPanel.ot-animated {
      -webkit-animation-duration: 1s;
      animation-duration: 1s;
      -webkit-animation-fill-mode: both;
      animation-fill-mode: both
    }

    #onetrust-pc-sdk #close-pc-btn-handler.ot-close-icon {
      background-color: transparent;
      border: none
    }

    #onetrust-pc-sdk .ot-pc-scrollbar::-webkit-scrollbar-track {
      margin-right: 20px
    }

    #onetrust-pc-sdk .ot-pc-scrollbar::-webkit-scrollbar {
      width: 11px
    }

    #onetrust-pc-sdk .ot-pc-scrollbar::-webkit-scrollbar-thumb {
      border-radius: 10px;
      background: #d8d8d8
    }

    #onetrust-pc-sdk .ot-pc-scrollbar {
      scrollbar-arrow-color: #d8d8d8;
      scrollbar-darkshadow-color: #d8d8d8;
      scrollbar-face-color: #d8d8d8;
      scrollbar-shadow-color: #d8d8d8
    }

    #onetrust-pc-sdk.ot-ftr-stacked .ot-pc-refuse-all-handler {
      margin-bottom: 0px
    }

    #onetrust-pc-sdk.ot-ftr-stacked #ot-pc-content {
      bottom: 160px
    }

    #onetrust-pc-sdk.ot-ftr-stacked .ot-pc-footer button {
      width: 100%;
      max-width: none
    }

    #onetrust-pc-sdk.ot-ftr-stacked #ot-lst-cnt {
      max-height: 84%
    }

    #onetrust-pc-sdk #ot-addtl-venlst .ot-arw-cntr,
    #onetrust-pc-sdk #ot-addtl-venlst .ot-plus-minus,
    #onetrust-pc-sdk .ot-hide-tgl {
      visibility: hidden
    }

    #onetrust-pc-sdk #ot-addtl-venlst .ot-arw-cntr *,
    #onetrust-pc-sdk #ot-addtl-venlst .ot-plus-minus *,
    #onetrust-pc-sdk .ot-hide-tgl * {
      visibility: hidden
    }

    #onetrust-pc-sdk .ot-pc-header {
      height: 39px;
      border-bottom: 1px solid #e9e9e9;
      padding: 10px 0 10px 25px
    }

    #onetrust-pc-sdk .ot-pc-logo {
      height: 40px;
      width: 120px;
      display: inline-block
    }

    #onetrust-pc-sdk .ot-close-icon {
      float: right;
      height: 10px;
      width: 10px;
      margin-top: 10px;
      margin-right: 5px
    }

    #onetrust-pc-sdk #ot-pc-content,
    #onetrust-pc-sdk #ot-pc-lst {
      position: absolute;
      top: 60px;
      bottom: 104px
    }

    #onetrust-pc-sdk #ot-pc-content {
      padding-left: 2px;
      padding-right: 10px;
      margin-left: 23px;
      margin-right: 7px;
      width: calc(100% - 42px);
      overflow-y: auto
    }

    #onetrust-pc-sdk #ot-pc-lst {
      width: 100%
    }

    #onetrust-pc-sdk .ot-pc-footer {
      position: absolute;
      bottom: 0px;
      width: 100%;
      max-height: 160px;
      border-top: 1px solid #d8d8d8
    }

    #onetrust-pc-sdk .ot-pc-footer button {
      margin-top: 19px
    }

    #onetrust-pc-sdk .ot-btn-container {
      text-align: left;
      margin-left: 25px;
      margin-right: 25px
    }

    #onetrust-pc-sdk .ot-btn-container button {
      min-width: calc(50% - 5px)
    }

    #onetrust-pc-sdk .ot-pc-footer-logo {
      padding-left: 25px;
      height: 30px;
      text-align: left;
      background: #f4f4f4;
      text-align: right
    }

    #onetrust-pc-sdk .ot-pc-footer-logo a {
      display: inline-block;
      margin-top: 5px;
      margin-right: 10px
    }

    #onetrust-pc-sdk.otPcPanel[dir=rtl] .ot-pc-footer-logo {
      direction: rtl
    }

    #onetrust-pc-sdk.otPcPanel[dir=rtl] .ot-pc-footer-logo a {
      margin-right: 25px
    }

    #onetrust-pc-sdk button {
      display: inline-block;
      font-size: .75em;
      letter-spacing: .08em;
      max-width: 394px;
      padding: 12px 30px;
      line-height: 1;
      word-break: break-word;
      word-wrap: break-word;
      white-space: normal;
      font-weight: bold;
      height: auto
    }

    #onetrust-pc-sdk button:hover,
    #onetrust-pc-sdk button:focus {
      color: #fff;
      border-color: #68b631
    }

    #onetrust-pc-sdk #accept-recommended-btn-handler {
      margin-right: 10px;
      margin-bottom: 25px
    }

    #onetrust-pc-sdk .ot-pc-refuse-all-handler {
      margin-right: 5px
    }

    #onetrust-pc-sdk .ot-tgl {
      float: right;
      position: relative;
      z-index: 1
    }

    #onetrust-pc-sdk .ot-tgl input:checked+.ot-switch .ot-switch-nob {
      background-color: #d5e9ff;
      border: 1px solid #3860be
    }

    #onetrust-pc-sdk .ot-tgl input:checked+.ot-switch .ot-switch-nob:before {
      -webkit-transform: translateX(21px);
      -ms-transform: translateX(21px);
      transform: translateX(21px);
      background-color: #3860be
    }

    #onetrust-pc-sdk .ot-tgl input:focus+.ot-switch .ot-switch-nob {
      box-shadow: 0 0 1px #2196f3;
      outline: #3b99fc auto 5px !important
    }

    #onetrust-pc-sdk .ot-switch {
      position: relative;
      display: inline-block;
      width: 45px;
      height: 25px;
      margin-bottom: 0
    }

    #onetrust-pc-sdk .ot-switch-nob {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #f2f1f1;
      border: 1px solid #ddd;
      transition: all .2s ease-in 0s;
      -moz-transition: all .2s ease-in 0s;
      -o-transition: all .2s ease-in 0s;
      -webkit-transition: all .2s ease-in 0s;
      border-radius: 20px
    }

    #onetrust-pc-sdk .ot-switch-nob:before {
      position: absolute;
      content: "";
      height: 21px;
      width: 21px;
      bottom: 1px;
      background-color: #7d7d7d;
      -webkit-transition: .4s;
      transition: .4s;
      border-radius: 20px
    }

    #onetrust-pc-sdk .ot-chkbox {
      z-index: 1
    }

    #onetrust-pc-sdk .ot-chkbox input:checked~label::before {
      background-color: #3860be
    }

    #onetrust-pc-sdk .ot-chkbox input+label::after {
      content: none;
      color: #fff
    }

    #onetrust-pc-sdk .ot-chkbox input:checked+label::after {
      content: ""
    }

    #onetrust-pc-sdk .ot-chkbox input:focus+label::before {
      outline-style: solid;
      outline-width: 2px;
      outline-style: auto
    }

    #onetrust-pc-sdk .ot-chkbox label {
      position: relative;
      display: inline-block;
      cursor: pointer
    }

    #onetrust-pc-sdk .ot-chkbox label::before,
    #onetrust-pc-sdk .ot-chkbox label::after {
      position: absolute;
      content: "";
      display: inline-block;
      border-radius: 3px
    }

    #onetrust-pc-sdk .ot-chkbox label::before {
      height: 18px;
      width: 18px;
      border: 1px solid #3860be;
      left: 0px
    }

    #onetrust-pc-sdk .ot-chkbox label::after {
      height: 5px;
      width: 9px;
      border-left: 3px solid;
      border-bottom: 3px solid;
      transform: rotate(-45deg);
      -o-transform: rotate(-45deg);
      -ms-transform: rotate(-45deg);
      -webkit-transform: rotate(-45deg);
      left: 4px;
      top: 5px
    }

    #onetrust-pc-sdk .ot-label-txt {
      display: none
    }

    #onetrust-pc-sdk .ot-chkbox input,
    #onetrust-pc-sdk .ot-tgl input {
      position: absolute;
      opacity: 0;
      width: 0;
      height: 0
    }

    #onetrust-pc-sdk .ot-arw-cntr {
      float: right;
      position: relative;
      left: 5px
    }

    #onetrust-pc-sdk .ot-arw {
      width: 16px;
      height: 16px;
      margin-left: 5px;
      color: dimgray;
      display: inline-block;
      vertical-align: middle;
      -webkit-transition: all 300ms ease-in 0s;
      -moz-transition: all 300ms ease-in 0s;
      -o-transition: all 300ms ease-in 0s;
      transition: all 300ms ease-in 0s
    }

    #onetrust-pc-sdk input:checked~.ot-acc-hdr .ot-arw {
      transform: rotate(90deg);
      -o-transform: rotate(90deg);
      -ms-transform: rotate(90deg);
      -webkit-transform: rotate(90deg)
    }

    #onetrust-pc-sdk input[type=checkbox]:focus+.ot-acc-hdr {
      outline: auto;
      outline-color: #007bff
    }

    #onetrust-pc-sdk .ot-acc-hdr {
      display: inline-block;
      width: 100%
    }

    #onetrust-pc-sdk #ot-fltr-modal .ot-label-txt {
      display: inline-block;
      font-size: .85em;
      color: dimgray
    }

    #onetrust-pc-sdk .ot-label-status {
      padding-left: 5px;
      font-size: .75em;
      display: none
    }

    #onetrust-pc-sdk .ot-plus-minus {
      width: 20px;
      height: 20px;
      font-size: 1.5em;
      position: relative;
      display: inline-block;
      margin-right: 5px;
      top: 3px
    }

    #onetrust-pc-sdk .ot-plus-minus span {
      position: absolute;
      background: #27455c;
      border-radius: 1px
    }

    #onetrust-pc-sdk .ot-plus-minus span:first-of-type {
      top: 25%;
      bottom: 25%;
      width: 10%;
      left: 45%
    }

    #onetrust-pc-sdk .ot-plus-minus span:last-of-type {
      left: 25%;
      right: 25%;
      height: 10%;
      top: 45%
    }

    #onetrust-pc-sdk input:checked~.ot-acc-hdr .ot-plus-minus span:first-of-type,
    #onetrust-pc-sdk input:checked~.ot-acc-hdr .ot-plus-minus span:last-of-type {
      transform: rotate(90deg)
    }

    #onetrust-pc-sdk input:checked~.ot-acc-hdr .ot-plus-minus span:last-of-type {
      left: 50%;
      right: 50%
    }

    #onetrust-pc-sdk .ot-host-item,
    #onetrust-pc-sdk .ot-ven-item {
      padding-right: 0
    }

    #onetrust-pc-sdk .ot-host-item .ot-plus-minus,
    #onetrust-pc-sdk .ot-ven-item .ot-plus-minus {
      float: left;
      margin-right: 8px;
      margin-top: 10px
    }

    #onetrust-pc-sdk #ot-pc-title,
    #onetrust-pc-sdk #ot-pc-desc,
    #onetrust-pc-sdk #ot-category-title,
    #onetrust-pc-sdk .ot-cat-header,
    #onetrust-pc-sdk .ot-cat-item p:last-of-type {
      color: dimgray
    }

    #onetrust-pc-sdk #ot-pc-title {
      margin-top: 20px;
      margin-bottom: 10px
    }

    #onetrust-pc-sdk #ot-pc-desc,
    #onetrust-pc-sdk .ot-cat-item p {
      font-size: .79em;
      line-height: 1.4
    }

    #onetrust-pc-sdk #ot-pc-desc *,
    #onetrust-pc-sdk .ot-cat-item p * {
      font-size: inherit;
      line-height: inherit
    }

    #onetrust-pc-sdk #ot-category-title,
    #onetrust-pc-sdk #ot-pc-title {
      font-size: 1.125em;
      line-height: 1.2
    }

    #onetrust-pc-sdk #ot-pc-desc {
      clear: both;
      font-size: .813em;
      line-height: 1.5;
      margin-bottom: 25px
    }

    #onetrust-pc-sdk #ot-pc-desc * {
      font-size: inherit
    }

    #onetrust-pc-sdk #ot-pc-desc a {
      display: block;
      margin-top: 5px
    }

    #onetrust-pc-sdk #ot-pc-desc li {
      padding: 10px 0px
    }

    #onetrust-pc-sdk #ot-pc-desc,
    #onetrust-pc-sdk #accept-recommended-btn-handler,
    #onetrust-pc-sdk #ot-pc-title,
    #onetrust-pc-sdk #ot-category-title,
    #onetrust-pc-sdk .ot-cat-header {
      float: left
    }

    #onetrust-pc-sdk #ot-category-title {
      width: 100%;
      text-align: left
    }

    #onetrust-pc-sdk #ot-pc-title,
    #onetrust-pc-sdk #ot-category-title,
    #onetrust-pc-sdk .ot-cat-header,
    #onetrust-pc-sdk #ot-lst-title,
    #onetrust-pc-sdk .ot-ven-hdr .ot-ven-name,
    #onetrust-pc-sdk .ot-always-active {
      font-weight: bold
    }

    #onetrust-pc-sdk a {
      color: #656565;
      cursor: pointer
    }

    #onetrust-pc-sdk a:hover {
      color: #3860be
    }

    #onetrust-pc-sdk .ot-always-active {
      float: right;
      color: #3860be;
      margin-top: -2px
    }

    #onetrust-pc-sdk .ot-cat-header,
    #onetrust-pc-sdk .ot-always-active {
      font-size: .88em;
      line-height: 1.4;
      position: relative
    }

    #onetrust-pc-sdk .ot-cat-item {
      margin-top: 25px;
      line-height: 1.1
    }

    #onetrust-pc-sdk .ot-cat-item p:last-of-type {
      clear: both;
      padding-top: 15px;
      margin: 0
    }

    #onetrust-pc-sdk .ot-acc-txt p ul,
    #onetrust-pc-sdk .ot-cat-item ul,
    #onetrust-pc-sdk li.ot-subgrp p ul {
      margin: 0px;
      list-style: disc;
      margin-left: 15px
    }

    #onetrust-pc-sdk .ot-acc-txt p ul li,
    #onetrust-pc-sdk .ot-cat-item ul li,
    #onetrust-pc-sdk li.ot-subgrp p ul li {
      font-size: inherit;
      margin: 5px 0 0 0;
      padding: 0;
      border: none
    }

    #onetrust-pc-sdk .ot-subgrp-cntr {
      display: inline-block;
      width: 100%
    }

    #onetrust-pc-sdk .ot-subgrp-cntr .ot-tgl-cntr {
      float: right
    }

    #onetrust-pc-sdk .ot-subgrp-cntr .ot-tgl-cntr.ot-always-active-subgroup {
      width: auto
    }

    #onetrust-pc-sdk .ot-subgrp-cntr ul.ot-subgrps li p,
    #onetrust-pc-sdk .ot-subgrp-cntr ul.ot-subgrps li h5 {
      font-size: .813em;
      line-height: 1.5;
      color: dimgray
    }

    #onetrust-pc-sdk .ot-subgrp-cntr ul.ot-subgrps .ot-acc-hdr {
      display: inline-block;
      width: 100%;
      vertical-align: middle
    }

    #onetrust-pc-sdk .ot-subgrp-cntr ul.ot-subgrps .ot-acc-txt {
      margin: 0
    }

    #onetrust-pc-sdk .ot-subgrp-cntr ul.ot-subgrps li {
      margin: 10px 0 0 0;
      padding: 0;
      border: none
    }

    #onetrust-pc-sdk .ot-subgrp-cntr ul.ot-subgrps li p {
      clear: both;
      float: left;
      padding-top: 10px;
      margin: 0
    }

    #onetrust-pc-sdk .ot-subgrp-cntr ul.ot-subgrps li h5 {
      font-weight: bold;
      margin-bottom: 0;
      float: left;
      position: relative;
      top: 6px
    }

    #onetrust-pc-sdk .ot-subgrp-cntr ul.ot-subgrps li.ot-subgrp {
      margin-left: 20px;
      overflow: hidden
    }

    #onetrust-pc-sdk .ot-subgrp-cntr ul.ot-subgrps li.ot-subgrp>p ul:first-child {
      padding-bottom: 7.5px
    }

    #onetrust-pc-sdk ul.ot-subgrps {
      margin: 0
    }

    #onetrust-pc-sdk .ot-hlst-cntr,
    #onetrust-pc-sdk .ot-vlst-cntr {
      margin-top: 3px;
      overflow: hidden;
      clear: both;
      padding-left: 2px;
      padding-bottom: 2px
    }

    #onetrust-pc-sdk .ot-always-active-subgroup {
      width: auto;
      padding-left: 0px !important;
      top: 3px;
      position: relative
    }

    #onetrust-pc-sdk .category-vendors-list-handler,
    #onetrust-pc-sdk .category-vendors-list-handler+a,
    #onetrust-pc-sdk .category-host-list-handler {
      color: #3860be;
      margin-left: 0;
      font-size: .813em;
      text-decoration: none;
      float: left;
      margin-top: 5px
    }

    #onetrust-pc-sdk .category-vendors-list-handler:hover,
    #onetrust-pc-sdk .category-vendors-list-handler+a:hover,
    #onetrust-pc-sdk .category-host-list-handler:hover {
      color: #1883fd
    }

    #onetrust-pc-sdk .category-vendors-list-handler+a {
      clear: none
    }

    #onetrust-pc-sdk .category-vendors-list-handler+a::after {
      content: "";
      height: 15px;
      width: 15px;
      background-repeat: no-repeat;
      margin-left: 5px;
      float: right;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 511.626 511.627'%3E%3Cg fill='%231276CE'%3E%3Cpath d='M392.857 292.354h-18.274c-2.669 0-4.859.855-6.563 2.573-1.718 1.708-2.573 3.897-2.573 6.563v91.361c0 12.563-4.47 23.315-13.415 32.262-8.945 8.945-19.701 13.414-32.264 13.414H82.224c-12.562 0-23.317-4.469-32.264-13.414-8.945-8.946-13.417-19.698-13.417-32.262V155.31c0-12.562 4.471-23.313 13.417-32.259 8.947-8.947 19.702-13.418 32.264-13.418h200.994c2.669 0 4.859-.859 6.57-2.57 1.711-1.713 2.566-3.9 2.566-6.567V82.221c0-2.662-.855-4.853-2.566-6.563-1.711-1.713-3.901-2.568-6.57-2.568H82.224c-22.648 0-42.016 8.042-58.102 24.125C8.042 113.297 0 132.665 0 155.313v237.542c0 22.647 8.042 42.018 24.123 58.095 16.086 16.084 35.454 24.13 58.102 24.13h237.543c22.647 0 42.017-8.046 58.101-24.13 16.085-16.077 24.127-35.447 24.127-58.095v-91.358c0-2.669-.856-4.859-2.574-6.57-1.713-1.718-3.903-2.573-6.565-2.573z'/%3E%3Cpath d='M506.199 41.971c-3.617-3.617-7.905-5.424-12.85-5.424H347.171c-4.948 0-9.233 1.807-12.847 5.424-3.617 3.615-5.428 7.898-5.428 12.847s1.811 9.233 5.428 12.85l50.247 50.248-186.147 186.151c-1.906 1.903-2.856 4.093-2.856 6.563 0 2.479.953 4.668 2.856 6.571l32.548 32.544c1.903 1.903 4.093 2.852 6.567 2.852s4.665-.948 6.567-2.852l186.148-186.148 50.251 50.248c3.614 3.617 7.898 5.426 12.847 5.426s9.233-1.809 12.851-5.426c3.617-3.616 5.424-7.898 5.424-12.847V54.818c-.001-4.952-1.814-9.232-5.428-12.847z'/%3E%3C/g%3E%3C/svg%3E")
    }

    #onetrust-pc-sdk .back-btn-handler {
      min-height: 20px;
      font-size: 1em;
      text-decoration: none
    }

    #onetrust-pc-sdk .back-btn-handler svg {
      width: 12px;
      height: 12px
    }

    #onetrust-pc-sdk .back-btn-handler:hover {
      opacity: .6
    }

    #onetrust-pc-sdk #ot-lst-title span {
      word-break: break-word;
      word-wrap: break-word;
      margin-bottom: 0;
      color: #656565;
      font-weight: bold;
      margin-left: 15px
    }

    #onetrust-pc-sdk #ot-lst-title {
      margin-top: 15px;
      font-size: 1em
    }

    #onetrust-pc-sdk #ot-pc-hdr {
      display: inline-block;
      padding-left: 27px;
      padding-right: 17px;
      width: calc(100% - 44px)
    }

    #onetrust-pc-sdk #ot-pc-hdr input::placeholder {
      color: #d4d4d4;
      font-style: italic
    }

    #onetrust-pc-sdk #vendor-search-handler {
      height: 31px;
      width: 100%;
      border-radius: 50px;
      font-size: .8em;
      padding-right: 35px;
      padding-left: 15px;
      float: left;
      margin-left: 15px
    }

    #onetrust-pc-sdk #ot-lst-cnt {
      transform: translate3d(0, 0, 0);
      position: relative;
      padding-left: 27px;
      margin-right: 10px;
      margin-top: 10px;
      width: calc(100% - 37px);
      top: 0;
      bottom: 70px;
      height: calc(100% - 94px);
      overflow-y: auto;
      overflow-x: hidden
    }

    #onetrust-pc-sdk #ot-pc-lst .ot-tgl-cntr {
      right: 32px;
      position: absolute;
      margin-top: 10px;
      height: 20px
    }

    #onetrust-pc-sdk #ot-pc-lst:not(.ot-enbl-chr):not(.ot-hosts-ui) .ot-tgl-cntr {
      right: 12px
    }

    #onetrust-pc-sdk #ot-sel-blk {
      position: sticky;
      position: -webkit-sticky;
      width: 100%;
      display: inline-block;
      top: 0;
      overflow: hidden;
      z-index: 3
    }

    #onetrust-pc-sdk #ot-sel-blk p {
      font-size: .75em;
      color: #6b6b6b;
      margin: 0;
      display: inline-block
    }

    #onetrust-pc-sdk .ot-enbl-chr>:not(.ot-hosts-ui) .ot-sel-all {
      padding-right: 33px
    }

    #onetrust-pc-sdk .ot-enbl-chr .ot-hosts-ui .ot-sel-all {
      padding-right: 23px
    }

    #onetrust-pc-sdk .ot-enbl-chr .ot-accordion-layout h4~.ot-tgl,
    #onetrust-pc-sdk .ot-enbl-chr .ot-accordion-layout h4~.ot-always-active {
      right: 30px
    }

    #onetrust-pc-sdk .ot-enbl-chr .ot-accordion-layout h4~.ot-tgl+.ot-tgl {
      right: 105px
    }

    #onetrust-pc-sdk .ot-enbl-chr .ot-cat-item h5+.ot-tgl-cntr,
    #onetrust-pc-sdk .ot-enbl-chr .ot-cat-item .ot-cat-header+.ot-tgl {
      padding-left: 31px;
      padding-right: 13px
    }

    #onetrust-pc-sdk #ot-pc-lst:not(.ot-enbl-chr) .ot-tgl-cntr .ot-arw-cntr,
    #onetrust-pc-sdk #ot-pc-lst:not(.ot-enbl-chr) .ot-tgl-cntr .ot-arw-cntr * {
      visibility: hidden
    }

    #onetrust-pc-sdk #ot-pc-content:not(.ot-enbl-chr) .ot-pli-hdr.ot-leg-border-color span:first-child {
      text-align: center
    }

    #onetrust-pc-sdk #ot-pc-content:not(.ot-enbl-chr) .ot-pli-hdr.ot-leg-border-color span:last-child {
      text-align: right
    }

    #onetrust-pc-sdk .ot-hosts-ui:not(.ot-enbl-chr) .ot-tgl-cntr {
      right: 23px
    }

    #onetrust-pc-sdk .ot-hosts-ui #ot-sel-blk {
      width: 100%
    }

    #onetrust-pc-sdk .ot-lst-subhdr {
      display: inline-block;
      width: 100%;
      margin-top: 10px
    }

    #onetrust-pc-sdk .ot-lst-subhdr svg {
      width: 30px;
      height: 30px;
      position: absolute;
      float: left;
      right: -15px
    }

    #onetrust-pc-sdk .ot-search-cntr {
      float: left;
      width: 82%;
      position: relative
    }

    #onetrust-pc-sdk .ot-fltr-cntr {
      float: right;
      right: 15px;
      position: relative
    }

    #onetrust-pc-sdk #filter-btn-handler {
      background-color: #3860be;
      border-radius: 17px;
      display: inline-block;
      position: relative;
      width: 32px;
      height: 32px;
      -moz-transition: .1s ease;
      -o-transition: .1s ease;
      -webkit-transition: 1s ease;
      transition: .1s ease;
      padding: 0;
      margin: 0
    }

    #onetrust-pc-sdk #filter-btn-handler:hover {
      opacity: .7
    }

    #onetrust-pc-sdk #filter-btn-handler svg {
      width: 12px;
      margin: 6px 10px 0 9px;
      display: block;
      height: 12px;
      position: static;
      right: auto;
      top: auto
    }

    #onetrust-pc-sdk .ot-ven-link {
      color: #3860be;
      text-decoration: none;
      display: inline-block;
      margin-top: 10px;
      transform: translate(0, 1%);
      -o-transform: translate(0, 1%);
      -ms-transform: translate(0, 1%);
      -webkit-transform: translate(0, 1%);
      z-index: 2;
      position: relative;
      font-size: .75em
    }

    #onetrust-pc-sdk .ot-ven-link:hover {
      text-decoration: underline
    }

    #onetrust-pc-sdk .ot-ven-link * {
      font-size: inherit
    }

    #onetrust-pc-sdk .ot-ven-name {
      vertical-align: middle
    }

    #onetrust-pc-sdk .ot-ven-hdr,
    #onetrust-pc-sdk .ot-host-hdr {
      width: calc(100% - 165px);
      height: auto;
      float: left;
      text-align: left;
      word-break: break-word;
      word-wrap: break-word;
      vertical-align: middle;
      padding-bottom: 2px;
      padding-left: 2px
    }

    #onetrust-pc-sdk #ot-host-lst .ot-host-info {
      font-size: .7em;
      line-height: 1.1
    }

    #onetrust-pc-sdk #ot-host-lst .ot-host-name,
    #onetrust-pc-sdk #ot-host-lst .ot-host-name a {
      color: dimgray;
      font-size: .81em;
      font-weight: bold;
      line-height: 1.4;
      margin-bottom: 5px;
      position: relative
    }

    #onetrust-pc-sdk #ot-host-lst .ot-host-name a,
    #onetrust-pc-sdk #ot-host-lst .ot-host-info a {
      font-size: 1em
    }

    #onetrust-pc-sdk #ot-host-lst .ot-host-desc {
      width: 100%;
      margin-bottom: 5px
    }

    #onetrust-pc-sdk #ot-host-lst .ot-host-expand,
    #onetrust-pc-sdk #ot-host-lst .ot-host-desc {
      color: dimgray;
      font-size: .69em;
      line-height: 1.4;
      float: left;
      font-weight: normal
    }

    #onetrust-pc-sdk #ot-host-lst .ot-host-hdr>a {
      text-decoration: underline;
      font-size: .69em;
      position: relative;
      z-index: 2;
      float: left;
      width: 100%;
      margin-bottom: 5px;
      line-height: 1.4
    }

    #onetrust-pc-sdk #ot-host-lst .ot-host-expand {
      color: #3860be
    }

    #onetrust-pc-sdk #ot-host-lst .ot-host-expand * {
      font-size: inherit
    }

    #onetrust-pc-sdk .ot-host-opt {
      margin: 0;
      font-size: inherit;
      display: inline-block;
      width: 100%
    }

    #onetrust-pc-sdk .ot-host-opt .ot-host-info {
      border: none;
      font-size: .8em;
      color: dimgray;
      display: inline-block;
      width: calc(100% - 20px);
      padding: 10px;
      margin-bottom: 10px;
      background-color: #f8f8f8
    }

    #onetrust-pc-sdk .ot-host-opt li>div div {
      font-size: .8em;
      padding: 5px 0
    }

    #onetrust-pc-sdk .ot-host-opt li>div div:nth-child(1) {
      width: 30%;
      float: left
    }

    #onetrust-pc-sdk .ot-host-opt li>div div:nth-child(2) {
      width: 70%;
      float: left;
      word-break: break-word;
      word-wrap: break-word
    }

    #onetrust-pc-sdk #ot-ven-lst .ot-acc-hdr {
      overflow: hidden;
      cursor: pointer
    }

    #onetrust-pc-sdk .ot-ven-dets {
      border-radius: 2px;
      margin-top: 10px;
      background-color: #f8f8f8
    }

    #onetrust-pc-sdk .ot-ven-dets div:first-child p:first-child {
      border-top: none
    }

    #onetrust-pc-sdk .ot-ven-dets p {
      font-size: .69em;
      color: dimgray;
      text-align: left;
      vertical-align: middle;
      word-break: break-word;
      word-wrap: break-word;
      margin: 0;
      padding-bottom: 10px;
      padding-left: 15px;
      color: #2e3644
    }

    #onetrust-pc-sdk .ot-ven-dets p:first-child {
      border-top: 1px solid #e9e9e9;
      border-bottom: 1px solid #e9e9e9;
      padding-top: 5px;
      padding-bottom: 5px;
      margin-bottom: 5px;
      font-weight: bold
    }

    #onetrust-pc-sdk #no-results {
      text-align: center;
      margin-top: 30px;
      height: calc(100% - 300px);
      margin-left: 27px
    }

    #onetrust-pc-sdk #no-results span {
      font-weight: bold
    }

    #onetrust-pc-sdk #no-results p {
      font-size: 1em;
      color: #2e3644;
      word-break: break-word;
      word-wrap: break-word
    }

    #onetrust-pc-sdk #ot-fltr-modal {
      right: 13px;
      top: 87px;
      height: 90%;
      max-height: 370px;
      display: none;
      -moz-transition: .2s ease;
      -o-transition: .2s ease;
      -webkit-transition: 2s ease;
      transition: .2s ease;
      opacity: 1;
      position: absolute
    }

    #onetrust-pc-sdk #ot-fltr-cnt {
      z-index: 2147483646;
      background-color: #fff;
      position: relative;
      height: 100%;
      max-width: 325px;
      border-radius: 3px;
      padding-right: 10px;
      padding-bottom: 5px;
      -webkit-box-shadow: 0px 0px 12px 2px #c7c5c7;
      -moz-box-shadow: 0px 0px 12px 2px #c7c5c7;
      box-shadow: 0px 0px 12px 2px #c7c5c7
    }

    #onetrust-pc-sdk .ot-fltr-scrlcnt {
      overflow-y: auto;
      overflow-x: hidden;
      clear: both;
      max-height: calc(100% - 50px)
    }

    #onetrust-pc-sdk #ot-anchor {
      border: 12px solid transparent;
      display: none;
      position: absolute;
      z-index: 2147483647;
      right: 36px;
      top: 75px;
      transform: rotate(45deg);
      -o-transform: rotate(45deg);
      -ms-transform: rotate(45deg);
      -webkit-transform: rotate(45deg);
      background-color: #fff;
      -webkit-box-shadow: -3px -3px 5px -2px #c7c5c7;
      -moz-box-shadow: -3px -3px 5px -2px #c7c5c7;
      box-shadow: -3px -3px 5px -2px #c7c5c7
    }

    #onetrust-pc-sdk .ot-fltr-btns {
      margin-left: 15px
    }

    #onetrust-pc-sdk #filter-apply-handler {
      margin-right: 15px
    }

    #onetrust-pc-sdk .ot-fltr-opt {
      margin-bottom: 20px;
      margin-left: 15px;
      width: 75%
    }

    #onetrust-pc-sdk .ot-fltr-opt label {
      padding-left: 30px
    }

    #onetrust-pc-sdk .ot-fltr-opt p {
      display: inline-block;
      margin: 0;
      font-size: .9em;
      color: #2e3644
    }

    #onetrust-pc-sdk #ot-sel-blk .ot-chkbox {
      width: 20px;
      height: 20px;
      float: right
    }

    #onetrust-pc-sdk .line-through label::after,
    #onetrust-pc-sdk[dir=rtl] .line-through label::after {
      height: auto;
      border-left: 0;
      transform: none;
      -o-transform: none;
      -ms-transform: none;
      -webkit-transform: none;
      left: 5px;
      top: 8px
    }

    #onetrust-pc-sdk #ot-selall-vencntr label,
    #onetrust-pc-sdk #ot-selall-adtlvencntr label,
    #onetrust-pc-sdk #ot-selall-hostcntr label,
    #onetrust-pc-sdk #ot-selall-licntr label {
      position: relative;
      display: inline-block;
      width: 20px;
      height: 20px;
      margin: 0
    }

    #onetrust-pc-sdk #ot-selall-vencntr input,
    #onetrust-pc-sdk #ot-selall-adtlvencntr input,
    #onetrust-pc-sdk #ot-selall-hostcntr input,
    #onetrust-pc-sdk #ot-selall-licntr input {
      height: auto;
      width: auto;
      border-radius: 0;
      font-size: initial;
      padding: 0;
      float: none
    }

    #onetrust-pc-sdk #ot-ven-lst:first-child {
      border-top: 1px solid #e2e2e2
    }

    #onetrust-pc-sdk #close-pc-btn-handler {
      margin-top: 15px;
      margin-right: 20px;
      float: right;
      height: 10px;
      width: 10px
    }

    #onetrust-pc-sdk #close-pc-btn-handler svg {
      display: block
    }

    #onetrust-pc-sdk #close-pc-btn-handler:hover {
      opacity: .7
    }

    #onetrust-pc-sdk .ot-close-icon {
      padding: 0;
      background-color: transparent;
      border: none
    }

    #onetrust-pc-sdk #clear-filters-handler {
      float: right;
      max-width: 200px;
      margin-bottom: 10px;
      text-decoration: none;
      margin-top: 20px;
      font-weight: bold;
      color: #3860be;
      font-size: .9em;
      letter-spacing: normal;
      border: none;
      padding: 1px
    }

    #onetrust-pc-sdk #clear-filters-handler:hover {
      color: #2285f7
    }

    #onetrust-pc-sdk #clear-filters-handler:focus {
      outline: #3860be solid 1px
    }

    #onetrust-pc-sdk .ot-accordion-layout.ot-cat-item {
      position: relative;
      border-radius: 1px;
      margin: 0;
      padding: 0;
      border: 1px solid #d8d8d8;
      border-top: none;
      float: left;
      width: calc(100% - 2px)
    }

    #onetrust-pc-sdk .ot-accordion-layout.ot-cat-item:first-of-type {
      margin-top: 10px;
      border-top: 1px solid #d8d8d8
    }

    #onetrust-pc-sdk .ot-accordion-layout.ot-cat-item .ot-vlst-cntr:first-child {
      margin-top: 15px
    }

    #onetrust-pc-sdk .ot-accordion-layout.ot-cat-item .ot-acc-grpdesc {
      font-size: .813em;
      line-height: 1.5;
      padding-left: 20px;
      padding-right: 20px;
      width: calc(100% - 40px)
    }

    #onetrust-pc-sdk .ot-accordion-layout.ot-cat-item .ot-acc-grpdesc ul {
      padding-bottom: 5px
    }

    #onetrust-pc-sdk .ot-accordion-layout .ot-acc-hdr {
      padding-top: 11.5px;
      padding-bottom: 11.5px;
      padding-left: 20px;
      padding-right: 15px;
      width: calc(100% - 35px);
      display: inline-block;
      position: relative;
      min-height: 25px
    }

    #onetrust-pc-sdk .ot-accordion-layout .ot-acc-txt {
      width: 100%;
      padding: 0px
    }

    #onetrust-pc-sdk .ot-accordion-layout .ot-subgrp-cntr,
    #onetrust-pc-sdk .ot-accordion-layout .ot-acc-grpdesc+.ot-leg-btn-container {
      padding-left: 20px;
      padding-right: 16px;
      width: calc(100% - 36px)
    }

    #onetrust-pc-sdk .ot-accordion-layout .ot-acc-grpdesc+.ot-leg-btn-container {
      margin-top: 5px;
      margin-bottom: 10px
    }

    #onetrust-pc-sdk .ot-accordion-layout .ot-acc-grpcntr {
      z-index: 1;
      position: relative
    }

    #onetrust-pc-sdk .ot-accordion-layout input[type=checkbox]:checked~.ot-acc-txt.ot-acc-grpcntr {
      width: auto;
      padding-bottom: 15px
    }

    #onetrust-pc-sdk .ot-accordion-layout .ot-cat-header {
      float: none;
      font-size: .88em;
      color: #2e3644;
      margin: 0;
      display: inline-block;
      height: auto;
      word-wrap: break-word;
      vertical-align: middle;
      min-height: inherit
    }

    #onetrust-pc-sdk .ot-accordion-layout .ot-vlst-cntr,
    #onetrust-pc-sdk .ot-accordion-layout .ot-hlst-cntr {
      padding-left: 20px;
      width: calc(100% - 20px);
      display: inline-block;
      margin-top: 0;
      padding-bottom: 2px
    }

    #onetrust-pc-sdk .ot-accordion-layout h4~.ot-tgl,
    #onetrust-pc-sdk .ot-accordion-layout h4~.ot-always-active {
      right: 16px
    }

    #onetrust-pc-sdk .ot-accordion-layout h4~.ot-tgl+.ot-tgl {
      right: 88px
    }

    #onetrust-pc-sdk .ot-accordion-layout .ot-cat-header+.ot-arw-cntr {
      right: 10px;
      margin-top: -2px;
      left: auto
    }

    #onetrust-pc-sdk .ot-accordion-layout h4~.ot-tgl,
    #onetrust-pc-sdk .ot-accordion-layout h4~.ot-always-active,
    #onetrust-pc-sdk .ot-accordion-layout .ot-cat-header+.ot-arw-cntr {
      position: absolute;
      top: 50%;
      transform: translateY(-50%)
    }

    #onetrust-pc-sdk #ot-category-title {
      padding-bottom: 10px
    }

    #onetrust-pc-sdk .ot-pli-hdr {
      color: #77808e;
      overflow: hidden;
      padding-top: 7.5px;
      padding-bottom: 7.5px;
      width: calc(100% - 2px);
      border-top-left-radius: 3px;
      border-top-right-radius: 3px
    }

    #onetrust-pc-sdk .ot-pli-hdr .ot-li-title {
      float: right;
      font-size: 13px
    }

    #onetrust-pc-sdk .ot-pli-hdr span:first-child {
      top: 50%;
      transform: translateY(50%);
      max-width: 80px
    }

    #onetrust-pc-sdk .ot-pli-hdr span:last-child {
      text-align: center;
      max-width: 95px
    }

    #onetrust-pc-sdk .ot-pli-hdr.ot-leg-border-color {
      background-color: #f8f8f8;
      border: 1px solid #e9e9e9
    }

    #onetrust-pc-sdk .ot-pli-hdr.ot-leg-border-color span:first-child {
      text-align: left;
      width: 80px
    }

    #onetrust-pc-sdk .ot-subgrp>h5,
    #onetrust-pc-sdk .ot-cat-header {
      width: calc(100% - 130px);
      max-width: 60%
    }

    #onetrust-pc-sdk .ot-pli-hdr~.ot-cat-item .ot-subgrp>h5,
    #onetrust-pc-sdk .ot-pli-hdr~.ot-cat-item .ot-cat-header {
      width: calc(100% - 145px)
    }

    #onetrust-pc-sdk .ot-pli-hdr~.ot-cat-item h5+.ot-tgl-cntr,
    #onetrust-pc-sdk .ot-pli-hdr~.ot-cat-item .ot-cat-header+.ot-tgl {
      padding-left: 28px
    }

    #onetrust-pc-sdk .ot-acc-grpcntr .ot-acc-grpdesc {
      margin-bottom: 5px
    }

    #onetrust-pc-sdk .ot-acc-grpcntr .ot-subgrp-cntr {
      border-top: 1px solid #e9e9e9
    }

    #onetrust-pc-sdk .ot-acc-grpcntr .ot-subgrp-cntr ul.ot-subgrps li {
      margin-top: 5px;
      margin-bottom: 5px
    }

    #onetrust-pc-sdk .ot-acc-hdr .ot-arw-cntr+.ot-tgl,
    #onetrust-pc-sdk .ot-cat-item h4+.ot-tgl,
    #onetrust-pc-sdk .ot-acc-txt h4+.ot-tgl-cntr {
      padding-left: 30px
    }

    #onetrust-pc-sdk .ot-sel-all-hdr,
    #onetrust-pc-sdk .ot-sel-all-chkbox {
      position: relative;
      display: inline-block;
      width: 100%
    }

    #onetrust-pc-sdk .ot-li-hdr,
    #onetrust-pc-sdk .ot-consent-hdr {
      float: right;
      font-size: .813em;
      line-height: normal;
      text-align: center;
      word-break: break-word;
      word-wrap: break-word
    }

    #onetrust-pc-sdk .ot-li-hdr {
      max-width: 100px;
      min-width: 100px
    }

    #onetrust-pc-sdk .ot-consent-hdr {
      margin-right: 5px;
      max-width: 55px
    }

    #onetrust-pc-sdk .ot-ven-litgl+.ot-arw-cntr {
      margin-left: 81px
    }

    #onetrust-pc-sdk .ot-sel-all {
      margin: 0;
      position: relative;
      padding-right: 13px;
      float: right
    }

    #onetrust-pc-sdk #ot-selall-hostcntr,
    #onetrust-pc-sdk #ot-selall-vencntr {
      right: 20px;
      position: relative
    }

    #onetrust-pc-sdk #ot-selall-licntr {
      position: relative;
      right: 79px
    }

    #onetrust-pc-sdk #ot-pc-lst #ot-ven-lst .ot-sel-all {
      position: relative;
      display: inline-block;
      width: 20px;
      height: 25px
    }

    #onetrust-pc-sdk #ot-pc-lst #ot-ven-lst .ot-sel-all label {
      position: absolute;
      padding: 0;
      width: 18px;
      height: 18px
    }

    #onetrust-pc-sdk .ot-always-active-group .ot-cat-header {
      width: 55%
    }

    #onetrust-pc-sdk .ot-leg-btn-container {
      display: inline-block;
      width: 100%;
      margin-top: 10px
    }

    #onetrust-pc-sdk .ot-leg-btn-container button {
      height: 32px;
      padding: 6.5px 8px;
      margin-bottom: 0;
      letter-spacing: 0
    }

    #onetrust-pc-sdk .ot-leg-btn-container svg {
      display: none;
      height: 14px;
      width: 14px;
      padding-right: 5px;
      vertical-align: sub
    }

    #onetrust-pc-sdk .ot-active-leg-btn {
      cursor: default;
      pointer-events: none
    }

    #onetrust-pc-sdk .ot-active-leg-btn svg {
      display: inline-block
    }

    #onetrust-pc-sdk .ot-remove-objection-handler {
      border: none;
      text-decoration: underline;
      padding: 0;
      font-size: .82em;
      font-weight: 600;
      line-height: 1.4;
      padding-left: 10px
    }

    #onetrust-pc-sdk .ot-obj-leg-btn-handler span {
      font-weight: bold;
      text-align: center;
      font-size: .91em;
      line-height: 1.5
    }

    #onetrust-pc-sdk[dir=rtl] input~.ot-acc-hdr .ot-arw,
    #onetrust-pc-sdk[dir=rtl] #ot-back-arw {
      transform: rotate(180deg);
      -o-transform: rotate(180deg);
      -ms-transform: rotate(180deg);
      -webkit-transform: rotate(180deg)
    }

    #onetrust-pc-sdk[dir=rtl] input:checked~.ot-acc-hdr .ot-arw {
      transform: rotate(270deg);
      -o-transform: rotate(270deg);
      -ms-transform: rotate(270deg);
      -webkit-transform: rotate(270deg)
    }

    #onetrust-pc-sdk[dir=rtl] .ot-chkbox label::after {
      transform: rotate(45deg);
      -webkit-transform: rotate(45deg);
      -o-transform: rotate(45deg);
      -ms-transform: rotate(45deg);
      border-left: 0;
      border-right: 3px solid
    }

    #onetrust-pc-sdk[dir=rtl] .ot-lst-subhdr svg {
      right: 0
    }

    #onetrust-pc-sdk .ot-ven-ctgl,
    #onetrust-pc-sdk .ot-ven-litgl,
    #onetrust-pc-sdk .ot-host-tgl {
      position: relative;
      display: inline-block;
      width: 20px;
      height: 20px;
      margin-left: 60px
    }

    #onetrust-pc-sdk .ot-ven-ctgl label,
    #onetrust-pc-sdk .ot-ven-litgl label,
    #onetrust-pc-sdk .ot-host-tgl label {
      position: absolute;
      width: 20px;
      height: 20px;
      margin: 0
    }

    #onetrust-pc-sdk #ot-host-lst .ot-host-expand {
      float: none;
      display: inline-block
    }

    #onetrust-pc-sdk ul {
      list-style: none
    }

    #onetrust-pc-sdk ul li {
      position: relative;
      margin: 0;
      padding: 15px 15px 15px 15px;
      border-bottom: 1px solid #e2e2e2
    }

    #onetrust-pc-sdk ul li h3 {
      font-size: .75em;
      color: #656565;
      margin: 0;
      height: auto;
      word-break: break-word;
      word-wrap: break-word
    }

    #onetrust-pc-sdk ul li p {
      margin: 0;
      font-size: .7em
    }

    #onetrust-pc-sdk .ot-ven-item>input[type=checkbox],
    #onetrust-pc-sdk .ot-host-item>input[type=checkbox],
    #onetrust-pc-sdk .ot-accordion-layout.ot-cat-item>input[type=checkbox],
    #onetrust-pc-sdk .ot-acc-cntr>input[type=checkbox] {
      position: absolute;
      cursor: pointer;
      width: 100%;
      height: 100%;
      opacity: 0;
      margin: 0;
      top: 0;
      left: 0;
      z-index: 1
    }

    #onetrust-pc-sdk .ot-ven-item>input[type=checkbox]:not(:checked)~.ot-acc-txt,
    #onetrust-pc-sdk .ot-host-item>input[type=checkbox]:not(:checked)~.ot-acc-txt,
    #onetrust-pc-sdk .ot-accordion-layout.ot-cat-item>input[type=checkbox]:not(:checked)~.ot-acc-txt,
    #onetrust-pc-sdk .ot-acc-cntr>input[type=checkbox]:not(:checked)~.ot-acc-txt {
      margin-top: 0;
      max-height: 0;
      opacity: 0;
      overflow: hidden;
      width: 100%;
      transition: .25s ease-out;
      display: none
    }

    #onetrust-pc-sdk .ot-ven-item>input[type=checkbox]:checked~.ot-acc-txt,
    #onetrust-pc-sdk .ot-host-item>input[type=checkbox]:checked~.ot-acc-txt,
    #onetrust-pc-sdk .ot-accordion-layout.ot-cat-item>input[type=checkbox]:checked~.ot-acc-txt,
    #onetrust-pc-sdk .ot-acc-cntr>input[type=checkbox]:checked~.ot-acc-txt {
      transition: .1s ease-in;
      width: auto;
      overflow: auto;
      display: block
    }

    #onetrust-pc-sdk.ot-addtl-vendors .ot-enbl-chr #ot-selall-vencntr {
      right: 0
    }

    #onetrust-pc-sdk.ot-addtl-vendors>:not(.ot-enbl-chr) #ot-selall-licntr {
      right: 135px
    }

    #onetrust-pc-sdk.ot-addtl-vendors>:not(.ot-enbl-chr) #ot-selall-adtlvencntr {
      right: 40px
    }

    #onetrust-pc-sdk.ot-addtl-vendors .ot-li-hdr {
      margin-right: 15px
    }

    #onetrust-pc-sdk.ot-addtl-vendors #ot-selall-licntr {
      right: 115px
    }

    #onetrust-pc-sdk.ot-addtl-vendors #ot-lst-cnt:not(.ot-host-cnt) #ot-sel-blk {
      background-color: #f9f9fc;
      border: 1px solid #e2e2e2;
      width: auto;
      padding-bottom: 5px;
      padding-top: 5px
    }

    #onetrust-pc-sdk.ot-addtl-vendors #ot-lst-cnt:not(.ot-host-cnt) .ot-sel-all {
      padding-right: 23px
    }

    #onetrust-pc-sdk.ot-addtl-vendors #ot-lst-cnt:not(.ot-host-cnt) ul li {
      border: 1px solid #e2e2e2;
      margin-bottom: 10px
    }

    #onetrust-pc-sdk.ot-addtl-vendors #ot-lst-cnt:not(.ot-host-cnt) .ot-sel-all-chkbox {
      float: right;
      width: auto;
      right: 3px
    }

    #onetrust-pc-sdk.ot-addtl-vendors #ot-lst-cnt:not(.ot-host-cnt) .ot-tgl-cntr {
      right: 12px
    }

    #onetrust-pc-sdk.ot-addtl-vendors #ot-lst-cnt:not(.ot-host-cnt) .ot-ven-ctgl {
      margin-left: 75px
    }

    #onetrust-pc-sdk.ot-addtl-vendors #ot-lst-cnt:not(.ot-host-cnt) .ot-ven-litgl+.ot-arw-cntr {
      margin-left: 95px
    }

    #onetrust-pc-sdk.ot-addtl-vendors #ot-lst-cnt:not(.ot-host-cnt) .ot-acc-cntr>.ot-acc-hdr {
      padding-top: 10px;
      padding-bottom: 10px
    }

    #onetrust-pc-sdk.ot-addtl-vendors #ot-lst-cnt:not(.ot-host-cnt) #ot-addtl-venlst .ot-tgl-cntr {
      right: 32px
    }

    #onetrust-pc-sdk.ot-addtl-vendors #ot-ven-lst:first-child {
      border-top: none
    }

    #onetrust-pc-sdk.ot-addtl-vendors #ot-selall-vencntr {
      right: 40px;
      position: absolute
    }

    #onetrust-pc-sdk #ot-selall-adtlvencntr {
      position: relative;
      right: 20px
    }

    #onetrust-pc-sdk .ot-acc-cntr {
      position: relative;
      border-left: 1px solid #e2e2e2;
      border-right: 1px solid #e2e2e2;
      border-bottom: 1px solid #e2e2e2
    }

    #onetrust-pc-sdk .ot-acc-cntr>.ot-acc-hdr {
      background-color: #f9f9fc;
      padding-left: 15px;
      padding-top: 5px;
      padding-bottom: 5px;
      width: calc(100% - 15px)
    }

    #onetrust-pc-sdk .ot-acc-cntr>.ot-acc-hdr .ot-plus-minus {
      vertical-align: middle;
      top: auto
    }

    #onetrust-pc-sdk .ot-acc-cntr>.ot-acc-hdr .ot-arw-cntr {
      right: 10px;
      left: auto
    }

    #onetrust-pc-sdk .ot-acc-cntr>input[type=checkbox]:checked~.ot-acc-hdr {
      border-bottom: 1px solid #e2e2e2
    }

    #onetrust-pc-sdk .ot-acc-cntr>.ot-acc-txt {
      padding-left: 10px;
      padding-right: 10px;
      padding-top: 10px;
      position: relative;
      z-index: 1
    }

    #onetrust-pc-sdk .ot-acc-cntr .ot-addtl-venbox {
      display: none
    }

    #onetrust-pc-sdk .ot-vensec-title {
      font-size: .813em;
      vertical-align: middle;
      display: inline-block
    }

    @media only screen and (max-width: 600px) {
      #onetrust-pc-sdk {
        max-width: 100%;
        min-width: 100%
      }

      #onetrust-pc-sdk .ot-ftr-stacked button {
        width: 100%
      }

      #onetrust-pc-sdk #ot-lst-cnt,
      #onetrust-pc-sdk #ot-pc-hdr {
        margin-top: 0;
        padding: 0 5px 0 10px;
        width: calc(100% - 25px)
      }
    }

    @media only screen and (max-width: 425px) {
      #onetrust-pc-sdk.otPcPanel {
        left: 0;
        min-width: 100%;
        height: 100%;
        top: 0;
        border-radius: 0
      }

      #onetrust-pc-sdk #ot-host-lst .ot-chkbox {
        margin-left: 0
      }

      #onetrust-pc-sdk #ot-pc-content {
        margin: 0 10px 0 20px
      }

      #onetrust-pc-sdk p {
        font-size: .7em
      }

      #onetrust-pc-sdk .ot-tgl-cntr {
        width: auto
      }

      #onetrust-pc-sdk #vendor-search-handler {
        font-size: 1em
      }

      #onetrust-pc-sdk #ot-lst-cnt {
        height: calc(100% - 95px)
      }

      #onetrust-pc-sdk .ot-switch+p {
        max-width: 80%
      }

      #onetrust-pc-sdk button {
        letter-spacing: .01em
      }

      #onetrust-pc-sdk .save-preference-btn-handler {
        margin-top: 0
      }

      #onetrust-pc-sdk .ot-search-cntr {
        width: 75%
      }
    }

    @media only screen and (max-width: 320px) {
      #onetrust-pc-sdk #ot-fltr-cnt {
        margin-left: 15px
      }
    }

    @media only screen and (max-width: 896px)and (max-height: 425px)and (orientation: landscape) {
      #onetrust-pc-sdk {
        left: 0;
        top: 0;
        min-width: 100%;
        height: 100%;
        border-radius: 0
      }

      #onetrust-pc-sdk button {
        letter-spacing: .02em
      }

      #onetrust-pc-sdk #ot-anchor {
        left: initial;
        right: 50px
      }

      #onetrust-pc-sdk #ot-lst-title {
        margin-top: 12px
      }

      #onetrust-pc-sdk #ot-lst-title * {
        font-size: inherit
      }

      #onetrust-pc-sdk #ot-pc-hdr input {
        margin-right: 0;
        padding-right: 45px
      }

      #onetrust-pc-sdk .ot-switch+p {
        max-width: 85%
      }

      #onetrust-pc-sdk #ot-lst-cnt {
        max-height: none;
        overflow: initial
      }

      #onetrust-pc-sdk #ot-lst-cnt.no-results {
        height: auto
      }

      #onetrust-pc-sdk input {
        font-size: 1em !important
      }

      #onetrust-pc-sdk p {
        font-size: .6em
      }

      #onetrust-pc-sdk #ot-pc-lst {
        overflow: auto
      }

      #onetrust-pc-sdk .ot-pc-footer-logo {
        display: none
      }

      #onetrust-pc-sdk #ot-fltr-modal {
        width: 100%;
        height: 100%;
        max-height: none;
        top: 0
      }

      #onetrust-pc-sdk #ot-sel-blk {
        position: static
      }

      #onetrust-pc-sdk #ot-fltr-cnt {
        height: 250px;
        width: 100%
      }

      #onetrust-pc-sdk.ot-shw-fltr #ot-anchor {
        display: none !important
      }

      #onetrust-pc-sdk.ot-shw-fltr #ot-pc-lst {
        height: 100% !important;
        overflow: hidden;
        top: 0px
      }

      #onetrust-pc-sdk.ot-shw-fltr #ot-fltr-cnt {
        margin: 0;
        height: 100%;
        padding: 10px;
        top: 0;
        width: calc(100% - 20px);
        position: absolute;
        right: 0;
        left: 0;
        max-width: none
      }

      #onetrust-pc-sdk.ot-shw-fltr .ot-fltr-scrlcnt {
        max-height: calc(100% - 65px)
      }
    }

    #onetrust-consent-sdk #onetrust-pc-sdk,
    #onetrust-consent-sdk #ot-search-cntr,
    #onetrust-consent-sdk #onetrust-pc-sdk .ot-switch.ot-toggle,
    #onetrust-consent-sdk #onetrust-pc-sdk ot-grp-hdr1 .checkbox,
    #onetrust-consent-sdk #onetrust-pc-sdk #ot-pc-title:after,
    #onetrust-consent-sdk #onetrust-pc-sdk #ot-sel-blk,
    #onetrust-consent-sdk #onetrust-pc-sdk #ot-fltr-cnt,
    #onetrust-consent-sdk #onetrust-pc-sdk #ot-anchor {
      background-color: #FFFFFF;
    }

    #onetrust-consent-sdk #onetrust-pc-sdk h3,
    #onetrust-consent-sdk #onetrust-pc-sdk h4,
    #onetrust-consent-sdk #onetrust-pc-sdk h5,
    #onetrust-consent-sdk #onetrust-pc-sdk h6,
    #onetrust-consent-sdk #onetrust-pc-sdk p,
    #onetrust-consent-sdk #onetrust-pc-sdk #ot-ven-lst .ot-ven-opts p,
    #onetrust-consent-sdk #onetrust-pc-sdk #ot-pc-desc,
    #onetrust-consent-sdk #onetrust-pc-sdk #ot-pc-title,
    #onetrust-consent-sdk #onetrust-pc-sdk .ot-li-title,
    #onetrust-consent-sdk #onetrust-pc-sdk .ot-sel-all-hdr span,
    #onetrust-consent-sdk #onetrust-pc-sdk #ot-host-lst .ot-host-info,
    #onetrust-consent-sdk #onetrust-pc-sdk #ot-fltr-modal #modal-header,
    #onetrust-consent-sdk #onetrust-pc-sdk .ot-checkbox label span,
    #onetrust-consent-sdk #onetrust-pc-sdk #ot-pc-lst #ot-sel-blk p,
    #onetrust-consent-sdk #onetrust-pc-sdk #ot-pc-lst #ot-lst-title span,
    #onetrust-consent-sdk #onetrust-pc-sdk #ot-pc-lst .back-btn-handler p,
    #onetrust-consent-sdk #onetrust-pc-sdk #ot-pc-lst .ot-ven-name,
    #onetrust-consent-sdk #onetrust-pc-sdk #ot-pc-lst #ot-ven-lst .consent-category,
    #onetrust-consent-sdk #onetrust-pc-sdk .ot-leg-btn-container .ot-inactive-leg-btn,
    #onetrust-consent-sdk #onetrust-pc-sdk .ot-label-status,
    #onetrust-consent-sdk #onetrust-pc-sdk .ot-chkbox label span,
    #onetrust-consent-sdk #onetrust-pc-sdk #clear-filters-handler {
      color: #000000;
    }

    #onetrust-consent-sdk #onetrust-pc-sdk .privacy-notice-link,
    #onetrust-consent-sdk #onetrust-pc-sdk .category-vendors-list-handler,
    #onetrust-consent-sdk #onetrust-pc-sdk .category-vendors-list-handler+a,
    #onetrust-consent-sdk #onetrust-pc-sdk .category-host-list-handler,
    #onetrust-consent-sdk #onetrust-pc-sdk .ot-ven-link,
    #onetrust-consent-sdk #onetrust-pc-sdk #ot-host-lst .ot-host-name a,
    #onetrust-consent-sdk #onetrust-pc-sdk #ot-host-lst .ot-acc-hdr .ot-host-expand,
    #onetrust-consent-sdk #onetrust-pc-sdk #ot-host-lst .ot-host-info a {
      color: #3860BE;
    }

    #onetrust-consent-sdk #onetrust-banner-sdk a[href] {
      color: #3860BE;
    }

    #onetrust-consent-sdk #onetrust-pc-sdk .category-vendors-list-handler:hover {
      opacity: .7;
    }

    #onetrust-consent-sdk #onetrust-pc-sdk button:not(#clear-filters-handler):not(.ot-close-icon):not(#filter-btn-handler):not(.ot-remove-objection-handler):not(.ot-obj-leg-btn-handler),
    #onetrust-consent-sdk #onetrust-pc-sdk .ot-leg-btn-container .ot-active-leg-btn {
      background-color: #000000;
      border-color: #000000;
      color: #FFFFFF;
    }

    #onetrust-consent-sdk #onetrust-pc-sdk .ot-active-menu {
      border-color: #000000;
    }

    #onetrust-consent-sdk #onetrust-pc-sdk .ot-leg-btn-container .ot-remove-objection-handler {
      background-color: transparent;
      border: 1px solid transparent;
    }

    #onetrust-consent-sdk #onetrust-pc-sdk .ot-leg-btn-container .ot-inactive-leg-btn {
      background-color: #FFFFFF;
      color: #78808E;
      border-color: #78808E;
    }

    .ot-sdk-cookie-policy {
      font-family: inherit;
      font-size: 16px
    }

    .ot-sdk-cookie-policy h3,
    .ot-sdk-cookie-policy h4,
    .ot-sdk-cookie-policy h6,
    .ot-sdk-cookie-policy p,
    .ot-sdk-cookie-policy li,
    .ot-sdk-cookie-policy a,
    .ot-sdk-cookie-policy th,
    .ot-sdk-cookie-policy #cookie-policy-description,
    .ot-sdk-cookie-policy .ot-sdk-cookie-policy-group,
    .ot-sdk-cookie-policy #cookie-policy-title {
      color: dimgray
    }

    .ot-sdk-cookie-policy #cookie-policy-description {
      margin-bottom: 1em
    }

    .ot-sdk-cookie-policy h4 {
      font-size: 1.2em
    }

    .ot-sdk-cookie-policy h6 {
      font-size: 1em;
      margin-top: 2em
    }

    .ot-sdk-cookie-policy th {
      min-width: 75px
    }

    .ot-sdk-cookie-policy a,
    .ot-sdk-cookie-policy a:hover {
      background: #fff
    }

    .ot-sdk-cookie-policy thead {
      background-color: #f6f6f4;
      font-weight: bold
    }

    .ot-sdk-cookie-policy .ot-mobile-border {
      display: none
    }

    .ot-sdk-cookie-policy section {
      margin-bottom: 2em
    }

    .ot-sdk-cookie-policy table {
      border-collapse: inherit
    }

    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy {
      font-family: inherit;
      font-size: 16px
    }

    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy h3,
    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy h4,
    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy h6,
    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy p,
    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy li,
    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy a,
    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy th,
    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy #cookie-policy-description,
    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy .ot-sdk-cookie-policy-group,
    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy #cookie-policy-title {
      color: dimgray
    }

    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy #cookie-policy-description {
      margin-bottom: 1em
    }

    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy .ot-sdk-subgroup {
      margin-left: 1.5rem
    }

    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy #cookie-policy-description,
    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy .ot-sdk-cookie-policy-group-desc,
    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy .ot-table-header,
    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy a,
    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy span {
      font-size: .9rem
    }

    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy .ot-sdk-cookie-policy-group {
      font-size: 1rem;
      margin-bottom: .6rem
    }

    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy .ot-sdk-cookie-policy-title {
      margin-bottom: 1.2rem
    }

    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy>section {
      margin-bottom: 1rem
    }

    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy th {
      min-width: 75px
    }

    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy a,
    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy a:hover {
      background: #fff
    }

    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy thead {
      background-color: #f6f6f4;
      font-weight: bold
    }

    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy .ot-mobile-border {
      display: none
    }

    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy section {
      margin-bottom: 2em
    }

    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy .ot-sdk-subgroup ul li {
      list-style: disc;
      margin-left: 1.5rem
    }

    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy .ot-sdk-subgroup ul li h4 {
      display: inline-block
    }

    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table {
      border-collapse: inherit;
      margin: auto;
      border: 1px solid #d7d7d7;
      border-radius: 5px;
      border-spacing: initial;
      width: 100%;
      overflow: hidden
    }

    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table th,
    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table td {
      border-bottom: 1px solid #d7d7d7;
      border-right: 1px solid #d7d7d7
    }

    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table tr:last-child td {
      border-bottom: 0px
    }

    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table tr th:last-child,
    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table tr td:last-child {
      border-right: 0px
    }

    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table .ot-host,
    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table .ot-cookies-type {
      width: 25%
    }

    .ot-sdk-cookie-policy[dir=rtl] {
      text-align: left
    }

    @media only screen and (max-width: 530px) {

      .ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) table,
      .ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) thead,
      .ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) tbody,
      .ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) th,
      .ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) td,
      .ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) tr {
        display: block
      }

      .ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) thead tr {
        position: absolute;
        top: -9999px;
        left: -9999px
      }

      .ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) tr {
        margin: 0 0 1rem 0
      }

      .ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) tr:nth-child(odd),
      .ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) tr:nth-child(odd) a {
        background: #f6f6f4
      }

      .ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) td {
        border: none;
        border-bottom: 1px solid #eee;
        position: relative;
        padding-left: 50%
      }

      .ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) td:before {
        position: absolute;
        height: 100%;
        left: 6px;
        width: 40%;
        padding-right: 10px
      }

      .ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) .ot-mobile-border {
        display: inline-block;
        background-color: #e4e4e4;
        position: absolute;
        height: 100%;
        top: 0;
        left: 45%;
        width: 2px
      }

      .ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) td:before {
        content: attr(data-label);
        font-weight: bold
      }

      .ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) li {
        word-break: break-word;
        word-wrap: break-word
      }

      #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table {
        overflow: hidden
      }

      #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table td {
        border: none;
        border-bottom: 1px solid #d7d7d7
      }

      #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table,
      #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy thead,
      #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy tbody,
      #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy th,
      #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy td,
      #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy tr {
        display: block
      }

      #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table .ot-host,
      #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table .ot-cookies-type {
        width: auto
      }

      #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy tr {
        margin: 0 0 1rem 0
      }

      #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy td:before {
        height: 100%;
        width: 40%;
        padding-right: 10px
      }

      #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy td:before {
        content: attr(data-label);
        font-weight: bold
      }

      #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy li {
        word-break: break-word;
        word-wrap: break-word
      }

      #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy thead tr {
        position: absolute;
        top: -9999px;
        left: -9999px;
        z-index: -9999
      }

      #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table tr:last-child td {
        border-bottom: 1px solid #d7d7d7;
        border-right: 0px
      }

      #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table tr:last-child td:last-child {
        border-bottom: 0px
      }
    }

    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy h5,
    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy h6,
    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy li,
    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy p,
    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy a,
    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy span,
    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy td,
    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy #cookie-policy-description {
      color: #696969;
    }

    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy th {
      color: #696969;
    }

    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy .ot-sdk-cookie-policy-group {
      color: #696969;
    }

    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy #cookie-policy-title {
      color: #696969;
    }


    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table th {
      background-color: #F8F8F8;
    }


    @keyframes slide-down-custom {
      0% {
        bottom: 764px !important;
      }

      100% {
        bottom: 0px;
      }
    }

    @-webkit-keyframes slide-down-custom {
      0% {
        bottom: 764px !important;
      }

      100% {
        bottom: 0px;
      }
    }

    @-moz-keyframes slide-down-custom {
      0% {
        bottom: 764px !important;
      }

      100% {
        bottom: 0px;
      }
    }

  </style>
  <script type="text/javascript" id="script_cohesion"
    class="optanon-category-2">
    ! function (co, h, e, s, i, o, n) {
      var d = 'documentElement';
      var a = 'className';
      h[d][a] += ' preampjs fusejs';
      n.k = e;
      co._Cohesion = n;
      co._Preamp = {
        k: s,
        start: new Date
      };
      co._Fuse = {
        k: i
      };
      co._Tagular = {
        k: o
      };
      [e, s, i, o].map(function (x) {
        co[x] = co[x] || function () {
          (co[x].q = co[x].q || []).push([].slice.call(arguments))
        }
      });
      h.addEventListener('DOMContentLoaded', function () {
        co.setTimeout(function () {
          var u = h[d][a];
          h[d][a] = u.replace(/ ?preampjs| ?fusejs/g, '')
        }, 3e3);
        co._Preamp.docReady = co._Fuse.docReady = !0
      });
      var z = h.createElement('script');
      z.async = 1;
      z.src = 'https://cdn.cohesionapps.com/cohesion/cohesion-latest.min.js';
      h.head.appendChild(z);
    }
    (window, document, 'cohesion', 'preamp', 'fuse', 'tagular', {
      tagular: {
        writeKey: 'wk_1jbobeCiGVIPZaBX3yXqhexRwuy',
        sourceKey: 'src_1jbobeEHGTZmBD9QsSnBOaHCzpy',
        apiVersion: 'v2/t'
      },
      preamp: {
        siteId: '6ROcMJLU4qafKKGM04m2iK'
      }
    })

  </script>
  <script async=""
    src="https://cdn.cohesionapps.com/cohesion/cohesion-latest.min.js"></script>
  <script type="text/javascript" class="optanon-category-2">
    window.CnetFunctions.logWithLabel('%c One Trust ',
      "Service loaded: script_cohesion with class optanon-category-2");

  </script>
  <style>
    .flipX video::-webkit-media-text-track-display {
      transform: matrix(-1, 0, 0, 1, 0, 0) !important;
    }

    .flipXY video::-webkit-media-text-track-display {
      transform: matrix(-1, 0, 0, -1, 0, 0) !important;
    }

    .flipXYX video::-webkit-media-text-track-display {
      transform: matrix(1, 0, 0, -1, 0, 0) !important;
    }

  </style>
  <script type="text/javascript" charset="utf-8" async=""
    data-requirecontext="_"
    data-requiremodule="https://www.gstatic.com/firebasejs/7.1.0/firebase-app.js"
    src="https://www.gstatic.com/firebasejs/7.1.0/firebase-app.js"></script>
  <script type="text/javascript" charset="utf-8" async=""
    data-requirecontext="_"
    data-requiremodule="https://www.gstatic.com/firebasejs/7.1.0/firebase-auth.js"
    src="https://www.gstatic.com/firebasejs/7.1.0/firebase-auth.js"></script>
  <script type="text/javascript" charset="utf-8" async=""
    data-requirecontext="_"
    data-requiremodule="https://www.gstatic.com/firebasejs/7.1.0/firebase-firestore.js"
    src="https://www.gstatic.com/firebasejs/7.1.0/firebase-firestore.js">
  </script>
  <script type="text/javascript" charset="utf-8" async=""
    data-requirecontext="_"
    data-requiremodule="https://www.gstatic.com/firebasejs/7.1.0/firebase-functions.js"
    src="https://www.gstatic.com/firebasejs/7.1.0/firebase-functions.js">
  </script>
  <script type="text/javascript" charset="utf-8" async=""
    data-requirecontext="_"
    data-requiremodule="//www.cnet.com/a/video-player/uvpjs-rv/3.2.1/video-player.js"
    src="//www.cnet.com/a/video-player/uvpjs-rv/3.2.1/video-player.js"></script>
  <script type="text/javascript" charset="utf-8" async=""
    data-requirecontext="_" data-requiremodule="https://urs.cnet.com/sdk/urs.js"
    src="https://urs.cnet.com/sdk/urs.js"></script>
  <script type="text/javascript" charset="utf-8" async=""
    data-requirecontext="_" data-requiremodule="fly/components/disqus-count-1.0"
    src="https://www.cnet.com/a/fly/js/../bundles/flyjs/js/components/disqus-count-1.0.js">
  </script>
  <style type="text/css"></style>
  <script type="text/javascript" charset="utf-8" async=""
    data-requirecontext="_" data-requiremodule="components/nav.mobile"
    src="https://www.cnet.com/a/fly/js/components/nav.mobile-9f6dd51442-rev.js">
  </script>
  <script type="text/javascript" charset="utf-8" async=""
    data-requirecontext="_" data-requiremodule="components/link-tracker"
    src="https://www.cnet.com/a/fly/js/components/link-tracker-ea39e5f518-rev.js">
  </script>
  <script type="text/javascript" charset="utf-8" async=""
    data-requirecontext="_" data-requiremodule="components/read-more"
    src="https://www.cnet.com/a/fly/js/components/read-more-0ac39818d2-rev.js">
  </script>
  <script type="text/javascript" charset="utf-8" async=""
    data-requirecontext="_" data-requiremodule="components/track-cwv"
    src="https://www.cnet.com/a/fly/js/components/track-cwv-a0fced62e5-rev.js">
  </script>
  <script type="text/javascript" charset="utf-8" async=""
    data-requirecontext="_" data-requiremodule="components/module-asset-tracker"
    src="https://www.cnet.com/a/fly/js/components/module-asset-tracker-435dba6984-rev.js">
  </script>
  <script type="text/javascript" charset="utf-8" async=""
    data-requirecontext="_" data-requiremodule="components/myFinance-widget"
    src="https://www.cnet.com/a/fly/js/components/myFinance-widget-6c202450ac-rev.js">
  </script>
  <script type="text/javascript" charset="utf-8" async=""
    data-requirecontext="_" data-requiremodule="components/interest"
    src="https://www.cnet.com/a/fly/js/components/interest-ecd302842b-rev.js">
  </script>
  <script type="text/javascript" charset="utf-8" async=""
    data-requirecontext="_" data-requiremodule="managers/core-web-vitals"
    src="https://www.cnet.com/a/fly/js/managers/core-web-vitals-724d4fab0b-rev.js">
  </script>
  <script type="text/javascript" charset="utf-8" async=""
    data-requirecontext="_"
    data-requiremodule="//static.myfinance.com/widget/inlineMedia_core.js"
    src="//static.myfinance.com/widget/inlineMedia_core.js"></script>
  <script type="text/javascript" charset="utf-8" async=""
    data-requirecontext="_" data-requiremodule="translations/interest"
    src="https://www.cnet.com/a/fly/js/translations/interest-ddf6324c20-rev.js">
  </script>
  <script src="https://aswpsdkus.com/notify/v1/ua-sdk.min.js" async=""
    id="_uasdk"></script>
  <script type="text/javascript" charset="utf-8" async=""
    data-requirecontext="_"
    data-requiremodule="//static.chartbeat.com/js/chartbeat_video.js"
    src="//static.chartbeat.com/js/chartbeat_video.js"></script>
  <script type="text/javascript" charset="utf-8" async=""
    data-requirecontext="_"
    data-requiremodule="//www.cnet.com/a/video-player/uvpjs-rv/3.2.1/lib/tracking/comscore/comscore.streaming.6.1.1.171219.min.js"
    src="//www.cnet.com/a/video-player/uvpjs-rv/3.2.1/lib/tracking/comscore/comscore.streaming.6.1.1.171219.min.js">
  </script>
  <script type="text/javascript" charset="utf-8" async=""
    data-requirecontext="_"
    data-requiremodule="//s0.2mdn.net/instream/html5/ima3.js"
    src="//s0.2mdn.net/instream/html5/ima3.js"></script>
  <script type="text/javascript" charset="utf-8" async=""
    data-requirecontext="_"
    data-requiremodule="//unpkg.com/web-vitals/dist/web-vitals.iife.js"
    src="//unpkg.com/web-vitals/dist/web-vitals.iife.js"></script>
</head>

<body class="not-logged-in
  us


  noBleed



  endlist-control
  desktop vertical

  v3Article
  cnet   skybox-auto-collapse
touch-disabled scrollBar-0px"
  data-cms-edit="{&quot;url&quot;:&quot;http:\/\/cms.cnet.com\/content\/article\/dfb49c4e-2dd0-4182-890c-37897d913be9&quot;}"
  data-new-gr-c-s-check-loaded="14.1034.0" data-gr-ext-installed=""
  cz-shortcut-listen="true">
  <svg class="svg-symbol">
    <symbol id="stars-full" viewBox="0 0 14 14">
      <polygon
        points="7,0 8.8,5.1 14,5.3 9.9,8.7 11.3,14 7,11 2.7,14 4.1,8.7 0,5.3 5.2,5.1">
      </polygon>
    </symbol>
    <symbol id="stars-half" viewBox="0 0 14 14">
      <polygon points="7.1,0 7.1,11 2.7,14 4.1,8.7 0,5.3 5.2,5.1"></polygon>
    </symbol>
    <symbol id="play" viewBox="0 0 26.6 32">
      <path
        d="M0,2.6c0-2.4,1.6-3.3,3.7-2L25,13.7c2,1.3,2,3.2,0,4.5L3.7,31.4c-2,1.3-3.7,0.3-3.7-2C0,29.4,0,2.6,0,2.6z">
      </path>
    </symbol>
    <symbol id="playOverlay" viewBox="0 0 70 70">
      <circle cx="35" cy="35" r="33"></circle>
      <path class="overlayBorder" d="M35,2c18.2,0,33,14.8,33,33S53.2,68,35,68S2,53.2,2,35S16.8,2,35,2 M35,0C15.7,0,0,15.7,0,35s15.7,35,35,35
          s35-15.7,35-35S54.3,0,35,0L35,0z"></path>
      <path class="overlayIcon" d="M24.7,22.6c0-2.2,1.5-3.1,3.4-1.9l19.8,12.2c1.9,1.2,1.9,3,0,4.2L28.1,49.3c-1.9,1.2-3.4,0.3-3.4-1.9
              C24.7,47.4,24.7,22.6,24.7,22.6z"></path>
    </symbol>
    <symbol id="pauseOverlay" viewBox="0 0 88 88">
      <g fill="none" fill-rule="evenodd">
        <path
          d="M44 85.486c22.912 0 41.486-18.574 41.486-41.486C85.486 21.088 66.912 2.514 44 2.514 21.088 2.514 2.514 21.088 2.514 44c0 22.912 18.574 41.486 41.486 41.486z"
          fill-opacity=".3" fill="#000"></path>
        <path class="overlayBorder"
          d="M44 2.514C66.88 2.514 85.486 21.12 85.486 44c0 22.88-18.606 41.486-41.486 41.486C21.12 85.486 2.514 66.88 2.514 44 2.514 21.12 21.12 2.514 44 2.514zM44 0C19.737 0 0 19.737 0 44s19.737 44 44 44 44-19.737 44-44S68.263 0 44 0z"
          fill="#FFF" opacity=".5"></path>
        <g class="overlayIcon" transform="translate(32 26)" fill="#FFF">
          <rect width="8" height="37" rx="4"></rect>
          <rect x="16" width="8" height="37" rx="4"></rect>
        </g>
      </g>
    </symbol>
    <symbol id="galleryOverlay" viewBox="0 0 70 70">
      <circle cx="35" cy="35" r="33"></circle>
      <path class="overlayBorder" d="M35,2c18.2,0,33,14.8,33,33S53.2,68,35,68S2,53.2,2,35S16.8,2,35,2 M35,0C15.7,0,0,15.7,0,35s15.7,35,35,35
  s35-15.7,35-35S54.3,0,35,0L35,0z"></path>
      <circle class="overlayIcon" cx="30.5" cy="34.2" r="1.8"></circle>
      <polygon class="overlayIcon"
        points="21.2,41.3 19.1,41.3 19.1,22.9 45.5,22.9 45.5,25 21.2,25 ">
      </polygon>
      <path class="overlayIcon"
        d="M48.9,30v15.1h-23V30H48.9 M50.9,28h-27v19.1h27V28L50.9,28z"></path>
      <polygon class="overlayIcon"
        points="29,42.8 33.1,37.5 35.7,40 40.4,33.6 46.6,42.8 "></polygon>
    </symbol>
    <symbol id="arrow-round" viewBox="0 0 16 28">
      <g>
        <path d="M0.6,27.4c0.8,0.8,2,0.8,2.8,0l12-12c0.8-0.8,0.8-2,0-2.8l-12-12C3,0.2,2.5,0,2,0C1.5,0,1,0.2,0.6,0.6
      c-0.8,0.8-0.8,2,0,2.8L11.2,14L0.6,24.6C-0.2,25.4-0.2,26.6,0.6,27.4z">
        </path>
      </g>
    </symbol>
    <symbol id="arrow-bend" viewBox="0 0 114 239">
      <path
        d="M103.9 200.4c0 12.7-8.5 22-21.1 23.1l-68 6c.1-3.3 1.7-6.7 1.7-6.7l-16 9.3 18 6.2c.5 0-3-3.3-3.6-6.8l68.1-6c13.7-1.2 23-11.3 23-25.1h-2.1z">
      </path>
    </symbol>
    <symbol id="arrow-bend-mobile" viewBox="0 0 375 574">
      <path
        d="M6.58583043,149.351079 L6.55092562,147.351384 L7.55077331,147.333932 L74.355353,146.16995 C83.7002802,146.612397 90.2497558,144.782192 94.0604388,140.758638 C97.9165664,136.687102 100.363342,129.159376 101.325316,118.165492 L113.134161,4.57491832 L115.12344,4.78172322 L115.020038,5.77636282 L103.31615,118.35605 C102.320035,129.741693 99.7445012,137.665556 95.5125432,142.133918 C91.2351406,146.650262 84.1323288,148.635093 74.3255122,148.168679 L6.58583043,149.351079 Z"
        transform="translate(60.837183, 76.962999) rotate(-5.000000) translate(-60.837183, -76.962999)">
      </path>
      <path
        d="M22.3340475,144.613022 L27.3496239,159.343153 L12.3340475,154.613022 C15.3255937,154.106111 17.6539152,153.020108 19.3190121,151.355011 C20.9841091,149.689914 21.9891209,147.442584 22.3340475,144.613022 Z"
        transform="translate(19.841836, 151.978088) rotate(130.000000) translate(-19.841836, -151.978088) ">
      </path>
    </symbol>
    <symbol id="allContent" viewBox="0 0 32 32">
      <g>
        <ellipse cx="4.1" cy="4.1" rx="4.1" ry="4.1"></ellipse>
        <ellipse cx="16" cy="4.1" rx="4.1" ry="4.1"></ellipse>
        <ellipse cx="27.9" cy="4.1" rx="4.1" ry="4.1"></ellipse>
      </g>
      <g>
        <ellipse cx="4.1" cy="16" rx="4.1" ry="4.1"></ellipse>
        <ellipse cx="16" cy="16" rx="4.1" ry="4.1"></ellipse>
        <ellipse cx="27.9" cy="16" rx="4.1" ry="4.1"></ellipse>
      </g>
      <g>
        <ellipse cx="4.1" cy="27.9" rx="4.1" ry="4.1"></ellipse>
        <ellipse cx="16" cy="27.9" rx="4.1" ry="4.1"></ellipse>
        <ellipse cx="27.9" cy="27.9" rx="4.1" ry="4.1"></ellipse>
      </g>
    </symbol>
    <symbol id="half-dome" viewBox="0 0 112 55">
      <path
        d="M56,0 C86.927946,0 112,21.2342596 112,47.4280463 C112,47.6188199 111.99867,47.8093305 111.996018,47.9995716 L0.005,47.999 L0,47.4280463 C0,21.4961975 24.5731201,0.425181145 55.0739376,0.00635400315 Z">
      </path>
    </symbol>
    <symbol id="video" viewBox="0 0 6 8">
      <path
        d="M0,0.656478384 C0,0.0632729356 0.365482234,-0.17940202 0.828426396,0.144164588 L5.65279188,3.43375844 C6.11573604,3.75732504 6.11573604,4.24267496 5.65279188,4.56624156 L0.828426396,7.85583541 C0.365482234,8.17940202 1.73127164e-15,7.93672706 1.73127164e-15,7.34352162 C0,7.34352162 0,0.656478384 0,0.656478384 L0,0.656478384 Z">
      </path>
    </symbol>
    <symbol id="cnet" viewBox="0 0 180 131.5">
      <rect x="44.2" width="8.1" height="131.5"></rect>
      <path d="M11.1,47.9c0.1-8.3,14.5-8.6,14.5-0.4l0.1,7.1h11V48c0.7-22-37-22-36.7-0.5v37.9c-0.3,21.5,37.5,22,36.8,0v-7.9H25.6v8.5
      c0,8.2-14.3,8.4-14.5,0V47.5L11.1,47.9z"></path>
      <path
        d="M73.8,39.3c0,0,1.9-2.8,6.9-5.6c6.4-3.5,12.6,0,11.7-0.4c5,2.2,6.9,7.6,7.3,13.6v44.9h7.1v8.2H88.6l0-53.6
      c0-7.8-13.2-7.6-13.3,0.4l0-0.4V100H59v-8.2l5.2,0V41.5l-6.1-0.1v-8.2h15.7L73.8,39.3z">
      </path>
      <path
        d="M156.1,85.2c0.4,6,2.3,11.4,7.3,13.6c-0.9-0.4,2.5,1.3,5.9,1.2c1.8,0,10.7,0,10.7,0v-8.2c0,0-4.5-0.1-6.9,0
      c-3,0-5.6-1.4-5.8-5.7l0,0l0-44.5l0-0.1H178v-8.1h-10.7l0-0.1V16h-11.2l0,0L156.1,85.2L156.1,85.2z">
      </path>
      <path
        d="M135.9,77.4v8.5c0,8.2-14.4,8.4-14.5,0V68.2l23.6,0c1.1,0,2-0.9,2-2l0-18.2c0.7-22-37-22-36.7-0.5v37.9
      c-0.3,21.5,37.5,22,36.8,0v-7.9H135.9z M121.4,47.4c0.2-8.2,14.5-8.2,14.5,0.1l0,12.4h-14.4V47.4z">
      </path>
    </symbol>
    <symbol id="cnet-es" viewBox="0 0 80 91.2">
      <path fill="currentColor"
        d="m0.3 91v-8.8h6.2v1.7h-4.3v1.9h4.3v1.7h-4.3v2h4.4v1.5h-6.3z"></path>
      <path fill="currentColor"
        d="M14.4,91l-4.2-5.8V91H8.3v-8.8h1.9l4.1,5.5v-5.5h1.9V91H14.4z"></path>
      <path fill="currentColor"
        d="m22 91v-8.8h6.2v1.7h-4.4v1.9h4.3v1.7h-4.3v2h4.4v1.5h-6.2z"></path>
      <path fill="currentColor"
        d="m29.5 89.8l1-1.4c0.6 0.7 1.6 1.2 2.8 1.2 1 0 1.5-0.5 1.5-1 0-1.6-5.1-0.5-5.1-3.8 0-1.5 1.3-2.7 3.3-2.7 1.4 0 2.6 0.4 3.4 1.2l-1 1.4c-0.7-0.7-1.7-1-2.6-1-0.8 0-1.2 0.4-1.2 0.9 0 1.4 5.1 0.5 5.1 3.8 0 1.6-1.2 2.8-3.5 2.8-1.7 0-2.9-0.6-3.7-1.4z">
      </path>
      <path fill="currentColor"
        d="m38.1 91v-8.8h4.1c1.9 0 3 1.3 3 2.8s-1.1 2.8-3 2.8h-2.2v3.2h-1.9zm5.2-6c0-0.7-0.5-1.2-1.3-1.2h-2v2.4h2c0.7 0 1.3-0.4 1.3-1.2z">
      </path>
      <path fill="currentColor"
        d="m51.7 91l-0.5-1.5h-3.8l-0.6 1.5h-2.1l3.4-8.8h2.3l3.4 8.8h-2.1zm-2.5-6.9l-1.4 3.8h2.8l-1.4-3.8z">
      </path>
      <path fill="currentColor"
        d="m60.8 91l-4.2-5.8v5.8h-1.9v-8.8h1.9l4.1 5.5v-5.5h1.9v8.8h-1.8zm-2.9-10.4c-0.3 0-0.5 0.2-0.5 0.8h-0.9c0-1.1 0.5-1.8 1.4-1.8s1 0.9 1.5 0.9c0.3 0 0.5-0.2 0.5-0.8h0.9c0 1.1-0.5 1.8-1.4 1.8s-1-0.9-1.5-0.9z">
      </path>
      <path fill="currentColor"
        d="m63.8 86.6c0-2.7 1.9-4.6 4.6-4.6s4.6 1.9 4.6 4.6-1.9 4.6-4.6 4.6-4.6-1.9-4.6-4.6zm7.3 0c0-1.7-1.1-2.9-2.7-2.9s-2.7 1.2-2.7 2.9c0 1.6 1 2.9 2.7 2.9s2.7-1.3 2.7-2.9z">
      </path>
      <path fill="currentColor" d="M74.1,91v-8.8H76v7.2h3.7V91H74.1z"></path>
      <path
        d="m19.6 16.3h3.6v58.6h-3.6v-58.6zm-14.6 21.3c0.1-3.7 6.4-3.8 6.4-0.2l0.1 3.2h4.8v-2.9c0.3-9.8-16.4-9.8-16.3-0.2v16.9c-0.1 9.6 16.6 9.8 16.3 0v-3.5h-5v3.8c0 3.7-6.4 3.7-6.4 0v-17.2l0.1 0.1zm27.8-3.8s0.8-1.3 3.1-2.5c2.8-1.6 5.6 0 5.2-0.2 2.2 1 3.1 3.4 3.2 6.1v20h3.2v3.6h-8v-23.8c0-3.5-5.9-3.4-5.9 0.2v23.7h-7.2v-3.7h2.3v-22.4h-2.7v-3.6h7v2.6h-0.2zm36.6 20.4c0.2 2.7 1 5.1 3.2 6.1-0.4-0.2 1.1 0.6 2.6 0.5h4.8v-3.7h-3c-1.3 0-2.5-0.6-2.6-2.5v-19.8h4.8v-3.6h-4.8v-7.8h-5v30.8zm-9-3.4v3.8c0 3.7-6.4 3.7-6.5 0v-7.9h10.5c0.5 0 0.9-0.4 0.9-0.9v-8.1c0.3-9.8-16.4-9.8-16.3-0.2v16.9c-0.1 9.6 16.6 9.8 16.3 0v-3.5l-4.9-0.1zm-6.4-13.4c0-3.7 6.5-3.6 6.5 0v5.5h-6.5v-5.5z">
      </path>
    </symbol>
    <symbol id="photo" viewBox="0 0 32 24">
      <path
        d="M30,4h-6c-0.5,0-2.9-4-4-4h-8c-1.1,0-3.6,4-4,4H2C0.9,4,0,4.9,0,6v16c0,1.1,0.9,2,2,2h28c1.1,0,2-0.9,2-2V6
          C32,4.9,31.1,4,30,4z M16,20.3c-3.9,0-7-3.1-7-7c0-3.9,3.1-7,7-7c3.9,0,7,3.1,7,7C23,17.2,19.9,20.3,16,20.3z M16,19
          c-3.1,0-5.6-2.5-5.6-5.6c0-3.1,2.5-5.6,5.6-5.6c3.1,0,5.6,2.5,5.6,5.6C21.6,16.5,19.1,19,16,19z">
      </path>
    </symbol>
    <symbol id="pop-out" viewBox="0 0 32 32">
      <g>
        <polygon
          points="25.2,19.9 25.2,27.9 4.1,27.9 4.1,7 12.6,7 12.6,2.7 0,2.7 0,32 29.3,32 29.3,19.9   ">
        </polygon>
        <polygon
          points="18.6,0 18.6,4.1 25.1,4.1 15.2,14 18.2,16.9 27.9,7.2 27.9,13 32,13 32,0   ">
        </polygon>
      </g>
    </symbol><svg id="pop-out-small" viewBox="0 0 24 24">
      <g transform="translate(5.000000, 5.000000)">
        <polygon
          points="10.8368601 8.79658703 10.8368601 12.2368601 1.76313993 12.2368601 1.76313993 3.24914676 5.41843003 3.24914676 5.41843003 1.4 0 1.4 0 14 12.6 14 12.6 8.79658703">
        </polygon>
        <polygon
          points="8.41666667 0 8.41666667 1.69822485 11.125 1.69822485 7 5.79881657 8.25 7 12.2916667 2.98224852 12.2916667 5.38461538 14 5.38461538 14 0">
        </polygon>
      </g>
    </svg>
    <symbol id="close" viewBox="0 0 28 28">
      <polygon
        points="28,1.9 26.1,0 14,12.1 1.9,0 0,1.9 12.1,14 0,26.1 1.9,28 14,15.9 26.1,28 28,26.1 15.9,14     ">
      </polygon>
    </symbol>
    <symbol id="timer" viewBox="0 0 12 12">
      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g transform="translate(-2.000000, -11.000000)" fill="#B80000">
          <g transform="translate(2.000000, 11.000000)">
            <g>
              <g transform="translate(0.500000, 0.500000)" fill-rule="nonzero">
                <path
                  d="M5.5,11 C2.46243388,11 0,8.53756612 0,5.5 C0,2.46243388 2.46243388,0 5.5,0 C8.53756612,0 11,2.46243388 11,5.5 C11,8.53756612 8.53756612,11 5.5,11 Z M5.5,10 C7.98528137,10 10,7.98528137 10,5.5 C10,3.01471863 7.98528137,1 5.5,1 C3.01471863,1 1,3.01471863 1,5.5 C1,7.98528137 3.01471863,10 5.5,10 Z"
                  id="Oval"></path>
                <path
                  d="M7,5.29289322 L7,3 C7,2.72385763 7.22385763,2.5 7.5,2.5 C7.77614237,2.5 8,2.72385763 8,3 L8,5.5 C8,5.63260824 7.94732158,5.7597852 7.85355339,5.85355339 L5.85355339,7.85355339 C5.65829124,8.04881554 5.34170876,8.04881554 5.14644661,7.85355339 C4.95118446,7.65829124 4.95118446,7.34170876 5.14644661,7.14644661 L7,5.29289322 Z"
                  id="Path-2"
                  transform="translate(6.500000, 5.250000) scale(-1, 1) translate(-6.500000, -5.250000) ">
                </path>
              </g>
            </g>
          </g>
        </g>
      </g>
    </symbol>
    <symbol id="location" viewBox="0 0 12 12">
      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <path
          d="M10.3072394,0.372643361 L1.68732742,4.20377151 C1.43498593,4.31592492 1.32134105,4.61140641 1.43349446,4.8637479 C1.50566136,5.02612103 1.6585812,5.13799529 1.83518257,5.15761796 L6.18255048,5.64066591 C6.27551996,5.65099601 6.34891085,5.72438713 6.35924064,5.81735664 L6.84227899,10.1647655 C6.87277337,10.439219 7.11998245,10.6369869 7.39443591,10.6064925 C7.57103985,10.5868701 7.72396168,10.4749935 7.79612783,10.3126173 L11.6271966,1.6925862 C11.851498,1.18790079 11.6242018,0.59694028 11.1195164,0.372638943 C10.8609504,0.257722415 10.5658041,0.25772402 10.3072394,0.372643361 Z"
          fill="#000000"></path>
      </g>
    </symbol>
    <symbol id="bubble" viewBox="0 0 21.5 24">
      <path
        d="M18.2 0h-15C1.5 0 0 1.5 0 3.3v11c0 1.8 1.5 3.3 3.3 3.3h1.9l.7 5.4.1 1 .7-.8 4.9-5.7h6.6c1.8 0 3.3-1.5 3.3-3.3v-11C21.5 1.5 20 0 18.2 0zm2.4 14.2c0 1.3-1 2.3-2.3 2.3h-7l-.3.2-4.3 5.1-.6-4.8-.1-.4H3.3c-1.3 0-2.3-1-2.3-2.3v-11C1 2 2 1 3.3 1h15c1.3 0 2.3 1 2.3 2.3v10.9z">
      </path>
    </symbol>
    <symbol id="notification-activated" viewBox="0 0 24 24">
      <path fill-rule="nonzero"
        d="M3.5 15a.5.5 0 1 1 0-1A1.5 1.5 0 0 0 5 12.5V7a7 7 0 1 1 14 0v5.5a1.5 1.5 0 0 0 1.5 1.5.5.5 0 1 1 0 1h-17zM9 20.5a.5.5 0 1 1 1 0v.5a2 2 0 1 0 4 0v-.5a.5.5 0 1 1 1 0v.5a3 3 0 0 1-6 0v-.5z">
      </path>
      <rect width="20" height="1" x="2" y="17" rx=".5"></rect>
    </symbol>
    <symbol id="notification" viewBox="0 0 24 24">
      <path fill-rule="nonzero"
        d="M3.5 15a.5.5 0 1 1 0-1A1.5 1.5 0 0 0 5 12.5V7a7 7 0 1 1 14 0v5.5a1.5 1.5 0 0 0 1.5 1.5.5.5 0 1 1 0 1 2.5 2.5 0 0 1-2.5-2.5V7A6 6 0 1 0 6 7v5.5A2.5 2.5 0 0 1 3.5 15zM9 20.5a.5.5 0 1 1 1 0v.5a2 2 0 1 0 4 0v-.5a.5.5 0 1 1 1 0v.5a3 3 0 0 1-6 0v-.5z">
      </path>
      <rect width="20" height="1" x="2" y="17" rx=".5"></rect>
    </symbol>
    <symbol id="loginHideEye" viewBox="0 0 16 12">
      <g transform="translate(-2.000000, -4.000000)" fill-rule="nonzero">
        <path
          d="M6.0069257,13.7859675 C4.84636366,13.0558026 3.68937086,12.00939 2.53553494,10.6524129 C2.21616183,10.2768118 2.21819336,9.72448733 2.54032276,9.35124359 C5.01143807,6.48804291 7.49670985,5.02958447 10,5.02958447 C11.2376677,5.02958447 12.4709296,5.38610105 13.7002515,6.09264169 L15.8033009,3.98959236 L16.5104076,4.69669914 L14.5616958,6.64541103 C15.5299982,7.33156282 16.4959167,8.23457792 17.4596798,9.35126936 C17.781807,9.72451125 17.7838382,10.2768358 17.4644627,10.6524391 C14.9921432,13.5600005 12.5053441,15.0417322 10,15.0417322 C8.96857717,15.0417322 7.94029831,14.7905966 6.9148804,14.2922264 L5.19669914,16.0104076 L4.48959236,15.3033009 L6.0069257,13.7859675 Z M8.05276359,13.1543432 C8.71317212,13.4135557 9.36236653,13.5417322 10,13.5417322 C11.9119997,13.5417322 13.9279547,12.3892295 16.0385189,10.0071275 C15.1709896,9.04285278 14.3192478,8.28333934 13.483765,7.72334183 L12.6376235,8.56948325 C12.8687226,8.99469018 13,9.48201435 13,10 C13,11.6568542 11.6568542,13 10,13 C9.48201435,13 8.99469018,12.8687226 8.56948325,12.6376235 L8.05276359,13.1543432 Z M7.09955223,12.693341 L7.7776396,12.0152536 C7.29443169,11.4827076 7,10.7757379 7,10 C7,8.34314575 8.34314575,7 10,7 C10.7757379,7 11.4827076,7.29443169 12.0152536,7.7776396 L12.5939154,7.19897778 C11.7101781,6.75055704 10.8453456,6.52958447 10,6.52958447 C8.08492203,6.52958447 6.06982901,7.66366186 3.96148139,10.0071073 C5.03226206,11.215662 6.07869272,12.1077172 7.09955223,12.693341 Z">
        </path>
      </g>
    </symbol>
    <symbol id="loginShowEye" viewBox="0 0 16 10">
      <g transform="translate(-2.000000, -7.000000)" fill-rule="nonzero">
        <path
          d="M9.70264139,7 C12.2059402,7 14.6912157,8.45846842 17.1623212,11.3216849 C17.4844484,11.6949268 17.4864795,12.2472513 17.1671041,12.6228547 C14.6947846,15.5304161 12.2079855,17.0121477 9.70264139,17.0121477 C7.19728849,17.0121477 4.71048562,15.5304058 2.23817633,12.6228284 C1.91880322,12.2472273 1.92083475,11.6949029 2.24296414,11.3216591 C4.71407946,8.45845844 7.19935124,7 9.70264139,7 Z M9.70264139,15.5121477 C11.6146411,15.5121477 13.6305961,14.359645 15.7411603,11.977543 C13.6328198,9.63408417 11.6177252,8.5 9.70264139,8.5 C7.78756341,8.5 5.7724704,9.63407739 3.66412277,11.9775228 C5.77467951,14.3596382 7.79063599,15.5121477 9.70264139,15.5121477 Z M9.70264139,14.9704155 C8.04578714,14.9704155 6.70264139,13.6272698 6.70264139,11.9704155 C6.70264139,10.3135613 8.04578714,8.97041553 9.70264139,8.97041553 C11.3594956,8.97041553 12.7026414,10.3135613 12.7026414,11.9704155 C12.7026414,13.6272698 11.3594956,14.9704155 9.70264139,14.9704155 Z">
        </path>
      </g>
    </symbol>
    <symbol id="thumb" viewBox="0 0 32 32">
      <g>
        <path
          d="M21.6495949,0.876595089 L21.8908168,1.16258621 C22.1852813,1.56144847 22.3826718,2.04473533 22.4687288,2.6238906 L22.4928561,2.9912666 L22.4928561,5.2448367 C22.4928561,5.55407538 22.4467241,5.86115877 22.3219905,6.25232637 L22.1882797,6.58202858 L20.3072953,10.4908441 C20.2052849,10.703148 20.3363655,10.9586768 20.4632341,10.9807734 L20.6072454,10.9945009 L28.0955994,10.9945009 C29.1723256,10.9945009 30.153587,11.6169655 30.6902825,12.6979221 L30.8250871,13.0393541 L31.8567115,16.2500262 C32.049313,16.850385 32.0531387,17.4967301 31.8353727,18.1957276 L31.6993729,18.5325606 L26.0158673,30.3449467 C25.601682,31.2081989 24.8124545,31.8081412 23.7997847,31.9743079 L23.4412846,32 L9.97477885,32 C9.22972265,32 8.54567333,31.7049644 8.03002781,31.202853 C7.60636297,31.6040488 7.05391549,31.8827718 6.42196795,31.9783917 L6.09168467,32 L2.87848836,32 C1.44702194,32 0.239093651,30.9088659 0.0208365128,29.3527562 L0,29.0091508 L0,13.9853501 C0,12.4982919 1.05114177,11.2430986 2.54801255,11.0161615 L2.87848836,10.9945009 L6.09168467,10.9945009 C6.62199245,10.9945009 7.1216619,11.1444526 7.5517,11.4123545 L7.70656383,11.1697585 L7.93955177,10.8924622 L17.5779899,0.877792357 C18.7013377,-0.292397831 20.5245189,-0.292397831 21.6495949,0.876595089 Z M19.2720993,2.62869444 L9.68884088,12.5750774 L9.58592756,12.6939369 C9.54459833,12.7531085 9.52238396,12.8240256 9.52238396,12.8975512 L9.51898734,28.8143558 L9.52611843,28.9646767 C9.54998276,29.1323859 9.69004037,29.258901 9.85750167,29.258901 L23.2383995,29.2631579 L23.3936569,29.2544732 C23.4992346,29.2367078 23.5912268,29.1668148 23.6395124,29.0662308 L29.2901265,17.3370618 L29.3515951,17.1911406 C29.3726919,17.1221384 29.3722552,17.0483954 29.3502149,16.9797284 L28.3525883,13.863788 L28.2958143,13.7136709 C28.2388557,13.5999701 28.1260367,13.5284401 28.0005061,13.5284401 L20.41396,13.5252046 L20.0907032,13.4983273 C18.1603924,13.2136129 17.0415323,11.0336321 17.9243132,9.19733905 L19.7683183,5.37573197 L19.8285071,5.23361421 L19.8440093,5.12976611 L19.848027,2.97957036 L19.8399063,2.82114862 C19.8288398,2.74941727 19.7956485,2.6813834 19.7444796,2.62744998 L19.6335637,2.55144449 C19.5128461,2.50087787 19.370132,2.52662786 19.2720993,2.62869444 Z M5.94394952,13.4736842 L2.71465706,13.4775226 C2.5555009,13.5024075 2.4337501,13.649977 2.4337501,13.8229303 L2.43037975,28.8132942 L2.43742289,28.9639657 C2.46100548,29.1319353 2.6002516,29.2596073 2.76490099,29.2596073 L5.85196251,29.2631579 L5.99455416,29.2557504 C6.15402411,29.2308662 6.27510041,29.0841895 6.27510041,28.9103612 L6.27848101,13.9201447 L6.27146093,13.7699409 C6.24786534,13.6020879 6.1079417,13.4736842 5.94394952,13.4736842 Z M2.92940252,13.5771092 L2.83544304,13.5807855 L6.07594937,13.5807855 L2.92940252,13.5771092 Z">
        </path>
      </g>
    </symbol>
    <symbol id="redball" viewBox="0 0 226 226">
      <g fill-rule="evenodd" clip-rule="evenodd">
        <path
          d="M112.7 0C50.4 0 0 50.4 0 112.7s50.4 112.7 112.7 112.7c62.2 0 112.7-50.4 112.7-112.7S174.9 0 112.7 0z"
          fill="#dc0c03"></path>
        <path
          d="M192.3 150.3c-3.4 0-6.8-1.6-5.9-1.2-4.9-2.2-6.8-7.5-7.2-13.5V67.2h11.1V84.4h10.6v8h-10.6v44.1c.2 4.2 2.8 5.6 5.8 5.6h6.8v8.1c0 .1-8.8 0-10.6.1zm-22-14.5c.7 21.8-36.6 21.2-36.3 0V98.3c-.3-21.2 37-21.2 36.3.5v18c0 1.1-.9 2-2 2H145v17.5c.1 8.3 14.3 8.1 14.3 0v-8.4h11v7.9zm-11-37.5c0-8.1-14.1-8.2-14.3-.1v12.3h14.3V98.3zm-28.7 51.9h-18v-53c0-7.7-12.9-7.5-13.1.3v52.7H83.3v-8.1h5.1V92.5h-6.1v-8.1h15.5l.1 5.9s1.9-2.8 6.8-5.5c6.3-3.5 12.5 0 11.6-.4 5 2.2 6.8 7.5 7.2 13.5v44.3h7l.1 8zm-61.9 31.2h8v-130h-8v130zm-7.4-45.6c.7 21.8-36.6 21.2-36.3 0V98.3c-.3-21.2 37-21.2 36.3.5v6.5H50.5l-.1-7c0-8.1-14-7.9-14.3.2v37.8c.1 8.3 14.3 8.1 14.3 0v-8.4h11v7.9z"
          fill="#fff"></path>
      </g>
    </symbol>
    <symbol id="media-gallery-icon" viewBox="0 0 36 22">
      <g fill="none" fill-rule="evenodd">
        <path d="M0 29h36V-7H0z"></path>
        <g fill="#000">
          <path
            d="M34 3H5c-1.104 0-2 .896-2 2v15c0 1.104.896 2 2 2h29c1.104 0 2-.896 2-2V5c0-1.104-.896-2-2-2m0 1c.551 0 1 .449 1 1v15c0 .551-.449 1-1 1H5c-.551 0-1-.449-1-1V5c0-.551.449-1 1-1h29">
          </path>
          <path
            d="M.5 20c-.276 0-.5-.224-.5-.5v-17C0 1.122 1.122 0 2.5 0h31c.276 0 .5.224.5.5s-.224.5-.5.5h-31C1.673 1 1 1.673 1 2.5v17c0 .276-.224.5-.5.5">
          </path>
          <path
            d="M32.5 19c-.128 0-.256-.049-.354-.146l-7.585-7.586c-.567-.567-1.555-.567-2.122 0l-4.171 4.171c-.975.975-2.561.975-3.536 0l-1.171-1.171c-.567-.567-1.555-.567-2.122 0l-4.585 4.586c-.196.195-.512.195-.708 0-.195-.196-.195-.512 0-.708l4.586-4.585c.945-.945 2.591-.945 3.536 0l1.171 1.171c.567.567 1.555.567 2.122 0l4.171-4.171c.945-.945 2.591-.945 3.536 0l7.586 7.585c.195.196.195.512 0 .708-.098.097-.226.146-.354.146M10 7c-1.104 0-2 .896-2 2s.896 2 2 2 2-.896 2-2-.896-2-2-2m0 1c.551 0 1 .449 1 1 0 .551-.449 1-1 1-.551 0-1-.449-1-1 0-.551.449-1 1-1">
          </path>
        </g>
      </g>
    </symbol>
    <symbol id="coupon" viewBox="0 0 14 14">
      <path
        d="M6.51026 1C6.77621 1 7.03121 1.10594 7.21886 1.29439L12.2959 6.3929C12.685 6.78366 12.6843 7.41567 12.2944 7.80562L7.80561 12.2944C7.41567 12.6843 6.78366 12.685 6.3929 12.2959L1.29439 7.21886C1.10594 7.0312 1 6.77621 1 6.51026V2C1 1.44771 1.44772 1 2 1H6.51026ZM4.42857 3C4.04171 2.61371 3.40857 2.62 3.01371 3.01429C2.62 3.40857 2.61371 4.04171 3.00057 4.42857C3.38686 4.81543 4.02 4.80914 4.41429 4.41486C4.80857 4.02057 4.81486 3.38686 4.42857 3Z">
      </path>
    </symbol>
    <symbol id="pin" viewBox="0 0 16 16">
      <path
        d="M2.16693 13.4576L1.0128 14.6117L0.435911 15.1888C0.300622 15.4382 0.163555 15.6859 0.0465775 15.9536C0.314311 15.8366 0.561955 15.6996 0.811378 15.5643L1.38844 14.9872L2.26169 14.114L3.69831 12.6773C3.89084 12.4864 4.08 12.2919 4.27556 12.1038C4.27716 12.1012 4.27858 12.0983 4.28 12.0956L4.66311 11.7125L8.95058 16L9.13813 15.8123C10.8036 14.147 11.3966 11.642 10.6725 9.38756L11.9783 8.08178C13.3918 8.24729 14.8155 7.75893 15.8121 6.76231L16 6.57458L9.42524 0L9.23769 0.187733C8.24089 1.18453 7.75307 2.60871 7.91822 4.02151L6.61244 5.32711C4.35787 4.60302 1.85298 5.19627 0.187733 6.86169L0 7.04942L4.28747 11.3371L3.90453 11.72L3.89618 11.7244C3.70773 11.9207 3.51253 12.1102 3.32107 12.3035L2.16693 13.4576Z">
      </path>
    </symbol>
    <symbol id="check-mark" viewBox="0 0 16 14">
      <path
        d="M16 2.15877L5.02857 13.1302L0 8.10163L1.28914 6.81249L5.02857 10.5428L14.7109 0.869629L16 2.15877Z"
        fill="#262626"></path>
    </symbol>
    <symbol id="refresh" viewBox="0 0 24 24">
      <path fill-rule="evenodd" clip-rule="evenodd"
        d="M5 4C5.55228 4 6 4.44772 6 5V6.70864C7.46535 5.04819 9.60966 4 12 4C16.0804 4 19.4459 7.05392 19.9382 11.0013C20.0065 11.5493 19.6176 12.049 19.0696 12.1173C18.5215 12.1857 18.0219 11.7968 17.9535 11.2487C17.5846 8.29003 15.059 6 12 6C10.0492 6 8.31451 6.93116 7.21818 8.375H9.375C9.92728 8.375 10.375 8.82272 10.375 9.375C10.375 9.92728 9.92728 10.375 9.375 10.375H5C4.44772 10.375 4 9.92728 4 9.375V5C4 4.44772 4.44772 4 5 4ZM4.93042 11.8827C5.47846 11.8143 5.97813 12.2032 6.04647 12.7513C6.41542 15.71 8.94104 18 12 18C13.9508 18 15.6855 17.0688 16.7818 15.625H14.625C14.0727 15.625 13.625 15.1773 13.625 14.625C13.625 14.0727 14.0727 13.625 14.625 13.625H19C19.5523 13.625 20 14.0727 20 14.625V19C20 19.5523 19.5523 20 19 20C18.4477 20 18 19.5523 18 19V17.2914C16.5346 18.9518 14.3903 20 12 20C7.91963 20 4.55407 16.9461 4.06184 12.9987C3.9935 12.4507 4.38238 11.951 4.93042 11.8827Z">
      </path>
    </symbol>
    <symbol id="warning" viewBox="0 0 16 14" fill="none">
      <path fill-rule="evenodd" clip-rule="evenodd"
        d="M6.25697 1.09898C7.02197 -0.261021 8.97897 -0.261021 9.74297 1.09898L15.323 11.019C16.073 12.353 15.11 13.999 13.581 13.999H2.41997C0.889971 13.999 -0.0730296 12.353 0.67697 11.019L6.25697 1.09898ZM8.99997 11C8.99997 11.2652 8.89461 11.5196 8.70708 11.7071C8.51954 11.8946 8.26519 12 7.99997 12C7.73475 12 7.4804 11.8946 7.29286 11.7071C7.10533 11.5196 6.99997 11.2652 6.99997 11C6.99997 10.7348 7.10533 10.4804 7.29286 10.2929C7.4804 10.1053 7.73475 9.99998 7.99997 9.99998C8.26519 9.99998 8.51954 10.1053 8.70708 10.2929C8.89461 10.4804 8.99997 10.7348 8.99997 11ZM7.99997 2.99998C7.73475 2.99998 7.4804 3.10534 7.29286 3.29287C7.10533 3.48041 6.99997 3.73476 6.99997 3.99998V6.99998C6.99997 7.2652 7.10533 7.51955 7.29286 7.70709C7.4804 7.89462 7.73475 7.99998 7.99997 7.99998C8.26519 7.99998 8.51954 7.89462 8.70708 7.70709C8.89461 7.51955 8.99997 7.2652 8.99997 6.99998V3.99998C8.99997 3.73476 8.89461 3.48041 8.70708 3.29287C8.51954 3.10534 8.26519 2.99998 7.99997 2.99998Z"
        fill="#FF9900"></path>
    </symbol>
    <symbol id="info-circle" viewBox="0 0 16 16" fill="none">
      <path fill-rule="evenodd" clip-rule="evenodd"
        d="M16 8C16 10.1217 15.1571 12.1566 13.6569 13.6569C12.1566 15.1571 10.1217 16 8 16C5.87827 16 3.84344 15.1571 2.34315 13.6569C0.842855 12.1566 0 10.1217 0 8C0 5.87827 0.842855 3.84344 2.34315 2.34315C3.84344 0.842855 5.87827 0 8 0C10.1217 0 12.1566 0.842855 13.6569 2.34315C15.1571 3.84344 16 5.87827 16 8ZM9 4C9 4.26522 8.89464 4.51957 8.70711 4.70711C8.51957 4.89464 8.26522 5 8 5C7.73478 5 7.48043 4.89464 7.29289 4.70711C7.10536 4.51957 7 4.26522 7 4C7 3.73478 7.10536 3.48043 7.29289 3.29289C7.48043 3.10536 7.73478 3 8 3C8.26522 3 8.51957 3.10536 8.70711 3.29289C8.89464 3.48043 9 3.73478 9 4ZM7 7C6.73478 7 6.48043 7.10536 6.29289 7.29289C6.10536 7.48043 6 7.73478 6 8C6 8.26522 6.10536 8.51957 6.29289 8.70711C6.48043 8.89464 6.73478 9 7 9V12C7 12.2652 7.10536 12.5196 7.29289 12.7071C7.48043 12.8946 7.73478 13 8 13H9C9.26522 13 9.51957 12.8946 9.70711 12.7071C9.89464 12.5196 10 12.2652 10 12C10 11.7348 9.89464 11.4804 9.70711 11.2929C9.51957 11.1054 9.26522 11 9 11V8C9 7.73478 8.89464 7.48043 8.70711 7.29289C8.51957 7.10536 8.26522 7 8 7H7Z"
        fill="#0094FF"></path>
    </symbol>
  </svg> <svg class="svg-symbol">
    <symbol id="hamburger" viewBox="0 0 40 36">
      <rect y="0" width="40" height="4"></rect>
      <rect y="16" width="40" height="4"></rect>
      <rect y="32" width="40" height="4"></rect>
    </symbol>
    <symbol id="mag" viewBox="0 0 16 16">
      <path
        d="M15.9,14.3l-3.5-3.5c0.7-1.1,1.1-2.4,1.1-3.8c0-3.8-3-6.9-6.7-6.9C3,0,0,3.1,0,6.9s3,6.9,6.7,6.9
      c1.6,0,3-0.5,4.1-1.5l3.5,3.5c0.1,0.1,0.2,0.1,0.3,0.1c0,0,0,0,0,0c0.1,0,0.3-0.1,0.3-0.2l0.8-0.9C16,14.8,16,14.5,15.9,14.3z
       M6.7,11.6c-2.6,0-4.6-2.1-4.6-4.7s2.1-4.7,4.6-4.7c2.6,0,4.6,2.1,4.6,4.7S9.3,11.6,6.7,11.6z">
      </path>
    </symbol>
    <symbol id="globe" viewBox="0 0 32 32">
      <path d="M16,0C7.2,0,0,7.2,0,16s7.2,16,16,16s16-7.2,16-16S24.8,0,16,0z M17,10.3c1.3,0,2.7-0.2,3.9-0.3c0.3,1.5,0.5,3.2,0.6,5H17
      V10.3z M17,8.3V2.7c1.3,0.7,2.5,2.6,3.4,5.3C19.3,8.1,18.2,8.2,17,8.3z M15,2.7v5.6c-1.2,0-2.3-0.1-3.4-0.3
      C12.5,5.2,13.7,3.3,15,2.7z M15,10.3V15h-4.5c0.1-1.8,0.3-3.5,0.6-5C12.3,10.1,13.7,10.2,15,10.3z M8.5,15H2.1
      c0.2-2.6,1.1-4.9,2.4-6.9c1.4,0.6,2.9,1.2,4.6,1.5C8.8,11.2,8.6,13.1,8.5,15z M8.5,17c0.1,2,0.3,3.8,0.6,5.5
      c-1.7,0.4-3.2,0.9-4.6,1.5c-1.4-2-2.3-4.4-2.5-7H8.5z M10.5,17H15v4.8c-1.3,0-2.7,0.1-3.9,0.3C10.8,20.6,10.5,18.8,10.5,17z
       M15,23.7v5.6c-1.3-0.7-2.5-2.5-3.4-5.3C12.7,23.9,13.8,23.8,15,23.7z M17,29.3v-5.6c1.2,0,2.3,0.1,3.4,0.3
      C19.5,26.8,18.3,28.7,17,29.3z M17,21.7V17h4.5c-0.1,1.9-0.3,3.6-0.6,5.1C19.7,21.9,18.3,21.8,17,21.7z M23.5,17H30
      c-0.2,2.6-1.1,5-2.5,7c-1.4-0.6-2.9-1.2-4.6-1.5C23.2,20.8,23.5,18.9,23.5,17z M23.5,15c-0.1-1.9-0.3-3.7-0.6-5.4
      c1.7-0.4,3.2-0.9,4.6-1.6c1.4,2,2.3,4.4,2.5,7H23.5z M26.2,6.4c-1.1,0.5-2.4,0.9-3.8,1.2c-0.6-2.1-1.5-3.8-2.5-5.1
      C22.3,3.2,24.5,4.6,26.2,6.4z M12.2,2.5c-1,1.3-1.9,3-2.5,5.1C8.2,7.3,6.9,6.9,5.8,6.5C7.5,4.6,9.7,3.3,12.2,2.5z M5.8,25.6
      c1.1-0.5,2.4-0.9,3.8-1.2c0.6,2.1,1.5,3.8,2.5,5.1C9.7,28.8,7.5,27.4,5.8,25.6z M19.8,29.5c1-1.2,1.9-3,2.5-5.1
      c1.4,0.3,2.7,0.7,3.8,1.2C24.5,27.4,22.3,28.7,19.8,29.5z"></path>
    </symbol>
    <symbol id="caret" viewBox="0 0 16 28">
      <g>
        <path d="M0.6,27.4c0.8,0.8,2,0.8,2.8,0l12-12c0.8-0.8,0.8-2,0-2.8l-12-12C3,0.2,2.5,0,2,0C1.5,0,1,0.2,0.6,0.6
      c-0.8,0.8-0.8,2,0,2.8L11.2,14L0.6,24.6C-0.2,25.4-0.2,26.6,0.6,27.4z">
        </path>
      </g>
    </symbol>
  </svg> <svg class="svg-symbol">
    <symbol id="comments" viewBox="0 0 21.5 24">
      <path d="M18.2 0h-15C1.5 0 0 1.5 0 3.3v11c0 1.8 1.5 3.3 3.3 3.3h1.9l.7 5.4.1 1 .7-.8 4.9-5.7h6.6c1.8 0 3.3-1.5 3.3-3.3v-11C21.5
      1.5 20 0 18.2 0zm2.4 14.2c0 1.3-1 2.3-2.3 2.3h-7l-.3.2-4.3 5.1-.6-4.8-.1-.4H3.3c-1.3 0-2.3-1-2.3-2.3v-11C1 2 2 1 3.3 1h15c1.3 0
      2.3 1 2.3 2.3v10.9z"></path>
    </symbol>
    <symbol id="copy" viewBox="0 0 16 8">
      <path
        d="M3.6,5.9C2.6,5.9,1.7,5,1.7,4c0-1,0.8-1.9,1.9-1.9l3.4,0c0.4,0,0.7,0.1,1,0.3c0.8,0.5,1.1,1.5,0.7,2.3l1.8,0
  C10.9,2.9,9.8,1.1,8,0.5C7.7,0.5,7.3,0.4,7,0.4l-3.4,0C1.6,0.4,0,2,0,4c0,2,1.6,3.6,3.6,3.6h2.3c-0.5-0.5-1-1.1-1.3-1.7H3.6z">
      </path>
      <path d="M12.4,0.4h-2.3c0.5,0.5,1,1.1,1.3,1.7h1c1,0,1.9,0.8,1.9,1.9c0,1-0.8,1.9-1.9,1.9H9C8,5.9,7.1,5,7.1,4c0-0.2,0-0.5,0.1-0.7
      l-1.8,0c0,0.2-0.1,0.5-0.1,0.7c0,0.6,0.2,1.3,0.5,1.9C6.4,6.6,7.1,7.2,8,7.5c0.3,0.1,0.7,0.1,1,0.1l3.4,0C14.4,7.6,16,6,16,4
      C16,2,14.4,0.4,12.4,0.4z"></path>
    </symbol>
    <symbol id="email" viewBox="0 0 32 21">
      <polygon points="32,19.5 32,1.3 23.1,10.4"></polygon>
      <path d="M16.9,13.8L30.4,0h-29l13.5,13.9C15.4,14.4,16.3,14.4,16.9,13.8z">
      </path>
      <polygon points="0,1.5 0,19.4 8.7,10.5"></polygon>
      <path
        d="M18.3,15.3c-0.7,0.7-1.6,1-2.4,1c-0.9,0-1.7-0.3-2.4-1L10.2,12l-8.8,9h29.2l-8.9-9.2L18.3,15.3z">
      </path>
      <polygon points="32,21 32,21 32,21"></polygon>
    </symbol>
    <symbol id="es" viewBox="0 0 16 8">
      <path
        d="M12.5 0c1.35 0 2.47.38 3.3 1.12l-1 1.24c-.7-.6-1.6-.87-2.47-.87-.78 0-1.2.3-1.2.8 0 1.26 4.87.4 4.87 3.4 0 1.46-1.1 2.57-3.4 2.57-1.64 0-2.8-.52-3.6-1.27l.98-1.3c.6.6 1.53 1.08 2.7 1.08 1 0 1.48-.43 1.48-.9 0-1.4-4.9-.43-4.9-3.44C9.27 1.1 10.5 0 12.5 0zM6 0v1.5H1.8v1.7h4.1v1.5H1.8v1.8H6V8H0V0h6z">
      </path>
    </symbol>
    <symbol id="facebook" viewBox="0 0 15.2 32">
      <path d="M15.2,11.1H9.6V7c0-1.2,1.3-1.5,1.9-1.5c0.6,0,3.6,0,3.6,0V0L11,0C5.4,0,4.1,4.1,4.1,6.7v4.4H0v5.6h4.1
      c0,7.3,0,15.2,0,15.2h5.5c0,0,0-8.1,0-15.2h4.7L15.2,11.1z"></path>
    </symbol>
    <symbol id="flipboard" viewBox="0 0 100 100">
      <g shape-rendering="crispEdges">
        <path d="M35 35h25v25H35z" opacity=".8"></path>
        <path d="M35 10h50v25H35z" opacity=".9"></path>
        <path d="M10 10h25v80H10z"></path>
      </g>
    </symbol>
    <symbol id="googleplus" viewBox="0 0 32 32">
      <path d="M18.8,1c1.1-0.6,1.6-1,1.6-1H9.9C7.8,0,2,2.4,2,7.9c0,5.5,6,6.7,8.2,6.6C9,16,10,17.4,10.7,18.1
      c0.7,0.7,0.5,0.9-0.3,0.9C9.7,19,0,19.1,0,26s12.8,7.4,17.1,3.7s3.3-8.9,0-11.2c-3.3-2.3-4.5-3.4-2.4-5.3
      c2.1-1.8,3.7-3.3,3.7-6.8s-2.8-5.2-2.8-5.2S17.7,1.6,18.8,1z M17.1,25.7c0,3-2.5,4.4-6.8,4.4c-4.3,0-6.6-2.1-6.6-5.4
      c0-3.2,3.1-4.8,9-4.8C14.3,21.2,17.1,22.7,17.1,25.7z M10.9,13.2c-5.2,0-7.5-12.1-1.4-12.1C14.2,0.9,17.8,13.2,10.9,13.2z
       M28.1,4V0.1h-2V4h-4v2h4V10h2V6.1H32V4H28.1z"></path>
    </symbol>
    <symbol id="instagram" viewBox="0 0 512 512">
      <path
        d="M256,49.471c67.266,0,75.233.257,101.8,1.469,24.562,1.121,37.9,5.224,46.778,8.674a78.052,78.052,0,0,1,28.966,18.845,78.052,78.052,0,0,1,18.845,28.966c3.45,8.877,7.554,22.216,8.674,46.778,1.212,26.565,1.469,34.532,1.469,101.8s-0.257,75.233-1.469,101.8c-1.121,24.562-5.225,37.9-8.674,46.778a83.427,83.427,0,0,1-47.811,47.811c-8.877,3.45-22.216,7.554-46.778,8.674-26.56,1.212-34.527,1.469-101.8,1.469s-75.237-.257-101.8-1.469c-24.562-1.121-37.9-5.225-46.778-8.674a78.051,78.051,0,0,1-28.966-18.845,78.053,78.053,0,0,1-18.845-28.966c-3.45-8.877-7.554-22.216-8.674-46.778-1.212-26.564-1.469-34.532-1.469-101.8s0.257-75.233,1.469-101.8c1.121-24.562,5.224-37.9,8.674-46.778A78.052,78.052,0,0,1,78.458,78.458a78.053,78.053,0,0,1,28.966-18.845c8.877-3.45,22.216-7.554,46.778-8.674,26.565-1.212,34.532-1.469,101.8-1.469m0-45.391c-68.418,0-77,.29-103.866,1.516-26.815,1.224-45.127,5.482-61.151,11.71a123.488,123.488,0,0,0-44.62,29.057A123.488,123.488,0,0,0,17.3,90.982C11.077,107.007,6.819,125.319,5.6,152.134,4.369,179,4.079,187.582,4.079,256S4.369,333,5.6,359.866c1.224,26.815,5.482,45.127,11.71,61.151a123.489,123.489,0,0,0,29.057,44.62,123.486,123.486,0,0,0,44.62,29.057c16.025,6.228,34.337,10.486,61.151,11.71,26.87,1.226,35.449,1.516,103.866,1.516s77-.29,103.866-1.516c26.815-1.224,45.127-5.482,61.151-11.71a128.817,128.817,0,0,0,73.677-73.677c6.228-16.025,10.486-34.337,11.71-61.151,1.226-26.87,1.516-35.449,1.516-103.866s-0.29-77-1.516-103.866c-1.224-26.815-5.482-45.127-11.71-61.151a123.486,123.486,0,0,0-29.057-44.62A123.487,123.487,0,0,0,421.018,17.3C404.993,11.077,386.681,6.819,359.866,5.6,333,4.369,324.418,4.079,256,4.079h0Z">
      </path>
      <path
        d="M256,126.635A129.365,129.365,0,1,0,385.365,256,129.365,129.365,0,0,0,256,126.635Zm0,213.338A83.973,83.973,0,1,1,339.974,256,83.974,83.974,0,0,1,256,339.973Z">
      </path>
      <circle cx="390.476" cy="121.524" r="30.23"></circle>
    </symbol>
    <symbol id="linkedin" viewBox="0 0 31.9 32">
      <path
        d="M24,8c-5.1,0.1-7.7,3.8-8,4V8h-6v24h6V18c0-0.5,1.3-4.6,6-4c2.5,0.2,3.9,3.5,4,4v14l6,0V15.4
      C31.7,13,30.5,8.1,24,8z M0,32h6V8H0V32z M3,0C1.3,0,0,1.3,0,3s1.3,3,3,3c1.7,0,3-1.3,3-3S4.7,0,3,0z">
      </path>
    </symbol>
    <symbol id="more" viewBox="0 0 32 7.3">
      <circle cx="3.7" cy="3.7" r="3.7"></circle>
      <circle cx="16" cy="3.7" r="3.7"></circle>
      <circle cx="28.3" cy="3.7" r="3.7"></circle>
    </symbol>
    <symbol id="pinterest" viewBox="0 0 24.9 32">
      <path d="M13.2,0C4.4,0,0,6.3,0,11.5c0,3.2,1.2,6,3.8,7c0.4,0.2,0.8,0,0.9-0.5c0.1-0.3,0.3-1.1,0.4-1.5
      c0.1-0.5,0.1-0.6-0.3-1c-0.7-0.9-1.2-2-1.2-3.6c0-4.6,3.5-8.8,9.1-8.8c5,0,7.7,3,7.7,7c0,5.3-2.4,9.8-5.9,9.8
      c-1.9,0-3.4-1.6-2.9-3.5c0.6-2.3,1.6-4.8,1.6-6.5c0-1.5-0.8-2.8-2.5-2.8c-2,0-3.6,2-3.6,4.8c0,1.7,0.6,2.9,0.6,2.9s-2,8.5-2.4,10
      c-0.7,3-0.1,6.6-0.1,7c0,0.2,0.3,0.3,0.4,0.1c0.2-0.2,2.5-3.1,3.3-6c0.2-0.8,1.3-5.1,1.3-5.1c0.6,1.2,2.5,2.3,4.5,2.3
      c5.9,0,10-5.4,10-12.6C24.9,5.1,20.3,0,13.2,0z"></path>
    </symbol>
    <symbol id="reddit" viewBox="0 0 32 26.7">
      <path
        d="M22.9,14.5C23,14.7,23,15,23,15.2c0,0.4-0.2,0.9-0.4,1.2c-0.3,0.3-0.6,0.6-1,0.7h0c0,0,0,0,0,0c0,0,0,0,0,0
      c-0.2,0.1-0.4,0.1-0.6,0.1c-0.5,0-0.9-0.2-1.3-0.5c-0.4-0.3-0.6-0.7-0.7-1.2c0,0,0,0,0,0c0,0,0,0,0,0h0c0-0.1,0-0.2,0-0.4
      c0-0.4,0.1-0.8,0.4-1.2c0.2-0.3,0.6-0.6,1-0.7c0,0,0,0,0,0c0,0,0,0,0,0c0.2-0.1,0.5-0.1,0.7-0.1c0.4,0,0.8,0.1,1.2,0.4
      C22.5,13.8,22.7,14.1,22.9,14.5C22.9,14.5,22.9,14.5,22.9,14.5C22.9,14.5,22.9,14.5,22.9,14.5L22.9,14.5z M21.6,19.7
      c-0.2-0.1-0.4-0.2-0.6-0.2c-0.2,0-0.3,0-0.5,0.1c-1.4,0.8-3.1,1.3-4.7,1.3c-1.2,0-2.5-0.3-3.6-0.8l0,0l0,0
      c-0.2-0.1-0.4-0.2-0.6-0.4c-0.1-0.1-0.2-0.1-0.3-0.2c-0.1-0.1-0.3-0.1-0.4-0.1c-0.1,0-0.2,0-0.4,0.1c0,0,0,0,0,0
      c-0.2,0.1-0.3,0.2-0.4,0.4c-0.1,0.2-0.2,0.4-0.2,0.6c0,0.2,0,0.3,0.1,0.5c0.1,0.1,0.2,0.3,0.4,0.4c1.6,1.1,3.5,1.6,5.4,1.6
      c1.7,0,3.4-0.4,4.9-1.1l0,0l0,0c0.2-0.1,0.5-0.2,0.7-0.4c0.1-0.1,0.2-0.2,0.4-0.3c0.1-0.1,0.2-0.3,0.2-0.4c0-0.1,0-0.2,0-0.2
      c0-0.1,0-0.3-0.1-0.4C21.9,19.9,21.8,19.8,21.6,19.7L21.6,19.7z M10.4,17.1C10.4,17.1,10.4,17.1,10.4,17.1
      c0.2,0.1,0.4,0.1,0.6,0.1c0.5,0,1-0.2,1.4-0.6c0.4-0.3,0.6-0.8,0.6-1.4c0,0,0,0,0,0c0,0,0-0.1,0-0.1c0-0.6-0.3-1-0.6-1.4
      c-0.4-0.3-0.9-0.6-1.4-0.6c-0.1,0-0.3,0-0.4,0c0,0,0,0,0,0h0c-0.7,0.1-1.3,0.7-1.5,1.4c0,0,0,0,0,0C9,14.8,9,15,9,15.2
      c0,0.4,0.1,0.9,0.4,1.2C9.6,16.7,10,17,10.4,17.1C10.4,17.1,10.4,17.1,10.4,17.1L10.4,17.1z M32,12.1L32,12.1c0,0.1,0,0.1,0,0.2
      c0,0.8-0.2,1.5-0.7,2.2c-0.4,0.6-0.9,1.1-1.5,1.4c0,0.3,0.1,0.6,0.1,0.9c0,1.7-0.6,3.3-1.6,4.6v0h0c-1.9,2.5-4.7,3.9-7.6,4.7l0,0
      c-1.5,0.4-3.1,0.6-4.7,0.6c-2.4,0-4.7-0.4-6.9-1.3v0h0c-2.3-0.9-4.5-2.4-5.8-4.6c-0.7-1.2-1.1-2.5-1.1-3.9c0-0.3,0-0.6,0.1-0.9
      c-0.6-0.3-1.1-0.8-1.5-1.4C0.3,13.9,0,13.2,0,12.4v0c0-1.1,0.5-2.1,1.2-2.8c0.7-0.7,1.7-1.2,2.8-1.2h0c0.1,0,0.2,0,0.3,0
      c0.5,0,1.1,0.1,1.6,0.3l0,0h0C6.3,8.8,6.8,9,7.1,9.3c0.1-0.1,0.3-0.1,0.4-0.2c2.3-1.4,5-1.9,7.6-2c0-1.3,0.2-2.7,0.8-3.8
      c0.5-1,1.4-1.8,2.5-2l0,0h0c0.4-0.1,0.8-0.1,1.2-0.1c1.1,0,2.2,0.3,3.2,0.7c0.5-0.7,1.1-1.2,1.9-1.5l0,0l0,0
      C25.3,0.1,25.8,0,26.2,0c0.5,0,1,0.1,1.5,0.3v0c0,0,0,0,0,0c0,0,0,0,0,0C28.4,0.6,29,1,29.4,1.6C29.8,2.2,30,3,30,3.7
      c0,0.1,0,0.3,0,0.4l0,0c0,0,0,0,0,0c-0.1,1-0.6,1.8-1.2,2.4c-0.7,0.6-1.6,1-2.5,1c-0.1,0-0.3,0-0.4,0c-0.9-0.1-1.8-0.5-2.4-1.2
      c-0.6-0.7-1-1.5-1-2.5c0,0,0-0.1,0-0.1C21.6,3.3,20.7,3,19.8,3c-0.1,0-0.3,0-0.4,0h0c-0.7,0.1-1.3,0.5-1.6,1.1v0
      c-0.5,0.9-0.6,1.9-0.6,3c2.6,0.2,5.2,0.8,7.4,2.1h0l0,0c0,0,0.1,0.1,0.2,0.1C25,9.2,25.2,9,25.4,8.9c0.7-0.5,1.5-0.7,2.3-0.7
      c0.4,0,0.7,0,1.1,0.1h0l0,0c0,0,0,0,0,0c0.8,0.2,1.6,0.7,2.2,1.3C31.5,10.4,31.9,11.2,32,12.1L32,12.1L32,12.1z M24.4,3.6
      c0,0,0,0.1,0,0.1v0c0,0.4,0.2,0.9,0.6,1.2c0.3,0.3,0.8,0.5,1.2,0.5h0c0,0,0.1,0,0.1,0c0.4,0,0.9-0.2,1.2-0.5
      C27.8,4.6,28,4.2,28,3.8v0c0,0,0-0.1,0-0.1c0-0.5-0.2-0.9-0.6-1.2c-0.3-0.3-0.8-0.5-1.2-0.5c-0.1,0-0.3,0-0.4,0.1h0l0,0
      c-0.4,0.1-0.7,0.3-1,0.6C24.6,2.9,24.4,3.2,24.4,3.6L24.4,3.6z M5.4,10.5c-0.3-0.2-0.7-0.3-1.1-0.3c-0.1,0-0.1,0-0.2,0h0l0,0
      c-0.5,0-1,0.2-1.4,0.6c-0.4,0.4-0.6,0.8-0.7,1.4v0l0,0c0,0,0,0.1,0,0.1c0,0.3,0.1,0.6,0.3,0.9c0.1,0.2,0.3,0.4,0.5,0.6
      C3.4,12.6,4.3,11.5,5.4,10.5L5.4,10.5z M27.8,16.9c0-1.2-0.4-2.3-1.1-3.2c-1.3-1.9-3.4-3.1-5.6-3.8l0,0c-0.4-0.1-0.8-0.2-1.3-0.3
      c-1.3-0.3-2.6-0.4-3.9-0.4c-1.7,0-3.5,0.3-5.2,0.8c-2.2,0.7-4.3,1.9-5.6,3.8v0c-0.7,0.9-1.1,2.1-1.1,3.3c0,0.4,0.1,0.9,0.2,1.3
      l0,0c0.2,0.9,0.7,1.8,1.3,2.5c0.6,0.7,1.4,1.3,2.2,1.8c0.2,0.1,0.4,0.2,0.5,0.3c2.3,1.3,5,1.9,7.6,1.9c0.4,0,0.9,0,1.3,0
      c2.7-0.2,5.3-1,7.5-2.6v0c0.7-0.5,1.3-1.1,1.8-1.8c0.5-0.7,0.9-1.5,1-2.3v0C27.8,17.5,27.8,17.2,27.8,16.9L27.8,16.9z M29.9,12.3
      c0-0.3-0.1-0.6-0.2-0.8l0,0l0,0c-0.2-0.4-0.5-0.7-0.8-0.9c-0.4-0.2-0.8-0.3-1.2-0.3c-0.4,0-0.7,0.1-1.1,0.3c1.1,0.9,2,2,2.6,3.3
      c0.2-0.2,0.4-0.4,0.5-0.6C29.8,13,29.9,12.6,29.9,12.3L29.9,12.3z M29.9,12.3">
      </path>
    </symbol>
    <symbol id="rss" viewBox="0 0 36 36">
      <path
        d="M0 14.4v7.2c8 0 14.4 6.4 14.4 14.4h7.2c0-11.9-9.7-21.6-21.6-21.6z">
      </path>
      <path d="M0 0v7.2c15.9 0 28.8 12.9 28.8 28.8H36C36 16.1 19.9 0 0 0z">
      </path>
      <circle cx="3.6" cy="32.4" r="3.6"></circle>
    </symbol>
    <symbol id="share" viewBox="0 0 16 16">
      <path
        d="M.73 13.74c.47.08.87-.26.96-.7 1.17-3.28 3-4.7 5.97-4.74h.66c.07 0 .17.12.17.3v.83c0 .45.2.9.57 1.13.4.26.9.24
      1.27-.06l5.17-4.05c.32-.25.5-.66.5-1.08 0-.38-.14-.74-.4-1l-.1-.07L10.35.24c-.37-.3-.87-.32-1.27-.06-.36.24-.57.68-.57 1.14v.8c0
      .2-.1.32-.17.32h-.2C4.8 2.38 2.33 4.56.73 8.57c-.6 1.6-.8 2.5-.7 3.9v.3c-.02.46.25.9.7.97zm.94-4.8c1.4-3.56 3.46-5.44 6.2-5.5h.46c.67 0
      1.17-.6 1.17-1.3V1.3c0-.14.05-.26.12-.3.04-.03.06-.03.1 0l5.17 4.06c.05.05.1.16.1.3 0 .12-.05.23-.1.28L9.7
      9.7c-.04.05-.06.05-.1.03-.07-.05-.12-.17-.12-.3V8.6c0-.7-.5-1.3-1.17-1.3h-.66c-3.1.03-5.2 1.45-6.55 4.45l-.12.3c0-1.04.17-1.8.67-3.12z">
      </path>
    </symbol>
    <symbol id="stumble" viewBox="0 0 32 24">
      <path d="M17.8,8.6l2.1,1.5l3.3-1.5l0-1.6C23,3.3,19.9,0,16,0c-3.8,0-7,3.2-7.1,6.9v9.9c0,0.9-0.8,1.6-1.7,1.6
      c-0.9,0-1.9-0.4-1.9-1.3V12H0c0,0,0,5.1,0,5.1C0,20.9,3.2,24,7.1,24c3.9,0,7.1-3.1,7.1-6.9V6.9c0-0.9,0.8-1.7,1.8-1.7
      c0.9,0,1.8,0.8,1.8,1.7V8.6z M26.7,12v5.1c0,0.9-1,1.5-1.9,1.5c-0.9,0-1.7-0.7-1.7-1.6l0-5l-3.3,1.6L17.8,12v5.1
      c0,3.8,3.2,6.9,7.1,6.9c3.9,0,7.1-3.1,7.1-6.9c0,0,0-5.1,0-5.1H26.7z">
      </path>
    </symbol>
    <symbol id="tumblr" viewBox="0 0 20.9 32">
      <path
        d="M7,0C6.8,1.8,6.7,2.4,6.1,3.6C5.5,4.7,4.7,6.2,3.7,7.1C2.8,7.9,1.4,8.7,0,9.2v5.5h4.1v8.9
      c0,1.6,0.2,2.8,0.5,3.6c0.3,0.8,0.9,1.6,1.8,2.4c0.9,0.7,2.8,1.7,2.8,1.7s1.6,0.6,3.2,0.6c1.4,0,2.7-0.1,4-0.4c1.2-0.3,3-1,4.6-1.7
      v-5.5c-1.8,1.1-4,1.9-5.8,1.9c-1,0-1.9-0.2-2.7-0.7c-0.6-0.3-1-0.8-1.2-1.4c-0.2-0.6-0.7-1.9-0.7-3.9v-5.7H19V8.6h-8.5V0H7z">
      </path>
    </symbol>
    <symbol id="twitter" viewBox="0 0 35 30">
      <path
        d="M32.5,3.4c-0.5,0.3-2.2,1-3.7,1.1c1-0.6,2.4-2.4,2.8-3.9c-0.9,0.6-3.1,1.6-4.2,1.6c0,0,0,0,0,0
      C26.1,0.9,24.4,0,22.5,0c-3.7,0-6.7,3.2-6.7,7.2c0,0.6,0.1,1.1,0.2,1.6h0C11,8.7,5.1,6,1.8,1.3c-2,3.8-0.3,8,2,9.5
      c-0.8,0.1-2.2-0.1-2.9-0.8c0,2.5,1.1,5.8,5.2,7c-0.8,0.5-2.2,0.3-2.8,0.2c0.2,2.1,3,4.9,6,4.9c-1.1,1.3-4.7,3.8-9.3,3
      c3.1,2,6.7,3.2,10.5,3.2c10.8,0,19.2-9.4,18.7-21.1c0,0,0,0,0,0c0,0,0-0.1,0-0.1c0,0,0-0.1,0-0.1C30.2,6.4,31.5,5.1,32.5,3.4z">
      </path>
    </symbol>
    <symbol id="yahoo" viewBox="-133 141 30.3 20">
      <path d="M-107.7,146.7c0,0,1.2-0.7,2.1-0.7s2.1,0,2.1,0l0.8-1.9h-12.9l0.1,1.4l0.8,0.4l3.3,0.1c0,0,0.2,0.7-0.3,1.1
      c-0.6,0.4-5.4,4.7-5.4,4.7l-6.2-7.5c0,0,0.1-0.2,1-0.2c0.9,0,3.5,0,3.5,0V141H-133v3.1c0,0,1.8,0,2.7,0c0.9,0,1.9,1,2.8,1.8
      c0.9,0.9,7.1,7.4,7.1,7.4v5.4c0,0-0.5,0.8-2.2,0.8c-1.8,0-2.5,0.1-2.5,0.1v1.4h14.1v-1c0,0-0.4-0.6-0.8-0.6h-3.4
      c-0.4,0-0.5-0.5-0.5-1.1c0-0.6,0-4.7,0-4.7L-107.7,146.7z"></path>
    </symbol>
    <symbol id="sms" viewBox="0 0 72 72">
      <path
        d="M38,58 L24,72 L24,58 L9.99237268,58 C4.47373763,58 0,53.5215031 0,48.0046567 L0,15.9953433 C0,10.4750676 4.47671884,
      6 9.99237268,6 L62.0076273,6 C67.5262624,6 72,10.4784969 72,15.9953433 L72,48.0046567 C72,53.5249324 67.5232812,58 62.0076273,
      58 L38,58 Z M9.9,28.475 C9.9,33.075 13.825,33.575 16.825,33.925 C18.675,34.125 20.2,34.475 20.2,35.725 C20.2,37.15 19,37.7 16.875,
      37.7 C15.275,37.7 14.15,37.05 13.65,35.975 C13.325,35.225 13.05,35.1 12.325,35.375 L10.5,36.025 C9.8,36.275 9.6,36.6 9.85,37.325 C10.825,
      39.95 12.975,41.375 16.875,41.375 C21.475,41.375 24.3,39.075 24.3,35.65 C24.3,31.25 20.75,30.5 17.825,30.15 C15.8,29.925 14.075,29.65 14.075,
      28.3 C14.075,27.1 14.975,26.575 17.025,26.575 C18.6,26.575 19.45,27.175 19.975,28.225 C20.35,28.9 20.625,29.025 21.275,28.725 L23.175,
      27.9 C23.875,27.6 24.075,27.25 23.725,26.55 C22.575,24.1 20.675,22.875 17.025,22.875 C12.35,22.875 9.9,25.25 9.9,28.475 Z M43.875,23.25 L42.1,
      23.25 C41.325,23.25 41.125,23.375 40.75,24.025 L36.15,32.45 L31.575,24 C31.225,23.4 30.975,23.25 30.15,23.25 L28.525,23.25 C27.775,23.25 27.5,
      23.5 27.5,24.25 L27.5,40 C27.5,40.75 27.775,41 28.525,41 L30.3,41 C31.05,41 31.3,40.75 31.3,40 L31.3,34.35 C31.3,32.675 31.15,31.25 30.55,28.7 L30.775,
      28.7 C31.275,30.525 31.825,32 32.625,33.35 L34.425,36.525 C34.725,37.1 34.95,37.25 35.575,37.25 L36.725,37.25 C37.35,37.25 37.575,37.05 37.85,
      36.525 L39.625,33.35 C40.375,32 41.075,30.4 41.6,28.7 L41.825,28.7 C41.2,31.15 41,32.8 41,34.325 L41,40 C41,40.75 41.25,41 42,41 L43.875,41 C44.65,
      41 44.9,40.75 44.9,40 L44.9,24.25 C44.9,23.5 44.65,23.25 43.875,23.25 Z M48.325,28.475 C48.325,33.075 52.25,33.575 55.25,33.925 C57.1,34.125 58.625,
      34.475 58.625,35.725 C58.625,37.15 57.425,37.7 55.3,37.7 C53.7,37.7 52.575,37.05 52.075,35.975 C51.75,35.225 51.475,35.1 50.75,35.375 L48.925,36.025 C48.225,
      36.275 48.025,36.6 48.275,37.325 C49.25,39.95 51.4,41.375 55.3,41.375 C59.9,41.375 62.725,39.075 62.725,35.65 C62.725,31.25 59.175,30.5 56.25,
      30.15 C54.225,29.925 52.5,29.65 52.5,28.3 C52.5,27.1 53.4,26.575 55.45,26.575 C57.025,26.575 57.875,27.175 58.4,28.225 C58.775,28.9 59.05,
      29.025 59.7,28.725 L61.6,27.9 C62.3,27.6 62.5,27.25 62.15,26.55 C61,24.1 59.1,22.875 55.45,22.875 C50.775,22.875 48.325,25.25 48.325,28.475 Z">
      </path>
    </symbol>
    <symbol id="youtube" viewBox="0 0 234.66667 165.33333">
      <g transform="matrix(1.3333333,0,0,-1.3333333,0,165.33333)" id="g10">
        <g transform="scale(0.1)" id="g12">
          <path id="path14"
            d="m 700,358.313 v 523.375 l 460,-261.7 z m 1023.22,688.057 c -20.24,76.22 -79.88,136.24 -155.6,156.61 C 1430.37,1240 880,1240 880,1240 c 0,0 -550.367,0 -687.621,-37.02 C 116.656,1182.61 57.0156,1122.59 36.7773,1046.37 0,908.227 0,620 0,620 0,620 0,331.777 36.7773,193.629 57.0156,117.41 116.656,57.3906 192.379,37.0117 329.633,0 880,0 880,0 c 0,0 550.37,0 687.62,37.0117 75.72,20.3789 135.36,80.3983 155.6,156.6173 C 1760,331.777 1760,620 1760,620 c 0,0 0,288.227 -36.78,426.37">
          </path>
        </g>
      </g>
    </symbol>
  </svg> <svg class="svg-symbol">
    <symbol id="deal" viewBox="0 0 28 28">
      <path
        d="M4.66666667,6.53333333 C3.63377778,6.53333333 2.8,5.69955556 2.8,4.66666667 C2.8,3.63377778 3.63377778,2.8 4.66666667,2.8 C5.69955556,2.8 6.53333333,3.63377778 6.53333333,4.66666667 C6.53333333,5.69955556 5.69955556,6.53333333 4.66666667,6.53333333 M27.174,13.412 L14.574,0.812 C14.07,0.308 13.37,0 12.6,0 L2.8,0 C1.26,0 0,1.26 0,2.8 L0,12.6 C0,13.37 0.308,14.07 0.826,14.588 L13.426,27.188 C13.93,27.692 14.63,28 15.4,28 C16.17,28 16.87,27.692 17.374,27.174 L27.174,17.374 C27.692,16.87 28,16.17 28,15.4 C28,14.63 27.678,13.916 27.174,13.412"
        id="deals-icon"></path>
    </symbol>
    <symbol id="gift" viewBox="0 0 32 32">
      <path
        d="M25,8c0.6-0.8,1-1.9,1-3c0-2.8-2.2-5-5-5c-2.4,0-4.4,1.7-4.9,4h-0.2c-0.5-2.3-2.5-4-4.9-4C8.2,0,6,2.2,6,5
          c0,1.1,0.4,2.2,1,3H0v8h4v16h24V16h4V8H25z M11,2c1.7,0,3,1.3,3,3s-1.3,3-3,3S8,6.7,8,5S9.3,2,11,2z M15,30H6V16h9V30z M15,14H4H2
          v-4h13V14z M21,2c1.7,0,3,1.3,3,3s-1.3,3-3,3s-3-1.3-3-3S19.3,2,21,2z M26,30h-9V16h9V30z M30,14h-2H17v-4h13V14z">
      </path>
    </symbol>
    <symbol id="list" viewBox="0 0 375 392">
      <path fill="white" d="M0,0v392h375V97.7L277.1,0H0z"></path>
      <path
        d="M0,0v392h375V97.7L277.1,0H0z M17,17h243v98h98v260H17V17z M277,23l75,75h-75V23z M81,164h65v-17H81V164z
               M81,212h213v-16H81V212z M81,261h213v-15H81V261z M81,309h147v-15H81V309z">
      </path>
    </symbol>
    <symbol id="video-stroke" viewBox="0 0 32 32">
      <path d="M18.8,32H2.9C1.3,32,0,30.6,0,29V12.3c0-1.6,1.4-2.9,2.9-2.9h15.9c1.6,0,2.9,1.4,2.9,2.9v3.3l0.3-0.2
              c3.7-2.6,5.3-3.7,5.9-3.9c1.3-0.4,3.1-0.1,3.3,0l0.7,0.2v17.7l-0.6,0.2c-0.2,0.1-1.6,0.6-3.1,0.1c-0.7-0.2-2.3-1.3-6.5-4.3v3.7
              C21.7,30.7,20.4,32,18.8,32z M2.9,11.3c-0.5,0-1,0.5-1,1V29c0,0.6,0.5,1.1,1,1.1h15.9c0.6,0,1-0.5,1-1v-7.4l1.9,1.4
              c2.4,1.7,6.3,4.6,7,4.8c0.5,0.1,1,0.1,1.4,0.1V13.2c-0.6-0.1-1.2-0.1-1.6,0.1l0,0c-0.5,0.2-3.4,2.2-5.4,3.6l-3.2,2.3v-6.9
              c0-0.5-0.5-1-1-1H2.9z"></path>
    </symbol>
    <symbol id="mag" viewBox="0 0 16 16">
      <path
        d="M15.9,14.3l-3.5-3.5c0.7-1.1,1.1-2.4,1.1-3.8c0-3.8-3-6.9-6.7-6.9C3,0,0,3.1,0,6.9s3,6.9,6.7,6.9
              c1.6,0,3-0.5,4.1-1.5l3.5,3.5c0.1,0.1,0.2,0.1,0.3,0.1c0,0,0,0,0,0c0.1,0,0.3-0.1,0.3-0.2l0.8-0.9C16,14.8,16,14.5,15.9,14.3z
               M6.7,11.6c-2.6,0-4.6-2.1-4.6-4.7s2.1-4.7,4.6-4.7c2.6,0,4.6,2.1,4.6,4.7S9.3,11.6,6.7,11.6z">
      </path>
    </symbol>
    <symbol id="media-gallery-icon" viewBox="0 0 36 22">
      <g fill="none" fill-rule="evenodd">
        <path d="M0 29h36V-7H0z"></path>
        <g fill="#000">
          <path
            d="M34 3H5c-1.104 0-2 .896-2 2v15c0 1.104.896 2 2 2h29c1.104 0 2-.896 2-2V5c0-1.104-.896-2-2-2m0 1c.551 0 1 .449 1 1v15c0 .551-.449 1-1 1H5c-.551 0-1-.449-1-1V5c0-.551.449-1 1-1h29">
          </path>
          <path
            d="M.5 20c-.276 0-.5-.224-.5-.5v-17C0 1.122 1.122 0 2.5 0h31c.276 0 .5.224.5.5s-.224.5-.5.5h-31C1.673 1 1 1.673 1 2.5v17c0 .276-.224.5-.5.5">
          </path>
          <path
            d="M32.5 19c-.128 0-.256-.049-.354-.146l-7.585-7.586c-.567-.567-1.555-.567-2.122 0l-4.171 4.171c-.975.975-2.561.975-3.536 0l-1.171-1.171c-.567-.567-1.555-.567-2.122 0l-4.585 4.586c-.196.195-.512.195-.708 0-.195-.196-.195-.512 0-.708l4.586-4.585c.945-.945 2.591-.945 3.536 0l1.171 1.171c.567.567 1.555.567 2.122 0l4.171-4.171c.945-.945 2.591-.945 3.536 0l7.586 7.585c.195.196.195.512 0 .708-.098.097-.226.146-.354.146M10 7c-1.104 0-2 .896-2 2s.896 2 2 2 2-.896 2-2-.896-2-2-2m0 1c.551 0 1 .449 1 1 0 .551-.449 1-1 1-.551 0-1-.449-1-1 0-.551.449-1 1-1">
          </path>
        </g>
      </g>
    </symbol>
    <symbol id="media-video-icon" viewBox="0 0 18 22">
      <g fill="none" fill-rule="evenodd">
        <path d="M-9 29h36V-7H-9z"></path>
        <path
          d="M1.9517 0C.9387 0-.0003.807-.0003 1.95v 18.1c0 1.143.939 1.95 1.952 1.95.354 0 .717-.099 1.053-.315l13.986-9.05c1.186-.768 1.186-2.502 0-3.27L3.0047.315C2.6687.099 2.3057 0 1.9517 0m0 1c.179 0 .351.052.51.155l13.986 9.05c.275.178.432.468.432.795s-.157.617-.432.795l-13.986 9.05c-.159.103-.331.155-.51.155-.458 0-.952-.363-.952-.95V1.95c0-.587.494-.95.952-.95"
          fill="#000"></path>
      </g>
    </symbol>
  </svg>













  <div data-ad="nav-ad-plus-leader" data-ad-container="61718e4e621d3"
    class="ad-nav-ad-plus-leader ad-slot  skybox-closeBtn skybox-collapseBtn skybox-videoStop ">
  </div>







  <div class="c-trendingBar container -noscroll -reversed"
    section="trending-bar">

    <span class="c-trendingBar_linkWrapper " section="ipod-at-20">
      <a class="c-trendingBar_link"
        href="/news/apples-ipod-is-improbably-still-around-heres-a-look-at-the-past-20-years/">iPod
        at 20</a>
    </span>

    <span class="c-trendingBar_linkWrapper " section="trump-social-network">
      <a class="c-trendingBar_link"
        href="/news/donald-trump-is-launching-a-new-social-network-called-truth-social/">Trump
        social network</a>
    </span>

    <span class="c-trendingBar_linkWrapper " section="moderna-booster">
      <a class="c-trendingBar_link"
        href="/health/moderna-booster-gets-fda-authorization-what-to-know/">Moderna
        booster</a>
    </span>

    <span class="c-trendingBar_linkWrapper "
      section="pixel-6:-everything-to-know">
      <a class="c-trendingBar_link"
        href="/news/pixel-6-everything-to-know-about-googles-newest-phone/">Pixel
        6: Everything to know</a>
    </span>

    <span class="c-trendingBar_linkWrapper "
      section="100-million-year-old-crab">
      <a class="c-trendingBar_link"
        href="/news/100-million-year-old-crab-trapped-in-amber-rewrites-ancient-crustacean-history/">100-million-year
        old crab</a>
    </span>

    <span class="c-trendingBar_linkWrapper " section="uncharted-movie-trailer">
      <a class="c-trendingBar_link"
        href="/news/uncharted-movie-trailer-introduces-tom-hollands-nathan-drake/">Uncharted
        movie trailer</a>
    </span>
  </div>

  <div id="rbWrapper">



    <header class="c-siteHeader is-collapsed  -hasCurated -reversed container"
      role="menubar" section="cnet-nav">
      <div class="mobileHeader">
        <div class="logo" section="logo">

          <a class="logo_link" itemprop="url" href="/" title="CNET Home"
            data-item="logo">
            <svg class="logo_image cnet">
              <use aria-hidden="false" xlink:href="#cnet"></use>
            </svg>

          </a>
        </div>

        <div class="c-verticalNav">
          <a href="/tech/">
            <h3 class="c-verticalNav_hed">Tech</h3>
          </a>
        </div>
        <button class="navToggle" data-component="nav">
          <svg class="icon navToggle_icon navToggle_icon-hamburger hamburger">
            <use aria-hidden="false" xlink:href="#hamburger"></use>
          </svg>

          <svg class="icon navToggle_icon navToggle_icon-close close">
            <use aria-hidden="false" xlink:href="#close"></use>
          </svg>

        </button>

      </div>
    </header>
    <nav class="nav">
      <svg
        class="icon navToggle_icon navToggle_icon-close navToggle_icon-vertical close">
        <use aria-hidden="false" xlink:href="#close"></use>
      </svg>
      <div class="c-primaryNav">
        <div class="navMenu" role="menuitem" aria-haspopup="true"
          section="curated" data-item="menuContainer">
          <button class="navMenuItem" aria-label="Reviews" data-item="menuItem">
            <span class="navMenuItem_text"> <span
                class="lastWord">Reviews</span>
            </span>
            <svg class="navMenuItem_icon caret">
              <use aria-hidden="false" xlink:href="#caret"></use>
            </svg>

          </button>
          <div class="m-menu" data-item="navMenu">


            <div class="m-menu_menuListContainer">
              <div class="menuList">
                <a class="menuList_title" href="/reviews/">All Reviews</a>
                <div class="menuList_linkHolder o-gridVerticalList">
                  <a class="menuList_link" href="/versus/">
                    Versus
                  </a>
                  <a class="menuList_link" href="/gifts/">
                    Gift Guide
                  </a>
                  <a class="menuList_link" href="/awards/">
                    Award Winners
                  </a>
                </div>
              </div>

              <div class="menuList">
                <a class="menuList_title" href="/best/">Best Products</a>
                <div class="menuList_linkHolder o-gridVerticalList">
                  <a class="menuList_link" href="/tech/mobile/best-5g-phones/">
                    Best 5G Phone
                  </a>
                  <a class="menuList_link"
                    href="/tech/services-and-software/best-antivirus/">
                    Best Antivirus
                  </a>
                  <a class="menuList_link"
                    href="/personal-finance/credit-cards/best-cash-back-credit-cards/">
                    Best Cash-Back Credit Cards
                  </a>
                  <a class="menuList_link"
                    href="/home/<USER>/best-cordless-vacuum/">
                    Best Cordless Vacuum
                  </a>
                  <a class="menuList_link"
                    href="/home/<USER>/best-internet-providers/">
                    Best Internet Providers
                  </a>
                  <a class="menuList_link" href="/tech/computing/best-laptop/">
                    Best Laptop
                  </a>
                  <a class="menuList_link"
                    href="/health/fitness/best-massage-gun/">
                    Best Massage Gun
                  </a>
                  <a class="menuList_link" href="/health/sleep/best-mattress/">
                    Best Mattress
                  </a>
                  <a class="menuList_link"
                    href="/health/nutrition/best-meal-kit-deals-and-sign-up-offers-2021/">
                    Best Meal Kit Delivery Service
                  </a>
                  <a class="menuList_link"
                    href="/home/<USER>/best-mesh-wi-fi-router/">
                    Best Mesh Wi-Fi
                  </a>
                  <a class="menuList_link"
                    href="/health/fitness/best-peloton-alternative/">
                    Best Peloton Alternative
                  </a>
                  <a class="menuList_link"
                    href="/health/fitness/best-rowing-machine/">
                    Best Rowing Machine
                  </a>
                  <a class="menuList_link"
                    href="/tech/services-and-software/best-live-tv-streaming-service-for-cord-cutters/">
                    Best Streaming Service
                  </a>
                  <a class="menuList_link" href="/news/best-tv/">
                    Best TV
                  </a>
                  <a class="menuList_link"
                    href="/tech/services-and-software/best-vpn/">
                    Best VPN
                  </a>
                  <a class="menuList_link"
                    href="/tech/mobile/best-wireless-earbuds-2021-true-headphones-airpods-samsung-buds/">
                    Best Wireless Earbuds
                  </a>
                </div>
              </div>

              <div class="menuList">
                <span class="menuList_title">Popular</span>
                <div class="menuList_linkHolder o-gridVerticalList">
                  <a class="menuList_link"
                    href="/tech/mobile/apple-iphone-13-review-familiarity-is-part-of-the-phones-charm/">
                    Apple iPhone 13
                  </a>
                  <a class="menuList_link"
                    href="/tech/computing/dell-inspiron-16-plus-review-a-macbook-pro-alternative-for-much-less/">
                    Dell Inspiron 16 Plus
                  </a>
                  <a class="menuList_link"
                    href="/tech/mobile/google-pixel-5a-review-snappy-performance-and-design/">
                    Google Pixel 5A
                  </a>
                  <a class="menuList_link"
                    href="/home/<USER>/nest-doorbell-with-battery-review-the-free-features-weve-always-wanted/">
                    Nest Doorbell with battery
                  </a>
                  <a class="menuList_link"
                    href="/tech/mobile/samsung-galaxy-watch-4-review-first-take-of-a-great-future-idea/">
                    Samsung Galaxy Watch 4
                  </a>
                  <a class="menuList_link"
                    href="/tech/mobile/samsung-galaxy-z-flip-3-review-the-first-foldable-you-may-actually-want-to-buy/">
                    Samsung Galaxy Z Flip 3
                  </a>
                  <a class="menuList_link"
                    href="/home/<USER>/t-mobile-5g-home-internet-review/">
                    T-Mobile 5G Home Internet
                  </a>
                  <a class="menuList_link"
                    href="/home/<USER>/tp-link-archer-ax21-review/">
                    TP-Link Archer AX21
                  </a>
                </div>
              </div>


            </div>
          </div>
        </div>

        <div class="navMenu" role="menuitem" aria-haspopup="true" section="news"
          data-item="menuContainer">
          <button class="navMenuItem" aria-label="News" data-item="menuItem">
            <span class="navMenuItem_text"> <span class="lastWord">News</span>
            </span>
            <svg class="navMenuItem_icon caret">
              <use aria-hidden="false" xlink:href="#caret"></use>
            </svg>

          </button>
          <div class="m-menu" data-item="navMenu">

            <div class="m-menu_menuListContainer">
              <div class="menuList">
                <a class="menuList_title" href="/news/">All News</a>
                <div class="menuList_linkHolder o-gridVerticalList">
                  <a class="menuList_link" href="/5g/">
                    5G
                  </a>
                  <a class="menuList_link" href="/tech-enabled/">
                    Accessibility
                  </a>
                  <a class="menuList_link" href="/tags/amazon/">
                    Amazon
                  </a>
                  <a class="menuList_link" href="/apple/">
                    Apple
                  </a>
                  <a class="menuList_link" href="/coronavirus/">
                    COVID-19
                  </a>
                  <a class="menuList_link"
                    href="/tech/services-and-software/online/cybersecurity/">
                    Cybersecurity
                  </a>
                  <a class="menuList_link"
                    href="/crossing-the-broadband-divide/">
                    Digital Divide
                  </a>
                  <a class="menuList_link" href="/tags/facebook/">
                    Facebook
                  </a>
                  <a class="menuList_link" href="/google/">
                    Google
                  </a>
                  <a class="menuList_link" href="/tech/mobile/">
                    Mobile
                  </a>
                  <a class="menuList_link" href="/now-what/">
                    Now What
                  </a>
                  <a class="menuList_link" href="/topics/science/">
                    Science
                  </a>
                  <a class="menuList_link" href="/topics/tech-industry/">
                    Tech Industry
                  </a>
                </div>
              </div>


              <div class="menuList">
                <a class="menuList_title" href="/topics/culture/">Culture</a>
                <div class="menuList_linkHolder o-gridVerticalList">
                  <a class="menuList_link" href="/topics/comics/">
                    Comics
                  </a>
                  <a class="menuList_link" href="/games/">
                    Gaming
                  </a>
                  <a class="menuList_link" href="/topics/internet-culture/">
                    Internet
                  </a>
                  <a class="menuList_link" href="/topics/music/">
                    Music
                  </a>
                  <a class="menuList_link" href="/topics/politics/">
                    Politics
                  </a>
                  <a class="menuList_link" href="/topics/sports/">
                    Sports
                  </a>
                  <a class="menuList_link" href="/topics/toys-and-games/">
                    Toys &amp; Games
                  </a>
                  <a class="menuList_link" href="/topics/tv-and-movies/">
                    TV &amp; Movies
                  </a>
                </div>
              </div>


              <div class="menuList">
                <a class="menuList_title" href="/special-features/">More</a>
                <div class="menuList_linkHolder o-gridVerticalList">
                  <a class="menuList_link" href="/user/join-cnet/">
                    Newsletters
                  </a>
                  <a class="menuList_link" href="/pictures/">
                    Photo Galleries
                  </a>
                  <a class="menuList_link" href="/special-features/">
                    Special Features
                  </a>
                  <a class="menuList_link" href="/videos/">
                    Videos
                  </a>
                </div>
              </div>

            </div>
          </div>
        </div>

        <div class="navMenu" role="menuitem" aria-haspopup="true" section="tech"
          data-item="menuContainer">
          <button class="navMenuItem" aria-label="Tech" data-item="menuItem">
            <span class="navMenuItem_text"> <span class="lastWord">Tech</span>
            </span>
            <svg class="navMenuItem_icon caret">
              <use aria-hidden="false" xlink:href="#caret"></use>
            </svg>

          </button>
          <div class="m-menu center" data-item="navMenu">

            <div class="m-menu_menuListContainer">
              <div class="menuList">
                <a class="menuList_title" href="/tech/">All Tech</a>
                <div class="menuList_linkHolder o-gridVerticalList">
                  <a class="menuList_link" href="/tech/computing/">
                    Computing
                  </a>
                  <a class="menuList_link" href="/tech/gaming/">
                    Gaming
                  </a>
                  <a class="menuList_link" href="/tech/home-entertainment/">
                    Home Entertainment
                  </a>
                  <a class="menuList_link" href="/tech/mobile/">
                    Mobile
                  </a>
                  <a class="menuList_link" href="/tech/services-and-software/">
                    Services &amp; Software
                  </a>
                </div>
              </div>

            </div>
          </div>
        </div>

        <div class="navMenu" role="menuitem" aria-haspopup="true"
          section="finance" data-item="menuContainer">
          <button class="navMenuItem" aria-label="Money" data-item="menuItem">
            <span class="navMenuItem_text"> <span class="lastWord">Money</span>
            </span>
            <svg class="navMenuItem_icon caret">
              <use aria-hidden="false" xlink:href="#caret"></use>
            </svg>

          </button>
          <div class="m-menu center" data-item="navMenu">

            <div class="m-menu_menuListContainer">
              <div class="menuList">
                <a class="menuList_title" href="/personal-finance/">All
                  Money</a>
                <div class="menuList_linkHolder o-gridVerticalList">
                  <a class="menuList_link" href="/personal-finance/banking/">
                    Banking
                  </a>
                  <a class="menuList_link"
                    href="/personal-finance/credit-cards/">
                    Credit Cards
                  </a>
                  <a class="menuList_link"
                    href="/personal-finance/cryptocurrency/">
                    Cryptocurrency
                  </a>
                  <a class="menuList_link" href="/personal-finance/insurance/">
                    Insurance
                  </a>
                  <a class="menuList_link" href="/personal-finance/investing/">
                    Investing
                  </a>
                  <a class="menuList_link" href="/personal-finance/loans/">
                    Loans
                  </a>
                  <a class="menuList_link" href="/personal-finance/mortgages/">
                    Mortgages
                  </a>
                  <a class="menuList_link" href="/personal-finance/taxes/">
                    Taxes
                  </a>
                  <a class="menuList_link" href="/personal-finance/your-money/">
                    Your Money
                  </a>
                </div>
              </div>

            </div>
          </div>
        </div>

        <div class="navMenu" role="menuitem" aria-haspopup="true"
          section="health" data-item="menuContainer">
          <button class="navMenuItem" aria-label="Wellness"
            data-item="menuItem">
            <span class="navMenuItem_text"> <span
                class="lastWord">Wellness</span>
            </span>
            <svg class="navMenuItem_icon caret">
              <use aria-hidden="false" xlink:href="#caret"></use>
            </svg>

          </button>
          <div class="m-menu center" data-item="navMenu">

            <div class="m-menu_menuListContainer">
              <div class="menuList">
                <a class="menuList_title" href="/health/">All Wellness</a>
                <div class="menuList_linkHolder o-gridVerticalList">
                  <a class="menuList_link" href="/health/fitness/">
                    Fitness
                  </a>
                  <a class="menuList_link" href="/health/medical/">
                    Medical
                  </a>
                  <a class="menuList_link" href="/health/nutrition/">
                    Nutrition
                  </a>
                  <a class="menuList_link" href="/health/parenting/">
                    Parenting
                  </a>
                  <a class="menuList_link" href="/health/personal-care/">
                    Personal Care
                  </a>
                  <a class="menuList_link" href="/health/sleep/">
                    Sleep
                  </a>
                </div>
              </div>

            </div>
          </div>
        </div>

        <div class="navMenu" role="menuitem" aria-haspopup="true"
          section="cnet-home" data-item="menuContainer">
          <button class="navMenuItem" aria-label="Home" data-item="menuItem">
            <span class="navMenuItem_text"> <span class="lastWord">Home</span>
            </span>
            <svg class="navMenuItem_icon caret">
              <use aria-hidden="false" xlink:href="#caret"></use>
            </svg>

          </button>
          <div class="m-menu center" data-item="navMenu">

            <div class="m-menu_menuListContainer">
              <div class="menuList">
                <a class="menuList_title" href="/home/">CNET Home</a>
                <div class="menuList_linkHolder o-gridVerticalList">
                  <a class="menuList_link" href="/home/<USER>/">
                    Home Energy &amp; Utilities
                  </a>
                  <a class="menuList_link" href="/home/<USER>/">
                    Home Internet
                  </a>
                  <a class="menuList_link" href="/home/<USER>/">
                    Home Security
                  </a>
                  <a class="menuList_link" href="/home/<USER>/">
                    Kitchen &amp; Household
                  </a>
                  <a class="menuList_link" href="/home/<USER>/">
                    Smart Home
                  </a>
                  <a class="menuList_link" href="/home/<USER>/">
                    Yard &amp; Outdoors
                  </a>
                </div>
              </div>

            </div>
          </div>
        </div>

        <div class="navMenu" role="menuitem" aria-haspopup="true" section="cars"
          data-item="menuContainer">
          <button class="navMenuItem" aria-label="Cars" data-item="menuItem">
            <span class="navMenuItem_text"> <span class="lastWord">Cars</span>
            </span>
            <svg class="navMenuItem_icon caret">
              <use aria-hidden="false" xlink:href="#caret"></use>
            </svg>

          </button>
          <div class="m-menu center" data-item="navMenu">

            <div class="m-menu_menuListContainer">
              <div class="menuList">
                <a class="menuList_title" href="/roadshow/">Roadshow</a>
                <div class="menuList_linkHolder o-gridVerticalList">
                  <a class="menuList_link" href="/roadshow/reviews/">
                    Reviews
                  </a>
                  <a class="menuList_link" href="/roadshow/videos/">
                    Video
                  </a>
                  <a class="menuList_link" href="/roadshow/news/">
                    News
                  </a>
                  <a class="menuList_link" href="/roadshow/pictures/">
                    Pictures
                  </a>
                  <a class="menuList_link" href="/roadshow/recalls/">
                    Recalls
                  </a>
                  <a class="menuList_link"
                    href="/roadshow/roadshow-auto-buying-program/">
                    Auto Buying Program
                  </a>
                  <a class="menuList_link" href="/carfection/">
                    Carfection
                  </a>
                  <a class="menuList_link" href="/roadshow/cooley-on-cars/">
                    Cooley On Cars
                  </a>
                </div>
              </div>


              <div class="menuList">
                <a class="menuList_title" href="/roadshow/best-cars/">Best
                  cars</a>
                <div class="menuList_linkHolder o-gridVerticalList">
                  <a class="menuList_link"
                    href="/roadshow/news/best-affordable-cars/">
                    Best Affordable Cars
                  </a>
                  <a class="menuList_link"
                    href="/roadshow/news/best-convertible/">
                    Best Convertibles
                  </a>
                  <a class="menuList_link"
                    href="/roadshow/news/best-electric-cars/">
                    Best Electric Cars
                  </a>
                  <a class="menuList_link"
                    href="/roadshow/best-cars/best-family-cars/">
                    Best Family Cars
                  </a>
                  <a class="menuList_link"
                    href="/roadshow/best-cars/best-fuel-efficient-cars/">
                    Best Fuel-Efficient Cars
                  </a>
                  <a class="menuList_link"
                    href="/roadshow/news/best-hybrid-vehicles/">
                    Best Hybrids
                  </a>
                  <a class="menuList_link"
                    href="/roadshow/news/best-luxury-suv-for-2021/">
                    Best Luxury SUVs
                  </a>
                  <a class="menuList_link"
                    href="/roadshow/news/best-midsize-suv/">
                    Best Midsize SUVs
                  </a>
                  <a class="menuList_link"
                    href="/roadshow/best-cars/best-sedans/">
                    Best Sedans
                  </a>
                  <a class="menuList_link"
                    href="/roadshow/news/best-small-compact-suv/">
                    Best Small SUVs
                  </a>
                  <a class="menuList_link" href="/roadshow/news/best-suvs/">
                    Best SUVs
                  </a>
                  <a class="menuList_link"
                    href="/roadshow/news/best-high-tech-car/">
                    Best Tech Cars
                  </a>
                  <a class="menuList_link"
                    href="/roadshow/best-cars/best-trucks/">
                    Best Trucks
                  </a>
                  <a class="menuList_link"
                    href="/roadshow/news/best-used-cars-new-luxury-vehicle-tesla-model-3/">
                    Best Used Cars
                  </a>
                </div>
              </div>


              <div class="menuList">
                <a class="menuList_title"
                  href="/roadshow/best-car-products/">Best car products</a>
                <div class="menuList_linkHolder o-gridVerticalList">
                  <a class="menuList_link"
                    href="/roadshow/news/best-backup-cameras-for-2021/">
                    Best Backup Camera
                  </a>
                  <a class="menuList_link"
                    href="/roadshow/news/best-car-battery-chargers/">
                    Best Car Battery Charger
                  </a>
                  <a class="menuList_link"
                    href="/roadshow/news/best-car-cover/">
                    Best Car Cover
                  </a>
                  <a class="menuList_link"
                    href="/roadshow/news/best-car-vacuum/">
                    Best Car Vacuum
                  </a>
                  <a class="menuList_link" href="/roadshow/news/best-dash-cam/">
                    Best Dash Cam
                  </a>
                  <a class="menuList_link"
                    href="/roadshow/news/best-portable-jump-starter/">
                    Best Portable Jump Starter
                  </a>
                  <a class="menuList_link"
                    href="/roadshow/news/best-portable-tire-inflator/">
                    Best Portable Tire Inflator
                  </a>
                  <a class="menuList_link"
                    href="/roadshow/news/best-radar-detector/">
                    Best Radar Detector
                  </a>
                </div>
              </div>

            </div>
          </div>
        </div>

        <div class="navMenu" role="menuitem" aria-haspopup="true"
          section="deals" data-item="menuContainer">
          <button class="navMenuItem" aria-label="Deals" data-item="menuItem">
            <span class="navMenuItem_text"> <span class="lastWord">Deals</span>
            </span>
            <svg class="navMenuItem_icon caret">
              <use aria-hidden="false" xlink:href="#caret"></use>
            </svg>

          </button>
          <div class="m-menu center" data-item="navMenu">

            <div class="m-menu_menuListContainer">
              <div class="menuList">
                <a class="menuList_title" href="/deals/">All Deals</a>
                <div class="menuList_linkHolder o-gridVerticalList">
                  <a class="menuList_link" href="/cheapskate/">
                    The Cheapskate
                  </a>
                  <a class="menuList_link"
                    href="/news/best-apple-watch-deals-save-79-on-a-product-red-series-6/">
                    Best Apple Watch Deals
                  </a>
                  <a class="menuList_link"
                    href="/news/best-laptop-deals-save-200-on-a-dell-inspiron-400-on-microsoft-surface-laptop-3-and-more/">
                    Best Laptop Deals
                  </a>
                  <a class="menuList_link"
                    href="/news/best-apple-macbook-deals-save-100-on-m1-macbook-air-or-pro/">
                    Best Macbook Deals
                  </a>
                  <a class="menuList_link"
                    href="/health/sleep/best-mattress-deals/">
                    Best Mattress Deals
                  </a>
                  <a class="menuList_link"
                    href="/news/best-meal-kit-deals-and-sign-up-offers-2021/">
                    Best Meal Kit Deals
                  </a>
                  <a class="menuList_link" href="/news/best-tv-deals/">
                    Best TV Deals
                  </a>
                  <a class="menuList_link"
                    href="/news/best-vpn-sales-and-deals-right-now/">
                    Best VPN Deals
                  </a>
                </div>
              </div>


              <div class="menuList">
                <a class="menuList_title"
                  href="https://coupons.cnet.com/?ref=cnet.com">Coupons</a>
                <div class="menuList_linkHolder o-gridVerticalList">
                  <a class="menuList_link"
                    href="https://coupons.cnet.com/coupons/adidas?ref=cnet.com">
                    Adidas Coupons
                  </a>
                  <a class="menuList_link"
                    href="https://coupons.cnet.com/coupons/ashley-furniture?ref=cnet.com">
                    Ashley Furniture Coupons
                  </a>
                  <a class="menuList_link"
                    href="https://coupons.cnet.com/coupons/dicks-sporting-goods?ref=cnet.com">
                    Dick's Sporting Goods Coupons
                  </a>
                  <a class="menuList_link"
                    href="https://coupons.cnet.com/coupons/doordash?ref=cnet.com">
                    DoorDash Promo Codes
                  </a>
                  <a class="menuList_link"
                    href="https://coupons.cnet.com/coupons/ebay?ref=cnet.com">
                    eBay Coupons
                  </a>
                  <a class="menuList_link"
                    href="https://coupons.cnet.com/coupons/expressvpn?ref=cnet.com">
                    ExpressVPN Coupons
                  </a>
                  <a class="menuList_link"
                    href="https://coupons.cnet.com/coupons/fanatics?ref=cnet.com">
                    Fanatics Coupons
                  </a>
                  <a class="menuList_link"
                    href="https://coupons.cnet.com/coupons/forever-21?ref=cnet.com">
                    Forever 21 Coupons
                  </a>
                  <a class="menuList_link"
                    href="https://coupons.cnet.com/coupons/grubhub?ref=cnet.com">
                    Grubhub Promo Codes
                  </a>
                  <a class="menuList_link"
                    href="https://coupons.cnet.com/coupons/h-m?ref=cnet.com">
                    H&amp;M Coupons
                  </a>
                  <a class="menuList_link"
                    href="https://coupons.cnet.com/coupons/home-depot?ref=cnet.com">
                    Home Depot Coupons
                  </a>
                  <a class="menuList_link"
                    href="https://coupons.cnet.com/coupons/hotels-com?ref=cnet.com">
                    Hotels.com Coupons
                  </a>
                  <a class="menuList_link"
                    href="https://coupons.cnet.com/coupons/hotwire?ref=cnet.com">
                    Hotwire Promo Codes
                  </a>
                  <a class="menuList_link"
                    href="https://coupons.cnet.com/coupons/macys?ref=cnet.com">
                    Macy's Coupons
                  </a>
                  <a class="menuList_link"
                    href="https://coupons.cnet.com/coupons/nordvpn?ref=cnet.com">
                    NordVPN Coupons
                  </a>
                  <a class="menuList_link"
                    href="https://coupons.cnet.com/coupons/office-depot?ref=cnet.com">
                    Office Depot Coupons
                  </a>
                  <a class="menuList_link"
                    href="https://coupons.cnet.com/coupons/overstock?ref=cnet.com">
                    Overstock Coupons
                  </a>
                  <a class="menuList_link"
                    href="https://coupons.cnet.com/coupons/samsung?ref=cnet.com">
                    Samsung Promo Codes
                  </a>
                  <a class="menuList_link"
                    href="https://coupons.cnet.com/coupons/stubhub?ref=cnet.com">
                    StubHub Discount Codes
                  </a>
                  <a class="menuList_link"
                    href="https://coupons.cnet.com/coupons/surfshark?ref=cnet.com">
                    Surfshark Coupons
                  </a>
                  <a class="menuList_link"
                    href="https://coupons.cnet.com/coupons/viator?ref=cnet.com">
                    Viator Promo Codes
                  </a>
                  <a class="menuList_link"
                    href="https://coupons.cnet.com/coupons/vistaprint?ref=cnet.com">
                    Vistaprint Coupons
                  </a>
                  <a class="menuList_link"
                    href="https://coupons.cnet.com/coupons/vitacost?ref=cnet.com">
                    Vitacost Coupons
                  </a>
                  <a class="menuList_link"
                    href="https://coupons.cnet.com/coupons/walmart?ref=cnet.com">
                    Walmart Promo Codes
                  </a>
                </div>
              </div>

            </div>
          </div>
        </div>

      </div>

      <div class="c-secondaryNav">
        <div class="loggedIn">
          <div class="navMenu" role="menuitem" aria-haspopup="true"
            section="cnetnav|log-in" data-item="menuContainer">
            <button class="navMenuItem" data-item="menuItem">
              <svg class="navMenuItem_icon caret">
                <use aria-hidden="false" xlink:href="#caret"></use>
              </svg>

              <span class="navMenuItem_gravatar" data-user-var="avatar"></span>
              <div class="navMenuItem_text"><span>Hi, </span><span
                  data-user-var="userName"></span></div>
            </button>
            <div class="m-menu" data-item="navMenu">
              <div class="m-menu_menuListContainer">
                <div class="menuList">
                  <span class="menuList_title">Settings</span>
                  <div class="menuList_linkHolder">
                    <a class="menuList_link" data-user-var="profileLink"
                      data-template="/profiles/__username__/">
                      My Profile
                    </a>
                    <a class="menuList_link" href="/user/logout/"
                      data-user-action="logout"
                      data-user-suppress-refresh="true">
                      Sign Out
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <span class="loggedOut" section="sign-in" role="button" tabindex="0"
          data-authentication="{&quot;appId&quot;:376,&quot;refresh&quot;:false}">

          <div class="button button_type_primary button_size_small ">
            <span data-item="buttonText">Join / Sign In</span>
          </div>

        </span>

        <div class="edition" section="edition">
          <div class="navMenu" role="menuitem" aria-haspopup="true"
            section="edition" data-item="menuContainer">
            <button class="navMenuItem" aria-label="Editions"
              data-item="menuItem">
              <span class="navMenuItem_soloIcon">
                <svg class="navMenuItem_icon globe">
                  <use aria-hidden="false" xlink:href="#globe"></use>
                </svg>

              </span>
              <span class="navMenuItem_text"> <span
                  class="lastWord">Editions</span>
              </span>
              <svg class="navMenuItem_icon caret">
                <use aria-hidden="false" xlink:href="#caret"></use>
              </svg>

            </button>
            <div class="m-menu" data-item="navMenu">

              <div class="m-menu_menuListContainer">
                <div class="menuList">
                  <span class="menuList_title">Editions</span>
                  <div class="menuList_linkHolder o-gridVerticalList">
                    <a class="menuList_link -isActive"
                      href="/tech/mobile/pebble-2-pebble-time-2-pebble-core-announced-coming-this-year-and-2017/">
                      English
                    </a>
                    <a class="menuList_link" href="http://www.cnetfrance.fr/"
                      rel="noopener noreferrer" target="_blank"
                      data-component="externalLink">
                      France
                    </a>
                    <a class="menuList_link" href="http://www.cnet.de/"
                      rel="noopener noreferrer" target="_blank"
                      data-component="externalLink">
                      Germany
                    </a>
                    <a class="menuList_link" href="http://japan.cnet.com/">
                      Japan
                    </a>
                    <a class="menuList_link" href="http://www.cnet.co.kr/"
                      rel="noopener noreferrer" target="_blank"
                      data-component="externalLink">
                      Korea
                    </a>
                  </div>
                </div>

              </div>
            </div>
          </div>

        </div>

        <div class="search" section="search">
          <div class="navMenu" role="menuitem" aria-haspopup="true"
            section="search" data-item="menuContainer">
            <button class="navMenuItem" aria-label="Search"
              data-item="menuItem">
              <span class="navMenuItem_soloIcon">
                <svg class="navMenuItem_icon mag">
                  <use aria-hidden="false" xlink:href="#mag"></use>
                </svg>

              </span>
              <span class="navMenuItem_text"> <span
                  class="lastWord">Search</span>
              </span>
              <svg class="navMenuItem_icon caret">
                <use aria-hidden="false" xlink:href="#caret"></use>
              </svg>

            </button>
            <div class="m-menu" data-item="navMenu">
              <div class="smartSearch">
                <form class="smartSearch_form" method="get" action="/search/"
                  data-auto-suggest-options="{&quot;url&quot;:&quot;\/smartsearch\/xhr\/&quot;,&quot;selectorMenu&quot;:&quot;.searchResults&quot;,&quot;selectorSuggestions&quot;:&quot;.searchResults_imgResults a, .searchResults_linkResults a&quot;,&quot;clickable&quot;:true}"
                  data-component="[&quot;formTrim&quot;,&quot;autoSuggest&quot;]"
                  section="brand_nav|top" data-searchtracking="null">
                  <input data-search-input="null" class="smartSearch_formInput"
                    type="search" placeholder="Search" name="query" value=""
                    autocomplete="off"><button class="smartSearch_button"
                    type="submit">
                    <div
                      class="button button_type_secondary button_size_medium ">
                      <span data-item="buttonText">Go</span></div>
                  </button> </form>
              </div>
            </div>
          </div>

        </div>

      </div>
    </nav>















    <div data-ad="intromercial" data-ad-container="61718e4e621d3"
      class="ad-intromercial ad-slot   ">
    </div>



    <div id="rbContent" class="">


      <div class="grid container verticalSubNav -article" next-page-hide="">



        <div class="c-universalSubNav">


          <div class="c-universalSubNav_scrollWrapper ">
            <div class="c-universalSubNav_items" section="categoryNav">
              <a class="c-universalSubNav_subnavLink" section="item.1"
                href="/tech/">
                Featured
              </a>
              <a class="c-universalSubNav_subnavLink -current" section="item.2"
                href="/tech/mobile/">
                Mobile
              </a>
              <a class="c-universalSubNav_subnavLink" section="item.3"
                href="/tech/computing/">
                Computing
              </a>
              <a class="c-universalSubNav_subnavLink" section="item.4"
                href="/tech/gaming/">
                Gaming
              </a>
              <a class="c-universalSubNav_subnavLink" section="item.5"
                href="/tech/home-entertainment/">
                Home Entertainment
              </a>
              <a class="c-universalSubNav_subnavLink" section="item.6"
                href="/tech/services-and-software/">
                Services &amp; Software
              </a>
            </div>
          </div>
        </div>
      </div>







      <div
        id="page-pebble-2-pebble-time-2-pebble-core-announced-coming-this-year-and-2017"
        class="pageWrapper pageContainer current "
        data-container-asset-id="dfb49c4e-2dd0-4182-890c-37897d913be9"
        data-article-options="{&quot;autoplayVideo&quot;:false}"
        data-page-options="{&quot;browserTitle&quot;:&quot;Pebble 2, Pebble Time 2, Pebble Core: Pebble\u0027s fitness hat trick adds heart rate, GPS, Spotify, and 3G&quot;,&quot;browserUrl&quot;:&quot;\/tech\/mobile\/pebble-2-pebble-time-2-pebble-core-announced-coming-this-year-and-2017\/&quot;,&quot;cmsUrl&quot;:&quot;https:\/\/cms.cnet.com\/content\/article\/dfb49c4e-2dd0-4182-890c-37897d913be9&quot;,&quot;initialPage&quot;:false,&quot;trackPageviewUp&quot;:true,&quot;trackingEventData&quot;:[],&quot;trackScroll25&quot;:true,&quot;trackScroll50&quot;:true,&quot;trackScroll75&quot;:true,&quot;trackScroll100&quot;:true}"
        data-component="[&quot;article&quot;,&quot;page&quot;]">





        <div class="content-header">
          <div class="hero-content">
            <div class="container">
              <div class="col-1 spacer"></div>
              <div class="col-10">









                <div class="c-head ">
                  <div class="c-head_hero">
                    <figure class=" img"><img
                        src="https://www.cnet.com/a/img/nkHnBz5tlKgLwecmn-8bs-qiWjI=/1092x614/2016/05/23/b2257bbb-1b75-4390-b5c1-303f2589b427/pebble.jpg"
                        class="" alt="" height="614" width="1092"></figure>
                  </div>




                  <h1 class="speakableText">Pebble 2, Pebble Time 2, Pebble
                    Core: Pebble's fitness hat trick adds heart rate, GPS,
                    Spotify, and 3G</h1>
                  <p class="c-head_dek">Smartwatches with heart rate support,
                    plus a stand-alone puck called Core that acts as a
                    3G-connected smart button: Pebble is trying for a two-device
                    approach in fitness wearables.</p>

                  <div class="row">

                    <div class="col-7 c-head_meta">






                      <section class="row c-assetAuthor" section="author">
                        <div class="c-assetAuthor_profile pic1"
                          section="author_headshot">
                          <a href="/profiles/scottstein8/" aria-hidden="true"
                            rel="author">
                            <figure class=" img"><img
                                src="https://www.cnet.com/a/img/c7b8OSBDuVvEoYLjA0v7ZIiD2iw=/84x84/2012/12/05/71108869-8ae8-11e2-9400-029118418759/Scott_Stein.jpg"
                                class="" alt="Scott Stein headshot" height="84"
                                width="84"></figure>
                          </a>
                        </div>

                        <div class="c-assetAuthor_meta">
                          <div class="c-assetAuthor_authors">
                            <span class="c-assetAuthor_singleLine">
                              <a class="author" rel="author"
                                aria-label="Scott Stein author page"
                                href="/profiles/scottstein8/">Scott Stein</a>
                              <span class="c-assetAuthor_twitter">
                                <a href="http://www.twitter.com/jetscott"
                                  data-component="linkTracker"
                                  data-link-tracker-options="{&quot;action&quot;:&quot;author|twitter&quot;}"
                                  rel="noopener noreferrer" target="_blank">
                                  <div
                                    class="social-button-small-author twitter ">
                                    <svg class=" twitter">
                                      <use aria-hidden="false"
                                        xlink:href="#twitter"></use>
                                    </svg>

                                  </div>

                                </a>
                              </span>
                            </span>
                          </div>

                          <div class="c-assetAuthor_date">

                            <time datetime="2016-05-24T07:00:00-0700">May 24,
                              2016 7:00 a.m. PT</time>
                          </div>
                        </div>
                      </section>
                      <div class="c-head_bottomWrapper">
                        <div class="c-head_audioPlayer loading"
                          data-component="readMore"
                          data-read-more-options="{&quot;url&quot;:&quot;\/news\/dfb49c4e-2dd0-4182-890c-37897d913be9\/audio\/xhr\/?slug=pebble-2-pebble-time-2-pebble-core-announced-coming-this-year-and-2017&amp;typeName=content_article&quot;,&quot;event&quot;:&quot;ready&quot;}">
                          <div data-read-more="container"></div>
                        </div>

                        <div class="c-head_share">





                          <div class="sharebarRedesign atTop"
                            data-component="sharebar"
                            data-sharebar-options="{&quot;title&quot;:&quot;Pebble 2, Pebble Time 2, Pebble Core: Pebble\u0027s fitness hat trick adds heart rate, GPS, Spotify, and 3G&quot;,&quot;description&quot;:&quot;Smartwatches with heart rate support, plus a stand-alone puck called Core that acts as a 3G-connected smart button: Pebble is trying for a two-device approach in fitness wearables.&quot;,&quot;url&quot;:&quot;https:\/\/www.cnet.com\/tech\/mobile\/pebble-2-pebble-time-2-pebble-core-announced-coming-this-year-and-2017\/&quot;,&quot;popupWidth&quot;:780,&quot;popupHeight&quot;:510,&quot;data&quot;:{&quot;media&quot;:&quot;https:\/\/www.cnet.com\/a\/img\/GLVw1pemN5L5Vta4oHRMNcb9Y04=\/196x147\/2016\/05\/23\/ee9a228a-d1d9-40b3-b401-4ba0e07e2fce\/pebblecore3.jpg&quot;},&quot;scrollToComments&quot;:false}">
                            <div class="sharebarWrapper ">
                              <ul class="sharebarContainer -right">

                                <li
                                  class="share-button sharebarRedesignButton moreButton"
                                  data-item="moreTrigger">
                                  <span class="moreWrap">
                                    <a>
                                      <div
                                        class="social-button-small-redesign more">
                                        <svg class=" share">
                                          <use aria-hidden="false"
                                            xlink:href="#share"></use>
                                        </svg>

                                      </div>
                                    </a>
                                  </span>
                                  <ul class="hoverWrap"
                                    data-item="moreDropdown">
                                    <li
                                      class="share-button sharebarRedesignButton facebookWrapper">
                                      <a tabindex="0"
                                        aria-label="Share on Facebook"
                                        data-popup="facebook"
                                        data-tracking="facebooksend">
                                        <div
                                          class="social-button-small-redesign facebook">
                                          <svg class=" facebook">
                                            <use aria-hidden="false"
                                              xlink:href="#facebook"></use>
                                          </svg>

                                        </div>
                                      </a>
                                    </li>


                                    <li
                                      class="share-button sharebarRedesignButton twitterWrapper">
                                      <a tabindex="0"
                                        aria-label="Share on Twitter"
                                        data-popup="twitter"
                                        data-tracking="twitter">
                                        <div
                                          class="social-button-small-redesign twitter">
                                          <svg class=" twitter">
                                            <use aria-hidden="false"
                                              xlink:href="#twitter"></use>
                                          </svg>

                                        </div>
                                      </a>
                                    </li>



                                    <li
                                      class="share-button sharebarRedesignButton flipboardWrapper">
                                      <a tabindex="0"
                                        aria-label="component.sharebar.share_on_flipboard"
                                        data-popup="flipboard"
                                        data-tracking="flipboard">
                                        <div
                                          class="social-button-small-redesign flipboard">
                                          <svg class=" flipboard">
                                            <use aria-hidden="false"
                                              xlink:href="#flipboard"></use>
                                          </svg>

                                        </div>
                                      </a>
                                    </li>


                                    <li
                                      class="share-button sharebarRedesignButton redditWrapper">
                                      <a tabindex="0"
                                        aria-label="component.sharebar.share_on_reddit"
                                        data-popup="reddit"
                                        data-tracking="reddit">
                                        <div
                                          class="social-button-small-redesign reddit">
                                          <svg class=" reddit">
                                            <use aria-hidden="false"
                                              xlink:href="#reddit"></use>
                                          </svg>

                                        </div>
                                      </a>
                                    </li>







                                  </ul>
                                </li>

                                <li
                                  class="share-button sharebarRedesignButton comment-count-redesign"
                                  data-conversation-id="dfb49c4e-2dd0-4182-890c-37897d913be9"
                                  data-article-id="dfb49c4e-2dd0-4182-890c-37897d913be9">
                                  <a href="#comments"
                                    section="sharebar_comments"
                                    aria-controls="disqusContainer-dfb49c4e-2dd0-4182-890c-37897d913be9">
                                    <div
                                      class="social-button-small-redesign comments-wrap">
                                      <span class="disqus-comment-count count"
                                        data-component="disqusCount"
                                        data-disqus-count-options="{&quot;disqusIdentifier&quot;:&quot;dfb49c4e-2dd0-4182-890c-37897d913be9&quot;}"
                                        data-disqus-identifier="dfb49c4e-2dd0-4182-890c-37897d913be9">21</span>
                                      <svg class=" comments">
                                        <use aria-hidden="false"
                                          xlink:href="#comments"></use>
                                      </svg>

                                    </div>
                                  </a>
                                </li>
                              </ul>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>



                </div>
              </div>

            </div>
          </div>
        </div>

        <div class="contentWrap">
          <div class="container">
            <div class="row">
              <div class="row">
                <div class="col-12">
                  <div class="row">
                    <div class="col-8">
                    </div>
                    <div class="col-4">
                    </div>
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col-12">








                  <article id="article-body" class="row" section="article-body"
                    data-component="trackCWV">
                    <img class="recommendationCallback"
                      src="https://i-cmg-amlg-prod.appspot.com/display?id=3f5acfb2-363b-49de-adf9-0fae22789d72"
                      width="1" height="1" border="0" qd919v1ca="">
                    <div class="col-1 spacer"></div>

                    <div class="col-7 article-main-body row "
                      data-component="lazyloadImages">

















                      <svg class="svg-symbol playerControls">
                        <symbol id="play" viewBox="0 0 26.6 32">
                          <path
                            d="M0,2.6c0-2.4,1.6-3.3,3.7-2L25,13.7c2,1.3,2,3.2,0,4.5L3.7,31.4c-2,1.3-3.7,0.3-3.7-2C0,29.4,0,2.6,0,2.6z">
                          </path>
                        </symbol>
                        <symbol id="pause" viewBox="0 0 26.6 32">
                          <g>
                            <polygon
                              points="16,32 26.5,32 26.5,8.8 26.5,0 16,0">
                            </polygon>
                            <rect x="0.1" y="0" width="10.6" height="32"></rect>
                          </g>
                        </symbol>
                        <symbol id="playlist" viewBox="0 0 32 22.9">
                          <g>
                            <circle class="st11" cx="2.3" cy="20.6" r="2.3">
                            </circle>
                            <circle class="st11" cx="2.3" cy="11.4" r="2.3">
                            </circle>
                            <circle class="st11" cx="2.3" cy="2.3" r="2.3">
                            </circle>
                            <path class="st11"
                              d="M32,1.1C32,0.5,31.5,0,30.9,0H10.3C9.7,0,9.1,0.5,9.1,1.1v2.3c0,0.6,0.5,1.1,1.1,1.1h20.6c0.6,0,1.1-0.5,1.1-1.1V1.1z">
                            </path>
                            <path class="st11"
                              d="M32,10.3c0-0.6-0.5-1.1-1.1-1.1H10.3c-0.6,0-1.1,0.5-1.1,1.1v2.3c0,0.6,0.5,1.1,1.1,1.1h20.6c0.6,0,1.1-0.5,1.1-1.1V10.3z">
                            </path>
                            <path class="st11"
                              d="M32,19.4c0-0.6-0.5-1.1-1.1-1.1H10.3c-0.6,0-1.1,0.5-1.1,1.1v2.3c0,0.6,0.5,1.1,1.1,1.1h20.6c0.6,0,1.1-0.5,1.1-1.1V19.4z">
                            </path>
                          </g>
                        </symbol>
                        <symbol id="speaker-on" viewBox="0 0 32 28.1">
                          <g>
                            <g>
                              <path
                                d="M12.6,5L6.3,8.7H0.6C0.3,8.7,0,9,0,9.3V19c0,0.4,0.3,0.7,0.6,0.7h5.9l6.1,3.4c1.3,0.8,1.5,0.2,1.5-1.5V6.5C14.2,4.8,13.9,4.2,12.6,5z">
                              </path>
                            </g>
                            <path
                              d="M18,23.1v-2.8c2.6,0,4.7-2.8,4.7-6.2S20.6,7.8,18,7.8V5c4.2,0,7.5,4,7.5,9S22.1,23.1,18,23.1z">
                            </path>
                            <path
                              d="M21.3,28.1v-2.8c4.4,0,7.9-5,7.9-11.2S25.6,2.8,21.3,2.8V0C27.2,0,32,6.3,32,14C32,21.8,27.2,28.1,21.3,28.1z">
                            </path>
                          </g>
                        </symbol>
                        <symbol id="speaker-off" viewBox="0 0 32 18.8">
                          <g>
                            <g>
                              <path
                                d="M12.6,0.4L6.3,4H0.6C0.3,4,0,4.3,0,4.7v9.7c0,0.4,0.3,0.7,0.6,0.7h5.9l6.1,3.4c1.3,0.8,1.5,0.2,1.5-1.5V1.8C14.2,0.2,13.9-0.5,12.6,0.4z">
                              </path>
                            </g>
                            <polygon
                              points="32,4.5 30.2,2.7 25.3,7.6 20.4,2.7 18.6,4.5 23.5,9.4 18.6,14.3 20.4,16.1 25.3,11.2 30.2,16.1 32,14.3 27.1,9.4 ">
                            </polygon>
                          </g>
                        </symbol>
                        <symbol id="captions" viewBox="0 0 32 16.2">
                          <g>
                            <path
                              d="M8.6,16.2c-1.2,0-2.4-0.2-3.4-0.6c-1.1-0.4-2-0.9-2.7-1.6c-0.8-0.7-1.4-1.6-1.8-2.6C0.2,10.4,0,9.3,0,8.1c0-1.2,0.2-2.3,0.7-3.3s1-1.8,1.8-2.6C3.2,1.5,4.1,1,5.2,0.6S7.4,0,8.6,0c1,0,1.9,0.1,2.7,0.4c0.8,0.3,1.4,0.6,2,1.1c0.6,0.4,1.1,0.9,1.4,1.5c0.4,0.5,0.7,1.1,0.9,1.6l-4.1,1.9c-0.1-0.3-0.2-0.6-0.4-0.9C11,5.3,10.8,5,10.5,4.8c-0.3-0.2-0.6-0.4-0.9-0.5c-0.3-0.1-0.7-0.2-1-0.2C8,4.2,7.5,4.3,7,4.5C6.6,4.7,6.2,5,5.8,5.3C5.5,5.7,5.2,6.1,5.1,6.5C4.9,7,4.8,7.5,4.8,8.1c0,0.5,0.1,1.1,0.3,1.5c0.2,0.5,0.4,0.9,0.8,1.2c0.3,0.4,0.7,0.6,1.2,0.8C7.5,11.9,8,12,8.6,12c0.3,0,0.7-0.1,1-0.2c0.3-0.1,0.6-0.3,0.9-0.5c0.3-0.2,0.5-0.4,0.7-0.7c0.2-0.3,0.3-0.6,0.4-0.9l4.1,1.9c-0.2,0.5-0.5,1-0.9,1.6c-0.4,0.5-0.9,1-1.4,1.5c-0.6,0.4-1.2,0.8-2,1.1C10.5,16,9.6,16.2,8.6,16.2z">
                            </path>
                            <path
                              d="M24.9,16.2c-1.2,0-2.4-0.2-3.4-0.6c-1.1-0.4-2-0.9-2.7-1.6c-0.8-0.7-1.4-1.6-1.8-2.6c-0.4-1-0.7-2.1-0.7-3.3c0-1.2,0.2-2.3,0.7-3.3c0.4-1,1-1.8,1.8-2.6c0.8-0.7,1.7-1.3,2.7-1.7C22.6,0.2,23.7,0,24.9,0c1,0,1.9,0.1,2.7,0.4c0.8,0.3,1.4,0.6,2,1.1c0.6,0.4,1.1,0.9,1.4,1.5C31.5,3.5,31.8,4,32,4.5l-4.1,1.9c-0.1-0.3-0.2-0.6-0.4-0.9c-0.2-0.3-0.4-0.5-0.7-0.7c-0.3-0.2-0.6-0.4-0.9-0.5c-0.3-0.1-0.7-0.2-1-0.2c-0.6,0-1.1,0.1-1.5,0.3c-0.5,0.2-0.9,0.5-1.2,0.8c-0.3,0.4-0.6,0.8-0.8,1.2c-0.2,0.5-0.3,1-0.3,1.5c0,0.5,0.1,1.1,0.3,1.5c0.2,0.5,0.4,0.9,0.8,1.2c0.3,0.4,0.7,0.6,1.2,0.8c0.5,0.2,1,0.3,1.5,0.3c0.3,0,0.7-0.1,1-0.2c0.3-0.1,0.6-0.3,0.9-0.5c0.3-0.2,0.5-0.4,0.7-0.7c0.2-0.3,0.3-0.6,0.4-0.9l4.1,1.9c-0.2,0.5-0.5,1-0.9,1.6c-0.4,0.5-0.9,1-1.4,1.5c-0.6,0.4-1.2,0.8-2,1.1C26.8,16,25.9,16.2,24.9,16.2z">
                            </path>
                          </g>
                        </symbol>
                        <symbol id="share" viewBox="0 0 32 20">
                          <path
                            d="M20,6c0,0-7.9-0.1-13.2,3.3C1.6,12.7,0,20,0,20s5.1-5.4,9.8-7.2C14.9,10.9,20,12,20,12v6l12-8L20,0V6z">
                          </path>
                        </symbol>
                        <symbol id="fullscreen" viewBox="0 0 32 32">
                          <g>
                            <g>
                              <polygon class="st11"
                                points="11.4,0 0,0 0,11.4 3.4,8 9.1,13.7 13.7,9.1 8,3.4 			">
                              </polygon>
                            </g>
                            <g>
                              <polygon class="st11"
                                points="20.6,32 32,32 32,20.6 28.6,24 22.9,18.3 18.3,22.9 24,28.6 			">
                              </polygon>
                            </g>
                            <g>
                              <polygon class="st11"
                                points="32,11.4 32,0 20.6,0 24,3.4 18.3,9.1 22.9,13.7 28.6,8 			">
                              </polygon>
                            </g>
                            <g>
                              <polygon class="st11"
                                points="0,20.6 0,32 11.4,32 8,28.6 13.7,22.9 9.1,18.3 3.4,24 			">
                              </polygon>
                            </g>
                          </g>
                        </symbol>
                        <symbol id="video" viewBox="0 0 32 22">
                          <g>
                            <path
                              d="M29,2.2c-0.7,0.2-4.6,3-7.9,5.3V2.1C21.1,1,20.1,0,19,0H2.1C1,0,0,1,0,2.1v17.7C0,21,1,22,2.1,22H19c1.2,0,2.1-1,2.1-2.1v-5.9c3.3,2.4,7.4,5.4,8.2,5.6c1.4,0.4,2.7-0.1,2.7-0.1V2.2C32,2.2,30.1,1.8,29,2.2z">
                            </path>
                          </g>
                        </symbol>
                      </svg>










                      <div class="shortcode video v2"
                        data-video-playlist="[{&quot;id&quot;:&quot;855ec352-ef0c-4f69-bfbb-e96201c4128e&quot;,&quot;title&quot;:&quot;Pebble unveils two new smartwatches and a wearable not meant for your wrist&quot;,&quot;description&quot;:&quot;Pebble is focusing on fitness, adding heart-rate monitors to its new Pebble 2 and Pebble Time 2. It\u0027s also launching a new wearable called Pebble Core, a mini-Android computer for runners who hate to run with their phones. CNET\u0027s Scott Stein gets a first look from Pebble CEO Eric Migicovsky.&quot;,&quot;slug&quot;:&quot;pebble-unveils-two-new-smartwatches-and-a-wearable-not-meant-for-your-wrist&quot;,&quot;chapters&quot;:{&quot;data&quot;:[],&quot;paging&quot;:{&quot;total&quot;:0,&quot;limit&quot;:15,&quot;offset&quot;:0}},&quot;datePublished&quot;:&quot;2016-05-24 14:00:00&quot;,&quot;duration&quot;:163,&quot;mpxRefId&quot;:&quot;IMktDTgi5Hok23h71paibAiYxSYX5Uyj&quot;,&quot;ratingVChip&quot;:&quot;TV-PG&quot;,&quot;primaryTopic&quot;:{&quot;id&quot;:&quot;********-4277-4785-9963-d0a540c5f9c8&quot;},&quot;author&quot;:{&quot;id&quot;:&quot;&quot;,&quot;firstName&quot;:&quot;&quot;,&quot;lastName&quot;:&quot;&quot;},&quot;primaryCollection&quot;:{&quot;id&quot;:&quot;2adf6930-fe69-4423-ab9c-9aae7f4f1fc2&quot;,&quot;title&quot;:&quot;CNET First Look&quot;},&quot;image&quot;:{&quot;path&quot;:&quot;https:\/\/www.cnet.com\/a\/img\/wWuMVN-uxoxPom80yqkfq5dA-n4=\/940x528\/2016\/05\/23\/b2257bbb-1b75-4390-b5c1-303f2589b427\/pebble.jpg&quot;},&quot;thumbnail&quot;:&quot;https:\/\/www.cnet.com\/a\/img\/5sIu1P5s4EhDgPOtYqAmuAVjjYs=\/194x109\/2016\/05\/23\/b2257bbb-1b75-4390-b5c1-303f2589b427\/pebble.jpg&quot;,&quot;closedCaptionPath&quot;:&quot;\/videos\/captions\/webvtt\/pebble-unveils-two-new-smartwatches-and-a-wearable-not-meant-for-your-wrist.vtt&quot;,&quot;urlPath&quot;:&quot;\/videos\/pebble-unveils-two-new-smartwatches-and-a-wearable-not-meant-for-your-wrist\/&quot;,&quot;isVertical&quot;:false,&quot;m3u8&quot;:&quot;https:\/\/mt-rv-v1.cnet.com\/vr\/2019\/10\/04\/691333699557\/140427_hls\/master.m3u8&quot;,&quot;mp4&quot;:&quot;https:\/\/mt-rv-v1.cnet.com\/vr\/2019\/10\/04\/691333699557\/FL_Pebble2016_140426_740.mp4&quot;,&quot;index&quot;:0}]">
                        <div class="embeddedVideoContainer" tabindex="0"
                          aria-label="Play video Pebble unveils two new smartwatches and a wearable not meant for your wrist"
                          data-load-video="0"
                          data-video-id="855ec352-ef0c-4f69-bfbb-e96201c4128e">
                          <div class="videoContainer">
                            <svg class=" play">
                              <use aria-hidden="false" xlink:href="#play"></use>
                            </svg>

                            <svg class=" pause">
                              <use aria-hidden="false" xlink:href="#pause">
                              </use>
                            </svg>


                            <figure class=" img"><img
                                src="https://www.cnet.com/a/img/INtYQUV_k360gJohjgKIkyC4ebA=/196x110/2016/05/23/b2257bbb-1b75-4390-b5c1-303f2589b427/pebble.jpg"
                                class="photo" alt="" height="110" width="196"
                                loading="lazy"><noscript><img
                                  src="https://www.cnet.com/a/img/INtYQUV_k360gJohjgKIkyC4ebA=/196x110/2016/05/23/b2257bbb-1b75-4390-b5c1-303f2589b427/pebble.jpg"
                                  class="photo" alt="" height="110"
                                  width="196"></noscript></figure>
                          </div>
                          <div class="videoTitle">
                            <span class="bold">
                              <span class="nowPlaying">Now playing:</span>
                              <span class="watchThis">Watch this:</span>
                            </span>
                            Pebble unveils two new smartwatches and a wearable
                            not...
                          </div>
                          <div class="meta videoMeta">
                            <span class="duration">
                              <svg class=" video">
                                <use aria-hidden="false" xlink:href="#video">
                                </use>
                              </svg>
                              2:43
                            </span>
                          </div>
                        </div>
                      </div>
                      <p class="speakableTextP1"><em><strong>Editors' note,
                            December 7, 2016</strong>: Pebble has announced that
                          its assets <a
                            href="https://blog.getpebble.com/2016/12/07/fitbit/"
                            rel="noopener noreferrer nofollow" target="_blank"
                            data-component="externalLink">have been acquired by
                            Fitbit</a> and it will no longer release new
                          products. While the Pebble 2 and other Pebble watches
                          will continue to work, no new software updates or
                          features will be released. Pebble</em><em> also warned
                          that down the road functionality or service quality
                          may be reduced. </em><em>Kickstarter backers of the
                          Pebble Time 2 and Pebble Core will receive a full
                          refund for their purchase.</em><em> </em></p>
                      <p class="speakableTextP2">Pebble's back with more
                        smartwatches...and something else.</p>
                      <p>Good news if you were a fan of Pebble's formula for
                        smartwatches: There are two new watches on their way
                        this September. They're more affordable, look better and
                        have added heart rate tracking. The Pebble 2 and Pebble
                        Time 2 <a href="http://pebble.com/kickstarter"
                          rel="noopener noreferrer nofollow" target="_blank"
                          data-component="externalLink">hit Kickstarter</a>
                        today. But there's another thing coming early next year,
                        called Pebble Core, that's not a watch at all.</p>
                      <p>It's a box. A clip-on. A keychain. A smart button. A
                        Spotify music player. A GPS and even 3G-enabled fitness
                        tracker. An emergency gadget-slash-Pebble accessory. A
                        programmable Android mini-computer.</p>
                      <div class="shortcode gallery" section="shortcodeGallery"
                        data-track="embedGallery"
                        data-component="moduleAssetTracker">
                        <h2 class="hed">Pebble 2, Pebble Time 2 and Pebble Core:
                          Two watches and a button (photos)</h2>
                        <a class="allLink"
                          href="/pictures/heres-what-the-new-pebble-2-and-time-2-look-like-next-to-pebble-core/"><svg
                            class=" media-gallery-icon">
                            <use aria-hidden="false"
                              xlink:href="#media-gallery-icon"></use>
                          </svg>
                          <span class="galleryText">See all photos</span>
                        </a>
                        <div class="grid">
                          <div data-item="image" section="galleryItem1">
                            <a
                              href="/pictures/heres-what-the-new-pebble-2-and-time-2-look-like-next-to-pebble-core/">
                              <figure class=" img"><img
                                  src="https://www.cnet.com/a/img/lhsAsbfXGQze1uEJcWPEbTaoqKY=/532x299/2016/05/23/81bf1e55-e9fe-4b65-a12a-b0c29b0dff85/pebblecore.jpg"
                                  class="" alt="" height="299" width="532"
                                  loading="lazy"><noscript><img
                                    src="https://www.cnet.com/a/img/lhsAsbfXGQze1uEJcWPEbTaoqKY=/532x299/2016/05/23/81bf1e55-e9fe-4b65-a12a-b0c29b0dff85/pebblecore.jpg"
                                    class="" alt="" height="299"
                                    width="532"></noscript></figure>
                            </a>
                          </div>
                          <div data-item="image" section="galleryItem2">
                            <a
                              href="/pictures/heres-what-the-new-pebble-2-and-time-2-look-like-next-to-pebble-core/">
                              <figure class=" img"><img
                                  src="https://www.cnet.com/a/img/r103cGeuwl0zqo8I6P28ajWqWJU=/532x299/2016/05/23/d149c6c1-4c82-40b9-a2d8-c392d949b742/pebbletime2core.jpg"
                                  class="" alt="" height="299" width="532"
                                  loading="lazy"><noscript><img
                                    src="https://www.cnet.com/a/img/r103cGeuwl0zqo8I6P28ajWqWJU=/532x299/2016/05/23/d149c6c1-4c82-40b9-a2d8-c392d949b742/pebbletime2core.jpg"
                                    class="" alt="" height="299"
                                    width="532"></noscript></figure>
                            </a>
                          </div>
                          <div data-item="image" section="galleryItem3">
                            <a
                              href="/pictures/heres-what-the-new-pebble-2-and-time-2-look-like-next-to-pebble-core/">
                              <figure class=" img"><img
                                  src="https://www.cnet.com/a/img/QzLDsyYXphQiVK8bE63NjtkRE7o=/532x299/2016/05/24/4711139d-b016-4af3-9027-56dced749cb3/pebblefamilywhtcrop.jpg"
                                  class="" alt="" height="299" width="532"
                                  loading="lazy"><noscript><img
                                    src="https://www.cnet.com/a/img/QzLDsyYXphQiVK8bE63NjtkRE7o=/532x299/2016/05/24/4711139d-b016-4af3-9027-56dced749cb3/pebblefamilywhtcrop.jpg"
                                    class="" alt="" height="299"
                                    width="532"></noscript></figure>
                              <span class="more">+12 More</span>
                            </a>
                          </div>
                        </div>
                      </div>

                      <p>And it doesn't even have a screen.</p>
                      <p>I got a chance to look at both at Pebble's headquarters
                        in Redwood City, California, while talking with CEO Eric
                        Migicovsky about why he's decided to move beyond the
                        wrist for the first time.</p>
                      <figure
                        class="image image-large pull-none hasCaption shortcode"
                        section="shortcodeImage"><span
                          class="imageContainer"><span><img
                              src="https://www.cnet.com/a/img/up4mZ8vK14vc6BuSynrWK3DTniM=/1092x0/2016/05/23/81bf1e55-e9fe-4b65-a12a-b0c29b0dff85/pebblecore.jpg"
                              class="" alt="pebblecore.jpg" height="728"
                              width="1092"></span></span>
                        <figcaption><span class="caption">
                            <p>Pebble Core is a little two-button clip-on.</p>
                          </span><span class="credit">
                            John Kim/CNET
                          </span></figcaption>
                      </figure>
                      <h3>Pebble Core: A little do-it-all smart button
                        box-slash-music player-slash-fitness thing</h3>
                      <p>Migicovsky has believed in fitness on smartwatches
                        since the Pebble first launched. The campaign video for
                        the Kickstarter campaign of the <a
                          href="https://www.cnet.com/news/pebble-smart-watch-sells-out-on-kickstarter/">first
                          Pebble Watch</a> in 2012 focused on running, cycling
                        and swim tracking. Migicovsky is a fitness addict. He's
                        also casual. He met with me wearing a t-shirt, cargo
                        shorts and flip-flops.</p>
                      <p>Pebble watches run their own operating system, and have
                        their own unique advantages compared to other watches in
                        the ever-more-crowded wearable world. They have
                        always-on reflective displays, week-plus battery life
                        and better water resistance. But they're also less
                        feature-rich than <a
                          href="https://www.cnet.com/news/android-wear-2-google-smartwatches-update/">Android
                          Wear</a>, <a
                          href="https://www.cnet.com/products/apple-watch/">Apple
                          Watch</a> and <a
                          href="https://www.cnet.com/products/samsung-gear-s2/">Samsung's
                          Gear S2</a>. For instance, they can't directly play
                        music.</p>
                      <p>The Core feels like a bundle of smartwatch features all
                        put into a separate kinda-sorta accessory. It has 4GB of
                        storage and dedicated Spotify support to download music
                        playlists (or, even stream over cellular wireless
                        connection once a 3G SIM card is popped in), it has GPS
                        and it has two touch surfaces that act as buttons, which
                        can be programmed for anything from hailing an Uber to
                        sending an emergency text SOS. Or, opening a smart lock.
                        "In some ways it's a computer on your keychain," says
                        Migicovsky.</p>
                      <p>It's small, it's plastic. And it looks more like a
                        pebble than anything Pebble's made before.</p>
                      <figure
                        class="image image-large pull-none hasCaption shortcode"
                        section="shortcodeImage"><span
                          class="imageContainer"><img
                            src="https://www.cnet.com/a/img/heo9arkg1YoMpq7P_WR9uORLjHU=/1092x0/2016/05/23/8e4c99f3-c663-4996-b651-dbf387b0fa4b/pebblecore2.jpg"
                            class="" alt="pebblecore2.jpg" height="728"
                            width="1092" loading="lazy"><noscript><img
                              src="https://www.cnet.com/a/img/heo9arkg1YoMpq7P_WR9uORLjHU=/1092x0/2016/05/23/8e4c99f3-c663-4996-b651-dbf387b0fa4b/pebblecore2.jpg"
                              class="" alt="pebblecore2.jpg" height="728"
                              width="1092"></noscript></span>
                        <figcaption><span class="caption">
                            <p>Yes, it's small.</p>
                          </span><span class="credit">
                            John Kim/CNET
                          </span></figcaption>
                      </figure>
                      <p>"I love running with Pebble, but at the same time, I
                        end up taking my phone because I like to listen to
                        Spotify when I run and I like GPS for tracking,"
                        explains Migicovsky. "And we looked at it and we looked
                        at integrating it all into a watch. And we decided to
                        actually create a secondary device, something that clips
                        on to your waist belt or you can put in your pocket."
                      </p>
                      <p>"We built it because we got feedback from our users
                        that said they didn't want to run with their phone,"
                        says Migicovsky. In that sense, Pebble Core is almost
                        like part of a high-end 3G GPS-enabled smartwatch like
                        the <a
                          href="https://www.cnet.com/products/samsung-gear-s2-3g/">Samsung
                          Gear S2 3G</a>, peeled off and turned into its own
                        device.</p>
                      <figure class="image image-large pull-none shortcode"
                        section="shortcodeImage"><span
                          class="imageContainer"><img
                            src="https://www.cnet.com/a/img/rb7RFxVNc6vlDbuft7vQuMJrtWA=/1092x0/2016/05/23/ee9a228a-d1d9-40b3-b401-4ba0e07e2fce/pebblecore3.jpg"
                            class="" alt="pebblecore3.jpg" height="728"
                            width="1092" loading="lazy"><noscript><img
                              src="https://www.cnet.com/a/img/rb7RFxVNc6vlDbuft7vQuMJrtWA=/1092x0/2016/05/23/ee9a228a-d1d9-40b3-b401-4ba0e07e2fce/pebblecore3.jpg"
                              class="" alt="pebblecore3.jpg" height="728"
                              width="1092"></noscript></span>
                        <figcaption><span class="credit">
                            John Kim/CNET
                          </span></figcaption>
                      </figure>
                      <p>I couldn't try Pebble Core. It was just a screenless
                        box I held for a few minutes. I was told it runs
                        Android, but I couldn't tell how, or in what way. But
                        Migicovsky proudly promises the Pebble Core is
                        "hackable", meaning you can install apps and program its
                        buttons in a variety of ways. ("You can write apps in
                        JavaScript for it," he explains, "they could store it on
                        here.") There's even a mounting input on the back, if
                        you take off the magnetic loop that normally clips onto
                        a belt or keyring. </p>
                      <p>Pebble Core could be a super-smart version of a <a
                          href="http://www.zdnet.com/article/review-tile-bluetooth-tag-verdict-great/"
                          rel="noopener noreferrer" target="_blank"
                          data-component="externalLink">Tile</a> thing-finder,
                        or simply a music player (it has a headphone jack and
                        Bluetooth) and GPS-connected fitness tracker. It
                        connects with a Pebble watch, or it can work alone. And
                        at just $69 for its Kickstarter price (about £50 or
                        AU$100 converted) it may well be the
                        wireless-headphone-friendly, Spotify-capable iPod
                        Shuffle replacement that many runners have long dreamed
                        of. Battery life lasts for up to 9 hours of music
                        playback with GPS enabled, or for days on standby. And
                        the Core charges wirelessly using the popular Qi
                        standard, unlike Pebble's watches that use their own
                        custom charge cables. </p>
                      <p>Migicovsky sees a future where many little wearable
                        devices work together all over your body, not just on
                        the wrist: "I see a future where at some point you're
                        gonna have multiple wearables that all talk to each
                        other, that connect through the Internet." Pebble Core
                        is clearly aiming to take a first bite of it. </p>
                      <figure
                        class="image image-large pull-none hasCaption shortcode"
                        section="shortcodeImage"><span
                          class="imageContainer"><img
                            src="https://www.cnet.com/a/img/ajxL2NE96o1ciu8UAZpnH9SgbSc=/1092x0/2016/05/24/0e502050-b202-4152-8c2e-5c9bcade7fba/pebble2-pebbletime2.jpg"
                            class="" alt="pebble2-pebbletime2.jpg" height="728"
                            width="1092" loading="lazy"><noscript><img
                              src="https://www.cnet.com/a/img/ajxL2NE96o1ciu8UAZpnH9SgbSc=/1092x0/2016/05/24/0e502050-b202-4152-8c2e-5c9bcade7fba/pebble2-pebbletime2.jpg"
                              class="" alt="pebble2-pebbletime2.jpg"
                              height="728" width="1092"></noscript></span>
                        <figcaption><span class="caption">
                            <p>Pebble Time 2 (left) and Pebble 2 (right): color,
                              black and white.</p>
                          </span><span class="credit">
                            John Kim/CNET
                          </span></figcaption>
                      </figure>
                      <h3>Pebble watches with heart rate are closer to Fitbit
                        than ever</h3>
                      <p>The watches are coming in September and fit a far
                        clearer need. They're both distinct, affordably priced
                        improvements to existing Pebble watches. And they both
                        come closer to being full-fledged fitness trackers.</p>
                      <p>Most notably, they both have optical heart rate
                        tracking via a little bump on the back where continuous
                        heart rate readings are measured on-wrist once every 10
                        minutes, or continuously during exercise.</p>
                      <p>The Pebble 2 is a reboot of the very first black and
                        white <a
                          href="https://www.cnet.com/products/pebble-watch/">plastic
                          Pebble</a>, redone with a Gorilla Glass-covered black
                        and white display and a slim, clean look. It has a
                        microphone, just like the Pebble Time, for voice
                        responses to messages. It's 30-meter water resistant. It
                        costs just $129. (That converts to about £90 or AU$180.)
                      </p>
                      <p>Pebble Time 2 gets rid of any distinction between the
                        plastic <a
                          href="https://www.cnet.com/products/pebble-time/">Pebble
                          Time</a> and its better <a
                          href="https://www.cnet.com/products/pebble-time-steel/">all-steel
                          model</a>. This is basically the Time Steel with heart
                        rate tracking and a 50 percent larger color screen, at a
                        lower $199 price (that converts to about £135 or
                        AU$275). Both watches are compatible with the same
                        magnetic chargers, and Pebble's embryonic smart strap
                        system, which allows bands to attach to the rear charge
                        port to access data and power. </p>
                      <p>One year later, and smart straps still seem more like a
                        promise than a reality, with only a few <a
                          href="https://www.cnet.com/news/pebble-time-gets-gps-longer-battery-life-with-new-smartstrap/">Kickstarter-</a>type
                        <a
                          href="https://www.cnet.com/news/pagare-pebble-time-smart-strap-nfc-payments-apple-pay/">concepts</a>
                        in existence. Migicovsky still says Pebble doesn't plan
                        to make any itself. "Our smart strap strategy is very
                        much about enabling and helping the community get these
                        to market. We're a small company, we're focused on
                        making the watches and the Core. That's the main focus
                        for us. We see that the community is already starting to
                        build these great accessories."</p>
                      <p>Other than a larger, color display and better battery
                        -- the Pebble 2 gets around 7 days of battery life, the
                        larger Pebble Time 2 should get 10, according to
                        Migicovsky -- these work the same. I'd probably go for
                        the smaller, more affordable Pebble 2. It reminds me of
                        a new-age Casio watch. I think it's the best-looking
                        option.</p>
                      <p>Much like last year, early <a
                          href="http://pebble.com/kickstarter"
                          rel="noopener noreferrer nofollow" target="_blank"
                          data-component="externalLink">Kickstarter adopters</a>
                        get discounts on both watches: $99 for Pebble 2, $169
                        for Pebble Time 2. Despite last year's somewhat
                        controversial Kickstarter-first launch of Pebble Time,
                        the same thing's happening again this year. According to
                        Migicovsky, it's to satisfy the Kickstarter community
                        that supported Pebble in the first place.</p>
                      <figure class="image image-large pull-none shortcode"
                        section="shortcodeImage"><span
                          class="imageContainer"><img
                            src="https://www.cnet.com/a/img/2TWLPA0Xeq0PbXVClt7AtnujimY=/1092x0/2016/05/24/3fa37186-15c5-4532-aa46-5ae42d82c05d/pebbletime2.jpg"
                            class="" alt="pebbletime2.jpg" height="819"
                            width="1092" loading="lazy"><noscript><img
                              src="https://www.cnet.com/a/img/2TWLPA0Xeq0PbXVClt7AtnujimY=/1092x0/2016/05/24/3fa37186-15c5-4532-aa46-5ae42d82c05d/pebbletime2.jpg"
                              class="" alt="pebbletime2.jpg" height="819"
                              width="1092"></noscript></span>
                        <figcaption><span class="credit">
                            Scott Stein/CNET
                          </span></figcaption>
                      </figure>
                      <p>Pebble's OS is also getting an update with some useful
                        additions:</p>
                      <ul>
                        <li>Quick-peek notifications now pop below the watch
                          face much like they do on Android Wear or Apple Watch,
                          instead of taking over the whole screen.</li>
                        <li>Actions, which are basically programmable quick
                          functions that can do specific tasks faster. Checking
                          weather, calling an Uber, or doing one-function things
                          can be assigned to the Pebble buttons, or pulled out
                          of a list-like menu.</li>
                      </ul>
                      <p>Migicovsky quotes the refrain that repeats throughout
                        wearable tech: It's still too slow, and "3 to 5 seconds"
                        is the goal for any interaction. "It still takes too
                        many clicks to navigate through the app list and dive
                        all the way into an app. And when in reality, you've got
                        your phone. If you're gonna spend more than 5 or 10
                        seconds on your watch, then you might as well take out
                        your phone."</p>
                      <figure
                        class="image image-large pull-none hasCaption shortcode"
                        section="shortcodeImage"><span
                          class="imageContainer"><img
                            src="https://www.cnet.com/a/img/X4I-rUUQUrM6_TzfFycRJc7kbzY=/1092x0/2016/05/23/b2257bbb-1b75-4390-b5c1-303f2589b427/pebble.jpg"
                            class="" alt="pebble.jpg" height="615" width="1092"
                            loading="lazy"><noscript><img
                              src="https://www.cnet.com/a/img/X4I-rUUQUrM6_TzfFycRJc7kbzY=/1092x0/2016/05/23/b2257bbb-1b75-4390-b5c1-303f2589b427/pebble.jpg"
                              class="" alt="pebble.jpg" height="615"
                              width="1092"></noscript></span>
                        <figcaption><span class="caption">
                            <p>How many people will consider wearing two things
                              at once...or swapping?</p>
                          </span><span class="credit">
                            John Kim/CNET
                          </span></figcaption>
                      </figure>
                      <h3>How much more room is there on the wrist...or anywhere
                        else?</h3>
                      <p>Shipments of wearable tech <a
                          href="https://www.cnet.com/news/fitness-bands-smartwatches-pick-up-pace-with-consumers-says-idc/">keep
                          rising</a>, but Pebble's position in smartwatches
                        seems to be in flux. Recent <a
                          href="http://www.businesswire.com/news/home/<USER>/en/Worldwide-Wearables-Market-Leaps-126.9-Fourth-Quarter"
                          rel="noopener noreferrer nofollow" target="_blank"
                          data-component="externalLink">wearable market
                          estimates</a> that include fitness trackers don't even
                        have Pebble in the top 5.</p>
                      <p>With Apple, Google, Samsung, Fitbit and a ton of other
                        traditional watch companies like Fossil heading into the
                        mix, Pebble might have a rough time gaining ground. But
                        maybe there's a future in devices off-wrist.</p>
                      <p>Or, maybe, Pebble Core hints at a future beyond the
                        current Pebble watch. Music playback. GPS. Android. A
                        more flexible, programmable design.</p>
                      <p>Doesn't sound like a bad idea for Pebble 3.</p>
                      <hr id="horizontalrule">
                      <p><em>This article also appears in Spanish. Read</em>: <a
                          href="https://www.cnet.com/es/noticias/pebble-2-pebble-time-2-pebble-core-nuevos-monitores-actividad/">Pebble
                          2, Pebble Time 2, Pebble Core: Pebble lanza tres
                          nuevos monitores de actividad</a></p>







                      <div id="myfinance-news" data-track="myfi-retargeted"
                        data-sub-id="prod|dfb49c4e-2dd0-4182-890c-37897d913be9|smartwatches|null|desktop"
                        data-component="myFinanceWidget"></div>




                      <footer class="row c-foot">
                        <div class="col-3 c-foot_seg">



                          <div class="comment-container"
                            data-component="sharebar"
                            data-sharebar-options="{&quot;title&quot;:&quot;Pebble 2, Pebble Time 2, Pebble Core: Pebble\u0027s fitness hat trick adds heart rate, GPS, Spotify, and 3G&quot;,&quot;description&quot;:&quot;Smartwatches with heart rate support, plus a stand-alone puck called Core that acts as a 3G-connected smart button: Pebble is trying for a two-device approach in fitness wearables.&quot;,&quot;url&quot;:&quot;https:\/\/www.cnet.com\/tech\/mobile\/pebble-2-pebble-time-2-pebble-core-announced-coming-this-year-and-2017\/&quot;,&quot;popupWidth&quot;:780,&quot;popupHeight&quot;:510,&quot;data&quot;:{&quot;media&quot;:&quot;&quot;},&quot;scrollToComments&quot;:true}">
                            <div
                              class="share-button sharebarRedesignButton comment-count-redesign"
                              data-conversation-id="dfb49c4e-2dd0-4182-890c-37897d913be9">
                              <a href="#comments" data-component="disqusCount"
                                data-disqus-count-options="{&quot;disqusIdentifier&quot;:&quot;dfb49c4e-2dd0-4182-890c-37897d913be9&quot;,&quot;disqusZeroCommentsText&quot;:&quot;Post a comment&quot;,&quot;disqusOneCommentText&quot;:&quot;Comment&quot;}"
                                class="disqus-comment-count"
                                data-disqus-identifier="dfb49c4e-2dd0-4182-890c-37897d913be9">

                                <div
                                  class="button button_type_primary button_size_medium ">
                                  <span class="button__icon">
                                    <span class="button__iconText"
                                      data-item="iconText">21</span><svg
                                      class=" comments">
                                      <use aria-hidden="false"
                                        xlink:href="#comments"></use>
                                    </svg>

                                  </span><span
                                    data-item="buttonText">Comments</span>
                                </div>

                              </a>
                            </div>
                          </div>
                        </div>

                        <div class="col-4 c-foot_seg">




                          <div class="row tagList desktop" section="tag">


                            <a class="tag"
                              href="/topics/smartwatches/">Smartwatches</a>


                            <a class="tag" href="/tags/fitness/">Fitness</a>

                            <a class="tag" href="/tags/spotify/">Spotify</a>



                            <a class="tag broadInterest  interestOnly">


                              <span class="interestContainer">
                                <span
                                  class="followInterestButton nonFollowedInterest hidden add"
                                  data-component="interest"
                                  data-interest-options="{&quot;id&quot;:&quot;be4e4302-f313-11e2-8262-0291187b029a_CNET_TAG&quot;,&quot;name&quot;:&quot;Wearable Tech&quot;,&quot;type&quot;:&quot;specific&quot;}">
                                  <span class="bell">
                                    <span class="activated">
                                      <svg class=" notification-activated">
                                        <use aria-hidden="false"
                                          xlink:href="#notification-activated">
                                        </use>
                                      </svg>

                                    </span>
                                    <span class="default">
                                      <svg class=" notification">
                                        <use aria-hidden="false"
                                          xlink:href="#notification"></use>
                                      </svg>

                                    </span>
                                  </span>
                                  <div class="tooltip interestBanner">
                                    <div class="on">Notification on</div>
                                    <div class="off">Notification off</div>
                                  </div>
                                </span>
                                <span class="text">Wearable Tech</span>
                              </span>
                            </a>
                          </div>
                        </div>
                      </footer>
                    </div>
                    <div class="col-4" section="rColumn">

                      <div class="stickyColumn ready"
                        data-component="stickyColumn"
                        data-sticky-column-options="{&quot;offset&quot;:0,&quot;selectorHeightConstraint&quot;:&quot;article&quot;,&quot;enableWaypoints&quot;:false}"
                        style="height: 8485px;">

                        <div class="stickyColumnElement"
                          data-meta="{&quot;height&quot;:620,&quot;type&quot;:&quot;required&quot;}">












                          <div data-ad="mpu-plus-top"
                            data-ad-container="61718e4e621d3"
                            class="ad-mpu-plus-top ad-slot   ">
                          </div>

                        </div>

                        <div class="stickyColumnElement"
                          data-meta="{&quot;height&quot;:300,&quot;type&quot;:&quot;required&quot;}"
                          style="top: 2828.33px;">












                          <div data-ad="native-mpu-middle"
                            data-ad-container="61718e4e621d3"
                            class="ad-native-mpu-middle ad-slot   ">
                          </div>

                        </div>

                        <div class="stickyColumnElement"
                          data-meta="{&quot;height&quot;:300,&quot;type&quot;:&quot;required&quot;}"
                          style="top: 5656.67px;">












                          <div data-ad="mpu-bottom"
                            data-ad-container="61718e4e621d3"
                            class="ad-mpu-bottom ad-slot   ">
                          </div>

                        </div>
                      </div>







                      <div data-video-player="container" class="">
                        <div class="inviewElement dock entered"
                          data-component="inviewElement"
                          data-inview-element-options="{&quot;events&quot;:[&quot;entered&quot;],&quot;repeat&quot;:false}">
                          <div class="upnext hidden" data-video-info="upnext">
                          </div>
                          <svg class="svg-symbol playerControls">
                            <symbol id="play" viewBox="0 0 26.6 32">
                              <path
                                d="M0,2.6c0-2.4,1.6-3.3,3.7-2L25,13.7c2,1.3,2,3.2,0,4.5L3.7,31.4c-2,1.3-3.7,0.3-3.7-2C0,29.4,0,2.6,0,2.6z">
                              </path>
                            </symbol>
                            <symbol id="pause" viewBox="0 0 26.6 32">
                              <g>
                                <polygon
                                  points="16,32 26.5,32 26.5,8.8 26.5,0 16,0">
                                </polygon>
                                <rect x="0.1" y="0" width="10.6" height="32">
                                </rect>
                              </g>
                            </symbol>
                            <symbol id="playlist" viewBox="0 0 32 22.9">
                              <g>
                                <circle class="st11" cx="2.3" cy="20.6" r="2.3">
                                </circle>
                                <circle class="st11" cx="2.3" cy="11.4" r="2.3">
                                </circle>
                                <circle class="st11" cx="2.3" cy="2.3" r="2.3">
                                </circle>
                                <path class="st11"
                                  d="M32,1.1C32,0.5,31.5,0,30.9,0H10.3C9.7,0,9.1,0.5,9.1,1.1v2.3c0,0.6,0.5,1.1,1.1,1.1h20.6c0.6,0,1.1-0.5,1.1-1.1V1.1z">
                                </path>
                                <path class="st11"
                                  d="M32,10.3c0-0.6-0.5-1.1-1.1-1.1H10.3c-0.6,0-1.1,0.5-1.1,1.1v2.3c0,0.6,0.5,1.1,1.1,1.1h20.6c0.6,0,1.1-0.5,1.1-1.1V10.3z">
                                </path>
                                <path class="st11"
                                  d="M32,19.4c0-0.6-0.5-1.1-1.1-1.1H10.3c-0.6,0-1.1,0.5-1.1,1.1v2.3c0,0.6,0.5,1.1,1.1,1.1h20.6c0.6,0,1.1-0.5,1.1-1.1V19.4z">
                                </path>
                              </g>
                            </symbol>
                            <symbol id="speaker-on" viewBox="0 0 32 28.1">
                              <g>
                                <g>
                                  <path
                                    d="M12.6,5L6.3,8.7H0.6C0.3,8.7,0,9,0,9.3V19c0,0.4,0.3,0.7,0.6,0.7h5.9l6.1,3.4c1.3,0.8,1.5,0.2,1.5-1.5V6.5C14.2,4.8,13.9,4.2,12.6,5z">
                                  </path>
                                </g>
                                <path
                                  d="M18,23.1v-2.8c2.6,0,4.7-2.8,4.7-6.2S20.6,7.8,18,7.8V5c4.2,0,7.5,4,7.5,9S22.1,23.1,18,23.1z">
                                </path>
                                <path
                                  d="M21.3,28.1v-2.8c4.4,0,7.9-5,7.9-11.2S25.6,2.8,21.3,2.8V0C27.2,0,32,6.3,32,14C32,21.8,27.2,28.1,21.3,28.1z">
                                </path>
                              </g>
                            </symbol>
                            <symbol id="speaker-off" viewBox="0 0 32 18.8">
                              <g>
                                <g>
                                  <path
                                    d="M12.6,0.4L6.3,4H0.6C0.3,4,0,4.3,0,4.7v9.7c0,0.4,0.3,0.7,0.6,0.7h5.9l6.1,3.4c1.3,0.8,1.5,0.2,1.5-1.5V1.8C14.2,0.2,13.9-0.5,12.6,0.4z">
                                  </path>
                                </g>
                                <polygon
                                  points="32,4.5 30.2,2.7 25.3,7.6 20.4,2.7 18.6,4.5 23.5,9.4 18.6,14.3 20.4,16.1 25.3,11.2 30.2,16.1 32,14.3 27.1,9.4 ">
                                </polygon>
                              </g>
                            </symbol>
                            <symbol id="captions" viewBox="0 0 32 16.2">
                              <g>
                                <path
                                  d="M8.6,16.2c-1.2,0-2.4-0.2-3.4-0.6c-1.1-0.4-2-0.9-2.7-1.6c-0.8-0.7-1.4-1.6-1.8-2.6C0.2,10.4,0,9.3,0,8.1c0-1.2,0.2-2.3,0.7-3.3s1-1.8,1.8-2.6C3.2,1.5,4.1,1,5.2,0.6S7.4,0,8.6,0c1,0,1.9,0.1,2.7,0.4c0.8,0.3,1.4,0.6,2,1.1c0.6,0.4,1.1,0.9,1.4,1.5c0.4,0.5,0.7,1.1,0.9,1.6l-4.1,1.9c-0.1-0.3-0.2-0.6-0.4-0.9C11,5.3,10.8,5,10.5,4.8c-0.3-0.2-0.6-0.4-0.9-0.5c-0.3-0.1-0.7-0.2-1-0.2C8,4.2,7.5,4.3,7,4.5C6.6,4.7,6.2,5,5.8,5.3C5.5,5.7,5.2,6.1,5.1,6.5C4.9,7,4.8,7.5,4.8,8.1c0,0.5,0.1,1.1,0.3,1.5c0.2,0.5,0.4,0.9,0.8,1.2c0.3,0.4,0.7,0.6,1.2,0.8C7.5,11.9,8,12,8.6,12c0.3,0,0.7-0.1,1-0.2c0.3-0.1,0.6-0.3,0.9-0.5c0.3-0.2,0.5-0.4,0.7-0.7c0.2-0.3,0.3-0.6,0.4-0.9l4.1,1.9c-0.2,0.5-0.5,1-0.9,1.6c-0.4,0.5-0.9,1-1.4,1.5c-0.6,0.4-1.2,0.8-2,1.1C10.5,16,9.6,16.2,8.6,16.2z">
                                </path>
                                <path
                                  d="M24.9,16.2c-1.2,0-2.4-0.2-3.4-0.6c-1.1-0.4-2-0.9-2.7-1.6c-0.8-0.7-1.4-1.6-1.8-2.6c-0.4-1-0.7-2.1-0.7-3.3c0-1.2,0.2-2.3,0.7-3.3c0.4-1,1-1.8,1.8-2.6c0.8-0.7,1.7-1.3,2.7-1.7C22.6,0.2,23.7,0,24.9,0c1,0,1.9,0.1,2.7,0.4c0.8,0.3,1.4,0.6,2,1.1c0.6,0.4,1.1,0.9,1.4,1.5C31.5,3.5,31.8,4,32,4.5l-4.1,1.9c-0.1-0.3-0.2-0.6-0.4-0.9c-0.2-0.3-0.4-0.5-0.7-0.7c-0.3-0.2-0.6-0.4-0.9-0.5c-0.3-0.1-0.7-0.2-1-0.2c-0.6,0-1.1,0.1-1.5,0.3c-0.5,0.2-0.9,0.5-1.2,0.8c-0.3,0.4-0.6,0.8-0.8,1.2c-0.2,0.5-0.3,1-0.3,1.5c0,0.5,0.1,1.1,0.3,1.5c0.2,0.5,0.4,0.9,0.8,1.2c0.3,0.4,0.7,0.6,1.2,0.8c0.5,0.2,1,0.3,1.5,0.3c0.3,0,0.7-0.1,1-0.2c0.3-0.1,0.6-0.3,0.9-0.5c0.3-0.2,0.5-0.4,0.7-0.7c0.2-0.3,0.3-0.6,0.4-0.9l4.1,1.9c-0.2,0.5-0.5,1-0.9,1.6c-0.4,0.5-0.9,1-1.4,1.5c-0.6,0.4-1.2,0.8-2,1.1C26.8,16,25.9,16.2,24.9,16.2z">
                                </path>
                              </g>
                            </symbol>
                            <symbol id="share" viewBox="0 0 32 20">
                              <path
                                d="M20,6c0,0-7.9-0.1-13.2,3.3C1.6,12.7,0,20,0,20s5.1-5.4,9.8-7.2C14.9,10.9,20,12,20,12v6l12-8L20,0V6z">
                              </path>
                            </symbol>
                            <symbol id="fullscreen" viewBox="0 0 32 32">
                              <g>
                                <g>
                                  <polygon class="st11"
                                    points="11.4,0 0,0 0,11.4 3.4,8 9.1,13.7 13.7,9.1 8,3.4 			">
                                  </polygon>
                                </g>
                                <g>
                                  <polygon class="st11"
                                    points="20.6,32 32,32 32,20.6 28.6,24 22.9,18.3 18.3,22.9 24,28.6 			">
                                  </polygon>
                                </g>
                                <g>
                                  <polygon class="st11"
                                    points="32,11.4 32,0 20.6,0 24,3.4 18.3,9.1 22.9,13.7 28.6,8 			">
                                  </polygon>
                                </g>
                                <g>
                                  <polygon class="st11"
                                    points="0,20.6 0,32 11.4,32 8,28.6 13.7,22.9 9.1,18.3 3.4,24 			">
                                  </polygon>
                                </g>
                              </g>
                            </symbol>
                            <symbol id="video" viewBox="0 0 32 22">
                              <g>
                                <path
                                  d="M29,2.2c-0.7,0.2-4.6,3-7.9,5.3V2.1C21.1,1,20.1,0,19,0H2.1C1,0,0,1,0,2.1v17.7C0,21,1,22,2.1,22H19c1.2,0,2.1-1,2.1-2.1v-5.9c3.3,2.4,7.4,5.4,8.2,5.6c1.4,0.4,2.7-0.1,2.7-0.1V2.2C32,2.2,30.1,1.8,29,2.2z">
                                </path>
                              </g>
                            </symbol>
                          </svg>

                          <div class="videoPlayer " data-component="videoPlayer"
                            data-video-player-options="{&quot;config&quot;:{&quot;policies&quot;:{&quot;default&quot;:11417438},&quot;tracking&quot;:{&quot;can_partner_id&quot;:&quot;canPartnerID&quot;,&quot;comscore_id&quot;:&quot;3000085&quot;,&quot;comscore_home&quot;:&quot;3000085&quot;,&quot;comscore_how_to&quot;:&quot;3000078&quot;,&quot;comscore_news&quot;:&quot;3000078&quot;,&quot;comscore_reviews&quot;:&quot;3000087&quot;,&quot;comscore_videos&quot;:&quot;3000088&quot;,&quot;comscore_sense_id&quot;:&quot;cnetvideo&quot;,&quot;comscore_sense_home&quot;:&quot;cnethome&quot;,&quot;comscore_sense_how_to&quot;:&quot;cnethowto&quot;,&quot;comscore_sense_news&quot;:&quot;cnetnews&quot;,&quot;comscore_sense_reviews&quot;:&quot;cnetreviews&quot;,&quot;comscore_sense_videos&quot;:&quot;cnetvideo&quot;,&quot;nielsen_cid&quot;:&quot;us-200330&quot;,&quot;nielsen_vcid&quot;:&quot;c07&quot;,&quot;nielsen_vcid_reviews&quot;:&quot;c05&quot;,&quot;nielsen_vcid_home&quot;:&quot;c07&quot;,&quot;nielsen_vcid_news&quot;:&quot;c08&quot;,&quot;nielsen_vcid_how_to&quot;:&quot;c09&quot;,&quot;nielsen_vcid_videos&quot;:&quot;c20&quot;},&quot;uvpConfig&quot;:{&quot;mpx_account&quot;:&quot;kYEXFC&quot;}},&quot;playlist&quot;:[],&quot;customCSS&quot;:&quot;https:\/\/www.cnet.com\/a\/fly\/css\/video\/common\/uvpjsDefaults-d41d8cd98f-rev.css&quot;,&quot;eventChannels&quot;:[&quot;videoPlayer&quot;],&quot;eventSubscriptions&quot;:{&quot;howlerAudioPlayer&quot;:{&quot;playAudio&quot;:&quot;pause&quot;,&quot;audioLoaded&quot;:&quot;setAutoplayToPause&quot;}},&quot;apeList&quot;:false,&quot;autoplay&quot;:&quot;load&quot;,&quot;contentType&quot;:&quot;vod-curated&quot;,&quot;getShortcodeVideo&quot;:1,&quot;getRelatedPlaylist&quot;:true,&quot;mainPlayer&quot;:true,&quot;suppressAds&quot;:false,&quot;loop&quot;:true}">

                          </div>

                        </div>
                      </div>

                    </div>
                  </article>


                  <div class="comments collapsible hidden"
                    data-component="toggle"
                    data-toggle-options="{&quot;closeOnUrlChange&quot;:true,&quot;openOnInit&quot;:false}">
                    <div class="close" data-item="trigger"
                      data-close="comments"><svg viewBox="0 0 28 28">
                        <polygon
                          points="28,1.9 26.1,0 14,12.1 1.9,0 0,1.9 12.1,14 0,26.1 1.9,28 14,15.9 26.1,28 28,26.1 15.9,14 	">
                        </polygon>
                      </svg> Close
                    </div>





                    <!-- General comments -->
                    <div
                      id="disqusContainer-dfb49c4e-2dd0-4182-890c-37897d913be9"
                      class="commentsContainer disqusContainer"
                      data-component="selfishScroll"
                      data-disqus-container="null">
                      <a name="comments"></a>
                      <div class="disqusThreadTitle">
                        <span>Discuss: Pebble 2, Pebble Time 2, Pebble Core:
                          Pebble's fitness hat trick adds heart rate, GPS,
                          Spotify, and 3G</span>
                      </div>

                      <button class="disqusLogin btn-link">Sign in to
                        comment</button>
                      <p class="policy">Be respectful, keep it civil and stay on
                        topic. We delete comments that violate <a
                          href="https://www.cnet.com/community-guidelines/"
                          target="_blank">our policy</a>, which we encourage you
                        to read. Discussion threads can be closed at any time at
                        our discretion.</p>


                      <div data-social="disqus"
                        data-id="dfb49c4e-2dd0-4182-890c-37897d913be9"
                        data-title="Pebble 2, Pebble Time 2, Pebble Core: Pebble's fitness hat trick adds heart rate, GPS, Spotify, and 3G"
                        data-url="https://www.cnet.com/tech/mobile/pebble-2-pebble-time-2-pebble-core-announced-coming-this-year-and-2017/">
                      </div>
                    </div>
                  </div>

                  <div class="fixate-bottom-anchor"></div>
                </div>
              </div>
            </div>
          </div>

        </div>

        <div class="tailWrap">












          <div data-ad="incontent-ad-plus-billboard-bottom"
            data-ad-container="61718e4e621d3"
            class="ad-incontent-ad-plus-billboard-bottom ad-slot   skybox-inContent">
          </div>

        </div>
      </div>







      <div
        id="page-giant-mystery-creature-filmed-by-scientists-exploring-red-sea-shipwreck"
        class="pageWrapper pageContainer"
        data-container-asset-id="c5cc64ea-b2f8-4fc4-9004-9c6fb9c37b08"
        data-component="page"
        data-page-options="{&quot;ajaxOffset&quot;:2000,&quot;ajaxUrl&quot;:&quot;\/news\/giant-mystery-creature-filmed-by-scientists-exploring-red-sea-shipwreck\/xhr\/?firstArticleId=dfb49c4e-2dd0-4182-890c-37897d913be9&amp;viewedItems=dfb49c4e-2dd0-4182-890c-37897d913be9&quot;,&quot;allowAds&quot;:true,&quot;amlTrackingCallback&quot;:&quot;https:\/\/i-cmg-amlg-prod.appspot.com\/take?id=3f5acfb2-363b-49de-adf9-0fae22789d72&amp;ordinal=0&amp;content_id=c5cc64ea-b2f8-4fc4-9004-9c6fb9c37b08&amp;p=2&quot;,&quot;browserTitle&quot;:&quot;Giant 'mystery creature' filmed by scientists exploring Red Sea shipwreck&quot;,&quot;browserUrl&quot;:&quot;\/news\/giant-mystery-creature-filmed-by-scientists-exploring-red-sea-shipwreck\/&quot;,&quot;cmsUrl&quot;:&quot;https:\/\/cms.cnet.com\/content\/article\/c5cc64ea-b2f8-4fc4-9004-9c6fb9c37b08&quot;,&quot;dwPostPend&quot;:&quot;&quot;,&quot;limit&quot;:5,&quot;resetAdPosition&quot;:true,&quot;trackingEventData&quot;:[],&quot;trackPageviewUp&quot;:true,&quot;trackScroll25&quot;:true,&quot;trackScroll50&quot;:true,&quot;trackScroll75&quot;:true,&quot;trackScroll100&quot;:true}">
      </div>
    </div>


    <svg class="svg-symbol">
      <defs>
        <linearGradient id="SVGID_1_" gradientUnits="userSpaceOnUse" x1="21.77"
          y1="606.68" x2="4.9876" y2="623.4624">
          <stop offset="0" stop-color="#00A0FF"></stop>
          <stop offset="6.574450e-03" stop-color="#00A1FF"></stop>
          <stop offset="0.2601" stop-color="#00BEFF"></stop>
          <stop offset="0.5122" stop-color="#00D2FF"></stop>
          <stop offset="0.7604" stop-color="#00DFFF"></stop>
          <stop offset="1" stop-color="#00E3FF"></stop>
        </linearGradient>
        <linearGradient id="SVGID_2_" gradientUnits="userSpaceOnUse"
          x1="33.8344" y1="618.05" x2="9.6375" y2="618.05">
          <stop offset="0" stop-color="#FFE000"></stop>
          <stop offset="0.4087" stop-color="#FFBD00"></stop>
          <stop offset="0.7754" stop-color="#FFA500"></stop>
          <stop offset="1" stop-color="#FF9C00"></stop>
        </linearGradient>
        <linearGradient id="SVGID_3_" gradientUnits="userSpaceOnUse"
          x1="24.8155" y1="620.2845" x2="2.0572" y2="643.0428">
          <stop offset="0" stop-color="#FF3A44"></stop>
          <stop offset="1" stop-color="#C31162"></stop>
        </linearGradient>
        <linearGradient id="SVGID_4_" gradientUnits="userSpaceOnUse" x1="7.2617"
          y1="598.2118" x2="17.4243" y2="608.3743">
          <stop offset="0" stop-color="#32A071"></stop>
          <stop offset="6.850000e-02" stop-color="#2DA771"></stop>
          <stop offset="0.4762" stop-color="#15CF74"></stop>
          <stop offset="0.8009" stop-color="#06E775"></stop>
          <stop offset="1" stop-color="#00F076"></stop>
        </linearGradient>
        <linearGradient id="SVGID_1_es" x1="21.77" x2="4.988" y1="606.68"
          y2="623.462" gradientUnits="userSpaceOnUse"
          gradientTransform="translate(0 -598)">
          <stop offset="0" stop-color="#00A0FF"></stop>
          <stop offset=".007" stop-color="#00A1FF"></stop>
          <stop offset=".26" stop-color="#00BEFF"></stop>
          <stop offset=".512" stop-color="#00D2FF"></stop>
          <stop offset=".76" stop-color="#00DFFF"></stop>
          <stop offset="1" stop-color="#00E3FF"></stop>
        </linearGradient>
        <linearGradient id="SVGID_2_es" x1="33.834" x2="9.637" y1="618.05"
          y2="618.05" gradientUnits="userSpaceOnUse"
          gradientTransform="translate(0 -598)">
          <stop offset="0" stop-color="#FFE000"></stop>
          <stop offset=".409" stop-color="#FFBD00"></stop>
          <stop offset=".775" stop-color="#FFA500"></stop>
          <stop offset="1" stop-color="#FF9C00"></stop>
        </linearGradient>
        <linearGradient id="SVGID_3_es" x1="24.816" x2="2.057" y1="620.284"
          y2="643.043" gradientUnits="userSpaceOnUse"
          gradientTransform="translate(0 -598)">
          <stop offset="0" stop-color="#FF3A44"></stop>
          <stop offset="1" stop-color="#C31162"></stop>
        </linearGradient>
        <linearGradient id="SVGID_4_es" x1="7.262" x2="17.424" y1="598.212"
          y2="608.374" gradientUnits="userSpaceOnUse"
          gradientTransform="translate(0 -598)">
          <stop offset="0" stop-color="#32A071"></stop>
          <stop offset=".069" stop-color="#2DA771"></stop>
          <stop offset=".476" stop-color="#15CF74"></stop>
          <stop offset=".801" stop-color="#06E775"></stop>
          <stop offset="1" stop-color="#00F076"></stop>
        </linearGradient>
      </defs>
      <symbol id="iTunes" viewBox="0 0 135 40"
        enable-background="new 0 0 135 40">
        <g>
          <path fill="#A6A6A6"
            d="M130.197,40H4.729C2.122,40,0,37.872,0,35.267V4.726C0,2.12,2.122,0,4.729,0h125.468
          C132.803,0,135,2.12,135,4.726v30.541C135,37.872,132.803,40,130.197,40L130.197,40z">
          </path>
          <path
            d="M134.032,35.268c0,2.116-1.714,3.83-3.834,3.83H4.729c-2.119,0-3.839-1.714-3.839-3.83V4.725
          c0-2.115,1.72-3.835,3.839-3.835h125.468c2.121,0,3.834,1.72,3.834,3.835L134.032,35.268L134.032,35.268z">
          </path>
          <g>
            <path fill="#FFFFFF" d="M30.128,19.784c-0.029-3.223,2.639-4.791,2.761-4.864c-1.511-2.203-3.853-2.504-4.676-2.528
                  c-1.967-0.207-3.875,1.177-4.877,1.177c-1.022,0-2.565-1.157-4.228-1.123c-2.14,0.033-4.142,1.272-5.24,3.196
                  c-2.266,3.923-0.576,9.688,1.595,12.859c1.086,1.553,2.355,3.287,4.016,3.226c1.625-0.067,2.232-1.036,4.193-1.036
                  c1.943,0,2.513,1.036,4.207,0.997c1.744-0.028,2.842-1.56,3.89-3.127c1.255-1.78,1.759-3.533,1.779-3.623
                  C33.507,24.924,30.161,23.647,30.128,19.784z"></path>
            <path fill="#FFFFFF"
              d="M26.928,10.306c0.874-1.093,1.472-2.58,1.306-4.089c-1.265,0.056-2.847,0.875-3.758,1.944
                  c-0.806,0.942-1.526,2.486-1.34,3.938C24.557,12.205,26.016,11.382,26.928,10.306z">
            </path>
          </g>
          <g>
            <path fill="#FFFFFF" d="M53.645,31.504h-2.271l-1.244-3.909h-4.324l-1.185,3.909h-2.211l4.284-13.308h2.646L53.645,31.504z
               M49.755,25.955L48.63,22.48c-0.119-0.355-0.342-1.191-0.671-2.507h-0.04c-0.131,0.566-0.342,1.402-0.632,2.507l-1.105,3.475
              H49.755z"></path>
            <path fill="#FFFFFF"
              d="M64.662,26.588c0,1.632-0.441,2.922-1.323,3.869c-0.79,0.843-1.771,1.264-2.942,1.264
              c-1.264,0-2.172-0.454-2.725-1.362h-0.04v5.055h-2.132V25.067c0-1.026-0.027-2.079-0.079-3.159h1.875l0.119,1.521h0.04
              c0.711-1.146,1.79-1.718,3.238-1.718c1.132,0,2.077,0.447,2.833,1.342C64.284,23.949,64.662,25.127,64.662,26.588z M62.49,26.666
              c0-0.934-0.21-1.704-0.632-2.31c-0.461-0.632-1.08-0.948-1.856-0.948c-0.526,0-1.004,0.176-1.431,0.523
              c-0.428,0.35-0.708,0.807-0.839,1.373c-0.066,0.264-0.099,0.48-0.099,0.65v1.6c0,0.698,0.214,1.287,0.642,1.768
              s0.984,0.721,1.668,0.721c0.803,0,1.428-0.31,1.875-0.928C62.266,28.496,62.49,27.68,62.49,26.666z">
            </path>
            <path fill="#FFFFFF"
              d="M75.699,26.588c0,1.632-0.441,2.922-1.324,3.869c-0.789,0.843-1.77,1.264-2.941,1.264
              c-1.264,0-2.172-0.454-2.724-1.362H68.67v5.055h-2.132V25.067c0-1.026-0.027-2.079-0.079-3.159h1.875l0.119,1.521h0.04
              c0.71-1.146,1.789-1.718,3.238-1.718c1.131,0,2.076,0.447,2.834,1.342C75.32,23.949,75.699,25.127,75.699,26.588z M73.527,26.666
              c0-0.934-0.211-1.704-0.633-2.31c-0.461-0.632-1.078-0.948-1.855-0.948c-0.527,0-1.004,0.176-1.432,0.523
              c-0.428,0.35-0.707,0.807-0.838,1.373c-0.065,0.264-0.099,0.48-0.099,0.65v1.6c0,0.698,0.214,1.287,0.64,1.768
              c0.428,0.48,0.984,0.721,1.67,0.721c0.803,0,1.428-0.31,1.875-0.928C73.303,28.496,73.527,27.68,73.527,26.666z">
            </path>
            <path fill="#FFFFFF"
              d="M88.039,27.772c0,1.132-0.393,2.053-1.182,2.764c-0.867,0.777-2.074,1.165-3.625,1.165
              c-1.432,0-2.58-0.276-3.449-0.829l0.494-1.777c0.936,0.566,1.963,0.85,3.082,0.85c0.803,0,1.428-0.182,1.877-0.544
              c0.447-0.362,0.67-0.848,0.67-1.454c0-0.54-0.184-0.995-0.553-1.364c-0.367-0.369-0.98-0.712-1.836-1.029
              c-2.33-0.869-3.494-2.142-3.494-3.816c0-1.094,0.408-1.991,1.225-2.689c0.814-0.699,1.9-1.048,3.258-1.048
              c1.211,0,2.217,0.211,3.02,0.632l-0.533,1.738c-0.75-0.408-1.598-0.612-2.547-0.612c-0.75,0-1.336,0.185-1.756,0.553
              c-0.355,0.329-0.533,0.73-0.533,1.205c0,0.526,0.203,0.961,0.611,1.303c0.355,0.316,1,0.658,1.936,1.027
              c1.145,0.461,1.986,1,2.527,1.618C87.77,26.081,88.039,26.852,88.039,27.772z"></path>
            <path fill="#FFFFFF" d="M95.088,23.508h-2.35v4.659c0,1.185,0.414,1.777,1.244,1.777c0.381,0,0.697-0.033,0.947-0.099l0.059,1.619
              c-0.42,0.157-0.973,0.236-1.658,0.236c-0.842,0-1.5-0.257-1.975-0.77c-0.473-0.514-0.711-1.376-0.711-2.587v-4.837h-1.4v-1.6h1.4
              v-1.757l2.094-0.632v2.389h2.35V23.508z"></path>
            <path fill="#FFFFFF"
              d="M105.691,26.627c0,1.475-0.422,2.686-1.264,3.633c-0.883,0.975-2.055,1.461-3.516,1.461
              c-1.408,0-2.529-0.467-3.365-1.401s-1.254-2.113-1.254-3.534c0-1.487,0.43-2.705,1.293-3.652c0.861-0.948,2.023-1.422,3.484-1.422
              c1.408,0,2.541,0.467,3.396,1.402C105.283,24.021,105.691,25.192,105.691,26.627z M103.479,26.696
              c0-0.885-0.189-1.644-0.572-2.277c-0.447-0.766-1.086-1.148-1.914-1.148c-0.857,0-1.508,0.383-1.955,1.148
              c-0.383,0.634-0.572,1.405-0.572,2.317c0,0.885,0.189,1.644,0.572,2.276c0.461,0.766,1.105,1.148,1.936,1.148
              c0.814,0,1.453-0.39,1.914-1.168C103.281,28.347,103.479,27.58,103.479,26.696z">
            </path>
            <path fill="#FFFFFF"
              d="M112.621,23.783c-0.211-0.039-0.436-0.059-0.672-0.059c-0.75,0-1.33,0.283-1.738,0.85
              c-0.355,0.5-0.533,1.132-0.533,1.895v5.035h-2.131l0.02-6.574c0-1.106-0.027-2.113-0.08-3.021h1.857l0.078,1.836h0.059
              c0.225-0.631,0.58-1.139,1.066-1.52c0.475-0.343,0.988-0.514,1.541-0.514c0.197,0,0.375,0.014,0.533,0.039V23.783z">
            </path>
            <path fill="#FFFFFF" d="M122.156,26.252c0,0.382-0.025,0.704-0.078,0.967h-6.396c0.025,0.948,0.334,1.673,0.928,2.173
              c0.539,0.447,1.236,0.671,2.092,0.671c0.947,0,1.811-0.151,2.588-0.454l0.334,1.48c-0.908,0.396-1.98,0.593-3.217,0.593
              c-1.488,0-2.656-0.438-3.506-1.313c-0.848-0.875-1.273-2.05-1.273-3.524c0-1.447,0.395-2.652,1.186-3.613
              c0.828-1.026,1.947-1.539,3.355-1.539c1.383,0,2.43,0.513,3.141,1.539C121.873,24.047,122.156,25.055,122.156,26.252z
               M120.123,25.699c0.014-0.632-0.125-1.178-0.414-1.639c-0.369-0.593-0.936-0.889-1.699-0.889c-0.697,0-1.264,0.289-1.697,0.869
              c-0.355,0.461-0.566,1.014-0.631,1.658H120.123z"></path>
          </g>
          <g>
            <path fill="#FFFFFF" d="M49.05,10.009c0,1.177-0.353,2.063-1.058,2.658c-0.653,0.549-1.581,0.824-2.783,0.824
                  c-0.596,0-1.106-0.026-1.533-0.078V6.982c0.557-0.09,1.157-0.136,1.805-0.136c1.145,0,2.008,0.249,2.59,0.747
                  C48.723,8.156,49.05,8.961,49.05,10.009z M47.945,10.038c0-0.763-0.202-1.348-0.606-1.756c-0.404-0.407-0.994-0.611-1.771-0.611
                  c-0.33,0-0.611,0.022-0.844,0.068v4.889c0.129,0.02,0.365,0.029,0.708,0.029c0.802,0,1.421-0.223,1.857-0.669
                  S47.945,10.892,47.945,10.038z"></path>
            <path fill="#FFFFFF" d="M54.909,11.037c0,0.725-0.207,1.319-0.621,1.785c-0.434,0.479-1.009,0.718-1.727,0.718
                  c-0.692,0-1.243-0.229-1.654-0.689c-0.41-0.459-0.615-1.038-0.615-1.736c0-0.73,0.211-1.329,0.635-1.794s0.994-0.698,1.712-0.698
                  c0.692,0,1.248,0.229,1.669,0.688C54.708,9.757,54.909,10.333,54.909,11.037z M53.822,11.071c0-0.435-0.094-0.808-0.281-1.119
                  c-0.22-0.376-0.533-0.564-0.94-0.564c-0.421,0-0.741,0.188-0.961,0.564c-0.188,0.311-0.281,0.69-0.281,1.138
                  c0,0.435,0.094,0.808,0.281,1.119c0.227,0.376,0.543,0.564,0.951,0.564c0.4,0,0.714-0.191,0.94-0.574
                  C53.725,11.882,53.822,11.506,53.822,11.071z"></path>
            <path fill="#FFFFFF" d="M62.765,8.719l-1.475,4.714h-0.96l-0.611-2.047c-0.155-0.511-0.281-1.019-0.379-1.523h-0.019
                  c-0.091,0.518-0.217,1.025-0.379,1.523l-0.649,2.047h-0.971l-1.387-4.714h1.077l0.533,2.241c0.129,0.53,0.235,1.035,0.32,1.513
                  h0.019c0.078-0.394,0.207-0.896,0.389-1.503l0.669-2.25h0.854l0.641,2.202c0.155,0.537,0.281,1.054,0.378,1.552h0.029
                  c0.071-0.485,0.178-1.002,0.32-1.552l0.572-2.202H62.765z">
            </path>
            <path fill="#FFFFFF" d="M68.198,13.433H67.15v-2.7c0-0.832-0.316-1.248-0.95-1.248c-0.311,0-0.562,0.114-0.757,0.343
                  c-0.193,0.229-0.291,0.499-0.291,0.808v2.796h-1.048v-3.366c0-0.414-0.013-0.863-0.038-1.349h0.921l0.049,0.737h0.029
                  c0.122-0.229,0.304-0.418,0.543-0.569c0.284-0.176,0.602-0.265,0.95-0.265c0.44,0,0.806,0.142,1.097,0.427
                  c0.362,0.349,0.543,0.87,0.543,1.562V13.433z"></path>
            <path fill="#FFFFFF" d="M71.088,13.433h-1.047V6.556h1.047V13.433z">
            </path>
            <path fill="#FFFFFF" d="M77.258,11.037c0,0.725-0.207,1.319-0.621,1.785c-0.434,0.479-1.01,0.718-1.727,0.718
                  c-0.693,0-1.244-0.229-1.654-0.689c-0.41-0.459-0.615-1.038-0.615-1.736c0-0.73,0.211-1.329,0.635-1.794s0.994-0.698,1.711-0.698
                  c0.693,0,1.248,0.229,1.67,0.688C77.057,9.757,77.258,10.333,77.258,11.037z M76.17,11.071c0-0.435-0.094-0.808-0.281-1.119
                  c-0.219-0.376-0.533-0.564-0.939-0.564c-0.422,0-0.742,0.188-0.961,0.564c-0.188,0.311-0.281,0.69-0.281,1.138
                  c0,0.435,0.094,0.808,0.281,1.119c0.227,0.376,0.543,0.564,0.951,0.564c0.4,0,0.713-0.191,0.939-0.574
                  C76.074,11.882,76.17,11.506,76.17,11.071z"></path>
            <path fill="#FFFFFF"
              d="M82.33,13.433h-0.941l-0.078-0.543h-0.029c-0.322,0.433-0.781,0.65-1.377,0.65
                  c-0.445,0-0.805-0.143-1.076-0.427c-0.246-0.258-0.369-0.579-0.369-0.96c0-0.576,0.24-1.015,0.723-1.319
                  c0.482-0.304,1.16-0.453,2.033-0.446V10.3c0-0.621-0.326-0.931-0.979-0.931c-0.465,0-0.875,0.117-1.229,0.349l-0.213-0.688
                  c0.438-0.271,0.979-0.407,1.617-0.407c1.232,0,1.85,0.65,1.85,1.95v1.736C82.262,12.78,82.285,13.155,82.33,13.433z
                   M81.242,11.813v-0.727c-1.156-0.02-1.734,0.297-1.734,0.95c0,0.246,0.066,0.43,0.201,0.553c0.135,0.123,0.307,0.184,0.512,0.184
                  c0.23,0,0.445-0.073,0.641-0.218c0.197-0.146,0.318-0.331,0.363-0.558C81.236,11.946,81.242,11.884,81.242,11.813z">
            </path>
            <path fill="#FFFFFF"
              d="M88.285,13.433h-0.93l-0.049-0.757h-0.029c-0.297,0.576-0.803,0.864-1.514,0.864
                  c-0.568,0-1.041-0.223-1.416-0.669s-0.562-1.025-0.562-1.736c0-0.763,0.203-1.381,0.611-1.853c0.395-0.44,0.879-0.66,1.455-0.66
                  c0.633,0,1.076,0.213,1.328,0.64h0.02V6.556h1.049v5.607C88.248,12.622,88.26,13.045,88.285,13.433z M87.199,11.445v-0.786
                  c0-0.136-0.01-0.246-0.029-0.33c-0.059-0.252-0.186-0.464-0.379-0.635c-0.195-0.171-0.43-0.257-0.701-0.257
                  c-0.391,0-0.697,0.155-0.922,0.466c-0.223,0.311-0.336,0.708-0.336,1.193c0,0.466,0.107,0.844,0.322,1.135
                  c0.227,0.31,0.533,0.465,0.916,0.465c0.344,0,0.619-0.129,0.828-0.388C87.1,12.069,87.199,11.781,87.199,11.445z">
            </path>
            <path fill="#FFFFFF" d="M97.248,11.037c0,0.725-0.207,1.319-0.621,1.785c-0.434,0.479-1.008,0.718-1.727,0.718
                  c-0.691,0-1.242-0.229-1.654-0.689c-0.41-0.459-0.615-1.038-0.615-1.736c0-0.73,0.211-1.329,0.635-1.794s0.994-0.698,1.713-0.698
                  c0.691,0,1.248,0.229,1.668,0.688C97.047,9.757,97.248,10.333,97.248,11.037z M96.162,11.071c0-0.435-0.094-0.808-0.281-1.119
                  c-0.221-0.376-0.533-0.564-0.941-0.564c-0.42,0-0.74,0.188-0.961,0.564c-0.188,0.311-0.281,0.69-0.281,1.138
                  c0,0.435,0.094,0.808,0.281,1.119c0.227,0.376,0.543,0.564,0.951,0.564c0.4,0,0.715-0.191,0.941-0.574
                  C96.064,11.882,96.162,11.506,96.162,11.071z"></path>
            <path fill="#FFFFFF" d="M102.883,13.433h-1.047v-2.7c0-0.832-0.316-1.248-0.951-1.248c-0.311,0-0.562,0.114-0.756,0.343
                  s-0.291,0.499-0.291,0.808v2.796h-1.049v-3.366c0-0.414-0.012-0.863-0.037-1.349h0.92l0.049,0.737h0.029
                  c0.123-0.229,0.305-0.418,0.543-0.569c0.285-0.176,0.602-0.265,0.951-0.265c0.439,0,0.805,0.142,1.096,0.427
                  c0.363,0.349,0.543,0.87,0.543,1.562V13.433z"></path>
            <path fill="#FFFFFF" d="M109.936,9.504h-1.154v2.29c0,0.582,0.205,0.873,0.611,0.873c0.188,0,0.344-0.016,0.467-0.049
                  l0.027,0.795c-0.207,0.078-0.479,0.117-0.814,0.117c-0.414,0-0.736-0.126-0.969-0.378c-0.234-0.252-0.35-0.676-0.35-1.271V9.504
                  h-0.689V8.719h0.689V7.855l1.027-0.31v1.173h1.154V9.504z">
            </path>
            <path fill="#FFFFFF"
              d="M115.484,13.433h-1.049v-2.68c0-0.845-0.316-1.268-0.949-1.268c-0.486,0-0.818,0.245-1,0.735
                  c-0.031,0.103-0.049,0.229-0.049,0.377v2.835h-1.047V6.556h1.047v2.841h0.02c0.33-0.517,0.803-0.775,1.416-0.775
                  c0.434,0,0.793,0.142,1.078,0.427c0.355,0.355,0.533,0.883,0.533,1.581V13.433z">
            </path>
            <path fill="#FFFFFF" d="M121.207,10.853c0,0.188-0.014,0.346-0.039,0.475h-3.143c0.014,0.466,0.164,0.821,0.455,1.067
                  c0.266,0.22,0.609,0.33,1.029,0.33c0.465,0,0.889-0.074,1.271-0.223l0.164,0.728c-0.447,0.194-0.973,0.291-1.582,0.291
                  c-0.73,0-1.305-0.215-1.721-0.645c-0.418-0.43-0.625-1.007-0.625-1.731c0-0.711,0.193-1.303,0.582-1.775
                  c0.406-0.504,0.955-0.756,1.648-0.756c0.678,0,1.193,0.252,1.541,0.756C121.068,9.77,121.207,10.265,121.207,10.853z
                   M120.207,10.582c0.008-0.311-0.061-0.579-0.203-0.805c-0.182-0.291-0.459-0.437-0.834-0.437c-0.342,0-0.621,0.142-0.834,0.427
                  c-0.174,0.227-0.277,0.498-0.311,0.815H120.207z"></path>
          </g>
        </g>
      </symbol>
      <symbol id="iTunes_ES" viewBox="0 0 135 40"
        enable-background="new 0 0 135 40">
        <g>
          <path fill="#A6A6A6"
            d="M130.197,40H4.729C2.122,40,0,37.872,0,35.267V4.726C0,2.12,2.122,0,4.729,0h125.468
  C132.803,0,135,2.12,135,4.726v30.541C135,37.872,132.803,40,130.197,40L130.197,40z"></path>
          <path
            d="M134.032,35.268c0,2.116-1.714,3.83-3.834,3.83H4.729c-2.119,0-3.839-1.714-3.839-3.83V4.725
  c0-2.115,1.72-3.835,3.839-3.835h125.468c2.121,0,3.834,1.72,3.834,3.835L134.032,35.268L134.032,35.268z">
          </path>
          <g>
            <g>
              <path fill="#FFFFFF" d="M30.128,19.784c-0.029-3.223,2.639-4.791,2.761-4.864c-1.511-2.203-3.853-2.504-4.676-2.528
      c-1.967-0.207-3.875,1.177-4.877,1.177c-1.022,0-2.565-1.157-4.228-1.123c-2.14,0.033-4.142,1.272-5.24,3.196
      c-2.266,3.923-0.576,9.688,1.595,12.859c1.086,1.553,2.355,3.287,4.016,3.226c1.625-0.067,2.232-1.036,4.193-1.036
      c1.943,0,2.513,1.036,4.207,0.997c1.744-0.028,2.842-1.56,3.89-3.127c1.255-1.78,1.759-3.533,1.779-3.623
      C33.507,24.924,30.161,23.647,30.128,19.784z"></path>
              <path fill="#FFFFFF"
                d="M26.928,10.306c0.874-1.093,1.472-2.58,1.306-4.089c-1.265,0.056-2.847,0.875-3.758,1.944
      c-0.806,0.942-1.526,2.486-1.34,3.938C24.557,12.205,26.016,11.382,26.928,10.306z"></path>
            </g>
          </g>
          <g>
            <path fill="#FFFFFF" d="M53.645,31.504h-2.271l-1.244-3.909h-4.324l-1.185,3.909h-2.211l4.284-13.308h2.646L53.645,31.504z
     M49.755,25.955L48.63,22.48c-0.119-0.355-0.342-1.191-0.671-2.507h-0.04c-0.131,0.566-0.342,1.402-0.632,2.507l-1.105,3.475
    H49.755z"></path>
            <path fill="#FFFFFF"
              d="M64.662,26.588c0,1.632-0.441,2.922-1.323,3.869c-0.79,0.843-1.771,1.264-2.942,1.264
    c-1.264,0-2.172-0.454-2.725-1.362h-0.04v5.055h-2.132V25.067c0-1.026-0.027-2.079-0.079-3.159h1.875l0.119,1.521h0.04
    c0.711-1.146,1.79-1.718,3.238-1.718c1.132,0,2.077,0.447,2.833,1.342C64.284,23.949,64.662,25.127,64.662,26.588z M62.49,26.666
    c0-0.934-0.21-1.704-0.632-2.31c-0.461-0.632-1.08-0.948-1.856-0.948c-0.526,0-1.004,0.176-1.431,0.523
    c-0.428,0.35-0.708,0.807-0.839,1.373c-0.066,0.264-0.099,0.48-0.099,0.65v1.6c0,0.698,0.214,1.287,0.642,1.768
    s0.984,0.721,1.668,0.721c0.803,0,1.428-0.31,1.875-0.928C62.266,28.496,62.49,27.68,62.49,26.666z">
            </path>
            <path fill="#FFFFFF"
              d="M75.699,26.588c0,1.632-0.441,2.922-1.324,3.869c-0.789,0.843-1.77,1.264-2.941,1.264
    c-1.264,0-2.172-0.454-2.724-1.362H68.67v5.055h-2.132V25.067c0-1.026-0.027-2.079-0.079-3.159h1.875l0.119,1.521h0.04
    c0.71-1.146,1.789-1.718,3.238-1.718c1.131,0,2.076,0.447,2.834,1.342C75.32,23.949,75.699,25.127,75.699,26.588z M73.527,26.666
    c0-0.934-0.211-1.704-0.633-2.31c-0.461-0.632-1.078-0.948-1.855-0.948c-0.527,0-1.004,0.176-1.432,0.523
    c-0.428,0.35-0.707,0.807-0.838,1.373c-0.065,0.264-0.099,0.48-0.099,0.65v1.6c0,0.698,0.214,1.287,0.64,1.768
    c0.428,0.48,0.984,0.721,1.67,0.721c0.803,0,1.428-0.31,1.875-0.928C73.303,28.496,73.527,27.68,73.527,26.666z">
            </path>
            <path fill="#FFFFFF"
              d="M88.039,27.772c0,1.132-0.393,2.053-1.182,2.764c-0.867,0.777-2.074,1.165-3.625,1.165
    c-1.432,0-2.58-0.276-3.449-0.829l0.494-1.777c0.936,0.566,1.963,0.85,3.082,0.85c0.803,0,1.428-0.182,1.877-0.544
    c0.447-0.362,0.67-0.848,0.67-1.454c0-0.54-0.184-0.995-0.553-1.364c-0.367-0.369-0.98-0.712-1.836-1.029
    c-2.33-0.869-3.494-2.142-3.494-3.816c0-1.094,0.408-1.991,1.225-2.689c0.814-0.699,1.9-1.048,3.258-1.048
    c1.211,0,2.217,0.211,3.02,0.632l-0.533,1.738c-0.75-0.408-1.598-0.612-2.547-0.612c-0.75,0-1.336,0.185-1.756,0.553
    c-0.355,0.329-0.533,0.73-0.533,1.205c0,0.526,0.203,0.961,0.611,1.303c0.355,0.316,1,0.658,1.936,1.027
    c1.145,0.461,1.986,1,2.527,1.618C87.77,26.081,88.039,26.852,88.039,27.772z"></path>
            <path fill="#FFFFFF" d="M95.088,23.508h-2.35v4.659c0,1.185,0.414,1.777,1.244,1.777c0.381,0,0.697-0.033,0.947-0.099l0.059,1.619
    c-0.42,0.157-0.973,0.236-1.658,0.236c-0.842,0-1.5-0.257-1.975-0.77c-0.473-0.514-0.711-1.376-0.711-2.587v-4.837h-1.4v-1.6h1.4
    v-1.757l2.094-0.632v2.389h2.35V23.508z"></path>
            <path fill="#FFFFFF"
              d="M105.691,26.627c0,1.475-0.422,2.686-1.264,3.633c-0.883,0.975-2.055,1.461-3.516,1.461
    c-1.408,0-2.529-0.467-3.365-1.401s-1.254-2.113-1.254-3.534c0-1.487,0.43-2.705,1.293-3.652c0.861-0.948,2.023-1.422,3.484-1.422
    c1.408,0,2.541,0.467,3.396,1.402C105.283,24.021,105.691,25.192,105.691,26.627z M103.479,26.696
    c0-0.885-0.189-1.644-0.572-2.277c-0.447-0.766-1.086-1.148-1.914-1.148c-0.857,0-1.508,0.383-1.955,1.148
    c-0.383,0.634-0.572,1.405-0.572,2.317c0,0.885,0.189,1.644,0.572,2.276c0.461,0.766,1.105,1.148,1.936,1.148
    c0.814,0,1.453-0.39,1.914-1.168C103.281,28.347,103.479,27.58,103.479,26.696z"></path>
            <path fill="#FFFFFF"
              d="M112.621,23.783c-0.211-0.039-0.436-0.059-0.672-0.059c-0.75,0-1.33,0.283-1.738,0.85
    c-0.355,0.5-0.533,1.132-0.533,1.895v5.035h-2.131l0.02-6.574c0-1.106-0.027-2.113-0.08-3.021h1.857l0.078,1.836h0.059
    c0.225-0.631,0.58-1.139,1.066-1.52c0.475-0.343,0.988-0.514,1.541-0.514c0.197,0,0.375,0.014,0.533,0.039V23.783z">
            </path>
            <path fill="#FFFFFF" d="M122.156,26.252c0,0.382-0.025,0.704-0.078,0.967h-6.396c0.025,0.948,0.334,1.673,0.928,2.173
    c0.539,0.447,1.236,0.671,2.092,0.671c0.947,0,1.811-0.151,2.588-0.454l0.334,1.48c-0.908,0.396-1.98,0.593-3.217,0.593
    c-1.488,0-2.656-0.438-3.506-1.313c-0.848-0.875-1.273-2.05-1.273-3.524c0-1.447,0.395-2.652,1.186-3.613
    c0.828-1.026,1.947-1.539,3.355-1.539c1.383,0,2.43,0.513,3.141,1.539C121.873,24.047,122.156,25.055,122.156,26.252z
     M120.123,25.699c0.014-0.632-0.125-1.178-0.414-1.639c-0.369-0.593-0.936-0.889-1.699-0.889c-0.697,0-1.264,0.289-1.697,0.869
    c-0.355,0.461-0.566,1.014-0.631,1.658H120.123z"></path>
          </g>
          <g>
            <g>
              <path fill="#FFFFFF" d="M48.236,7.077l-0.233,0.837c-0.362-0.168-0.775-0.252-1.241-0.252c-0.705,0-1.265,0.214-1.679,0.643
      c-0.439,0.448-0.659,1.075-0.659,1.879c0,0.772,0.204,1.378,0.611,1.816c0.407,0.438,0.973,0.657,1.697,0.657
      c0.511,0,0.94-0.081,1.29-0.243l0.185,0.828c-0.389,0.186-0.941,0.278-1.659,0.278c-0.982,0-1.762-0.288-2.338-0.863
      c-0.595-0.595-0.892-1.403-0.892-2.425c0-1.047,0.323-1.888,0.97-2.522c0.627-0.607,1.438-0.912,2.435-0.912
      C47.381,6.798,47.886,6.891,48.236,7.077z"></path>
              <path fill="#FFFFFF" d="M54.027,11.037c0,0.725-0.207,1.319-0.621,1.785c-0.434,0.479-1.009,0.718-1.727,0.718
      c-0.692,0-1.243-0.229-1.654-0.689c-0.41-0.459-0.615-1.038-0.615-1.736c0-0.73,0.211-1.329,0.635-1.794s0.994-0.698,1.712-0.698
      c0.692,0,1.248,0.229,1.669,0.688C53.826,9.757,54.027,10.333,54.027,11.037z M52.94,11.071c0-0.435-0.094-0.808-0.281-1.119
      c-0.22-0.376-0.533-0.564-0.94-0.564c-0.421,0-0.741,0.188-0.961,0.564c-0.188,0.311-0.281,0.69-0.281,1.138
      c0,0.435,0.094,0.808,0.281,1.119c0.227,0.376,0.543,0.564,0.951,0.564c0.4,0,0.714-0.191,0.94-0.574
      C52.843,11.882,52.94,11.506,52.94,11.071z"></path>
              <path fill="#FFFFFF" d="M59.662,13.433h-1.048v-2.7c0-0.832-0.316-1.248-0.95-1.248c-0.311,0-0.562,0.114-0.757,0.343
      c-0.193,0.229-0.291,0.499-0.291,0.808v2.796h-1.048v-3.366c0-0.414-0.013-0.863-0.038-1.349h0.921L56.5,9.456h0.029
      c0.122-0.229,0.304-0.418,0.543-0.569c0.284-0.176,0.602-0.265,0.95-0.265c0.44,0,0.806,0.142,1.097,0.427
      c0.362,0.349,0.543,0.87,0.543,1.562V13.433z"></path>
              <path fill="#FFFFFF"
                d="M64.386,12.079c0,0.438-0.161,0.79-0.484,1.055s-0.77,0.397-1.339,0.397c-0.537,0-0.992-0.107-1.367-0.32
      l0.223-0.776c0.362,0.22,0.747,0.33,1.154,0.33c0.537,0,0.805-0.197,0.805-0.592c0-0.174-0.058-0.318-0.174-0.432
      c-0.117-0.113-0.324-0.225-0.621-0.334c-0.841-0.311-1.261-0.763-1.261-1.358c0-0.407,0.155-0.747,0.465-1.019
      c0.311-0.271,0.722-0.407,1.232-0.407c0.466,0,0.863,0.095,1.193,0.285L63.989,9.66c-0.304-0.181-0.624-0.271-0.96-0.271
      c-0.22,0-0.392,0.052-0.515,0.155c-0.122,0.104-0.184,0.235-0.184,0.397c0,0.161,0.064,0.293,0.193,0.396
      c0.11,0.097,0.323,0.203,0.641,0.319C63.979,10.966,64.386,11.44,64.386,12.079z"></path>
              <path fill="#FFFFFF"
                d="M66.996,13.433h-1.048V8.719h1.048V13.433z M67.898,6.74l-1.174,1.406h-0.728l0.834-1.406H67.898z">
              </path>
              <path fill="#FFFFFF"
                d="M72.99,8.719c-0.027,0.382-0.039,0.828-0.039,1.339v2.696c0,1.015-0.227,1.727-0.68,2.134
      c-0.414,0.375-0.996,0.562-1.746,0.562c-0.652,0-1.18-0.123-1.581-0.369l0.243-0.805c0.395,0.239,0.84,0.359,1.338,0.359
      c0.926,0,1.387-0.498,1.387-1.494v-0.456h-0.02c-0.291,0.479-0.752,0.718-1.387,0.718c-0.568,0-1.037-0.216-1.406-0.65
      c-0.369-0.433-0.552-0.983-0.552-1.649c0-0.756,0.213-1.368,0.64-1.833c0.395-0.433,0.875-0.65,1.445-0.65
      c0.641,0,1.1,0.249,1.377,0.747h0.02l0.039-0.65H72.99z M71.902,11.416v-0.834c0-0.317-0.098-0.588-0.297-0.815
      c-0.197-0.227-0.459-0.339-0.783-0.339c-0.357,0-0.652,0.148-0.883,0.446c-0.23,0.298-0.346,0.695-0.346,1.193
      c0,0.453,0.107,0.818,0.322,1.096c0.221,0.304,0.523,0.456,0.906,0.456c0.232,0,0.443-0.069,0.627-0.208
      c0.186-0.139,0.318-0.328,0.395-0.567C71.883,11.726,71.902,11.583,71.902,11.416z"></path>
              <path fill="#FFFFFF" d="M78.887,13.433h-0.922l-0.059-0.718h-0.02c-0.33,0.55-0.828,0.825-1.494,0.825
      c-0.465,0-0.836-0.146-1.115-0.437c-0.33-0.356-0.494-0.893-0.494-1.61V8.719h1.047v2.6c0,0.906,0.311,1.358,0.932,1.358
      c0.467,0,0.789-0.226,0.971-0.679c0.045-0.116,0.068-0.249,0.068-0.398V8.719h1.047v3.356
      C78.848,12.521,78.861,12.974,78.887,13.433z"></path>
              <path fill="#FFFFFF" d="M84.592,10.853c0,0.188-0.014,0.346-0.039,0.475H81.41c0.012,0.466,0.164,0.821,0.455,1.067
      c0.266,0.22,0.607,0.33,1.027,0.33c0.467,0,0.891-0.074,1.271-0.223l0.164,0.728c-0.445,0.194-0.973,0.291-1.58,0.291
      c-0.73,0-1.305-0.215-1.723-0.645c-0.416-0.43-0.625-1.007-0.625-1.731c0-0.711,0.193-1.303,0.582-1.775
      c0.408-0.504,0.957-0.756,1.648-0.756c0.68,0,1.193,0.252,1.543,0.756C84.451,9.77,84.592,10.265,84.592,10.853z M83.592,10.582
      c0.006-0.311-0.062-0.579-0.203-0.805C83.207,9.485,82.93,9.34,82.555,9.34c-0.344,0-0.621,0.142-0.836,0.427
      c-0.174,0.227-0.277,0.498-0.309,0.815H83.592z"></path>
              <path fill="#FFFFFF" d="M87.16,13.433h-1.047V6.556h1.047V13.433z">
              </path>
              <path fill="#FFFFFF" d="M93.33,11.037c0,0.725-0.207,1.319-0.621,1.785c-0.434,0.479-1.01,0.718-1.727,0.718
      c-0.693,0-1.244-0.229-1.654-0.689c-0.41-0.459-0.615-1.038-0.615-1.736c0-0.73,0.211-1.329,0.635-1.794s0.994-0.698,1.711-0.698
      c0.693,0,1.248,0.229,1.67,0.688C93.129,9.757,93.33,10.333,93.33,11.037z M92.242,11.071c0-0.435-0.094-0.808-0.281-1.119
      c-0.219-0.376-0.533-0.564-0.939-0.564c-0.422,0-0.742,0.188-0.961,0.564c-0.188,0.311-0.281,0.69-0.281,1.138
      c0,0.435,0.094,0.808,0.281,1.119c0.227,0.376,0.543,0.564,0.951,0.564c0.4,0,0.713-0.191,0.939-0.574
      C92.146,11.882,92.242,11.506,92.242,11.071z"></path>
              <path fill="#FFFFFF" d="M101.584,10.853c0,0.188-0.014,0.346-0.039,0.475h-3.143c0.014,0.466,0.164,0.821,0.455,1.067
      c0.266,0.22,0.609,0.33,1.029,0.33c0.465,0,0.889-0.074,1.271-0.223l0.164,0.728c-0.447,0.194-0.973,0.291-1.582,0.291
      c-0.73,0-1.305-0.215-1.721-0.645c-0.418-0.43-0.625-1.007-0.625-1.731c0-0.711,0.193-1.303,0.582-1.775
      c0.406-0.504,0.955-0.756,1.648-0.756c0.678,0,1.193,0.252,1.541,0.756C101.445,9.77,101.584,10.265,101.584,10.853z
       M100.584,10.582c0.008-0.311-0.061-0.579-0.203-0.805c-0.182-0.291-0.459-0.437-0.834-0.437c-0.342,0-0.621,0.142-0.834,0.427
      c-0.174,0.227-0.277,0.498-0.311,0.815H100.584z"></path>
              <path fill="#FFFFFF" d="M107.199,13.433h-1.047v-2.7c0-0.832-0.316-1.248-0.951-1.248c-0.311,0-0.562,0.114-0.756,0.343
      s-0.291,0.499-0.291,0.808v2.796h-1.049v-3.366c0-0.414-0.012-0.863-0.037-1.349h0.92l0.049,0.737h0.029
      c0.123-0.229,0.305-0.418,0.543-0.569c0.285-0.176,0.602-0.265,0.951-0.265c0.439,0,0.805,0.142,1.096,0.427
      c0.363,0.349,0.543,0.87,0.543,1.562V13.433z"></path>
              <path fill="#FFFFFF" d="M115.756,10.853c0,0.188-0.014,0.346-0.039,0.475h-3.143c0.012,0.466,0.164,0.821,0.455,1.067
      c0.266,0.22,0.607,0.33,1.027,0.33c0.467,0,0.891-0.074,1.271-0.223l0.164,0.728c-0.445,0.194-0.973,0.291-1.58,0.291
      c-0.73,0-1.305-0.215-1.723-0.645c-0.416-0.43-0.625-1.007-0.625-1.731c0-0.711,0.193-1.303,0.582-1.775
      c0.408-0.504,0.957-0.756,1.648-0.756c0.68,0,1.193,0.252,1.543,0.756C115.615,9.77,115.756,10.265,115.756,10.853z
       M114.756,10.582c0.006-0.311-0.062-0.579-0.203-0.805c-0.182-0.291-0.459-0.437-0.834-0.437c-0.344,0-0.621,0.142-0.836,0.427
      c-0.174,0.227-0.277,0.498-0.309,0.815H114.756z"></path>
              <path fill="#FFFFFF"
                d="M118.326,13.433h-1.049V6.556h1.049V13.433z"></path>
            </g>
          </g>
        </g>
      </symbol>
      <symbol id="GooglePlay" viewBox="0 0 135 40">
        <path
          d="M130 40H5c-2.8 0-5-2.2-5-5V5c0-2.8 2.2-5 5-5h125c2.8 0 5 2.2 5 5v30c0 2.7-2.2 5-5 5z">
        </path>
        <path fill="#A6A6A6"
          d="M130 .8c2.3 0 4.2 1.9 4.2 4.2v30c0 2.3-1.9 4.2-4.2 4.2H5C2.7 39.2.8 37.3.8 35V5C.8 2.7 2.7.8 5 .8h125m0-.8H5C2.2 0 0 2.2 0 5v30c0 2.8 2.2 5 5 5h125c2.8 0 5-2.2 5-5V5c0-2.7-2.2-5-5-5z">
        </path>
        <path fill="#FFFFFF" stroke="#FFFFFF" stroke-width="0.2"
          stroke-miterlimit="10"
          d="M47.4 10.2c0 .8-.2 1.5-.7 2-.6.6-1.3.9-2.2.9s-1.6-.3-2.2-.9c-.6-.6-.9-1.3-.9-2.2 0-.9.3-1.6.9-2.2.6-.6 1.3-.9 2.2-.9.4 0 .8.1 1.2.3.4.2.7.4.9.7l-.5.5c-.4-.5-.9-.7-1.6-.7-.6 0-1.2.2-1.6.7-.5.4-.7 1-.7 1.7s.2 1.3.7 1.7c.5.4 1 .7 1.6.7.7 0 1.2-.2 1.7-.7.3-.3.5-.7.5-1.2h-2.2v-.7h2.9v.3zM52 7.7h-2.7v1.9h2.5v.7h-2.5v1.9H52v.8h-3.5V7H52v.7zm3.3 5.3h-.8V7.7h-1.7V7H57v.7h-1.7V13zm4.6 0V7h.8v6h-.8zm4.2 0h-.8V7.7h-1.7V7h4.1v.7H64l.1 5.3zm9.5-.8c-.6.6-1.3.9-2.2.9-.9 0-1.6-.3-2.2-.9s-.9-1.3-.9-2.2.3-1.6.9-2.2 1.3-.9 2.2-.9c.9 0 1.6.3 2.2.9s.9 1.3.9 2.2c0 .9-.3 1.6-.9 2.2zm-3.8-.5c.4.4 1 .7 1.6.7s1.2-.2 1.6-.7c.4-.4.7-1 .7-1.7s-.2-1.3-.7-1.7c-.4-.4-1-.7-1.6-.7s-1.2.2-1.6.7c-.4.4-.7 1-.7 1.7s.2 1.3.7 1.7zm5.8 1.3V7h.9l2.9 4.7V7h.8v6h-.8l-3.1-4.9V13h-.7z">
        </path>
        <path fill="#FFFFFF"
          d="M68.1 21.8c-2.4 0-4.3 1.8-4.3 4.3 0 2.4 1.9 4.3 4.3 4.3s4.3-1.8 4.3-4.3c0-2.6-1.9-4.3-4.3-4.3zm0 6.8c-1.3 0-2.4-1.1-2.4-2.6s1.1-2.6 2.4-2.6 2.4 1 2.4 2.6c0 1.5-1.1 2.6-2.4 2.6zm-9.3-6.8c-2.4 0-4.3 1.8-4.3 4.3 0 2.4 1.9 4.3 4.3 4.3s4.3-1.8 4.3-4.3c0-2.6-1.9-4.3-4.3-4.3zm0 6.8c-1.3 0-2.4-1.1-2.4-2.6s1.1-2.6 2.4-2.6 2.4 1 2.4 2.6c0 1.5-1.1 2.6-2.4 2.6zm-11.1-5.5v1.8H52c-.1 1-.5 1.8-1 2.3-.6.6-1.6 1.3-3.3 1.3-2.7 0-4.7-2.1-4.7-4.8s2.1-4.8 4.7-4.8c1.4 0 2.5.6 3.3 1.3l1.3-1.3c-1.1-1-2.5-1.8-4.5-1.8-3.6 0-6.7 3-6.7 6.6s3.1 6.6 6.7 6.6c2 0 3.4-.6 4.6-1.9 1.2-1.2 1.6-2.9 1.6-4.2 0-.4 0-.8-.1-1.1h-6.2zm45.4 1.4c-.4-1-1.4-2.7-3.6-2.7s-4 1.7-4 4.3c0 2.4 1.8 4.3 4.2 4.3 1.9 0 3.1-1.2 3.5-1.9l-1.4-1c-.5.7-1.1 1.2-2.1 1.2s-1.6-.4-2.1-1.3l5.7-2.4-.2-.5zm-5.8 1.4c0-1.6 1.3-2.5 2.2-2.5.7 0 1.4.4 1.6.9l-3.8 1.6zM82.6 30h1.9V17.5h-1.9V30zm-3-7.3c-.5-.5-1.3-1-2.3-1-2.1 0-4.1 1.9-4.1 4.3s1.9 4.2 4.1 4.2c1 0 1.8-.5 2.2-1h.1v.6c0 1.6-.9 2.5-2.3 2.5-1.1 0-1.9-.8-2.1-1.5l-1.6.7c.5 1.1 1.7 2.5 3.8 2.5 2.2 0 4-1.3 4-4.4V22h-1.8v.7zm-2.2 5.9c-1.3 0-2.4-1.1-2.4-2.6s1.1-2.6 2.4-2.6 2.3 1.1 2.3 2.6-1 2.6-2.3 2.6zm24.4-11.1h-4.5V30h1.9v-4.7h2.6c2.1 0 4.1-1.5 4.1-3.9s-2-3.9-4.1-3.9zm.1 6h-2.7v-4.3h2.7c1.4 0 2.2 1.2 2.2 2.1-.1 1.1-.9 2.2-2.2 2.2zm11.5-1.8c-1.4 0-2.8.6-3.3 1.9l1.7.7c.4-.7 1-.9 1.7-.9 1 0 1.9.6 2 1.6v.1c-.3-.2-1.1-.5-1.9-.5-1.8 0-3.6 1-3.6 2.8 0 1.7 1.5 2.8 3.1 2.8 1.3 0 1.9-.6 2.4-1.2h.1v1h1.8v-4.8c-.2-2.2-1.9-3.5-4-3.5zm-.2 6.9c-.6 0-1.5-.3-1.5-1.1 0-1 1.1-1.3 2-1.3.8 0 1.2.2 1.7.4-.2 1.2-1.2 2-2.2 2zm10.5-6.6l-2.1 5.4h-.1l-2.2-5.4h-2l3.3 7.6-1.9 4.2h1.9l5.1-11.8h-2zm-16.8 8h1.9V17.5h-1.9V30z">
        </path>
        <path fill="url(#SVGID_1_)"
          d="M10.4,7.5c-0.3,0.3-0.5,0.8-0.5,1.4V31c0,0.6,0.2,1.1,0.5,1.4l0.1,0.1l12.4-12.4V20v-0.1L10.4,7.5L10.4,7.5z">
        </path>
        <path fill="url(#SVGID_2_)"
          d="M27,24.3l-4.1-4.1V20v-0.1l4.1-4.1l0.1,0.1l4.9,2.8c1.4,0.8,1.4,2.1,0,2.9L27,24.3L27,24.3z">
        </path>
        <path fill="url(#SVGID_3_)"
          d="M27.1,24.2L22.9,20L10.4,32.5c0.5,0.5,1.2,0.5,2.1,0.1L27.1,24.2">
        </path>
        <path fill="url(#SVGID_4_)"
          d="M27.1,15.8L12.5,7.5c-0.9-0.5-1.6-0.4-2.1,0.1L22.9,20L27.1,15.8z">
        </path>
        <path opacity="0.2"
          d="M27 24.1l-14.5 8.2c-.8.5-1.5.4-2 0l-.1.1.1.1c.5.4 1.2.5 2 0L27 24.1z">
        </path>
        <path opacity="0.12"
          d="M10.4 32.3c-.3-.3-.4-.8-.4-1.4v.1c0 .6.2 1.1.5 1.4v-.1h-.1zm21.6-11l-5 2.8.1.1 4.9-2.9c.7-.3 1-.8 1-1.3 0 .5-.4.9-1 1.3z">
        </path>
        <path opacity="0.25" fill="#FFFFFF"
          d="M12.5 7.6L32 18.7c.6.4 1 .8 1 1.3 0-.5-.3-1-1-1.4L12.5 7.5C11.1 6.7 10 7.4 10 9v.1c0-1.6 1.1-2.3 2.5-1.5z">
        </path>
      </symbol>
      <symbol id="GooglePlay_ES" viewBox="0 0 135 40">
        <path
          d="M130 40H5c-2.8 0-5-2.2-5-5V5c0-2.8 2.2-5 5-5h125c2.8 0 5 2.2 5 5v30c0 2.7-2.2 5-5 5z">
        </path>
        <path fill="#A6A6A6"
          d="M130 .8c2.3 0 4.2 1.9 4.2 4.2v30c0 2.3-1.9 4.2-4.2 4.2H5C2.7 39.2.8 37.3.8 35V5C.8 2.7 2.7.8 5 .8h125m0-.8H5C2.2 0 0 2.2 0 5v30c0 2.8 2.2 5 5 5h125c2.8 0 5-2.2 5-5V5c0-2.7-2.2-5-5-5z">
        </path>
        <path fill="#FFFFFF"
          d="M68.1 21.8c-2.4 0-4.3 1.8-4.3 4.3 0 2.4 1.9 4.3 4.3 4.3s4.3-1.8 4.3-4.3c0-2.6-1.9-4.3-4.3-4.3zm0 6.8c-1.3 0-2.4-1.1-2.4-2.6s1.1-2.6 2.4-2.6 2.4 1 2.4 2.6c0 1.5-1.1 2.6-2.4 2.6zm-9.3-6.8c-2.4 0-4.3 1.8-4.3 4.3 0 2.4 1.9 4.3 4.3 4.3s4.3-1.8 4.3-4.3c0-2.6-1.9-4.3-4.3-4.3zm0 6.8c-1.3 0-2.4-1.1-2.4-2.6s1.1-2.6 2.4-2.6 2.4 1 2.4 2.6c0 1.5-1.1 2.6-2.4 2.6zm-11.1-5.5v1.8H52c-.1 1-.5 1.8-1 2.3-.6.6-1.6 1.3-3.3 1.3-2.7 0-4.7-2.1-4.7-4.8s2.1-4.8 4.7-4.8c1.4 0 2.5.6 3.3 1.3l1.3-1.3c-1.1-1-2.5-1.8-4.5-1.8-3.6 0-6.7 3-6.7 6.6s3.1 6.6 6.7 6.6c2 0 3.4-.6 4.6-1.9 1.2-1.2 1.6-2.9 1.6-4.2 0-.4 0-.8-.1-1.1h-6.2zm45.4 1.4c-.4-1-1.4-2.7-3.6-2.7s-4 1.7-4 4.3c0 2.4 1.8 4.3 4.2 4.3 1.9 0 3.1-1.2 3.5-1.9l-1.4-1c-.5.7-1.1 1.2-2.1 1.2s-1.6-.4-2.1-1.3l5.7-2.4-.2-.5zm-5.8 1.4c0-1.6 1.3-2.5 2.2-2.5.7 0 1.4.4 1.6.9l-3.8 1.6zM82.6 30h1.9V17.5h-1.9V30zm-3-7.3c-.5-.5-1.3-1-2.3-1-2.1 0-4.1 1.9-4.1 4.3s1.9 4.2 4.1 4.2c1 0 1.8-.5 2.2-1h.1v.6c0 1.6-.9 2.5-2.3 2.5-1.1 0-1.9-.8-2.1-1.5l-1.6.7c.5 1.1 1.7 2.5 3.8 2.5 2.2 0 4-1.3 4-4.4V22h-1.8v.7zm-2.2 5.9c-1.3 0-2.4-1.1-2.4-2.6s1.1-2.6 2.4-2.6 2.3 1.1 2.3 2.6-1 2.6-2.3 2.6zm24.4-11.1h-4.5V30h1.9v-4.7h2.6c2.1 0 4.1-1.5 4.1-3.9s-2-3.9-4.1-3.9zm.1 6h-2.7v-4.3h2.7c1.4 0 2.2 1.2 2.2 2.1-.1 1.1-.9 2.2-2.2 2.2zm11.5-1.8c-1.4 0-2.8.6-3.3 1.9l1.7.7c.4-.7 1-.9 1.7-.9 1 0 1.9.6 2 1.6v.1c-.3-.2-1.1-.5-1.9-.5-1.8 0-3.6 1-3.6 2.8 0 1.7 1.5 2.8 3.1 2.8 1.3 0 1.9-.6 2.4-1.2h.1v1h1.8v-4.8c-.2-2.2-1.9-3.5-4-3.5zm-.2 6.9c-.6 0-1.5-.3-1.5-1.1 0-1 1.1-1.3 2-1.3.8 0 1.2.2 1.7.4-.2 1.2-1.2 2-2.2 2zm10.5-6.6l-2.1 5.4h-.1l-2.2-5.4h-2l3.3 7.6-1.9 4.2h1.9l5.1-11.8h-2zm-16.8 8h1.9V17.5h-1.9V30z">
        </path>
        <path fill="url(#SVGID_1_es)"
          d="M10.4 7.5c-.3.3-.5.8-.5 1.4V31c0 .6.2 1.1.5 1.4l.1.1 12.4-12.4v-.2L10.4 7.5z">
        </path>
        <path fill="url(#SVGID_2_es)"
          d="M27 24.3l-4.1-4.1v-.3l4.1-4.1.1.1 4.9 2.8c1.4.8 1.4 2.1 0 2.9l-5 2.7z">
        </path>
        <path fill="url(#SVGID_3_es)"
          d="M27.1 24.2L22.9 20 10.4 32.5c.5.5 1.2.5 2.1.1l14.6-8.4"></path>
        <path fill="url(#SVGID_4_es)"
          d="M27.1 15.8L12.5 7.5c-.9-.5-1.6-.4-2.1.1L22.9 20l4.2-4.2z"></path>
        <path opacity="0.2"
          d="M27 24.1l-14.5 8.2c-.8.5-1.5.4-2 0l-.1.1.1.1c.5.4 1.2.5 2 0L27 24.1z">
        </path>
        <path opacity="0.12"
          d="M10.4 32.3c-.3-.3-.4-.8-.4-1.4v.1c0 .6.2 1.1.5 1.4v-.1h-.1zm21.6-11l-5 2.8.1.1 4.9-2.9c.7-.3 1-.8 1-1.3 0 .5-.4.9-1 1.3z">
        </path>
        <path opacity="0.25" fill="#FFFFFF"
          d="M12.5 7.6L32 18.7c.6.4 1 .8 1 1.3 0-.5-.3-1-1-1.4L12.5 7.5C11.1 6.7 10 7.4 10 9v.1c0-1.6 1.1-2.3 2.5-1.5z">
        </path>
        <path fill="#fff" stroke="#fff" stroke-width=".16"
          stroke-miterlimit="10"
          d="M41.3 13V7h1.8c.9 0 1.7.3 2.2.8.6.6.8 1.3.8 2.2 0 .9-.3
      1.6-.8 2.2-.5.5-1.3.8-2.2.8h-1.8zm.8-.7h1.1c.7 0 1.2-.2 1.6-.6.4-.4.6-1 .6-1.7s-.2-1.3-.6-1.7c-.4-.4-1-.6-1.6-.6h-1.1v4.6zm5.1.7V7h.8v6h-.8zm3.9.1c-.4
      0-.9-.1-1.3-.4-.4-.3-.7-.7-.8-1.2l.7-.3c.1.3.3.6.5.8.3.2.6.3.9.3.3 0 .6-.1.9-.3.2-.2.4-.4.4-.7 0-.3-.1-.6-.4-.8-.2-.2-.6-.4-1.1-.5-.5-.2-.9-.4-1.2-.6-.3-.3-.4-.6-.4-1s.2-.8.5-1.1c.3-.3.8-.5
      1.3-.5s.9.1 1.2.4.5.5.6.8l-.7.3c-.1-.2-.2-.4-.4-.5-.2-.2-.5-.2-.8-.2s-.5.1-.8.2c-.1.2-.2.4-.2.7 0 .2.1.4.3.6s.5.3.9.4c.3.1.5.2.7.3.2.1.4.2.6.4.2.1.4.3.5.6.1.2.2.5.2.8s-.1.6-.2.8c-.1.2-.3.4-.5.5-.3
      0-.5.1-.7.1-.2.1-.5.1-.7.1zM55 13h-.8V7h2c.5 0 .9.2 1.3.5s.6.8.6 1.3-.2.9-.6 1.3c-.4.3-.8.5-1.3.5H55V13zm0-3.2h1.3c.3 0 .6-.1.8-.3.2-.2.3-.5.3-.7 0-.3-.1-.5-.3-.7-.2-.2-.5-.3-.8-.3H55v2zm9
      2.4c-.6.6-1.3.9-2.2.9-.9 0-1.6-.3-2.2-.9s-.9-1.3-.9-2.2.3-1.6.9-2.2 1.3-.9 2.2-.9c.9 0 1.6.3 2.2.9s.9 1.3.9 2.2c0 .9-.3 1.6-.9 2.2zm-3.8-.5c.4.4 1 .7 1.6.7s1.2-.2 1.6-.7c.4-.4.7-1
      .7-1.7s-.2-1.3-.7-1.7c-.4-.4-1-.7-1.6-.7s-1.2.2-1.6.7c-.4.4-.7 1-.7 1.7s.2 1.3.7 1.7zM66 13V7h.9l2.9 4.7V7h.8v6h-.8l-3.1-4.9V13H66zm6 0V7h.8v6H72zm2.1 0V7h2.2c.5 0 .9.2 1.2.5.3.3.5.7.5
      1.2 0 .3-.1.5-.2.8-.1.2-.3.4-.6.5.3.1.5.3.7.5s.3.5.3.9c0 .5-.2.9-.5 1.2-.4.3-.8.5-1.3.5h-2.3V13zm.8-3.4h1.4c.3 0 .5-.1.7-.3.2-.2.3-.4.3-.6s-.2-.5-.3-.7c-.2-.2-.4-.3-.7-.3h-1.4v1.9zm0 2.7h1.6c.3 0
      .5-.1.7-.3.2-.2.3-.4.3-.7 0-.2-.1-.5-.3-.7-.2-.2-.4-.3-.7-.3H75l-.1 2zm4.3.7V7h.8v5.3h2.6v.7h-3.4zm7.9-5.3h-2.7v1.9h2.5v.7h-2.5v1.9h2.7v.8h-3.5V7h3.5v.7zm6.7
      0h-2.7v1.9h2.5v.7h-2.5v1.9h2.7v.8h-3.5V7h3.5v.7zM95 13V7h.9l2.9 4.7V7h.8v6h-.8l-3.1-4.9V13H95z">
        </path>
      </symbol>
    </svg>


    <div class="container">
      <footer class="c-footer" section="footer">
        <div class="row">
          <div class="col-2">
            <div class="c-footer_site">
              <a href="/" section="logo"><svg class="cnet redball">
                  <use aria-hidden="false" xlink:href="#redball"></use>
                </svg>
              </a>
            </div>
          </div>

          <div class="col-3">

            <div class="c-footer_hed">MORE FROM CNET</div>
            <div class="c-footer_promo" section="promo">
              <div class="c-footer_link_wrap"><a class="c-footer_link"
                  href="/news/iphone-13-and-all-of-apple-most-exciting-announcements-this-week/">iPhone
                  13</a></div>
              <div class="c-footer_link_wrap"><a class="c-footer_link"
                  href="/personal-finance/current-stimulus-check-details-600-california-payments-1000-teacher-bonuses-more/">4th
                  stimulus check status</a></div>
              <div class="c-footer_link_wrap"><a class="c-footer_link"
                  href="/news/best-vpn/">Best VPN service of 2021</a></div>
              <div class="c-footer_link_wrap"><a class="c-footer_link"
                  href="/news/best-wi-fi-router/">The best Wi-Fi routers for
                  2021</a></div>
              <div class="c-footer_link_wrap"><a class="c-footer_link"
                  href="/news/windows-10-update-is-here-make-sure-you-know-these-top-tips-and-tricks/">Windows
                  10 tips and tricks</a></div>
            </div>
          </div>

          <div class="col-2">
            <div class="c-footer_hed">About</div>
            <div class="c-footer_about">
              <div class="c-footer_link_wrap"><a class="c-footer_link"
                  href="/about/" section="about">About CNET</a></div>
              <div class="c-footer_link_wrap"><a class="c-footer_link"
                  href="/user/join-cnet/" section="newsletter">Newsletter</a>
              </div>
              <div class="c-footer_link_wrap"><a class="c-footer_link"
                  href="/sitemaps/" section="sitemap">Sitemap</a></div>

              <div class="c-footer_link_wrap"><a class="c-footer_link"
                  href="https://careers.redventures.com/"
                  data-component="linkTracker"
                  data-link-tracker-options="{&quot;action&quot;:&quot;footer|career&quot;}"
                  rel="noopener noreferrer nofollow" target="_blank">Careers</a>
              </div>

              <div class="c-footer_link_wrap"><a class="c-footer_link"
                  href="https://cnet.zendesk.com/hc/en-us"
                  data-component="linkTracker"
                  data-link-tracker-options="{&quot;action&quot;:&quot;footer|help-center&quot;}"
                  rel="noopener noreferrer nofollow" target="_blank">Help
                  Center</a>
              </div>

              <div class="c-footer_link_wrap"><a class="c-footer_link"
                  href="https://www.rvlicensing.com/"
                  data-component="linkTracker"
                  data-link-tracker-options="{&quot;action&quot;:&quot;footer|licensing&quot;}"
                  rel="noopener noreferrer nofollow"
                  target="_blank">Licensing</a>
              </div>
            </div>
          </div>

          <div class="col-2">
            <div class="c-footer_hed">Policies</div>
            <div class="c-footer_policies">
              <div class="c-footer_link_wrap"><a class="c-footer_link"
                  href="https://redventures.com/privacy-policy.html"
                  data-component="linkTracker"
                  data-link-tracker-options="{&quot;action&quot;:&quot;footer|privacy-policy&quot;}"
                  rel="noopener noreferrer nofollow" target="_blank">Privacy
                  Policy</a>
              </div>

              <div class="c-footer_link_wrap"><a class="c-footer_link"
                  href="https://redventures.com/CMG-terms-of-use.html"
                  data-component="linkTracker"
                  data-link-tracker-options="{&quot;action&quot;:&quot;footer|terms-of-use&quot;}"
                  rel="noopener noreferrer nofollow" target="_blank">Terms of
                  Use</a>
              </div>

              <div class="c-footer_link_wrap"><a
                  class="c-footer_link ot-sdk-show-settings"
                  data-component="linkTracker"
                  data-link-tracker-options="{&quot;action&quot;:&quot;footer|ad-choice&quot;}">Cookie
                  Settings </a>
              </div>

              <div class="c-footer_link_wrap"><a class="c-footer_link"
                  href="https://privacyportal.onetrust.com/webform/************************************/ae88e03f-2b16-4276-9980-27124ba4b2c1"
                  data-component="linkTracker"
                  data-link-tracker-options="{&quot;action&quot;:&quot;footer|compliance-privacy-not-sell&quot;}"
                  rel="noopener noreferrer nofollow" target="_blank">Do Not Sell
                  My Information</a>
              </div>
            </div>
          </div>

          <div class="col-3">

            <div class="c-footer_social">
              <div class="c-footer_hed -lessSpace">Follow</div>

              <div class="c-footer_social_links">
                <div class="social-link facebook">
                  <a href="https://www.facebook.com/cnet"
                    data-component="linkTracker"
                    data-link-tracker-options="{&quot;action&quot;:&quot;footer|facebook&quot;}"
                    rel="noopener noreferrer" target="_blank"><svg
                      class=" facebook">
                      <use aria-hidden="false" xlink:href="#facebook"></use>
                    </svg>
                  </a>
                </div>
                <div class="social-link twitter">
                  <a href="https://twitter.com/CNET"
                    data-component="linkTracker"
                    data-link-tracker-options="{&quot;action&quot;:&quot;footer|twitter&quot;}"
                    rel="noopener noreferrer" target="_blank"><svg
                      class=" twitter">
                      <use aria-hidden="false" xlink:href="#twitter"></use>
                    </svg>
                  </a>
                </div>
                <div class="social-link youtube">
                  <a href="https://www.youtube.com/user/CNETTV/"
                    data-component="linkTracker"
                    data-link-tracker-options="{&quot;action&quot;:&quot;footer|youtube&quot;}"
                    rel="noopener noreferrer" target="_blank"><svg
                      class=" youtube">
                      <use aria-hidden="false" xlink:href="#youtube"></use>
                    </svg>
                  </a>
                </div>
                <div class="social-link instagram">
                  <a href="https://www.instagram.com/cnet/"
                    data-component="linkTracker"
                    data-link-tracker-options="{&quot;action&quot;:&quot;footer|instagram&quot;}"
                    rel="noopener noreferrer nofollow" target="_blank"><svg
                      class=" instagram">
                      <use aria-hidden="false" xlink:href="#instagram"></use>
                    </svg>
                  </a>
                </div>
                <div class="social-link flipboard">
                  <a href="https://flipboard.com/@cnet/"
                    data-component="linkTracker"
                    data-link-tracker-options="{&quot;action&quot;:&quot;footer|flipboard&quot;}"
                    rel="noopener noreferrer nofollow" target="_blank"><svg
                      class=" flipboard">
                      <use aria-hidden="false" xlink:href="#flipboard"></use>
                    </svg>
                  </a>
                </div>
              </div>
            </div>

            <div class="c-footer_info">
              <div class="c-footer_copyright">© 2021 CNET, A RED VENTURES
                COMPANY. ALL RIGHTS RESERVED.</div>
            </div>
          </div>
        </div>
      </footer>
    </div>

  </div>


  <script async="" src="https://www.cnet.com/a/fly/js/native/nav.js"></script>



  <script async="" type="application/javascript"
    data-main="https://www.cnet.com/a/fly/a28c66-fly/js/main.default.js"
    src="https://www.cnet.com/a/fly/js/libs/require-2.1.2.js"></script>




  <button id="ot-sdk-btn" class="ot-sdk-show-settings"
    style="display: block; position: fixed; bottom: 10px; left: 10px; z-index: 3000001;">Cookie
    Settings </button>
  <div id="onetrust-consent-sdk">
    <div class="onetrust-pc-dark-filter ot-fade-in"
      style="z-index: 2147483645; visibility: hidden; opacity: 0; transition: visibility 0s ease 400ms, opacity 400ms linear 0s; display: none;">
    </div>
    <div id="onetrust-pc-sdk" class="otPcPanel ot-hide ot-fade-in" role="dialog"
      aria-labelledby="ot-pc-title" aria-modal="true" lang="en"
      style="visibility: hidden; opacity: 0; transition: visibility 0s ease 400ms, opacity 400ms linear 0s; display: none;">
      <!-- PC Header -->
      <div class="ot-pc-header">
        <div class="ot-pc-logo" role="img" aria-label="Company Logo"
          style="background-image: url(&quot;https://cdn.cookielaw.org/logos/************************************/d7b19758-74b7-4244-8f94-61299f58ea38/11911c5d-a839-46df-a86c-1107b957299e/cnet.png&quot;)">
        </div><button id="close-pc-btn-handler" class="ot-close-icon"
          aria-label="Close"></button>
      </div>
      <div id="ot-pc-content" class="ot-pc-scrollbar">
        <h3 id="ot-pc-title">Privacy Settings</h3>
        <div id="ot-pc-desc">When you visit any website, it may store or
          retrieve information on your browser, mostly in the form of cookies.
          This information might be about you, your preferences or your device
          and is mostly used to make the site work as you expect it to. The
          information does not usually directly identify you, but it can give
          you a more personalized web experience. Because we respect your right
          to privacy, you can choose not to allow some types of cookies. Click
          on the different category headings to find out more and change our
          default settings. However, blocking some types of cookies may impact
          your experience of the site and the services we are able to offer.
          <a href="https://redventures.com/privacy-policy.html"
            class="privacy-notice-link" target="_blank"
            aria-label="More information, Opens in a new window">More
            information</a></div><button
          id="accept-recommended-btn-handler">Allow All</button>
        <section class="ot-sdk-row ot-cat-grp">
          <h3 id="ot-category-title"> Manage Consent Preferences</h3>
          <div class="ot-cat-item ot-always-active-group"
            data-optanongroupid="C0001">
            <h4 class="ot-cat-header" id="ot-header-id-C0001">Strictly Necessary
              Cookies</h4>
            <div class="ot-always-active">Always Active</div>
            <p class="ot-category-desc" id="ot-desc-id-C0001">These cookies are
              necessary for the website to function and cannot be switched off
              in our systems. They are usually only set in response to actions
              made by you which amount to a request for services, such as
              setting your privacy preferences, logging in or filling in forms.
              &nbsp; &nbsp;You can set your browser to block or alert you about
              these cookies, but some parts of the site will not then work.
              These cookies do not store any personally identifiable
              information.</p>
            <div class="ot-hlst-cntr"><a class="category-host-list-handler"
                href="javascript:void(0)" data-parent-id="C0001">Cookies
                Details‎</a></div>
          </div>
          <div class="ot-cat-item" data-optanongroupid="C0002">
            <h4 class="ot-cat-header" id="ot-header-id-C0002">Performance
              Cookies</h4>
            <div class="ot-tgl"><input type="checkbox" name="ot-group-id-C0002"
                id="ot-group-id-C0002" aria-checked="true" role="switch"
                class="category-switch-handler" data-optanongroupid="C0002"
                aria-labelledby="ot-header-id-C0002" checked=""> <label
                class="ot-switch" for="ot-group-id-C0002"><span
                  class="ot-switch-nob"></span> <span
                  class="ot-label-txt">Performance Cookies</span></label> </div>
            <p class="ot-category-desc" id="ot-desc-id-C0002">These cookies
              allow us to count visits and traffic sources so we can measure and
              improve the performance of our site. They help us to know which
              pages are the most and least popular and see how visitors move
              around the site. &nbsp; &nbsp;All information these cookies
              collect is aggregated and therefore anonymous. If you do not allow
              these cookies we will not know when you have visited our site, and
              will not be able to monitor its performance.</p>
            <div class="ot-hlst-cntr"><a class="category-host-list-handler"
                href="javascript:void(0)" data-parent-id="C0002">Cookies
                Details‎</a></div>
          </div>
          <div class="ot-cat-item" data-optanongroupid="C0003">
            <h4 class="ot-cat-header" id="ot-header-id-C0003">Functional Cookies
            </h4>
            <div class="ot-tgl"><input type="checkbox" name="ot-group-id-C0003"
                id="ot-group-id-C0003" aria-checked="true" role="switch"
                class="category-switch-handler" data-optanongroupid="C0003"
                aria-labelledby="ot-header-id-C0003" checked=""> <label
                class="ot-switch" for="ot-group-id-C0003"><span
                  class="ot-switch-nob"></span> <span
                  class="ot-label-txt">Functional Cookies</span></label> </div>
            <p class="ot-category-desc" id="ot-desc-id-C0003">These cookies
              enable the website to provide enhanced functionality and
              personalisation. They may be set by us or by third party providers
              whose services we have added to our pages. &nbsp; &nbsp;If you do
              not allow these cookies then some or all of these services may not
              function properly.</p>
            <div class="ot-hlst-cntr"><a class="category-host-list-handler"
                href="javascript:void(0)" data-parent-id="C0003">Cookies
                Details‎</a></div>
          </div>
          <div class="ot-cat-item" data-optanongroupid="C0004">
            <h4 class="ot-cat-header" id="ot-header-id-C0004">Targeting Cookies
            </h4>
            <div class="ot-tgl"><input type="checkbox" name="ot-group-id-C0004"
                id="ot-group-id-C0004" aria-checked="true" role="switch"
                class="category-switch-handler" data-optanongroupid="C0004"
                aria-labelledby="ot-header-id-C0004" checked=""> <label
                class="ot-switch" for="ot-group-id-C0004"><span
                  class="ot-switch-nob"></span> <span
                  class="ot-label-txt">Targeting Cookies</span></label> </div>
            <p class="ot-category-desc" id="ot-desc-id-C0004">These cookies may
              be set through our site by our advertising partners. They may be
              used by those companies to build a profile of your interests and
              show you relevant adverts on other sites. &nbsp; &nbsp;They do not
              store directly personal information, but are based on uniquely
              identifying your browser and internet device. If you do not allow
              these cookies, you will experience less targeted advertising.</p>
            <div class="ot-hlst-cntr"><a class="category-host-list-handler"
                href="javascript:void(0)" data-parent-id="C0004">Cookies
                Details‎</a></div>
          </div><!-- Non Accordion Group -->
          <!-- Accordion Group section starts -->
          <!-- Accordion Group section ends -->
        </section>
      </div>
      <section id="ot-pc-lst" class="ot-hide ot-pc-scrollbar ot-enbl-chr">
        <div id="ot-pc-hdr">
          <h3 id="ot-lst-title"><a class="back-btn-handler"
              href="javascript:void(0)" aria-label="Back"><svg id="ot-back-arw"
                xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
                viewBox="0 0 444.531 444.531" xml:space="preserve">
                <title>Back Button</title>
                <g>
                  <path fill="#656565" d="M213.13,222.409L351.88,83.653c7.05-7.043,10.567-15.657,10.567-25.841c0-10.183-3.518-18.793-10.567-25.835
        l-21.409-21.416C323.432,3.521,314.817,0,304.637,0s-18.791,3.521-25.841,10.561L92.649,196.425
        c-7.044,7.043-10.566,15.656-10.566,25.841s3.521,18.791,10.566,25.837l186.146,185.864c7.05,7.043,15.66,10.564,25.841,10.564
        s18.795-3.521,25.834-10.564l21.409-21.412c7.05-7.039,10.567-15.604,10.567-25.697c0-10.085-3.518-18.746-10.567-25.978
        L213.13,222.409z"></path>
                </g>
              </svg> </a><span>Back</span></h3>
          <div class="ot-lst-subhdr">
            <div class="ot-search-cntr"><label for="vendor-search-handler"
                class="ot-scrn-rdr">Vendor Search</label> <input
                id="vendor-search-handler" type="text" placeholder="Search..."
                name="vendor-search-handler"> <svg
                xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
                viewBox="0 -30 110 110">
                <title>Search Icon</title>
                <path fill="#2e3644" d="M55.146,51.887L41.588,37.786c3.486-4.144,5.396-9.358,5.396-14.786c0-12.682-10.318-23-23-23s-23,10.318-23,23
          s10.318,23,23,23c4.761,0,9.298-1.436,13.177-4.162l13.661,14.208c0.571,0.593,1.339,0.92,2.162,0.92
          c0.779,0,1.518-0.297,2.079-0.837C56.255,54.982,56.293,53.08,55.146,51.887z M23.984,6c9.374,0,17,7.626,17,17s-7.626,17-17,17
          s-17-7.626-17-17S14.61,6,23.984,6z"></path>
              </svg></div>
            <div class="ot-fltr-cntr"><button id="filter-btn-handler"
                aria-label="Filter"><svg role="presentation" aria-hidden="true"
                  xmlns="http://www.w3.org/2000/svg"
                  xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
                  viewBox="0 0 402.577 402.577" xml:space="preserve">
                  <title>Filter Icon</title>
                  <g>
                    <path fill="#fff" d="M400.858,11.427c-3.241-7.421-8.85-11.132-16.854-11.136H18.564c-7.993,0-13.61,3.715-16.846,11.136
    c-3.234,7.801-1.903,14.467,3.999,19.985l140.757,140.753v138.755c0,4.955,1.809,9.232,5.424,12.854l73.085,73.083
    c3.429,3.614,7.71,5.428,12.851,5.428c2.282,0,4.66-0.479,7.135-1.43c7.426-3.238,11.14-8.851,11.14-16.845V172.166L396.861,31.413
    C402.765,25.895,404.093,19.231,400.858,11.427z"></path>
                  </g>
                </svg></button></div>
            <div id="ot-anchor"></div>
            <section id="ot-fltr-modal">
              <div id="ot-fltr-cnt"><button
                  id="clear-filters-handler">Clear</button>
                <div class="ot-fltr-scrlcnt ot-pc-scrollbar">
                  <div class="ot-fltr-opts">
                    <div class="ot-fltr-opt">
                      <div class="ot-chkbox"><input id="chkbox-id"
                          type="checkbox" aria-checked="false"
                          class="category-filter-handler"> <label
                          for="chkbox-id"><span class="ot-label-txt">checkbox
                            label</span></label> <span
                          class="ot-label-status">label</span></div>
                    </div>
                  </div>
                  <div class="ot-fltr-btns"><button
                      id="filter-apply-handler">Apply</button> <button
                      id="filter-cancel-handler">Cancel</button></div>
                </div>
              </div>
            </section>
          </div>
        </div>
        <section id="ot-lst-cnt" class="ot-pc-scrollbar">
          <div id="ot-sel-blk">
            <div class="ot-sel-all">
              <div class="ot-sel-all-hdr"><span
                  class="ot-consent-hdr">Consent</span> <span
                  class="ot-li-hdr">Leg.Interest</span></div>
              <div class="ot-sel-all-chkbox">
                <div class="ot-chkbox" id="ot-selall-hostcntr"><input
                    id="select-all-hosts-groups-handler" type="checkbox"
                    aria-checked="false"> <label
                    for="select-all-hosts-groups-handler"><span
                      class="ot-label-txt">checkbox label</span></label> <span
                    class="ot-label-status">label</span></div>
                <div class="ot-chkbox" id="ot-selall-vencntr"><input
                    id="select-all-vendor-groups-handler" type="checkbox"
                    aria-checked="false"> <label
                    for="select-all-vendor-groups-handler"><span
                      class="ot-label-txt">checkbox label</span></label> <span
                    class="ot-label-status">label</span></div>
                <div class="ot-chkbox" id="ot-selall-licntr"><input
                    id="select-all-vendor-leg-handler" type="checkbox"
                    aria-checked="false"> <label
                    for="select-all-vendor-leg-handler"><span
                      class="ot-label-txt">checkbox label</span></label> <span
                    class="ot-label-status">label</span></div>
              </div>
            </div>
          </div>
          <div class="ot-sdk-row">
            <div class="ot-sdk-column">
              <ul id="ot-host-lst">
                <li class="ot-host-item"><input type="checkbox"
                    class="ot-host-box" role="button" aria-expanded="false">
                  <section class="ot-acc-hdr">
                    <div class="ot-host-hdr">
                      <h3 class="ot-host-name">33Across</h3>
                      <h4 class="ot-host-desc">host description</h4>
                      <h4 class="ot-host-expand">View Cookies</h4>
                    </div>
                    <div class="ot-tgl-cntr"></div>
                  </section>
                  <div class="ot-acc-txt">
                    <div class="ot-host-opts">
                      <ul class="ot-host-opt">
                        <li class="ot-host-info">
                          <div>
                            <div>Name</div>
                            <div>cookie name</div>
                          </div>
                        </li>
                      </ul>
                    </div>
                  </div>
                </li>
              </ul>
              <ul id="ot-ven-lst">
                <li class="ot-ven-item"><input type="checkbox"
                    class="ot-ven-box" role="button" aria-expanded="false">
                  <section class="ot-acc-hdr">
                    <div class="ot-ven-hdr">
                      <h3 class="ot-ven-name">33Across</h3><a
                        class="ot-ven-link" href="#">View Privacy Notice</a>
                    </div>
                    <div class="ot-tgl-cntr"></div>
                  </section>
                  <div class="ot-acc-txt">
                    <div class="ot-ven-dets">
                      <div class="ot-ven-pur"></div>
                    </div>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </section>
      </section><!-- Footer buttons and logo -->
      <div class="ot-pc-footer">
        <div class="ot-btn-container"> <button
            class="save-preference-btn-handler onetrust-close-btn-handler">Confirm
            My Choices</button></div>
        <div class="ot-pc-footer-logo"><a
            href="https://onetrust.com/poweredbyonetrust" target="_blank"
            rel="noopener" aria-label="Powered by Onetrust"
            style="background-image: url(&quot;https://cdn.cookielaw.org/logos/static/poweredBy_ot_logo.svg&quot;)"></a>
        </div>
      </div><!-- Cookie subgroup container -->
      <!-- Vendor list link -->
      <!-- Cookie lost link -->
      <!-- Toggle HTML element -->
      <!-- Checkbox HTML -->
      <!-- Arrow SVG element -->
      <!-- plus minus-->
      <!-- Accordion basic element --><span class="ot-scrn-rdr"
        aria-atomic="true" aria-live="polite">Your Privacy [`dialog
        closed`]</span><iframe class="ot-text-resize"
        title="onetrust-text-resize"
        style="position:absolute;top:-50000px;width:100em;"
        aria-hidden="true"></iframe>
    </div>
    <div id="onetrust-banner-sdk" class="otFlat bottom vertical-align-content"
      role="dialog" aria-labelledby="onetrust-policy-title"
      aria-describedby="onetrust-policy-text"
      style="bottom: 0px; visibility: hidden; opacity: 0; transition: visibility 0s ease 400ms, opacity 400ms linear 0s; display: none;">
      <div class="ot-sdk-container">
        <div class="ot-sdk-row">
          <div id="onetrust-group-container"
            class="ot-sdk-eight ot-sdk-columns">
            <div class="banner_logo"></div>
            <div id="onetrust-policy">
              <!-- Mobile Close Button -->
              <div id="onetrust-close-btn-container-mobile"
                class="ot-hide-large"></div><!-- Mobile Close Button END-->
              <p id="onetrust-policy-text">We use cookies and similar
                technologies to understand how you use our services, improve
                your experience and serve you personalized content and
                advertising. By clicking "Accept", you accept all cookies. To
                manage your cookies and learn more about our use of cookies
                click “Cookie Settings”.<a
                  href="https://redventures.com/privacy-policy.html"
                  tabindex="0">Learn more.</a></p>
            </div>
          </div>
          <div id="onetrust-button-group-parent"
            class="ot-sdk-three ot-sdk-columns">
            <div id="onetrust-button-group"><button id="onetrust-pc-btn-handler"
                tabindex="0">Cookie Settings </button> <button
                id="onetrust-accept-btn-handler" tabindex="0">Accept</button>
            </div>
          </div><!-- Close Button -->
          <div id="onetrust-close-btn-container"
            class="ot-sdk-one ot-sdk-column ot-hide-small"></div>
          <!-- Close Button END-->
        </div>
      </div>
    </div>
  </div>
</body>
</html>
