<!DOCTYPE html>
<html lang="en" data-lt-installed="true">

<head>
  <script async="" src="//connect.facebook.net/en_US/fbevents.js"></script>
  <script type="text/javascript" charset="utf-8" async="" src="https://ssl.p.jwpcdn.com/player/v/8.27.1/jwpsrv.js"></script>
  <title>The new 'Dune' is a great start — but can't outrun the book's biggest problem | Mashable</title>
  <meta charset="utf-8">
  <link rel="canonical" href="https://mashable.com/article/dune-movie-hbo-review">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="theme-color" content="#00aeef">
  <meta name="application-name" content="Mashable.com">
  <meta name="description" content="The movie, now on HBO Max, is brilliantly faithful to the Frank Herbert original. That reveals some flaws. ">
  <meta property="fb:app_id" content="122071082108">
  <meta property="fb:pages" content="18807449704">
  <meta property="og:site_name" content="Mashable">
  <meta property="og:title" content="The new 'Dune' is a great start — but can't outrun the book's biggest problem">
  <meta property="og:url" content="https://mashable.com/article/dune-movie-hbo-review">
  <meta property="og:description" content="The movie, now on HBO Max, is brilliantly faithful to the Frank Herbert original. That reveals some flaws. ">
  <meta property="og:image" content="https://helios-i.mashable.com/imagery/articles/03kMbX4uhsnOpnHQkfhoNzO/hero-image.fill.size_1200x675.v1634938507.jpg">
  <meta property="og:image:secure_url" content="https://helios-i.mashable.com/imagery/articles/03kMbX4uhsnOpnHQkfhoNzO/hero-image.fill.size_1200x675.v1634938507.jpg">
  <meta property="og:image:height" content="675">
  <meta property="og:image:width" content="1200">
  <meta property="og:type" content="article">
  <meta property="article:author" content="Chris Taylor">
  <meta property="article:modified_time" content="2021-10-29T15:36:15+00:00">
  <meta property="article:published_time" content="2021-10-22T00:44:09+00:00">
  <meta property="article:section" content="Entertainment">
  <meta name="twitter:site" content="@mashable">
  <meta property="twitter:card" content="summary_large_image">
  <meta property="twitter:creator" content="@mashable">
  <meta property="twitter:title" content="The new 'Dune' is a great start — but can't outrun the book's biggest problem">
  <meta property="twitter:description" content="The movie, now on HBO Max, is brilliantly faithful to the Frank Herbert original. That reveals some flaws. ">
  <meta property="twitter:image" content="https://helios-i.mashable.com/imagery/articles/03kMbX4uhsnOpnHQkfhoNzO/hero-image.fill.size_1200x675.v1634938507.jpg">
  <meta name="robots" content="max-image-preview:large">
  <link rel="alternate" hreflang="en" href="https://mashable.com/article/dune-movie-hbo-review">
  <link rel="alternate" hreflang="en-in" href="https://in.mashable.com/entertainment/25477/the-new-dune-is-a-great-start-but-cant-outrun-the-books-biggest-problem">
  <link rel="alternate" hreflang="en-my" href="https://sea.mashable.com/entertainment/17964/the-new-dune-is-a-great-start-but-cant-outrun-the-books-biggest-problem">
  <link rel="alternate" hreflang="en-pk" href="https://pk.mashable.com/entertainment/13049/the-new-dune-is-a-great-start-but-cant-outrun-the-books-biggest-problem">
  <link rel="alternate" hreflang="en-sg" href="https://sea.mashable.com/entertainment/17964/the-new-dune-is-a-great-start-but-cant-outrun-the-books-biggest-problem">
  <link rel="alternate" hreflang="en-us" href="https://mashable.com/article/dune-movie-hbo-review">
  <link rel="alternate" hreflang="x-default" href="https://mashable.com/article/dune-movie-hbo-review">
  <link rel="shortcut icon mask-icon" href="/favicons/favicon.svg" color="#000">
  <link rel="icon" type="image/png" href="/favicons/favicon-16x16.png" sizes="16x16">
  <link rel="icon" type="image/png" href="/favicons/favicon-32x32.png" sizes="32x32">
  <link rel="apple-touch-icon" sizes="180x180" href="/favicons/apple-touch-icon.png">
  <link rel="icon" type="image/png" href="/favicons/android-chrome-192x192.png" sizes="192x192">
  <link rel="icon" type="image/png" href="/favicons/android-chrome-512x512.png" sizes="512x512">
  <link rel="manifest" href="/manifest.json" crossorigin="use-credentials">
  <link rel="stylesheet" href="/css/app.css?id=3aae50c2cd2fa1d8e3da650d8a727f1f">
  <script type="text/javascript" async="" src="//static.chartbeat.com/js/chartbeat_video.js"></script>
  <script async="" src="https://www.googletagmanager.com/gtm.js?id=GTM-MN78SWW"></script>
  <script async="" src="https://www.google-analytics.com/analytics.js"></script>
  <script
    type="application/ld+json">{"@context":"https:\/\/schema.org","@type":"NewsArticle","headline":"The new 'Dune' is a great start — but can't outrun the book's biggest problem","datePublished":"2021-10-22T00:44:09+00:00","dateModified":"2021-10-29T15:36:15+00:00","publisher":{"@type":"Organization","url":"https:\/\/mashable.com\/","name":"Mashable","logo":{"@type":"ImageObject","url":"https:\/\/mashable.com\/images\/mashable-logomark.png","width":2400,"height":2400},"sameAs":["https:\/\/www.facebook.com\/mashable\/","https:\/\/twitter.com\/mashable","https:\/\/flipboard.com\/@Mashable","https:\/\/www.pinterest.com\/mashable\/","https:\/\/www.youtube.com\/user\/mashable"],"description":"Mashable is a global, multi-platform media and entertainment company.\n            Powered by its own proprietary technology, Mashable is the go-to source for tech,\n             digital culture and entertainment content for its dedicated and influential audience around the globe."},"author":[{"@type":"Person","name":"Chris Taylor","email":"<EMAIL>","url":"https:\/\/mashable.com\/author\/chris-taylor","image":[{"@type":"ImageObject","url":"https:\/\/helios-i.mashable.com\/imagery\/authors\/05EkIwdTFdfBnl3ahBh6Vjv\/image.fill.size_1200x675.v1624381061.jpg","width":1200,"height":675},{"@type":"ImageObject","url":"https:\/\/helios-i.mashable.com\/imagery\/authors\/05EkIwdTFdfBnl3ahBh6Vjv\/image.fill.size_1200x900.v1624381061.jpg","width":1200,"height":900},{"@type":"ImageObject","url":"https:\/\/helios-i.mashable.com\/imagery\/authors\/05EkIwdTFdfBnl3ahBh6Vjv\/image.fill.size_1200x1200.v1624381061.jpg","width":1200,"height":1200}]}],"image":[{"@type":"ImageObject","url":"https:\/\/helios-i.mashable.com\/imagery\/articles\/03kMbX4uhsnOpnHQkfhoNzO\/hero-image.fill.size_1200x675.v1634938507.jpg","width":1200,"height":675,"caption":"Timothée Chalamet, a.k.a. Paul Atreides, a.k.a. Muad'Dib, a.k.a. the Kwisatz Haderach: A white male savior by many other names."},{"@type":"ImageObject","url":"https:\/\/helios-i.mashable.com\/imagery\/articles\/03kMbX4uhsnOpnHQkfhoNzO\/hero-image.fill.size_1200x900.v1634938507.jpg","width":1200,"height":900,"caption":"Timothée Chalamet, a.k.a. Paul Atreides, a.k.a. Muad'Dib, a.k.a. the Kwisatz Haderach: A white male savior by many other names."},{"@type":"ImageObject","url":"https:\/\/helios-i.mashable.com\/imagery\/articles\/03kMbX4uhsnOpnHQkfhoNzO\/hero-image.fill.size_1200x1200.v1634938507.jpg","width":1200,"height":1200,"caption":"Timothée Chalamet, a.k.a. Paul Atreides, a.k.a. Muad'Dib, a.k.a. the Kwisatz Haderach: A white male savior by many other names."}],"description":"The movie, now on HBO Max, is brilliantly faithful to the Frank Herbert original. That reveals some flaws. ","mainEntityOfPage":"https:\/\/mashable.com\/article\/dune-movie-hbo-review","articleSection":"Entertainment"}</script>
  <script type="application/ld+json">{"@context":"https:\/\/schema.org","@type":"Organization","url":"https:\/\/mashable.com\/","name":"Mashable","logo":{"@type":"ImageObject","url":"https:\/\/mashable.com\/images\/mashable-logomark.png","width":2400,"height":2400},"sameAs":["https:\/\/www.facebook.com\/mashable\/","https:\/\/twitter.com\/mashable","https:\/\/flipboard.com\/@Mashable","https:\/\/www.pinterest.com\/mashable\/","https:\/\/www.youtube.com\/user\/mashable"],"description":"Mashable is a global, multi-platform media and entertainment company.\n            Powered by its own proprietary technology, Mashable is the go-to source for tech,\n             digital culture and entertainment content for its dedicated and influential audience around the globe."}</script>
  <script type="application/ld+json">{"@context":"https:\/\/schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"name":"Home","item":"https:\/\/mashable.com"},{"@type":"ListItem","position":2,"name":"Entertainment","item":"https:\/\/mashable.com\/entertainment"}]}</script>
  <link rel="preconnect" href="//cdn.ziffstatic.com">
  <link rel="preconnect" href="//www.googletagmanager.com">
  <link rel="preconnect" href="https://use.typekit.net">
  <link rel="preload" href="/fonts/Outfit.woff2" as="font" type="font/woff2" crossorigin="">
  <link rel="preload" href="https://g.mashable.com/mashable.js?url=https%3A%2F%2Fmashable.com%2Farticle%2Fdune-movie-hbo-review" as="script">
  <script type="text/javascript" src="https://g.mashable.com/mashable.js?url=https%3A%2F%2Fmashable.com%2Farticle%2Fdune-movie-hbo-review" async=""></script>
  <link rel="preload" as="image" href="/images/icons/spritemap.svg">
  <link rel="preload" href="https://cdn.ziffstatic.com/jst/zdconsent.js" as="script">
  <script type="text/javascript" src="https://cdn.ziffstatic.com/jst/zdconsent.js" async=""></script>
  <link rel="preload" href="https://cdn.static.zdbb.net/js/z0WVjCBSEeGLoxIxOQVEwQ.min.js" as="script">
  <script type="text/javascript" src="https://cdn.static.zdbb.net/js/z0WVjCBSEeGLoxIxOQVEwQ.min.js" async=""></script>
  <link rel="preload" href="https://www.google-analytics.com/analytics.js" as="script">
  <script>
    (function (i, s, o, g, r, a, m) {
      i['GoogleAnalyticsObject'] = r; i[r] = i[r] || function () {
        (i[r].q = i[r].q || []).push(arguments)
      }, i[r].l = 1 * new Date(); a = s.createElement(o),
        m = s.getElementsByTagName(o)[0]; a.async = 1; a.src = g; m.parentNode.insertBefore(a, m)
    })(window, document, 'script', 'https://www.google-analytics.com/analytics.js', 'ga');

    window.globalGAInfo = { "page_view_type": "Standard", "template": "article", "content_type": "article", "transport_type": "beacon", "allow_ad_personalization_signals": false, "test_uuid": null, "test_version": null, "custom_map": { "dimension10": "content_type", "dimension11": "channel_name", "dimension12": "post_lead_type", "dimension13": "content_source_type", "dimension14": "content_source_name", "dimension15": "author_name", "dimension17": "day_published", "dimension18": "month_published", "dimension19": "year_published", "dimension20": "full_published_date", "dimension21": "video_show_name", "dimension23": "cms_id", "dimension27": "level_of_effort", "dimension29": "job_function", "dimension32": "tags", "dimension33": "story_flags", "dimension34": "categories", "dimension36": "page_view_type", "dimension38": "ad_block", "dimension41": "videoId", "dimension42": "videoPlayType", "dimension43": "videoPlayerType", "dimension44": "videoUrl", "dimension45": "revenue_page_type", "dimension46": "videoName", "dimension49": "template", "dimension50": "module", "dimension51": "element", "dimension52": "item", "dimension53": "position", "dimension62": "object_type", "dimension63": "object_uuid", "dimension65": "test_uuid", "dimension66": "test_version", "dimension67": "original_published_date", "dimension69": "days_since_updated", "dimension70": "weeks_since_updated", "dimension71": "days_since_originally_published", "dimension72": "weeks_since_originally_published" }, "cms_id": 132388, "object_uuid": "03kMbX4uhsnOpnHQkfhoNzO", "object_type": "article", "title": "The new \u0027Dune\u0027 is a great start \u2014 but can\u0027t outrun the book\u0027s biggest problem", "canonical_url": "https:\/\/mashable.com\/article\/dune-movie-hbo-review", "full_published_date": "10\/22\/2021", "original_published_date": "10\/22\/2021", "channel_name": "Entertainment", "job_function": "Explainer\/Analysis", "level_of_effort": "One day", "story_flags": "evergreen", "content_source_name": "Internal", "video_show_name": "", "content_source_type": "Internal", "author_name": "Chris Taylor", "day_published": 22, "month_published": 10, "year_published": 2021, "days_since_updated": 622, "weeks_since_updated": 88, "days_since_originally_published": 622, "weeks_since_originally_published": 88, "categories": "", "sections": "Entertainment", "tags": "", "post_lead_type": "Alt Image Lead" };
    window.globalGAInfo.page_path = window.location.pathname;

    ga('create', 'UA-92124-1', 'auto');

    if (typeof window._ziffIntlGeoRedirect === 'undefined') {
      ga('send', 'pageview', {
        'dimension10': window.globalGAInfo.content_type || null,
        'dimension11': window.globalGAInfo.channel_name || null,
        'dimension12': window.globalGAInfo.post_lead_type || null,
        'dimension13': window.content_source_type || null,
        'dimension14': window.globalGAInfo.content_source_name || null,
        'dimension15': window.globalGAInfo.author_name || null,
        'dimension17': window.globalGAInfo.day_published || null,
        'dimension18': window.globalGAInfo.month_published || null,
        'dimension19': window.globalGAInfo.year_published || null,
        'dimension20': window.globalGAInfo.full_published_date || null,
        'dimension21': window.globalGAInfo.video_show_name || null,
        'dimension23': window.globalGAInfo.cms_id || null,
        'dimension27': window.globalGAInfo.level_of_effort || null,
        'dimension29': window.globalGAInfo.job_function || null,
        'dimension32': window.globalGAInfo.tags || null,
        'dimension33': window.globalGAInfo.story_flags || null,
        'dimension34': window.globalGAInfo.categories || null,
        'dimension36': window.globalGAInfo.page_view_type || null,
        'dimension38': window.adblock ? 'Blocked' : 'NotBlocked',
        'dimension41': window.globalGAInfo.videoId || null,
        'dimension42': window.globalGAInfo.videoPlayType || null,
        'dimension43': window.globalGAInfo.videoPlayerType || null,
        'dimension44': window.globalGAInfo.videoUrl || null,
        'dimension45': window.globalGAInfo.revenue_page_type || null,
        'dimension46': window.globalGAInfo.videoName || null,
        'dimension49': window.globalGAInfo.template || null,
        'dimension50': window.globalGAInfo.module || null,
        'dimension51': window.globalGAInfo.element || null,
        'dimension52': window.globalGAInfo.item || null,
        'dimension53': window.globalGAInfo.position || null,
        'dimension62': window.globalGAInfo.object_type || null,
        'dimension63': window.globalGAInfo.object_uuid || null,
        'dimension65': window.globalGAInfo.test_uuid || null,
        'dimension66': window.globalGAInfo.test_version || null,
        'dimension67': window.globalGAInfo.original_published_date || null,
        'dimension69': window.globalGAInfo.days_since_updated || null,
        'dimension70': window.globalGAInfo.weeks_since_updated || null,
        'dimension71': window.globalGAInfo.days_since_originally_published || null,
        'dimension72': window.globalGAInfo.weeks_since_originally_published || null,
      });
    }
  </script>
  <script async="" src="https://www.googletagmanager.com/gtag/js?id=G-BPBF083TYP"></script>
  <script>
    window.dataLayer = window.dataLayer || [];

    function gtag() { dataLayer.push(arguments); }
    gtag('js', new Date());
    gtag('config', 'G-BPBF083TYP', {
      'send_page_view': false,
    });

    if (typeof window._ziffIntlGeoRedirect === 'undefined') {
      gtag('event', 'page_view', {
        ad_block: window.adblock ? 'Blocked' : 'NotBlocked',
        author: window.globalGAInfo.author_name || null,
        categories: window.globalGAInfo.categories || null,
        channel_name: window.globalGAInfo.channel_name || null,
        content_lead_type: window.globalGAInfo.post_lead_type || null,
        content_source_name: window.globalGAInfo.content_source_name || null,
        content_source_type: window.globalGAInfo.content_source_type || null,
        day_published: window.globalGAInfo.day_published || null,
        days_since_originally_published: window.globalGAInfo.days_since_originally_published || null,
        days_since_updated: window.globalGAInfo.days_since_updated || null,
        element: window.globalGAInfo.element || null,
        first_published_at: window.globalGAInfo.original_published_date || null,
        item: window.globalGAInfo.item || null,
        job_function: window.globalGAInfo.job_function || null,
        level_of_effort: window.globalGAInfo.level_of_effort || null,
        module: window.globalGAInfo.module || null,
        month_published: window.globalGAInfo.month_published || null,
        object_type: window.globalGAInfo.object_type || null,
        object_uuid: window.globalGAInfo.object_uuid || null,
        pageview_type: window.globalGAInfo.page_view_type || null,
        position: window.globalGAInfo.position || null,
        published_at: window.globalGAInfo.full_published_date || null,
        revenue_page_type: window.globalGAInfo.revenue_page_type || null,
        story_flags: window.globalGAInfo.story_flags || null,
        tags: window.globalGAInfo.tags || null,
        template: window.globalGAInfo.template || null,
        test_uuid: window.globalGAInfo.test_uuid || null,
        test_version: window.globalGAInfo.test_version || null,
        video_id: window.globalGAInfo.video_id || null,
        video_name: window.globalGAInfo.video_name || null,
        video_play_type: window.globalGAInfo.video_play_type || null,
        video_player_type: window.globalGAInfo.video_player_type || null,
        video_show_name: window.globalGAInfo.video_show_name || null,
        video_url: window.globalGAInfo.video_url || null,
        weeks_since_originally_published: window.globalGAInfo.weeks_since_originally_published || null,
        weeks_since_updated: window.globalGAInfo.weeks_since_updated || null,
        year_published: window.globalGAInfo.year_published || null,
      });
    }
  </script>
  <script>(function (w, d, s, l, i) { w[l] = w[l] || []; w[l].push({ 'gtm.start': new Date().getTime(), event: 'gtm.js' }); var f = d.getElementsByTagName(s)[0], j = d.createElement(s), dl = l != 'dataLayer' ? '&l=' + l : ''; j.async = true; j.src = 'https://www.googletagmanager.com/gtm.js?id=' + i + dl; f.parentNode.insertBefore(j, f); })(window, document, 'script', 'dataLayer', 'GTM-MN78SWW');</script>
  <script type="text/javascript">
    (function () {
      let sections = '';
      if (window.globalGAInfo.sections) {
        sections = window.globalGAInfo.sections.split(',').map(item => 'Category - ' + item.trim()).join(',');
      }

      //Adding tags into sections with "Tag - " prefix for chartbeat reporting
      if (window.globalGAInfo.tags) {
        sections += (sections !== '' ? ',' : '') + window.globalGAInfo.tags.split(',').map(item => 'Tag - ' + item.trim()).join(',');
      }
      if (window.globalGAInfo.content_type) {
        sections += (sections !== '' ? ',' : '') + 'Content Type - ' + window.globalGAInfo.content_type;
      }
      var _sf_async_config = window._sf_async_config = (window._sf_async_config || {});
      /** CONFIGURATION START **/
      _sf_async_config.uid = 65789;
      _sf_async_config.domain = 'mashable.com';
      _sf_async_config.useCanonical = true;
      _sf_async_config.useCanonicalDomain = true;
      _sf_async_config.sections = sections;
      _sf_async_config.authors = window.globalGAInfo.author_name || '';
      _sf_async_config.type = window.globalGAInfo.content_type || '';
      /** CONFIGURATION END **/
      function loadChartbeat() {
        var e = document.createElement('script');
        var n = document.getElementsByTagName('script')[0];
        e.type = 'text/javascript';
        e.async = true;
        e.src = '//static.chartbeat.com/js/chartbeat_video.js';
        n.parentNode.insertBefore(e, n);
      }

      loadChartbeat();
    })();
  </script>
  <script>
    var facebookPixelLoaded = false;
    window.addEventListener('load', function () {
      document.addEventListener('scroll', facebookPixelScript);
      document.addEventListener('mousemove', facebookPixelScript);
    })
    function facebookPixelScript() {
      if (!facebookPixelLoaded) {
        facebookPixelLoaded = true;
        document.removeEventListener('scroll', facebookPixelScript);
        document.removeEventListener('mousemove', facebookPixelScript);
        !function (f, b, e, v, n, t, s) {
          if (f.fbq) return; n = f.fbq = function () {
            n.callMethod ?
            n.callMethod.apply(n, arguments) : n.queue.push(arguments)
          }; if (!f._fbq) f._fbq = n;
          n.push = n; n.loaded = !0; n.version = '2.0'; n.queue = []; t = b.createElement(e); t.async = !0;
          t.src = v; s = b.getElementsByTagName(e)[0]; s.parentNode.insertBefore(t, s)
        }(window,
          document, 'script', '//connect.facebook.net/en_US/fbevents.js');
        fbq('init', '1453039084979896');
        fbq('track', "PageView");
      }
    }
  </script>
  <script type="text/javascript" id="pogo" src="https://cdn.ziffstatic.com/pg/mashable.js" async=""></script>
  <link rel="preload" as="script" href="https://cdn.ziffstatic.com/pg/mashable.js">
  <link rel="preload" as="script" href="https://cdn.ziffstatic.com/pg/mashable.prebid.js">
  <link rel="preload" as="style" href="https://cdn.ziffstatic.com/pg/mashable.css" onload="this.onload=null;this.rel='stylesheet'">
  <link rel="preload" as="script" href="https://securepubads.g.doubleclick.net/tag/js/gpt.js">
  <script type="text/javascript" src="https://securepubads.g.doubleclick.net/tag/js/gpt.js" async=""></script>
  <script>
    window.PogoConfig =
      {
        "template": "article",
        "category": "entertainment",
        "tags": ["entertainment"],
      }
  </script>

  <script>
        (function (d) {
          var bounceExchangeLoaded = false;
          window.addEventListener('load', function () {
            document.addEventListener('scroll', bounceExchangeScript);
            document.addEventListener('mousemove', bounceExchangeScript);
          })
          function bounceExchangeScript() {
            if (!bounceExchangeLoaded) {
              bounceExchangeLoaded = true;
              document.removeEventListener('scroll', bounceExchangeScript);
              document.removeEventListener('mousemove', bounceExchangeScript);
              var e = d.createElement('script');
              e.src = d.location.protocol + '//tag.bounceexchange.com/3441/i.js';
              e.async = true;
              d.getElementsByTagName("head")[0].appendChild(e);
            }
          }
        }(document));
  </script>

  <script src="https://cdn.p-n.io/pushly-sdk.min.js?domain_key=TXpY3X8ls7A4Zbp78hzgCks4F8YHWBaGK9tn" async=""></script>
  <script>
    var PushlySDK = window.PushlySDK || [];
    function pushly() { PushlySDK.push(arguments) }
    pushly('load', {
      domainKey: 'TXpY3X8ls7A4Zbp78hzgCks4F8YHWBaGK9tn',
      sw: '/js/pushly-sdk-worker.js',
    });
    pushly('on_prompt_shown', function () {
      if (window.gtag) {
        gtag('event', 'Pushly_on_prompt_shown', {
          'event_category': 'impressions',
          'event_label': 'Pushly_on_prompt_shown'
        });
      }
    });
    pushly('on_permission_allowed', function () {
      if (window.gtag) {
        gtag('event', 'Pushly_on_permission_allowed', {
          'event_category': 'clicks',
          'event_label': 'Pushly_on_permission_allowed'
        });
      }
    });
  </script>
  <style>
    * .pogoPgWrap {
      transition: all .01s ease;
    }

    @font-face {
      font-display: optional;
      font-family: outfit;
      font-weight: 100 800;
      src: url(/fonts/Outfit.woff2) format("woff2")
    }
  </style>
  <script src="https://content.jwplatform.com/libraries/dloDxAmE.js" async=""></script>
  <script src="https://cdn.ziffstatic.com/mashable/streamingtag_plugin_jwplayer.js" async=""></script>
  <style>
    .flipX video::-webkit-media-text-track-display {
      transform: matrix(-1, 0, 0, 1, 0, 0) !important;
    }

    .flipXY video::-webkit-media-text-track-display {
      transform: matrix(-1, 0, 0, -1, 0, 0) !important;
    }

    .flipXYX video::-webkit-media-text-track-display {
      transform: matrix(1, 0, 0, -1, 0, 0) !important;
    }
  </style>
  <style>
    @keyframes blinkWarning {
      0% {
        color: red;
      }

      100% {
        color: white;
      }
    }

    @-webkit-keyframes blinkWarning {
      0% {
        color: red;
      }

      100% {
        color: white;
      }
    }

    .blinkWarning {
      -webkit-animation: blinkWarning 1s linear infinite;
      -moz-animation: blinkWarning 1s linear infinite;
      animation: blinkWarning 1s linear infinite;
    }
  </style>
  <script src="https://tag.bounceexchange.com/3441/i.js" async=""></script>
  <style>
    .jw-reset {
      text-align: left;
      direction: ltr
    }

    .jw-reset,
    .jw-reset-text {
      color: inherit;
      background-color: transparent;
      padding: 0;
      margin: 0;
      float: none;
      font-family: Arial, Helvetica, sans-serif;
      font-size: 1em;
      line-height: 1em;
      list-style: none;
      text-transform: none;
      vertical-align: baseline;
      border: 0;
      font-variant: inherit;
      font-stretch: inherit;
      -webkit-tap-highlight-color: rgba(255, 255, 255, 0)
    }

    body .jw-error,
    body .jwplayer.jw-state-error {
      height: 100%;
      width: 100%
    }

    .jw-title {
      position: absolute;
      top: 0
    }

    .jw-background-color {
      background: rgba(0, 0, 0, .4)
    }

    .jw-text {
      color: rgba(255, 255, 255, .8)
    }

    .jw-knob {
      color: rgba(255, 255, 255, .8);
      background-color: #fff
    }

    .jw-button-color {
      color: rgba(255, 255, 255, .8)
    }

    :not(.jw-flag-touch) .jw-button-color:not(.jw-logo-button):focus,
    :not(.jw-flag-touch) .jw-button-color:not(.jw-logo-button):hover {
      color: #fff
    }

    .jw-toggle {
      color: #fff
    }

    .jw-toggle.jw-off {
      color: rgba(255, 255, 255, .8)
    }

    .jw-toggle.jw-off:focus {
      color: #fff
    }

    .jw-toggle:focus {
      outline: 0
    }

    :not(.jw-flag-touch) .jw-toggle.jw-off:hover {
      color: #fff
    }

    .jw-rail {
      background: rgba(255, 255, 255, .3)
    }

    .jw-buffer {
      background: rgba(255, 255, 255, .3)
    }

    .jw-progress {
      background: #f2f2f2
    }

    .jw-time-tip,
    .jw-volume-tip {
      border: 0
    }

    .jw-slider-volume.jw-volume-tip.jw-background-color.jw-slider-vertical {
      background: 0 0
    }

    .jw-skip {
      padding: .5em;
      outline: 0
    }

    .jw-skip .jw-skip-icon,
    .jw-skip .jw-skiptext {
      color: rgba(255, 255, 255, .8)
    }

    .jw-skip.jw-skippable:focus .jw-skip-icon,
    .jw-skip.jw-skippable:hover .jw-skip-icon {
      color: #fff
    }

    .jw-icon-cast google-cast-launcher {
      --connected-color: #fff;
      --disconnected-color: rgba(255, 255, 255, 0.8)
    }

    .jw-icon-cast google-cast-launcher:focus {
      outline: 0
    }

    .jw-icon-cast google-cast-launcher.jw-off {
      --connected-color: rgba(255, 255, 255, 0.8)
    }

    .jw-icon-cast:focus google-cast-launcher {
      --connected-color: #fff;
      --disconnected-color: #fff
    }

    .jw-icon-cast:hover google-cast-launcher {
      --connected-color: #fff;
      --disconnected-color: #fff
    }

    .jw-nextup-container {
      bottom: 2.5em;
      padding: 5px .5em
    }

    .jw-nextup {
      border-radius: 0
    }

    .jw-color-active {
      color: #fff;
      stroke: #fff;
      border-color: #fff
    }

    :not(.jw-flag-touch) .jw-color-active-hover:focus,
    :not(.jw-flag-touch) .jw-color-active-hover:hover {
      color: #fff;
      stroke: #fff;
      border-color: #fff
    }

    .jw-color-inactive {
      color: rgba(255, 255, 255, .8);
      stroke: rgba(255, 255, 255, .8);
      border-color: rgba(255, 255, 255, .8)
    }

    :not(.jw-flag-touch) .jw-color-inactive-hover:hover {
      color: rgba(255, 255, 255, .8);
      stroke: rgba(255, 255, 255, .8);
      border-color: rgba(255, 255, 255, .8)
    }

    .jw-option {
      color: rgba(255, 255, 255, .8)
    }

    .jw-option.jw-active-option {
      color: #fff;
      background-color: rgba(255, 255, 255, .1)
    }

    :not(.jw-flag-touch) .jw-option:hover {
      color: #fff
    }

    .jwplayer {
      width: 100%;
      font-size: 16px;
      position: relative;
      display: block;
      min-height: 0;
      overflow: hidden;
      box-sizing: border-box;
      font-family: Arial, Helvetica, sans-serif;
      -webkit-touch-callout: none;
      -webkit-user-select: none;
      user-select: none;
      outline: 0
    }

    .jwplayer * {
      box-sizing: inherit
    }

    .jwplayer.jw-tab-focus:focus {
      outline: solid 2px #4d90fe
    }

    .jwplayer.jw-flag-aspect-mode {
      height: auto !important
    }

    .jwplayer.jw-flag-aspect-mode .jw-aspect {
      display: block
    }

    .jwplayer .jw-aspect {
      display: none
    }

    .jw-media,
    .jw-preview {
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      bottom: 0;
      right: 0
    }

    .jw-media {
      overflow: hidden;
      cursor: pointer
    }

    .jw-plugin {
      position: absolute;
      bottom: 66px
    }

    .jw-breakpoint-7 .jw-plugin {
      bottom: 132px
    }

    .jw-plugin .jw-banner {
      max-width: 100%;
      opacity: 0;
      cursor: pointer;
      position: absolute;
      margin: auto auto 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: block
    }

    .jw-captions,
    .jw-preview,
    .jw-title {
      pointer-events: none
    }

    .jw-logo,
    .jw-media {
      pointer-events: all
    }

    .jw-wrapper {
      background-color: #000;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0
    }

    .jw-hidden-accessibility {
      border: 0;
      clip: rect(0 0 0 0);
      height: 1px;
      margin: -1px;
      overflow: hidden;
      padding: 0;
      position: absolute;
      width: 1px
    }

    .jw-contract-trigger::before {
      content: "";
      overflow: hidden;
      width: 200%;
      height: 200%;
      display: block;
      position: absolute;
      top: 0;
      left: 0
    }

    .jwplayer .jw-media video {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 100%;
      margin: auto;
      background: 0 0;
      -webkit-tap-highlight-color: transparent;
      -webkit-focus-ring-color: transparent;
      outline: 0
    }

    .jwplayer .jw-media video::-webkit-media-controls-start-playback-button {
      display: none
    }

    .jwplayer.jw-stretch-uniform .jw-media video {
      object-fit: contain
    }

    .jwplayer.jw-stretch-none .jw-media video {
      object-fit: none
    }

    .jwplayer.jw-stretch-fill .jw-media video {
      object-fit: cover
    }

    .jwplayer.jw-stretch-exactfit .jw-media video {
      object-fit: fill
    }

    .jw-preview {
      position: absolute;
      display: none;
      opacity: 1;
      visibility: visible;
      width: 100%;
      height: 100%;
      background: #000 no-repeat 50% 50%
    }

    .jw-error .jw-preview,
    .jwplayer .jw-preview {
      background-size: contain
    }

    .jw-stretch-none .jw-preview {
      background-size: auto auto
    }

    .jw-stretch-fill .jw-preview {
      background-size: cover
    }

    .jw-stretch-exactfit .jw-preview {
      background-size: 100% 100%
    }

    .jw-title {
      display: none;
      padding-top: 20px;
      width: 100%;
      z-index: 1
    }

    .jw-title-primary,
    .jw-title-secondary {
      color: #fff;
      padding-left: 20px;
      padding-right: 20px;
      padding-bottom: .5em;
      overflow: hidden;
      text-overflow: ellipsis;
      direction: unset;
      white-space: nowrap;
      width: 100%
    }

    .jw-title-primary {
      font-size: 1.625em
    }

    .jw-breakpoint-2 .jw-title-primary,
    .jw-breakpoint-3 .jw-title-primary {
      font-size: 1.5em
    }

    .jw-flag-small-player .jw-title-primary {
      font-size: 1.25em
    }

    .jw-breakpoint-0 .jw-ab-truncated .jw-title-primary,
    .jw-breakpoint-1 .jw-ab-truncated .jw-title-primary,
    .jw-breakpoint-2 .jw-ab-truncated .jw-title-primary {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      padding-bottom: 0;
      margin-bottom: .5em;
      white-space: pre-wrap;
      line-height: 1.2
    }

    .jw-breakpoint-1 .jw-ab-truncated .jw-title-primary {
      font-size: 1.25em
    }

    .jw-breakpoint-0 .jw-ab-truncated .jw-title-primary {
      font-size: 1em
    }

    .jw-breakpoint-0 .jw-ab-truncated .jw-title-secondary,
    .jw-breakpoint-1 .jw-ab-truncated .jw-title-secondary,
    .jw-breakpoint-2 .jw-ab-truncated .jw-title-secondary,
    .jw-flag-small-player .jw-title-secondary,
    .jw-title-secondary:empty {
      display: none
    }

    .jw-captions {
      position: absolute;
      width: 100%;
      height: 100%;
      text-align: center;
      display: none;
      letter-spacing: normal;
      word-spacing: normal;
      text-transform: none;
      text-indent: 0;
      text-decoration: none;
      pointer-events: none;
      overflow: hidden;
      top: 0
    }

    .jw-captions.jw-captions-enabled {
      display: block
    }

    .jw-captions-window {
      display: none;
      padding: .25em;
      border-radius: .25em
    }

    .jw-captions-window.jw-captions-window-active {
      display: inline-block
    }

    .jw-captions-text {
      display: inline-block;
      color: #fff;
      background-color: #000;
      word-wrap: normal;
      word-break: normal;
      white-space: pre-line;
      font-style: normal;
      font-weight: 400;
      text-align: center;
      text-decoration: none
    }

    .jw-text-track-display {
      font-size: inherit;
      line-height: 1.5
    }

    .jw-text-track-cue {
      background-color: rgba(0, 0, 0, .5);
      color: #fff;
      padding: .1em .3em
    }

    .jwplayer video::-webkit-media-controls {
      display: none;
      justify-content: flex-start
    }

    .jwplayer video::-webkit-media-text-track-display {
      min-width: -webkit-min-content
    }

    .jwplayer video::cue {
      background-color: rgba(0, 0, 0, .5)
    }

    .jwplayer video::-webkit-media-controls-panel-container {
      display: none
    }

    .jwplayer.jw-flag-media-audio.jw-state-playing .jw-captions,
    .jwplayer.jw-state-playing:not(.jw-flag-user-inactive):not(.jw-flag-controls-hidden) .jw-captions,
    .jwplayer:not(.jw-flag-controls-hidden):not(.jw-state-playing) .jw-captions {
      max-height: calc(100% - 60px)
    }

    .jwplayer.jw-flag-media-audio.jw-state-playing:not(.jw-flag-ios-fullscreen) video::-webkit-media-text-track-container,
    .jwplayer.jw-state-playing:not(.jw-flag-user-inactive):not(.jw-flag-controls-hidden):not(.jw-flag-ios-fullscreen) video::-webkit-media-text-track-container,
    .jwplayer:not(.jw-flag-controls-hidden):not(.jw-state-playing):not(.jw-flag-ios-fullscreen) video::-webkit-media-text-track-container {
      max-height: calc(100% - 60px)
    }

    .jw-logo {
      position: absolute;
      margin: 20px;
      cursor: pointer;
      pointer-events: all;
      background-repeat: no-repeat;
      background-size: contain;
      top: auto;
      right: auto;
      left: auto;
      bottom: auto;
      outline: 0
    }

    .jw-logo.jw-tab-focus:focus {
      outline: solid 2px #4d90fe
    }

    .jw-flag-audio-player .jw-logo {
      display: none
    }

    .jw-logo-top-right {
      top: 0;
      right: 0
    }

    .jw-logo-top-left {
      top: 0;
      left: 0
    }

    .jw-logo-bottom-left {
      left: 0
    }

    .jw-logo-bottom-right {
      right: 0
    }

    .jw-logo-bottom-left,
    .jw-logo-bottom-right {
      bottom: 44px;
      transition: bottom 150ms cubic-bezier(0, .25, .25, 1)
    }

    .jw-state-idle .jw-logo {
      z-index: 1
    }

    .jw-state-setup .jw-wrapper {
      background-color: inherit
    }

    .jw-state-setup .jw-controls,
    .jw-state-setup .jw-controls-backdrop,
    .jw-state-setup .jw-logo {
      visibility: hidden
    }

    span.jw-break {
      display: block
    }

    body .jw-error,
    body .jwplayer.jw-state-error {
      background-color: #333;
      color: #fff;
      font-size: 16px;
      display: table;
      opacity: 1;
      position: relative
    }

    body .jw-error .jw-display,
    body .jwplayer.jw-state-error .jw-display {
      display: none
    }

    body .jw-error .jw-media,
    body .jwplayer.jw-state-error .jw-media {
      cursor: default
    }

    body .jw-error .jw-preview,
    body .jwplayer.jw-state-error .jw-preview {
      background-color: #333
    }

    body .jw-error .jw-error-msg,
    body .jwplayer.jw-state-error .jw-error-msg {
      background-color: #000;
      border-radius: 2px;
      display: flex;
      flex-direction: row;
      align-items: stretch;
      padding: 20px
    }

    body .jw-error .jw-error-msg .jw-icon,
    body .jwplayer.jw-state-error .jw-error-msg .jw-icon {
      height: 30px;
      width: 30px;
      margin-right: 20px;
      flex: 0 0 auto;
      align-self: center
    }

    body .jw-error .jw-error-msg .jw-icon:empty,
    body .jwplayer.jw-state-error .jw-error-msg .jw-icon:empty {
      display: none
    }

    body .jw-error .jw-error-msg .jw-info-container,
    body .jwplayer.jw-state-error .jw-error-msg .jw-info-container {
      margin: 0;
      padding: 0
    }

    body .jw-error:not(.jw-flag-audio-player).jw-breakpoint-2 .jw-error-msg,
    body .jw-error:not(.jw-flag-audio-player).jw-flag-small-player .jw-error-msg,
    body .jwplayer.jw-state-error:not(.jw-flag-audio-player).jw-breakpoint-2 .jw-error-msg,
    body .jwplayer.jw-state-error:not(.jw-flag-audio-player).jw-flag-small-player .jw-error-msg {
      flex-direction: column
    }

    body .jw-error:not(.jw-flag-audio-player).jw-breakpoint-2 .jw-error-msg .jw-error-text,
    body .jw-error:not(.jw-flag-audio-player).jw-flag-small-player .jw-error-msg .jw-error-text,
    body .jwplayer.jw-state-error:not(.jw-flag-audio-player).jw-breakpoint-2 .jw-error-msg .jw-error-text,
    body .jwplayer.jw-state-error:not(.jw-flag-audio-player).jw-flag-small-player .jw-error-msg .jw-error-text {
      text-align: center
    }

    body .jw-error:not(.jw-flag-audio-player).jw-breakpoint-2 .jw-error-msg .jw-icon,
    body .jw-error:not(.jw-flag-audio-player).jw-flag-small-player .jw-error-msg .jw-icon,
    body .jwplayer.jw-state-error:not(.jw-flag-audio-player).jw-breakpoint-2 .jw-error-msg .jw-icon,
    body .jwplayer.jw-state-error:not(.jw-flag-audio-player).jw-flag-small-player .jw-error-msg .jw-icon {
      flex: .5 0 auto;
      margin-right: 0;
      margin-bottom: 20px
    }

    .jwplayer.jw-state-error.jw-breakpoint-2 .jw-error-msg .jw-break,
    .jwplayer.jw-state-error.jw-flag-audio-player .jw-error-msg .jw-break,
    .jwplayer.jw-state-error.jw-flag-small-player .jw-error-msg .jw-break {
      display: inline
    }

    .jwplayer.jw-state-error.jw-breakpoint-2 .jw-error-msg .jw-break:before,
    .jwplayer.jw-state-error.jw-flag-audio-player .jw-error-msg .jw-break:before,
    .jwplayer.jw-state-error.jw-flag-small-player .jw-error-msg .jw-break:before {
      content: " "
    }

    .jwplayer.jw-state-error.jw-flag-audio-player .jw-error-msg {
      height: 100%;
      width: 100%;
      top: 0;
      position: absolute;
      left: 0;
      background: #000;
      transform: none;
      padding: 4px 16px;
      z-index: 1
    }

    .jwplayer.jw-state-error.jw-flag-audio-player .jw-error-msg.jw-info-overlay {
      max-width: none;
      max-height: none
    }

    .jw-state-idle .jw-title,
    .jwplayer.jw-state-complete:not(.jw-flag-casting):not(.jw-flag-audio-player):not(.jw-flag-overlay-open-related) .jw-title,
    body .jwplayer.jw-state-error .jw-title {
      display: block
    }

    .jw-state-idle .jw-preview,
    .jwplayer.jw-state-complete:not(.jw-flag-casting):not(.jw-flag-audio-player):not(.jw-flag-overlay-open-related) .jw-preview,
    body .jwplayer.jw-state-error .jw-preview {
      display: block
    }

    .jw-state-idle .jw-captions,
    .jwplayer.jw-state-complete .jw-captions,
    body .jwplayer.jw-state-error .jw-captions {
      display: none
    }

    .jw-state-idle video::-webkit-media-text-track-container,
    .jwplayer.jw-state-complete video::-webkit-media-text-track-container,
    body .jwplayer.jw-state-error video::-webkit-media-text-track-container {
      display: none
    }

    .jwplayer.jw-flag-fullscreen {
      width: 100% !important;
      height: 100% !important;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      z-index: 1000;
      margin: 0;
      position: fixed
    }

    .jwplayer.jw-flag-controls-hidden .jw-media {
      cursor: default
    }

    .jw-flag-audio-player .jw-media {
      visibility: hidden
    }

    .jw-flag-audio-player .jw-title {
      background: 0 0
    }

    .jw-flag-floating {
      background-size: cover;
      background-color: #000
    }

    .jw-flag-floating.jw-floating-dismissible .jw-wrapper {
      bottom: 3rem
    }

    .jw-flag-floating .jw-wrapper {
      position: fixed;
      z-index: 2147483647;
      top: auto;
      bottom: 1rem;
      left: auto;
      right: 1rem;
      max-width: 400px;
      max-height: 400px;
      margin: 0 auto
    }

    @media screen and (min-width:481px) {
      .jw-flag-floating .jw-wrapper:not(.jw-floating-dragged) {
        animation: jw-float-to-bottom 150ms cubic-bezier(0, .25, .25, 1) forwards 1
      }
    }

    @media screen and (max-width:480px) {
      .jw-flag-floating .jw-wrapper {
        width: 100%;
        left: 0;
        right: 0
      }
    }

    .jw-flag-floating .jw-wrapper.jw-float-to-top {
      animation: jw-float-to-top-anim 150ms cubic-bezier(0, .25, .25, 1) forwards 1;
      bottom: auto;
      top: 1rem
    }

    .jw-flag-floating .jw-wrapper.jw-floating-dragging {
      transition: none !important
    }

    .jw-flag-floating .jw-wrapper .jw-media {
      touch-action: none
    }

    .jw-flag-floating .jw-icon {
      margin: 0;
      padding: 0
    }

    .jw-flag-floating .jw-float-bar-icon {
      pointer-events: all;
      cursor: pointer;
      display: flex;
      height: 24px;
      width: 24px
    }

    .jw-flag-floating .jw-float-bar-icon:hover {
      background: #1d1d1d;
      border-radius: 100px
    }

    .jw-flag-floating .jw-float-bar-icon .jw-svg-icon {
      display: block;
      margin: auto;
      padding: 0;
      height: 10px;
      width: 10px
    }

    .jw-flag-floating.jw-floating-dismissible .jw-dismiss-icon {
      display: none
    }

    .jw-flag-floating.jw-floating-dismissible .jw-float-bar {
      display: inline-flex;
      flex-direction: row;
      align-items: center;
      position: fixed;
      z-index: 2147483647;
      top: 100%;
      height: 32px;
      width: 100%;
      max-height: 32px;
      margin: 0 auto
    }

    @media screen and (min-width:481px) {
      .jw-flag-floating.jw-floating-dismissible .jw-float-bar:not(.jw-floating-dragged) {
        animation: jw-float-to-bottom 150ms cubic-bezier(0, .25, .25, 1) forwards 1
      }
    }

    @media screen and (max-width:480px) {
      .jw-flag-floating.jw-floating-dismissible .jw-float-bar {
        left: 0;
        right: 0;
        top: auto
      }
    }

    .jw-flag-floating.jw-floating-dismissible.jw-state-paused .jw-logo,
    .jw-flag-floating.jw-floating-dismissible:not(.jw-flag-user-inactive) .jw-logo {
      display: none
    }

    .jw-float-bar {
      display: none;
      position: absolute;
      padding: 4px 8px;
      background: #2f2d2d;
      touch-action: none
    }

    .jw-float-bar-title {
      width: 100%;
      max-height: 24px;
      font-family: Arial, sans-serif;
      font-style: normal;
      font-weight: 700;
      font-size: 13px;
      line-height: 16px;
      margin-left: 0;
      padding-right: 22px;
      color: rgba(255, 255, 255, .8) !important;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap
    }

    .jw-flag-pip.jw-flag-floating .jw-wrapper {
      visibility: hidden
    }

    @keyframes jw-float-to-bottom {
      from {
        transform: translateY(100%)
      }

      to {
        transform: translateY(0)
      }
    }

    @keyframes jw-float-to-top {
      from {
        transform: translateY(0)
      }

      to {
        transform: translateY(100%)
      }
    }

    .jw-flag-top {
      margin-top: 2em;
      overflow: visible
    }

    .jw-top {
      height: 2em;
      line-height: 2;
      pointer-events: none;
      text-align: center;
      opacity: .8;
      position: absolute;
      top: -2em;
      width: 100%
    }

    .jw-top .jw-icon {
      cursor: pointer;
      pointer-events: all;
      height: auto;
      width: auto
    }

    .jw-top .jw-text {
      color: #555
    }
  </style>
  <style>
    .jw-controls,
    .jw-controls-backdrop,
    .jw-flag-small-player .jw-settings-menu,
    .jw-overlays,
    .jw-settings-submenu {
      height: 100%;
      width: 100%
    }

    .jw-icon-settings::after,
    .jw-icon-volume::after,
    .jw-settings-menu .jw-icon.jw-button-color::after,
    .jw-settings-menu .jw-icon::after {
      position: absolute;
      right: 0
    }

    .jw-controls,
    .jw-controls-backdrop,
    .jw-overlays,
    .jw-settings-item-active::before {
      top: 0;
      position: absolute;
      left: 0
    }

    .jw-icon-settings::after,
    .jw-icon-volume::after,
    .jw-settings-menu .jw-icon.jw-button-color::after,
    .jw-settings-menu .jw-icon::after {
      position: absolute;
      bottom: 0;
      left: 0
    }

    .jw-nextup-close {
      position: absolute;
      top: 0;
      right: 0
    }

    .jw-controls,
    .jw-flag-small-player .jw-settings-menu,
    .jw-overlays {
      position: absolute;
      bottom: 0;
      right: 0
    }

    .jw-controlbar .jw-tooltip::after,
    .jw-icon-settings::after,
    .jw-icon-volume::after,
    .jw-settings-menu .jw-icon.jw-button-color::after,
    .jw-settings-menu .jw-icon::after,
    .jw-settings-menu .jw-tooltip::after,
    .jw-text-live::before,
    .jw-time-tip::after {
      content: "";
      display: block
    }

    .jw-svg-icon {
      height: 24px;
      width: 24px;
      fill: currentColor;
      pointer-events: none
    }

    .jw-icon {
      height: 44px;
      width: 44px;
      background-color: transparent;
      outline: 0
    }

    .jw-icon.jw-tab-focus:focus {
      border: solid 2px #4d90fe
    }

    .jw-icon-airplay .jw-svg-icon-airplay-off {
      display: none
    }

    .jw-off.jw-icon-airplay .jw-svg-icon-airplay-off {
      display: block
    }

    .jw-icon-airplay .jw-svg-icon-airplay-on {
      display: block
    }

    .jw-off.jw-icon-airplay .jw-svg-icon-airplay-on {
      display: none
    }

    .jw-icon-cc .jw-svg-icon-cc-off {
      display: none
    }

    .jw-off.jw-icon-cc .jw-svg-icon-cc-off {
      display: block
    }

    .jw-icon-cc .jw-svg-icon-cc-on {
      display: block
    }

    .jw-off.jw-icon-cc .jw-svg-icon-cc-on {
      display: none
    }

    .jw-icon-fullscreen .jw-svg-icon-fullscreen-off {
      display: none
    }

    .jw-off.jw-icon-fullscreen .jw-svg-icon-fullscreen-off {
      display: block
    }

    .jw-icon-fullscreen .jw-svg-icon-fullscreen-on {
      display: block
    }

    .jw-off.jw-icon-fullscreen .jw-svg-icon-fullscreen-on {
      display: none
    }

    .jw-icon-pip .jw-svg-icon-pip-off {
      display: none
    }

    .jw-off.jw-icon-pip .jw-svg-icon-pip-off {
      display: block
    }

    .jw-icon-pip .jw-svg-icon-pip-on {
      display: block
    }

    .jw-off.jw-icon-pip .jw-svg-icon-pip-on {
      display: none
    }

    .jw-icon-volume .jw-svg-icon-volume-0 {
      display: none
    }

    .jw-off.jw-icon-volume .jw-svg-icon-volume-0 {
      display: block
    }

    .jw-icon-volume .jw-svg-icon-volume-100 {
      display: none
    }

    .jw-full.jw-icon-volume .jw-svg-icon-volume-100 {
      display: block
    }

    .jw-icon-volume .jw-svg-icon-volume-50 {
      display: block
    }

    .jw-full.jw-icon-volume .jw-svg-icon-volume-50,
    .jw-off.jw-icon-volume .jw-svg-icon-volume-50 {
      display: none
    }

    .jw-icon-settings::after,
    .jw-icon-volume::after,
    .jw-settings-menu .jw-icon::after {
      height: 100%;
      width: 24px;
      box-shadow: inset 0 -3px 0 -1px currentColor;
      margin: auto;
      opacity: 0;
      transition: opacity 150ms cubic-bezier(0, .25, .25, 1)
    }

    .jw-icon-volume.jw-open::after,
    .jw-settings-menu .jw-icon[aria-checked=true]::after,
    .jw-settings-open .jw-icon-settings::after {
      opacity: 1
    }

    .jwplayer.jw-breakpoint--1:not(.jw-flag-audio-player) .jw-icon-audio-tracks,
    .jwplayer.jw-breakpoint--1:not(.jw-flag-audio-player) .jw-icon-cc,
    .jwplayer.jw-breakpoint--1:not(.jw-flag-audio-player) .jw-icon-fullscreen,
    .jwplayer.jw-breakpoint--1:not(.jw-flag-audio-player) .jw-icon-hd,
    .jwplayer.jw-breakpoint--1:not(.jw-flag-audio-player) .jw-icon-settings,
    .jwplayer.jw-breakpoint--1:not(.jw-flag-audio-player) .jw-settings-sharing,
    .jwplayer.jw-breakpoint--1:not(.jw-flag-audio-player).jw-flag-cast-available .jw-icon-airplay,
    .jwplayer.jw-breakpoint--1:not(.jw-flag-audio-player).jw-flag-cast-available .jw-icon-cast {
      display: none
    }

    .jwplayer.jw-breakpoint--1:not(.jw-flag-audio-player) .jw-icon-volume,
    .jwplayer.jw-breakpoint--1:not(.jw-flag-audio-player) .jw-text-live {
      bottom: 6px
    }

    .jwplayer.jw-breakpoint--1:not(.jw-flag-audio-player) .jw-icon-volume::after {
      display: none
    }

    .jw-controls,
    .jw-overlays {
      pointer-events: none
    }

    .jw-controls-backdrop {
      display: block;
      background: linear-gradient(to bottom, transparent, rgba(0, 0, 0, .4) 77%, rgba(0, 0, 0, .4) 100%) 100% 100%/100% 240px no-repeat transparent;
      transition: opacity 250ms cubic-bezier(0, .25, .25, 1), background-size 250ms cubic-bezier(0, .25, .25, 1);
      pointer-events: none
    }

    .jw-overlays {
      cursor: auto
    }

    .jw-controls {
      overflow: hidden
    }

    .jw-flag-small-player .jw-controls {
      text-align: center
    }

    .jw-text {
      height: 1em;
      font-family: Arial, Helvetica, sans-serif;
      font-size: .75em;
      font-style: normal;
      font-weight: 400;
      color: #fff;
      text-align: center;
      font-variant: normal;
      font-stretch: normal
    }

    .jw-autostart-mute,
    .jw-controlbar,
    .jw-display-icon-container .jw-icon,
    .jw-nextup-container,
    .jw-overlays .jw-plugin,
    .jw-skip {
      pointer-events: all
    }

    .jw-error .jw-display-icon-container,
    .jwplayer .jw-display-icon-container {
      width: auto;
      height: auto;
      box-sizing: content-box
    }

    .jw-display {
      display: flex;
      flex-direction: column;
      justify-content: center;
      height: 100%;
      padding: 57px 0;
      position: relative;
      width: 100%
    }

    .jw-flag-dragging .jw-display {
      display: none
    }

    .jw-state-idle:not(.jw-flag-cast-available) .jw-display {
      padding: 0
    }

    .jw-display-container {
      text-align: center
    }

    .jw-display-controls {
      display: inline-block
    }

    .jwplayer .jw-display-icon-container {
      float: left
    }

    .jw-display-icon-container {
      display: inline-block;
      padding: 5.5px;
      margin: 0 22px
    }

    .jw-display-icon-container .jw-icon {
      height: 75px;
      width: 75px;
      cursor: pointer;
      display: flex;
      justify-content: center;
      align-items: center
    }

    .jw-display-icon-container .jw-icon .jw-svg-icon {
      height: 33px;
      width: 33px;
      padding: 0;
      position: relative
    }

    .jw-display-icon-container .jw-icon .jw-svg-icon-rewind {
      padding: .2em .05em
    }

    .jw-breakpoint--1 .jw-nextup-container {
      display: none
    }

    .jw-breakpoint--1 .jw-display-icon-next,
    .jw-breakpoint--1 .jw-display-icon-rewind,
    .jw-breakpoint-0 .jw-display-icon-next,
    .jw-breakpoint-0 .jw-display-icon-rewind {
      display: none
    }

    .jw-breakpoint--1.jw-flag-touch .jw-display .jw-icon,
    .jw-breakpoint--1.jw-flag-touch .jw-display .jw-svg-icon,
    .jw-breakpoint-0.jw-flag-touch .jw-display .jw-icon,
    .jw-breakpoint-0.jw-flag-touch .jw-display .jw-svg-icon {
      z-index: 100;
      position: relative
    }

    .jw-breakpoint--1 .jw-display .jw-icon,
    .jw-breakpoint--1 .jw-display .jw-svg-icon,
    .jw-breakpoint-0 .jw-display .jw-icon,
    .jw-breakpoint-0 .jw-display .jw-svg-icon {
      width: 44px;
      height: 44px;
      line-height: 44px
    }

    .jw-breakpoint--1 .jw-display .jw-icon:before,
    .jw-breakpoint--1 .jw-display .jw-svg-icon:before,
    .jw-breakpoint-0 .jw-display .jw-icon:before,
    .jw-breakpoint-0 .jw-display .jw-svg-icon:before {
      width: 22px;
      height: 22px
    }

    .jw-breakpoint-1 .jw-display .jw-icon,
    .jw-breakpoint-1 .jw-display .jw-svg-icon {
      width: 44px;
      height: 44px;
      line-height: 44px
    }

    .jw-breakpoint-1 .jw-display .jw-icon:before,
    .jw-breakpoint-1 .jw-display .jw-svg-icon:before {
      width: 22px;
      height: 22px
    }

    .jw-breakpoint-1 .jw-display .jw-icon.jw-icon-rewind:before {
      width: 33px;
      height: 33px
    }

    .jw-breakpoint-2 .jw-display .jw-icon,
    .jw-breakpoint-2 .jw-display .jw-svg-icon,
    .jw-breakpoint-3 .jw-display .jw-icon,
    .jw-breakpoint-3 .jw-display .jw-svg-icon {
      width: 77px;
      height: 77px;
      line-height: 77px
    }

    .jw-breakpoint-2 .jw-display .jw-icon:before,
    .jw-breakpoint-2 .jw-display .jw-svg-icon:before,
    .jw-breakpoint-3 .jw-display .jw-icon:before,
    .jw-breakpoint-3 .jw-display .jw-svg-icon:before {
      width: 38.5px;
      height: 38.5px
    }

    .jw-breakpoint-4 .jw-display .jw-icon,
    .jw-breakpoint-4 .jw-display .jw-svg-icon,
    .jw-breakpoint-5 .jw-display .jw-icon,
    .jw-breakpoint-5 .jw-display .jw-svg-icon,
    .jw-breakpoint-6 .jw-display .jw-icon,
    .jw-breakpoint-6 .jw-display .jw-svg-icon,
    .jw-breakpoint-7 .jw-display .jw-icon,
    .jw-breakpoint-7 .jw-display .jw-svg-icon {
      width: 88px;
      height: 88px;
      line-height: 88px
    }

    .jw-breakpoint-4 .jw-display .jw-icon:before,
    .jw-breakpoint-4 .jw-display .jw-svg-icon:before,
    .jw-breakpoint-5 .jw-display .jw-icon:before,
    .jw-breakpoint-5 .jw-display .jw-svg-icon:before,
    .jw-breakpoint-6 .jw-display .jw-icon:before,
    .jw-breakpoint-6 .jw-display .jw-svg-icon:before,
    .jw-breakpoint-7 .jw-display .jw-icon:before,
    .jw-breakpoint-7 .jw-display .jw-svg-icon:before {
      width: 44px;
      height: 44px
    }

    .jw-controlbar {
      display: flex;
      flex-flow: row wrap;
      align-items: center;
      justify-content: center;
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      border: none;
      border-radius: 0;
      background-size: auto;
      box-shadow: none;
      max-height: 72px;
      transition: 250ms cubic-bezier(0, .25, .25, 1);
      transition-property: opacity, visibility;
      transition-delay: 0s
    }

    .jw-flag-touch.jw-breakpoint-0 .jw-controlbar .jw-icon-inline {
      height: 40px
    }

    .jw-breakpoint-7:not(.jw-flag-audio-player) .jw-controlbar {
      max-height: 140px
    }

    .jw-breakpoint-7:not(.jw-flag-audio-player) .jw-controlbar .jw-button-container {
      padding: 0 48px 20px
    }

    .jw-breakpoint-7:not(.jw-flag-audio-player) .jw-controlbar .jw-button-container .jw-tooltip {
      margin-bottom: -7px
    }

    .jw-breakpoint-7:not(.jw-flag-audio-player) .jw-controlbar .jw-button-container .jw-icon-volume .jw-overlay {
      padding-bottom: 40%
    }

    .jw-breakpoint-7:not(.jw-flag-audio-player) .jw-controlbar .jw-button-container .jw-text {
      font-size: 1em
    }

    .jw-breakpoint-7:not(.jw-flag-audio-player) .jw-controlbar .jw-button-container .jw-text.jw-text-elapsed {
      justify-content: flex-end
    }

    .jw-breakpoint-7:not(.jw-flag-audio-player) .jw-controlbar .jw-button-container .jw-icon-inline:not(.jw-text-live),
    .jw-breakpoint-7:not(.jw-flag-audio-player) .jw-controlbar .jw-button-container .jw-icon-volume {
      height: 60px;
      width: 60px
    }

    .jw-breakpoint-7:not(.jw-flag-audio-player) .jw-controlbar .jw-button-container .jw-icon-inline:not(.jw-text-live) .jw-svg-icon,
    .jw-breakpoint-7:not(.jw-flag-audio-player) .jw-controlbar .jw-button-container .jw-icon-volume .jw-svg-icon {
      height: 30px;
      width: 30px
    }

    .jw-breakpoint-7:not(.jw-flag-audio-player) .jw-controlbar .jw-slider-time {
      padding: 0 60px;
      height: 34px
    }

    .jw-breakpoint-7:not(.jw-flag-audio-player) .jw-controlbar .jw-slider-time .jw-slider-container {
      height: 10px
    }

    .jw-controlbar .jw-button-image {
      background: no-repeat 50% 50%;
      background-size: contain;
      max-height: 24px
    }

    .jw-controlbar .jw-spacer {
      margin: 0 auto
    }

    .jw-controlbar .jw-icon.jw-button-color:hover {
      color: #fff
    }

    .jw-button-container {
      display: flex;
      flex-flow: row nowrap;
      flex: 1 1 auto;
      align-items: center;
      justify-content: flex-start;
      width: 100%;
      padding: 0 12px
    }

    .jw-slider-horizontal {
      background-color: transparent
    }

    .jw-icon-inline {
      position: relative
    }

    .jw-icon-inline,
    .jw-icon-tooltip {
      height: 44px;
      width: 44px;
      align-items: center;
      display: flex;
      justify-content: center
    }

    .jw-icon-inline:not(.jw-text),
    .jw-icon-tooltip,
    .jw-slider-horizontal {
      cursor: pointer
    }

    .jw-text-duration,
    .jw-text-elapsed {
      justify-content: flex-start;
      width: -moz-fit-content;
      width: fit-content
    }

    .jw-icon-tooltip {
      position: relative
    }

    .jw-icon-display:hover,
    .jw-icon-inline:hover,
    .jw-icon-tooltip:hover,
    .jw-knob:hover,
    .jw-option:before:hover {
      color: #fff
    }

    .jw-controlbar .jw-tooltip,
    .jw-settings-menu .jw-tooltip,
    .jw-time-tip {
      pointer-events: none
    }

    .jw-icon-cast {
      display: none;
      margin: 0;
      padding: 0
    }

    .jw-icon-cast google-cast-launcher {
      background-color: transparent;
      border: none;
      padding: 0;
      width: 24px;
      height: 24px;
      cursor: pointer
    }

    .jw-fullscreen-ima {
      display: none
    }

    .jw-icon-inline.jw-icon-volume {
      display: none
    }

    .jwplayer .jw-text-countdown {
      display: none
    }

    .jw-flag-small-player .jw-display {
      padding-top: 0;
      padding-bottom: 0
    }

    .jw-flag-small-player:not(.jw-flag-audio-player):not(.jw-flag-ads) .jw-controlbar .jw-button-container>.jw-icon-next,
    .jw-flag-small-player:not(.jw-flag-audio-player):not(.jw-flag-ads) .jw-controlbar .jw-button-container>.jw-icon-playback,
    .jw-flag-small-player:not(.jw-flag-audio-player):not(.jw-flag-ads) .jw-controlbar .jw-button-container>.jw-icon-rewind {
      display: none
    }

    .jw-flag-ads-vpaid:not(.jw-flag-media-audio):not(.jw-flag-audio-player):not(.jw-flag-ads-vpaid-controls):not(.jw-flag-casting) .jw-controlbar,
    .jw-flag-user-inactive.jw-state-buffering:not(.jw-flag-media-audio):not(.jw-flag-audio-player):not(.jw-flag-ads-vpaid-controls):not(.jw-flag-casting) .jw-controlbar,
    .jw-flag-user-inactive.jw-state-playing:not(.jw-flag-media-audio):not(.jw-flag-audio-player):not(.jw-flag-ads-vpaid-controls):not(.jw-flag-casting) .jw-controlbar {
      visibility: hidden;
      pointer-events: none;
      opacity: 0;
      transition-delay: 0s, 250ms
    }

    .jw-flag-ads-vpaid:not(.jw-flag-media-audio):not(.jw-flag-audio-player):not(.jw-flag-ads-vpaid-controls):not(.jw-flag-casting) .jw-controls-backdrop,
    .jw-flag-user-inactive.jw-state-buffering:not(.jw-flag-media-audio):not(.jw-flag-audio-player):not(.jw-flag-ads-vpaid-controls):not(.jw-flag-casting) .jw-controls-backdrop,
    .jw-flag-user-inactive.jw-state-playing:not(.jw-flag-media-audio):not(.jw-flag-audio-player):not(.jw-flag-ads-vpaid-controls):not(.jw-flag-casting) .jw-controls-backdrop {
      opacity: 0
    }

    .jwplayer:not(.jw-flag-ads):not(.jw-flag-live).jw-breakpoint-0 .jw-text-countdown {
      display: flex
    }

    .jwplayer:not(.jw-flag-ads):not(.jw-flag-live).jw-breakpoint--1 .jw-text-duration,
    .jwplayer:not(.jw-flag-ads):not(.jw-flag-live).jw-breakpoint--1 .jw-text-elapsed,
    .jwplayer:not(.jw-flag-ads):not(.jw-flag-live).jw-breakpoint-0 .jw-text-duration,
    .jwplayer:not(.jw-flag-ads):not(.jw-flag-live).jw-breakpoint-0 .jw-text-elapsed {
      display: none
    }

    .jwplayer.jw-breakpoint--1:not(.jw-flag-ads):not(.jw-flag-audio-player) .jw-related-btn,
    .jwplayer.jw-breakpoint--1:not(.jw-flag-ads):not(.jw-flag-audio-player) .jw-slider-volume,
    .jwplayer.jw-breakpoint--1:not(.jw-flag-ads):not(.jw-flag-audio-player) .jw-text-countdown {
      display: none
    }

    .jwplayer.jw-breakpoint--1:not(.jw-flag-ads):not(.jw-flag-audio-player) .jw-controlbar {
      flex-direction: column-reverse
    }

    .jwplayer.jw-breakpoint--1:not(.jw-flag-ads):not(.jw-flag-audio-player) .jw-button-container {
      height: 30px
    }

    .jw-breakpoint--1.jw-flag-ads:not(.jw-flag-audio-player) .jw-icon-fullscreen,
    .jw-breakpoint--1.jw-flag-ads:not(.jw-flag-audio-player) .jw-icon-volume {
      display: none
    }

    .jwplayer:not(.jw-breakpoint--1) .jw-text-duration:before,
    .jwplayer:not(.jw-breakpoint-0) .jw-text-duration:before {
      content: "/";
      padding-right: 1ch;
      padding-left: 1ch
    }

    .jwplayer:not(.jw-flag-user-inactive) .jw-controlbar {
      will-change: transform
    }

    .jwplayer:not(.jw-flag-user-inactive) .jw-controlbar .jw-text {
      transform-style: preserve-3d
    }

    .jwplayer:not(.jw-flag-fullscreen) .jw-fullscreen-disallowed {
      display: none
    }

    .jw-slider-container {
      display: flex;
      align-items: center;
      position: relative;
      touch-action: none
    }

    .jw-buffer,
    .jw-progress,
    .jw-rail {
      position: absolute;
      cursor: pointer
    }

    .jw-progress {
      background-color: #f2f2f2
    }

    .jw-rail {
      background-color: rgba(255, 255, 255, .3)
    }

    .jw-buffer {
      background-color: rgba(255, 255, 255, .3)
    }

    .jw-knob {
      height: 13px;
      width: 13px;
      background-color: #fff;
      border-radius: 50%;
      box-shadow: 0 0 10px rgba(0, 0, 0, .4);
      opacity: 1;
      pointer-events: none;
      position: absolute;
      transform: translate(-50%, -50%) scale(0);
      transition: 150ms cubic-bezier(0, .25, .25, 1);
      transition-property: opacity, transform
    }

    .jw-flag-dragging .jw-slider-time .jw-knob,
    .jw-icon-volume:active .jw-slider-volume .jw-knob {
      box-shadow: 0 0 26px rgba(0, 0, 0, .2), 0 0 10px rgba(0, 0, 0, .4), 0 0 0 6px rgba(255, 255, 255, .2)
    }

    .jw-slider-horizontal,
    .jw-slider-vertical {
      display: flex
    }

    .jw-slider-horizontal .jw-slider-container {
      height: 5px;
      width: 100%
    }

    .jw-slider-horizontal .jw-buffer,
    .jw-slider-horizontal .jw-cue,
    .jw-slider-horizontal .jw-knob,
    .jw-slider-horizontal .jw-progress,
    .jw-slider-horizontal .jw-rail {
      top: 50%
    }

    .jw-slider-horizontal .jw-buffer,
    .jw-slider-horizontal .jw-cue,
    .jw-slider-horizontal .jw-progress,
    .jw-slider-horizontal .jw-rail {
      transform: translate(0, -50%)
    }

    .jw-slider-horizontal .jw-buffer,
    .jw-slider-horizontal .jw-progress,
    .jw-slider-horizontal .jw-rail {
      height: 5px
    }

    .jw-slider-horizontal .jw-rail {
      width: 100%
    }

    .jw-slider-vertical {
      align-items: center;
      flex-direction: column
    }

    .jw-slider-vertical .jw-slider-container {
      height: 88px;
      width: 5px
    }

    .jw-slider-vertical .jw-buffer,
    .jw-slider-vertical .jw-knob,
    .jw-slider-vertical .jw-progress,
    .jw-slider-vertical .jw-rail {
      left: 50%
    }

    .jw-slider-vertical .jw-buffer,
    .jw-slider-vertical .jw-progress,
    .jw-slider-vertical .jw-rail {
      height: 100%;
      width: 5px;
      -webkit-backface-visibility: hidden;
      backface-visibility: hidden;
      transform: translate(-50%, 0);
      transition: transform 150ms ease-in-out;
      bottom: 0
    }

    .jw-slider-vertical .jw-knob {
      transform: translate(-50%, 50%)
    }

    .jw-slider-time.jw-tab-focus:focus .jw-rail {
      outline: solid 2px #4d90fe
    }

    .jw-horizontal-volume-container .jw-slider-volume,
    .jw-slider-time:not(.jw-chapter-slider-time) {
      height: 17px;
      width: 100%;
      align-items: center;
      background: transparent none;
      padding: 0 12px
    }

    .jw-slider-time .jw-cue {
      background-color: rgba(33, 33, 33, .8);
      cursor: pointer;
      position: absolute;
      width: 6px
    }

    .jw-horizontal-volume-container,
    .jw-slider-time:not(.jw-chapter-slider-time) {
      z-index: 1;
      outline: 0
    }

    .jw-horizontal-volume-container .jw-buffer,
    .jw-horizontal-volume-container .jw-cue,
    .jw-horizontal-volume-container .jw-progress,
    .jw-horizontal-volume-container .jw-rail,
    .jw-slider-time:not(.jw-chapter-slider-time) .jw-buffer,
    .jw-slider-time:not(.jw-chapter-slider-time) .jw-cue,
    .jw-slider-time:not(.jw-chapter-slider-time) .jw-progress,
    .jw-slider-time:not(.jw-chapter-slider-time) .jw-rail {
      -webkit-backface-visibility: hidden;
      backface-visibility: hidden;
      height: 100%;
      transform: translate(0, -50%) scale(1, .6);
      transition: transform 150ms ease-in-out
    }

    .jw-flag-dragging .jw-horizontal-volume-container .jw-buffer,
    .jw-flag-dragging .jw-horizontal-volume-container .jw-cue,
    .jw-flag-dragging .jw-horizontal-volume-container .jw-progress,
    .jw-flag-dragging .jw-horizontal-volume-container .jw-rail,
    .jw-flag-dragging .jw-slider-time:not(.jw-chapter-slider-time) .jw-buffer,
    .jw-flag-dragging .jw-slider-time:not(.jw-chapter-slider-time) .jw-cue,
    .jw-flag-dragging .jw-slider-time:not(.jw-chapter-slider-time) .jw-progress,
    .jw-flag-dragging .jw-slider-time:not(.jw-chapter-slider-time) .jw-rail,
    .jw-flag-touch .jw-horizontal-volume-container .jw-buffer,
    .jw-flag-touch .jw-horizontal-volume-container .jw-cue,
    .jw-flag-touch .jw-horizontal-volume-container .jw-progress,
    .jw-flag-touch .jw-horizontal-volume-container .jw-rail,
    .jw-flag-touch .jw-slider-time:not(.jw-chapter-slider-time) .jw-buffer,
    .jw-flag-touch .jw-slider-time:not(.jw-chapter-slider-time) .jw-cue,
    .jw-flag-touch .jw-slider-time:not(.jw-chapter-slider-time) .jw-progress,
    .jw-flag-touch .jw-slider-time:not(.jw-chapter-slider-time) .jw-rail,
    .jw-horizontal-volume-container:focus .jw-buffer,
    .jw-horizontal-volume-container:focus .jw-cue,
    .jw-horizontal-volume-container:focus .jw-progress,
    .jw-horizontal-volume-container:focus .jw-rail,
    .jw-horizontal-volume-container:hover .jw-buffer,
    .jw-horizontal-volume-container:hover .jw-cue,
    .jw-horizontal-volume-container:hover .jw-progress,
    .jw-horizontal-volume-container:hover .jw-rail,
    .jw-slider-time:not(.jw-chapter-slider-time):focus .jw-buffer,
    .jw-slider-time:not(.jw-chapter-slider-time):focus .jw-cue,
    .jw-slider-time:not(.jw-chapter-slider-time):focus .jw-progress,
    .jw-slider-time:not(.jw-chapter-slider-time):focus .jw-rail,
    .jw-slider-time:not(.jw-chapter-slider-time):hover .jw-buffer,
    .jw-slider-time:not(.jw-chapter-slider-time):hover .jw-cue,
    .jw-slider-time:not(.jw-chapter-slider-time):hover .jw-progress,
    .jw-slider-time:not(.jw-chapter-slider-time):hover .jw-rail {
      transform: translate(0, -50%) scale(1, 1)
    }

    .jw-horizontal-volume-container:focus .jw-knob,
    .jw-horizontal-volume-container:hover .jw-knob,
    .jw-slider-time:not(.jw-chapter-slider-time):focus .jw-knob,
    .jw-slider-time:not(.jw-chapter-slider-time):hover .jw-knob {
      transform: translate(-50%, -50%) scale(1)
    }

    .jw-horizontal-volume-container .jw-rail,
    .jw-slider-time:not(.jw-chapter-slider-time) .jw-rail {
      background-color: rgba(255, 255, 255, .2)
    }

    .jw-horizontal-volume-container .jw-buffer,
    .jw-slider-time:not(.jw-chapter-slider-time) .jw-buffer {
      background-color: rgba(255, 255, 255, .4)
    }

    .jw-flag-touch .jw-horizontal-volume-container::before,
    .jw-flag-touch .jw-slider-time:not(.jw-chapter-slider-time)::before {
      height: 44px;
      width: 100%;
      content: "";
      position: absolute;
      display: block;
      bottom: calc(100% - 17px);
      left: 0
    }

    .jw-breakpoint-0.jw-flag-touch .jw-horizontal-volume-container::before,
    .jw-breakpoint-0.jw-flag-touch .jw-slider-time:not(.jw-chapter-slider-time)::before {
      height: 34px
    }

    .jw-horizontal-volume-container.jw-tab-focus:focus .jw-rail,
    .jw-slider-time:not(.jw-chapter-slider-time).jw-tab-focus:focus .jw-rail {
      outline: solid 2px #4d90fe
    }

    .jw-flag-horizontal-slider .jw-overlay {
      display: none
    }

    .jw-flag-audio-player .jw-flag-horizontal-slider~.jw-horizontal-volume-container,
    .jwplayer:not(.jw-flag-small-player) .jw-flag-horizontal-slider~.jw-horizontal-volume-container {
      display: flex;
      transition: width .3s cubic-bezier(0, .25, .25, 1);
      width: 0
    }

    .jw-flag-audio-player .jw-flag-horizontal-slider~.jw-horizontal-volume-container.jw-open,
    .jwplayer:not(.jw-flag-small-player) .jw-flag-horizontal-slider~.jw-horizontal-volume-container.jw-open {
      width: 140px
    }

    .jw-flag-audio-player .jw-flag-horizontal-slider~.jw-horizontal-volume-container.jw-open .jw-slider-volume,
    .jwplayer:not(.jw-flag-small-player) .jw-flag-horizontal-slider~.jw-horizontal-volume-container.jw-open .jw-slider-volume {
      padding-right: 12px;
      opacity: 1
    }

    .jw-flag-audio-player .jw-flag-horizontal-slider~.jw-horizontal-volume-container .jw-slider-volume,
    .jwplayer:not(.jw-flag-small-player) .jw-flag-horizontal-slider~.jw-horizontal-volume-container .jw-slider-volume {
      transition: opacity .3s;
      opacity: 0
    }

    .jw-flag-audio-player .jw-flag-horizontal-slider~.jw-horizontal-volume-container .jw-slider-volume .jw-knob,
    .jwplayer:not(.jw-flag-small-player) .jw-flag-horizontal-slider~.jw-horizontal-volume-container .jw-slider-volume .jw-knob {
      transform: translate(-50%, -50%)
    }

    .jw-flag-audio-player .jw-button-container .jw-icon,
    .jwplayer:not(.jw-flag-small-player) .jw-button-container .jw-icon {
      flex: 0 0 auto
    }

    .jw-breakpoint--1:not(.jw-flag-audio-player) .jw-slider-time {
      height: 17px;
      padding: 0
    }

    .jw-breakpoint--1:not(.jw-flag-audio-player) .jw-slider-time .jw-slider-container {
      height: 10px
    }

    .jw-breakpoint--1:not(.jw-flag-audio-player) .jw-slider-time .jw-knob {
      border-radius: 0;
      border: 1px solid rgba(0, 0, 0, .75);
      height: 12px;
      width: 10px
    }

    .jw-breakpoint-0 .jw-slider-time {
      height: 11px
    }

    .jw-horizontal-volume-container {
      display: none
    }

    .jw-slider-horizontal.jw-chapter-slider-time {
      height: 16px;
      width: 100%;
      align-items: center;
      background: transparent none;
      padding: 0 12px;
      outline: 0
    }

    .jw-slider-horizontal.jw-chapter-slider-time .jw-old-buffer,
    .jw-slider-horizontal.jw-chapter-slider-time .jw-old-progress,
    .jw-slider-horizontal.jw-chapter-slider-time .jw-old-rail {
      position: absolute;
      cursor: pointer
    }

    .jw-slider-horizontal.jw-chapter-slider-time .jw-old-rail {
      width: 100%;
      -webkit-backface-visibility: hidden;
      backface-visibility: hidden;
      height: 100%;
      display: flex;
      background-color: rgba(0, 0, 0, 0)
    }

    .jw-slider-horizontal.jw-chapter-slider-time .jw-old-buffer,
    .jw-slider-horizontal.jw-chapter-slider-time .jw-old-progress {
      opacity: 0;
      pointer-events: none
    }

    .jw-slider-horizontal.jw-chapter-slider-time .jw-cue {
      width: 3px;
      height: 100%
    }

    .jw-slider-horizontal.jw-chapter-slider-time .jw-slider-container {
      z-index: 1;
      height: 100%
    }

    .jw-slider-horizontal.jw-chapter-slider-time .jw-slider-container:hover .jw-knob {
      transform: translate(-50%, -50%) scale(1)
    }

    .jw-slider-horizontal.jw-chapter-slider-time .jw-slider-container .jw-horizontal-volume-container {
      display: none
    }

    .jw-slider-horizontal.jw-chapter-slider-time .jw-slider-container .jw-timesegment {
      position: relative;
      flex: 0 0 0px;
      height: 100%
    }

    .jw-slider-horizontal.jw-chapter-slider-time .jw-slider-container .jw-timesegment:hover .jw-timesegment-resetter {
      transform: translate(0, -50%) scale(1, 1)
    }

    .jw-slider-horizontal.jw-chapter-slider-time .jw-slider-container .jw-timesegment:hover .jw-timesegment-background {
      background-color: rgba(255, 255, 255, .49)
    }

    .jw-slider-horizontal.jw-chapter-slider-time .jw-slider-container .jw-timesegment:hover .jw-timesegment-buffered {
      background-color: rgba(0, 0, 0, 0)
    }

    .jw-slider-horizontal.jw-chapter-slider-time .jw-slider-container .jw-timesegment-resetter {
      height: 5px;
      width: 100%;
      top: 50%;
      position: relative;
      transform: translate(0, -50%) scale(1, .6)
    }

    .jw-slider-horizontal.jw-chapter-slider-time .jw-slider-container .jw-timesegment-container {
      height: 100%;
      position: relative
    }

    .jw-slider-horizontal.jw-chapter-slider-time .jw-slider-container .jw-timesegment-bar {
      height: 100%;
      position: absolute
    }

    .jw-slider-horizontal.jw-chapter-slider-time .jw-slider-container .jw-timesegment-background {
      width: 100%;
      background-color: rgba(255, 255, 255, .3)
    }

    .jw-slider-horizontal.jw-chapter-slider-time .jw-slider-container .jw-timesegment-buffered {
      width: 0%;
      background-color: rgba(255, 255, 255, .3)
    }

    .jw-slider-horizontal.jw-chapter-slider-time .jw-slider-container .jw-timesegment-progress {
      width: 0%;
      background-color: #f2f2f2
    }

    .jw-flag-touch .jw-slider-horizontal.jw-chapter-slider-time .jw-slider-container::before {
      height: 44px;
      width: 100%;
      content: "";
      position: absolute;
      display: block;
      bottom: calc(100% - 17px);
      left: 0
    }

    .jw-breakpoint-0.jw-flag-touch .jw-slider-horizontal.jw-chapter-slider-time::before {
      height: 34px
    }

    .jw-flag-dragging .jw-slider-horizontal.jw-chapter-slider-time .jw-timesegment-resetter,
    .jw-flag-touch .jw-slider-horizontal.jw-chapter-slider-time .jw-timesegment-resetter {
      transform: translate(0, -50%) scale(1, 1)
    }

    .jw-slider-horizontal.jw-chapter-slider-time:focus .jw-knob,
    .jw-slider-horizontal.jw-chapter-slider-time:hover .jw-knob {
      transform: translate(-50%, -50%) scale(1)
    }

    .jw-slider-horizontal.jw-chapter-slider-time.jw-tab-focus:focus .jw-old-rail {
      outline: solid 2px #4d90fe
    }

    .jw-breakpoint--1:not(.jw-flag-audio-player) .jw-slider-horizontal.jw-chapter-slider-time .jw-slider-container {
      height: 100%
    }

    .jw-breakpoint--1:not(.jw-flag-audio-player) .jw-slider-horizontal.jw-chapter-slider-time .jw-timesegment-resetter {
      height: 10px
    }

    .jw-breakpoint--1:not(.jw-flag-audio-player) .jw-slider-horizontal.jw-chapter-slider-time .jw-knob {
      border-radius: 0;
      border: 1px solid rgba(0, 0, 0, .75);
      height: 12px;
      width: 10px
    }

    .jw-breakpoint-0 .jw-slider-horizontal.jw-chapter-slider-time {
      height: 11px
    }

    .jw-breakpoint-7:not(.jw-flag-audio-player) .jw-controlbar .jw-slider-horizontal.jw-chapter-slider-time {
      padding: 0 60px;
      height: 34px
    }

    .jw-breakpoint-7:not(.jw-flag-audio-player) .jw-controlbar .jw-slider-horizontal.jw-chapter-slider-time .jw-slider-container {
      height: 100%
    }

    .jw-breakpoint-7:not(.jw-flag-audio-player) .jw-controlbar .jw-slider-horizontal.jw-chapter-slider-time .jw-timesegment-resetter {
      height: 10px
    }

    .jw-modal {
      width: 284px
    }

    .jw-breakpoint-5 .jw-modal,
    .jw-breakpoint-6 .jw-modal,
    .jw-breakpoint-7 .jw-modal {
      height: 232px
    }

    .jw-breakpoint-3 .jw-modal,
    .jw-breakpoint-4 .jw-modal {
      height: 192px
    }

    .jw-breakpoint-2 .jw-modal,
    .jw-flag-small-player .jw-modal {
      bottom: 0;
      right: 0;
      height: 100%;
      width: 100%;
      max-height: none;
      max-width: none;
      z-index: 2
    }

    .jwplayer .jw-rightclick {
      display: none;
      position: absolute;
      white-space: nowrap
    }

    .jwplayer .jw-rightclick.jw-open {
      display: block
    }

    .jwplayer .jw-rightclick .jw-rightclick-list {
      border-radius: 1px;
      list-style: none;
      margin: 0;
      padding: 0
    }

    .jwplayer .jw-rightclick .jw-rightclick-list .jw-rightclick-item {
      background-color: rgba(0, 0, 0, .8);
      border-bottom: 1px solid #444;
      margin: 0
    }

    .jwplayer .jw-rightclick .jw-rightclick-list .jw-rightclick-item .jw-rightclick-logo {
      color: #fff;
      display: inline-flex;
      padding: 0 10px 0 0;
      vertical-align: middle
    }

    .jwplayer .jw-rightclick .jw-rightclick-list .jw-rightclick-item .jw-rightclick-logo .jw-svg-icon {
      height: 20px;
      width: 20px
    }

    .jwplayer .jw-rightclick .jw-rightclick-list .jw-rightclick-item .jw-rightclick-link {
      border: none;
      color: #fff;
      display: block;
      font-size: 11px;
      font-weight: 400;
      line-height: 1em;
      padding: 15px 23px;
      text-align: start;
      text-decoration: none;
      width: 100%
    }

    .jwplayer .jw-rightclick .jw-rightclick-list .jw-rightclick-item:last-child {
      border-bottom: none
    }

    .jwplayer .jw-rightclick .jw-rightclick-list .jw-rightclick-item:hover {
      cursor: pointer
    }

    .jwplayer .jw-rightclick .jw-rightclick-list .jw-featured {
      vertical-align: middle
    }

    .jwplayer .jw-rightclick .jw-rightclick-list .jw-featured .jw-rightclick-link {
      color: #fff
    }

    .jwplayer .jw-rightclick .jw-rightclick-list .jw-featured .jw-rightclick-link span {
      color: #fff;
      font-size: 12px
    }

    .jwplayer .jw-rightclick .jw-rightclick-link {
      border: none;
      background-color: transparent;
      outline: 0;
      cursor: pointer
    }

    .jw-icon-tooltip.jw-open .jw-overlay {
      opacity: 1;
      pointer-events: auto;
      transition-delay: 0s
    }

    .jw-icon-tooltip.jw-open .jw-overlay:focus {
      outline: 0
    }

    .jw-icon-tooltip.jw-open .jw-overlay:focus.jw-tab-focus {
      outline: solid 2px #4d90fe
    }

    .jw-slider-time .jw-overlay:before {
      height: 1em;
      top: auto
    }

    .jw-slider-time .jw-icon-tooltip.jw-open .jw-overlay {
      pointer-events: none
    }

    .jw-volume-tip {
      padding: 13px 0 26px
    }

    .jw-controlbar .jw-tooltip,
    .jw-settings-menu .jw-tooltip,
    .jw-time-tip {
      height: auto;
      width: 100%;
      box-shadow: 0 0 10px rgba(0, 0, 0, .4);
      color: #fff;
      display: block;
      margin: 0 0 14px;
      pointer-events: none;
      position: relative;
      z-index: 0;
      background-color: #fff
    }

    .jw-controlbar .jw-tooltip::after,
    .jw-settings-menu .jw-tooltip::after,
    .jw-time-tip::after {
      top: 100%;
      position: absolute;
      left: 50%;
      height: 14px;
      width: 14px;
      border-radius: 1px;
      background-color: currentColor;
      transform-origin: 75% 50%;
      transform: translate(-50%, -50%) rotate(45deg);
      z-index: -1
    }

    .jw-controlbar .jw-tooltip .jw-text,
    .jw-settings-menu .jw-tooltip .jw-text,
    .jw-time-tip .jw-text {
      background-color: #fff;
      border-radius: 1px;
      color: #000;
      font-size: 10px;
      height: auto;
      line-height: 1;
      padding: 7px 10px;
      display: inline-block;
      min-width: 100%;
      vertical-align: middle;
      min-height: 2.4em
    }

    .jw-controlbar .jw-overlay {
      position: absolute;
      bottom: 100%;
      left: 50%;
      margin: 0;
      min-height: 44px;
      min-width: 44px;
      opacity: 0;
      pointer-events: none;
      transition: 150ms cubic-bezier(0, .25, .25, 1);
      transition-property: opacity, visibility;
      transition-delay: 0s, 150ms;
      transform: translate(-50%, 0);
      width: 100%;
      z-index: 1
    }

    .jw-controlbar .jw-overlay .jw-contents {
      position: relative
    }

    .jw-controlbar .jw-option {
      position: relative;
      white-space: nowrap;
      cursor: pointer;
      list-style: none;
      height: 1.5em;
      font-family: inherit;
      line-height: 1.5em;
      padding: 0 .5em;
      font-size: .8em;
      margin: 0
    }

    .jw-controlbar .jw-option::before {
      padding-right: .125em
    }

    .jw-controlbar .jw-tooltip,
    .jw-settings-menu .jw-tooltip {
      position: absolute;
      bottom: 100%;
      left: 50%;
      opacity: 0;
      transform: translate(-50%, 0);
      transition: .1s 0s cubic-bezier(0, .25, .25, 1);
      transition-property: opacity, transform, visibility;
      visibility: hidden;
      white-space: nowrap;
      width: auto;
      z-index: 1
    }

    .jw-controlbar .jw-tooltip.jw-open,
    .jw-settings-menu .jw-tooltip.jw-open {
      opacity: 1;
      transform: translate(-50%, -10px);
      transition-duration: 150ms;
      transition-delay: .5s, 0s, .5s;
      visibility: visible
    }

    .jw-controlbar .jw-tooltip.jw-tooltip-fullscreen,
    .jw-settings-menu .jw-tooltip.jw-tooltip-fullscreen {
      left: auto;
      right: 0;
      transform: translate(0, 0)
    }

    .jw-controlbar .jw-tooltip.jw-tooltip-fullscreen.jw-open,
    .jw-settings-menu .jw-tooltip.jw-tooltip-fullscreen.jw-open {
      transform: translate(0, -10px)
    }

    .jw-controlbar .jw-tooltip.jw-tooltip-fullscreen::after,
    .jw-settings-menu .jw-tooltip.jw-tooltip-fullscreen::after {
      left: auto;
      right: 9px
    }

    .jw-tooltip-time {
      height: auto;
      width: 0;
      bottom: 100%;
      line-height: normal;
      padding: 0;
      pointer-events: none;
      -webkit-user-select: none;
      user-select: none
    }

    .jw-tooltip-time .jw-overlay {
      bottom: 0;
      min-height: 0;
      width: auto
    }

    .jw-tooltip {
      bottom: 57px;
      display: none;
      position: absolute
    }

    .jw-tooltip .jw-text {
      height: 100%;
      white-space: nowrap;
      text-overflow: ellipsis;
      direction: unset;
      max-width: 246px;
      overflow: hidden
    }

    .jw-flag-audio-player .jw-tooltip {
      display: none
    }

    .jw-flag-small-player .jw-time-thumb {
      display: none
    }

    .jw-chapter-slider-time .jw-tooltip-time .jw-overlay:before {
      height: 1em;
      top: auto
    }

    .jw-chapter-slider-time .jw-tooltip-time .jw-icon-tooltip.jw-open .jw-overlay {
      pointer-events: none
    }

    .jwplayer .jw-shortcuts-tooltip {
      top: 50%;
      position: absolute;
      left: 50%;
      background: rgba(38, 38, 38, .8);
      transform: translate(-50%, -50%);
      display: none;
      color: #fff;
      pointer-events: all;
      -webkit-user-select: text;
      user-select: text;
      overflow: hidden;
      flex-direction: column;
      z-index: 1
    }

    .jwplayer .jw-shortcuts-tooltip.jw-open {
      display: flex
    }

    .jwplayer .jw-shortcuts-tooltip .jw-shortcuts-close {
      flex: 0 0 auto;
      margin: 5px 5px 5px auto
    }

    .jwplayer .jw-shortcuts-tooltip .jw-shortcuts-container {
      display: flex;
      flex: 1 1 auto;
      flex-flow: column;
      font-size: 12px;
      margin: 0 20px 20px;
      overflow-y: auto;
      padding: 5px
    }

    .jwplayer .jw-shortcuts-tooltip .jw-shortcuts-container::-webkit-scrollbar {
      background-color: transparent;
      width: 6px
    }

    .jwplayer .jw-shortcuts-tooltip .jw-shortcuts-container::-webkit-scrollbar-thumb {
      background-color: #fff;
      border: 1px solid #262626;
      border-radius: 6px
    }

    .jwplayer .jw-shortcuts-tooltip .jw-shortcuts-container .jw-shortcuts-title {
      font-weight: 700
    }

    .jwplayer .jw-shortcuts-tooltip .jw-shortcuts-container .jw-shortcuts-header {
      align-items: center;
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px
    }

    .jwplayer .jw-shortcuts-tooltip .jw-shortcuts-container .jw-shortcuts-tooltip-list {
      display: flex;
      max-width: 340px;
      margin: 0 10px
    }

    .jwplayer .jw-shortcuts-tooltip .jw-shortcuts-container .jw-shortcuts-tooltip-list .jw-shortcuts-tooltip-descriptions {
      width: 100%
    }

    .jwplayer .jw-shortcuts-tooltip .jw-shortcuts-container .jw-shortcuts-tooltip-list .jw-shortcuts-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 10px 0;
      width: 100%
    }

    .jwplayer .jw-shortcuts-tooltip .jw-shortcuts-container .jw-shortcuts-tooltip-list .jw-shortcuts-row .jw-shortcuts-description {
      margin-right: 10px;
      max-width: 70%
    }

    .jwplayer .jw-shortcuts-tooltip .jw-shortcuts-container .jw-shortcuts-tooltip-list .jw-shortcuts-row .jw-shortcuts-key {
      background: #fefefe;
      color: #333;
      overflow: hidden;
      padding: 7px 10px;
      text-overflow: ellipsis;
      white-space: nowrap
    }

    .jw-skip {
      color: rgba(255, 255, 255, .8);
      cursor: default;
      position: absolute;
      display: flex;
      right: .75em;
      bottom: 56px;
      padding: .5em;
      border: 1px solid #333;
      background-color: #000;
      align-items: center;
      height: 2em
    }

    .jw-skip.jw-tab-focus:focus {
      outline: solid 2px #4d90fe
    }

    .jw-skip.jw-skippable {
      cursor: pointer;
      padding: .25em .75em
    }

    .jw-skip.jw-skippable:hover {
      cursor: pointer;
      color: #fff
    }

    .jw-skip.jw-skippable .jw-skip-icon {
      display: inline;
      height: 24px;
      width: 24px;
      margin: 0
    }

    .jw-breakpoint-7 .jw-skip {
      padding: 1.35em 1em;
      bottom: 130px
    }

    .jw-breakpoint-7 .jw-skip .jw-text {
      font-size: 1em;
      font-weight: 400
    }

    .jw-breakpoint-7 .jw-skip .jw-icon-inline {
      height: 30px;
      width: 30px
    }

    .jw-breakpoint-7 .jw-skip .jw-icon-inline .jw-svg-icon {
      height: 30px;
      width: 30px
    }

    .jw-skip .jw-skip-icon {
      display: none;
      margin-left: -.75em;
      padding: 0 .5em;
      pointer-events: none
    }

    .jw-skip .jw-skip-icon .jw-svg-icon-next {
      display: block;
      padding: 0
    }

    .jw-skip .jw-skip-icon,
    .jw-skip .jw-text {
      vertical-align: middle;
      font-size: .7em
    }

    .jw-skip .jw-text {
      font-weight: 700
    }

    .jw-cast {
      background-size: cover;
      display: none;
      height: 100%;
      position: relative;
      width: 100%
    }

    .jw-cast-container {
      background: linear-gradient(180deg, rgba(25, 25, 25, .75), rgba(25, 25, 25, .25), rgba(25, 25, 25, 0));
      left: 0;
      padding: 20px 20px 80px;
      position: absolute;
      top: 0;
      width: 100%
    }

    .jw-cast-text {
      color: #fff;
      font-size: 1.6em
    }

    .jw-breakpoint--1 .jw-cast-text,
    .jw-breakpoint-0 .jw-cast-text {
      font-size: 1.15em
    }

    .jw-breakpoint-1 .jw-cast-text,
    .jw-breakpoint-2 .jw-cast-text,
    .jw-breakpoint-3 .jw-cast-text {
      font-size: 1.3em
    }

    .jw-nextup-container {
      position: absolute;
      bottom: 66px;
      left: 0;
      background-color: transparent;
      cursor: pointer;
      margin: 0 auto;
      padding: 12px;
      pointer-events: none;
      right: 0;
      text-align: right;
      visibility: hidden;
      width: 100%
    }

    .jw-info-open .jw-nextup-container,
    .jw-settings-open .jw-nextup-container {
      display: none
    }

    .jw-breakpoint-7 .jw-nextup-container {
      padding: 60px
    }

    .jw-flag-small-player .jw-nextup-container {
      padding: 0 12px 0 0
    }

    .jw-flag-small-player .jw-nextup-container .jw-nextup-close,
    .jw-flag-small-player .jw-nextup-container .jw-nextup-duration,
    .jw-flag-small-player .jw-nextup-container .jw-nextup-title {
      display: none
    }

    .jw-flag-small-player .jw-nextup-container .jw-nextup-tooltip {
      height: 30px
    }

    .jw-flag-small-player .jw-nextup-container .jw-nextup-header {
      font-size: 12px
    }

    .jw-flag-small-player .jw-nextup-container .jw-nextup-body {
      justify-content: center;
      align-items: center;
      padding: .75em .3em
    }

    .jw-flag-small-player .jw-nextup-container .jw-nextup-thumbnail {
      width: 50%
    }

    .jw-flag-small-player .jw-nextup-container .jw-nextup {
      max-width: 65px
    }

    .jw-flag-small-player .jw-nextup-container .jw-nextup.jw-nextup-thumbnail-visible {
      max-width: 120px
    }

    .jw-nextup {
      background: rgba(38, 38, 38, .8);
      border-radius: 0;
      box-shadow: 0 0 10px rgba(0, 0, 0, .5);
      color: rgba(255, 255, 255, .8);
      display: inline-block;
      max-width: 280px;
      overflow: hidden;
      opacity: 0;
      position: relative;
      width: 64%;
      pointer-events: all;
      transform: translate(0, -5px);
      transition: 150ms cubic-bezier(0, .25, .25, 1);
      transition-property: opacity, transform;
      transition-delay: 0s
    }

    .jw-nextup:hover .jw-nextup-tooltip {
      color: #fff
    }

    .jw-nextup.jw-nextup-thumbnail-visible {
      max-width: 400px
    }

    .jw-nextup.jw-nextup-thumbnail-visible .jw-nextup-thumbnail {
      display: block
    }

    .jw-nextup-container-visible {
      visibility: visible
    }

    .jw-nextup-container-visible .jw-nextup {
      opacity: 1;
      transform: translate(0, 0);
      transition-delay: 0s, 0s, 150ms
    }

    .jw-nextup-tooltip {
      display: flex;
      height: 80px
    }

    .jw-nextup-thumbnail {
      width: 120px;
      background-position: center;
      background-size: cover;
      flex: 0 0 auto;
      display: none
    }

    .jw-nextup-body {
      flex: 1 1 auto;
      overflow: hidden;
      padding: .75em .875em;
      display: flex;
      flex-flow: column wrap;
      justify-content: space-between
    }

    .jw-nextup-header,
    .jw-nextup-title {
      font-size: 14px;
      line-height: 1.35
    }

    .jw-nextup-header {
      font-weight: 700
    }

    .jw-nextup-title {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      width: 100%
    }

    .jw-nextup-duration {
      align-self: flex-end;
      text-align: right;
      font-size: 12px
    }

    .jw-nextup-close {
      height: 24px;
      width: 24px;
      border: none;
      color: rgba(255, 255, 255, .8);
      cursor: pointer;
      margin: 6px;
      visibility: hidden
    }

    .jw-nextup-close:hover {
      color: #fff
    }

    .jw-nextup-sticky .jw-nextup-close {
      visibility: visible
    }

    .jw-nextup-firefox-pip-fix {
      background: #262626
    }

    .jw-autostart-mute {
      position: absolute;
      bottom: 0;
      right: 12px;
      height: 44px;
      width: 44px;
      background-color: rgba(33, 33, 33, .4);
      padding: 5px 4px 5px 6px;
      display: none
    }

    .jwplayer.jw-flag-autostart:not(.jw-flag-media-audio) .jw-nextup {
      display: none
    }

    .jw-settings-menu {
      position: absolute;
      bottom: 57px;
      right: 12px;
      align-items: flex-start;
      background-color: #262626;
      display: none;
      flex-flow: column nowrap;
      max-width: 284px;
      pointer-events: auto
    }

    .jw-settings-open .jw-settings-menu {
      display: flex
    }

    .jw-breakpoint-7 .jw-settings-menu {
      bottom: 130px;
      right: 60px;
      max-height: none;
      max-width: none;
      height: 35%;
      width: 25%;
      min-height: 200px
    }

    .jw-breakpoint-7 .jw-settings-menu .jw-settings-topbar:not(.jw-nested-menu-open) .jw-icon-inline {
      height: 60px;
      width: 60px
    }

    .jw-breakpoint-7 .jw-settings-menu .jw-settings-topbar:not(.jw-nested-menu-open) .jw-icon-inline .jw-svg-icon {
      height: 30px;
      width: 30px
    }

    .jw-breakpoint-7 .jw-settings-menu .jw-settings-topbar:not(.jw-nested-menu-open) .jw-icon-inline .jw-tooltip .jw-text {
      font-size: 1em
    }

    .jw-breakpoint-7 .jw-settings-menu .jw-settings-back {
      min-width: 60px
    }

    .jw-breakpoint-5 .jw-settings-menu,
    .jw-breakpoint-6 .jw-settings-menu {
      height: 232px;
      width: 284px;
      max-height: 232px
    }

    .jw-breakpoint-3 .jw-settings-menu,
    .jw-breakpoint-4 .jw-settings-menu {
      height: 192px;
      width: 284px;
      max-height: 192px
    }

    .jw-breakpoint-2 .jw-settings-menu {
      height: 179px;
      width: 284px;
      max-height: 179px
    }

    .jw-flag-small-player .jw-settings-menu {
      max-width: none
    }

    .jw-settings-menu .jw-icon.jw-button-color::after {
      height: 100%;
      width: 24px;
      box-shadow: inset 0 -3px 0 -1px currentColor;
      margin: auto;
      opacity: 0;
      transition: opacity 150ms cubic-bezier(0, .25, .25, 1)
    }

    .jw-settings-menu .jw-icon.jw-button-color[aria-expanded=true]::after {
      opacity: 1
    }

    .jw-settings-menu .jw-settings-reset {
      text-decoration: underline
    }

    .jw-settings-topbar {
      align-items: center;
      background-color: rgba(0, 0, 0, .4);
      display: flex;
      flex: 0 0 auto;
      padding: 3px 5px 0;
      width: 100%
    }

    .jw-settings-topbar.jw-nested-menu-open {
      padding: 0
    }

    .jw-settings-topbar.jw-nested-menu-open .jw-icon:not(.jw-settings-close):not(.jw-settings-back) {
      display: none
    }

    .jw-settings-topbar.jw-nested-menu-open .jw-svg-icon-close {
      width: 20px
    }

    .jw-settings-topbar.jw-nested-menu-open .jw-svg-icon-arrow-left {
      height: 12px
    }

    .jw-settings-topbar.jw-nested-menu-open .jw-settings-topbar-text {
      display: block;
      outline: 0
    }

    .jw-settings-topbar .jw-settings-back {
      min-width: 44px
    }

    .jw-settings-topbar .jw-settings-topbar-buttons {
      display: inherit;
      width: 100%;
      height: 100%
    }

    .jw-settings-topbar .jw-settings-topbar-text {
      display: none;
      color: #fff;
      font-size: 13px;
      width: 100%
    }

    .jw-settings-topbar .jw-settings-close {
      margin-left: auto
    }

    .jw-settings-submenu {
      display: none;
      flex: 1 1 auto;
      overflow-y: auto;
      padding: 8px 20px 0 5px
    }

    .jw-settings-submenu::-webkit-scrollbar {
      background-color: transparent;
      width: 6px
    }

    .jw-settings-submenu::-webkit-scrollbar-thumb {
      background-color: #fff;
      border: 1px solid #262626;
      border-radius: 6px
    }

    .jw-settings-submenu.jw-settings-submenu-active {
      display: block
    }

    .jw-settings-submenu .jw-submenu-topbar {
      box-shadow: 0 2px 9px 0 #1d1d1d;
      background-color: #2f2d2d;
      margin: -8px -20px 0 -5px
    }

    .jw-settings-submenu .jw-submenu-topbar .jw-settings-content-item {
      cursor: pointer;
      text-align: right;
      padding-right: 15px;
      text-decoration: underline
    }

    .jw-settings-submenu .jw-settings-value-wrapper {
      float: right;
      display: flex;
      align-items: center
    }

    .jw-settings-submenu .jw-settings-value-wrapper .jw-settings-content-item-arrow {
      display: flex
    }

    .jw-settings-submenu .jw-settings-value-wrapper .jw-svg-icon-arrow-right {
      width: 8px;
      margin-left: 5px;
      height: 12px
    }

    .jw-breakpoint-7 .jw-settings-submenu .jw-settings-content-item {
      font-size: 1em;
      padding: 11px 15px 11px 30px
    }

    .jw-breakpoint-7 .jw-settings-submenu .jw-settings-content-item .jw-settings-item-active::before {
      justify-content: flex-end
    }

    .jw-breakpoint-7 .jw-settings-submenu .jw-settings-content-item .jw-auto-label {
      font-size: .85em;
      padding-left: 10px
    }

    .jw-flag-touch .jw-settings-submenu {
      overflow-y: scroll;
      -webkit-overflow-scrolling: touch
    }

    .jw-auto-label {
      font-size: 10px;
      font-weight: initial;
      opacity: .75;
      padding-left: 5px
    }

    .jw-settings-content-item {
      position: relative;
      color: rgba(255, 255, 255, .8);
      cursor: pointer;
      font-size: 12px;
      line-height: 1;
      padding: 7px 0 7px 15px;
      width: 100%;
      text-align: left;
      outline: 0
    }

    .jw-settings-content-item:hover {
      color: #fff
    }

    .jw-settings-content-item:focus {
      font-weight: 700
    }

    .jw-flag-small-player .jw-settings-content-item {
      line-height: 1.75
    }

    .jw-settings-content-item.jw-tab-focus:focus {
      border: solid 2px #4d90fe
    }

    .jw-settings-item-active {
      font-weight: 700;
      position: relative
    }

    .jw-settings-item-active::before {
      height: 100%;
      width: 1em;
      align-items: center;
      content: "\2022";
      display: inline-flex;
      justify-content: center
    }

    .jw-breakpoint-2 .jw-settings-open .jw-display-container,
    .jw-flag-small-player .jw-settings-open .jw-display-container,
    .jw-flag-touch .jw-settings-open .jw-display-container {
      display: none
    }

    .jw-breakpoint-2 .jw-settings-open.jw-controls,
    .jw-flag-small-player .jw-settings-open.jw-controls,
    .jw-flag-touch .jw-settings-open.jw-controls {
      z-index: 1
    }

    .jw-flag-small-player .jw-settings-open .jw-controlbar {
      display: none
    }

    .jw-settings-open .jw-icon-settings::after {
      opacity: 1
    }

    .jw-settings-open .jw-tooltip-settings {
      display: none
    }

    .jw-sharing-link {
      cursor: pointer
    }

    .jw-shortcuts-container .jw-switch {
      position: relative;
      display: flex;
      align-items: center;
      transition: ease-out .15s;
      transition-property: opacity, background;
      border-radius: 18px;
      width: 80px;
      height: 20px;
      padding: 10px;
      background: rgba(80, 80, 80, .8);
      cursor: pointer;
      font-size: inherit;
      vertical-align: middle;
      outline: 0
    }

    .jw-shortcuts-container .jw-switch.jw-tab-focus {
      border: solid 2px #4d90fe
    }

    .jw-shortcuts-container .jw-switch .jw-switch-knob {
      position: absolute;
      left: 1px;
      transition: ease-out .15s;
      box-shadow: 0 0 10px rgba(0, 0, 0, .4);
      border-radius: 13px;
      width: 15px;
      height: 15px;
      background: #fefefe
    }

    .jw-shortcuts-container .jw-switch .jw-switch-disabled,
    .jw-shortcuts-container .jw-switch .jw-switch-enabled {
      position: absolute;
      transition: inherit;
      color: #fefefe
    }

    .jw-shortcuts-container .jw-switch .jw-switch-disabled {
      right: 8px
    }

    .jw-shortcuts-container .jw-switch .jw-switch-enabled {
      left: 8px;
      opacity: 0
    }

    .jw-shortcuts-container .jw-switch[aria-checked=true] {
      background: #475470
    }

    .jw-shortcuts-container .jw-switch[aria-checked=true] .jw-switch-disabled {
      opacity: 0
    }

    .jw-shortcuts-container .jw-switch[aria-checked=true] .jw-switch-enabled {
      opacity: 1
    }

    .jw-shortcuts-container .jw-switch[aria-checked=true] .jw-switch-knob {
      left: 60px
    }

    .jw-idle-icon-text {
      display: none;
      line-height: 1;
      position: absolute;
      text-align: center;
      text-indent: .35em;
      top: 100%;
      white-space: nowrap;
      left: 50%;
      transform: translateX(-50%)
    }

    .jw-idle-label {
      border-radius: 50%;
      color: #fff;
      filter: drop-shadow(1px 1px 5px rgba(12, 26, 71, .25));
      font: 400 16px/1 Arial, Helvetica, sans-serif;
      position: relative;
      transition: background-color 150ms cubic-bezier(0, .25, .25, 1);
      transition-property: background-color, filter;
      -webkit-font-smoothing: antialiased
    }

    .jw-state-idle .jw-icon-display.jw-idle-label .jw-idle-icon-text {
      display: block
    }

    .jw-state-idle .jw-icon-display.jw-idle-label .jw-svg-icon-play {
      transform: scale(.7, .7)
    }

    .jw-breakpoint--1.jw-state-idle .jw-icon-display.jw-idle-label,
    .jw-breakpoint-0.jw-state-idle .jw-icon-display.jw-idle-label {
      font-size: 12px
    }

    @supports (filter:drop-shadow(0 0 3px #000)) {

      .jwplayer.jw-ab-drop-shadow .jw-controls .jw-icon.jw-text,
      .jwplayer.jw-ab-drop-shadow .jw-controls .jw-svg-icon,
      .jwplayer.jw-ab-drop-shadow .jw-slider-container .jw-rail,
      .jwplayer.jw-ab-drop-shadow .jw-title {
        text-shadow: none;
        box-shadow: none;
        filter: drop-shadow(0 2px 3px rgba(0, 0, 0, .3))
      }

      .jwplayer.jw-ab-drop-shadow .jw-button-color {
        opacity: .8;
        transition-property: color, opacity
      }

      .jwplayer.jw-ab-drop-shadow .jw-button-color:not(:hover) {
        color: #fff;
        opacity: .8
      }

      .jwplayer.jw-ab-drop-shadow .jw-button-color:hover {
        opacity: 1
      }

      .jwplayer.jw-ab-drop-shadow .jw-controls-backdrop {
        background-image: linear-gradient(to bottom, hsla(0, 0%, 0%, 0), hsla(0, 0%, 0%, 0.00787) 10.79%, hsla(0, 0%, 0%, 0.02963) 21.99%, hsla(0, 0%, 0%, 0.0625) 33.34%, hsla(0, 0%, 0%, 0.1037) 44.59%, hsla(0, 0%, 0%, 0.15046) 55.48%, hsla(0, 0%, 0%, 0.2) 65.75%, hsla(0, 0%, 0%, 0.24954) 75.14%, hsla(0, 0%, 0%, 0.2963) 83.41%, hsla(0, 0%, 0%, 0.3375) 90.28%, hsla(0, 0%, 0%, 0.37037) 95.51%, hsla(0, 0%, 0%, 0.39213) 98.83%, hsla(0, 0%, 0%, 0.4));
        mix-blend-mode: multiply;
        transition-property: opacity
      }

      .jw-state-idle.jwplayer.jw-ab-drop-shadow .jw-controls-backdrop {
        background-image: linear-gradient(to bottom, hsla(0, 0%, 0%, 0.2), hsla(0, 0%, 0%, 0.19606) 1.17%, hsla(0, 0%, 0%, 0.18519) 4.49%, hsla(0, 0%, 0%, 0.16875) 9.72%, hsla(0, 0%, 0%, 0.14815) 16.59%, hsla(0, 0%, 0%, 0.12477) 24.86%, hsla(0, 0%, 0%, 0.1) 34.25%, hsla(0, 0%, 0%, 0.07523) 44.52%, hsla(0, 0%, 0%, 0.05185) 55.41%, hsla(0, 0%, 0%, 0.03125) 66.66%, hsla(0, 0%, 0%, 0.01481) 78.01%, hsla(0, 0%, 0%, 0.00394) 89.21%, hsla(0, 0%, 0%, 0));
        background-size: 100% 7rem;
        background-position: 50% 0
      }

      .jwplayer.jw-ab-drop-shadow.jw-state-idle .jw-controls {
        background-color: transparent
      }
    }

    .jw-video-thumbnail-container {
      position: relative;
      overflow: hidden
    }

    .jw-video-thumbnail-container:not(.jw-related-shelf-item-image) {
      height: 100%;
      width: 100%
    }

    .jw-video-thumbnail-container.jw-video-thumbnail-generated {
      position: absolute;
      top: 0;
      left: 0
    }

    .jw-related-item-content:hover .jw-video-thumbnail-container,
    .jw-related-shelf-item:hover .jw-video-thumbnail-container,
    .jw-video-thumbnail-container:hover {
      cursor: pointer
    }

    .jw-related-item-content:hover .jw-video-thumbnail-container .jw-video-thumbnail:not(.jw-video-thumbnail-completed),
    .jw-related-shelf-item:hover .jw-video-thumbnail-container .jw-video-thumbnail:not(.jw-video-thumbnail-completed),
    .jw-video-thumbnail-container:hover .jw-video-thumbnail:not(.jw-video-thumbnail-completed) {
      opacity: 1
    }

    .jw-video-thumbnail-container .jw-video-thumbnail {
      position: absolute;
      top: 50%;
      left: 50%;
      bottom: unset;
      transform: translate(-50%, -50%);
      width: 100%;
      height: auto;
      min-width: 100%;
      min-height: 100%;
      opacity: 0;
      transition: opacity .3s ease;
      object-fit: cover;
      background: #000
    }

    .jw-related-item-next-up .jw-video-thumbnail-container .jw-video-thumbnail {
      height: 100%;
      width: auto
    }

    .jw-video-thumbnail-container .jw-video-thumbnail.jw-video-thumbnail-visible:not(.jw-video-thumbnail-completed) {
      opacity: 1
    }

    .jw-video-thumbnail-container .jw-video-thumbnail.jw-video-thumbnail-completed {
      opacity: 0
    }

    .jw-video-thumbnail-container .jw-video-thumbnail~.jw-svg-icon-play {
      display: none
    }

    .jw-video-thumbnail-container .jw-video-thumbnail+.jw-related-shelf-item-aspect {
      pointer-events: none
    }

    .jw-video-thumbnail-container .jw-video-thumbnail+.jw-related-item-poster-content {
      pointer-events: none
    }

    .jw-preview {
      overflow: hidden
    }

    .jw-preview .jw-ab-zoom-thumbnail {
      all: inherit;
      animation: jw-ab-zoom-thumbnail-animation 10s infinite
    }

    @keyframes jw-ab-zoom-thumbnail-animation {
      0% {
        transform: scale(1, 1)
      }

      50% {
        transform: scale(1.25, 1.25)
      }

      100% {
        transform: scale(1, 1)
      }
    }

    .jw-state-idle:not(.jw-flag-cast-available) .jw-display {
      padding: 0
    }

    .jw-state-idle .jw-controls {
      background: rgba(0, 0, 0, .4)
    }

    .jw-state-idle.jw-flag-cardboard-available .jw-controlbar .jw-icon:not(.jw-icon-cardboard):not(.jw-icon-cast):not(.jw-icon-airplay),
    .jw-state-idle.jw-flag-cardboard-available .jw-controlbar .jw-slider-time,
    .jw-state-idle.jw-flag-cast-available:not(.jw-flag-audio-player) .jw-controlbar .jw-icon:not(.jw-icon-cardboard):not(.jw-icon-cast):not(.jw-icon-airplay),
    .jw-state-idle.jw-flag-cast-available:not(.jw-flag-audio-player) .jw-controlbar .jw-slider-time {
      display: none
    }

    .jwplayer.jw-state-buffering .jw-display-icon-display .jw-icon:focus {
      border: none
    }

    .jwplayer.jw-state-buffering .jw-display-icon-display .jw-icon .jw-svg-icon-buffer {
      animation: jw-spin 2s linear infinite;
      display: block
    }

    @keyframes jw-spin {
      100% {
        transform: rotate(360deg)
      }
    }

    .jwplayer.jw-state-buffering .jw-icon-playback .jw-svg-icon-play {
      display: none
    }

    .jwplayer.jw-state-buffering .jw-icon-display .jw-svg-icon-pause {
      display: none
    }

    .jwplayer.jw-state-playing .jw-display .jw-icon-display .jw-svg-icon-play,
    .jwplayer.jw-state-playing .jw-icon-playback .jw-svg-icon-play {
      display: none
    }

    .jwplayer.jw-state-playing .jw-display .jw-icon-display .jw-svg-icon-pause,
    .jwplayer.jw-state-playing .jw-icon-playback .jw-svg-icon-pause {
      display: block
    }

    .jwplayer.jw-state-playing.jw-flag-user-inactive:not(.jw-flag-audio-player):not(.jw-flag-casting):not(.jw-flag-media-audio) .jw-controls-backdrop {
      opacity: 0
    }

    .jwplayer.jw-state-playing.jw-flag-user-inactive:not(.jw-flag-audio-player):not(.jw-flag-casting):not(.jw-flag-media-audio) .jw-logo-bottom-left,
    .jwplayer.jw-state-playing.jw-flag-user-inactive:not(.jw-flag-audio-player):not(.jw-flag-casting):not(.jw-flag-media-audio):not(.jw-flag-autostart) .jw-logo-bottom-right {
      bottom: 0
    }

    .jwplayer .jw-icon-playback .jw-svg-icon-stop {
      display: none
    }

    .jwplayer.jw-state-complete .jw-svg-icon-pause,
    .jwplayer.jw-state-error .jw-svg-icon-pause,
    .jwplayer.jw-state-idle .jw-svg-icon-pause,
    .jwplayer.jw-state-paused .jw-svg-icon-pause {
      display: none
    }

    .jwplayer.jw-state-buffering .jw-icon-display .jw-svg-icon-play,
    .jwplayer.jw-state-complete .jw-icon-display .jw-svg-icon-play,
    .jwplayer.jw-state-error .jw-icon-display .jw-svg-icon-play {
      display: none
    }

    .jwplayer:not(.jw-state-buffering) .jw-svg-icon-buffer {
      display: none
    }

    .jwplayer:not(.jw-state-complete) .jw-svg-icon-replay {
      display: none
    }

    .jwplayer:not(.jw-state-error) .jw-svg-icon-error {
      display: none
    }

    .jwplayer.jw-state-complete .jw-display .jw-icon-display .jw-svg-icon-replay {
      display: block
    }

    .jwplayer.jw-state-complete .jw-display .jw-text {
      display: none
    }

    .jwplayer.jw-state-complete .jw-controls {
      background: rgba(0, 0, 0, .4);
      height: 100%
    }

    .jw-state-idle .jw-icon-display .jw-svg-icon-pause,
    .jwplayer.jw-state-complete .jw-icon-playback .jw-svg-icon-pause,
    .jwplayer.jw-state-paused .jw-icon-display .jw-svg-icon-pause,
    .jwplayer.jw-state-paused .jw-icon-playback .jw-svg-icon-pause {
      display: none
    }

    .jw-state-idle .jw-display-icon-next,
    .jw-state-idle .jw-display-icon-rewind,
    .jwplayer.jw-state-buffering .jw-display-icon-next,
    .jwplayer.jw-state-buffering .jw-display-icon-rewind,
    .jwplayer.jw-state-complete .jw-display-icon-next,
    .jwplayer.jw-state-complete .jw-display-icon-rewind,
    body .jw-error .jw-display-icon-next,
    body .jw-error .jw-display-icon-rewind,
    body .jwplayer.jw-state-error .jw-display-icon-next,
    body .jwplayer.jw-state-error .jw-display-icon-rewind {
      display: none
    }

    body .jw-error .jw-icon-display,
    body .jwplayer.jw-state-error .jw-icon-display {
      cursor: default
    }

    body .jw-error .jw-icon-display .jw-svg-icon-error,
    body .jwplayer.jw-state-error .jw-icon-display .jw-svg-icon-error {
      display: block
    }

    body .jw-error .jw-icon-container {
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      bottom: 0;
      right: 0
    }

    body .jwplayer.jw-state-error.jw-flag-audio-player .jw-preview {
      display: none
    }

    body .jwplayer.jw-state-error.jw-flag-audio-player .jw-title {
      padding-top: 4px
    }

    body .jwplayer.jw-state-error.jw-flag-audio-player .jw-title-primary {
      width: auto;
      display: inline-block;
      padding-right: .5ch
    }

    body .jwplayer.jw-state-error.jw-flag-audio-player .jw-title-secondary {
      width: auto;
      display: inline-block;
      padding-left: 0
    }

    .jwplayer.jw-state-idle:not(.jw-flag-audio-player):not(.jw-flag-cast-available):not(.jw-flag-cardboard-available) .jw-controlbar,
    body .jwplayer.jw-state-error .jw-controlbar {
      display: none
    }

    .jwplayer.jw-state-idle:not(.jw-flag-audio-player):not(.jw-flag-cast-available):not(.jw-flag-cardboard-available) .jw-settings-menu,
    body .jwplayer.jw-state-error .jw-settings-menu {
      height: 100%;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%)
    }

    .jwplayer.jw-state-idle:not(.jw-flag-audio-player):not(.jw-flag-cast-available):not(.jw-flag-cardboard-available) .jw-display,
    body .jwplayer.jw-state-error .jw-display {
      padding: 0
    }

    .jwplayer.jw-state-idle:not(.jw-flag-audio-player):not(.jw-flag-cast-available):not(.jw-flag-cardboard-available) .jw-logo-bottom-left,
    .jwplayer.jw-state-idle:not(.jw-flag-audio-player):not(.jw-flag-cast-available):not(.jw-flag-cardboard-available) .jw-logo-bottom-right,
    body .jwplayer.jw-state-error .jw-logo-bottom-left,
    body .jwplayer.jw-state-error .jw-logo-bottom-right {
      bottom: 0
    }

    .jwplayer.jw-state-playing.jw-flag-user-inactive .jw-display {
      visibility: hidden;
      pointer-events: none;
      opacity: 0
    }

    .jwplayer.jw-state-paused:not(.jw-flag-touch):not(.jw-flag-small-player):not(.jw-flag-casting):not(.jw-flag-play-rejected) .jw-display,
    .jwplayer.jw-state-playing:not(.jw-flag-touch):not(.jw-flag-small-player):not(.jw-flag-casting) .jw-display {
      display: none
    }

    .jwplayer.jw-state-paused.jw-flag-play-rejected:not(.jw-flag-touch):not(.jw-flag-small-player):not(.jw-flag-casting) .jw-display-icon-next,
    .jwplayer.jw-state-paused.jw-flag-play-rejected:not(.jw-flag-touch):not(.jw-flag-small-player):not(.jw-flag-casting) .jw-display-icon-rewind {
      display: none
    }

    .jwplayer.jw-state-buffering .jw-display-icon-display .jw-text,
    .jwplayer.jw-state-complete .jw-display .jw-text {
      display: none
    }

    .jwplayer.jw-flag-casting:not(.jw-flag-audio-player) .jw-cast {
      display: block
    }

    .jwplayer.jw-flag-casting .jw-captions,
    .jwplayer.jw-flag-casting .jw-icon-audio-tracks,
    .jwplayer.jw-flag-casting .jw-icon-fullscreen,
    .jwplayer.jw-flag-casting .jw-icon-hd {
      display: none
    }

    .jwplayer.jw-flag-casting.jw-flag-airplay-casting .jw-icon-airplay {
      color: #fff
    }

    .jw-state-paused.jw-flag-casting:not(.jw-flag-audio-player) .jw-display,
    .jw-state-playing.jw-flag-casting:not(.jw-flag-audio-player) .jw-display {
      display: flex
    }

    .jwplayer.jw-flag-cast-available .jw-icon-airplay,
    .jwplayer.jw-flag-cast-available .jw-icon-cast {
      display: flex
    }

    .jwplayer.jw-flag-cardboard-available .jw-icon-cardboard {
      display: flex
    }

    .jwplayer.jw-flag-live .jw-display-icon-rewind {
      visibility: hidden
    }

    .jwplayer.jw-flag-live .jw-controlbar .jw-slider-time,
    .jwplayer.jw-flag-live .jw-controlbar .jw-text-countdown,
    .jwplayer.jw-flag-live .jw-controlbar .jw-text-duration,
    .jwplayer.jw-flag-live .jw-controlbar .jw-text-elapsed {
      display: none
    }

    .jwplayer.jw-flag-live .jw-controlbar .jw-text-alt {
      display: flex
    }

    .jwplayer.jw-flag-live .jw-controlbar .jw-overlay:after {
      display: none
    }

    .jwplayer.jw-flag-live .jw-nextup-container {
      bottom: 44px
    }

    .jwplayer.jw-flag-live .jw-text-duration,
    .jwplayer.jw-flag-live .jw-text-elapsed {
      display: none
    }

    .jwplayer.jw-flag-live .jw-text-live {
      cursor: default
    }

    .jwplayer.jw-flag-live .jw-text-live:hover {
      color: rgba(255, 255, 255, .8)
    }

    .jwplayer.jw-flag-live.jw-state-buffering .jw-icon-playback .jw-svg-icon-stop,
    .jwplayer.jw-flag-live.jw-state-playing .jw-icon-playback .jw-svg-icon-stop {
      display: block
    }

    .jwplayer.jw-flag-live.jw-state-buffering .jw-icon-playback .jw-svg-icon-pause,
    .jwplayer.jw-flag-live.jw-state-playing .jw-icon-playback .jw-svg-icon-pause {
      display: none
    }

    .jw-text-live {
      height: 24px;
      width: auto;
      align-items: center;
      border-radius: 1px;
      color: rgba(255, 255, 255, .8);
      display: flex;
      font-size: 12px;
      font-weight: 700;
      margin-right: 10px;
      padding: 0 1ch;
      text-rendering: geometricPrecision;
      text-transform: uppercase;
      transition: 150ms cubic-bezier(0, .25, .25, 1);
      transition-property: box-shadow, color
    }

    .jw-text-live::before {
      height: 8px;
      width: 8px;
      background-color: currentColor;
      border-radius: 50%;
      margin-right: 6px;
      opacity: 1;
      transition: opacity 150ms cubic-bezier(0, .25, .25, 1)
    }

    .jw-text-live.jw-dvr-live {
      box-shadow: inset 0 0 0 2px currentColor
    }

    .jw-text-live.jw-dvr-live::before {
      opacity: .5
    }

    .jw-text-live.jw-dvr-live:hover {
      color: #fff
    }

    .jwplayer.jw-flag-controls-hidden .jw-logo.jw-hide {
      visibility: hidden;
      pointer-events: none;
      opacity: 0
    }

    .jwplayer.jw-flag-controls-hidden:not(.jw-flag-casting) .jw-logo-top-right {
      top: 0
    }

    .jwplayer.jw-flag-controls-hidden .jw-plugin {
      bottom: .5em
    }

    .jwplayer.jw-flag-controls-hidden:not(.jw-flag-touch.jw-breakpoint-4):not(.jw-flag-touch.jw-breakpoint-5):not(.jw-flag-touch.jw-breakpoint-6):not(.jw-flag-touch.jw-breakpoint-7) .jw-nextup-container {
      transform: translateY(66px)
    }

    .jwplayer.jw-flag-controls-hidden.jw-flag-touch.jw-state-playing.jw-breakpoint-4 .jw-nextup-container,
    .jwplayer.jw-flag-controls-hidden.jw-flag-touch.jw-state-playing.jw-breakpoint-5 .jw-nextup-container,
    .jwplayer.jw-flag-controls-hidden.jw-flag-touch.jw-state-playing.jw-breakpoint-6 .jw-nextup-container,
    .jwplayer.jw-flag-controls-hidden.jw-flag-touch.jw-state-playing.jw-breakpoint-7 .jw-nextup-container {
      transform: translateY(4.25em)
    }

    .jw-flag-controls-hidden .jw-controlbar,
    .jw-flag-controls-hidden .jw-display {
      visibility: hidden;
      pointer-events: none;
      opacity: 0;
      transition-delay: 0s, 250ms
    }

    .jw-flag-controls-hidden .jw-controls-backdrop {
      opacity: 0
    }

    .jw-flag-controls-hidden .jw-logo {
      visibility: visible
    }

    .jwplayer.jw-flag-user-inactive:not(.jw-flag-media-audio).jw-state-playing .jw-logo.jw-hide {
      visibility: hidden;
      pointer-events: none;
      opacity: 0
    }

    .jwplayer.jw-flag-user-inactive:not(.jw-flag-media-audio).jw-state-playing:not(.jw-flag-casting) .jw-logo-top-right {
      top: 0
    }

    .jwplayer.jw-flag-user-inactive:not(.jw-flag-media-audio).jw-state-playing .jw-plugin {
      bottom: .5em
    }

    .jwplayer.jw-flag-user-inactive:not(.jw-flag-media-audio).jw-state-playing:not(.jw-flag-touch.jw-breakpoint-4):not(.jw-flag-touch.jw-breakpoint-5):not(.jw-flag-touch.jw-breakpoint-6):not(.jw-flag-touch.jw-breakpoint-7) .jw-nextup-container {
      transform: translateY(66px)
    }

    .jwplayer.jw-flag-user-inactive:not(.jw-flag-media-audio).jw-state-playing.jw-flag-touch.jw-state-playing.jw-breakpoint-4 .jw-nextup-container,
    .jwplayer.jw-flag-user-inactive:not(.jw-flag-media-audio).jw-state-playing.jw-flag-touch.jw-state-playing.jw-breakpoint-5 .jw-nextup-container,
    .jwplayer.jw-flag-user-inactive:not(.jw-flag-media-audio).jw-state-playing.jw-flag-touch.jw-state-playing.jw-breakpoint-6 .jw-nextup-container,
    .jwplayer.jw-flag-user-inactive:not(.jw-flag-media-audio).jw-state-playing.jw-flag-touch.jw-state-playing.jw-breakpoint-7 .jw-nextup-container {
      transform: translateY(4.25em)
    }

    .jwplayer.jw-flag-user-inactive:not(.jw-flag-media-audio).jw-state-playing:not(.jw-flag-controls-hidden) .jw-media {
      cursor: none;
      -webkit-cursor-visibility: auto-hide
    }

    .jwplayer.jw-flag-user-inactive:not(.jw-flag-media-audio).jw-state-playing.jw-flag-casting .jw-display {
      display: flex
    }

    .jwplayer.jw-flag-user-inactive:not(.jw-flag-media-audio).jw-state-playing:not(.jw-flag-ads) .jw-autostart-mute {
      display: flex
    }

    .jwplayer.jw-flag-user-inactive:not(.jw-flag-media-audio).jw-flag-casting .jw-nextup-container {
      bottom: 66px
    }

    .jwplayer.jw-flag-user-inactive:not(.jw-flag-media-audio).jw-flag-casting.jw-state-idle .jw-nextup-container {
      display: none
    }

    .jw-flag-media-audio .jw-preview {
      display: block
    }

    .jwplayer.jw-flag-ads .jw-captions.jw-captions-enabled,
    .jwplayer.jw-flag-ads .jw-logo,
    .jwplayer.jw-flag-ads .jw-nextup-container,
    .jwplayer.jw-flag-ads .jw-preview,
    .jwplayer.jw-flag-ads .jw-text-duration,
    .jwplayer.jw-flag-ads .jw-text-elapsed {
      display: none
    }

    .jwplayer.jw-flag-ads video::-webkit-media-text-track-container {
      display: none
    }

    .jwplayer.jw-flag-ads.jw-flag-small-player .jw-display-icon-display,
    .jwplayer.jw-flag-ads.jw-flag-small-player .jw-display-icon-next,
    .jwplayer.jw-flag-ads.jw-flag-small-player .jw-display-icon-rewind {
      display: none
    }

    .jwplayer.jw-flag-ads.jw-flag-small-player.jw-state-buffering .jw-display-icon-display {
      display: inline-block
    }

    .jwplayer.jw-flag-ads .jw-controlbar {
      flex-direction: column-reverse
    }

    .jwplayer.jw-flag-ads .jw-controlbar .jw-slider-time {
      height: auto;
      padding: 0;
      pointer-events: none
    }

    .jwplayer.jw-flag-ads .jw-controlbar .jw-slider-time .jw-slider-container {
      height: 5px
    }

    .jwplayer.jw-flag-ads .jw-controlbar .jw-slider-time .jw-buffer,
    .jwplayer.jw-flag-ads .jw-controlbar .jw-slider-time .jw-cue,
    .jwplayer.jw-flag-ads .jw-controlbar .jw-slider-time .jw-icon-settings,
    .jwplayer.jw-flag-ads .jw-controlbar .jw-slider-time .jw-knob,
    .jwplayer.jw-flag-ads .jw-controlbar .jw-slider-time .jw-rail {
      display: none
    }

    .jwplayer.jw-flag-ads .jw-controlbar .jw-slider-time .jw-progress {
      transform: none;
      top: auto
    }

    .jwplayer.jw-flag-ads .jw-controlbar .jw-icon-inline:not(.jw-icon-playback):not(.jw-icon-fullscreen):not(.jw-icon-volume),
    .jwplayer.jw-flag-ads .jw-controlbar .jw-icon-tooltip:not(.jw-icon-volume),
    .jwplayer.jw-flag-ads .jw-controlbar .jw-tooltip {
      display: none
    }

    .jwplayer.jw-flag-ads .jw-controlbar .jw-volume-tip {
      padding: 13px 0
    }

    .jwplayer.jw-flag-ads .jw-controlbar .jw-text-alt {
      display: flex
    }

    .jwplayer.jw-flag-ads .jw-fullscreen-ima {
      display: none
    }

    .jwplayer.jw-flag-ads.jw-flag-ads.jw-state-playing.jw-flag-touch:not(.jw-flag-ads-vpaid) .jw-controls .jw-controlbar,
    .jwplayer.jw-flag-ads.jw-flag-ads.jw-state-playing.jw-flag-touch:not(.jw-flag-ads-vpaid).jw-flag-autostart .jw-controls .jw-controlbar {
      display: flex;
      pointer-events: all;
      visibility: visible;
      opacity: 1
    }

    .jwplayer.jw-flag-ads.jw-flag-ads.jw-state-playing.jw-flag-touch:not(.jw-flag-ads-vpaid).jw-flag-autostart.jw-flag-user-inactive .jw-controls-backdrop,
    .jwplayer.jw-flag-ads.jw-flag-ads.jw-state-playing.jw-flag-touch:not(.jw-flag-ads-vpaid).jw-flag-user-inactive .jw-controls-backdrop {
      opacity: 1;
      background-size: 100% 60px
    }

    .jwplayer.jw-flag-ads-vpaid .jw-display-container,
    .jwplayer.jw-flag-ads-vpaid .jw-skip,
    .jwplayer.jw-flag-touch.jw-flag-ads-vpaid .jw-display-container,
    .jwplayer.jw-flag-touch.jw-flag-ads-vpaid .jw-skip {
      display: none
    }

    .jwplayer.jw-flag-ads-vpaid.jw-flag-small-player .jw-controls {
      background: 0 0
    }

    .jwplayer.jw-flag-ads-vpaid.jw-flag-small-player .jw-controls::after {
      content: none
    }

    .jwplayer.jw-flag-ads-hide-controls .jw-controls,
    .jwplayer.jw-flag-ads-hide-controls .jw-controls-backdrop {
      display: none !important
    }

    .jw-flag-overlay-open-related .jw-controls,
    .jw-flag-overlay-open-related .jw-logo,
    .jw-flag-overlay-open-related .jw-title {
      display: none
    }

    .jwplayer.jw-flag-rightclick-open {
      overflow: visible
    }

    .jwplayer.jw-flag-rightclick-open .jw-rightclick {
      z-index: 16777215
    }

    .jw-flag-touch.jw-breakpoint-4 .jw-captions,
    .jw-flag-touch.jw-breakpoint-4 .jw-nextup-container,
    .jw-flag-touch.jw-breakpoint-5 .jw-captions,
    .jw-flag-touch.jw-breakpoint-5 .jw-nextup-container,
    .jw-flag-touch.jw-breakpoint-6 .jw-captions,
    .jw-flag-touch.jw-breakpoint-6 .jw-nextup-container,
    .jw-flag-touch.jw-breakpoint-7 .jw-captions,
    .jw-flag-touch.jw-breakpoint-7 .jw-nextup-container {
      bottom: 4.25em
    }

    .jw-flag-touch .jw-controlbar .jw-icon-volume {
      display: flex
    }

    .jw-flag-touch .jw-display,
    .jw-flag-touch .jw-display-container,
    .jw-flag-touch .jw-display-controls {
      pointer-events: none
    }

    .jw-flag-touch.jw-state-paused:not(.jw-breakpoint-1) .jw-display-icon-next,
    .jw-flag-touch.jw-state-paused:not(.jw-breakpoint-1) .jw-display-icon-rewind,
    .jw-flag-touch.jw-state-playing:not(.jw-breakpoint-1) .jw-display-icon-next,
    .jw-flag-touch.jw-state-playing:not(.jw-breakpoint-1) .jw-display-icon-rewind {
      display: none
    }

    .jw-flag-touch.jw-state-paused.jw-flag-dragging .jw-display {
      display: none
    }

    .jw-flag-audio-player {
      background-color: #000
    }

    .jw-flag-audio-player .jw-media {
      visibility: hidden
    }

    .jw-flag-audio-player .jw-title {
      background: 0 0
    }

    .jw-flag-audio-player:not(.jw-flag-live) .jw-spacer {
      display: none
    }

    .jw-flag-audio-player .jw-display,
    .jw-flag-audio-player .jw-nextup-container,
    .jw-flag-audio-player .jw-preview,
    .jw-flag-audio-player .jw-title {
      display: none
    }

    .jw-flag-audio-player .jw-controlbar {
      position: relative
    }

    .jw-flag-audio-player .jw-controlbar .jw-button-container {
      padding-right: 3px;
      padding-left: 0;
      justify-content: flex-start
    }

    .jw-flag-audio-player .jw-controlbar .jw-icon-inline,
    .jw-flag-audio-player .jw-controlbar .jw-icon-tooltip {
      display: none
    }

    .jw-flag-audio-player .jw-controlbar .jw-icon-airplay,
    .jw-flag-audio-player .jw-controlbar .jw-icon-cast,
    .jw-flag-audio-player .jw-controlbar .jw-icon-next,
    .jw-flag-audio-player .jw-controlbar .jw-icon-playback,
    .jw-flag-audio-player .jw-controlbar .jw-icon-rewind,
    .jw-flag-audio-player .jw-controlbar .jw-icon-volume,
    .jw-flag-audio-player .jw-controlbar .jw-logo-button,
    .jw-flag-audio-player .jw-controlbar .jw-text-duration,
    .jw-flag-audio-player .jw-controlbar .jw-text-elapsed,
    .jw-flag-audio-player .jw-controlbar .jw-text-live {
      display: flex;
      flex: 0 0 auto
    }

    .jw-flag-audio-player .jw-controlbar .jw-text-countdown,
    .jw-flag-audio-player .jw-controlbar .jw-text-duration {
      padding-right: 10px
    }

    .jw-flag-audio-player .jw-controlbar .jw-chapter-slider-time,
    .jw-flag-audio-player .jw-controlbar .jw-slider-time {
      flex: 0 1 auto;
      align-items: center;
      display: flex;
      order: 1
    }

    .jw-flag-audio-player .jw-controlbar .jw-icon-volume {
      margin-right: 0;
      transition: margin-right 150ms cubic-bezier(0, .25, .25, 1)
    }

    .jw-flag-audio-player .jw-controlbar .jw-icon-volume .jw-overlay {
      display: none
    }

    .jw-flag-audio-player .jw-controlbar .jw-horizontal-volume-container~.jw-chapter-slider-time,
    .jw-flag-audio-player .jw-controlbar .jw-horizontal-volume-container~.jw-slider-time {
      transition: opacity .3s, width .3s
    }

    .jw-flag-audio-player .jw-controlbar .jw-horizontal-volume-container.jw-open~.jw-chapter-slider-time,
    .jw-flag-audio-player .jw-controlbar .jw-horizontal-volume-container.jw-open~.jw-slider-time {
      flex: 1 1 auto;
      width: auto
    }

    .jw-flag-audio-player .jw-controlbar .jw-slider-volume~.jw-icon-volume {
      margin-right: 140px
    }

    .jw-flag-audio-player.jw-breakpoint-1 .jw-horizontal-volume-container.jw-open~.jw-chapter-slider-time,
    .jw-flag-audio-player.jw-breakpoint-1 .jw-horizontal-volume-container.jw-open~.jw-slider-time,
    .jw-flag-audio-player.jw-breakpoint-2 .jw-horizontal-volume-container.jw-open~.jw-chapter-slider-time,
    .jw-flag-audio-player.jw-breakpoint-2 .jw-horizontal-volume-container.jw-open~.jw-slider-time {
      opacity: 0
    }

    .jw-flag-audio-player.jw-flag-small-player .jw-text-duration,
    .jw-flag-audio-player.jw-flag-small-player .jw-text-elapsed {
      display: none
    }

    .jw-flag-audio-player.jw-flag-ads .jw-chapter-slider-time,
    .jw-flag-audio-player.jw-flag-ads .jw-slider-time {
      display: none
    }

    .jw-hidden {
      display: none
    }
  </style>
</head>

<body class="vsc-initialized" cz-shortcut-listen="true">
  <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-MN78SWW" height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
  <nav x-data="{navOpen: false}" class="top-0 z-30 py-3 w-full bg-white border-b-2 border-primary-400" data-ga-module="global_nav">
    <div class="flex relative justify-between items-center px-4 pl-3 mx-auto max-w-8xl">
      <button @click="navOpen = true; $nextTick(() => $refs.firstMenuItem.focus());" class="flex-shrink px-2" aria-label="Menu">
        <svg class="inline-block w-7 h-7 fill-current">
          <use href="/images/icons/spritemap.svg#sprite-hamburger"></use>
        </svg>
      </button>
      <a href="https://mashable.com" class="flex items-center mr-8 w-full xl:w-auto" aria-label="Home" data-ga-click="" data-ga-element="navigation_logo" data-ga-action="navigation_logo" data-ga-item="logo">
        <div x-data="{animate: false, reverse: false}" x-init="setTimeout(() => animate = true, 1000)">
          <svg x-ref="wordmark" id="mashable-wordmark-animated" class="inline-block -mb-3 w-40 h-11 fill-current hover:fill-secondary-100 animate" :class="{ 'animate': animate, 'animate-reverse': reverse }" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 2200 650" shape-rendering="geometricPrecision" text-rendering="geometricPrecision">
            <style>
              .animate #euSMf1FbiNs11_to {
                animation: euSMf1FbiNs11_to__to 1s linear 1 normal forwards
              }

              .animate-reverse #euSMf1FbiNs11_to {
                animation: euSMf1FbiNs11_to__back 1s linear 1 normal forwards
              }

              @keyframes euSMf1FbiNs11_to__to {
                0% {
                  transform: translate(2326.395841px, 218.229656px)
                }

                45% {
                  transform: translate(2326.395841px, 218.229656px)
                }

                73% {
                  transform: translate(662.384075px, 215.434656px)
                }

                100% {
                  transform: translate(662.384075px, 215.434656px)
                }
              }

              @keyframes euSMf1FbiNs11_to__back {
                0% {
                  transform: translate(662.384075px, 215.434656px)
                }

                45% {
                  transform: translate(662.384075px, 215.434656px)
                }

                73% {
                  transform: translate(2326.395841px, 218.229656px)
                }

                100% {
                  transform: translate(2326.395841px, 218.229656px)
                }
              }

              .animate #euSMf1FbiNs15_to {
                animation: euSMf1FbiNs15_to__to 1s linear 1 normal forwards
              }

              .animate-reverse #euSMf1FbiNs15_to {
                animation: euSMf1FbiNs15_to__back 1s linear 1 normal forwards
              }

              @keyframes euSMf1FbiNs15_to__to {
                0% {
                  transform: translate(144.277373px, 1260.854733px)
                }

                41% {
                  transform: translate(144.277373px, 331.868153px)
                }

                100% {
                  transform: translate(144.277373px, 331.868153px)
                }
              }

              @keyframes euSMf1FbiNs15_to__back {
                0% {
                  transform: translate(144.277373px, 331.868153px)
                }

                41% {
                  transform: translate(144.277373px, 331.868153px)
                }

                100% {
                  transform: translate(144.277373px, 1260.854733px)
                }
              }

              .animate #euSMf1FbiNs19_to {
                animation: euSMf1FbiNs19_to__to 1s linear 1 normal forwards
              }

              .animate-reverse #euSMf1FbiNs19_to {
                animation: euSMf1FbiNs19_to__back 1s linear 1 normal forwards
              }

              @keyframes euSMf1FbiNs19_to__to {
                0% {
                  transform: translate(144.277373px, 1003.724046px)
                }

                60% {
                  transform: translate(144.277373px, 1003.724046px)
                }

                80% {
                  transform: translate(145.277373px, 766.938989px)
                }

                100% {
                  transform: translate(145.277373px, 766.938989px)
                }
              }

              @keyframes euSMf1FbiNs19_to__back {
                0% {
                  transform: translate(145.277373px, 766.938989px)
                }

                60% {
                  transform: translate(145.277373px, 766.938989px)
                }

                80% {
                  transform: translate(144.277373px, 1003.724046px)
                }

                100% {
                  transform: translate(144.277373px, 1003.724046px)
                }
              }

              .animate #euSMf1FbiNs22_to {
                animation: euSMf1FbiNs22_to__to 1s linear 1 normal forwards
              }

              .animate-reverse #euSMf1FbiNs22_to {
                animation: euSMf1FbiNs22_to__back 1s linear 1 normal forwards
              }

              @keyframes euSMf1FbiNs22_to__to {
                0% {
                  transform: translate(5474.895347px, 1202.280706px)
                }

                60% {
                  transform: translate(5474.895347px, 1202.280706px)
                }

                90% {
                  transform: translate(13.364022px, 1202.286993px)
                }

                100% {
                  transform: translate(13.364022px, 1202.286993px)
                }
              }

              @keyframes euSMf1FbiNs22_to__back {
                0% {
                  transform: translate(13.364022px, 1202.286993px)
                }

                60% {
                  transform: translate(13.364022px, 1202.286993px)
                }

                90% {
                  transform: translate(5474.895347px, 1202.280706px)
                }

                100% {
                  transform: translate(5474.895347px, 1202.280706px)
                }
              }
            </style>
            <g id="euSMf1FbiNs2" transform="matrix(1 0 0 1 327.55203400000028 85.59466518500005)">
              <polygon id="euSMf1FbiNs3" points="221.679659,0.620000 221.679659,0.669994 221.589769,0.620000 143.495006,136.034612 65.410231,0.620000 65.320341,0.669994 65.320341,0.620000 0,0.620000 0,264.620000 65.320341,264.620000 65.320341,131.265154 119.594153,225.374460 119.594153,225.374460 129.252375,242.112558 157.747625,242.112558 170.312302,220.325034 221.679659,131.265154 221.679659,264.620000 287,264.620000 287,0.620000 221.679659,0.620000" fill="currentColor" stroke="none" stroke-width="1"></polygon>
              <path id="euSMf1FbiNs4" d="M442.390000,897.214899C442.390000,839.553909,481.940898,800.570000,521.811158,800.570000C549.465848,800.570000,567.150479,814.747785,577.439900,829.245519L583.867046,805.729194L642.390000,805.729194L642.390000,987.410806L583.867046,987.410806L577.439900,963.894481C567.150479,978.392215,549.495788,992.570000,521.811158,992.570000C481.920938,992.570000,442.390000,953.586091,442.390000,897.214899ZM574.545689,896.574999C574.545689,876.578124,562.010758,861.780436,543.677425,861.780436C524.715349,861.780436,511.841098,877.887919,511.841098,896.574999C511.841098,915.901979,524.705369,931.359564,543.677425,931.359564C562.010758,931.359564,574.545689,916.541879,574.545689,896.574999Z" transform="matrix(1 0 0 1 -127.05000000000000 -727.75999999999999)" fill="currentColor" stroke="none" stroke-width="1"></path>
              <path id="euSMf1FbiNs5" d="M812.223369,813.460670L812.223369,868.883557C799.336738,861.473171,775.822886,855.002834,755.848108,855.002834C744.261137,855.002834,738.132739,857.262952,738.132739,861.773187C738.132739,866.283422,744.581053,869.183573,762.296422,873.053774C793.538254,879.814126,825.110000,891.054712,825.110000,930.406761C825.110000,968.408741,795.117841,992.590000,740.712065,992.590000C712.719383,992.590000,689.825369,986.149665,674.359412,977.769228L674.359412,918.176124C690.785118,929.176697,710.759895,938.177166,737.492906,938.177166C751.019370,938.177166,758.747350,935.917048,758.747350,930.766780C758.747350,924.966478,749.089875,923.036377,727.185601,917.236075C699.192919,909.825689,672.110000,896.294985,672.110000,862.133205C672.110000,826.691359,701.102420,800.590000,752.628949,800.590000C774.213306,800.569999,797.077329,805.410251,812.223369,813.460670Z" transform="matrix(1 0 0 1 -127.05000000000000 -727.75999999999999)" fill="currentColor"
                stroke="none" stroke-width="1"></path>
              <path id="euSMf1FbiNs6" d="M920.501202,819.668695L920.822692,819.668695C930.869277,809.056557,947.697307,800.379339,967.438845,800.379339C1005.957451,800.379339,1030.230000,823.837750,1030.230000,864.650596L1030.230000,986.760000L960.968845,986.760000L960.968845,882.643356C960.968845,867.862164,954.488798,861.439028,941.880334,861.439028C928.608796,861.439028,920.511248,870.116246,920.511248,881.685870L920.511248,986.760000L851.230000,986.760000L851.230000,727.760000L920.501202,727.760000Z" transform="matrix(1 0 0 1 -127.05000000000000 -727.75999999999999)" fill="currentColor" stroke="none" stroke-width="1"></path>
              <path id="euSMf1FbiNs7" d="M1053.890000,897.214899C1053.890000,839.553909,1093.440898,800.570000,1133.311158,800.570000C1160.955868,800.570000,1178.650479,814.747785,1188.939900,829.245519L1195.367046,805.729194L1253.890000,805.729194L1253.890000,987.410806L1195.367046,987.410806L1188.939900,963.894481C1178.650479,978.392215,1160.995788,992.570000,1133.311158,992.570000C1093.440898,992.570000,1053.890000,953.586091,1053.890000,897.214899ZM1186.045689,896.574999C1186.045689,876.578124,1173.500778,861.780436,1155.177425,861.780436C1136.215349,861.780436,1123.341098,877.887919,1123.341098,896.574999C1123.341098,915.901979,1136.205369,931.359564,1155.177425,931.359564C1173.500778,931.359564,1186.045689,916.541879,1186.045689,896.574999Z" transform="matrix(1 0 0 1 -127.05000000000000 -727.75999999999999)" fill="currentColor" stroke="none" stroke-width="1"></path>
              <path id="euSMf1FbiNs8" d="M1357.242375,827.972530C1367.531796,812.559819,1388.759341,800.347289,1411.264331,800.347289C1453.060739,800.347289,1488.430000,837.931972,1488.430000,896.053644C1488.430000,954.175317,1449.847166,991.760000,1408.050758,991.760000C1381.683493,991.760000,1363.679501,977.623374,1353.380100,963.167726L1346.952954,986.615783L1288.430000,986.615783L1288.430000,727.760000L1357.292275,727.760000ZM1356.603653,896.073583C1356.603653,916.311490,1369.138583,930.757168,1387.471916,930.757168C1406.114631,930.757168,1418.978902,915.663478,1418.978902,896.392605C1418.978902,877.450722,1406.114631,861.380029,1387.142575,861.380029C1369.138583,861.380029,1356.603653,876.154698,1356.603653,896.073583Z" transform="matrix(1 0 0 1 -127.05000000000000 -727.75999999999999)" fill="currentColor" stroke="none" stroke-width="1"></path>
              <path id="euSMf1FbiNs9" d="M1514.920000,727.760000L1583.920000,727.760000L1583.920000,986.760000L1514.920000,986.760000Z" transform="matrix(1 0 0 1 -127.05000000000000 -727.75999999999999)" fill="currentColor" stroke="none" stroke-width="1"></path>
              <path id="euSMf1FbiNs10" d="M1789.715993,914.612181L1676.080565,914.612181C1684.748180,932.009463,1704.653808,940.058205,1726.163442,940.058205C1745.740298,940.058205,1763.075529,933.619211,1779.125562,926.530319L1779.125562,979.042114C1760.505132,988.040708,1739.274456,992.570000,1716.529437,992.570000C1647.198476,992.570000,1609.320000,951.656393,1609.320000,896.894949C1609.320000,840.193809,1649.768872,800.570000,1703.368610,800.570000C1751.837517,800.570000,1791.320000,832.784966,1791.320000,893.675452C1791.329416,900.685633,1790.793115,907.685839,1789.715993,914.612181ZM1726.482251,876.618117C1726.163442,864.060080,1717.814635,853.101792,1703.049801,853.101792C1689.570164,853.101792,1678.332152,862.100386,1673.520131,876.618117Z" transform="matrix(1 0 0 1 -127.05000000000000 -727.75999999999999)" fill="currentColor" stroke="none" stroke-width="1"></path>
            </g>
            <g id="euSMf1FbiNs11_to" transform="translate(2326.395841,218.229656)">
              <polygon id="euSMf1FbiNs11" points="2994.404278,1003.400000 1373.292899,1003.400000 1167.283721,1164.601894 916.220000,1387.164859 2994.404278,1387.164859 2994.404278,1003.400000" transform="scale(0.922440,0.922440) translate(-1188.489990,-1195.614990)" fill="rgb(255,255,255)" stroke="none" stroke-width="1"></polygon>
            </g>
            <g id="euSMf1FbiNs12" transform="matrix(1 0 0 1 0 -4)" mask="url(#euSMf1FbiNs14)">
              <polygon id="euSMf1FbiNs13" points="-0.124694,383.594023 0.000007,787.553039 0.000007,842.876599 0.000007,1388.080235 457.526979,1383.558047 457.526979,628.534061 457.526979,573.210501 458.403224,0 -0.124694,383.594023" transform="matrix(0.44272113509609 0 0 0.43153125079610 43.00491365445770 18.64549964827933)" fill="currentColor" stroke="none" stroke-width="1"></polygon>
              <mask id="euSMf1FbiNs14" mask-type="alpha">
                <g id="euSMf1FbiNs15_to" transform="translate(144.277373,1260.854733)">
                  <polygon id="euSMf1FbiNs15" points="-0.124694,383.429007 -0.000073,787.214245 -0.000073,842.514006 -0.000073,1387.483103 452.947836,1385.590461 457.231375,628.263675 457.231375,572.963914 458.107053,0 -0.124694,383.429007" transform="scale(0.938390,0.938390) translate(-228.750000,-725.840023)" fill="currentColor" stroke="none" stroke-width="1"></polygon>
                </g>
              </mask>
            </g>
            <g id="euSMf1FbiNs16" transform="matrix(1 0 0 1 0 -4)" mask="url(#euSMf1FbiNs18)">
              <polygon id="euSMf1FbiNs17" points="-0.124694,383.160079 -0.000226,786.662112 -0.000226,841.923087 3.273209,1314.095486 489.720027,1314.095486 486.446592,1026.057228 456.669898,572.562051 457.544501,0 -0.124694,383.160079" transform="matrix(0.44299752680467 0 0 0.45278292652264 42.94168905111819 3.22018284927219)" fill="rgb(255,255,255)" stroke="none" stroke-width="1"></polygon>
              <mask id="euSMf1FbiNs18" mask-type="alpha">
                <g id="euSMf1FbiNs19_to" transform="translate(144.277373,1003.724046)">
                  <polygon id="euSMf1FbiNs19" points="-0.124694,383.022539 -0.000070,786.379730 -0.000070,841.620869 -0.000070,1386.012253 452.957811,1384.121616 457.241445,627.597662 457.241445,572.356523 458.117142,0 -0.124694,383.022539" transform="scale(0.442998,0.442998) translate(-228.750000,-725.840023)" fill="rgb(0,0,0)" stroke="none" stroke-width="1"></polygon>
                </g>
              </mask>
            </g>
            <g id="euSMf1FbiNs20" transform="matrix(0.44964075354686 0 0 0.44964075354686 -85.59085363638468 -7.93672154314413)">
              <g id="euSMf1FbiNs21" mask="url(#euSMf1FbiNs35)">
                <g id="euSMf1FbiNs22_to" transform="translate(5474.895347,1202.280706)">
                  <g id="euSMf1FbiNs22" transform="translate(-1027.768089,-1215.630979)">
                    <polygon id="euSMf1FbiNs23" points="632.859436,1003.400000 545.466598,1003.400000 87.980000,1388.151601 174.242154,1388.151601 632.859436,1003.400000" transform="matrix(1 0 0 1 1278.41180423626361 20.01597926568184)" fill="currentColor" stroke="none" stroke-width="1"></polygon>
                    <polygon id="euSMf1FbiNs24" points="632.859436,1003.400000 545.466598,1003.400000 87.980000,1388.151601 174.242154,1388.151601 632.859436,1003.400000" transform="matrix(1 0 0 1 279.34607797268734 20.01597926568184)" fill="currentColor" stroke="none" stroke-width="1"></polygon>
                    <polygon id="euSMf1FbiNs25" points="797.209436,1003.400000 709.824999,1003.400000 252.330000,1388.151601 338.593738,1388.151601 797.209436,1003.400000" transform="matrix(1 0 0 1 1278.41180423626383 20.01597926568184)" fill="currentColor" stroke="none" stroke-width="1"></polygon>
                    <polygon id="euSMf1FbiNs26" points="797.209436,1003.400000 709.824999,1003.400000 252.330000,1388.151601 338.593738,1388.151601 797.209436,1003.400000" transform="matrix(1 0 0 1 279.34607797268734 20.01597926568184)" fill="currentColor" stroke="none" stroke-width="1"></polygon>
                    <polygon id="euSMf1FbiNs27" points="963.879436,1003.400000 876.494999,1003.400000 419,1388.151601 505.263738,1388.151601 963.879436,1003.400000" transform="matrix(1 0 0 1 1278.41180423626383 20.01597926568184)" fill="currentColor" stroke="none" stroke-width="1"></polygon>
                    <polygon id="euSMf1FbiNs28" points="963.879436,1003.400000 876.494999,1003.400000 419,1388.151601 505.263738,1388.151601 963.879436,1003.400000" transform="matrix(1 0 0 1 279.34607797268734 20.01597926568184)" fill="currentColor" stroke="none" stroke-width="1"></polygon>
                    <polygon id="euSMf1FbiNs29" points="1128.219436,1003.400000 1040.834999,1003.400000 583.340000,1388.151601 669.603738,1388.151601 1128.219436,1003.400000" transform="matrix(1 0 0 1 1277.96797339258319 22.59894977233307)" fill="currentColor" stroke="none" stroke-width="1"></polygon>
                    <polygon id="euSMf1FbiNs30" points="1128.219436,1003.400000 1040.834999,1003.400000 583.340000,1388.151601 669.603738,1388.151601 1128.219436,1003.400000" transform="matrix(1 0 0 1 278.90224712900635 22.59894977233307)" fill="currentColor" stroke="none" stroke-width="1"></polygon>
                    <polygon id="euSMf1FbiNs31" points="1296.749436,1003.400000 1209.356598,1003.400000 751.870000,1388.151601 838.132154,1388.151601 1296.749436,1003.400000" transform="matrix(1 0 0 1 1278.41180423626565 20.01597926568184)" fill="currentColor" stroke="none" stroke-width="1"></polygon>
                    <polygon id="euSMf1FbiNs32" points="1296.749436,1003.400000 1209.356598,1003.400000 751.870000,1388.151601 838.132154,1388.151601 1296.749436,1003.400000" transform="matrix(1 0 0 1 279.34607797268723 20.01597926568184)" fill="currentColor" stroke="none" stroke-width="1"></polygon>
                    <polygon id="euSMf1FbiNs33" points="1461.099436,1003.400000 1373.714999,1003.400000 916.220000,1388.151601 1002.483738,1388.151601 1461.099436,1003.400000" transform="matrix(1 0 0 1 1278.41180423626429 20.01597926568184)" fill="currentColor" stroke="none" stroke-width="1"></polygon>
                    <polygon id="euSMf1FbiNs34" points="1461.099436,1003.400000 1373.714999,1003.400000 916.220000,1388.151601 1002.483738,1388.151601 1461.099436,1003.400000" transform="matrix(1 0 0 1 279.34607797268723 20.01597926568184)" fill="currentColor" stroke="none" stroke-width="1"></polygon>
                  </g>
                </g>
                <mask id="euSMf1FbiNs35" mask-type="alpha">
                  <rect id="euSMf1FbiNs36" width="668.168909" height="172.855395" rx="0" ry="0" transform="matrix(6.63914743458407 0 0 3.36734374316499 306.31173456484885 871.81575675521890)" fill="rgb(210,219,237)" stroke="none" stroke-width="0"></rect>
                </mask>
              </g>
            </g>
            <polygon id="euSMf1FbiNs37" points="221.679659,0.620000 221.679659,0.669994 221.589769,0.620000 143.495006,136.034612 65.410231,0.620000 65.320341,0.669994 65.320341,0.620000 0,0.620000 0,264.620000 65.320341,264.620000 65.320341,131.265154 119.594153,225.374460 119.594153,225.374460 129.252375,242.112558 157.747625,242.112558 170.312302,220.325034 221.679659,131.265154 221.679659,264.620000 287,264.620000 287,0.620000 221.679659,0.620000" transform="matrix(1 0 0 1 327.55203400000005 85.78466519499989)" fill="currentColor" stroke="none" stroke-width="1"></polygon>
          </svg>
        </div>
      </a>
      <div class="flex hidden flex-grow items-center space-x-6 text-sm font-semibold tracking-wide xl:block" data-ga-element="navigation_link" data-ga-action="navigation_link" data-ga-item="title">
        <a href="/category/prime-day" class="text-secondary-300 hover:text-primary-400" data-ga-click="" data-ga-label="$text">Amazon Prime Day</a>
        <a href="https://mashable.com/tech" data-ga-click="" data-ga-label="$text">Tech</a>
        <a href="https://mashable.com/science" data-ga-click="" data-ga-label="$text">Science</a>
        <a href="https://mashable.com/life" data-ga-click="" data-ga-label="$text">Life</a>
        <a href="https://mashable.com/category/social-good" data-ga-click="" data-ga-label="$text">Social Good</a>
        <a href="https://mashable.com/entertainment" data-ga-click="" data-ga-label="$text">Entertainment</a>
        <a href="https://mashable.com/deals" data-ga-click="" data-ga-label="$text">Deals</a>
        <a href="https://mashable.com/shopping" data-ga-click="" data-ga-label="$text">Shopping</a>
        <a href="https://mashable.com/category/travel" data-ga-click="" data-ga-label="$text">Travel</a>
      </div>
      <div x-data="window.navSearch()" x-init="init('prod_site_search_autocomplete')" @click="searchOpen = true" @click.outside="searchOpen = false" class="absolute right-0 py-3 mx-4 lg:py-2 lg:mx-4 lg:max-w-xs xl:max-w-sm lg:min-w-20 w-6" :class="{ 'w-64 mx-0 bg-white ': searchOpen, 'w-6 ': !searchOpen}">
        <div class="relative cursor-pointer">
          <input x-ref="searchInput" x-model="query" x-on:input="evt=>query=evt.target.value" x-on:keyup="onKeyUp" x-on:keydown="onKeyDown" :class="{ 'visible': searchOpen, 'invisible': !searchOpen }" aria-label="Search Mashable Content" placeholder="Search" class="invisible py-1 pr-2 pl-8 w-full text-sm lg:border focus:outline-none lg:focus:border-gray-400">
          <svg x-show="searchOpen" class="inline-block absolute top-0 left-0 mt-2 ml-2 w-4 h-4 fill-current" style="display: none;">
            <use href="/images/icons/spritemap.svg#sprite-search-solid"></use>
          </svg>
          <div x-show="!searchOpen" class="flex absolute top-1 flex-row justify-between py-1 w-full">
            <div class="flex absolute top-0 flex-row justify-between py-0.5 w-full">
              <div class="flex flex-row items-center w-full">
                <button class="flex"><svg class="inline-block ml-2 w-4 h-4 fill-current">
                    <use href="/images/icons/spritemap.svg#sprite-search-solid"></use>
                  </svg></button>
                <span x-show="!searchOpen" class="hidden ml-2 text-sm text-base font-semibold lg:block">Search</span>
              </div>
            </div>
            <button x-show="searchOpen" @click.stop="searchOpen = false" class="inline-block absolute right-2 text-sm font-semibold lg:hidden" style="display: none;">Cancel</button>
          </div>
          <div x-show="searchOpen" class="absolute z-40 w-full bg-white shadow" style="display: none;">
            <template x-for="[key, value] in Object.entries(results)" :key="key" hidden="">
              <ul class="my-2" x-show="value &amp;&amp; value.length > 0">
                <li class="py-2 pr-5 pl-8 text-sm text-gray-600" x-text="key + ':'"></li>
                <template x-for="(item, index) in value" :key="index" hidden="">
                  <li class="" :class="{'bg-gray-100': index + '-' + key  === selectedIndex}" @mouseenter="selectedIndex = index + '-' + key">
                    <a class="block py-2 pr-5 pl-8 leading-tight no-underline break-words cursor-pointer transition-bg" :href="item.url" :name="item.title" x-html="item.title">
                      Search Result
                    </a>
                  </li>
                </template>
                <hr class="my-4 mr-5 ml-8 border border-gray-100 border-1">
              </ul>
            </template>
          </div>
        </div>
      </div>
    </div>
    <div x-show="navOpen" x-trap="navOpen" @click.outside="navOpen = false" x-transition:enter="transition duration-300 ease-out" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-1" x-transition:leave="transition duration-300 ease-out" x-transition:leave-end="opacity-0" class="flex fixed top-0 z-30 flex-col w-screen h-screen bg-white shadow-md" style="max-width: 400px; display: none;">
      <div class="flex relative px-4 mt-4">
        <button @click="navOpen = false" x-ref="firstMenuItem" class="absolute" aria-label="Close">
          <svg class="inline-block w-6 h-6 fill-current">
            <use href="/images/icons/spritemap.svg#sprite-times-solid"></use>
          </svg>
        </button>
        <div class="relative flex-shrink mx-auto text-center text-primary-400">
          <svg class="inline-block w-6 h-6 fill-current">
            <use href="/images/icons/spritemap.svg#sprite-mashable-m"></use>
          </svg>
          <svg class="absolute top-0 left-0 -mt-1 -ml-5 w-12 h-12 fill-current">
            <use href="/images/icons/spritemap.svg#sprite-logomark"></use>
          </svg>
        </div>
      </div>
      <div class="flex overflow-auto overscroll-none flex-col flex-grow py-6 px-8 mt-6" data-ga-element="side_navigation_link" data-ga-action="side_navigation_link" data-ga-item="title">
        <a href="/category/prime-day" data-ga-click="" data-ga-label="$text" class="mt-6 uppercase text-secondary-300 header-300 hover:text-primary-400">Amazon Prime Day</a>
        <div x-data="{drawerOpen: false}" class="mt-6">
          <button @click="drawerOpen = !drawerOpen" class="uppercase cursor-pointer select-none header-300">
            <span class="font-semibold">Tech</span>
            <div class="inline-block relative">
              <svg x-show="drawerOpen" class="inline-block mb-1 w-5 h-5 text-secondary-300" style="display: none;">
                <use href="/images/icons/spritemap.svg#sprite-minus-solid"></use>
              </svg>
              <svg x-show="!drawerOpen" class="inline-block mb-1 w-5 h-5 text-secondary-300">
                <use href="/images/icons/spritemap.svg#sprite-plus-solid"></use>
              </svg>
            </div>
          </button>
          <div x-show="drawerOpen" class="text-base" style="display: none;">
            <a href="https://mashable.com/category/apps-and-software" class="block mt-4" data-ga-click="" data-ga-label="$text">Apps &amp; Software</a>
            <a href="https://mashable.com/category/artificial-intelligence" class="block mt-4" data-ga-click="" data-ga-label="$text">Artificial Intelligence</a>
            <a href="https://mashable.com/category/cybersecurity" class="block mt-4" data-ga-click="" data-ga-label="$text">Cybersecurity</a>
            <a href="https://mashable.com/category/cryptocurrency" class="block mt-4" data-ga-click="" data-ga-label="$text">Cryptocurrency</a>
            <a href="https://mashable.com/category/mobile" class="block mt-4" data-ga-click="" data-ga-label="$text">Mobile</a>
            <a href="https://mashable.com/category/smart-home" class="block mt-4" data-ga-click="" data-ga-label="$text">Smart Home</a>
            <a href="https://mashable.com/category/social-media" class="block mt-4" data-ga-click="" data-ga-label="$text">Social Media</a>
            <a href="https://mashable.com/category/tech-industry" class="block mt-4" data-ga-click="" data-ga-label="$text">Tech Industry</a>
            <a href="https://mashable.com/category/transportation" class="block mt-4" data-ga-click="" data-ga-label="$text">Transportation</a>
            <a href="https://mashable.com/tech" class="block mt-4 font-bold" data-ga-click="" data-ga-label="$text">All Tech</a>
          </div>
        </div>
        <div x-data="{drawerOpen: false}" class="mt-6">
          <button @click="drawerOpen = !drawerOpen" class="uppercase cursor-pointer select-none header-300">
            <span class="font-semibold">Science</span>
            <div class="inline-block relative">
              <svg x-show="drawerOpen" class="inline-block mb-1 w-5 h-5 text-secondary-300" style="display: none;">
                <use href="/images/icons/spritemap.svg#sprite-minus-solid"></use>
              </svg>
              <svg x-show="!drawerOpen" class="inline-block mb-1 w-5 h-5 text-secondary-300">
                <use href="/images/icons/spritemap.svg#sprite-plus-solid"></use>
              </svg>
            </div>
          </button>
          <div x-show="drawerOpen" class="text-base" style="display: none;">
            <a href="https://mashable.com/category/space" class="block mt-4" data-ga-click="" data-ga-label="$text">Space</a>
            <a href="https://mashable.com/category/climate-change" class="block mt-4" data-ga-click="" data-ga-label="$text">Climate Change</a>
            <a href="https://mashable.com/category/environment" class="block mt-4" data-ga-click="" data-ga-label="$text">Environment</a>
            <a href="https://mashable.com/videos/category/science" class="block mt-4" data-ga-click="" data-ga-label="$text">Video</a>
            <a href="https://mashable.com/science" class="block mt-4 font-bold" data-ga-click="" data-ga-label="$text">All Science</a>
          </div>
        </div>
        <div x-data="{drawerOpen: false}" class="mt-6">
          <button @click="drawerOpen = !drawerOpen" class="uppercase cursor-pointer select-none header-300">
            <span class="font-semibold">Life</span>
            <div class="inline-block relative">
              <svg x-show="drawerOpen" class="inline-block mb-1 w-5 h-5 text-secondary-300" style="display: none;">
                <use href="/images/icons/spritemap.svg#sprite-minus-solid"></use>
              </svg>
              <svg x-show="!drawerOpen" class="inline-block mb-1 w-5 h-5 text-secondary-300">
                <use href="/images/icons/spritemap.svg#sprite-plus-solid"></use>
              </svg>
            </div>
          </button>
          <div x-show="drawerOpen" class="text-base" style="display: none;">
            <a href="https://mashable.com/category/digital-culture" class="block mt-4" data-ga-click="" data-ga-label="$text">Digital Culture</a>
            <a href="https://mashable.com/category/family-parenting" class="block mt-4" data-ga-click="" data-ga-label="$text">Family &amp; Parenting</a>
            <a href="https://mashable.com/category/health-wellness" class="block mt-4" data-ga-click="" data-ga-label="$text">Health &amp; Wellness</a>
            <a href="https://mashable.com/category/sex-dating-relationships" class="block mt-4" data-ga-click="" data-ga-label="$text">Sex, Dating &amp; Relationships</a>
            <a href="https://mashable.com/category/sleep" class="block mt-4" data-ga-click="" data-ga-label="$text">Sleep</a>
            <a href="https://mashable.com/category/careers" class="block mt-4" data-ga-click="" data-ga-label="$text">Careers</a>
            <a href="https://mashable.com/category/mental-health" class="block mt-4" data-ga-click="" data-ga-label="$text">Mental Health</a>
            <a href="https://mashable.com/life" class="block mt-4 font-bold" data-ga-click="" data-ga-label="$text">All Life</a>
          </div>
        </div>
        <div x-data="{drawerOpen: false}" class="mt-6">
          <button @click="drawerOpen = !drawerOpen" class="uppercase cursor-pointer select-none header-300">
            <span class="font-semibold">Social Good</span>
            <div class="inline-block relative">
              <svg x-show="drawerOpen" class="inline-block mb-1 w-5 h-5 text-secondary-300" style="display: none;">
                <use href="/images/icons/spritemap.svg#sprite-minus-solid"></use>
              </svg>
              <svg x-show="!drawerOpen" class="inline-block mb-1 w-5 h-5 text-secondary-300">
                <use href="/images/icons/spritemap.svg#sprite-plus-solid"></use>
              </svg>
            </div>
          </button>
          <div x-show="drawerOpen" class="text-base" style="display: none;">
            <a href="https://mashable.com/category/activism" class="block mt-4" data-ga-click="" data-ga-label="$text">Activism</a>
            <a href="https://mashable.com/category/gender" class="block mt-4" data-ga-click="" data-ga-label="$text">Gender</a>
            <a href="https://mashable.com/category/lgbtq" class="block mt-4" data-ga-click="" data-ga-label="$text">LGBTQ</a>
            <a href="https://mashable.com/category/racism" class="block mt-4" data-ga-click="" data-ga-label="$text">Racial Justice</a>
            <a href="https://mashable.com/category/sustainability" class="block mt-4" data-ga-click="" data-ga-label="$text">Sustainability</a>
            <a href="https://mashable.com/category/politics" class="block mt-4" data-ga-click="" data-ga-label="$text">Politics</a>
            <a href="https://mashable.com/category/social-good" class="block mt-4 font-bold" data-ga-click="" data-ga-label="$text">All Social Good</a>
          </div>
        </div>
        <div x-data="{drawerOpen: false}" class="mt-6">
          <button @click="drawerOpen = !drawerOpen" class="uppercase cursor-pointer select-none header-300">
            <span class="font-semibold">Entertainment</span>
            <div class="inline-block relative">
              <svg x-show="drawerOpen" class="inline-block mb-1 w-5 h-5 text-secondary-300" style="display: none;">
                <use href="/images/icons/spritemap.svg#sprite-minus-solid"></use>
              </svg>
              <svg x-show="!drawerOpen" class="inline-block mb-1 w-5 h-5 text-secondary-300">
                <use href="/images/icons/spritemap.svg#sprite-plus-solid"></use>
              </svg>
            </div>
          </button>
          <div x-show="drawerOpen" class="text-base" style="display: none;">
            <a href="https://mashable.com/category/games" class="block mt-4" data-ga-click="" data-ga-label="$text">Games</a>
            <a href="https://mashable.com/category/movies" class="block mt-4" data-ga-click="" data-ga-label="$text">Movies</a>
            <a href="https://mashable.com/category/podcasts" class="block mt-4" data-ga-click="" data-ga-label="$text">Podcasts</a>
            <a href="https://mashable.com/category/tv-shows" class="block mt-4" data-ga-click="" data-ga-label="$text">TV Shows</a>
            <a href="https://mashable.com/videos/category/entertainment" class="block mt-4" data-ga-click="" data-ga-label="$text">Video</a>
            <a href="https://mashable.com/reviews/category/entertainment" class="block mt-4" data-ga-click="" data-ga-label="$text">Reviews</a>
            <a href="https://mashable.com/category/watch-guides" class="block mt-4" data-ga-click="" data-ga-label="$text">Watch Guides</a>
            <a href="https://mashable.com/entertainment" class="block mt-4 font-bold" data-ga-click="" data-ga-label="$text">All Entertainment</a>
          </div>
        </div>
        <div x-data="{drawerOpen: false}" class="mt-6">
          <button @click="drawerOpen = !drawerOpen" class="uppercase cursor-pointer select-none header-300">
            <span class="font-semibold">SHOP THE BEST</span>
            <div class="inline-block relative">
              <svg x-show="drawerOpen" class="inline-block mb-1 w-5 h-5 text-secondary-300" style="display: none;">
                <use href="/images/icons/spritemap.svg#sprite-minus-solid"></use>
              </svg>
              <svg x-show="!drawerOpen" class="inline-block mb-1 w-5 h-5 text-secondary-300">
                <use href="/images/icons/spritemap.svg#sprite-plus-solid"></use>
              </svg>
            </div>
          </button>
          <div x-show="drawerOpen" class="text-base" style="display: none;">
            <a href="https://mashable.com/roundups/category/house-home" class="block mt-4" data-ga-click="" data-ga-label="$text">Home</a>
            <a href="https://mashable.com/roundups/category/tech" class="block mt-4" data-ga-click="" data-ga-label="$text">Tech</a>
            <a href="https://mashable.com/roundups/category/kitchen" class="block mt-4" data-ga-click="" data-ga-label="$text">Kitchen</a>
            <a href="https://mashable.com/roundups/category/fitness" class="block mt-4" data-ga-click="" data-ga-label="$text">Fitness</a>
            <a href="https://mashable.com/roundups/category/sex-toys" class="block mt-4" data-ga-click="" data-ga-label="$text">Sex Toys</a>
            <a href="https://mashable.com/roundups/category/apps-and-software" class="block mt-4" data-ga-click="" data-ga-label="$text">Apps &amp; Software</a>
            <a href="https://mashable.com/roundups/category/sex-dating-relationships" class="block mt-4" data-ga-click="" data-ga-label="$text">Dating</a>
            <a href="https://mashable.com/gifts" class="block mt-4" data-ga-click="" data-ga-label="$text">Gift Guides</a>
            <a href="https://mashable.com/mashable-choice" class="block mt-4" data-ga-click="" data-ga-label="$text">Mashable Choice</a>
            <a href="https://mashable.com/category/mashable-selects" class="block mt-4" data-ga-click="" data-ga-label="$text">Mashable Selects</a>
            <a href="https://mashable.com/roundups/category/gaming" class="block mt-4" data-ga-click="" data-ga-label="$text">Gaming</a>
            <a href="https://mashable.com/roundups" class="block mt-4 font-bold" data-ga-click="" data-ga-label="$text">All Best Products</a>
          </div>
        </div>
        <div x-data="{drawerOpen: false}" class="mt-6">
          <button @click="drawerOpen = !drawerOpen" class="uppercase cursor-pointer select-none header-300">
            <span class="font-semibold">Travel</span>
            <div class="inline-block relative">
              <svg x-show="drawerOpen" class="inline-block mb-1 w-5 h-5 text-secondary-300" style="display: none;">
                <use href="/images/icons/spritemap.svg#sprite-minus-solid"></use>
              </svg>
              <svg x-show="!drawerOpen" class="inline-block mb-1 w-5 h-5 text-secondary-300">
                <use href="/images/icons/spritemap.svg#sprite-plus-solid"></use>
              </svg>
            </div>
          </button>
          <div x-show="drawerOpen" class="text-base" style="display: none;">
            <a href="https://mashable.com/deals/category/travel" class="block mt-4" data-ga-click="" data-ga-label="$text">Deals</a>
            <a href="https://mashable.com/roundups/category/travel" class="block mt-4" data-ga-click="" data-ga-label="$text">Best of</a>
            <a href="https://mashable.com/category/travel" class="block mt-4 font-bold" data-ga-click="" data-ga-label="$text">All Travel</a>
          </div>
        </div>
        <div x-data="{drawerOpen: false}" class="mt-6">
          <button @click="drawerOpen = !drawerOpen" class="uppercase cursor-pointer select-none header-300">
            <span class="font-semibold">Product Reviews</span>
            <div class="inline-block relative">
              <svg x-show="drawerOpen" class="inline-block mb-1 w-5 h-5 text-secondary-300" style="display: none;">
                <use href="/images/icons/spritemap.svg#sprite-minus-solid"></use>
              </svg>
              <svg x-show="!drawerOpen" class="inline-block mb-1 w-5 h-5 text-secondary-300">
                <use href="/images/icons/spritemap.svg#sprite-plus-solid"></use>
              </svg>
            </div>
          </button>
          <div x-show="drawerOpen" class="text-base" style="display: none;">
            <a href="https://mashable.com/reviews/category/tech" class="block mt-4" data-ga-click="" data-ga-label="$text">Tech</a>
            <a href="https://mashable.com/reviews/category/life" class="block mt-4" data-ga-click="" data-ga-label="$text">Life</a>
            <a href="https://mashable.com/reviews/category/beauty" class="block mt-4" data-ga-click="" data-ga-label="$text">Beauty Tech</a>
            <a href="https://mashable.com/reviews/category/kitchen" class="block mt-4" data-ga-click="" data-ga-label="$text">Kitchen</a>
            <a href="https://mashable.com/reviews/category/family-parenting" class="block mt-4" data-ga-click="" data-ga-label="$text">Kids/Toys</a>
            <a href="https://mashable.com/reviews/category/gaming" class="block mt-4" data-ga-click="" data-ga-label="$text">Gaming</a>
            <a href="https://mashable.com/reviews/category/fitness" class="block mt-4" data-ga-click="" data-ga-label="$text">Fitness</a>
            <a href="https://mashable.com/reviews/category/sex-toys" class="block mt-4" data-ga-click="" data-ga-label="$text">Sex Toys</a>
            <a href="https://mashable.com/reviews/category/apps-software" class="block mt-4" data-ga-click="" data-ga-label="$text">Apps &amp; Software</a>
            <a href="https://mashable.com/review" class="block mt-4 font-bold" data-ga-click="" data-ga-label="$text">All Product Reviews</a>
          </div>
        </div>
        <div x-data="{drawerOpen: false}" class="mt-6">
          <button @click="drawerOpen = !drawerOpen" class="uppercase cursor-pointer select-none header-300">
            <span class="font-semibold">DEALS</span>
            <div class="inline-block relative">
              <svg x-show="drawerOpen" class="inline-block mb-1 w-5 h-5 text-secondary-300" style="display: none;">
                <use href="/images/icons/spritemap.svg#sprite-minus-solid"></use>
              </svg>
              <svg x-show="!drawerOpen" class="inline-block mb-1 w-5 h-5 text-secondary-300">
                <use href="/images/icons/spritemap.svg#sprite-plus-solid"></use>
              </svg>
            </div>
          </button>
          <div x-show="drawerOpen" class="text-base" style="display: none;">
            <a href="https://mashable.com/deals/category/house-home" class="block mt-4" data-ga-click="" data-ga-label="$text">Home</a>
            <a href="https://mashable.com/deals/category/tech" class="block mt-4" data-ga-click="" data-ga-label="$text">Tech</a>
            <a href="https://mashable.com/deals/category/tvs" class="block mt-4" data-ga-click="" data-ga-label="$text">TV</a>
            <a href="https://mashable.com/deals/category/laptops" class="block mt-4" data-ga-click="" data-ga-label="$text">Laptop</a>
            <a href="https://mashable.com/deals/category/vpn" class="block mt-4" data-ga-click="" data-ga-label="$text">VPN</a>
            <a href="https://mashable.com/deals/category/vacuums" class="block mt-4" data-ga-click="" data-ga-label="$text">Vacuum</a>
            <a href="https://mashable.com/deals/category/gaming" class="block mt-4" data-ga-click="" data-ga-label="$text">Gaming</a>
            <a href="https://mashable.com/deals/category/streaming" class="block mt-4" data-ga-click="" data-ga-label="$text">Streaming</a>
            <a href="https://mashable.com/deals/category/apple" class="block mt-4" data-ga-click="" data-ga-label="$text">Apple</a>
            <a href="https://shop.mashable.com/" class="block mt-4" data-ga-click="" data-ga-label="$text" target="_blank" rel="noopener">
              Mashable Shop<span class="sr-only">(opens in a new tab)</span>
              <svg class="inline-block mb-1 ml-1 w-4 h-4 text-secondary-300">
                <use href="/images/icons/spritemap.svg#sprite-external-link"></use>
              </svg>
            </a>
            <a href="https://mashable.com/deals" class="block mt-4 font-bold" data-ga-click="" data-ga-label="$text">All Deals</a>
          </div>
        </div>
        <div class="mt-6">
          <a href="https://mashable.com/newsletters" class="uppercase cursor-pointer select-none header-300" data-ga-click="" data-ga-label="$text"><span class="font-semibold">Newsletters</span></a>
        </div>
        <div x-data="{drawerOpen: false}" class="mt-6">
          <button @click="drawerOpen = !drawerOpen" class="uppercase cursor-pointer select-none header-300">
            <span class="font-semibold">VIDEOS</span>
            <div class="inline-block relative">
              <svg x-show="drawerOpen" class="inline-block mb-1 w-5 h-5 text-secondary-300" style="display: none;">
                <use href="/images/icons/spritemap.svg#sprite-minus-solid"></use>
              </svg>
              <svg x-show="!drawerOpen" class="inline-block mb-1 w-5 h-5 text-secondary-300">
                <use href="/images/icons/spritemap.svg#sprite-plus-solid"></use>
              </svg>
            </div>
          </button>
          <div x-show="drawerOpen" class="text-base" style="display: none;">
            <a href="https://mashable.com/shows" class="block mt-4" data-ga-click="" data-ga-label="$text">Mashable Shows</a>
            <a href="https://mashable.com/videos" class="block mt-4 font-bold" data-ga-click="" data-ga-label="$text">All Videos</a>
          </div>
        </div>
      </div>
    </div>
  </nav>
  <header class="max-w-7xl px-4 mt-8 text-primary-400 font-sans mx-auto">
    <div class="flex flex-wrap subtitle-2 ">
      <div><a href="/entertainment" class="mr-2 underline-link">Entertainment</a></div>
    </div>
    <h1 class="mt-4 header-100 leading-tight max-w-5xl ">The new 'Dune' is a great start — but can't outrun the book's biggest problem</h1>
    <div class="mt-2 leading-tight md:leading-normal text-xl max-w-4xl ">The movie, now on HBO Max, is brilliantly faithful to the Frank Herbert original. That reveals some flaws. </div>
    <div class="w-full subtitle-2 mt-8 text-left md:flex md:flex-wrap md:items-baseline md:space-x-8 ">
      <div>
        By
        <a href="/author/chris-taylor" class="underline-link">Chris Taylor</a> &nbsp;on&nbsp;<time datetime="Fri, 22 Oct 2021 00:44:09 +0000">October 21, 2021</time>
      </div>
      <div class="flex flex-initial content-start mt-4 text-base md:mt-0">
        <a href="https://www.facebook.com/sharer.php?u=https%3A%2F%2Fmashable.com%2Farticle%2Fdune-movie-hbo-review" data-ga-element="social-share-link" data-ga-action="social_share_link" data-ga-position="1" data-ga-label="facebook" data-ga-click="" aria-label="Facebook Share" class="pr-3 hover:text-primary-400" target="_blank" rel="noopener">
          <svg class="inline-block w-5 h-4 fill-current">
            <use href="/images/icons/spritemap.svg#sprite-facebook-f-brands"></use>
          </svg>
          <span class="sr-only">Share on Facebook (opens in a new window)</span>
        </a>
        <a href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fmashable.com%2Farticle%2Fdune-movie-hbo-review&amp;text=The+new+%27Dune%27+is+a+great+start+%E2%80%94+but+can%27t+outrun+the+book%27s+biggest+problem" data-ga-element="social-share-link" data-ga-action="social_share_link" data-ga-position="2" data-ga-label="twitter" data-ga-click="" aria-label="Twitter Share" class="px-3 hover:text-primary-400" target="_blank" rel="noopener">
          <svg class="inline-block w-5 h-4 fill-current">
            <use href="/images/icons/spritemap.svg#sprite-twitter-brands"></use>
          </svg>
          <span class="sr-only">Share on Twitter (opens in a new window)</span>
        </a>
        <a href="https://share.flipboard.com/bookmarklet/popout?v=2&amp;url=https%3A%2F%2Fmashable.com%2Farticle%2Fdune-movie-hbo-review&amp;title=The+new+%27Dune%27+is+a+great+start+%E2%80%94+but+can%27t+outrun+the+book%27s+biggest+problem" data-ga-element="social-share-link" data-ga-action="social_share_link" data-ga-position="3" data-ga-label="flipboard" data-ga-click="" aria-label="Flipboard Share" class="px-3 hover:text-primary-400" target="_blank" rel="noopener">
          <svg class="inline-block w-5 h-4 fill-current">
            <use href="/images/icons/spritemap.svg#sprite-flipboard-brands"></use>
          </svg>
          <span class="sr-only">Share on Flipboard (opens in a new window)</span>
        </a>
      </div>
    </div>
    <div class="">
    </div>
  </header>
  <main class="justify-between items-stretch px-4 mx-auto lg:flex max-w-8xl">
    <section class="flex-grow 2xl:pr-4" data-ga-module="content_body">
      <div class="mx-auto -mx-4 max-w-5xl lg:px-8 lg:mx-auto">
        <div class="max-w-7xl px-4 mt-8 text-primary-400 font-sans mx-auto mt-10">
          <img class="w-full border border-gray-100" src="https://helios-i.mashable.com/imagery/articles/03kMbX4uhsnOpnHQkfhoNzO/hero-image.fill.size_1248x702.v1634938507.jpg" alt="The new 'Dune' is a great start — but can't outrun the book's biggest problem" width="1248" height="702" srcset="https://helios-i.mashable.com/imagery/articles/03kMbX4uhsnOpnHQkfhoNzO/hero-image.fill.size_400x225.v1634938507.jpg 400w, https://helios-i.mashable.com/imagery/articles/03kMbX4uhsnOpnHQkfhoNzO/hero-image.fill.size_800x450.v1634938507.jpg 800w, https://helios-i.mashable.com/imagery/articles/03kMbX4uhsnOpnHQkfhoNzO/hero-image.fill.size_1248x702.v1634938507.jpg 1600w" sizes="(max-width: 1280px) 100vw, 1280px">
          <div class="mt-2 leading-none text-left">
            <span class="font-sans normal-case subtitle-2 text-gray-1000">Timothée Chalamet, a.k.a. Paul Atreides, a.k.a.&nbsp;Muad'Dib, a.k.a. the Kwisatz Haderach: A white male savior by many other names.</span>
            <span class="font-sans text-gray-600 subtitle-2">Credit: Courtesy of Warner Bros. Pictures</span>
          </div>
        </div>
        <div class="max-w-7xl px-4 mx-auto">
          <hr class="border-top border-gray-100 mt-12">
        </div>
      </div>
      <div class="mx-auto mt-8 max-w-3xl">
        <section class="mt-8" style="">
          <div class="text-gray-700 subtitle-2 text-uppercase" style="">
            <a href="https://mashable.com" style="" aria-label="Mashable Logo">
              <svg class="inline-block w-5 h-4 fill-current">
                <use href="/images/icons/spritemap.svg#sprite-logomark"></use>
              </svg>
            </a>
            <span class="ml-1">&gt;</span>
            <a class="ml-1 " href="/entertainment" style="">Entertainment</a>
          </div>
        </section>
      </div>
      <article id="article" data-autopogo="" class="mt-8 font-serif editor-content">
        <p>Call off the search: There is, finally, a definitive movie adaptation of Frank Herbert's classic 1965 sci-fi novel <em>Dune</em>. Or at least, a definitive adaptation of the first <em>half</em> of that <a href="https://www.amazon.com/Dune-Frank-Herbert/dp/0441172717" target="_blank">896-page paperback monster<span class="sr-only">(opens in a new tab)</span></a>. </p>
        <p>Denis Villeneuve has made a big, rich, moody sci-fi tone poem in the style of his previous outing, <em>Blade Runner 2049</em>. If you liked that — <a href="https://mashable.com/article/blade-runner-review-ryan-gosling-harrison-ford" target="_self">and we did</a> — you'll love this, and its 150 minutes will fly by in a kind of pleasant hypnotic trance, with a few flashes of humor to relieve the ever-present tension. </p>
        <p><em>Dune</em> launched Thursday night on HBO Max. Villeneuve would prefer you risk rousing the giant sandworm named Delta-variant COVID by seeing it in theaters if you can do so (relatively) safely, as home viewing would be "a diluted experience". He's got a point: Like many desert movie classics, this is best seen in an epic setting. And best <em>felt</em> there, too, with Hans Zimmer's portentous, bass-heavy score rattling the seats. A good chunk of the early third is dimly lit, which makes for a nice contrast when we reach sunlit Arrakis, but also means home viewers with sub-$5,000 TVs may have another <a href="https://mashable.com/article/game-of-thrones-too-dark-cinematographer" target="_self">Battle of Winterfell</a> situation on their hands. </p>
        <p>Beyond the audio-visual artistry, however, how you feel about <em>Dune</em> may depend on how you feel about Herbert's book. If you've never read the sprawling original, Villeneuve's script does just enough hand-holding to keep you afloat — though you may wonder why we're asked to sympathize with the aristocratic, militaristic Atreides family at its center. If you read it and loved it, Villeneuve's your guy. If you read it and were ambivalent, like me, then you may appreciate the small updates and omissions and flash-forwards that make it seem less like a story about a colonizing white male savior. </p>
        <p>Trouble is, <em>Dune</em> is <em>still</em> a story about a colonizing white male savior, especially in its first half. To put it in 21st century terms and with only spice-sized spoilers, book protagonist Paul Atreides is a somewhat creepy rich religious mama's boy who steps into his fated role by taking a bunch of drugs during a desert outing that goes horribly wrong. (<a href="https://mashable.com/article/burning-man-2021" target="_self">Burning Man</a>, anyone?) He then joins a bunch of locals, his former servants, all examples of the <a href="https://theconversation.com/explainer-the-myth-of-the-noble-savage-55316" target="_blank">noble savage trope<span class="sr-only">(opens in a new tab)</span></a>. The rest of Herbert's tale is more quirky and interesting, but since Villeneuve has paused there, so shall we. </p><q>
          Trouble is, Dune is still a story about a colonizing white male savior.
        </q>
        <p>Some of this one-percenter crudeness was Herbert's intent. Paul was supposed to subtly subvert a lot of expectations about saviors — but in 2021, our expectations have evolved. It's easier now to see through the beautiful language and intriguing world-building grafted on to his problematic arc. By being faithful to the story, Villeneuve — who says he loved the book as a kid, and identified with Paul Atreides — unintentionally reveals that the story has not aged well. </p>
        <p>The native Fremen get more respect and screen time than in the 1984 <em>Dune</em>, but only by a few precious scenes. Chani (Zendaya) is a stunning presence throughout, but barely gets to speak. Were it not for Paul (Timothée Chalamet) and his screen dad Duke Leto (Oscar Isaac) both looking so goddamn pretty and puppy-dog sad all the time, you might wonder who to root for. </p>
        <p>Chalamet may be the most fitting big-screen Paul yet, with apologies to Kyle MacLachlan. The characters arrayed around him may help provide some much-needed warmth. But you are still, by design, going to get some serious <a href="https://mashable.com/article/game-of-thrones-bran-stark-relatable" target="_self">Bran-in-</a><a href="https://mashable.com/article/game-of-thrones-bran-stark-relatable" target="_self"><em>Game-of-Thrones</em></a> vibes here. </p>
        <h2><em><strong>Dune</strong></em><strong> it right </strong></h2>
        <p>The long and tortured story of <em>Dune</em> adaptations is familiar to many movie nerds by now. First in the 1970s came Alejandro Jodorowsky, a brilliantly batty Chilean indie auteur with a very specific vision of the book — one that could only be fulfilled with Salvador Dalí playing the Emperor of the universe. His Yankee studio bankers pulled the funding, so we never saw the up-to-12-hours-long film he wanted to make. We did at least get the documentary <em>Jodorosky's Dune, </em>which is so off-the-wall entertaining, it may win the all-time award for best piece of celluloid to cover Herbert's story in some way. </p>
        <p>Then in 1984 came David Lynch's attempt at <em>Dune</em>, which is best remembered these days for nearly-naked Sting's turn as an evil spike-haired assassin. Lynch lost control over the final cut and disavowed the film; like Jodorosky, he wanted it to be as long as he wanted it to be. But you can still blame Lynch for the way the characters are oddly static in scenes, for the way they talk in endless internal narration, and for his campy, gross and <a href="https://www.thecompanion.app/2021/10/12/dune-baron-harkonnen-queer-menace-and-frank-herberts-homophobia/" target="_blank">homophobic interpretation<span class="sr-only">(opens in a new tab)</span></a> of Baron Harkonnen.</p>
        <p>To be fair to Lynch, the Baron is a tough nut to crack. He is so cartoonishly villainous in the book, floating through the air like a bad guy in a cliched kid's adventure, how do you not put him on screen to laugh at rather than to fear? Even Villeneuve's Harkonnen (Stellan Skarsgård) tends a bit towards the gross-out side of things. Only the combination of lighting, score and Skarsgård's sinister, subtle grumpiness prevents this Baron from being too two-dimensional. </p>
        <p>But overall, Villeneuve has done what his auteur predecessors failed to do: He has translated <em>Dune</em> to the screen in a basically reasonable way, with clear exposition, no Emperor, and enough diversity to bring it kicking and screaming into the 21st century. He could have paid more attention to what keeps happening to his characters of color; there is going to be more discussion of that once more of us have seen it. But he made the Atreides clan likable, which these days is a feat in itself. </p>
        <p>Now, if the gamble he learned from his predecessor's mistakes (do it long but in two parts) has worked to pull in audiences, it will be interesting to see what Villeneuve can do with the complex, hazy, hallucinatory part 2. The result could soar higher than any sandworm. </p>
        <h3>Related video: Timothée Chalamet and Zendaya on bringing a sci-fi epic back to the big screen </h3>
        <div class="content-block-mashable-video mt-8">
          <div id="mashable-video-container-3Y0Njq530G" class="jwplayer jw-reset jw-state-idle jw-stretch-uniform jw-flag-aspect-mode jw-skin-bekle jw-breakpoint-4 jw-flag-user-inactive" tabindex="0" aria-label="Video Player" role="application" style="width: 100%;" aria-describedby="jw-mashable-video-container-3Y0Njq530G-shortcuts-tooltip-explanation">
            <div class="jw-aspect jw-reset" style="padding-top: 56.25%;"></div>
            <div class="jw-wrapper jw-reset">
              <div style="opacity: 0; visibility: hidden; overflow: hidden; display: block; position: absolute; top: 0px; left: 0px; width: 100%; height: 100%;">
                <div style="overflow: auto; display: block; position: absolute; top: 0px; left: 0px; width: 100%; height: 100%;">
                  <div style="height: 1px; width: 769px;"></div>
                </div>
                <div class="jw-contract-trigger" style="overflow: auto; display: block; position: absolute; top: 0px; left: 0px; width: 100%; height: 100%;"></div>
              </div>
              <div class="jw-top jw-reset"></div>
              <div class="jw-aspect jw-reset" style="padding-top: 56.25%;"></div>
              <div class="jw-media jw-reset">
                <div class="vsc-controller vsc-nosource"></div><video class="jw-video jw-reset" tabindex="-1" disableremoteplayback="" webkit-playsinline="" playsinline=""></video>
              </div>
              <div class="jw-preview jw-reset"></div>
              <div class="jw-controls-backdrop jw-reset"></div>
              <div class="jw-captions jw-reset" style="font-size: 22px;">
                <div class="jw-captions-window jw-reset"><span class="jw-captions-text jw-reset"></span></div>
              </div>
              <div class="jw-title jw-reset-text" dir="auto" style="display: none;">
                <div class="jw-title-primary jw-reset-text"></div>
                <div class="jw-title-secondary jw-reset-text"></div>
              </div>
              <div class="jw-overlays jw-reset">
                <div id="mashable-video-container-3Y0Njq530G_jwpsrv" class="jw-plugin jw-reset"></div>
              </div>
              <div class="jw-hidden-accessibility"><span class="jw-time-update" aria-live="assertive">0 seconds of 0 seconds</span><span class="jw-volume-update" aria-live="assertive">Volume 0%</span></div>
              <div class="jw-shortcuts-tooltip jw-modal jw-reset" title="Keyboard Shortcuts">
                <div class="jw-icon jw-icon-inline jw-button-color jw-reset jw-shortcuts-close" role="button" tabindex="0" aria-label="Close" style=""><svg xmlns="http://www.w3.org/2000/svg" class="jw-svg-icon jw-svg-icon-close" viewBox="0 0 240 240" focusable="false">
                    <path d="M134.8,120l48.6-48.6c2-1.9,2.1-5.2,0.2-7.2c0,0-0.1-0.1-0.2-0.2l-7.4-7.4c-1.9-2-5.2-2.1-7.2-0.2c0,0-0.1,0.1-0.2,0.2L120,105.2L71.4,56.6c-1.9-2-5.2-2.1-7.2-0.2c0,0-0.1,0.1-0.2,0.2L56.6,64c-2,1.9-2.1,5.2-0.2,7.2c0,0,0.1,0.1,0.2,0.2l48.6,48.7l-48.6,48.6c-2,1.9-2.1,5.2-0.2,7.2c0,0,0.1,0.1,0.2,0.2l7.4,7.4c1.9,2,5.2,2.1,7.2,0.2c0,0,0.1-0.1,0.2-0.2l48.7-48.6l48.6,48.6c1.9,2,5.2,2.1,7.2,0.2c0,0,0.1-0.1,0.2-0.2l7.4-7.4c2-1.9,2.1-5.2,0.2-7.2c0,0-0.1-0.1-0.2-0.2L134.8,120z"></path>
                  </svg></div><span class="jw-hidden" id="jw-mashable-video-container-3Y0Njq530G-shortcuts-tooltip-explanation">Press shift question mark to access a list of keyboard shortcuts</span>
                <div class="jw-reset jw-shortcuts-container">
                  <div class="jw-reset jw-shortcuts-header"><span class="jw-reset jw-shortcuts-title">Keyboard Shortcuts</span><button role="switch" aria-label="Keyboard Shortcuts" class="jw-reset jw-switch"><span class="jw-reset jw-switch-knob"></span><span class="jw-reset-text jw-switch-enabled">Enabled</span><span class="jw-reset-text jw-switch-disabled">Disabled</span></button></div>
                  <div class="jw-reset jw-shortcuts-tooltip-list">
                    <div class="jw-shortcuts-tooltip-descriptions jw-reset">
                      <div class="jw-shortcuts-row jw-reset"><span class="jw-shortcuts-description jw-reset">Play/Pause</span><span class="jw-shortcuts-key jw-reset">SPACE</span></div>
                      <div class="jw-shortcuts-row jw-reset"><span class="jw-shortcuts-description jw-reset">Increase Volume</span><span class="jw-shortcuts-key jw-reset">↑</span></div>
                      <div class="jw-shortcuts-row jw-reset"><span class="jw-shortcuts-description jw-reset">Decrease Volume</span><span class="jw-shortcuts-key jw-reset">↓</span></div>
                      <div class="jw-shortcuts-row jw-reset"><span class="jw-shortcuts-description jw-reset">Seek Forward</span><span class="jw-shortcuts-key jw-reset">→</span></div>
                      <div class="jw-shortcuts-row jw-reset"><span class="jw-shortcuts-description jw-reset">Seek Backward</span><span class="jw-shortcuts-key jw-reset">←</span></div>
                      <div class="jw-shortcuts-row jw-reset"><span class="jw-shortcuts-description jw-reset">Captions On/Off</span><span class="jw-shortcuts-key jw-reset">c</span></div>
                      <div class="jw-shortcuts-row jw-reset"><span class="jw-shortcuts-description jw-reset">Fullscreen/Exit Fullscreen</span><span class="jw-shortcuts-key jw-reset">f</span></div>
                      <div class="jw-shortcuts-row jw-reset"><span class="jw-shortcuts-description jw-reset">Mute/Unmute</span><span class="jw-shortcuts-key jw-reset">m</span></div>
                      <div class="jw-shortcuts-row jw-reset"><span class="jw-shortcuts-description jw-reset">Seek %</span><span class="jw-shortcuts-key jw-reset">0-9</span></div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="jw-controls jw-reset">
                <div class="jw-display jw-reset">
                  <div class="jw-display-container jw-reset">
                    <div class="jw-display-controls jw-reset">
                      <div class="jw-display-icon-container jw-display-icon-rewind jw-reset">
                        <div class="jw-icon jw-icon-rewind jw-button-color jw-reset" role="button" tabindex="0" aria-label="Rewind 10 Seconds"><svg xmlns="http://www.w3.org/2000/svg" class="jw-svg-icon jw-svg-icon-rewind" viewBox="0 0 240 240" focusable="false">
                            <path d="M113.2,131.078a21.589,21.589,0,0,0-17.7-10.6,21.589,21.589,0,0,0-17.7,10.6,44.769,44.769,0,0,0,0,46.3,21.589,21.589,0,0,0,17.7,10.6,21.589,21.589,0,0,0,17.7-10.6,44.769,44.769,0,0,0,0-46.3Zm-17.7,47.2c-7.8,0-14.4-11-14.4-24.1s6.6-24.1,14.4-24.1,14.4,11,14.4,24.1S103.4,178.278,95.5,178.278Zm-43.4,9.7v-51l-4.8,4.8-6.8-6.8,13-13a4.8,4.8,0,0,1,8.2,3.4v62.7l-9.6-.1Zm162-130.2v125.3a4.867,4.867,0,0,1-4.8,4.8H146.6v-19.3h48.2v-96.4H79.1v19.3c0,5.3-3.6,7.2-8,4.3l-41.8-27.9a6.013,6.013,0,0,1-2.7-8,5.887,5.887,0,0,1,2.7-2.7l41.8-27.9c4.4-2.9,8-1,8,4.3v19.3H209.2A4.974,4.974,0,0,1,214.1,57.778Z"></path>
                          </svg></div>
                      </div>
                      <div class="jw-display-icon-container jw-display-icon-display jw-reset">
                        <div class="jw-icon jw-icon-display jw-button-color jw-reset" role="button" tabindex="0" aria-label="Play"><svg xmlns="http://www.w3.org/2000/svg" class="jw-svg-icon jw-svg-icon-buffer" viewBox="0 0 240 240" focusable="false">
                            <path d="M120,186.667a66.667,66.667,0,0,1,0-133.333V40a80,80,0,1,0,80,80H186.667A66.846,66.846,0,0,1,120,186.667Z"></path>
                          </svg><svg xmlns="http://www.w3.org/2000/svg" class="jw-svg-icon jw-svg-icon-replay" viewBox="0 0 240 240" focusable="false">
                            <path d="M120,41.9v-20c0-5-4-8-8-4l-44,28a5.865,5.865,0,0,0-3.3,7.6A5.943,5.943,0,0,0,68,56.8l43,29c5,4,9,1,9-4v-20a60,60,0,1,1-60,60H40a80,80,0,1,0,80-79.9Z"></path>
                          </svg><svg xmlns="http://www.w3.org/2000/svg" class="jw-svg-icon jw-svg-icon-play" viewBox="0 0 240 240" focusable="false">
                            <path d="M62.8,199.5c-1,0.8-2.4,0.6-3.3-0.4c-0.4-0.5-0.6-1.1-0.5-1.8V42.6c-0.2-1.3,0.7-2.4,1.9-2.6c0.7-0.1,1.3,0.1,1.9,0.4l154.7,77.7c2.1,1.1,2.1,2.8,0,3.8L62.8,199.5z"></path>
                          </svg><svg xmlns="http://www.w3.org/2000/svg" class="jw-svg-icon jw-svg-icon-pause" viewBox="0 0 240 240" focusable="false">
                            <path d="M100,194.9c0.2,2.6-1.8,4.8-4.4,5c-0.2,0-0.4,0-0.6,0H65c-2.6,0.2-4.8-1.8-5-4.4c0-0.2,0-0.4,0-0.6V45c-0.2-2.6,1.8-4.8,4.4-5c0.2,0,0.4,0,0.6,0h30c2.6-0.2,4.8,1.8,5,4.4c0,0.2,0,0.4,0,0.6V194.9z M180,45.1c0.2-2.6-1.8-4.8-4.4-5c-0.2,0-0.4,0-0.6,0h-30c-2.6-0.2-4.8,1.8-5,4.4c0,0.2,0,0.4,0,0.6V195c-0.2,2.6,1.8,4.8,4.4,5c0.2,0,0.4,0,0.6,0h30c2.6,0.2,4.8-1.8,5-4.4c0-0.2,0-0.4,0-0.6V45.1z"></path>
                          </svg></div>
                      </div>
                      <div class="jw-display-icon-container jw-display-icon-next jw-reset" style="visibility: hidden;">
                        <div class="jw-icon jw-icon-next jw-button-color jw-reset" role="button" tabindex="0" aria-label="Next"><svg xmlns="http://www.w3.org/2000/svg" class="jw-svg-icon jw-svg-icon-next" viewBox="0 0 240 240" focusable="false">
                            <path d="M165,60v53.3L59.2,42.8C56.9,41.3,55,42.3,55,45v150c0,2.7,1.9,3.8,4.2,2.2L165,126.6v53.3h20v-120L165,60L165,60z"></path>
                          </svg></div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="jw-nextup-container jw-reset">
                  <div class="jw-nextup jw-background-color jw-reset">
                    <div class="jw-nextup-tooltip jw-reset">
                      <div class="jw-nextup-thumbnail jw-reset"></div>
                      <div class="jw-nextup-body jw-reset">
                        <div class="jw-nextup-header jw-reset"></div>
                        <div class="jw-nextup-title jw-reset-text" dir="auto"></div>
                        <div class="jw-nextup-duration jw-reset"></div>
                      </div>
                    </div><button type="button" class="jw-icon jw-nextup-close jw-reset" aria-label="Close"><svg xmlns="http://www.w3.org/2000/svg" class="jw-svg-icon jw-svg-icon-close" viewBox="0 0 240 240" focusable="false">
                        <path d="M134.8,120l48.6-48.6c2-1.9,2.1-5.2,0.2-7.2c0,0-0.1-0.1-0.2-0.2l-7.4-7.4c-1.9-2-5.2-2.1-7.2-0.2c0,0-0.1,0.1-0.2,0.2L120,105.2L71.4,56.6c-1.9-2-5.2-2.1-7.2-0.2c0,0-0.1,0.1-0.2,0.2L56.6,64c-2,1.9-2.1,5.2-0.2,7.2c0,0,0.1,0.1,0.2,0.2l48.6,48.7l-48.6,48.6c-2,1.9-2.1,5.2-0.2,7.2c0,0,0.1,0.1,0.2,0.2l7.4,7.4c1.9,2,5.2,2.1,7.2,0.2c0,0,0.1-0.1,0.2-0.2l48.7-48.6l48.6,48.6c1.9,2,5.2,2.1,7.2,0.2c0,0,0.1-0.1,0.2-0.2l7.4-7.4c2-1.9,2.1-5.2,0.2-7.2c0,0-0.1-0.1-0.2-0.2L134.8,120z"></path>
                      </svg></button>
                  </div>
                </div>
                <div id="jw-mashable-video-container-3Y0Njq530G-settings-menu" class="jw-reset jw-settings-menu" aria-expanded="false">
                  <div class="jw-reset jw-settings-topbar">
                    <div class="jw-reset jw-settings-topbar-text" tabindex="0"></div>
                    <div class="jw-reset jw-settings-topbar-buttons">
                      <div class="jw-icon jw-icon-inline jw-button-color jw-reset jw-settings-sharing jw-submenu-sharing" role="button" tabindex="0" aria-label="Share" name="sharing" type="button" aria-expanded="false" aria-controls="jw-mashable-video-container-3Y0Njq530G-settings-submenu-sharing" style=""><svg class="jw-svg-icon jw-svg-icon-sharing" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 240 240" focusable="false">
                          <path d="M175,160c-6.9,0.2-13.6,2.6-19,7l-62-39c0.8-2.6,1.2-5.3,1-8c0.2-2.7-0.2-5.4-1-8l62-39c5.4,4.4,12.1,6.8,19,7c16.3,0.3,29.7-12.6,30-28.9c0-0.4,0-0.7,0-1.1c0-16.5-13.5-30-30-30s-30,13.5-30,30c-0.2,2.7,0.2,5.4,1,8L84,97c-5.4-4.4-12.1-6.8-19-7c-16.5,0-30,13.5-30,30s13.5,30,30,30c6.9-0.2,13.6-2.6,19-7l62,39c-0.8,2.6-1.2,5.3-1,8c0,16.5,13.5,30,30,30s30-13.5,30-30S191.6,160,175,160z"></path>
                        </svg>
                        <div class="jw-reset-text jw-tooltip jw-tooltip-sharing" dir="auto">
                          <div class="jw-text"></div>
                        </div>
                      </div>
                      <div class="jw-icon jw-icon-inline jw-button-color jw-reset jw-settings-close" role="button" tabindex="0" aria-label="Close" style=""><svg xmlns="http://www.w3.org/2000/svg" class="jw-svg-icon jw-svg-icon-close" viewBox="0 0 240 240" focusable="false">
                          <path d="M134.8,120l48.6-48.6c2-1.9,2.1-5.2,0.2-7.2c0,0-0.1-0.1-0.2-0.2l-7.4-7.4c-1.9-2-5.2-2.1-7.2-0.2c0,0-0.1,0.1-0.2,0.2L120,105.2L71.4,56.6c-1.9-2-5.2-2.1-7.2-0.2c0,0-0.1,0.1-0.2,0.2L56.6,64c-2,1.9-2.1,5.2-0.2,7.2c0,0,0.1,0.1,0.2,0.2l48.6,48.7l-48.6,48.6c-2,1.9-2.1,5.2-0.2,7.2c0,0,0.1,0.1,0.2,0.2l7.4,7.4c1.9,2,5.2,2.1,7.2,0.2c0,0,0.1-0.1,0.2-0.2l48.7-48.6l48.6,48.6c1.9,2,5.2,2.1,7.2,0.2c0,0,0.1-0.1,0.2-0.2l7.4-7.4c2-1.9,2.1-5.2,0.2-7.2c0,0-0.1-0.1-0.2-0.2L134.8,120z"></path>
                        </svg></div>
                    </div>
                  </div>
                  <div id="jw-mashable-video-container-3Y0Njq530G-settings-submenu-sharing" class="jw-reset jw-settings-submenu jw-settings-submenu-sharing jw-sharing-menu" role="menu" aria-expanded="false">
                    <div class="jw-reset jw-settings-submenu-items"><button class="jw-reset jw-settings-content-item jw-sharing-link" aria-checked="false" type="button" role="button" aria-label="facebook" tabindex="0"><svg class="jw-svg-icon jw-svg-icon-facebook" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 160 160" focusable="false">
                          <path d="M137.8,15H22.1A7.127,7.127,0,0,0,15,22.1V137.8a7.28,7.28,0,0,0,7.1,7.2H84.5V95H67.6V75.5H84.5v-15a23.637,23.637,0,0,1,21.3-25.9,28.08,28.08,0,0,1,4.1-.1c7.2,0,13.7.6,14.9.6V52.7H114.4c-8.5,0-9.7,3.9-9.7,9.7V74.7h19.5l-2.6,19.5H104.7v50.7h33.1a7.3,7.3,0,0,0,7.2-7.2V22A7.13,7.13,0,0,0,137.8,15Z"></path>
                        </svg> facebook</button><button class="jw-reset jw-settings-content-item jw-sharing-link" aria-checked="false" type="button" role="button" aria-label="twitter" tabindex="0"><svg class="jw-svg-icon jw-svg-icon-twitter" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 160 160" focusable="false">
                          <path d="M56.8,132.5a75.177,75.177,0,0,0,75.3-75.1V54A53.405,53.405,0,0,0,145,40.5a58.075,58.075,0,0,1-15.4,3.9,27.138,27.138,0,0,0,11.6-14.8A53.038,53.038,0,0,1,124.5,36a25.736,25.736,0,0,0-19.3-8.4A26.12,26.12,0,0,0,78.8,53.4V54a16.5,16.5,0,0,0,.7,5.8,71.966,71.966,0,0,1-54.1-27,23.9,23.9,0,0,0-3.9,13.5A26.043,26.043,0,0,0,33.1,68.2,27.018,27.018,0,0,1,20.9,65v.7A26.15,26.15,0,0,0,42.1,91.4a24.149,24.149,0,0,1-7.1.7,12.625,12.625,0,0,1-5.1-.7,25.657,25.657,0,0,0,24.5,18A53.519,53.519,0,0,1,21.6,121a19.683,19.683,0,0,1-6.4-.7,80.388,80.388,0,0,0,41.6,12.2"></path>
                        </svg> twitter</button><button class="jw-reset jw-settings-content-item jw-sharing-link" aria-checked="false" type="button" role="button" aria-label="email" tabindex="0"><svg xmlns="http://www.w3.org/2000/svg" class="jw-svg-icon jw-svg-icon-email" viewBox="0 0 160 160" focusable="false">
                          <path d="M147.3,27.9H11.9L10,29.8v97a3.02,3.02,0,0,0,2.8,3.2H146.6a3.02,3.02,0,0,0,3.2-2.8V31C150.5,29.2,149.2,27.9,147.3,27.9ZM125.6,40.7,80.3,77.1,35,40.7Zm12.1,76.6H22.8V47.7l57.5,46,57.5-46-.1,69.6Z"></path>
                        </svg> Email</button>
                      <div class="jw-reset jw-settings-content-item jw-sharing-copy" aria-label="link" role="button" tabindex="0"><button class="jw-reset jw-sharing-link" aria-checked="false" type="button" role="button"><svg xmlns="http://www.w3.org/2000/svg" class="jw-svg-icon jw-svg-icon-link" viewBox="0 0 160 160" focusable="false">
                            <path d="M79.4,99.6H92.5v2a33.6,33.6,0,0,1-9.8,24.2l-9.8,9.8a34.716,34.716,0,0,1-48.4,0,34.716,34.716,0,0,1,0-48.4l9.2-10.5a33.6,33.6,0,0,1,24.2-9.8h1.9V80H58.5a19.359,19.359,0,0,0-15.1,6.5l-9.8,9.8a20.976,20.976,0,0,0-.5,29.6l.5.5a20.976,20.976,0,0,0,29.6.5l.5-.5,9.8-9.8a20.905,20.905,0,0,0,6.5-15h0v-2ZM135,24.4h0a34.716,34.716,0,0,0-48.4,0L76.1,34.2a33.6,33.6,0,0,0-9.8,24.2v2H79.4v-2a19.359,19.359,0,0,1,6.5-15.1l9.8-9.8a20.976,20.976,0,0,1,29.6-.5l.5.5a20.976,20.976,0,0,1,.5,29.6l-.5.5-10.5,9.8a20.905,20.905,0,0,1-15,6.5H99V93h1.3a33.6,33.6,0,0,0,24.2-9.8l9.8-9.8A34.89,34.89,0,0,0,135,24.4ZM63,106.2l42.5-42.5-9.8-9.8L53.2,96.4Z"></path>
                          </svg> Link</button><textarea readonly="true" class="jw-reset jw-sharing-textarea">https://mashable.com/article/dune-movie-hbo-review</textarea>
                        <div class="jw-reset jw-tooltip jw-tooltip-sharing-Link">
                          <div class="jw-text">Copied</div>
                        </div>
                      </div>
                      <div class="jw-reset jw-settings-content-item jw-sharing-copy" aria-label="embed" role="button" tabindex="0"><button class="jw-reset jw-sharing-link" aria-checked="false" type="button" role="button"><svg xmlns="http://www.w3.org/2000/svg" class="jw-svg-icon jw-svg-icon-embed" viewBox="0 0 160 160" focusable="false">
                            <path d="M153.224,81.594,126.971,54.685,117.6,64.061l21.846,21.846L117.6,107.752l8.719,8.719L152.567,90.22a5.583,5.583,0,0,0,1.406-7.782,6.067,6.067,0,0,0-.75-.844ZM33.12,54.685,6.868,80.938A5.973,5.973,0,0,0,6.68,89.47l.188.188L33.12,117.128l9.376-9.376-22.5-21.846L42.5,64.061ZM53.747,134.1,94.437,21.5,106.345,25.9,65.654,138.5Z"></path>
                          </svg> Embed</button><textarea readonly="true" class="jw-reset jw-sharing-textarea">&lt;iframe src="//cdn.jwplayer.com/players/MEDIAID-dloDxAmE.html" width="640" height="360" frameborder="0" scrolling="auto"&gt;&lt;/iframe&gt;</textarea>
                        <div class="jw-reset jw-tooltip jw-tooltip-sharing-Embed">
                          <div class="jw-text">Copied</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="jw-controlbar jw-reset">
                  <div class="jw-slider-time jw-chapter-slider-time jw-background-color jw-reset jw-slider-horizontal jw-reset" aria-valuetext="0 seconds of 0 seconds" aria-valuenow="0" tabindex="0" role="slider" aria-label="Seek">
                    <div class="jw-slider-container jw-reset">
                      <div class="jw-reset jw-old-rail"></div>
                      <div class="jw-reset jw-old-buffer" style="width: 0%;"></div>
                      <div class="jw-reset jw-old-progress" style="width: 0%;"></div>
                      <div class="jw-knob jw-reset" style="left: 0%;"></div>
                      <div class="jw-icon jw-icon-tooltip jw-tooltip-time jw-button-color jw-reset">
                        <div class="jw-overlay jw-reset">
                          <div class="jw-time-tip jw-reset">
                            <div class="jw-time-thumb jw-reset" style="width: 0px; height: 0px;"></div><span class="jw-time-chapter jw-text jw-reset" style="display: none;"></span><span class="jw-time-time jw-text jw-reset"></span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="jw-reset jw-button-container">
                    <div class="jw-icon jw-icon-inline jw-button-color jw-reset jw-icon-playback" role="button" tabindex="0" aria-label="Play" style=""><svg xmlns="http://www.w3.org/2000/svg" class="jw-svg-icon jw-svg-icon-play" viewBox="0 0 240 240" focusable="false">
                        <path d="M62.8,199.5c-1,0.8-2.4,0.6-3.3-0.4c-0.4-0.5-0.6-1.1-0.5-1.8V42.6c-0.2-1.3,0.7-2.4,1.9-2.6c0.7-0.1,1.3,0.1,1.9,0.4l154.7,77.7c2.1,1.1,2.1,2.8,0,3.8L62.8,199.5z"></path>
                      </svg><svg xmlns="http://www.w3.org/2000/svg" class="jw-svg-icon jw-svg-icon-pause" viewBox="0 0 240 240" focusable="false">
                        <path d="M100,194.9c0.2,2.6-1.8,4.8-4.4,5c-0.2,0-0.4,0-0.6,0H65c-2.6,0.2-4.8-1.8-5-4.4c0-0.2,0-0.4,0-0.6V45c-0.2-2.6,1.8-4.8,4.4-5c0.2,0,0.4,0,0.6,0h30c2.6-0.2,4.8,1.8,5,4.4c0,0.2,0,0.4,0,0.6V194.9z M180,45.1c0.2-2.6-1.8-4.8-4.4-5c-0.2,0-0.4,0-0.6,0h-30c-2.6-0.2-4.8,1.8-5,4.4c0,0.2,0,0.4,0,0.6V195c-0.2,2.6,1.8,4.8,4.4,5c0.2,0,0.4,0,0.6,0h30c2.6,0.2,4.8-1.8,5-4.4c0-0.2,0-0.4,0-0.6V45.1z"></path>
                      </svg><svg xmlns="http://www.w3.org/2000/svg" class="jw-svg-icon jw-svg-icon-stop" viewBox="0 0 240 240" focusable="false">
                        <path d="M190,185c0.2,2.6-1.8,4.8-4.4,5c-0.2,0-0.4,0-0.6,0H55c-2.6,0.2-4.8-1.8-5-4.4c0-0.2,0-0.4,0-0.6V55c-0.2-2.6,1.8-4.8,4.4-5c0.2,0,0.4,0,0.6,0h130c2.6-0.2,4.8,1.8,5,4.4c0,0.2,0,0.4,0,0.6V185z"></path>
                      </svg>
                      <div class="jw-reset-text jw-tooltip jw-tooltip-play" dir="auto">
                        <div class="jw-text"></div>
                      </div>
                    </div>
                    <div class="jw-icon jw-icon-inline jw-button-color jw-reset jw-icon-rewind" role="button" tabindex="0" aria-label="Rewind 10 Seconds" style=""><svg xmlns="http://www.w3.org/2000/svg" class="jw-svg-icon jw-svg-icon-rewind" viewBox="0 0 240 240" focusable="false">
                        <path d="M113.2,131.078a21.589,21.589,0,0,0-17.7-10.6,21.589,21.589,0,0,0-17.7,10.6,44.769,44.769,0,0,0,0,46.3,21.589,21.589,0,0,0,17.7,10.6,21.589,21.589,0,0,0,17.7-10.6,44.769,44.769,0,0,0,0-46.3Zm-17.7,47.2c-7.8,0-14.4-11-14.4-24.1s6.6-24.1,14.4-24.1,14.4,11,14.4,24.1S103.4,178.278,95.5,178.278Zm-43.4,9.7v-51l-4.8,4.8-6.8-6.8,13-13a4.8,4.8,0,0,1,8.2,3.4v62.7l-9.6-.1Zm162-130.2v125.3a4.867,4.867,0,0,1-4.8,4.8H146.6v-19.3h48.2v-96.4H79.1v19.3c0,5.3-3.6,7.2-8,4.3l-41.8-27.9a6.013,6.013,0,0,1-2.7-8,5.887,5.887,0,0,1,2.7-2.7l41.8-27.9c4.4-2.9,8-1,8,4.3v19.3H209.2A4.974,4.974,0,0,1,214.1,57.778Z"></path>
                      </svg>
                      <div class="jw-reset-text jw-tooltip jw-tooltip-rewind" dir="auto">
                        <div class="jw-text"></div>
                      </div>
                    </div>
                    <div class="jw-icon jw-icon-inline jw-button-color jw-reset jw-icon-next" role="button" tabindex="0" aria-label="Next" dir="auto" style="display: none;"><svg xmlns="http://www.w3.org/2000/svg" class="jw-svg-icon jw-svg-icon-next" viewBox="0 0 240 240" focusable="false">
                        <path d="M165,60v53.3L59.2,42.8C56.9,41.3,55,42.3,55,45v150c0,2.7,1.9,3.8,4.2,2.2L165,126.6v53.3h20v-120L165,60L165,60z"></path>
                      </svg>
                      <div class="jw-reset-text jw-tooltip jw-tooltip-next" dir="auto">
                        <div class="jw-text"></div>
                      </div>
                    </div>
                    <div aria-label="Unmute button" role="group" tabindex="0" class="jw-icon jw-icon-tooltip jw-icon-volume jw-button-color jw-reset jw-off">
                      <div class="jw-overlay jw-reset" tabindex="0" aria-label="Volume" aria-orientation="vertical" aria-valuemin="0" aria-valuemax="100" role="slider" aria-valuenow="0" aria-valuetext="Volume 0%">
                        <div class="jw-slider-volume jw-volume-tip jw-reset jw-slider-vertical" aria-hidden="true">
                          <div class="jw-slider-container jw-reset">
                            <div class="jw-rail jw-reset"></div>
                            <div class="jw-buffer jw-reset"></div>
                            <div class="jw-progress jw-reset" style="height: 0%;"></div>
                            <div class="jw-knob jw-reset" style="bottom: 0%;"></div>
                          </div>
                        </div>
                      </div><svg xmlns="http://www.w3.org/2000/svg" class="jw-svg-icon jw-svg-icon-volume-0" viewBox="0 0 240 240" focusable="false">
                        <path d="M116.4,42.8v154.5c0,2.8-1.7,3.6-3.8,1.7l-54.1-48.1H28.9c-2.8,0-5.2-2.3-5.2-5.2V94.2c0-2.8,2.3-5.2,5.2-5.2h29.6l54.1-48.1C114.6,39.1,116.4,39.9,116.4,42.8z M212.3,96.4l-14.6-14.6l-23.6,23.6l-23.6-23.6l-14.6,14.6l23.6,23.6l-23.6,23.6l14.6,14.6l23.6-23.6l23.6,23.6l14.6-14.6L188.7,120L212.3,96.4z"></path>
                      </svg><svg xmlns="http://www.w3.org/2000/svg" class="jw-svg-icon jw-svg-icon-volume-50" viewBox="0 0 240 240" focusable="false">
                        <path d="M116.4,42.8v154.5c0,2.8-1.7,3.6-3.8,1.7l-54.1-48.1H28.9c-2.8,0-5.2-2.3-5.2-5.2V94.2c0-2.8,2.3-5.2,5.2-5.2h29.6l54.1-48.1C114.7,39.1,116.4,39.9,116.4,42.8z M178.2,120c0-22.7-18.5-41.2-41.2-41.2v20.6c11.4,0,20.6,9.2,20.6,20.6c0,11.4-9.2,20.6-20.6,20.6v20.6C159.8,161.2,178.2,142.7,178.2,120z"></path>
                      </svg><svg xmlns="http://www.w3.org/2000/svg" class="jw-svg-icon jw-svg-icon-volume-100" viewBox="0 0 240 240" focusable="false">
                        <path d="M116.5,42.8v154.4c0,2.8-1.7,3.6-3.8,1.7l-54.1-48H29c-2.8,0-5.2-2.3-5.2-5.2V94.3c0-2.8,2.3-5.2,5.2-5.2h29.6l54.1-48C114.8,39.2,116.5,39.9,116.5,42.8z"></path>
                        <path d="M136.2,160v-20c11.1,0,20-8.9,20-20s-8.9-20-20-20V80c22.1,0,40,17.9,40,40S158.3,160,136.2,160z"></path>
                        <path d="M216.2,120c0-44.2-35.8-80-80-80v20c33.1,0,60,26.9,60,60s-26.9,60-60,60v20C180.4,199.9,216.1,164.1,216.2,120z"></path>
                      </svg>
                    </div>
                    <div class="jw-horizontal-volume-container" tabindex="0" aria-label="Volume" aria-orientation="horizontal" aria-valuemin="0" aria-valuemax="100" role="slider" aria-valuenow="0" aria-valuetext="Volume 0%">
                      <div class="jw-slider-volume jw-reset jw-slider-horizontal" aria-hidden="true">
                        <div class="jw-slider-container jw-reset">
                          <div class="jw-rail jw-reset"></div>
                          <div class="jw-buffer jw-reset"></div>
                          <div class="jw-progress jw-reset" style="width: 0%;"></div>
                          <div class="jw-knob jw-reset" style="left: 0%;"></div>
                        </div>
                      </div>
                    </div>
                    <div class="jw-icon jw-icon-inline jw-button-color jw-reset jw-icon-fullscreen jw-fullscreen-ima" role="button" tabindex="0" aria-label="Fullscreen" style=""><svg xmlns="http://www.w3.org/2000/svg" class="jw-svg-icon jw-svg-icon-fullscreen-off" viewBox="0 0 240 240" focusable="false">
                        <path d="M109.2,134.9l-8.4,50.1c-0.4,2.7-2.4,3.3-4.4,1.4L82,172l-27.9,27.9l-14.2-14.2l27.9-27.9l-14.4-14.4c-1.9-1.9-1.3-3.9,1.4-4.4l50.1-8.4c1.8-0.5,3.6,0.6,4.1,2.4C109.4,133.7,109.4,134.3,109.2,134.9L109.2,134.9z M172.1,82.1L200,54.2L185.8,40l-27.9,27.9l-14.4-14.4c-1.9-1.9-3.9-1.3-4.4,1.4l-8.4,50.1c-0.5,1.8,0.6,3.6,2.4,4.1c0.5,0.2,1.2,0.2,1.7,0l50.1-8.4c2.7-0.4,3.3-2.4,1.4-4.4L172.1,82.1z"></path>
                      </svg><svg xmlns="http://www.w3.org/2000/svg" class="jw-svg-icon jw-svg-icon-fullscreen-on" viewBox="0 0 240 240" focusable="false">
                        <path d="M96.3,186.1c1.9,1.9,1.3,4-1.4,4.4l-50.6,8.4c-1.8,0.5-3.7-0.6-4.2-2.4c-0.2-0.6-0.2-1.2,0-1.7l8.4-50.6c0.4-2.7,2.4-3.4,4.4-1.4l14.5,14.5l28.2-28.2l14.3,14.3l-28.2,28.2L96.3,186.1z M195.8,39.1l-50.6,8.4c-2.7,0.4-3.4,2.4-1.4,4.4l14.5,14.5l-28.2,28.2l14.3,14.3l28.2-28.2l14.5,14.5c1.9,1.9,4,1.3,4.4-1.4l8.4-50.6c0.5-1.8-0.6-3.6-2.4-4.2C197,39,196.4,39,195.8,39.1L195.8,39.1z"></path>
                      </svg>
                      <div class="jw-reset-text jw-tooltip jw-tooltip-undefined" dir="auto">
                        <div class="jw-text"></div>
                      </div>
                    </div><span class="jw-text jw-reset-text jw-text-alt" role="status" dir="auto"></span>
                    <div class="jw-icon jw-icon-inline jw-button-color jw-reset jw-text-live" role="button" tabindex="0" aria-label="Live" style="display: none;">Live</div>
                    <div class="jw-icon jw-icon-inline jw-text jw-reset jw-text-elapsed" role="timer">00:00</div>
                    <div class="jw-icon jw-icon-inline jw-text jw-reset jw-text-countdown" role="timer">00:00</div>
                    <div class="jw-icon jw-icon-inline jw-text jw-reset jw-text-duration" role="timer">00:00</div>
                    <div class="jw-reset jw-spacer"></div>
                    <div class="jw-icon jw-icon-inline jw-button-color jw-reset jw-icon-cast" role="button" tabindex="0" aria-label="Chromecast" style="display: none; cursor: pointer;"><google-cast-launcher tabindex="-1" class="jw-reset jw-off"></google-cast-launcher>
                      <div class="jw-reset-text jw-tooltip jw-tooltip-chromecast" dir="auto">
                        <div class="jw-text"></div>
                      </div>
                    </div>
                    <div class="jw-icon jw-icon-inline jw-button-color jw-reset jw-icon-cc jw-settings-submenu-button jw-off" role="button" tabindex="0" aria-label="Closed Captions" aria-controls="jw-mashable-video-container-3Y0Njq530G-settings-submenu-captions" style="display: none;"><svg xmlns="http://www.w3.org/2000/svg" class="jw-svg-icon jw-svg-icon-cc-on" viewBox="0 0 240 240" focusable="false">
                        <path d="M215,40H25c-2.7,0-5,2.2-5,5v150c0,2.7,2.2,5,5,5h190c2.7,0,5-2.2,5-5V45C220,42.2,217.8,40,215,40z M108.1,137.7c0.7-0.7,1.5-1.5,2.4-2.3l6.6,7.8c-2.2,2.4-5,4.4-8,5.8c-8,3.5-17.3,2.4-24.3-2.9c-3.9-3.6-5.9-8.7-5.5-14v-25.6c0-2.7,0.5-5.3,1.5-7.8c0.9-2.2,2.4-4.3,4.2-5.9c5.7-4.5,13.2-6.2,20.3-4.6c3.3,0.5,6.3,2,8.7,4.3c1.3,1.3,2.5,2.6,3.5,4.2l-7.1,6.9c-2.4-3.7-6.5-5.9-10.9-5.9c-2.4-0.2-4.8,0.7-6.6,2.3c-1.7,1.7-2.5,4.1-2.4,6.5v25.6C90.4,141.7,102,143.5,108.1,137.7z M152.9,137.7c0.7-0.7,1.5-1.5,2.4-2.3l6.6,7.8c-2.2,2.4-5,4.4-8,5.8c-8,3.5-17.3,2.4-24.3-2.9c-3.9-3.6-5.9-8.7-5.5-14v-25.6c0-2.7,0.5-5.3,1.5-7.8c0.9-2.2,2.4-4.3,4.2-5.9c5.7-4.5,13.2-6.2,20.3-4.6c3.3,0.5,6.3,2,8.7,4.3c1.3,1.3,2.5,2.6,3.5,4.2l-7.1,6.9c-2.4-3.7-6.5-5.9-10.9-5.9c-2.4-0.2-4.8,0.7-6.6,2.3c-1.7,1.7-2.5,4.1-2.4,6.5v25.6C135.2,141.7,146.8,143.5,152.9,137.7z"></path>
                      </svg><svg xmlns="http://www.w3.org/2000/svg" class="jw-svg-icon jw-svg-icon-cc-off" viewBox="0 0 240 240" focusable="false">
                        <path d="M99.4,97.8c-2.4-0.2-4.8,0.7-6.6,2.3c-1.7,1.7-2.5,4.1-2.4,6.5v25.6c0,9.6,11.6,11.4,17.7,5.5c0.7-0.7,1.5-1.5,2.4-2.3l6.6,7.8c-2.2,2.4-5,4.4-8,5.8c-8,3.5-17.3,2.4-24.3-2.9c-3.9-3.6-5.9-8.7-5.5-14v-25.6c0-2.7,0.5-5.3,1.5-7.8c0.9-2.2,2.4-4.3,4.2-5.9c5.7-4.5,13.2-6.2,20.3-4.6c3.3,0.5,6.3,2,8.7,4.3c1.3,1.3,2.5,2.6,3.5,4.2l-7.1,6.9C107.9,100,103.8,97.8,99.4,97.8z M144.1,97.8c-2.4-0.2-4.8,0.7-6.6,2.3c-1.7,1.7-2.5,4.1-2.4,6.5v25.6c0,9.6,11.6,11.4,17.7,5.5c0.7-0.7,1.5-1.5,2.4-2.3l6.6,7.8c-2.2,2.4-5,4.4-8,5.8c-8,3.5-17.3,2.4-24.3-2.9c-3.9-3.6-5.9-8.7-5.5-14v-25.6c0-2.7,0.5-5.3,1.5-7.8c0.9-2.2,2.4-4.3,4.2-5.9c5.7-4.5,13.2-6.2,20.3-4.6c3.3,0.5,6.3,2,8.7,4.3c1.3,1.3,2.5,2.6,3.5,4.2l-7.1,6.9C152.6,100,148.5,97.8,144.1,97.8L144.1,97.8z M200,60v120H40V60H200 M215,40H25c-2.7,0-5,2.2-5,5v150c0,2.7,2.2,5,5,5h190c2.7,0,5-2.2,5-5V45C220,42.2,217.8,40,215,40z"></path>
                      </svg>
                      <div class="jw-reset-text jw-tooltip jw-tooltip-captions" dir="auto">
                        <div class="jw-text"></div>
                      </div>
                    </div>
                    <div class="jw-icon jw-icon-inline jw-button-color jw-reset jw-settings-sharing" button="share" role="button" tabindex="0" aria-label="Share" aria-controls="jw-mashable-video-container-3Y0Njq530G-settings-submenu-sharing"><svg xmlns="http://www.w3.org/2000/svg" class="jw-svg-icon jw-svg-icon-sharing" viewBox="0 0 240 240" focusable="false">
                        <path d="M175,160c-6.9,0.2-13.6,2.6-19,7l-62-39c0.8-2.6,1.2-5.3,1-8c0.2-2.7-0.2-5.4-1-8l62-39c5.4,4.4,12.1,6.8,19,7c16.3,0.3,29.7-12.6,30-28.9c0-0.4,0-0.7,0-1.1c0-16.5-13.5-30-30-30s-30,13.5-30,30c-0.2,2.7,0.2,5.4,1,8L84,97c-5.4-4.4-12.1-6.8-19-7c-16.5,0-30,13.5-30,30s13.5,30,30,30c6.9-0.2,13.6-2.6,19-7l62,39c-0.8,2.6-1.2,5.3-1,8c0,16.5,13.5,30,30,30s30-13.5,30-30S191.6,160,175,160z"></path>
                      </svg>
                      <div class="jw-reset-text jw-tooltip jw-tooltip-share" dir="auto">
                        <div class="jw-text"></div>
                      </div>
                    </div>
                    <div class="jw-icon jw-icon-inline jw-button-color jw-reset jw-icon-settings jw-settings-submenu-button" role="button" tabindex="0" aria-label="Settings" aria-controls="jw-mashable-video-container-3Y0Njq530G-settings-menu" aria-expanded="false" style="display: none;"><svg xmlns="http://www.w3.org/2000/svg" class="jw-svg-icon jw-svg-icon-settings" viewBox="0 0 240 240" focusable="false">
                        <path d="M204,145l-25-14c0.8-3.6,1.2-7.3,1-11c0.2-3.7-0.2-7.4-1-11l25-14c2.2-1.6,3.1-4.5,2-7l-16-26c-1.2-2.1-3.8-2.9-6-2l-25,14c-6-4.2-12.3-7.9-19-11V35c0.2-2.6-1.8-4.8-4.4-5c-0.2,0-0.4,0-0.6,0h-30c-2.6-0.2-4.8,1.8-5,4.4c0,0.2,0,0.4,0,0.6v28c-6.7,3.1-13,6.7-19,11L56,60c-2.2-0.9-4.8-0.1-6,2L35,88c-1.6,2.2-1.3,5.3,0.9,6.9c0,0,0.1,0,0.1,0.1l25,14c-0.8,3.6-1.2,7.3-1,11c-0.2,3.7,0.2,7.4,1,11l-25,14c-2.2,1.6-3.1,4.5-2,7l16,26c1.2,2.1,3.8,2.9,6,2l25-14c5.7,4.6,12.2,8.3,19,11v28c-0.2,2.6,1.8,4.8,4.4,5c0.2,0,0.4,0,0.6,0h30c2.6,0.2,4.8-1.8,5-4.4c0-0.2,0-0.4,0-0.6v-28c7-2.3,13.5-6,19-11l25,14c2.5,1.3,5.6,0.4,7-2l15-26C206.7,149.4,206,146.7,204,145z M120,149.9c-16.5,0-30-13.4-30-30s13.4-30,30-30s30,13.4,30,30c0.3,16.3-12.6,29.7-28.9,30C120.7,149.9,120.4,149.9,120,149.9z"></path>
                      </svg>
                      <div class="jw-reset-text jw-tooltip jw-tooltip-settings" dir="auto">
                        <div class="jw-text"></div>
                      </div>
                    </div>
                    <div class="jw-icon jw-icon-inline jw-button-color jw-reset jw-icon-pip" role="button" tabindex="0" aria-label="Picture in Picture (PiP)" style=""><svg xmlns="http://www.w3.org/2000/svg" class="jw-svg-icon jw-svg-icon-pip-on" viewBox="0 0 24 24">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M20 5.125V9.125H22V4.155C22 3.58616 21.5389 3.125 20.97 3.125H2.03C1.46116 3.125 1 3.58613 1 4.155V17.095C1 17.6639 1.46119 18.125 2.03 18.125H12V16.125H3V5.125H20ZM14 11.875C14 11.3227 14.4477 10.875 15 10.875H22C22.5523 10.875 23 11.3227 23 11.875V17.875C23 18.4273 22.5523 18.875 22 18.875H15C14.4477 18.875 14 18.4273 14 17.875V11.875ZM6 12.375L7.79289 10.5821L5.29288 8.0821L6.7071 6.66788L9.20711 9.16789L11 7.375V12.375H6Z"></path>
                      </svg><svg xmlns="http://www.w3.org/2000/svg" class="jw-svg-icon jw-svg-icon-pip-off" viewBox="0 0 24 24">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M20 5.75V9.75H22V4.78C22 4.21116 21.5389 3.75 20.97 3.75H2.03C1.46116 3.75 1 4.21113 1 4.78V17.72C1 18.2889 1.46119 18.75 2.03 18.75H12V16.75H3V5.75H20ZM14 13.25C14 12.6977 14.4477 12.25 15 12.25H22C22.5523 12.25 23 12.6977 23 13.25V19.25C23 19.8023 22.5523 20.25 22 20.25H15C14.4477 20.25 14 19.8023 14 19.25V13.25ZM10 9.25L8.20711 11.0429L10.7071 13.5429L9.29289 14.9571L6.79289 12.4571L5 14.25V9.25H10Z"></path>
                      </svg>
                      <div class="jw-reset-text jw-tooltip jw-tooltip-pip" dir="auto">
                        <div class="jw-text"></div>
                      </div>
                    </div>
                    <div class="jw-icon jw-icon-inline jw-button-color jw-reset jw-icon-fullscreen" role="button" tabindex="0" aria-label="Fullscreen" style=""><svg xmlns="http://www.w3.org/2000/svg" class="jw-svg-icon jw-svg-icon-fullscreen-off" viewBox="0 0 240 240" focusable="false">
                        <path d="M109.2,134.9l-8.4,50.1c-0.4,2.7-2.4,3.3-4.4,1.4L82,172l-27.9,27.9l-14.2-14.2l27.9-27.9l-14.4-14.4c-1.9-1.9-1.3-3.9,1.4-4.4l50.1-8.4c1.8-0.5,3.6,0.6,4.1,2.4C109.4,133.7,109.4,134.3,109.2,134.9L109.2,134.9z M172.1,82.1L200,54.2L185.8,40l-27.9,27.9l-14.4-14.4c-1.9-1.9-3.9-1.3-4.4,1.4l-8.4,50.1c-0.5,1.8,0.6,3.6,2.4,4.1c0.5,0.2,1.2,0.2,1.7,0l50.1-8.4c2.7-0.4,3.3-2.4,1.4-4.4L172.1,82.1z"></path>
                      </svg><svg xmlns="http://www.w3.org/2000/svg" class="jw-svg-icon jw-svg-icon-fullscreen-on" viewBox="0 0 240 240" focusable="false">
                        <path d="M96.3,186.1c1.9,1.9,1.3,4-1.4,4.4l-50.6,8.4c-1.8,0.5-3.7-0.6-4.2-2.4c-0.2-0.6-0.2-1.2,0-1.7l8.4-50.6c0.4-2.7,2.4-3.4,4.4-1.4l14.5,14.5l28.2-28.2l14.3,14.3l-28.2,28.2L96.3,186.1z M195.8,39.1l-50.6,8.4c-2.7,0.4-3.4,2.4-1.4,4.4l14.5,14.5l-28.2,28.2l14.3,14.3l28.2-28.2l14.5,14.5c1.9,1.9,4,1.3,4.4-1.4l8.4-50.6c0.5-1.8-0.6-3.6-2.4-4.2C197,39,196.4,39,195.8,39.1L195.8,39.1z"></path>
                      </svg>
                      <div class="jw-reset-text jw-tooltip jw-tooltip-fullscreen" dir="auto">
                        <div class="jw-text"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="afs_ads ad-placement" style="width: 1px; height: 1px; position: absolute; background: transparent;">&nbsp;</div>
          </div>
        </div>
      </article>
      <section class="mx-auto mt-12 max-w-3xl border-t border-gray-200 border-solid">
        <div class="py-8 w-full">
          <div class="flex flex-col items-center pb-8 border-b border-gray-200 md:flex-row md:space-x-8">
            <div class="flex flex-col justify-center items-center space-y-4 text-center md:flex-row md:justify-start md:space-y-0 md:space-x-6 md:text-left">
              <img class="w-24 h-24 rounded-full border border-gray-200" src="https://helios-i.mashable.com/imagery/authors/05EkIwdTFdfBnl3ahBh6Vjv/image.fill.size_100x100.v1624381061.jpg" alt="Mashable Image" width="100" height="100">
              <div class="flex flex-col space-y-2">
                <div class="w-full font-bold header-200">Chris Taylor</div>
                <div class="w-full text-xl font-bold"></div>
              </div>
            </div>
            <div class="flex flex-row justify-center pt-4 mt-8 w-full border-t border-gray-200 md:justify-start md:pt-0 md:pl-4 md:mt-0 md:w-auto md:border-t-0 md:border-l md:border-solid space-x4" style="height:75px;align-items:center;">
              <a href="https://www.facebook.com/futurechris" class="px-4 hover:text-primary-400" target="_blank" rel="noopener">
                <svg class="inline-block w-5 h-5 fill-current md:h-4">
                  <use href="/images/icons/spritemap.svg#sprite-facebook-f-brands"></use>
                </svg>
                <span class="sr-only">(opens in a new tab)</span>
              </a>
              <a href="https://twitter.com/FutureBoy" class="px-3 hover:text-primary-400" target="_blank" rel="noopener">
                <svg class="inline-block w-5 h-5 fill-current md:h-4">
                  <use href="/images/icons/spritemap.svg#sprite-twitter-brands"></use>
                </svg>
                <span class="sr-only">(opens in a new tab)</span>
              </a>
            </div>
          </div>
          <div class="mx-0 max-w-3xl font-serif editor-content">
            <p>Chris is a veteran journalist and the author of 'How Star Wars Conquered the Universe.' Hailing from the U.K., Chris got his start working as a sub editor on national newspapers in London and Glasgow. He moved to the U.S. in 1996, and became senior news writer for Time.com a year later. In 2000, he was named San Francisco bureau chief for Time magazine. He has served as senior editor for Business 2.0, West Coast editor for Fortune Small Business and West Coast web editor for Fast Company.Chris is a graduate of Merton College, Oxford and the Columbia University Graduate School of Journalism. He is also a long-time volunteer at 826 Valencia, the nationwide after-school program co-founded by author Dave Eggers. His book on the history of Star Wars is an international bestseller and has been translated into 11 languages.</p>
          </div>
        </div>
      </section>
    </section>
    <aside style="width:300px;" class="hidden flex-none 2xl:block" data-ga-module="content_rail">
      <div class="sticky top-0 pt-2">
        <div id="stickyVideoContainer">
        </div>
        <div data-pogo="sidebar"></div>
      </div>
    </aside>
  </main>
  <div class="px-4 mx-auto w-full 2xl:px-0 full-width">
    <div data-pogo="nativespotlight"></div>
  </div>
  <div class="justify-between items-stretch px-4 mx-auto lg:flex max-w-8xl">
    <section class="flex-grow 2xl:pr-4" data-ga-module="content_body">
      <section class="mx-auto max-w-3xl">
        <section class="mx-auto max-w-8xl">
          <hr class="my-8 mx-auto border border-gray-100 md:my-12">
          <div class="mt-12 font-bold header-200 text-primary-400 md:!leading-6">Recommended For You</div>
          <div class="justify-center mt-8 w-full" data-module="content-list" data-ga-module="bibblio-recirc" data-ga-element="content-stripe" data-ga-action="content-stripe">
            <div class="w-full" data-ga-position="1" id="firstRecommendation">
              <div class="flex flex-row mx-auto mt-4 max-w-4xl font-sans md:flex-nowrap md:justify-around md:mx-0 md:mt-8">
                <div class="flex flex-col flex-wrap mr-4 w-3/4 text-left md:mt-0 xl:relative items-around">
                  <a class="recs block text-primary-400 font-semibold leading-6 text-lg header-500 md:text-xl" data-ga-click="" data-ga-item="title" data-ga-label="$text" href="/article/black-mirror-beyond-the-sea-season-6-clues-books" data-bibblio-link="https://api.bibblio.org/v1/activities/0029dc8a-5719-47cf-b566-fe1eac0686bd"
                    data-bibblio-payload="{&quot;type&quot;:&quot;Clicked&quot;,&quot;object&quot;:[[&quot;contentItemId&quot;,&quot;680bc720-4171-483a-8474-c928e455467c&quot;]],&quot;context&quot;:[[&quot;sourceHref&quot;,&quot;https:\/\/api.bibblio.org\/v1\/content-items\/96314440-7622-40a2-9eb0-08ff175bfb30&quot;],[&quot;sourceContentItemId&quot;,&quot;96314440-7622-40a2-9eb0-08ff175bfb30&quot;],[&quot;recommendations.contentItemId&quot;,&quot;680bc720-4171-483a-8474-c928e455467c&quot;],[&quot;recommendations.contentItemId&quot;,&quot;90377454-3373-46e4-9dce-4649dcbefe4f&quot;],[&quot;recommendations.contentItemId&quot;,&quot;c5110dd0-2db2-48f6-a46b-73bec5702241&quot;],[&quot;recommendations.contentItemId&quot;,&quot;29ec9b4e-9b2d-4593-814c-9471a87f2a9d&quot;],[&quot;recommendations.contentItemId&quot;,&quot;2c6b1cf5-d6c2-42d7-a3c9-7c30bbde3921&quot;],[&quot;recommendations.contentItemId&quot;,&quot;46c5a1fa-7d5d-49c8-89b6-9b84c2bfaf96&quot;],[&quot;recommendations.contentItemId&quot;,&quot;789f0fe0-c84b-4888-bc3b-b6f6bae28f8a&quot;],[&quot;recommendations.contentItemId&quot;,&quot;4c8b8a11-1575-42e3-a393-5956a6086882&quot;],[&quot;recommendations.contentItemId&quot;,&quot;718cc952-9070-4b88-a769-8008ea0ced9a&quot;],[&quot;recommendations.contentItemId&quot;,&quot;01921b5e-b371-4f26-ac7d-c005ee425aad&quot;],[&quot;recommendations.catalogueId&quot;,&quot;330050e0-220f-4749-b7e2-3d63c15afafa&quot;],[&quot;recommendations.catalogueId&quot;,&quot;93added7-f049-4406-8429-aba05c9816ac&quot;],[&quot;recommendations.catalogueId&quot;,&quot;cce2a427-339d-49a4-afbc-76a37727db59&quot;],[&quot;recommendations.catalogueId&quot;,&quot;89b8a249-0d12-4a72-b6f9-59b42c647943&quot;]]}">
                    'Black Mirror' dropped tons of bookish clues in 'Beyond the Sea'</a>
                  <div class="hidden text-base md:block md:mt-1 md:leading-tight text-primary-400 font-regular">Read between the lines.</div>
                  <div class="flex flex-row mt-3 font-serif italic md:justify-start">
                    <time class="leading-tight font-regular subtitle-1" datetime="Fri, 16 Jun 2023 17:30:57 +0000">
                      06/16/2023
                    </time>
                    <div class="pl-3 leading-tight md:px-4 font-regular subtitle-1">
                      By <a href="/author/shannon-connellan">Shannon Connellan</a> </div>
                  </div>
                </div>
                <a aria-hidden="true" class="recs block w-1/4" data-ga-click="" data-ga-item="image" data-ga-label="'Black Mirror' dropped tons of bookish clues in 'Beyond the Sea'" href="/article/black-mirror-beyond-the-sea-season-6-clues-books" data-bibblio-link="https://api.bibblio.org/v1/activities/0029dc8a-5719-47cf-b566-fe1eac0686bd"
                  data-bibblio-payload="{&quot;type&quot;:&quot;Clicked&quot;,&quot;object&quot;:[[&quot;contentItemId&quot;,&quot;680bc720-4171-483a-8474-c928e455467c&quot;]],&quot;context&quot;:[[&quot;sourceHref&quot;,&quot;https:\/\/api.bibblio.org\/v1\/content-items\/96314440-7622-40a2-9eb0-08ff175bfb30&quot;],[&quot;sourceContentItemId&quot;,&quot;96314440-7622-40a2-9eb0-08ff175bfb30&quot;],[&quot;recommendations.contentItemId&quot;,&quot;680bc720-4171-483a-8474-c928e455467c&quot;],[&quot;recommendations.contentItemId&quot;,&quot;90377454-3373-46e4-9dce-4649dcbefe4f&quot;],[&quot;recommendations.contentItemId&quot;,&quot;c5110dd0-2db2-48f6-a46b-73bec5702241&quot;],[&quot;recommendations.contentItemId&quot;,&quot;29ec9b4e-9b2d-4593-814c-9471a87f2a9d&quot;],[&quot;recommendations.contentItemId&quot;,&quot;2c6b1cf5-d6c2-42d7-a3c9-7c30bbde3921&quot;],[&quot;recommendations.contentItemId&quot;,&quot;46c5a1fa-7d5d-49c8-89b6-9b84c2bfaf96&quot;],[&quot;recommendations.contentItemId&quot;,&quot;789f0fe0-c84b-4888-bc3b-b6f6bae28f8a&quot;],[&quot;recommendations.contentItemId&quot;,&quot;4c8b8a11-1575-42e3-a393-5956a6086882&quot;],[&quot;recommendations.contentItemId&quot;,&quot;718cc952-9070-4b88-a769-8008ea0ced9a&quot;],[&quot;recommendations.contentItemId&quot;,&quot;01921b5e-b371-4f26-ac7d-c005ee425aad&quot;],[&quot;recommendations.catalogueId&quot;,&quot;330050e0-220f-4749-b7e2-3d63c15afafa&quot;],[&quot;recommendations.catalogueId&quot;,&quot;93added7-f049-4406-8429-aba05c9816ac&quot;],[&quot;recommendations.catalogueId&quot;,&quot;cce2a427-339d-49a4-afbc-76a37727db59&quot;],[&quot;recommendations.catalogueId&quot;,&quot;89b8a249-0d12-4a72-b6f9-59b42c647943&quot;]]}">
                  <div class="hidden border border-gray-100 md:block">
                    <img class="w-full" src="https://helios-i.mashable.com/imagery/articles/000yriOhugVN1727ZyhmEna/hero-image.fill.size_220x133.v1686927573.jpg" alt="In the TV show &quot;Black Mirror,&quot; Kate Mara stands in a field beyond a barn in a red cardigan." width="220" height="133" loading="lazy">
                  </div>
                  <div class="border border-gray-100 md:hidden">
                    <img class="w-full" src="https://helios-i.mashable.com/imagery/articles/000yriOhugVN1727ZyhmEna/hero-image.fill.size_220x220.v1686927573.jpg" alt="In the TV show &quot;Black Mirror,&quot; Kate Mara stands in a field beyond a barn in a red cardigan." width="220" height="220" loading="lazy">
                  </div>
                </a>
              </div>
            </div>
            <hr class="my-6 mx-auto w-3/4 border border-gray-100 md:hidden md:my-8">
            <div class="w-full" data-ga-position="2">
              <div class="flex flex-row mx-auto mt-4 max-w-4xl font-sans md:flex-nowrap md:justify-around md:mx-0 md:mt-8">
                <div class="flex flex-col flex-wrap mr-4 w-3/4 text-left md:mt-0 xl:relative items-around">
                  <a class="recs block text-primary-400 font-semibold leading-6 text-lg header-500 md:text-xl" data-ga-click="" data-ga-item="title" data-ga-label="$text" href="/article/the-idol-finale-hbo-hairbrush-jocelyn-tedros-twist-meaning" data-bibblio-link="https://api.bibblio.org/v1/activities/0029dc8a-5719-47cf-b566-fe1eac0686bd"
                    data-bibblio-payload="{&quot;type&quot;:&quot;Clicked&quot;,&quot;object&quot;:[[&quot;contentItemId&quot;,&quot;90377454-3373-46e4-9dce-4649dcbefe4f&quot;]],&quot;context&quot;:[[&quot;sourceHref&quot;,&quot;https:\/\/api.bibblio.org\/v1\/content-items\/96314440-7622-40a2-9eb0-08ff175bfb30&quot;],[&quot;sourceContentItemId&quot;,&quot;96314440-7622-40a2-9eb0-08ff175bfb30&quot;],[&quot;recommendations.contentItemId&quot;,&quot;680bc720-4171-483a-8474-c928e455467c&quot;],[&quot;recommendations.contentItemId&quot;,&quot;90377454-3373-46e4-9dce-4649dcbefe4f&quot;],[&quot;recommendations.contentItemId&quot;,&quot;c5110dd0-2db2-48f6-a46b-73bec5702241&quot;],[&quot;recommendations.contentItemId&quot;,&quot;29ec9b4e-9b2d-4593-814c-9471a87f2a9d&quot;],[&quot;recommendations.contentItemId&quot;,&quot;2c6b1cf5-d6c2-42d7-a3c9-7c30bbde3921&quot;],[&quot;recommendations.contentItemId&quot;,&quot;46c5a1fa-7d5d-49c8-89b6-9b84c2bfaf96&quot;],[&quot;recommendations.contentItemId&quot;,&quot;789f0fe0-c84b-4888-bc3b-b6f6bae28f8a&quot;],[&quot;recommendations.contentItemId&quot;,&quot;4c8b8a11-1575-42e3-a393-5956a6086882&quot;],[&quot;recommendations.contentItemId&quot;,&quot;718cc952-9070-4b88-a769-8008ea0ced9a&quot;],[&quot;recommendations.contentItemId&quot;,&quot;01921b5e-b371-4f26-ac7d-c005ee425aad&quot;],[&quot;recommendations.catalogueId&quot;,&quot;330050e0-220f-4749-b7e2-3d63c15afafa&quot;],[&quot;recommendations.catalogueId&quot;,&quot;93added7-f049-4406-8429-aba05c9816ac&quot;],[&quot;recommendations.catalogueId&quot;,&quot;cce2a427-339d-49a4-afbc-76a37727db59&quot;],[&quot;recommendations.catalogueId&quot;,&quot;89b8a249-0d12-4a72-b6f9-59b42c647943&quot;]]}">
                    'The Idol's finale twist makes absolutely zero sense</a>
                  <div class="hidden text-base md:block md:mt-1 md:leading-tight text-primary-400 font-regular">At least Tedros Tedros is out of our lives.</div>
                  <div class="flex flex-row mt-3 font-serif italic md:justify-start">
                    <time class="leading-tight font-regular subtitle-1" datetime="Mon, 03 Jul 2023 16:34:36 +0000">
                      07/03/2023
                    </time>
                    <div class="pl-3 leading-tight md:px-4 font-regular subtitle-1">
                      By <a href="/author/belen-edwards">Belen Edwards</a> </div>
                  </div>
                </div>
                <a aria-hidden="true" class="recs block w-1/4" data-ga-click="" data-ga-item="image" data-ga-label="'The Idol's finale twist makes absolutely zero sense" href="/article/the-idol-finale-hbo-hairbrush-jocelyn-tedros-twist-meaning" data-bibblio-link="https://api.bibblio.org/v1/activities/0029dc8a-5719-47cf-b566-fe1eac0686bd"
                  data-bibblio-payload="{&quot;type&quot;:&quot;Clicked&quot;,&quot;object&quot;:[[&quot;contentItemId&quot;,&quot;90377454-3373-46e4-9dce-4649dcbefe4f&quot;]],&quot;context&quot;:[[&quot;sourceHref&quot;,&quot;https:\/\/api.bibblio.org\/v1\/content-items\/96314440-7622-40a2-9eb0-08ff175bfb30&quot;],[&quot;sourceContentItemId&quot;,&quot;96314440-7622-40a2-9eb0-08ff175bfb30&quot;],[&quot;recommendations.contentItemId&quot;,&quot;680bc720-4171-483a-8474-c928e455467c&quot;],[&quot;recommendations.contentItemId&quot;,&quot;90377454-3373-46e4-9dce-4649dcbefe4f&quot;],[&quot;recommendations.contentItemId&quot;,&quot;c5110dd0-2db2-48f6-a46b-73bec5702241&quot;],[&quot;recommendations.contentItemId&quot;,&quot;29ec9b4e-9b2d-4593-814c-9471a87f2a9d&quot;],[&quot;recommendations.contentItemId&quot;,&quot;2c6b1cf5-d6c2-42d7-a3c9-7c30bbde3921&quot;],[&quot;recommendations.contentItemId&quot;,&quot;46c5a1fa-7d5d-49c8-89b6-9b84c2bfaf96&quot;],[&quot;recommendations.contentItemId&quot;,&quot;789f0fe0-c84b-4888-bc3b-b6f6bae28f8a&quot;],[&quot;recommendations.contentItemId&quot;,&quot;4c8b8a11-1575-42e3-a393-5956a6086882&quot;],[&quot;recommendations.contentItemId&quot;,&quot;718cc952-9070-4b88-a769-8008ea0ced9a&quot;],[&quot;recommendations.contentItemId&quot;,&quot;01921b5e-b371-4f26-ac7d-c005ee425aad&quot;],[&quot;recommendations.catalogueId&quot;,&quot;330050e0-220f-4749-b7e2-3d63c15afafa&quot;],[&quot;recommendations.catalogueId&quot;,&quot;93added7-f049-4406-8429-aba05c9816ac&quot;],[&quot;recommendations.catalogueId&quot;,&quot;cce2a427-339d-49a4-afbc-76a37727db59&quot;],[&quot;recommendations.catalogueId&quot;,&quot;89b8a249-0d12-4a72-b6f9-59b42c647943&quot;]]}">
                  <div class="hidden border border-gray-100 md:block">
                    <img class="w-full" src="https://helios-i.mashable.com/imagery/articles/03fvR4TdSD0lOdAJKPk0P2m/hero-image.fill.size_220x133.v1688398767.jpg" alt="A woman in a white dress onstage at a stadium concert speaks to a man in a blue suit." width="220" height="133" loading="lazy">
                  </div>
                  <div class="border border-gray-100 md:hidden">
                    <img class="w-full" src="https://helios-i.mashable.com/imagery/articles/03fvR4TdSD0lOdAJKPk0P2m/hero-image.fill.size_220x220.v1688398767.jpg" alt="A woman in a white dress onstage at a stadium concert speaks to a man in a blue suit." width="220" height="220" loading="lazy">
                  </div>
                </a>
              </div>
            </div>
            <hr class="my-6 mx-auto w-3/4 border border-gray-100 md:hidden md:my-8">
            <div class="w-full" data-ga-position="3">
              <div class="flex flex-row mx-auto mt-4 max-w-4xl font-sans md:flex-nowrap md:justify-around md:mx-0 md:mt-8">
                <div class="flex flex-col flex-wrap mr-4 w-3/4 text-left md:mt-0 xl:relative items-around">
                  <a class="recs block text-primary-400 font-semibold leading-6 text-lg header-500 md:text-xl" data-ga-click="" data-ga-item="title" data-ga-label="$text" href="/video/final-cut-trailer" data-bibblio-link="https://api.bibblio.org/v1/activities/0029dc8a-5719-47cf-b566-fe1eac0686bd"
                    data-bibblio-payload="{&quot;type&quot;:&quot;Clicked&quot;,&quot;object&quot;:[[&quot;contentItemId&quot;,&quot;c5110dd0-2db2-48f6-a46b-73bec5702241&quot;]],&quot;context&quot;:[[&quot;sourceHref&quot;,&quot;https:\/\/api.bibblio.org\/v1\/content-items\/96314440-7622-40a2-9eb0-08ff175bfb30&quot;],[&quot;sourceContentItemId&quot;,&quot;96314440-7622-40a2-9eb0-08ff175bfb30&quot;],[&quot;recommendations.contentItemId&quot;,&quot;680bc720-4171-483a-8474-c928e455467c&quot;],[&quot;recommendations.contentItemId&quot;,&quot;90377454-3373-46e4-9dce-4649dcbefe4f&quot;],[&quot;recommendations.contentItemId&quot;,&quot;c5110dd0-2db2-48f6-a46b-73bec5702241&quot;],[&quot;recommendations.contentItemId&quot;,&quot;29ec9b4e-9b2d-4593-814c-9471a87f2a9d&quot;],[&quot;recommendations.contentItemId&quot;,&quot;2c6b1cf5-d6c2-42d7-a3c9-7c30bbde3921&quot;],[&quot;recommendations.contentItemId&quot;,&quot;46c5a1fa-7d5d-49c8-89b6-9b84c2bfaf96&quot;],[&quot;recommendations.contentItemId&quot;,&quot;789f0fe0-c84b-4888-bc3b-b6f6bae28f8a&quot;],[&quot;recommendations.contentItemId&quot;,&quot;4c8b8a11-1575-42e3-a393-5956a6086882&quot;],[&quot;recommendations.contentItemId&quot;,&quot;718cc952-9070-4b88-a769-8008ea0ced9a&quot;],[&quot;recommendations.contentItemId&quot;,&quot;01921b5e-b371-4f26-ac7d-c005ee425aad&quot;],[&quot;recommendations.catalogueId&quot;,&quot;330050e0-220f-4749-b7e2-3d63c15afafa&quot;],[&quot;recommendations.catalogueId&quot;,&quot;93added7-f049-4406-8429-aba05c9816ac&quot;],[&quot;recommendations.catalogueId&quot;,&quot;cce2a427-339d-49a4-afbc-76a37727db59&quot;],[&quot;recommendations.catalogueId&quot;,&quot;89b8a249-0d12-4a72-b6f9-59b42c647943&quot;]]}">
                    A zombie movie goes off the rails in 'Final Cut' trailer</a>
                  <div class="hidden text-base md:block md:mt-1 md:leading-tight text-primary-400 font-regular">You've never seen gore this goofy.</div>
                  <div class="flex flex-row mt-3 font-serif italic md:justify-start">
                    <time class="leading-tight font-regular subtitle-1" datetime="Mon, 26 Jun 2023 19:17:13 +0000">
                      06/26/2023
                    </time>
                    <div class="pl-3 leading-tight md:px-4 font-regular subtitle-1">
                      By <a href="/author/belen-edwards">Belen Edwards</a> </div>
                  </div>
                </div>
                <a aria-hidden="true" class="recs block w-1/4" data-ga-click="" data-ga-item="image" data-ga-label="A zombie movie goes off the rails in 'Final Cut' trailer" href="/video/final-cut-trailer" data-bibblio-link="https://api.bibblio.org/v1/activities/0029dc8a-5719-47cf-b566-fe1eac0686bd"
                  data-bibblio-payload="{&quot;type&quot;:&quot;Clicked&quot;,&quot;object&quot;:[[&quot;contentItemId&quot;,&quot;c5110dd0-2db2-48f6-a46b-73bec5702241&quot;]],&quot;context&quot;:[[&quot;sourceHref&quot;,&quot;https:\/\/api.bibblio.org\/v1\/content-items\/96314440-7622-40a2-9eb0-08ff175bfb30&quot;],[&quot;sourceContentItemId&quot;,&quot;96314440-7622-40a2-9eb0-08ff175bfb30&quot;],[&quot;recommendations.contentItemId&quot;,&quot;680bc720-4171-483a-8474-c928e455467c&quot;],[&quot;recommendations.contentItemId&quot;,&quot;90377454-3373-46e4-9dce-4649dcbefe4f&quot;],[&quot;recommendations.contentItemId&quot;,&quot;c5110dd0-2db2-48f6-a46b-73bec5702241&quot;],[&quot;recommendations.contentItemId&quot;,&quot;29ec9b4e-9b2d-4593-814c-9471a87f2a9d&quot;],[&quot;recommendations.contentItemId&quot;,&quot;2c6b1cf5-d6c2-42d7-a3c9-7c30bbde3921&quot;],[&quot;recommendations.contentItemId&quot;,&quot;46c5a1fa-7d5d-49c8-89b6-9b84c2bfaf96&quot;],[&quot;recommendations.contentItemId&quot;,&quot;789f0fe0-c84b-4888-bc3b-b6f6bae28f8a&quot;],[&quot;recommendations.contentItemId&quot;,&quot;4c8b8a11-1575-42e3-a393-5956a6086882&quot;],[&quot;recommendations.contentItemId&quot;,&quot;718cc952-9070-4b88-a769-8008ea0ced9a&quot;],[&quot;recommendations.contentItemId&quot;,&quot;01921b5e-b371-4f26-ac7d-c005ee425aad&quot;],[&quot;recommendations.catalogueId&quot;,&quot;330050e0-220f-4749-b7e2-3d63c15afafa&quot;],[&quot;recommendations.catalogueId&quot;,&quot;93added7-f049-4406-8429-aba05c9816ac&quot;],[&quot;recommendations.catalogueId&quot;,&quot;cce2a427-339d-49a4-afbc-76a37727db59&quot;],[&quot;recommendations.catalogueId&quot;,&quot;89b8a249-0d12-4a72-b6f9-59b42c647943&quot;]]}">
                  <div class="hidden border border-gray-100 md:block">
                    <div class="relative">
                      <svg class="block absolute bottom-0.5 right-2.5 -mb-1 w-7 h-10 text-white fill-current">
                        <use href="/images/icons/spritemap.svg#sprite-icon-video-tag"></use>
                      </svg>
                      <img class="w-full" src="https://helios-i.mashable.com/imagery/videos/053QM7TURPAVDTrPqQlr3i5/hero-image.fill.size_220x133.v1687799268.jpg" alt="A woman in a yellow shirt raises an axe." width="220" height="133" loading="lazy">
                    </div>
                  </div>
                  <div class="border border-gray-100 md:hidden">
                    <div class="relative">
                      <svg class="block absolute bottom-0.5 right-2.5 -mb-1 w-7 h-10 text-white fill-current">
                        <use href="/images/icons/spritemap.svg#sprite-icon-video-tag"></use>
                      </svg>
                      <img class="w-full" src="https://helios-i.mashable.com/imagery/videos/053QM7TURPAVDTrPqQlr3i5/hero-image.fill.size_220x220.v1687799268.jpg" alt="A woman in a yellow shirt raises an axe." width="220" height="220" loading="lazy">
                    </div>
                  </div>
                </a>
              </div>
            </div>
            <hr class="my-6 mx-auto w-3/4 border border-gray-100 md:hidden md:my-8">
            <div class="w-full" data-ga-position="4">
              <div class="flex flex-row mx-auto mt-4 max-w-4xl font-sans md:flex-nowrap md:justify-around md:mx-0 md:mt-8">
                <div class="flex flex-col flex-wrap mr-4 w-3/4 text-left md:mt-0 xl:relative items-around">
                  <a class="recs block text-primary-400 font-semibold leading-6 text-lg header-500 md:text-xl" data-ga-click="" data-ga-item="title" data-ga-label="$text" href="/article/nimona-animation-style" data-bibblio-link="https://api.bibblio.org/v1/activities/0029dc8a-5719-47cf-b566-fe1eac0686bd"
                    data-bibblio-payload="{&quot;type&quot;:&quot;Clicked&quot;,&quot;object&quot;:[[&quot;contentItemId&quot;,&quot;29ec9b4e-9b2d-4593-814c-9471a87f2a9d&quot;]],&quot;context&quot;:[[&quot;sourceHref&quot;,&quot;https:\/\/api.bibblio.org\/v1\/content-items\/96314440-7622-40a2-9eb0-08ff175bfb30&quot;],[&quot;sourceContentItemId&quot;,&quot;96314440-7622-40a2-9eb0-08ff175bfb30&quot;],[&quot;recommendations.contentItemId&quot;,&quot;680bc720-4171-483a-8474-c928e455467c&quot;],[&quot;recommendations.contentItemId&quot;,&quot;90377454-3373-46e4-9dce-4649dcbefe4f&quot;],[&quot;recommendations.contentItemId&quot;,&quot;c5110dd0-2db2-48f6-a46b-73bec5702241&quot;],[&quot;recommendations.contentItemId&quot;,&quot;29ec9b4e-9b2d-4593-814c-9471a87f2a9d&quot;],[&quot;recommendations.contentItemId&quot;,&quot;2c6b1cf5-d6c2-42d7-a3c9-7c30bbde3921&quot;],[&quot;recommendations.contentItemId&quot;,&quot;46c5a1fa-7d5d-49c8-89b6-9b84c2bfaf96&quot;],[&quot;recommendations.contentItemId&quot;,&quot;789f0fe0-c84b-4888-bc3b-b6f6bae28f8a&quot;],[&quot;recommendations.contentItemId&quot;,&quot;4c8b8a11-1575-42e3-a393-5956a6086882&quot;],[&quot;recommendations.contentItemId&quot;,&quot;718cc952-9070-4b88-a769-8008ea0ced9a&quot;],[&quot;recommendations.contentItemId&quot;,&quot;01921b5e-b371-4f26-ac7d-c005ee425aad&quot;],[&quot;recommendations.catalogueId&quot;,&quot;330050e0-220f-4749-b7e2-3d63c15afafa&quot;],[&quot;recommendations.catalogueId&quot;,&quot;93added7-f049-4406-8429-aba05c9816ac&quot;],[&quot;recommendations.catalogueId&quot;,&quot;cce2a427-339d-49a4-afbc-76a37727db59&quot;],[&quot;recommendations.catalogueId&quot;,&quot;89b8a249-0d12-4a72-b6f9-59b42c647943&quot;]]}">
                    How 'Nimona' crafted its unique animation style</a>
                  <div class="hidden text-base md:block md:mt-1 md:leading-tight text-primary-400 font-regular">Hear from directors Nick Bruno and Troy Quane, VFX supervisor Archie Donato, and animation director Ted Ty.</div>
                  <div class="flex flex-row mt-3 font-serif italic md:justify-start">
                    <time class="leading-tight font-regular subtitle-1" datetime="Fri, 30 Jun 2023 09:00:00 +0000">
                      06/30/2023
                    </time>
                    <div class="pl-3 leading-tight md:px-4 font-regular subtitle-1">
                      By <a href="/author/belen-edwards">Belen Edwards</a> </div>
                  </div>
                </div>
                <a aria-hidden="true" class="recs block w-1/4" data-ga-click="" data-ga-item="image" data-ga-label="How 'Nimona' crafted its unique animation style" href="/article/nimona-animation-style" data-bibblio-link="https://api.bibblio.org/v1/activities/0029dc8a-5719-47cf-b566-fe1eac0686bd"
                  data-bibblio-payload="{&quot;type&quot;:&quot;Clicked&quot;,&quot;object&quot;:[[&quot;contentItemId&quot;,&quot;29ec9b4e-9b2d-4593-814c-9471a87f2a9d&quot;]],&quot;context&quot;:[[&quot;sourceHref&quot;,&quot;https:\/\/api.bibblio.org\/v1\/content-items\/96314440-7622-40a2-9eb0-08ff175bfb30&quot;],[&quot;sourceContentItemId&quot;,&quot;96314440-7622-40a2-9eb0-08ff175bfb30&quot;],[&quot;recommendations.contentItemId&quot;,&quot;680bc720-4171-483a-8474-c928e455467c&quot;],[&quot;recommendations.contentItemId&quot;,&quot;90377454-3373-46e4-9dce-4649dcbefe4f&quot;],[&quot;recommendations.contentItemId&quot;,&quot;c5110dd0-2db2-48f6-a46b-73bec5702241&quot;],[&quot;recommendations.contentItemId&quot;,&quot;29ec9b4e-9b2d-4593-814c-9471a87f2a9d&quot;],[&quot;recommendations.contentItemId&quot;,&quot;2c6b1cf5-d6c2-42d7-a3c9-7c30bbde3921&quot;],[&quot;recommendations.contentItemId&quot;,&quot;46c5a1fa-7d5d-49c8-89b6-9b84c2bfaf96&quot;],[&quot;recommendations.contentItemId&quot;,&quot;789f0fe0-c84b-4888-bc3b-b6f6bae28f8a&quot;],[&quot;recommendations.contentItemId&quot;,&quot;4c8b8a11-1575-42e3-a393-5956a6086882&quot;],[&quot;recommendations.contentItemId&quot;,&quot;718cc952-9070-4b88-a769-8008ea0ced9a&quot;],[&quot;recommendations.contentItemId&quot;,&quot;01921b5e-b371-4f26-ac7d-c005ee425aad&quot;],[&quot;recommendations.catalogueId&quot;,&quot;330050e0-220f-4749-b7e2-3d63c15afafa&quot;],[&quot;recommendations.catalogueId&quot;,&quot;93added7-f049-4406-8429-aba05c9816ac&quot;],[&quot;recommendations.catalogueId&quot;,&quot;cce2a427-339d-49a4-afbc-76a37727db59&quot;],[&quot;recommendations.catalogueId&quot;,&quot;89b8a249-0d12-4a72-b6f9-59b42c647943&quot;]]}">
                  <div class="hidden border border-gray-100 md:block">
                    <img class="w-full" src="https://helios-i.mashable.com/imagery/articles/035JzfsBY30p54VqrkxVMkL/hero-image.fill.size_220x133.v1688060603.jpg" alt="A young girl with pink hair grins, exposing fangs, and extends her hands in a big gesture." width="220" height="133" loading="lazy">
                  </div>
                  <div class="border border-gray-100 md:hidden">
                    <img class="w-full" src="https://helios-i.mashable.com/imagery/articles/035JzfsBY30p54VqrkxVMkL/hero-image.fill.size_220x220.v1688060603.jpg" alt="A young girl with pink hair grins, exposing fangs, and extends her hands in a big gesture." width="220" height="220" loading="lazy">
                  </div>
                </a>
              </div>
            </div>
            <hr class="my-6 mx-auto w-3/4 border border-gray-100 md:hidden md:my-8">
            <div class="w-full" data-ga-position="5">
              <div class="flex flex-row mx-auto mt-4 max-w-4xl font-sans md:flex-nowrap md:justify-around md:mx-0 md:mt-8">
                <div class="flex flex-col flex-wrap mr-4 w-3/4 text-left md:mt-0 xl:relative items-around">
                  <a class="recs block text-primary-400 font-semibold leading-6 text-lg header-500 md:text-xl" data-ga-click="" data-ga-item="title" data-ga-label="$text" href="/article/final-cut-review" data-bibblio-link="https://api.bibblio.org/v1/activities/0029dc8a-5719-47cf-b566-fe1eac0686bd"
                    data-bibblio-payload="{&quot;type&quot;:&quot;Clicked&quot;,&quot;object&quot;:[[&quot;contentItemId&quot;,&quot;2c6b1cf5-d6c2-42d7-a3c9-7c30bbde3921&quot;]],&quot;context&quot;:[[&quot;sourceHref&quot;,&quot;https:\/\/api.bibblio.org\/v1\/content-items\/96314440-7622-40a2-9eb0-08ff175bfb30&quot;],[&quot;sourceContentItemId&quot;,&quot;96314440-7622-40a2-9eb0-08ff175bfb30&quot;],[&quot;recommendations.contentItemId&quot;,&quot;680bc720-4171-483a-8474-c928e455467c&quot;],[&quot;recommendations.contentItemId&quot;,&quot;90377454-3373-46e4-9dce-4649dcbefe4f&quot;],[&quot;recommendations.contentItemId&quot;,&quot;c5110dd0-2db2-48f6-a46b-73bec5702241&quot;],[&quot;recommendations.contentItemId&quot;,&quot;29ec9b4e-9b2d-4593-814c-9471a87f2a9d&quot;],[&quot;recommendations.contentItemId&quot;,&quot;2c6b1cf5-d6c2-42d7-a3c9-7c30bbde3921&quot;],[&quot;recommendations.contentItemId&quot;,&quot;46c5a1fa-7d5d-49c8-89b6-9b84c2bfaf96&quot;],[&quot;recommendations.contentItemId&quot;,&quot;789f0fe0-c84b-4888-bc3b-b6f6bae28f8a&quot;],[&quot;recommendations.contentItemId&quot;,&quot;4c8b8a11-1575-42e3-a393-5956a6086882&quot;],[&quot;recommendations.contentItemId&quot;,&quot;718cc952-9070-4b88-a769-8008ea0ced9a&quot;],[&quot;recommendations.contentItemId&quot;,&quot;01921b5e-b371-4f26-ac7d-c005ee425aad&quot;],[&quot;recommendations.catalogueId&quot;,&quot;330050e0-220f-4749-b7e2-3d63c15afafa&quot;],[&quot;recommendations.catalogueId&quot;,&quot;93added7-f049-4406-8429-aba05c9816ac&quot;],[&quot;recommendations.catalogueId&quot;,&quot;cce2a427-339d-49a4-afbc-76a37727db59&quot;],[&quot;recommendations.catalogueId&quot;,&quot;89b8a249-0d12-4a72-b6f9-59b42c647943&quot;]]}">
                    'Final Cut' review: Why does this meta zombie remake exist?</a>
                  <div class="hidden text-base md:block md:mt-1 md:leading-tight text-primary-400 font-regular">Blood, guts, and questions about remakes abound.</div>
                  <div class="flex flex-row mt-3 font-serif italic md:justify-start">
                    <time class="leading-tight font-regular subtitle-1" datetime="Wed, 14 Jun 2023 09:00:00 +0000">
                      06/14/2023
                    </time>
                    <div class="pl-3 leading-tight md:px-4 font-regular subtitle-1">
                      By <a href="/author/belen-edwards">Belen Edwards</a> </div>
                  </div>
                </div>
                <a aria-hidden="true" class="recs block w-1/4" data-ga-click="" data-ga-item="image" data-ga-label="'Final Cut' review: Why does this meta zombie remake exist?" href="/article/final-cut-review" data-bibblio-link="https://api.bibblio.org/v1/activities/0029dc8a-5719-47cf-b566-fe1eac0686bd"
                  data-bibblio-payload="{&quot;type&quot;:&quot;Clicked&quot;,&quot;object&quot;:[[&quot;contentItemId&quot;,&quot;2c6b1cf5-d6c2-42d7-a3c9-7c30bbde3921&quot;]],&quot;context&quot;:[[&quot;sourceHref&quot;,&quot;https:\/\/api.bibblio.org\/v1\/content-items\/96314440-7622-40a2-9eb0-08ff175bfb30&quot;],[&quot;sourceContentItemId&quot;,&quot;96314440-7622-40a2-9eb0-08ff175bfb30&quot;],[&quot;recommendations.contentItemId&quot;,&quot;680bc720-4171-483a-8474-c928e455467c&quot;],[&quot;recommendations.contentItemId&quot;,&quot;90377454-3373-46e4-9dce-4649dcbefe4f&quot;],[&quot;recommendations.contentItemId&quot;,&quot;c5110dd0-2db2-48f6-a46b-73bec5702241&quot;],[&quot;recommendations.contentItemId&quot;,&quot;29ec9b4e-9b2d-4593-814c-9471a87f2a9d&quot;],[&quot;recommendations.contentItemId&quot;,&quot;2c6b1cf5-d6c2-42d7-a3c9-7c30bbde3921&quot;],[&quot;recommendations.contentItemId&quot;,&quot;46c5a1fa-7d5d-49c8-89b6-9b84c2bfaf96&quot;],[&quot;recommendations.contentItemId&quot;,&quot;789f0fe0-c84b-4888-bc3b-b6f6bae28f8a&quot;],[&quot;recommendations.contentItemId&quot;,&quot;4c8b8a11-1575-42e3-a393-5956a6086882&quot;],[&quot;recommendations.contentItemId&quot;,&quot;718cc952-9070-4b88-a769-8008ea0ced9a&quot;],[&quot;recommendations.contentItemId&quot;,&quot;01921b5e-b371-4f26-ac7d-c005ee425aad&quot;],[&quot;recommendations.catalogueId&quot;,&quot;330050e0-220f-4749-b7e2-3d63c15afafa&quot;],[&quot;recommendations.catalogueId&quot;,&quot;93added7-f049-4406-8429-aba05c9816ac&quot;],[&quot;recommendations.catalogueId&quot;,&quot;cce2a427-339d-49a4-afbc-76a37727db59&quot;],[&quot;recommendations.catalogueId&quot;,&quot;89b8a249-0d12-4a72-b6f9-59b42c647943&quot;]]}">
                  <div class="hidden border border-gray-100 md:block">
                    <img class="w-full" src="https://helios-i.mashable.com/imagery/articles/07GStG8ePL50icHFEvRt7GB/hero-image.fill.size_220x133.v1686677093.jpg" alt="A man holding a movie camera and a young woman dressed all in black hold back a woman with blood all over her face." width="220" height="133" loading="lazy">
                  </div>
                  <div class="border border-gray-100 md:hidden">
                    <img class="w-full" src="https://helios-i.mashable.com/imagery/articles/07GStG8ePL50icHFEvRt7GB/hero-image.fill.size_220x220.v1686677093.jpg" alt="A man holding a movie camera and a young woman dressed all in black hold back a woman with blood all over her face." width="220" height="220" loading="lazy">
                  </div>
                </a>
              </div>
            </div>
          </div>
        </section>
        <section class="mx-auto max-w-8xl">
          <hr class="my-8 mx-auto border border-gray-100 md:my-12">
          <div class="mt-12 font-bold header-200 text-primary-400 md:!leading-6">Trending on Mashable</div>
          <div class="justify-center mt-8 w-full" data-module="content-list" data-ga-module="chartbeat-recirc" data-ga-element="content-stripe" data-ga-action="content-stripe">
            <div class="w-full" data-ga-position="1">
              <div class="flex flex-row mx-auto mt-4 max-w-4xl font-sans md:flex-nowrap md:justify-around md:mx-0 md:mt-8">
                <div class="flex flex-col flex-wrap mr-4 w-3/4 text-left md:mt-0 xl:relative items-around">
                  <a class=" block text-primary-400 font-semibold leading-6 text-lg header-500 md:text-xl" data-ga-click="" data-ga-item="title" data-ga-label="$text" href="/article/wordle-today-answer-july-6-2023">
                    Wordle today: Here's the answer and hints for July 6</a>
                  <div class="hidden text-base md:block md:mt-1 md:leading-tight text-primary-400 font-regular">Here are some tips and tricks to help you find the answer to "Wordle" #747.</div>
                  <div class="flex flex-row mt-3 font-serif italic md:justify-start">
                    <time class="leading-tight font-regular subtitle-1" datetime="Wed, 05 Jul 2023 23:06:22 +0000">
                      8 hours ago
                    </time>
                    <div class="pl-3 leading-tight md:px-4 font-regular subtitle-1">
                      By <a href="/author/mashable-team">Mashable Team</a> </div>
                  </div>
                </div>
                <a aria-hidden="true" class=" block w-1/4" data-ga-click="" data-ga-item="image" data-ga-label="Wordle today: Here's the answer and hints for July 6" href="/article/wordle-today-answer-july-6-2023">
                  <div class="hidden border border-gray-100 md:block">
                    <img class="w-full" src="https://helios-i.mashable.com/imagery/articles/01dRwzozW7SRTOdhdrXHGji/hero-image.fill.size_220x133.v1688597877.jpg" alt="Woman plays Wordle on her smartphone from the living room of her home" width="220" height="133" loading="lazy">
                  </div>
                  <div class="border border-gray-100 md:hidden">
                    <img class="w-full" src="https://helios-i.mashable.com/imagery/articles/01dRwzozW7SRTOdhdrXHGji/hero-image.fill.size_220x220.v1688597877.jpg" alt="Woman plays Wordle on her smartphone from the living room of her home" width="220" height="220" loading="lazy">
                  </div>
                </a>
              </div>
            </div>
            <hr class="my-6 mx-auto w-3/4 border border-gray-100 md:hidden md:my-8">
            <div class="w-full" data-ga-position="2">
              <div class="flex flex-row mx-auto mt-4 max-w-4xl font-sans md:flex-nowrap md:justify-around md:mx-0 md:mt-8">
                <div class="flex flex-col flex-wrap mr-4 w-3/4 text-left md:mt-0 xl:relative items-around">
                  <a class=" block text-primary-400 font-semibold leading-6 text-lg header-500 md:text-xl" data-ga-click="" data-ga-item="title" data-ga-label="$text" href="/article/run-rabbit-run-netflix-movie-ending-explainer">
                    We need to talk about 'Run Rabbit Run's twisted ending</a>
                  <div class="hidden text-base md:block md:mt-1 md:leading-tight text-primary-400 font-regular">That's a lot to unpack.</div>
                  <div class="flex flex-row mt-3 font-serif italic md:justify-start">
                    <time class="leading-tight font-regular subtitle-1" datetime="Wed, 05 Jul 2023 17:23:58 +0000">
                      14 hours ago
                    </time>
                    <div class="pl-3 leading-tight md:px-4 font-regular subtitle-1">
                      By <a href="/author/sam-haysom">Sam Haysom</a> </div>
                  </div>
                </div>
                <a aria-hidden="true" class=" block w-1/4" data-ga-click="" data-ga-item="image" data-ga-label="We need to talk about 'Run Rabbit Run's twisted ending" href="/article/run-rabbit-run-netflix-movie-ending-explainer">
                  <div class="hidden border border-gray-100 md:block">
                    <img class="w-full" src="https://helios-i.mashable.com/imagery/articles/007k3FdjQG5mNLuAhHRz9jT/hero-image.fill.size_220x133.v1688566388.jpg" alt="A woman standing in darkness stares at the camera." width="220" height="133" loading="lazy">
                  </div>
                  <div class="border border-gray-100 md:hidden">
                    <img class="w-full" src="https://helios-i.mashable.com/imagery/articles/007k3FdjQG5mNLuAhHRz9jT/hero-image.fill.size_220x220.v1688566388.jpg" alt="A woman standing in darkness stares at the camera." width="220" height="220" loading="lazy">
                  </div>
                </a>
              </div>
            </div>
            <hr class="my-6 mx-auto w-3/4 border border-gray-100 md:hidden md:my-8">
            <div class="w-full" data-ga-position="3">
              <div class="flex flex-row mx-auto mt-4 max-w-4xl font-sans md:flex-nowrap md:justify-around md:mx-0 md:mt-8">
                <div class="flex flex-col flex-wrap mr-4 w-3/4 text-left md:mt-0 xl:relative items-around">
                  <a class=" block text-primary-400 font-semibold leading-6 text-lg header-500 md:text-xl" data-ga-click="" data-ga-item="title" data-ga-label="$text" href="/article/dimension-20-dungeons-and-drag-queens-brennan-lee-mulligan-interview">
                    Brennan Lee Mulligan on the joys of 'Dimension 20: Dungeons and Drag Queens'</a>
                  <div class="hidden text-base md:block md:mt-1 md:leading-tight text-primary-400 font-regular">What's it like to play D&amp;D with Alaska, Bob the Drag Queen, Jujubee, and Monét X Change?</div>
                  <div class="flex flex-row mt-3 font-serif italic md:justify-start">
                    <time class="leading-tight font-regular subtitle-1" datetime="Wed, 05 Jul 2023 23:00:00 +0000">
                      9 hours ago
                    </time>
                    <div class="pl-3 leading-tight md:px-4 font-regular subtitle-1">
                      By <a href="/author/belen-edwards">Belen Edwards</a> </div>
                  </div>
                </div>
                <a aria-hidden="true" class=" block w-1/4" data-ga-click="" data-ga-item="image" data-ga-label="Brennan Lee Mulligan on the joys of 'Dimension 20: Dungeons and Drag Queens'" href="/article/dimension-20-dungeons-and-drag-queens-brennan-lee-mulligan-interview">
                  <div class="hidden border border-gray-100 md:block">
                    <img class="w-full" src="https://helios-i.mashable.com/imagery/articles/01h0EzVEMORHfDsHekppBGg/hero-image.fill.size_220x133.v1688588463.png" alt="A composite image of drag queens Monét X Change, Alaska Thunderfuck, Bob the Drag Queen, and Jujubee playing &quot;Dungeons and Dragons&quot; with Dungeon Master Brennan Lee Mulligan. " width="220" height="133" loading="lazy">
                  </div>
                  <div class="border border-gray-100 md:hidden">
                    <img class="w-full" src="https://helios-i.mashable.com/imagery/articles/01h0EzVEMORHfDsHekppBGg/hero-image.fill.size_220x220.v1688588463.png" alt="A composite image of drag queens Monét X Change, Alaska Thunderfuck, Bob the Drag Queen, and Jujubee playing &quot;Dungeons and Dragons&quot; with Dungeon Master Brennan Lee Mulligan. " width="220" height="220" loading="lazy">
                  </div>
                </a>
              </div>
            </div>
            <hr class="my-6 mx-auto w-3/4 border border-gray-100 md:hidden md:my-8">
            <div class="w-full" data-ga-position="4">
              <div class="flex flex-row mx-auto mt-4 max-w-4xl font-sans md:flex-nowrap md:justify-around md:mx-0 md:mt-8">
                <div class="flex flex-col flex-wrap mr-4 w-3/4 text-left md:mt-0 xl:relative items-around">
                  <a class=" block text-primary-400 font-semibold leading-6 text-lg header-500 md:text-xl" data-ga-click="" data-ga-item="title" data-ga-label="$text" href="/article/threads-meta-twitter-delete-account-instagram">
                    Twitter competitor Threads won't let you delete your account unless you also delete Instagram</a>
                  <div class="hidden text-base md:block md:mt-1 md:leading-tight text-primary-400 font-regular">Remember to read the fine print before you rush to sign up.</div>
                  <div class="flex flex-row mt-3 font-serif italic md:justify-start">
                    <time class="leading-tight font-regular subtitle-1" datetime="Thu, 06 Jul 2023 06:49:54 +0000">
                      1 hour ago
                    </time>
                    <div class="pl-3 leading-tight md:px-4 font-regular subtitle-1">
                      By <a href="/author/amanda-yeo">Amanda Yeo</a> </div>
                  </div>
                </div>
                <a aria-hidden="true" class=" block w-1/4" data-ga-click="" data-ga-item="image" data-ga-label="Twitter competitor Threads won't let you delete your account unless you also delete Instagram" href="/article/threads-meta-twitter-delete-account-instagram">
                  <div class="hidden border border-gray-100 md:block">
                    <img class="w-full" src="https://helios-i.mashable.com/imagery/articles/03iguVQT4HmUsjjfypzUalz/hero-image.fill.size_220x133.v1688621741.jpg" alt="Threads, an Instagram app, on the App Store, in front of the logos of Instagram and Threads." width="220" height="133" loading="lazy">
                  </div>
                  <div class="border border-gray-100 md:hidden">
                    <img class="w-full" src="https://helios-i.mashable.com/imagery/articles/03iguVQT4HmUsjjfypzUalz/hero-image.fill.size_220x220.v1688621741.jpg" alt="Threads, an Instagram app, on the App Store, in front of the logos of Instagram and Threads." width="220" height="220" loading="lazy">
                  </div>
                </a>
              </div>
            </div>
            <hr class="my-6 mx-auto w-3/4 border border-gray-100 md:hidden md:my-8">
            <div class="w-full" data-ga-position="5">
              <div class="flex flex-row mx-auto mt-4 max-w-4xl font-sans md:flex-nowrap md:justify-around md:mx-0 md:mt-8">
                <div class="flex flex-col flex-wrap mr-4 w-3/4 text-left md:mt-0 xl:relative items-around">
                  <a class=" block text-primary-400 font-semibold leading-6 text-lg header-500 md:text-xl" data-ga-click="" data-ga-item="title" data-ga-label="$text" href="/video/sex-education-season-4-trailer-netflix">
                    'Sex Education' marks the final term with hilarious Season 4 teaser trailer</a>
                  <div class="hidden text-base md:block md:mt-1 md:leading-tight text-primary-400 font-regular">"I live and breathe sex all day — everyday."</div>
                  <div class="flex flex-row mt-3 font-serif italic md:justify-start">
                    <time class="leading-tight font-regular subtitle-1" datetime="Wed, 05 Jul 2023 15:42:18 +0000">
                      16 hours ago
                    </time>
                    <div class="pl-3 leading-tight md:px-4 font-regular subtitle-1">
                      By <a href="/author/yasmeen-hamadeh">Yasmeen Hamadeh</a> </div>
                  </div>
                </div>
                <a aria-hidden="true" class=" block w-1/4" data-ga-click="" data-ga-item="image" data-ga-label="'Sex Education' marks the final term with hilarious Season 4 teaser trailer" href="/video/sex-education-season-4-trailer-netflix">
                  <div class="hidden border border-gray-100 md:block">
                    <div class="relative">
                      <svg class="block absolute bottom-0.5 right-2.5 -mb-1 w-7 h-10 text-white fill-current">
                        <use href="/images/icons/spritemap.svg#sprite-icon-video-tag"></use>
                      </svg>
                      <img class="w-full" src="https://helios-i.mashable.com/imagery/videos/00vU9oOtVF6NnjFqgoWHXUP/hero-image.fill.size_220x133.v1688570868.png" alt="A group of high school students wearing colorful clothes, gasp in shock during an assembly. " width="220" height="133" loading="lazy">
                    </div>
                  </div>
                  <div class="border border-gray-100 md:hidden">
                    <div class="relative">
                      <svg class="block absolute bottom-0.5 right-2.5 -mb-1 w-7 h-10 text-white fill-current">
                        <use href="/images/icons/spritemap.svg#sprite-icon-video-tag"></use>
                      </svg>
                      <img class="w-full" src="https://helios-i.mashable.com/imagery/videos/00vU9oOtVF6NnjFqgoWHXUP/hero-image.fill.size_220x220.v1688570868.png" alt="A group of high school students wearing colorful clothes, gasp in shock during an assembly. " width="220" height="220" loading="lazy">
                    </div>
                  </div>
                </a>
              </div>
            </div>
          </div>
        </section>
      </section>
    </section>
    <aside style="width:300px;" class="hidden flex-none 2xl:block" data-ga-module="content_rail">
      <div class="sticky top-0 pt-16 mt-8">
        <div data-pogo="sidebar"></div>
      </div>
    </aside>
  </div>
  <div x-data="window.newsletter()" x-init="init()" class="py-9 px-6 mt-20 text-center text-black rounded border md:mt-32 bg-warmgray-100 border-warmgray-100" data-ga-impression="" data-ga-category="newsletters" data-ga-module="footer_nl_signup" data-ga-label="Top Stories">
    <div x-show="!isSuccess" class="flex flex-col mx-auto max-w-4xl font-sans md:flex-row">
      <div class="font-bold leading-8 text-left md:w-1/2 header-300">The biggest stories of the day delivered to your inbox.</div>
      <div class="flex flex-col md:mt-2 md:w-1/2">
        <form x-ref="subscribeForm" class="flex flex-row mt-8 h-12 border-b-2 md:mt-0 text-primary-400 border-primary-400 focus-within:text-secondary-300" data-initial-lists="top-stories" x-on:submit.prevent="submit()">
          <input aria-describedby="emailSubscribeErrorContainer" x-ref="emailInput" type="email" x-model="email" class="py-2 px-4 w-4/6 text-base font-normal leading-5 placeholder-gray-600 text-gray-600 bg-white md:w-3/4" placeholder="Email Address" aria-label="Email">
          <input x-bind:value="selectedListsValue()" type="hidden" name="lists" value="top-stories">
          <input type="hidden" name="source" value="site-footer">
          <input type="hidden" name="courierList" value="Mashable On-Site - Top Stories Daily">
          <button :disabled="isLoading" type="submit" class="py-2 px-1 w-2/6 font-bold leading-5 text-center bg-white md:w-1/4 subtitle-1">
            <span x-show="isLoading" class="text-primary-400" style="display: none;">Loading...</span>
            <span x-show="!isLoading" :class="{'text-red': isError}" class="text-sm">Subscribe</span>
          </button>
        </form>
        <div x-show="isError" class="flex flex-row px-4 mt-4 text-red" style="display: none;">
          <svg class="inline-block w-4 h-4 leading-4 fill-current">
            <use href="/images/icons/spritemap.svg#sprite-exclamation-square"></use>
          </svg>
          <div id="emailSubscribeErrorContainer" x-text="error" class="ml-1 font-bold leading-4 text-left subtitle-2"></div>
        </div>
      </div>
    </div>
    <div x-show="!isSuccess" class="mx-auto mt-4 max-w-4xl font-sans leading-normal text-left subtitle-1">
      This newsletter may contain advertising, deals, or affiliate links. Subscribing to a newsletter indicates your consent to our <a href="https://www.ziffdavis.com/terms-of-use" target="_blank" rel="noopener">Terms of Use<span class="sr-only">(opens in a new tab)</span></a> and <a href="https://www.ziffdavis.com/ztg-privacy-policy" target="_blank" rel="noopener">Privacy Policy<span class="sr-only">(opens in a new tab)</span></a>. You may unsubscribe from the newsletters at any time.
    </div>
    <div x-show="isSuccess" class="py-1 text-center" style="display: none;">
      <svg class="inline-block w-24 h-20 leading-4 fill-current">
        <use href="/images/icons/spritemap.svg#sprite-mailbox-regular"></use>
      </svg>
      <div class="header-300">Thanks for signing up. See you at your inbox!</div>
    </div>
  </div>
  <footer class="py-12 mx-auto w-full font-sans text-center text-white bg-primary-400" data-ga-module="global_footer" data-ga-action="footer_link">
    <div class="flex flex-col px-8 md:mx-auto lg:px-4 max-w-8xl">
      <div class="inline-block relative mb-4 ml-5 text-left text-white">
        <svg class="inline-block w-6 h-6 fill-current">
          <use href="/images/icons/spritemap.svg#sprite-mashable-m"></use>
        </svg>
        <svg class="absolute top-0 left-0 -mt-1 -ml-5 w-12 h-12 fill-current">
          <use href="/images/icons/spritemap.svg#sprite-logomark"></use>
        </svg>
      </div>
      <div class="my-8 text-left lg:pr-24 lg:mr-7">
        <ul class="flex flex-col mt-8 space-y-8 text-2xl font-bold text-white lg:flex-row lg:mt-0 lg:space-y-0 lg:space-x-12">
          <li><a href="https://mashable.com/tech" class="text-white hover:text-accent" data-ga-click="" data-ga-label="$text">TECH</a></li>
          <li><a href="https://mashable.com/science" class="text-white hover:text-accent" data-ga-click="" data-ga-label="$text">SCIENCE</a></li>
          <li><a href="https://mashable.com/life" class="text-white hover:text-accent" data-ga-click="" data-ga-label="$text">LIFE</a></li>
          <li><a href="https://mashable.com/category/social-good" class="text-white hover:text-accent" data-ga-click="" data-ga-label="$text">SOCIAL GOOD</a></li>
          <li><a href="https://mashable.com/entertainment" class="text-white hover:text-accent" data-ga-click="" data-ga-label="$text">ENTERTAINMENT</a></li>
          <li><a href="https://mashable.com/roundups" class="text-white hover:text-accent" data-ga-click="" data-ga-label="$text">BEST PRODUCTS</a></li>
          <li><a href="https://mashable.com/deals" class="text-white hover:text-accent" data-ga-click="" data-ga-label="$text">DEALS</a></li>
        </ul>
        <ul class="flex flex-col mt-12 space-y-4 text-sm font-bold text-white md:flex-row md:space-y-0 md:space-x-6">
          <li><a href="https://mashable.com/about/mashable-staff-masthead" class="text-white hover:text-accent" data-ga-click="" data-ga-label="$text">About Mashable</a></li>
          <li><a href="https://mashable.com/about/contact-us" class="text-white hover:text-accent" data-ga-click="" data-ga-label="$text">Contact Us</a></li>
          <li><a href="https://www.j2global.com/careers/jobs/?brand=Mashable" rel="noopener" class="text-white hover:text-accent" data-ga-click="" data-ga-label="$text">We're Hiring</a></li>
          <li><a href="https://mashable.com/newsletters" rel="noopener" class="text-white hover:text-accent" data-ga-click="" data-ga-label="$text">Newsletters</a></li>
          <li><a href="https://mashable.com/sitemap" class="text-white hover:text-accent" data-ga-click="" data-ga-label="$text">Sitemap</a></li>
        </ul>
        <ul class="flex flex-row flex-wrap my-8 text-white md:mt-5 md:mb-8 md:space-x-8">
          <li><a href="https://www.facebook.com/mashable/" aria-label="Mashable Facebook Page" rel="noopener" data-ga-click="" data-ga-label="facebook"><svg class="inline-block mt-6 mr-8 w-5 h-5 text-white fill-current md:mt-0 md:mr-0 hover:text-accent">
                <use href="/images/icons/spritemap.svg#sprite-facebook-f-brands"></use>
              </svg></a></li>
          <li><a href="https://twitter.com/mashable" aria-label="Mashable Twitter Page" rel="noopener" data-ga-click="" data-ga-label="twitter"><svg class="inline-block mt-6 mr-8 w-5 h-5 text-white fill-current md:mt-0 md:mr-0 hover:text-accent">
                <use href="/images/icons/spritemap.svg#sprite-twitter-brands"></use>
              </svg></a></li>
          <li><a href="http://instagram.com/Mashable" aria-label="Mashable Instagram Page" rel="noopener" data-ga-click="" data-ga-label="instagram"><svg class="inline-block mt-6 mr-8 w-5 h-5 text-white fill-current md:mt-0 md:mr-0 hover:text-accent">
                <use href="/images/icons/spritemap.svg#sprite-instagram-brands"></use>
              </svg></a></li>
          <li><a href="https://www.youtube.com/user/mashable" aria-label="Mashable Youtube Page" rel="noopener" data-ga-click="" data-ga-label="youtube"><svg class="inline-block mt-6 mr-8 w-5 h-5 text-white fill-current md:mt-0 md:mr-0 hover:text-accent">
                <use href="/images/icons/spritemap.svg#sprite-youtube"></use>
              </svg></a></li>
          <li><a href="https://www.pinterest.com/mashable/" aria-label="Mashable Pinterest Page" rel="noopener" data-ga-click="" data-ga-label="pinterest"><svg class="inline-block mt-6 mr-8 w-5 h-5 text-white fill-current md:mt-0 md:mr-0 hover:text-accent">
                <use href="/images/icons/spritemap.svg#sprite-pinterest"></use>
              </svg></a></li>
          <li><a href=" https://www.tiktok.com/@mashable" aria-label="Mashable Tiktok Page" rel="noopener" data-ga-click="" data-ga-label="tiktok"><svg class="inline-block mt-6 mr-8 w-5 h-5 text-white fill-current md:mt-0 md:mr-0 hover:text-accent">
                <use href="/images/icons/spritemap.svg#sprite-tiktok"></use>
              </svg></a></li>
          <li><a href="https://flipboard.com/@Mashable" aria-label="Mashable Flipboard Page" rel="noopener" data-ga-click="" data-ga-label="flipboard"><svg class="inline-block mt-6 mr-8 w-5 h-5 text-white fill-current md:mt-0 md:mr-0 hover:text-accent">
                <use href="/images/icons/spritemap.svg#sprite-flipboard-brands"></use>
              </svg></a></li>
          <li><a href="https://www.linkedin.com/company/mashable" aria-label="Mashable Linkedin Page" rel="noopener" data-ga-click="" data-ga-label="linkedin"><svg class="inline-block mt-6 mr-8 w-5 h-5 text-white fill-current md:mt-0 md:mr-0 hover:text-accent">
                <use href="/images/icons/spritemap.svg#sprite-linkedin"></use>
              </svg></a></li>
          <li><a href="https://mashable.com/feeds/rss/all" aria-label="Mashable Feeds Page" rel="noopener" data-ga-click="" data-ga-label="rss"><svg class="inline-block mt-6 mr-8 w-5 h-5 text-white fill-current md:mt-0 md:mr-0 hover:text-accent">
                <use href="/images/icons/spritemap.svg#sprite-rss-solid"></use>
              </svg></a></li>
        </ul>
        <div class="flex flex-col mb-8 space-y-4 md:flex-row md:items-center md:space-y-0 md:space-x-4">
          <img src="/images/group-black-logo-purple.png" width="150px" alt="Group Black" loading="lazy">
          <div class="max-w-3xl leading-normal subtitle-1">Mashable supports <a href="https://www.ziffdavis.com/general/group-black-and-ziff-davis-partner-to-amplify-and-scale-black-voices-in-media" rel="noopener" class="font-bold text-white" data-ga-click="" data-ga-label="$text">Group Black</a> and its mission to increase greater diversity in media voices and media ownership. Group Black's collective includes <a href="https://www.essence.com/" rel="noopener" class="font-bold text-white" data-ga-click="" data-ga-label="$text">Essence</a>, <a href="https://theshaderoom.com/" rel="noopener" class="font-bold text-white" data-ga-click="" data-ga-label="$text">TheShadeRoom</a> and <a href="https://afropunk.com/" rel="noopener" class="font-bold text-white" data-ga-click="" data-ga-label="$text">Afro-Punk</a>.</div>
        </div>
        <div class="font-sans leading-5 subtitle-1">©2005–2023 Mashable, Inc., a Ziff Davis company. All Rights Reserved.</div>
        <div class="font-sans leading-5 subtitle-1">Mashable is a registered trademark of Ziff Davis and may not be used by third parties without express written permission.</div>
        <ul class="flex flex-col flex-wrap justify-start items-baseline mt-8 space-y-4 font-sans underline md:flex-row md:space-y-0 md:space-x-6 lg:flex-row subtitle-2">
          <li><a href="https://www.ziffdavis.com/about" rel="noopener" class="text-white hover:text-accent" data-ga-click="" data-ga-label="$text">About Ziff Davis</a></li>
          <li><a href="https://www.ziffdavis.com/ztg-privacy-policy" rel="noopener" class="text-white hover:text-accent" data-ga-click="" data-ga-label="$text">Privacy Policy</a></li>
          <li><a href="https://www.ziffdavis.com/terms-of-use" rel="noopener" class="text-white hover:text-accent" data-ga-click="" data-ga-label="$text">Terms of Use</a></li>
          <li><a href="https://ziffmedia.com/" rel="noopener" class="text-white hover:text-accent" data-ga-click="" data-ga-label="$text">Advertise</a></li>
          <li><a href="https://www.ziffdavis.com/accessibility" rel="noopener" class="text-white hover:text-accent" data-ga-click="" data-ga-label="$text">Accessibility</a></li>
          <li><a href="https://mashable.com/ccpa" class="text-white hover:text-accent" data-ga-click="" data-ga-label="$text">Do Not Sell My Personal Information</a></li>
          <li><a href="https://www.bbb.org/us/ny/new-york/profile/digital-media/ziff-davis-llc-0121-531/#sealclick" rel="noopener" aria-label="BBB Accredited Business Logo" data-ga-click="" data-ga-label="BBB Accredited Business Logo"><svg width="96" height="50" viewBox="0 0 96 50" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                <rect width="96" height="50" fill="url(#pattern1)"></rect>
                <defs>
                  <pattern id="pattern1" patternContentUnits="objectBoundingBox" width="1" height="1">
                    <use xlink:href="#image1" transform="translate(0 -0.00223217) scale(0.0104167 0.0200893)"></use>
                  </pattern>
                  <image id="image1" width="96" height="50"
                    xlink:href="data:image/png;base64,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">
                  </image>
                </defs>
              </svg></a></li>
          <li>
            <button class="block" onclick="window.zdconsent.showConsentTool();" data-ga-click="" data-ga-label="zd_consent">
              <img src="https://c.evidon.com/pub/icong1.png" class="inline-block mr-2" alt="ZiffDavis AdChoices" width="14px" height="18px" loading="lazy">AdChoices
            </button>
          </li>
        </ul>
      </div>
    </div>
  </footer>
  <script async="" src="/js/app.js?id=92fa1ace6c87aaa3dcd9a10a0bc22433"></script>
  <script>
    window.videoEmbeds = window.videoEmbeds || []
    window.videoEmbeds.push({
      elemId: 'mashable-video-container-3Y0Njq530G',
      data: { "slug": "3Y0Njq530G", "url": "https:\/\/videos.mashable.com\/cms\/2021\/12\/ecee6866-228c-e0b3\/mp4\/1080.mp4", "transcoded_urls": ["https:\/\/videos.mashable.com\/cms\/2021\/12\/ecee6866-228c-e0b3\/hls.m3u8", "https:\/\/videos.mashable.com\/cms\/2021\/12\/ecee6866-228c-e0b3\/dash.mpd", "https:\/\/videos.mashable.com\/cms\/2021\/12\/ecee6866-228c-e0b3\/mp4\/1080.mp4", "https:\/\/videos.mashable.com\/cms\/2021\/12\/ecee6866-228c-e0b3\/mp4\/720.mp4", "https:\/\/videos.mashable.com\/cms\/2021\/12\/ecee6866-228c-e0b3\/mp4\/480.mp4"], "title": "Timoth\u00e9e Chalamet and Zendaya on bringing a sci-fi epic back to the big screen", "thumbnail_url": "https:\/\/blueprint-api-production.s3.amazonaws.com\/uploads\/video_uploaders\/distribution_thumb\/image\/96698\/6e6bb59c-77ba-448d-b2c7-dd0cc1f7566e.png", "disable_ads": 0 }
    });
  </script>
  <script>
    window.addEventListener('load', () => {
      window.trackAndReportBibblioInView(document.getElementById('firstRecommendation'),
        'https://api.bibblio.org/v1/activities/0029dc8a-5719-47cf-b566-fe1eac0686bd',
        '{"type":"Viewed","object":null,"context":[["sourceHref","https:\/\/api.bibblio.org\/v1\/content-items\/96314440-7622-40a2-9eb0-08ff175bfb30"],["sourceContentItemId","96314440-7622-40a2-9eb0-08ff175bfb30"],["recommendations.contentItemId","680bc720-4171-483a-8474-c928e455467c"],["recommendations.contentItemId","90377454-3373-46e4-9dce-4649dcbefe4f"],["recommendations.contentItemId","c5110dd0-2db2-48f6-a46b-73bec5702241"],["recommendations.contentItemId","29ec9b4e-9b2d-4593-814c-9471a87f2a9d"],["recommendations.contentItemId","2c6b1cf5-d6c2-42d7-a3c9-7c30bbde3921"],["recommendations.contentItemId","46c5a1fa-7d5d-49c8-89b6-9b84c2bfaf96"],["recommendations.contentItemId","789f0fe0-c84b-4888-bc3b-b6f6bae28f8a"],["recommendations.contentItemId","4c8b8a11-1575-42e3-a393-5956a6086882"],["recommendations.contentItemId","718cc952-9070-4b88-a769-8008ea0ced9a"],["recommendations.contentItemId","01921b5e-b371-4f26-ac7d-c005ee425aad"],["recommendations.catalogueId","330050e0-220f-4749-b7e2-3d63c15afafa"],["recommendations.catalogueId","93added7-f049-4406-8429-aba05c9816ac"],["recommendations.catalogueId","cce2a427-339d-49a4-afbc-76a37727db59"],["recommendations.catalogueId","89b8a249-0d12-4a72-b6f9-59b42c647943"]]}');
    });
  </script>
  <script defer="" src="https://static.cloudflareinsights.com/beacon.min.js/v52afc6f149f6479b8c77fa569edb01181681764108816" integrity="sha512-jGCTpDpBAYDGNYR5ztKt4BQPGef1P0giN6ZGVUi835kFF88FOmmn8jBQWNgrNd8g/Yu421NdgWhwQoaOPFflDw==" data-cf-beacon="{&quot;rayId&quot;:&quot;7e2655afdbb82147&quot;,&quot;token&quot;:&quot;9170c8255f824528826550ee74ecc073&quot;,&quot;version&quot;:&quot;2023.4.0&quot;,&quot;si&quot;:100}" crossorigin="anonymous"></script>


</body>

</html>