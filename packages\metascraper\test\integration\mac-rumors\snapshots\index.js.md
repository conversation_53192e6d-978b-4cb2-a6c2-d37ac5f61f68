# Snapshot report for `test/integration/mac-rumors/index.js`

The actual snapshot is saved in `index.js.snap`.

Generated by [AVA](https://avajs.dev).

## mac-rumors

> Snapshot 1

    {
      audio: null,
      author: '<PERSON><PERSON>',
      date: '2016-05-24T16:54:45.000Z',
      description: 'Apple is actively developing a product that would compete with the Amazon Echo and Google Home, reports The Information. Citing a source with direct…',
      image: 'https://images.macrumors.com/t/kOeU4X_9erya_4EHeAex8MHWdgI=/1600x/article-new/2016/05/amazonecho2-250x362.jpg',
      lang: 'en',
      logo: 'https://images.macrumors.com/images-new/favicon-32x32.png',
      publisher: 'MacRumors',
      title: 'Apple Working on Amazon Echo Competitor, Opening Siri Up to Developers',
      url: 'https://www.macrumors.com/2016/05/24/apple-siri-sdk-amazon-echo-competitor/',
      video: null,
    }
