{"name": "metascraper-clearbit", "description": "Metascraper integration with Clearbit Logo API", "homepage": "https://github.com/microlinkhq/metascraper/packages/metascraper-clearbit", "version": "5.49.1", "types": "src/index.d.ts", "main": "src/index.js", "author": {"email": "<EMAIL>", "name": "microlink.io", "url": "https://microlink.io"}, "repository": {"directory": "packages/metascraper-clearbit", "type": "git", "url": "git+https://github.com/microlinkhq/metascraper.git"}, "bugs": {"url": "https://github.com/microlinkhq/metascraper/issues"}, "keywords": ["autocomplete", "clearbit", "logo", "metascraper"], "dependencies": {"@keyvhq/memoize": "~2.1.9", "@metascraper/helpers": "workspace:*", "async-memoize-one": "~1.1.8", "got": "~11.8.6", "lodash": "~4.17.21"}, "devDependencies": {"ava": "5"}, "engines": {"node": ">= 16"}, "files": ["src"], "scripts": {"test": "NODE_PATH=.. TZ=UTC ava --timeout 15s"}, "license": "MIT"}