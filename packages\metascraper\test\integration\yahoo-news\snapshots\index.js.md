# Snapshot report for `test/integration/yahoo-news/index.js`

The actual snapshot is saved in `index.js.snap`.

Generated by [AVA](https://avajs.dev).

## yahoo-news

> Snapshot 1

    {
      audio: null,
      author: 'Mic',
      date: '2016-05-25T13:38:00.000Z',
      description: '<PERSON> coldly rejects a fan’s gift in this viral video.',
      image: 'https://s.yimg.com/ny/api/res/1.2/1zChVKewXwbEWKPqZbfHgg--/YXBwaWQ9aGlnaGxhbmRlcjt3PTYxNjtoPTQ2MDtjZj13ZWJw/https://media.zenfs.com/en-US/homerun/mic_26/8de763298d0eafd3534895283457892c',
      lang: 'en',
      logo: 'https://s.yimg.com/rz/l/favicon.ico',
      publisher: 'Yahoo News',
      title: '<PERSON> Coldly Throws <PERSON>’s Gift Out Car Window, Belieber Hearts Break',
      url: 'https://www.yahoo.com/news/justin-bieber-coldly-throws-fans-133800208.html',
      video: null,
    }
