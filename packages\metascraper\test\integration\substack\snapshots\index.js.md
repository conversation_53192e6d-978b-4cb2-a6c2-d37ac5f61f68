# Snapshot report for `test/integration/substack/index.js`

The actual snapshot is saved in `index.js.snap`.

Generated by [AVA](https://avajs.dev).

## substack

> Snapshot 1

    {
      audio: null,
      author: '<PERSON>',
      description: 'The world is a very malleable place. When I read biographies, early lives leap out the most. <PERSON> was a studio apprentice to <PERSON><PERSON><PERSON><PERSON><PERSON> at 14. <PERSON> took on a number of jobs, chiefly delivering papers, from 11 years old. <PERSON> published his first book (a collection of poems) at 16, while still in school. Andrew <PERSON>',
      image: 'https://substackcdn.com/image/fetch/w_1200,h_600,c_fill,f_jpg,q_auto:good,fl_progressive:steep,g_auto/https%3A%2F%2Fbucketeer-e05bbc84-baa3-437e-9518-adb32be77984.s3.amazonaws.com%2Fpublic%2Fimages%2Fef3bd0df-b9fa-4358-afee-116c23f4c55f_2560x1902.jpeg',
      lang: 'en',
      publisher: 'The Map is Mostly Water',
      title: 'The Most Precious Resource is Agency',
      url: 'https://map.simonsarris.com/p/the-most-precious-resource-is-agency',
      video: null,
    }
