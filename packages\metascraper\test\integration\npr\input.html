<!DOCTYPE html>
<html class="js no-touch csstransforms3d video audio audio-modules-mounted" lang="en" style="" data-whatintent="mouse"><div id="b4e503bf-7c41-4dc3-bf5b-0565dd446bda" style="display: none;">{"method":"get","path":"self.navigator.mimeTypes","level":"info","category":"navigator","jsContextId":"719f9323-1968-4484-98af-c3d7c7053aa0","url":"https://www.npr.org/2020/12/23/*********/fork-the-government","stack":[{"functionName":"e.jPlayer._getFlashPluginVersion","fileName":"https://bundles.npr.org/dist/bundles/0.288e792ce5a8625be98a.chunk.js","lineNumber":"60","columnNumber":"11207"},{"functionName":"e.jPlayer._checkForFlash","fileName":"https://bundles.npr.org/dist/bundles/0.288e792ce5a8625be98a.chunk.js","lineNumber":"60","columnNumber":"11417"},{"functionName":"e.jPlayer._init","fileName":"https://bundles.npr.org/dist/bundles/0.288e792ce5a8625be98a.chunk.js","lineNumber":"59","columnNumber":"1339"},{"functionName":"new e.jPlayer","fileName":"https://bundles.npr.org/dist/bundles/0.288e792ce5a8625be98a.chunk.js","lineNumber":"58","columnNumber":"19520"},{"functionName":"HTMLDivElement.&lt;anonymous&gt;","fileName":"https://bundles.npr.org/dist/bundles/0.288e792ce5a8625be98a.chunk.js","lineNumber":"58","columnNumber":"19308"},{"functionName":"Function.each","fileName":"https://bundles.npr.org/dist/bundles/0.288e792ce5a8625be98a.chunk.js","lineNumber":"113","columnNumber":"14722"}]}</div><head><script type="text/javascript" async="" src="https://www.gstatic.com/recaptcha/releases/qc5B-qjP0QEimFYUxcpWJy5B/recaptcha__en.js" crossorigin="anonymous" integrity="sha384-EauiKN7dy30bq/wDo7lcvebLQr7wwQPtEV6A1G43RAWnhPwxWZFCCTOT/hE+ffe3"></script><script async="" src="https://sb.scorecardresearch.com/beacon.js"></script><script async="" type="text/javascript" src="https://rumcdn.geoedge.be/880a45f2-0015-49d2-b38f-2d26be44ae09/grumi-ip.js"></script><script async="" type="text/javascript" src="https://www.googletagservices.com/tag/js/gpt.js"></script><script type="text/javascript" async="" defer="" src="https://www.google.com/recaptcha/api.js"></script><script async="" src="https://connect.facebook.net/en_US/fbevents.js"></script><script type="text/javascript" async="" src="https://www.google-analytics.com/analytics.js"></script><script>
   try {var _sf_startpt=(new Date()).getTime();} catch(e){}
</script>
<meta itemscope="" itemtype="https://schema.org/SpeakableSpecification" itemref="speakable"><title>Audrey Tang brings civic tech to Taiwan's coronavirus pandemic response : Planet Money : NPR</title><meta id="speakable" itemprop="cssSelector" content="[data-is-speakable]"><meta id="google-site-verification" name="google-site-verification" content="1VqzbDm4ukeFVHOoq5LjIKA2fVKZD-EYv8cXZhKXSfU"><meta charset="utf-8"><meta name="robots" content="noarchive,index,follow"><meta name="Rating" content="General"><meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"><meta name="npr-pjax"><meta name="date" content="2020-12-23"><link rel="canonical" href="https://www.npr.org/2020/12/23/*********/fork-the-government"><meta name="description" content="A global pandemic might not be the best time to try something new with technology. But Taiwan decided to do it anyway. | Subscribe to our weekly newsletter here."><link rel="alternate" type="application/rss+xml" title="Asia" href="https://feeds.npr.org/1125/rss.xml"><link rel="alternate" type="application/json" title="Asia" href="https://feeds.npr.org/feeds/1125/feed.json"><link rel="apple-touch-icon" sizes="180x180" href="https://static-assets.npr.org/static/images/favicon/favicon-180x180.png"><link rel="icon" sizes="96x96" href="https://static-assets.npr.org/static/images/favicon/favicon-96x96.png"><link rel="icon" sizes="32x32" href="https://static-assets.npr.org/static/images/favicon/favicon-32x32.png"><link rel="icon" sizes="16x16" href="https://static-assets.npr.org/static/images/favicon/favicon-16x16.png"><meta property="og:title" content="Fork The Government : Planet Money"><meta property="og:url" content="https://www.npr.org/2020/12/23/*********/fork-the-government"><meta property="og:type" content="article"><meta property="og:description" content="A global pandemic might not be the best time to try something new with technology. But Taiwan decided to do it anyway. | Subscribe to our weekly newsletter here."><meta property="og:image" content="https://media.npr.org/assets/img/2020/12/23/gettyimages-1199493836_wide-b0f8c2e44d3617f2f5ff7f4dceff064ecad00439.jpg?s=1400"><meta property="og:site_name" content="NPR.org"><meta property="fb:app_id" content="***************"><meta property="fb:pages" content="10643211755"><meta property="article:content_tier" content="free"><meta property="article:opinion" content="false"><meta name="twitter:card" content="summary_large_image"><meta name="twitter:title" content="Fork The Government : Planet Money"><meta name="twitter:site" content="@NPR"><meta name="twitter:domain" content="npr.org"><meta name="twitter:image:src" content="https://media.npr.org/assets/img/2020/12/23/gettyimages-1199493836_wide-b0f8c2e44d3617f2f5ff7f4dceff064ecad00439.jpg?s=1400"><script id="npr-vars">
   window.NPR = window.NPR || {};
NPR.ServerConstants = {"cbHost":"npr.org","webHost":"https:\/\/www.npr.org","legacyWebHost":"https:\/\/legacy.npr.org","stripeServiceHost":"https:\/\/stripe.npr.org","recaptchaV3SiteKey":"6LcdN9IZAAAAAOHT985BJjbzjkBjKSxdxK3GeKSt","embedHost":"https:\/\/www.npr.org","webHostSecure":"https:\/\/secure.npr.org","identityHost":"https:\/\/identity.api.npr.org","apiHost":"https:\/\/api.npr.org","serverMediaCache":"https:\/\/media.npr.org","googleAnalyticsAccount":"**********-4","nielsenSFCode":"dcr","nielsenAPN":"NPR-dcr","shouldShowHPLocalContent":true,"readingServiceHostname":"https:\/\/reading.api.npr.org","cookieDomain":".npr.org"};
NPR.serverVars = {"storyId":"*********","facebookAppId":"***************","webpackPublicPath":"https:\/\/bundles.npr.org\/dist\/bundles\/","persistenceVersion":"ea6d8b47d10fc2098d879df800ccc947","isBuildOut":true,"topicIds":["P1125","1004","1006","1017"],"primaryTopic":"Asia","topics":["Asia","World","Business","Economy"],"blog":"Planet Money","theme":"510289","aggIds":["1004","1006","1017","1125","510289","********"],"byline":["Darian Woods","Sarah Gonzalez"],"pubDate":"**********","pageTypeId":"15","title":"Fork The Government","publisherOrgId":"1"};
</script>
<script type="text/javascript" src="https://cdn.optimizely.com/js/***********.js">
   </script>
<script type="text/javascript" src="https://static-assets.npr.org/static/javascript/lib/modernizr/modernizr.custom.js">
   </script>
<script type="text/javascript" async="" src="https://js.stripe.com/v3/">
   </script>
<script>
   !function(e){function c(a){if(d[a])return d[a].exports;var f=d[a]={exports:{},id:a,loaded:!1};return e[a].call(f.exports,f,f.exports,c),f.loaded=!0,f.exports}var a=window.webpackJsonp;window.webpackJsonp=function(b,t){for(var n,r,o=0,i=[];o<b.length;o++)r=b[o],f[r]&&i.push.apply(i,f[r]),f[r]=0;for(n in t)if(Object.prototype.hasOwnProperty.call(t,n)){var p=t[n];switch(typeof p){case"object":e[n]=function(c){var a=c.slice(1),d=c[0];return function(c,f,b){e[d].apply(this,[c,f,b].concat(a))}}(p);break;case"function":e[n]=p;break;default:e[n]=e[p]}}for(a&&a(b,t);i.length;)i.shift().call(null,c);if(t[0])return d[0]=0,c(0)};var d={},f={23:0};c.e=function(e,a){if(0===f[e])return a.call(null,c);if(void 0!==f[e])f[e].push(a);else{f[e]=[a];var d=document.getElementsByTagName("head")[0],b=document.createElement("script");b.type="text/javascript",b.charset="utf-8",b.async=!0,b.src=c.p+""+e+"."+{0:"288e792ce5a8625be98a",1:"967c5e6c2dce7cecc18a",2:"86d5ffc6c34564b132b4",3:"581f169e66f4ec2ee67d",4:"641fbaffd0fd1dfe82fb",5:"2eca3544c343394cefce",6:"7c11d4118d85d384612d",7:"5d3cda17a74b946359bd",8:"b8d25221663fe723f8dd",9:"b8753cabfef746b9eeba",10:"d11c526b5212874de245",11:"f1d6a8098e3ed3c0337e",12:"8731efcc1e6f157d24fd",13:"5f79880c1a4681a94e9f",14:"cc956923eba275031bf0",15:"384d4d801afa26274c01",16:"1300a9edc192a60fc06b",17:"491931a2768eb03abd40",18:"3dca39e5e134ca707fa8",19:"46a6a82c5be4b601f4f3",20:"70bea3cdfd83fe5efc6f",21:"f3f3e4b337d37291c90f",22:"2f80b8c3dceb5649c336",24:"9756b574b39f4a6e70b7",25:"da35a14c486a9e31520d",26:"80b979b1e4dfcd2aeca3",27:"5855b08c8b66aab913c1",28:"c805b9d2ef139ea1747c",29:"15ef5b748263b9602138",30:"201baa563dba2c3a77c3",31:"4c473fa63b3b69743342",32:"79edc8fe3b027800b190",33:"ca4e892f74f8174ef23c",34:"90f111071342785bc3cd",35:"24e18266fef68b1c69b6",36:"b0a6120b40a139e0f0dd",37:"6c990c831e0a0f14e368",38:"1cb2c63ff6b9e88e3446",39:"1e1d63546be3f9fce19d",40:"024669e4cc9f710e3c3c",41:"9ee5a8e4088def3b7cd6",42:"d11c5ce8b1380cd3de6c",43:"72bbd338ec15b1e090f0",44:"f8a1a2c04f79e75a4cb1",45:"117e331ecab70e6de791",46:"22b8fb8249a3aee7a627",47:"610341e767e5b7d18a47",48:"5deda2c4e1d43e4bce12",49:"8b2fb21061261aa7178f",50:"d2966dd7b67c27e952b1",51:"031150ee2ca633729486",52:"93b50ab36f382e42a66f",53:"1cc471967eaea763ccd9",54:"9eab024bc061290b4d9f",55:"b02ecbd95f2cfd51d871",56:"81ad280bdcd5be81a35a",57:"1d0a35133d0556883bd2",58:"8e60f7e4b87688df29a2",59:"2465df91d38d1bd49b47",60:"c3cdae4194942b3f6d5e",61:"0f47f8b0acdc6a0efed7",62:"2e156776d8351b446107",63:"ceb4f051433cd3785cae",64:"97cb70c491f16962e87a",65:"20643a58ed64d870dfe7",66:"01ddae414b0a137e6122",67:"193dd11642648d8b96d8",68:"558831f6ad0ca93e1968",69:"2fd87b57b9b8b63a483a",70:"2f869c9ea792943479ae",71:"5861c75e098d2cd45656",72:"1212caa954400be6f245",73:"e31016259b7b890a7d24",74:"3d612e09c819aed77041",75:"29647f204938d35e0c1c",76:"18fb7e6f3771dfe1d64a",77:"c3da9ffed3eae997bb8f",78:"d11b1f34fc9d7355c186",79:"560553bca643e8059f74",80:"8e98d20af99cbb7b9161"}[e]+".chunk.js",d.appendChild(b)}},c.m=e,c.c=d,c.p="/dist/bundles/"}(function(e){for(var c in e)if(Object.prototype.hasOwnProperty.call(e,c))switch(typeof e[c]){case"function":break;case"object":e[c]=function(c){var a=c.slice(1),d=e[c[0]];return function(e,c,f){d.apply(this,[e,c,f].concat(a))}}(e[c]);break;default:e[c]=e[e[c]]}return e}([]));
</script>
<script type="text/javascript" src="https://bundles.npr.org/dist/bundles/globalNavigationDropdowns-80b979b1e4dfcd2aeca3.js">
   </script>
<link rel="stylesheet" data-persist="true" media="screen, print" href="https://bundles.npr.org/dist/bundles/persistent-css-6cfbbb05cb4c06793f5a.css"><link rel="stylesheet" media="screen, print" href="https://bundles.npr.org/dist/bundles/newsStory-css-6cfbbb05cb4c06793f5a.css"><script type="application/ld+json">
   {"@type":"NewsArticle","publisher":{"@type":"Organization","name":"NPR","logo":{"@type":"ImageObject","url":"https:\/\/media.npr.org\/chrome\/npr-logo.jpg"}},"headline":"Fork The Government","mainEntityOfPage":{"@type":"WebPage","@id":"https:\/\/www.npr.org\/2020\/12\/23\/*********\/fork-the-government"},"datePublished":"2020-12-23T22:36:27-05:00","dateModified":"2020-12-23T22:36:00-05:00","author":{"@type":"Person","name":["Darian Woods","Sarah Gonzalez"]},"description":"A global pandemic might not be the best time to try something new with technology. But Taiwan decided to do it anyway. | Subscribe to our weekly newsletter <a href=\"https:\/\/www.npr.org\/newsletter\/money?utm_source=rss_feed_copy&amp;utm_medium=podcast&amp;utm_term=planet_money\">here<\/a>.","image":{"@type":"ImageObject","url":"https:\/\/media.npr.org\/assets\/img\/2020\/12\/23\/gettyimages-1199493836_wide-b0f8c2e44d3617f2f5ff7f4dceff064ecad00439.jpg"},"@context":"http:\/\/schema.org"}
</script>
<script type="text/javascript">
   document.domain="npr.org";
</script>
<script type="text/javascript" charset="utf-8" async="" src="https://bundles.npr.org/dist/bundles/0.288e792ce5a8625be98a.chunk.js"></script><script type="text/javascript" charset="utf-8" async="" src="https://bundles.npr.org/dist/bundles/3.581f169e66f4ec2ee67d.chunk.js"></script><style id="fit-vids-style">.fluid-width-video-wrapper{width:100%;position:relative;padding:0;}.fluid-width-video-wrapper iframe,.fluid-width-video-wrapper object,.fluid-width-video-wrapper embed {position:absolute;top:0;left:0;width:100%;height:100%;}</style></head><body id="news" class="news tmplShowEpisode  story-layout is-buildout no-transcript has-no-branding id*********" data-pjax-modules="util/env images/lazy-load chartbeat comscore metrics nielsen nielsenDCR copyright auto-refresh navigation/global-navigation fitvids dfp globalfooter persistent-audio player/more-options player/audio-module-clickable what-input metrics/doubleclickFloodlight metrics/facebookTracking callout/on-page-callouts global-alerts ecommerce recommendations/end-of-story-recommendations-callout jw social/share slideshow images/enlargement swfbuilder contentfooter story/sticky-header story/date-tooltip social/sharepop podcast-cards search/faceting-param local/newsletter/callout social/recaptcha" data-nav-overlay-menu-system="false" data-new-gr-c-s-check-loaded="14.990.0" data-gr-ext-installed="" cz-shortcut-listen="true"><div class="skip-links">
      <span>Accessibility links</span>   <ul>
            <li><a href="#mainContent" class="skiplink">Skip to main content</a></li>
      <li><a href="https://help.npr.org/contact/s/article?name=what-are-the-keyboard-shortcuts-for-using-the-npr-org-audio-player" class="skiplink">Keyboard shortcuts for audio player</a></li>
   </ul>
</div>
<div id="player-react-mount"><div data-reactroot="" class="player-react-mount-inner"><div id="audio-player" style="width: 0px; height: 0px;"><img id="jp_poster_0" style="width: 0px; height: 0px; display: none;"><audio id="jp_audio_0" preload="metadata"></audio></div><div class="player-wrap-outer "><div class="player-wrap-inner"><span><section id="npr-player" class="npr-player has-expanded-pane" aria-label="NPR audio player"><span><div class="player-initial"><div><article class="player-item item-initial item-news"><button class="play-initial"><b><b class="tag-one program-stream">NPR 24 Hour Program Stream</b><b class="tag-two">On Air Now</b></b></button></article></div></div></span><span></span><span></span><span class="player-extras-wrap"><ul class="player-extras" role="navigation" aria-label="audio player navigation"><li><button class="btn-newscast" data-metrics="{}"><b>Hourly News</b></button></li><li><button class="btn-live-radio" data-metrics="{}"><b>Listen Live</b></button></li><li><button class="btn-playlist" data-metrics="{}"><b>Playlist</b></button></li></ul></span><span></span><!-- react-text: 29 --><!-- /react-text --><!-- react-text: 30 --><!-- /react-text --></section></span></div></div></div></div><header class="npr-header" id="globalheader" aria-label="NPR header">
<nav class="navigation">
    <div class="navigation__top-level-wrap" data-metrics-all="" data-metrics-category="global navigation">
        <ul class="navigation__top-level">
            <li class="navigation__toggle-wrap">
                <button id="navigation__toggle--open" class="navigation__toggle navigation__toggle--open" aria-haspopup="true" aria-expanded="false" data-metrics-action="open navigation menu">
                    Open Navigation Menu
                </button>
            </li>

            <li class="navigation__home">
                <a href="/" data-metrics-action="click npr logo">
                    <img src="https://media.npr.org/chrome_svg/npr-logo.svg" alt="NPR logo">
                </a>
            </li>

            <li id="navigation__station-mount"><!-- react-empty: 1 --></li>

            <li id="user-account-dropdown-mount" class="navigation__account"><div data-reactroot="" class="user-account"><a class="user-account__sign-in" href="#" data-metrics="{&quot;action&quot;:&quot;click sign in/register&quot;,&quot;label&quot;:&quot;/2020/12/23/*********/fork-the-government&quot;,&quot;category&quot;:&quot;global navigation&quot;}">Sign In</a></div></li>

            <li class="navigation__shop">
                <a href="https://shop.npr.org" data-metrics-action="click npr shop">NPR Shop</a>
            </li>

            <li id="navigation__station-donate-mount"><a data-reactroot="" class="donation-links__button" href="/donations/support" data-metrics="{&quot;action&quot;:&quot;click donate&quot;,&quot;label&quot;:&quot;not localized&quot;,&quot;category&quot;:&quot;global navigation&quot;}"><span class="navigation__donate">Donate</span></a></li>
        </ul>
    </div>
    <section id="main-menu" class="menu menu--main" data-metrics-all="" data-metrics-category="global navigation">
        <div class="menu__inner">
            <header class="menu__header">
                <button id="navigation__toggle--close" class="navigation__toggle navigation__toggle--close" aria-haspopup="true" aria-expanded="true" data-metrics-action="close navigation menu">
                    Close <span>Navigation Menu</span>
                </button>
            </header>
            <ul class="menu__list">
                <li class="menu__item menu__item--home">
                    <div class="menu__item-inner"><a href="/" data-metrics-action="click npr logo">Home</a></div>
                </li>
                <li class="menu__item menu__item--news menu__item--has-submenu" aria-haspopup="true" aria-expanded="false">
                    <div class="menu__item-inner">
                        <a href="/sections/news/" data-metrics-action="click news">News</a>
                        <button class="menu__toggle-submenu" data-metrics-action="toggle news drawer">Expand/collapse submenu for News</button>
                    </div>

                    <ul class="submenu submenu--news">
                        <li class="submenu__item"><a href="/sections/national/" data-metrics-action="click national">National</a></li>
                        <li class="submenu__item"><a href="/sections/world/" data-metrics-action="click world">World</a></li>
                        <li class="submenu__item"><a href="/sections/politics/" data-metrics-action="click politics">Politics</a></li>
                        <li class="submenu__item"><a href="/sections/business/" data-metrics-action="click business">Business</a></li>
                        <li class="submenu__item"><a href="/sections/health/" data-metrics-action="click health">Health</a></li>
                        <li class="submenu__item"><a href="/sections/science/" data-metrics-action="click science">Science</a></li>
                        <li class="submenu__item"><a href="/sections/technology/" data-metrics-action="click technology">Technology</a></li>
                        <li class="submenu__item"><a href="/sections/codeswitch/" data-metrics-action="click race &amp; culture">Race &amp; Culture</a></li>
                    </ul>
                </li>
                <li class="menu__item menu__item--arts-life menu__item--has-submenu" aria-haspopup="true" aria-expanded="false">
                    <div class="menu__item-inner">
                        <a href="/sections/arts/" data-metrics-action="click arts &amp; life">Arts &amp; Life</a>
                        <button class="menu__toggle-submenu" data-metrics-action="toggle arts drawer">Expand/collapse submenu for Arts &amp; Life</button>
                    </div>

                    <ul class="submenu submenu--arts-life">
                        <li class="submenu__item"><a href="/books/" data-metrics-action="click books">Books</a></li>
                        <li class="submenu__item"><a href="/sections/movies/" data-metrics-action="click movies">Movies</a></li>
                        <li class="submenu__item"><a href="/sections/television/" data-metrics-action="click television">Television</a></li>
                        <li class="submenu__item"><a href="/sections/pop-culture/" data-metrics-action="click pop culture">Pop Culture</a></li>
                        <li class="submenu__item"><a href="/sections/food/" data-metrics-action="click food">Food</a></li>
                        <li class="submenu__item"><a href="/sections/art-design/" data-metrics-action="click art &amp; design">Art &amp; Design</a> </li>
                        <li class="submenu__item"><a href="/sections/performing-arts/" data-metrics-action="click performing arts">Performing Arts</a></li>
                    </ul>
                </li>
                <li class="menu__item menu__item--music menu__item--has-submenu" aria-haspopup="true" aria-expanded="false">
                    <div class="menu__item-inner">
                        <a href="/music/" data-metrics-action="click music">Music</a>
                        <button class="menu__toggle-submenu" data-metrics-action="toggle music drawer">Expand/collapse submenu for Music</button>
                    </div>

                    <ul class="submenu submenu--music"><li class="submenu__item">
    <a href="https://www.npr.org/series/tiny-desk-concerts/" data-metrics-action="click tiny desk">
        Tiny Desk
    </a>
</li><li class="submenu__item">
    <a href="https://www.npr.org/sections/allsongs/" data-metrics-action="click all songs considered">
        All Songs Considered
    </a>
</li><li class="submenu__item">
    <a href="https://www.npr.org/series/801298042/best-music-of-2020" data-metrics-action="click best music of 2020">
        Best Music Of 2020
    </a>
</li><li class="submenu__item">
    <a href="https://www.npr.org/sections/music-news/" data-metrics-action="click music news">
        Music News
    </a>
</li><li class="submenu__item">
    <a href="https://www.npr.org/sections/new-music/" data-metrics-action="click new music">
        New Music
    </a>
</li><li class="submenu__item">
    <a href="https://www.npr.org/sections/music-features" data-metrics-action="click music features">
        Music Features
    </a>
</li><li class="submenu__item">
    <a href="https://www.npr.org/series/770565791/npr-music-live-sessions" data-metrics-action="click live sessions">
        Live Sessions
    </a>
</li></ul>

                </li>
                <li class="menu__item menu__item--shows-podcasts menu__item--has-submenu" aria-haspopup="true" aria-expanded="false">
                    <div class="menu__item-inner">
                        <a href="/programs/" data-metrics-action="click shows &amp; podcasts">Shows &amp; Podcasts</a>
                        <button class="menu__toggle-submenu" data-metrics-action="toggle programs &amp; podcasts drawer">Expand/collapse submenu for Shows &amp; Podcasts</button>
                    </div>

                    <div class="submenu submenu--shows-podcasts">
                        <span class="submenu__category submenu__category--daily">Daily</span>
                        <ul class="submenu__list submenu__list--daily">
                            <li class="submenu__item submenu__item--timed submenu__item--weekday">
                                <a href="/programs/morning-edition/" data-metrics-action="click morning edition">
                                    <img src="https://media.npr.org/chrome/programs/logos/morning-edition.jpg" alt="">
                                    Morning Edition
                                </a>
                            </li>
                            <li class="submenu__item submenu__item--timed submenu__item--saturday hidden">
                                <a href="/programs/weekend-edition-saturday/" data-metrics-action="click weekend edition saturday">
                                    <img src="https://media.npr.org/assets/img/2019/02/26/we_otherentitiestemplatesat_sq-cbde87a2fa31b01047441e6f34d2769b0287bcd4-s100-c85.png" alt="">
                                    Weekend Edition Saturday
                                </a>
                            </li>
                            <li class="submenu__item submenu__item--timed submenu__item--sunday hidden">
                                <a href="/programs/weekend-edition-sunday/" data-metrics-action="click weekend edition sunday">
                                    <img src="https://media.npr.org/assets/img/2019/02/26/we_otherentitiestemplatesun_sq-4a03b35e7e5adfa446aec374523a578d54dc9bf5-s100-c85.png" alt="">
                                    Weekend Edition Sunday
                                </a>
                            </li>
                            <li class="submenu__item">
                                <a href="/programs/all-things-considered/" data-metrics-action="click all things considered">
                                    <img src="https://media.npr.org/chrome/programs/logos/all-things-considered.png" alt="">
                                    All Things Considered
                                </a>
                            </li>
                            <li class="submenu__item">
                                <a href="/programs/fresh-air/" data-metrics-action="click fresh air">
                                    <img src="https://media.npr.org/chrome/programs/logos/fresh-air.png" alt="">
                                    Fresh Air
                                </a>
                            </li>
                            <li class="submenu__item">
                                <a href="/podcasts/510318/up-first" data-metrics-action="click up first">
                                    <img src="https://media.npr.org/chrome/programs/logos/up-first.jpg?version=2" alt="">
                                    Up First
                                </a>
                            </li>
                        </ul>
                        <span class="submenu__category submenu__category--featured">Featured</span>

                        <ul class="submenu__list submenu__list--featured"><li class="submenu__item">
    <a href="https://www.npr.org/podcasts/510338/all-guides" data-metrics-action="click life kit">
        <img src="https://media.npr.org/assets/img/2020/12/21/lifekit_tile_3000x3000_sq-b3f049c61e447353e6d40c158e91a260f707b64f.png?s=100" alt="" class="hoverZoomLink">
        Life Kit
    </a>
</li><li class="submenu__item">
    <a href="https://www.npr.org/podcasts/510289/planet-money" data-metrics-action="click planet money">
        <img src="https://media.npr.org/assets/img/2018/08/02/npr_planetmoney_podcasttile_sq-7b7fab0b52fd72826936c3dbe51cff94889797a0.jpg?s=100" alt="" class="hoverZoomLink">
        Planet Money
    </a>
</li><li class="submenu__item">
    <a href="https://www.npr.org/podcasts/510313/how-i-built-this" data-metrics-action="click how i built this with guy raz">
        <img src="https://media.npr.org/assets/img/2018/08/03/npr_hibt_podcasttile_sq-98320b282169a8cea04a406530e6e7b957665b3f.jpg?s=100" alt="" class="hoverZoomLink">
        How I Built This with Guy Raz
    </a>
</li><li class="submenu__item">
    <a href="https://www.npr.org/podcasts/344098539/wait-wait-don-t-tell-me" data-metrics-action="click wait wait... don't tell me!">
        <img src="https://media.npr.org/assets/img/2019/11/20/npr_91201389-1-_sq-ca0128ef6da8bbc571cf2bc15e5aecb4d1b33fb4.jpg?s=100" alt="" class="hoverZoomLink">
        Wait Wait... Don't Tell Me!
    </a>
</li></ul>

                        <ul class="submenu__list submenu__list--directories">
                            <li class="submenu__item"><a href="/programs/" data-metrics-action="click all programs">More Shows &amp; Podcasts</a></li>
                        </ul>
                    </div>
                </li>
                <li class="menu__item menu__item--search">
                    <div class="menu__item-inner">
                        <a href="/search" id="navigation_dropdown-search" data-metrics-action="click search">Search</a>
                    </div>
                </li>
                <li id="user-account-dropdown-mount-two" class="menu__item menu__item--account"><div data-reactroot="" class="user-account"><a class="user-account__sign-in" href="#" data-metrics="{&quot;action&quot;:&quot;click sign in/register&quot;,&quot;label&quot;:&quot;/2020/12/23/*********/fork-the-government&quot;,&quot;category&quot;:&quot;global navigation&quot;}">Sign In</a></div></li>
                <li class="menu__item menu__item--shop">
                    <div class="menu__item-inner">
                        <a href="https://shop.npr.org" data-metrics-action="click npr shop">NPR Shop</a>
                    </div>
                </li>
            </ul>
        </div>
    </section>
    <section class="menu menu--music" data-metrics-all="" data-metrics-category="music ecosystem navigation">
        <div class="menu__list-wrap">
            <ul class="menu__list menu__list--ecosystem menu__list--music">
                <li class="menu__item menu__item--music-home">
                    <a href="/music/" data-metrics-action="click npr music logo">
                        <img src="https://media.npr.org/chrome_svg/music-logo-dark.svg" class="music-logo" alt="NPR Music">
                        <img src="https://media.npr.org/chrome_svg/music-logo-light.svg" class="music-logo-alt" alt="NPR Music">
                    </a>
                </li>

                <li class="menu__item">
    <a href="https://www.npr.org/series/tiny-desk-concerts/" data-metrics-action="click tiny desk">
        Tiny Desk
    </a>
</li><li class="menu__item">
    <a href="https://www.npr.org/sections/allsongs/" data-metrics-action="click all songs considered">
        All Songs Considered
    </a>
</li><li class="menu__item">
    <a href="https://www.npr.org/series/801298042/best-music-of-2020" data-metrics-action="click best music of 2020">
        Best Music Of 2020
    </a>
</li><li class="menu__item">
    <a href="https://www.npr.org/sections/music-news/" data-metrics-action="click music news">
        Music News
    </a>
</li><li class="menu__item">
    <a href="https://www.npr.org/sections/new-music/" data-metrics-action="click new music">
        New Music
    </a>
</li><li class="menu__item">
    <a href="https://www.npr.org/sections/music-features" data-metrics-action="click music features">
        Music Features
    </a>
</li><li class="menu__item">
    <a href="https://www.npr.org/series/770565791/npr-music-live-sessions" data-metrics-action="click live sessions">
        Live Sessions
    </a>
</li>
            </ul>
        </div>
    </section>
    <section class="menu menu--about" data-metrics-all="" data-metrics-category="about ecosystem navigation">
        <div class="menu__list-wrap">
            <ul class="menu__list menu__list--ecosystem menu__list--about">
                <li class="menu__item menu__item--about-home"><a href="/about/" data-metrics-action="click about npr">About NPR</a></li>
                <li class="menu__item menu__item--about-overview"><a href="/overview/" data-metrics-action="click overview">Overview</a></li>
                <li class="menu__item menu__item--about-support"><a href="/support/" data-metrics-action="click support">Support</a></li>
                <li class="menu__item menu__item--about-careers"><a href="/careers/" data-metrics-action="click careers">Careers</a></li>
                <li class="menu__item menu__item--about-connect"><a href="/connect/" data-metrics-action="click connect">Connect</a></li>
                <li class="menu__item menu__item--about-press"><a href="/press/" data-metrics-action="click press">Press</a></li>
                <li class="menu__item menu__item--about-ethics"><a href="/ethics/" data-metrics-action="click ethics">Ethics</a></li>
            </ul>
        </div>
    </section>
</nav>
</header><main aria-label="main content"><div class="speakable">
      <b data-is-speakable="">Audrey Tang brings civic tech to Taiwan's coronavirus pandemic response : Planet Money</b>   <b data-is-speakable="">A global pandemic might not be the best time to try something new with technology. But Taiwan decided to do it anyway. | Subscribe to our weekly newsletter <a href="https://www.npr.org/newsletter/money?utm_source=rss_feed_copy&amp;utm_medium=podcast&amp;utm_term=planet_money">here</a>.</b>
</div>
<div id="wrapper">
      <div class="story-sticky-header">
    <div class="story-sticky-header-inner">
        <a href="/" class="nprhome nprhome-news" data-metrics="{&quot;category&quot;:&quot;global navigation&quot;,&quot;action&quot;:&quot;click npr logo&quot;,&quot;label&quot;:&quot;\/&quot;}">
            <img src="https://media.npr.org/chrome_svg/npr-logo.svg" alt="NPR logo">
        </a>

        <article id="res949890910" class="bucketwrap resaudio" aria-label="audio-module"><div class="audio-module">
      <h4 class="audio-module-title">Fork The Government</h4>
   <div class="audio-module-controls-wrap" data-audio-metrics="{&quot;playedFrom&quot;:&quot;from sticky header&quot;}"><div data-reactroot="" class="audio-module-controls"><button class="audio-module-listen"><b class="audio-module-listen-inner"><b class="audio-module-listen-icon icn-play"></b><b class="audio-module-listen-text"><b class="audio-module-cta">Listen</b><b class="audio-module-listen-duration"><!-- react-text: 8 -->· <!-- /react-text --><!-- react-text: 9 -->24:37<!-- /react-text --></b></b></b></button><time class="audio-module-duration" datetime="P24M,37S">24-Minute Listen</time><button class="audio-module-queue"><b class="audio-module-queue-inner"><b class="icn-playlist"></b><b class="audio-module-queue-text"><b class="audio-module-queue-text-extra">Add to </b><!-- react-text: 16 -->Playlist<!-- /react-text --></b></b></button></div></div>


    <div class="audio-module-tools">
        <button class="audio-module-tools-toggle" data-metrics="{&quot;category&quot;:&quot;audio actions&quot;,&quot;action&quot;:&quot;open more options&quot;,&quot;label&quot;:null,&quot;noninteraction&quot;:true}">
            <b class="icn-toggle"></b>
            <b class="label">Toggle more options</b>
        </button>
        <ul class="audio-module-more-tools">
                <li class="audio-tool audio-tool-download"><a href="https://play.podtrac.com/510289/edge1.pod.npr.org/anon.npr-mp3/npr/pmoney/2020/12/20201223_pmoney_pmpod1054.mp3?orgId=1&amp;topicId=1125&amp;d=1477&amp;p=510289&amp;story=*********&amp;t=podcast&amp;e=*********&amp;dl=1&amp;siteplayer=true&amp;size=23584686&amp;awCollectionId=510289&amp;awEpisodeId=*********&amp;dl=1" data-metrics="{&quot;category&quot;:&quot;audio actions&quot;,&quot;action&quot;:&quot;download audio&quot;,&quot;label&quot;:&quot;*********&quot;,&quot;noninteraction&quot;:true}"><b class="icn-download"></b><b class="audio-tool-label">Download</b></a>
                </li>

                <li class="audio-tool audio-tool-embed">
                    <button data-embed-url="https://www.npr.org/player/embed/*********/949890910" data-metrics="{&quot;category&quot;:&quot;audio actions&quot;,&quot;action&quot;:&quot;embed audio&quot;,&quot;label&quot;:&quot;*********&quot;,&quot;noninteraction&quot;:true}"><b class="button-inner"><b class="icn-embed"></b><b class="audio-tool-label">Embed</b></b></button>

                    <div class="audio-embed-overlay">
                        <button class="btn-close"></button>
                        <label class="embed-label">
                            <b class="audio-embed-overlay__icon"></b>
                            <b class="label">Embed</b>
                            <input class="embed-url embed-url-no-touch" readonly="" value="<iframe src=&quot;https://www.npr.org/player/embed/*********/949890910&quot; width=&quot;100%&quot; height=&quot;290&quot; frameborder=&quot;0&quot; scrolling=&quot;no&quot; title=&quot;NPR embedded audio player&quot;></iframe>">
                        </label>

                        <b class="embed-url embed-url-touch">
                            <code><b class="punctuation">&lt;</b>iframe src="https://www.npr.org/player/embed/*********/949890910" width="100%" height="290" frameborder="0" scrolling="no" title="NPR embedded audio player"&gt;</code>
                        </b>

                    </div>
                </li>

                <li class="audio-tool audio-tool-transcript">
                  <a href="https://www.npr.org/transcripts/*********" data-metrics="{&quot;category&quot;:&quot;audio actions&quot;,&quot;action&quot;:&quot;click transcript&quot;,&quot;label&quot;:&quot;*********&quot;,&quot;noninteraction&quot;:true}">
                    <b class="icn-transcript"></b>
                    <b class="audio-tool-label">Transcript</b>
                  </a>
                </li>
        </ul>
    </div>
</div>
</article>

        <a href="#" class="title">
          <b>Fork The Government</b>
          <time datetime="P24M,37S">24:37</time>
        </a>
    </div>
</div>   <section id="main-section"><header class="contentheader contentheader--one" data-metrics="{&quot;category&quot;:&quot;Fork The Government&quot;}"></header><article class="story"><a id="mainContent"></a><ul class="podcast-tools podcast-tools--510289">
    <div class="imagewrap" data-crop-type="">
    <img src="//media.npr.org/assets/img/2018/08/02/npr_planetmoney_podcasttile_sq-7b7fab0b52fd72826936c3dbe51cff94889797a0-s100-c85.jpg" class="img" alt="Planet Money" loading="lazy" style="">
</div>
<div class="credit-caption">
    <div class="caption-wrap">
        <div class="caption" aria-label="Image caption">
        </div>


    </div>

    <span class="credit" aria-label="Image credit">

        NPR

    </span>
</div>

    <li class="subscribe" data-sharepop="" data-selector=".sharepop.subscribe">
      <button data-metrics="{&quot;category&quot;:&quot;podcast directory&quot;,&quot;action&quot;:&quot;toggle subscribe menu&quot;}">subscribe
        <span class="subscribe-more-info"> to
          <span class="channel-title">Planet Money</span>
          <span class="generic-title">podcast</span>
        </span>
      </button>
    </li>

    <li class="podcast-tools__subscribe-links">
      <div class="sharepop-four">
          <div class="sharepop">
              <ul>
                  <li>
                  <svg width="17" class="subscribe__tooltip">
                      <g id="Canvas" fill="none">
                        <g id="tooltip-point">
                          <path d="M 10.7679 3C 11.5377 1.66667 13.4623 1.66667 14.2321 3L 21.5933 15.75C 22.3631 17.0833 21.4008 18.75 19.8612 18.75L 5.13878 18.75C 3.59918 18.75 2.63693 17.0833 3.40673 15.75L 10.7679 3Z" transform="matrix(0 -1 1 0 -2 22)" fill="#1D3261"></path>
                        </g>
                      </g>
                    </svg>
                    <a href="https://rpb3r.app.goo.gl/M4f5" data-analytics-allow-facebook="data-analytics-allow-facebook" data-metrics="{&quot;category&quot;:&quot;podcast directory&quot;,&quot;action&quot;:&quot;click npr one universal&quot;,&quot;label&quot;:&quot;planet money&quot;}">NPR One</a></li>
                  <li>
                    <a href="https://podcasts.apple.com/podcast/id290783428?mt=2&amp;at=11l79Y&amp;ct=nprdirectory" data-analytics-allow-facebook="data-analytics-allow-facebook" data-metrics="{&quot;category&quot;:&quot;podcast directory&quot;,&quot;action&quot;:&quot;click itunes&quot;,&quot;label&quot;:&quot;planet money&quot;}">Apple Podcasts</a></li>
                  <li>
                    <a href="https://www.google.com/podcasts?feed=aHR0cHM6Ly93d3cubnByLm9yZy9yc3MvcG9kY2FzdC5waHA_aWQ9NTEwMjg5" data-analytics-allow-facebook="data-analytics-allow-facebook" data-metrics="{&quot;category&quot;:&quot;podcast directory&quot;,&quot;action&quot;:&quot;click google podcasts&quot;,&quot;label&quot;:&quot;planet money&quot;}">Google Podcasts</a></li>
                  <li>
                      <a href="https://pca.st/9NRE" data-analytics-allow-facebook="data-analytics-allow-facebook" data-metrics="{&quot;category&quot;:&quot;podcast directory&quot;,&quot;action&quot;:&quot;click Pocket Casts&quot;,&quot;label&quot;:&quot;planet money&quot;}">Pocket Casts</a></li>
                  <li>
                      <a href="https://open.spotify.com/show/4FYpq3lSeQMAhqNI81O0Cn" data-analytics-allow-facebook="data-analytics-allow-facebook" data-metrics="{&quot;category&quot;:&quot;podcast directory&quot;,&quot;action&quot;:&quot;click Spotify&quot;,&quot;label&quot;:&quot;planet money&quot;}">Spotify</a></li>


                  <li>
                    <a target="_blank" href="https://feeds.npr.org/510289/podcast.xml" data-analytics-allow-facebook="data-analytics-allow-facebook" data-metrics="{&quot;category&quot;:&quot;podcast directory&quot;,&quot;action&quot;:&quot;click rss&quot;,&quot;label&quot;:&quot;planet money&quot;}">RSS link</a></li>
              </ul>
          </div>
      </div>
    </li>
</ul><div class="slug-wrap">

    <h3 class="slug">
        <a href="https://www.npr.org/podcasts/510289/planet-money">Planet Money</a>
    </h3>
</div>
<div class="storytitle">
      <h1>Fork The Government</h1>
   <input type="hidden" id="title*********" value="Fork The Government">
   <input type="hidden" id="shortTitle*********" value="Fork The Government">
   <input type="hidden" id="modelFullUrl*********" value="https://www.npr.org/2020/12/23/*********/fork-the-government">
</div>

<!-- END CLASS="STORYTITLE" -->
<div class="story-tools">
      <ul class="podcast-tools podcast-tools--510289">
    <div class="imagewrap" data-crop-type="">
    <img src="//media.npr.org/assets/img/2018/08/02/npr_planetmoney_podcasttile_sq-7b7fab0b52fd72826936c3dbe51cff94889797a0-s200-c85.jpg" class="img" alt="Planet Money" loading="lazy">
</div>
<div class="credit-caption">
    <div class="caption-wrap">
        <div class="caption" aria-label="Image caption">
        </div>


    </div>

    <span class="credit" aria-label="Image credit">

        NPR

    </span>
</div>

    <li class="subscribe" data-sharepop="" data-selector=".sharepop.subscribe">
      <button data-metrics="{&quot;category&quot;:&quot;podcast directory&quot;,&quot;action&quot;:&quot;toggle subscribe menu&quot;}">subscribe
        <span class="subscribe-more-info"> to
          <span class="channel-title">Planet Money</span>
          <span class="generic-title">podcast</span>
        </span>
      </button>
    </li>

    <li class="podcast-tools__subscribe-links">
      <div class="sharepop-four">
          <div class="sharepop">
              <ul>
                  <li>
                  <svg width="17" class="subscribe__tooltip">
                      <g id="Canvas" fill="none">
                        <g id="tooltip-point">
                          <path d="M 10.7679 3C 11.5377 1.66667 13.4623 1.66667 14.2321 3L 21.5933 15.75C 22.3631 17.0833 21.4008 18.75 19.8612 18.75L 5.13878 18.75C 3.59918 18.75 2.63693 17.0833 3.40673 15.75L 10.7679 3Z" transform="matrix(0 -1 1 0 -2 22)" fill="#1D3261"></path>
                        </g>
                      </g>
                    </svg>
                    <a href="https://rpb3r.app.goo.gl/M4f5" data-analytics-allow-facebook="data-analytics-allow-facebook" data-metrics="{&quot;category&quot;:&quot;podcast directory&quot;,&quot;action&quot;:&quot;click npr one universal&quot;,&quot;label&quot;:&quot;planet money&quot;}">NPR One</a></li>
                  <li>
                    <a href="https://podcasts.apple.com/podcast/id290783428?mt=2&amp;at=11l79Y&amp;ct=nprdirectory" data-analytics-allow-facebook="data-analytics-allow-facebook" data-metrics="{&quot;category&quot;:&quot;podcast directory&quot;,&quot;action&quot;:&quot;click itunes&quot;,&quot;label&quot;:&quot;planet money&quot;}">Apple Podcasts</a></li>
                  <li>
                    <a href="https://www.google.com/podcasts?feed=aHR0cHM6Ly93d3cubnByLm9yZy9yc3MvcG9kY2FzdC5waHA_aWQ9NTEwMjg5" data-analytics-allow-facebook="data-analytics-allow-facebook" data-metrics="{&quot;category&quot;:&quot;podcast directory&quot;,&quot;action&quot;:&quot;click google podcasts&quot;,&quot;label&quot;:&quot;planet money&quot;}">Google Podcasts</a></li>
                  <li>
                      <a href="https://pca.st/9NRE" data-analytics-allow-facebook="data-analytics-allow-facebook" data-metrics="{&quot;category&quot;:&quot;podcast directory&quot;,&quot;action&quot;:&quot;click Pocket Casts&quot;,&quot;label&quot;:&quot;planet money&quot;}">Pocket Casts</a></li>
                  <li>
                      <a href="https://open.spotify.com/show/4FYpq3lSeQMAhqNI81O0Cn" data-analytics-allow-facebook="data-analytics-allow-facebook" data-metrics="{&quot;category&quot;:&quot;podcast directory&quot;,&quot;action&quot;:&quot;click Spotify&quot;,&quot;label&quot;:&quot;planet money&quot;}">Spotify</a></li>


                  <li>
                    <a target="_blank" href="https://feeds.npr.org/510289/podcast.xml" data-analytics-allow-facebook="data-analytics-allow-facebook" data-metrics="{&quot;category&quot;:&quot;podcast directory&quot;,&quot;action&quot;:&quot;click rss&quot;,&quot;label&quot;:&quot;planet money&quot;}">RSS link</a></li>
              </ul>
          </div>
      </div>
    </li>
</ul>   <div class="share-tools share-tools--primary" aria-label="Share tools">
            <ul>
                  <li class="share-tools__service share-tools__service--facebook"><button class="fbStory*********" data-share-facebook="{&quot;storyId&quot;: ********* }" data-metrics="{&quot;action&quot;:&quot;Click Facebook&quot;,&quot;category&quot;:&quot;Share Tools&quot;,&quot;label&quot;:&quot;https:\/\/www.npr.org\/2020\/12\/23\/*********\/fork-the-government&quot;}"><b class="share-tools__service-name">Facebook</b></button></li>
         <li class="share-tools__service share-tools__service--twitter"><button class="story*********" data-share-twitter="{&quot;storyId&quot;: ********* }" data-metrics="{&quot;action&quot;:&quot;Click Twitter&quot;,&quot;category&quot;:&quot;Share Tools&quot;,&quot;label&quot;:&quot;https:\/\/www.npr.org\/2020\/12\/23\/*********\/fork-the-government&quot;}"><b class="share-tools__service-name">Twitter</b></button></li>
         <li class="share-tools__service share-tools__service--flipboard"><button class="story*********" data-share-flipboard="{&quot;storyId&quot;: ********* }" data-metrics="{&quot;action&quot;:&quot;Click Flipboard&quot;,&quot;category&quot;:&quot;Share Tools&quot;,&quot;label&quot;:&quot;https:\/\/www.npr.org\/2020\/12\/23\/*********\/fork-the-government&quot;}"><b class="share-tools__service-name">Flipboard</b></button></li>
         <li class="share-tools__service share-tools__service--email"><button class="share-tools__email-link" data-story-id="*********" data-metrics="{&quot;action&quot;:&quot;Click Email a Friend&quot;,&quot;category&quot;:&quot;Share Tools&quot;,&quot;label&quot;:&quot;https:\/\/www.npr.org\/2020\/12\/23\/*********\/fork-the-government&quot;}"><b class="share-tools__service-name">Email</b></button></li>
      </ul>
   </div>

<!-- END CLASS="SHARE-TOOLS SHARE-TOOLS--PRIMARY" ARIA-LABEL="SHARE TOOLS" -->
</div>

<!-- END CLASS="STORY-TOOLS" -->
<div id="story-meta" class="story-meta has-byline">
      <div class="story-meta__one">
            <div class="dateblock">
    <time datetime="2020-12-23T22:36:27-05:00">
            <span class="date">December 23, 2020</span><span class="time">10:36 PM ET</span>
    </time>
</div>
   </div>

<!-- END CLASS="STORY-META__ONE" -->
   <div class="story-meta__two">
            <div id="storybyline" class="storybyline-wrap linkLocation">
                  <div class="bucketwrap byline" id="res949764448" previewtitle="bylines">
                        <div class="byline-container--block">
                              <div class="byline byline--block byline--has-link" aria-label="Byline">

  <p class="byline__name byline__name--block">
    <a href="https://www.npr.org/people/724387257/darian-woods" rel="author" data-metrics="{&quot;action&quot;:&quot;Click Byline&quot;,&quot;category&quot;:&quot;Story Metadata&quot;}">
      Darian Woods
    </a>
  </p>

    <a class="byline__social-handle byline__social-handle--5" href="https://www.twitter.com/darianwoods" data-metrics="{&quot;action&quot;:&quot;Click Social Handle&quot;,&quot;category&quot;:&quot;Story Metadata&quot;}">
      Twitter
    </a>
</div>               <div class="byline byline--block byline--has-link" aria-label="Byline">
    <div class="byline__photo">
      <a href="https://www.npr.org/people/605388008/sarah-gonzalez" rel="author" data-metrics="{&quot;action&quot;:&quot;Click Byline&quot;,&quot;category&quot;:&quot;Story Metadata&quot;}">
        <img src="https://media.npr.org/assets/img/2018/05/01/sarah-gonzalez-picture-003-_sq-8407b232e977a5733b1dd033741ebedbfd3fe0cf-s100-c85.jpg" class="img" alt="Head shot of Sarah Gonzalez (2018 square)">
      </a>
    </div>

  <p class="byline__name byline__name--block">
    <a href="https://www.npr.org/people/605388008/sarah-gonzalez" rel="author" data-metrics="{&quot;action&quot;:&quot;Click Byline&quot;,&quot;category&quot;:&quot;Story Metadata&quot;}">
      Sarah Gonzalez
    </a>
  </p>

    <a class="byline__social-handle byline__social-handle--5" href="https://www.twitter.com/GonzalezSarahA" data-metrics="{&quot;action&quot;:&quot;Click Social Handle&quot;,&quot;category&quot;:&quot;Story Metadata&quot;}">
      Twitter
    </a>
</div>
            </div>
         </div>

<!-- END CLASS="BUCKETWRAP BYLINE" ID="RES949764448" PREVIEWTITLE="BYLINES" -->
      </div>
   </div>

<!-- END CLASS="STORY-META__TWO" -->
</div>

<!-- END ID="STORY-META" CLASS="STORY-META HAS-BYLINE" -->
<div id="primaryaudio" class="storylocation linkLocation">
      <article id="res949890910" class="bucketwrap resaudio primaryaudio" aria-label="audio-module"><div class="audio-module">
      <h4 class="audio-module-title">Fork The Government</h4>
   <div class="audio-module-controls-wrap" data-audio-metrics="[]"><div data-reactroot="" class="audio-module-controls"><button class="audio-module-listen"><b class="audio-module-listen-inner"><b class="audio-module-listen-icon icn-play"></b><b class="audio-module-listen-text"><b class="audio-module-cta">Listen</b><b class="audio-module-listen-duration"><!-- react-text: 8 -->· <!-- /react-text --><!-- react-text: 9 -->24:37<!-- /react-text --></b></b></b></button><time class="audio-module-duration" datetime="P24M,37S">24-Minute Listen</time><button class="audio-module-queue"><b class="audio-module-queue-inner"><b class="icn-playlist"></b><b class="audio-module-queue-text"><b class="audio-module-queue-text-extra">Add to </b><!-- react-text: 16 -->Playlist<!-- /react-text --></b></b></button></div></div>


    <div class="audio-module-tools">
        <button class="audio-module-tools-toggle" data-metrics="{&quot;category&quot;:&quot;audio actions&quot;,&quot;action&quot;:&quot;open more options&quot;,&quot;label&quot;:null,&quot;noninteraction&quot;:true}">
            <b class="icn-toggle"></b>
            <b class="label">Toggle more options</b>
        </button>
        <ul class="audio-module-more-tools">
                <li class="audio-tool audio-tool-download"><a href="https://play.podtrac.com/510289/edge1.pod.npr.org/anon.npr-mp3/npr/pmoney/2020/12/20201223_pmoney_pmpod1054.mp3?orgId=1&amp;topicId=1125&amp;d=1477&amp;p=510289&amp;story=*********&amp;t=podcast&amp;e=*********&amp;dl=1&amp;siteplayer=true&amp;size=23584686&amp;awCollectionId=510289&amp;awEpisodeId=*********&amp;dl=1" data-metrics="{&quot;category&quot;:&quot;audio actions&quot;,&quot;action&quot;:&quot;download audio&quot;,&quot;label&quot;:&quot;*********&quot;,&quot;noninteraction&quot;:true}"><b class="icn-download"></b><b class="audio-tool-label">Download</b></a>
                </li>

                <li class="audio-tool audio-tool-embed">
                    <button data-embed-url="https://www.npr.org/player/embed/*********/949890910" data-metrics="{&quot;category&quot;:&quot;audio actions&quot;,&quot;action&quot;:&quot;embed audio&quot;,&quot;label&quot;:&quot;*********&quot;,&quot;noninteraction&quot;:true}"><b class="button-inner"><b class="icn-embed"></b><b class="audio-tool-label">Embed</b></b></button>

                    <div class="audio-embed-overlay">
                        <button class="btn-close"></button>
                        <label class="embed-label">
                            <b class="audio-embed-overlay__icon"></b>
                            <b class="label">Embed</b>
                            <input class="embed-url embed-url-no-touch" readonly="" value="<iframe src=&quot;https://www.npr.org/player/embed/*********/949890910&quot; width=&quot;100%&quot; height=&quot;290&quot; frameborder=&quot;0&quot; scrolling=&quot;no&quot; title=&quot;NPR embedded audio player&quot;></iframe>">
                        </label>

                        <b class="embed-url embed-url-touch">
                            <code><b class="punctuation">&lt;</b>iframe src="https://www.npr.org/player/embed/*********/949890910" width="100%" height="290" frameborder="0" scrolling="no" title="NPR embedded audio player"&gt;</code>
                        </b>

                    </div>
                </li>

                <li class="audio-tool audio-tool-transcript">
                  <a href="https://www.npr.org/transcripts/*********" data-metrics="{&quot;category&quot;:&quot;audio actions&quot;,&quot;action&quot;:&quot;click transcript&quot;,&quot;label&quot;:&quot;*********&quot;,&quot;noninteraction&quot;:true}">
                    <b class="icn-transcript"></b>
                    <b class="audio-tool-label">Transcript</b>
                  </a>
                </li>
        </ul>
    </div>
</div>
</article>
</div>
<div id="storytext" class="storytext storylocation linkLocation">
      <div id="res949773762" class="bucketwrap image large">
            <div class="imagewrap" data-crop-type="">
    <img src="//media.npr.org/assets/img/2020/12/23/gettyimages-1199493836_wide-b0f8c2e44d3617f2f5ff7f4dceff064ecad00439-s1600-c85.jpg" class="img" alt="A Taiwanese new system app on a smartphone to find the closest pharmacies to buy face masks in New Taipei." loading="lazy">
        <div class="enlarge-options">

        <button class="enlargebtn">Enlarge this image</button></div>
</div>
<div class="credit-caption">
    <div class="caption-wrap">
        <div class="caption" aria-label="Image caption">
        </div>


    </div>

    <span class="credit" aria-label="Image credit">

        Walid Berrazeg/SOPA Images/LightRocket via Getty Images

    </span>
</div>
<div class="enlarge_measure">
    <div class="img_wrap">
        <img data-original="https://media.npr.org/assets/img/2020/12/23/gettyimages-1199493836_wide-b0f8c2e44d3617f2f5ff7f4dceff064ecad00439-s1200.jpg" alt="A Taiwanese new system app on a smartphone to find the closest pharmacies to buy face masks in New Taipei.">
    </div>
</div>
<div class="enlarge_html">
    <div class="image_data">
        <span class="credit" aria-label="Image credit">

            Walid Berrazeg/SOPA Images/LightRocket via Getty Images

        </span>
    </div>
</div>
   </div>
   <p>As countries around the world struggle to handle the coronavirus pandemic, Taiwan stands out as a relative success story... so far. Since April, only one locally transmitted case has been reported. There have been only seven deaths — in the entire country.</p>   <p>There are a lot of reasons why Taiwan has been able to keep its infection and death rates so low. For one, it's an island. Also, it's dealt with a respiratory virus epidemic before.</p>   <p>But Taiwan has also been taking a relatively experimental approach to the pandemic with technology. Like working with civic hackers to code its way out of the pandemic. Today on the show, we dive into Taiwan's pandemic policies and ask: Would the U.S. ever take a similar approach?</p>   <p><em>Music: "</em><a href="http://links.firstcom.com/uD4P2x"><em>Souvenir Dansant</em></a><em>" and "</em><a href="http://links.firstcom.com/yGrtWt"><em>Blonde Disco</em></a><em>." </em></p>   <p><em>Find us: </em><a href="http://twitter.com/planetmoney"><em>Twitter</em></a><em> / </em><a href="http://www.facebook.com/home.php?ref=home#%21/planetmoney?ref=ts"><em>Facebook</em></a><em> / </em><a href="https://www.instagram.com/planetmoney/"><em>Instagram</em></a><em> / </em><a href="https://www.tiktok.com/@planetmoney"><em>TikTok</em></a></p>   <p><em>Subscribe to our show on</em><a href="https://itunes.apple.com/us/podcast/planet-money/id290783428?mt=2"><em> Apple Podcasts</em></a><em>, </em><a href="http://pca.st/planetmoney"><em>Pocket Casts</em></a><em> and </em><a href="http://one.npr.org/"><em>NPR One</em></a><em>.</em></p>   <p><em>Does the part of your brain that loves to learn and laugh feel weirdly active when listening to Planet Money? This is not a hack. This is just us! For more of the same, </em><a href="http://donate.npr.org/planetmoney"><em>support us</em></a><em>.</em></p>   <aside id="ad-backstage-wrap" aria-label="advertisement">


    <div id="ad-backstage-podcasts_Planet_Money" class="ad-backstage" data-ad-config="{&quot;network&quot;:&quot;\/6735\/&quot;,&quot;site&quot;:{&quot;default&quot;:&quot;n6735.NPR&quot;,&quot;mobile&quot;:&quot;n6735.NPRMOBILE&quot;,&quot;sponsor_content&quot;:&quot;npr_sponsor_content&quot;,&quot;default_secondary&quot;:&quot;NPRSecondary&quot;,&quot;mobile_secondary&quot;:&quot;NPRMobileSecondary&quot;},&quot;zone&quot;:&quot;podcasts_Planet_Money&quot;,&quot;targets&quot;:{&quot;testserver&quot;:&quot;false&quot;,&quot;isPodcastEpisode&quot;:&quot;true&quot;,&quot;storyId&quot;:&quot;*********&quot;,&quot;blog&quot;:&quot;********&quot;,&quot;orgId&quot;:&quot;1&quot;},&quot;location&quot;:&quot;backstage&quot;,&quot;deferred&quot;:false,&quot;isBetweenContent&quot;:false,&quot;isAggSponsorship&quot;:false,&quot;borderClass&quot;:&quot;&quot;}"></div>

</aside>
</div>
<div class="sponsors-link-container">
      <ul class="sponsor-link">
      <li><a data-metrics="{&quot;category&quot;:&quot;podcast directory&quot;,&quot;action&quot;:&quot;click sponsor link Planet Money&quot;}" href="https://www.nationalpublicmedia.com/podcastsponsors/planetmoney">See Planet Money sponsors and promo codes </a></li>
   </ul>
</div>

<!-- END CLASS="SPONSORS-LINK-CONTAINER"  -->
<div class="share-tools share-tools--secondary" aria-label="Share tools">
      <ul>
            <li class="share-tools__service share-tools__service--facebook"><button class="fbStory*********" data-share-facebook="{&quot;storyId&quot;: ********* }" data-metrics="{&quot;action&quot;:&quot;Click Facebook&quot;,&quot;category&quot;:&quot;Share Tools&quot;,&quot;label&quot;:&quot;https:\/\/www.npr.org\/2020\/12\/23\/*********\/fork-the-government&quot;}"><b class="share-tools__service-name">Facebook</b></button></li>
      <li class="share-tools__service share-tools__service--twitter"><button class="story*********" data-share-twitter="{&quot;storyId&quot;: ********* }" data-metrics="{&quot;action&quot;:&quot;Click Twitter&quot;,&quot;category&quot;:&quot;Share Tools&quot;,&quot;label&quot;:&quot;https:\/\/www.npr.org\/2020\/12\/23\/*********\/fork-the-government&quot;}"><b class="share-tools__service-name">Twitter</b></button></li>
      <li class="share-tools__service share-tools__service--flipboard"><button class="story*********" data-share-flipboard="{&quot;storyId&quot;: ********* }" data-metrics="{&quot;action&quot;:&quot;Click Flipboard&quot;,&quot;category&quot;:&quot;Share Tools&quot;,&quot;label&quot;:&quot;https:\/\/www.npr.org\/2020\/12\/23\/*********\/fork-the-government&quot;}"><b class="share-tools__service-name">Flipboard</b></button></li>
      <li class="share-tools__service share-tools__service--email"><button class="share-tools__email-link" data-story-id="*********" data-metrics="{&quot;action&quot;:&quot;Click Email a Friend&quot;,&quot;category&quot;:&quot;Share Tools&quot;,&quot;label&quot;:&quot;https:\/\/www.npr.org\/2020\/12\/23\/*********\/fork-the-government&quot;}"><b class="share-tools__service-name">Email</b></button></li>
   </ul>
</div>

<!-- END CLASS="SHARE-TOOLS SHARE-TOOLS--SECONDARY" ARIA-LABEL="SHARE TOOLS" -->
<div id="newsletter-acquisition-callout-data" data-newsletter="{&quot;newsletterId&quot;:&quot;money&quot;,&quot;title&quot;:&quot;Planet Money&quot;,&quot;marketingHeader&quot;:&quot;Sign Up For The Planet Money Newsletter&quot;,&quot;frequency&quot;:&quot;every week&quot;,&quot;shortDescription&quot;:&quot;Just the right amount of economics, sent weekly.&quot;,&quot;stickyDescription&quot;:&quot;Sign up for the Planet Money newsletter. Just the right amount of economics, sent weekly.&quot;,&quot;contentImage&quot;:&quot;https:\/\/media.npr.org\/assets\/img\/2019\/04\/16\/newsletter_planet_money_custom.png&quot;,&quot;staticMarkupDir&quot;:&quot;\/buckets\/blogs\/money\/subnav.html&quot;,&quot;brandingDir&quot;:&quot;\/branding\/sections\/money\/&quot;,&quot;brandingLink&quot;:&quot;https:\/\/www.npr.org\/sections\/money\/&quot;,&quot;organizationId&quot;:1,&quot;recaptchaSiteKey&quot;:&quot;6LfD6CYUAAAAAIBeUekwZ9KCjF4UyLFtu7NWNaEK&quot;}">
   </div>

<!-- END ID="NEWSLETTER-ACQUISITION-CALLOUT-DATA" DATA-NEWSLETTER="{&QUOT;NEWSLETTERID&QUOT;:&QUOT;MONEY&QUOT;,&QUOT;TITLE&QUOT;:&QUOT;PLANET MONEY&QUOT;,&QUOT;MARKETINGHEADER&QUOT;:&QUOT;SIGN UP FOR THE PLANET MONEY NEWSLETTER&QUOT;,&QUOT;FREQUENCY&QUOT;:&QUOT;EVERY WEEK&QUOT;,&QUOT;SHORTDESCRIPTION&QUOT;:&QUOT;JUST THE RIGHT AMOUNT OF ECONOMICS, SENT WEEKLY.&QUOT;,&QUOT;STICKYDESCRIPTION&QUOT;:&QUOT;SIGN UP FOR THE PLANET MONEY NEWSLETTER. JUST THE RIGHT AMOUNT OF ECONOMICS, SENT WEEKLY.&QUOT;,&QUOT;CONTENTIMAGE&QUOT;:&QUOT;HTTPS:\/\/MEDIA.NPR.ORG\/ASSETS\/IMG\/2019\/04\/16\/NEWSLETTER_PLANET_MONEY_CUSTOM.PNG&QUOT;,&QUOT;STATICMARKUPDIR&QUOT;:&QUOT;\/BUCKETS\/BLOGS\/MONEY\/SUBNAV.HTML&QUOT;,&QUOT;BRANDINGDIR&QUOT;:&QUOT;\/BRANDING\/SECTIONS\/MONEY\/&QUOT;,&QUOT;BRANDINGLINK&QUOT;:&QUOT;HTTPS:\/\/WWW.NPR.ORG\/SECTIONS\/MONEY\/&QUOT;,&QUOT;ORGANIZATIONID&QUOT;:1,&QUOT;RECAPTCHASITEKEY&QUOT;:&QUOT;6LFD6CYUAAAAAIBEUEKWZ9KCJF4UYLFTU7NWNAEK&QUOT;}" -->
<div id="podcast-callout-data" data-podcast="{&quot;name&quot;:&quot;Planet Money&quot;,&quot;itunesUrl&quot;:&quot;https:\/\/podcasts.apple.com\/podcast\/id290783428?mt=2&amp;at=11l79Y&amp;ct=nprdirectory&quot;,&quot;googlePodcastsUrl&quot;:&quot;https:\/\/www.google.com\/podcasts?feed=aHR0cHM6Ly93d3cubnByLm9yZy9yc3MvcG9kY2FzdC5waHA_aWQ9NTEwMjg5&quot;,&quot;rssUrl&quot;:&quot;https:\/\/feeds.npr.org\/510289\/podcast.xml&quot;,&quot;jsonFeedUrl&quot;:&quot;https:\/\/feeds.npr.org\/feeds\/*********\/feed.json&quot;,&quot;nprOneUniversalUrl&quot;:&quot;https:\/\/rpb3r.app.goo.gl\/M4f5&quot;,&quot;amazonAlexaUrl&quot;:&quot;&quot;,&quot;pocketCastsUrl&quot;:&quot;https:\/\/pca.st\/9NRE&quot;,&quot;spotifyUrl&quot;:&quot;https:\/\/open.spotify.com\/show\/4FYpq3lSeQMAhqNI81O0Cn&quot;,&quot;brandingUrl&quot;:&quot;https:\/\/media.npr.org\/assets\/img\/2018\/08\/02\/npr_planetmoney_podcasttile_sq-7b7fab0b52fd72826936c3dbe51cff94889797a0.jpg&quot;}">
   </div>

<!-- END ID="PODCAST-CALLOUT-DATA" DATA-PODCAST="{&QUOT;NAME&QUOT;:&QUOT;PLANET MONEY&QUOT;,&QUOT;ITUNESURL&QUOT;:&QUOT;HTTPS:\/\/PODCASTS.APPLE.COM\/PODCAST\/ID290783428?MT=2&AMP;AT=11L79Y&AMP;CT=NPRDIRECTORY&QUOT;,&QUOT;GOOGLEPODCASTSURL&QUOT;:&QUOT;HTTPS:\/\/WWW.GOOGLE.COM\/PODCASTS?FEED=AHR0CHM6LY93D3CUBNBYLM9YZY9YC3MVCG9KY2FZDC5WAHA_AWQ9NTEWMJG5&QUOT;,&QUOT;RSSURL&QUOT;:&QUOT;HTTPS:\/\/FEEDS.NPR.ORG\/510289\/PODCAST.XML&QUOT;,&QUOT;JSONFEEDURL&QUOT;:&QUOT;HTTPS:\/\/FEEDS.NPR.ORG\/FEEDS\/*********\/FEED.JSON&QUOT;,&QUOT;NPRONEUNIVERSALURL&QUOT;:&QUOT;HTTPS:\/\/RPB3R.APP.GOO.GL\/M4F5&QUOT;,&QUOT;AMAZONALEXAURL&QUOT;:&QUOT;&QUOT;,&QUOT;POCKETCASTSURL&QUOT;:&QUOT;HTTPS:\/\/PCA.ST\/9NRE&QUOT;,&QUOT;SPOTIFYURL&QUOT;:&QUOT;HTTPS:\/\/OPEN.SPOTIFY.COM\/SHOW\/4FYPQ3LSEQMAHQNI81O0CN&QUOT;,&QUOT;BRANDINGURL&QUOT;:&QUOT;HTTPS:\/\/MEDIA.NPR.ORG\/ASSETS\/IMG\/2018\/08\/02\/NPR_PLANETMONEY_PODCASTTILE_SQ-7B7FAB0B52FD72826936C3DBE51CFF94889797A0.JPG&QUOT;}" -->
<div id="callout-end-of-story-mount"><!-- react-empty: 1 --></div>

<!-- END ID="CALLOUT-END-OF-STORY-MOUNT" -->
<aside id="end-of-story-recommendations-mount" class="recommended-stories" aria-label="recommended stories"><div data-reactroot=""><section class="story-recommendations"><h3 class="story-recommendations__header">More Stories From NPR</h3><div class="story-recommendations__result story-recommendations__result--related"><div style="width: 100%; height: 100%; padding: 0px; border: 0px;"><article class="recommended-story recommended-story--954153248"><a class="recommended-story__image-link" href="https://www.npr.org/2021/01/06/954153248/chaos-at-the-capitol" data-metrics="{&quot;action&quot;:&quot;click related story 1&quot;,&quot;label&quot;:&quot;954153248 - image&quot;,&quot;category&quot;:&quot;egg carton&quot;}"><img class="recommended-story__image hoverZoomLink" src="https://media.npr.org/assets/img/2021/01/06/gettyimages-1230454930_wide-78640997bcf40feb6452528bca8f88d02147af6e.jpg?s=800" loading="lazy"></a><div class="recommended-story__info"><h5 class="recommended-story__slug"><a href="https://www.npr.org/sections/elections/" data-metrics="{&quot;action&quot;:&quot;click related slug 1&quot;,&quot;label&quot;:&quot;https://www.npr.org/sections/elections/&quot;,&quot;category&quot;:&quot;egg carton&quot;}">Elections</a></h5><h4 class="recommended-story__title"><a href="https://www.npr.org/2021/01/06/954153248/chaos-at-the-capitol" data-metrics="{&quot;action&quot;:&quot;click related story 1&quot;,&quot;label&quot;:&quot;954153248 - headline&quot;,&quot;category&quot;:&quot;egg carton&quot;}">Chaos At The Capitol</a></h4></div></article></div></div><div class="story-recommendations__result story-recommendations__result--related"><div style="width: 100%; height: 100%; padding: 0px; border: 0px;"><article class="recommended-story recommended-story--951416358"><a class="recommended-story__image-link" href="https://www.npr.org/2020/12/30/951416358/the-rest-of-the-story-2020" data-metrics="{&quot;action&quot;:&quot;click related story 2&quot;,&quot;label&quot;:&quot;951416358 - image&quot;,&quot;category&quot;:&quot;egg carton&quot;}"><img class="recommended-story__image hoverZoomLink" src="https://media.npr.org/assets/img/2020/12/30/restofstory2_wide-dc64b72db59b638f1fa95fabcb8aa9783168cd51.png?s=800" loading="lazy"></a><div class="recommended-story__info"><h5 class="recommended-story__slug"><a href="https://www.npr.org/sections/economy/" data-metrics="{&quot;action&quot;:&quot;click related slug 2&quot;,&quot;label&quot;:&quot;https://www.npr.org/sections/economy/&quot;,&quot;category&quot;:&quot;egg carton&quot;}">Economy</a></h5><h4 class="recommended-story__title"><a href="https://www.npr.org/2020/12/30/951416358/the-rest-of-the-story-2020" data-metrics="{&quot;action&quot;:&quot;click related story 2&quot;,&quot;label&quot;:&quot;951416358 - headline&quot;,&quot;category&quot;:&quot;egg carton&quot;}">The Rest Of The Story, 2020</a></h4></div></article></div></div><div class="story-recommendations__result story-recommendations__result--related"><div style="width: 100%; height: 100%; padding: 0px; border: 0px;"><article class="recommended-story recommended-story--949881982"><a class="recommended-story__image-link" href="https://www.npr.org/2020/12/23/949881982/how-to-stop-an-asteroid" data-metrics="{&quot;action&quot;:&quot;click related story 3&quot;,&quot;label&quot;:&quot;949881982 - image&quot;,&quot;category&quot;:&quot;egg carton&quot;}"><img class="recommended-story__image hoverZoomLink" src="https://media.npr.org/assets/img/2020/12/23/bennu-twelve-image-mosaic_wide-5d9e4d701a53257f94f9b051f362ad71c30a5a84.png?s=800" loading="lazy"></a><div class="recommended-story__info"><h5 class="recommended-story__slug"><a href="https://www.npr.org/sections/economy/" data-metrics="{&quot;action&quot;:&quot;click related slug 3&quot;,&quot;label&quot;:&quot;https://www.npr.org/sections/economy/&quot;,&quot;category&quot;:&quot;egg carton&quot;}">Economy</a></h5><h4 class="recommended-story__title"><a href="https://www.npr.org/2020/12/23/949881982/how-to-stop-an-asteroid" data-metrics="{&quot;action&quot;:&quot;click related story 3&quot;,&quot;label&quot;:&quot;949881982 - headline&quot;,&quot;category&quot;:&quot;egg carton&quot;}">How To Stop An Asteroid</a></h4></div></article></div></div><div class="story-recommendations__result story-recommendations__result--related"><div style="width: 100%; height: 100%; padding: 0px; border: 0px;"><article class="recommended-story recommended-story--948185902"><a class="recommended-story__image-link" href="https://www.npr.org/2020/12/18/948185902/the-mixtape-drama" data-metrics="{&quot;action&quot;:&quot;click related story 4&quot;,&quot;label&quot;:&quot;948185902 - image&quot;,&quot;category&quot;:&quot;egg carton&quot;}"><img class="recommended-story__image hoverZoomLink" src="https://media.npr.org/assets/img/2020/12/18/djdrama_bugged_wide-8d93dd82cd5df28ff8cdb723f68b63f0634de652.jpg?s=800" loading="lazy"></a><div class="recommended-story__info"><h5 class="recommended-story__slug"><a href="https://www.npr.org/sections/economy/" data-metrics="{&quot;action&quot;:&quot;click related slug 4&quot;,&quot;label&quot;:&quot;https://www.npr.org/sections/economy/&quot;,&quot;category&quot;:&quot;egg carton&quot;}">Economy</a></h5><h4 class="recommended-story__title"><a href="https://www.npr.org/2020/12/18/948185902/the-mixtape-drama" data-metrics="{&quot;action&quot;:&quot;click related story 4&quot;,&quot;label&quot;:&quot;948185902 - headline&quot;,&quot;category&quot;:&quot;egg carton&quot;}">The Mixtape Drama</a></h4></div></article></div></div><div class="story-recommendations__result story-recommendations__result--related"><div style="width: 100%; height: 100%; padding: 0px; border: 0px;"><article class="recommended-story recommended-story--947160910"><a class="recommended-story__image-link" href="https://www.npr.org/2020/12/16/947160910/the-case-against-facebook" data-metrics="{&quot;action&quot;:&quot;click related story 5&quot;,&quot;label&quot;:&quot;947160910 - image&quot;,&quot;category&quot;:&quot;egg carton&quot;}"><img class="recommended-story__image hoverZoomLink" src="https://media.npr.org/assets/img/2020/12/16/gettyimages-1227830807_wide-589cf0652b1a8fa93961d65509c7c83176cdeb94.jpg?s=800" loading="lazy"></a><div class="recommended-story__info"><h5 class="recommended-story__slug"><a href="https://www.npr.org/sections/economy/" data-metrics="{&quot;action&quot;:&quot;click related slug 5&quot;,&quot;label&quot;:&quot;https://www.npr.org/sections/economy/&quot;,&quot;category&quot;:&quot;egg carton&quot;}">Economy</a></h5><h4 class="recommended-story__title"><a href="https://www.npr.org/2020/12/16/947160910/the-case-against-facebook" data-metrics="{&quot;action&quot;:&quot;click related story 5&quot;,&quot;label&quot;:&quot;947160910 - headline&quot;,&quot;category&quot;:&quot;egg carton&quot;}">The Case Against Facebook</a></h4></div></article></div></div><div class="story-recommendations__result story-recommendations__result--related"><div style="width: 100%; height: 100%; padding: 0px; border: 0px;"><article class="recommended-story recommended-story--945606413"><a class="recommended-story__image-link" href="https://www.npr.org/2020/12/11/945606413/we-buy-a-lot-of-christmas-trees" data-metrics="{&quot;action&quot;:&quot;click related story 6&quot;,&quot;label&quot;:&quot;945606413 - image&quot;,&quot;category&quot;:&quot;egg carton&quot;}"><img class="recommended-story__image hoverZoomLink" src="https://media.npr.org/assets/img/2020/12/11/image-from-ios-3-_wide-d9e29e0d2467a92579f804a0c88917bd84f0db69.jpg?s=800" loading="lazy"></a><div class="recommended-story__info"><h5 class="recommended-story__slug"><a href="https://www.npr.org/sections/economy/" data-metrics="{&quot;action&quot;:&quot;click related slug 6&quot;,&quot;label&quot;:&quot;https://www.npr.org/sections/economy/&quot;,&quot;category&quot;:&quot;egg carton&quot;}">Economy</a></h5><h4 class="recommended-story__title"><a href="https://www.npr.org/2020/12/11/945606413/we-buy-a-lot-of-christmas-trees" data-metrics="{&quot;action&quot;:&quot;click related story 6&quot;,&quot;label&quot;:&quot;945606413 - headline&quot;,&quot;category&quot;:&quot;egg carton&quot;}">We Buy A Lot Of Christmas Trees</a></h4></div></article></div></div></section><section class="story-recommendations"><h3 class="story-recommendations__header">Popular on NPR.org</h3><div class="story-recommendations__result story-recommendations__result--popular"><div style="width: 100%; height: 100%; padding: 0px; border: 0px;"><article class="recommended-story recommended-story--954028436"><a class="recommended-story__image-link" href="https://www.npr.org/sections/congress-electoral-college-tally-live-updates/2021/01/06/954028436/u-s-capitol-locked-down-amid-escalating-far-right-protests" data-metrics="{&quot;action&quot;:&quot;click popular story 1&quot;,&quot;label&quot;:&quot;954028436 - image&quot;,&quot;category&quot;:&quot;egg carton&quot;}"><img class="recommended-story__image hoverZoomLink" src="https://media.npr.org/assets/img/2021/01/06/gettyimages-1230455089_wide-894db8d9df5d7ef55a206b08b2b50fdfa5da87e4.jpg?s=800" loading="lazy"></a><div class="recommended-story__info"><h5 class="recommended-story__slug"><a href="https://www.npr.org/sections/elections/" data-metrics="{&quot;action&quot;:&quot;click popular slug 1&quot;,&quot;label&quot;:&quot;https://www.npr.org/sections/elections/&quot;,&quot;category&quot;:&quot;egg carton&quot;}">Elections</a></h5><h4 class="recommended-story__title"><a href="https://www.npr.org/sections/congress-electoral-college-tally-live-updates/2021/01/06/954028436/u-s-capitol-locked-down-amid-escalating-far-right-protests" data-metrics="{&quot;action&quot;:&quot;click popular story 1&quot;,&quot;label&quot;:&quot;954028436 - headline&quot;,&quot;category&quot;:&quot;egg carton&quot;}">Congress Reconvenes After Pro-Trump Mob Brings Chaos To The Capitol</a></h4></div></article></div></div><div class="story-recommendations__result story-recommendations__result--popular"><div style="width: 100%; height: 100%; padding: 0px; border: 0px;"><article class="recommended-story recommended-story--952417689"><a class="recommended-story__image-link" href="https://www.npr.org/2021/01/06/952417689/democrat-jon-ossoff-claims-victory-over-david-perdue-in-georgia-runoff" data-metrics="{&quot;action&quot;:&quot;click popular story 2&quot;,&quot;label&quot;:&quot;952417689 - image&quot;,&quot;category&quot;:&quot;egg carton&quot;}"><img class="recommended-story__image hoverZoomLink" src="https://media.npr.org/assets/img/2021/01/04/gettyimages-1230426512_wide-4578cafa1604827f3d6e1b37258854cdb1f7583d.jpg?s=800" loading="lazy"></a><div class="recommended-story__info"><h5 class="recommended-story__slug"><a href="https://www.npr.org/sections/elections/" data-metrics="{&quot;action&quot;:&quot;click popular slug 2&quot;,&quot;label&quot;:&quot;https://www.npr.org/sections/elections/&quot;,&quot;category&quot;:&quot;egg carton&quot;}">Elections</a></h5><h4 class="recommended-story__title"><a href="https://www.npr.org/2021/01/06/952417689/democrat-jon-ossoff-claims-victory-over-david-perdue-in-georgia-runoff" data-metrics="{&quot;action&quot;:&quot;click popular story 2&quot;,&quot;label&quot;:&quot;952417689 - headline&quot;,&quot;category&quot;:&quot;egg carton&quot;}">Jon Ossoff Wins Georgia Runoff, Handing Democrats Senate Control</a></h4></div></article></div></div><div class="story-recommendations__result story-recommendations__result--popular"><div style="width: 100%; height: 100%; padding: 0px; border: 0px;"><article class="recommended-story recommended-story--953616207"><a class="recommended-story__image-link" href="https://www.npr.org/sections/congress-electoral-college-tally-live-updates/2021/01/06/953616207/diehard-trump-supporters-gather-in-the-nations-capital-to-protest-election-resul" data-metrics="{&quot;action&quot;:&quot;click popular story 3&quot;,&quot;label&quot;:&quot;953616207 - image&quot;,&quot;category&quot;:&quot;egg carton&quot;}"><img class="recommended-story__image hoverZoomLink" src="https://media.npr.org/assets/img/2021/01/06/gettyimages-1230454991_wide-0a4e9a63f4c5d75c98fb426c57fe19d18966528f.jpg?s=800" loading="lazy"></a><div class="recommended-story__info"><h5 class="recommended-story__slug"><a href="https://www.npr.org/sections/politics/" data-metrics="{&quot;action&quot;:&quot;click popular slug 3&quot;,&quot;label&quot;:&quot;https://www.npr.org/sections/politics/&quot;,&quot;category&quot;:&quot;egg carton&quot;}">Politics</a></h5><h4 class="recommended-story__title"><a href="https://www.npr.org/sections/congress-electoral-college-tally-live-updates/2021/01/06/953616207/diehard-trump-supporters-gather-in-the-nations-capital-to-protest-election-resul" data-metrics="{&quot;action&quot;:&quot;click popular story 3&quot;,&quot;label&quot;:&quot;953616207 - headline&quot;,&quot;category&quot;:&quot;egg carton&quot;}">Trump Supporters Storm U.S. Capitol, Clash With Police</a></h4></div></article></div></div><div class="story-recommendations__result story-recommendations__result--popular"><div style="width: 100%; height: 100%; padding: 0px; border: 0px;"><article class="recommended-story recommended-story--953245235"><a class="recommended-story__image-link" href="https://www.npr.org/2021/01/05/953245235/georgia-senate-runoffs-to-determine-which-party-controls-chamber" data-metrics="{&quot;action&quot;:&quot;click popular story 4&quot;,&quot;label&quot;:&quot;953245235 - image&quot;,&quot;category&quot;:&quot;egg carton&quot;}"><img class="recommended-story__image hoverZoomLink" src="https://media.npr.org/assets/img/2021/01/05/ap_21005554187753_wide-e303d742e05133b5fb04cec4f84eb8b894032f57.jpg?s=800" loading="lazy"></a><div class="recommended-story__info"><h5 class="recommended-story__slug"><a href="https://www.npr.org/sections/politics/" data-metrics="{&quot;action&quot;:&quot;click popular slug 4&quot;,&quot;label&quot;:&quot;https://www.npr.org/sections/politics/&quot;,&quot;category&quot;:&quot;egg carton&quot;}">Politics</a></h5><h4 class="recommended-story__title"><a href="https://www.npr.org/2021/01/05/953245235/georgia-senate-runoffs-to-determine-which-party-controls-chamber" data-metrics="{&quot;action&quot;:&quot;click popular story 4&quot;,&quot;label&quot;:&quot;953245235 - headline&quot;,&quot;category&quot;:&quot;egg carton&quot;}">Democrats Win 1 Of 2 Georgia Races, With Senate Control In Sight</a></h4></div></article></div></div><div class="story-recommendations__result story-recommendations__result--popular"><div style="width: 100%; height: 100%; padding: 0px; border: 0px;"><article class="recommended-story recommended-story--953712195"><a class="recommended-story__image-link" href="https://www.npr.org/2021/01/06/953712195/democrats-move-closer-to-senate-control-as-counting-continues-in-georgia" data-metrics="{&quot;action&quot;:&quot;click popular story 5&quot;,&quot;label&quot;:&quot;953712195 - image&quot;,&quot;category&quot;:&quot;egg carton&quot;}"><img class="recommended-story__image hoverZoomLink" src="https://media.npr.org/assets/img/2021/01/06/ap_20307659243297_wide-6648c294dba23afe6e46404a79fafcd062f5fc50.jpg?s=800" loading="lazy"></a><div class="recommended-story__info"><h5 class="recommended-story__slug"><a href="https://www.npr.org/sections/politics/" data-metrics="{&quot;action&quot;:&quot;click popular slug 5&quot;,&quot;label&quot;:&quot;https://www.npr.org/sections/politics/&quot;,&quot;category&quot;:&quot;egg carton&quot;}">Politics</a></h5><h4 class="recommended-story__title"><a href="https://www.npr.org/2021/01/06/953712195/democrats-move-closer-to-senate-control-as-counting-continues-in-georgia" data-metrics="{&quot;action&quot;:&quot;click popular story 5&quot;,&quot;label&quot;:&quot;953712195 - headline&quot;,&quot;category&quot;:&quot;egg carton&quot;}">Democrats Take Control Of Senate With Twin Georgia Victories</a></h4></div></article></div></div><div class="story-recommendations__result story-recommendations__result--popular"><div style="width: 100%; height: 100%; padding: 0px; border: 0px;"><article class="recommended-story recommended-story--954164654"><a class="recommended-story__image-link" href="https://www.npr.org/sections/congress-electoral-college-tally-live-updates/2021/01/06/954164654/congress-reconvenes-after-violent-rioters-breach-u-s-capitol" data-metrics="{&quot;action&quot;:&quot;click popular story 6&quot;,&quot;label&quot;:&quot;954164654 - image&quot;,&quot;category&quot;:&quot;egg carton&quot;}"><img class="recommended-story__image hoverZoomLink" src="https://media.npr.org/assets/img/2021/01/06/gettyimages-1230457148_wide-720c51aaff43913468aa064067b5d52456c623b0.jpg?s=800" loading="lazy"></a><div class="recommended-story__info"><h5 class="recommended-story__slug"><a href="https://www.npr.org/sections/congress-electoral-college-tally-live-updates" data-metrics="{&quot;action&quot;:&quot;click popular slug 6&quot;,&quot;label&quot;:&quot;https://www.npr.org/sections/congress-electoral-college-tally-live-updates&quot;,&quot;category&quot;:&quot;egg carton&quot;}">Insurrection At The Capitol: Live Updates</a></h5><h4 class="recommended-story__title"><a href="https://www.npr.org/sections/congress-electoral-college-tally-live-updates/2021/01/06/954164654/congress-reconvenes-after-violent-rioters-breach-u-s-capitol" data-metrics="{&quot;action&quot;:&quot;click popular story 6&quot;,&quot;label&quot;:&quot;954164654 - headline&quot;,&quot;category&quot;:&quot;egg carton&quot;}">'Will Not Be Intimidated': After Insurrection, Congress Blocks State Objections </a></h4></div></article></div></div></section><section class="story-recommendations"><h3 class="story-recommendations__header">NPR Editors' Picks</h3><div class="story-recommendations__result story-recommendations__result--editor"><div style="width: 100%; height: 100%; padding: 0px; border: 0px;"><article class="recommended-story recommended-story--954438077"><a class="recommended-story__image-link" href="https://www.npr.org/sections/congress-electoral-college-tally-live-updates/2021/01/07/954438077/top-senate-democrat-calls-for-trumps-removal-from-office" data-metrics="{&quot;action&quot;:&quot;click editor story 1&quot;,&quot;label&quot;:&quot;954438077 - image&quot;,&quot;category&quot;:&quot;egg carton&quot;}"><img class="recommended-story__image hoverZoomLink" src="https://media.npr.org/assets/img/2021/01/07/gettyimages-1230449859_wide-27c439d022d7b612a89cc380762ef35bde851e55.jpg?s=800" loading="lazy"></a><div class="recommended-story__info"><h5 class="recommended-story__slug"><a href="https://www.npr.org/sections/politics/" data-metrics="{&quot;action&quot;:&quot;click editor slug 1&quot;,&quot;label&quot;:&quot;https://www.npr.org/sections/politics/&quot;,&quot;category&quot;:&quot;egg carton&quot;}">Politics</a></h5><h4 class="recommended-story__title"><a href="https://www.npr.org/sections/congress-electoral-college-tally-live-updates/2021/01/07/954438077/top-senate-democrat-calls-for-trumps-removal-from-office" data-metrics="{&quot;action&quot;:&quot;click editor story 1&quot;,&quot;label&quot;:&quot;954438077 - headline&quot;,&quot;category&quot;:&quot;egg carton&quot;}">Senate Democratic Leader Chuck Schumer Calls For Trump's Removal From Office</a></h4></div></article></div></div><div class="story-recommendations__result story-recommendations__result--editor"><div style="width: 100%; height: 100%; padding: 0px; border: 0px;"><article class="recommended-story recommended-story--954495552"><a class="recommended-story__image-link" href="https://www.npr.org/sections/congress-electoral-college-tally-live-updates/2021/01/07/954495552/transportation-secretary-elaine-chao-to-resign-citing-violence-at-capitol" data-metrics="{&quot;action&quot;:&quot;click editor story 2&quot;,&quot;label&quot;:&quot;954495552 - image&quot;,&quot;category&quot;:&quot;egg carton&quot;}"><img class="recommended-story__image hoverZoomLink" src="https://media.npr.org/assets/img/2021/01/07/ap_20184644300308_wide-89a7402c0552d6b2af9057e2cbe781529620583d.jpg?s=800" loading="lazy"></a><div class="recommended-story__info"><h5 class="recommended-story__slug"><a href="https://www.npr.org/sections/congress-electoral-college-tally-live-updates" data-metrics="{&quot;action&quot;:&quot;click editor slug 2&quot;,&quot;label&quot;:&quot;https://www.npr.org/sections/congress-electoral-college-tally-live-updates&quot;,&quot;category&quot;:&quot;egg carton&quot;}">Insurrection At The Capitol: Live Updates</a></h5><h4 class="recommended-story__title"><a href="https://www.npr.org/sections/congress-electoral-college-tally-live-updates/2021/01/07/954495552/transportation-secretary-elaine-chao-to-resign-citing-violence-at-capitol" data-metrics="{&quot;action&quot;:&quot;click editor story 2&quot;,&quot;label&quot;:&quot;954495552 - headline&quot;,&quot;category&quot;:&quot;egg carton&quot;}">Transportation Secretary Elaine Chao Resigns, Citing Violence At Capitol</a></h4></div></article></div></div><div class="story-recommendations__result story-recommendations__result--editor"><div style="width: 100%; height: 100%; padding: 0px; border: 0px;"><article class="recommended-story recommended-story--954453630"><a class="recommended-story__image-link" href="https://www.npr.org/2021/01/07/954453630/facebook-bans-president-trump-from-posting-for-the-rest-of-his-presidency" data-metrics="{&quot;action&quot;:&quot;click editor story 3&quot;,&quot;label&quot;:&quot;954453630 - image&quot;,&quot;category&quot;:&quot;egg carton&quot;}"><img class="recommended-story__image hoverZoomLink" src="https://media.npr.org/assets/img/2021/01/07/ap_19296520174133_wide-311fad337a606e6e59cdae00f3a9723097486096.jpg?s=800" loading="lazy"></a><div class="recommended-story__info"><h5 class="recommended-story__slug"><a href="https://www.npr.org/sections/technology/" data-metrics="{&quot;action&quot;:&quot;click editor slug 3&quot;,&quot;label&quot;:&quot;https://www.npr.org/sections/technology/&quot;,&quot;category&quot;:&quot;egg carton&quot;}">Technology</a></h5><h4 class="recommended-story__title"><a href="https://www.npr.org/2021/01/07/954453630/facebook-bans-president-trump-from-posting-for-the-rest-of-his-presidency" data-metrics="{&quot;action&quot;:&quot;click editor story 3&quot;,&quot;label&quot;:&quot;954453630 - headline&quot;,&quot;category&quot;:&quot;egg carton&quot;}">Facebook Bans President Trump From Posting For The Rest Of His Presidency </a></h4></div></article></div></div><div class="story-recommendations__result story-recommendations__result--editor"><div style="width: 100%; height: 100%; padding: 0px; border: 0px;"><article class="recommended-story recommended-story--954410419"><a class="recommended-story__image-link" href="https://www.npr.org/sections/congress-electoral-college-tally-live-updates/2021/01/07/954410419/how-the-u-s-capitol-mob-was-treated-differently-than-earlier-black-protesters" data-metrics="{&quot;action&quot;:&quot;click editor story 4&quot;,&quot;label&quot;:&quot;954410419 - image&quot;,&quot;category&quot;:&quot;egg carton&quot;}"><img class="recommended-story__image hoverZoomLink" src="https://media.npr.org/assets/img/2021/01/07/gettyimages-1230456070_wide-1ffe57c2ca792c9eadf510125c7907366b318ef2.jpg?s=800" loading="lazy"></a><div class="recommended-story__info"><h5 class="recommended-story__slug"><a href="https://www.npr.org/sections/race/" data-metrics="{&quot;action&quot;:&quot;click editor slug 4&quot;,&quot;label&quot;:&quot;https://www.npr.org/sections/race/&quot;,&quot;category&quot;:&quot;egg carton&quot;}">Race</a></h5><h4 class="recommended-story__title"><a href="https://www.npr.org/sections/congress-electoral-college-tally-live-updates/2021/01/07/954410419/how-the-u-s-capitol-mob-was-treated-differently-than-earlier-black-protesters" data-metrics="{&quot;action&quot;:&quot;click editor story 4&quot;,&quot;label&quot;:&quot;954410419 - headline&quot;,&quot;category&quot;:&quot;egg carton&quot;}">How Police Handled Pro-Trump Mob Compared With Protesters For Black Racial Justice</a></h4></div></article></div></div><div class="story-recommendations__result story-recommendations__result--editor"><div style="width: 100%; height: 100%; padding: 0px; border: 0px;"><article class="recommended-story recommended-story--954446008"><a class="recommended-story__image-link" href="https://www.npr.org/sections/congress-electoral-college-tally-live-updates/2021/01/07/954446008/authorities-identify-woman-killed-by-police-during-u-s-capitol-rioting" data-metrics="{&quot;action&quot;:&quot;click editor story 5&quot;,&quot;label&quot;:&quot;954446008 - image&quot;,&quot;category&quot;:&quot;egg carton&quot;}"><img class="recommended-story__image hoverZoomLink" src="https://media.npr.org/assets/img/2021/01/07/erkvwmiucaayrf1_wide-1bf593d2e4ecc104dd04d9554153c3088dabc250.jpeg?s=800" loading="lazy"></a><div class="recommended-story__info"><h5 class="recommended-story__slug"><a href="https://www.npr.org/sections/national/" data-metrics="{&quot;action&quot;:&quot;click editor slug 5&quot;,&quot;label&quot;:&quot;https://www.npr.org/sections/national/&quot;,&quot;category&quot;:&quot;egg carton&quot;}">National</a></h5><h4 class="recommended-story__title"><a href="https://www.npr.org/sections/congress-electoral-college-tally-live-updates/2021/01/07/954446008/authorities-identify-woman-killed-by-police-during-u-s-capitol-rioting" data-metrics="{&quot;action&quot;:&quot;click editor story 5&quot;,&quot;label&quot;:&quot;954446008 - headline&quot;,&quot;category&quot;:&quot;egg carton&quot;}">Authorities Identify Woman Killed By Police During U.S. Capitol Rioting</a></h4></div></article></div></div><div class="story-recommendations__result story-recommendations__result--editor"><div style="width: 100%; height: 100%; padding: 0px; border: 0px;"><article class="recommended-story recommended-story--954349992"><a class="recommended-story__image-link" href="https://www.npr.org/sections/congress-electoral-college-tally-live-updates/2021/01/07/954349992/where-was-the-security-when-a-mob-stormed-the-capitol" data-metrics="{&quot;action&quot;:&quot;click editor story 6&quot;,&quot;label&quot;:&quot;954349992 - image&quot;,&quot;category&quot;:&quot;egg carton&quot;}"><img class="recommended-story__image hoverZoomLink" src="https://media.npr.org/assets/img/2021/01/07/gettyimages-1230455611_wide-41e9bbceb46f9fec653ee16a90f2760a3644fec2.jpg?s=800" loading="lazy"></a><div class="recommended-story__info"><h5 class="recommended-story__slug"><a href="https://www.npr.org/sections/national-security/" data-metrics="{&quot;action&quot;:&quot;click editor slug 6&quot;,&quot;label&quot;:&quot;https://www.npr.org/sections/national-security/&quot;,&quot;category&quot;:&quot;egg carton&quot;}">National Security</a></h5><h4 class="recommended-story__title"><a href="https://www.npr.org/sections/congress-electoral-college-tally-live-updates/2021/01/07/954349992/where-was-the-security-when-a-mob-stormed-the-capitol" data-metrics="{&quot;action&quot;:&quot;click editor story 6&quot;,&quot;label&quot;:&quot;954349992 - headline&quot;,&quot;category&quot;:&quot;egg carton&quot;}">Where Was Security When A Pro-Trump Mob Stormed The Capitol?</a></h4></div></article></div></div></section></div></aside>
<!-- END ID="END-OF-STORY-RECOMMENDATIONS-MOUNT" CLASS="RECOMMENDED-STORIES" ARIA-LABEL="RECOMMENDED STORIES" -->
</article><header class="contentheader contentheader--two" data-metrics="{&quot;category&quot;:&quot;Fork The Government&quot;}"></header><footer id="npr-footer" class="npr-footer" role="contentinfo" aria-label="NPR footer"><div class="npr-footer__content" data-metrics-category="Global Footer" data-metrics-label="Footer">
    <div class="npr-footer__module-group">
        <div class="npr-footer__module">
            <h6 class="npr-footer__header">Read &amp; Listen</h6>

            <ul>
                <li class="npr-footer__item"><a href="/" data-metrics-action="Click Homepage">Home</a></li>
                <li class="npr-footer__item"><a href="/sections/news/" data-metrics-action="Click News">News</a></li>
                <li class="npr-footer__item"><a href="/sections/arts/" data-metrics-action="Click Arts &amp; Life">Arts &amp; Life</a></li>
                <li class="npr-footer__item"><a href="/music/" data-metrics-action="Click Music">Music</a></li>
                <li class="npr-footer__item"><a href="/podcasts/" data-metrics-action="Click Podcasts">Podcasts</a></li>
                <li class="npr-footer__item"><a href="/programs/" data-metrics-action="Click Programs">Programs</a></li>
            </ul>
        </div>

        <div class="npr-footer__module">
            <h6 class="npr-footer__header">Connect</h6>

            <ul>
                <li class="npr-footer__item"><a href="/newsletters/" data-metrics-action="Click Newsletters">Newsletters</a></li>
                <li class="npr-footer__item"><a href="https://www.facebook.com/NPR/" data-metrics-action="Click Facebook">Facebook</a></li>
                <li class="npr-footer__item"><a href="https://twitter.com/NPR" data-metrics-action="Click Twitter">Twitter</a></li>
                <li class="npr-footer__item"><a href="https://www.instagram.com/npr/" data-metrics-action="Click Instagram">Instagram</a></li>
                <li class="npr-footer__item"><a href="https://www.npr.org/contact" data-metrics-action="Click Contact">Contact</a></li>
                <li class="npr-footer__item"><a href="https://help.npr.org" data-metrics-action="Click Help">Help</a></li>
            </ul>
        </div>

        <div class="npr-footer__module">
            <h6 class="npr-footer__header">About NPR</h6>

            <ul>
                <li class="npr-footer__item"><a href="/about/" data-metrics-action="Click Overview">Overview</a></li>
                <li class="npr-footer__item"><a href="/about-npr/178660742/public-radio-finances" data-metrics-action="Click Finances">Finances</a></li>
                <li class="npr-footer__item"><a href="/about-npr/179803822/people-at-npr" data-metrics-action="Click People">People</a></li>
                <li class="npr-footer__item"><a href="/press/" data-metrics-action="Click Press">Press</a></li>
                <li class="npr-footer__item"><a href="/sections/publiceditor/" data-metrics-action="Click Public Editor">Public Editor</a></li>
                <li class="npr-footer__item"><a href="/corrections/" data-metrics-action="Click Corrections">Corrections</a></li>
            </ul>
        </div>

        <div class="npr-footer__module">
            <h6 class="npr-footer__header">Get Involved</h6>

            <ul>
                <li class="npr-footer__item"><a href="/support/" data-metrics-action="Click Support Public Radio">Support Public Radio</a></li>
                <li class="npr-footer__item"><a href="/about-npr/186948703/corporate-sponsorship" data-metrics-action="Click Sponsor NPR">Sponsor NPR</a></li>
                <li class="npr-footer__item"><a href="/careers/" data-metrics-action="Click NPR Careers">NPR Careers</a></li>
                <li class="npr-footer__item"><a href="https://shop.npr.org/" data-metrics-action="Click NPR Shop">NPR Shop</a></li>
                <li class="npr-footer__item"><a href="https://www.nprpresents.org" data-metrics-action="Click NPR Events">NPR Events</a></li>
                <li class="npr-footer__item"><a href="/about-npr/177066727/visit-npr" data-metrics-action="Click Visit NPR">Visit NPR</a></li>
            </ul>
        </div>
    </div>

    <div class="npr-footer__module npr-footer__module--secondary">
        <ul>
            <li class="npr-footer__item"><a href="/about-npr/179876898/terms-of-use" data-metrics-action="Click Terms of Use">Terms of Use</a></li>
            <li class="npr-footer__item"><a href="/about-npr/179878450/privacy-policy" data-metrics-action="Click Privacy">Privacy</a></li>
            <li class="npr-footer__item"><a href="/about-npr/179878450/privacy-policy#yourchoices" data-metrics-action="Click Your Privacy Choices">Your Privacy Choices</a></li>
            <li class="npr-footer__item"><a href="https://text.npr.org/" data-metrics-action="Click Text Only">Text Only</a></li>
            <li class="npr-footer__item"><span class="copy-year">© 2021 npr</span></li>
        </ul>
    </div>
</div></footer><div id="global-stickybar-mount" class="global-stickybar-mount"><span data-reactroot=""></span></div>
</section>   <div id="main-sidebar">
            <aside id="ad-standard-wrap" aria-label="advertisement">


    <div id="ad-standard-podcasts_Planet_Money" class="ad-standard" data-ad-config="{&quot;network&quot;:&quot;\/6735\/&quot;,&quot;site&quot;:{&quot;default&quot;:&quot;n6735.NPR&quot;,&quot;mobile&quot;:&quot;n6735.NPRMOBILE&quot;,&quot;sponsor_content&quot;:&quot;npr_sponsor_content&quot;,&quot;default_secondary&quot;:&quot;NPRSecondary&quot;,&quot;mobile_secondary&quot;:&quot;NPRMobileSecondary&quot;},&quot;zone&quot;:&quot;podcasts_Planet_Money&quot;,&quot;targets&quot;:{&quot;testserver&quot;:&quot;false&quot;,&quot;isPodcastEpisode&quot;:&quot;true&quot;,&quot;storyId&quot;:&quot;*********&quot;,&quot;blog&quot;:&quot;********&quot;,&quot;orgId&quot;:&quot;1&quot;},&quot;location&quot;:&quot;standard&quot;,&quot;deferred&quot;:false,&quot;isBetweenContent&quot;:false,&quot;isAggSponsorship&quot;:false,&quot;borderClass&quot;:&quot;&quot;}"></div>
    <p class="left">NPR thanks our sponsors</p><p class="right"><a href="/about-npr/186948703/corporate-sponsorship">Become an NPR sponsor</a></p>

</aside>
   </div>

<!-- END ID="MAIN-SIDEBAR" -->
</div>

<!-- END ID="WRAPPER" -->
<section id="global-modal-mount"><!-- react-empty: 1 --></section>
<!-- END ID="GLOBAL-MODAL-MOUNT" -->
<script type="text/javascript">
   (function () {
    var loadPageJs = function () { webpackJsonp([77,23],{0:function(n,t,c){n.exports=c(1924)},1924:function(n,t,c){"use strict";c.p=NPR.serverVars.webpackPublicPath,function(){var n=2,t=function(){--n<1&&function(n){c(41),c(2325),c(272),c(384),c(217),c(1512),c(653),c(390),c(2330),c(389),c(2329),c(2328),c(443),c(2323),c(1662),c(2316),c(262)}(c)}.bind(this);c.e(0,t),c.e(3,t)}.call(this)}}); };
    if (document.readyState === 'complete') {
        loadPageJs();
    } else {
        window.addEventListener('load', function load() {
            window.removeEventListener('load', load, false);
            loadPageJs();
        });
    }
})();
</script>
</main><noscript><iframe><img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=1621557368158968&ev=PageView&noscript=1" /></iframe></noscript><script type="text/javascript">
    var _sf_async_config = _sf_async_config || {};
    /** CONFIGURATION START **/
    _sf_async_config.uid = '18888';
    _sf_async_config.domain = NPR.ServerConstants.cbHost;
    _sf_async_config.useCanonical = true;

    /** CONFIGURATION END **/
    (function() {
        function loadChartbeat() {
            // Wait until the metric for chartbeat have been setup
            // in templates/javascript/metrics/chartbeat.js
            if( !!NPR.ChartbeatLoaded) {
                var e = document.createElement('script');
                e.setAttribute('language', 'javascript');
                e.setAttribute('type', 'text/javascript');
                e.setAttribute('src', 'https://static.chartbeat.com/js/chartbeat.js');
                document.body.appendChild(e);
            } else {
                setTimeout(loadChartbeat, 1000);
            }
        }

        var oldonload = window.onload;
        window.onload = (typeof window.onload != 'function') ?
                loadChartbeat : function() {
            oldonload();
            window._sf_endpt = (new Date()).getTime();
            loadChartbeat();
        };
    })();
</script><iframe frameborder="0" allowtransparency="true" scrolling="no" name="__privateStripeMetricsController2530" allowpaymentrequest="true" src="https://js.stripe.com/v3/m-outer-59cdd15d8db95826a41100f00b589171.html#url=https%3A%2F%2Fwww.npr.org%2F2020%2F12%2F23%2F*********%2Ffork-the-government&amp;title=Audrey%20Tang%20brings%20civic%20tech%20to%20Taiwan's%20coronavirus%20pandemic%20response%20%3A%20Planet%20Money%20%3A%20NPR&amp;referrer=&amp;muid=211ecfcb-3b59-43fc-a37f-c08e636240b4eeb418&amp;sid=ddaf8dde-7244-402a-a283-5e6accc3e2baa47992&amp;version=6&amp;preview=false" aria-hidden="true" tabindex="-1" style="border: none !important; margin: 0px !important; padding: 0px !important; width: 1px !important; min-width: 100% !important; overflow: hidden !important; display: block !important; visibility: hidden !important; position: fixed !important; height: 1px !important; pointer-events: none !important; user-select: none !important;"></iframe><div id="loading-bar" style="width: 100%; height: 4px; z-index: 9999; top: 0px; float: left; position: fixed;"><div style="background-color: rgb(109, 138, 196); width: 0px; height: 100%; clear: both; transition: height 0.3s ease 0s; float: left;"></div></div><script language="javascript" type="text/javascript" src="https://static.chartbeat.com/js/chartbeat.js"></script></body></html>
