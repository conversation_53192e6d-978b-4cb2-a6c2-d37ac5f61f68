# Snapshot report for `test/integration/reuters/index.js`

The actual snapshot is saved in `index.js.snap`.

Generated by [AVA](https://avajs.dev).

## reuters

> Snapshot 1

    {
      audio: null,
      author: '<PERSON>',
      date: '2016-05-13T16:43:53.000Z',
      description: 'Venture capitalists are raising money at the fastest rate in a decade, raking in about $13 billion in the first quarter of 2016.',
      image: 'http://s4.reutersmedia.net/resources/r/?m=02&d=20160513&t=2&i=1137169522&w=&fh=545px&fw=&ll=&pl=&sq=&r=LYNXNPEC4C0SU',
      lang: 'en',
      logo: 'https://www.reuters.com/favicon.ico',
      publisher: 'Reuters',
      title: 'Silicon Valley venture capitalists raise more money, give less away',
      url: 'http://www.reuters.com/article/us-venture-fundraising-idUSKCN0Y41DQ',
      video: null,
    }
