# Snapshot report for `test/integration/cnet/index.js`

The actual snapshot is saved in `index.js.snap`.

Generated by [AVA](https://avajs.dev).

## cnet

> Snapshot 1

    {
      audio: null,
      author: '<PERSON>',
      date: '2016-05-24T14:00:00.000Z',
      description: 'Pebble’s latest wearables are trying harder than ever to nail fitness better, but one is aiming at being a new type of smart button.',
      image: 'https://www.cnet.com/a/img/o7o1R0SgMd_Zf4mL0U0ICpR5x2o=/1200x630/2016/05/23/ee9a228a-d1d9-40b3-b401-4ba0e07e2fce/pebblecore3.jpg',
      lang: 'en',
      logo: 'https://www.cnet.com/a/fly/bundles/cnetcss/images/core/redball/logo_310_white.png',
      publisher: 'CNET',
      title: 'Pebble 2 watch adds heart rate, and the Spotify-enabled 3G GPS Pebble Core isn’t a watch at all',
      url: 'https://www.cnet.com/tech/mobile/pebble-2-pebble-time-2-pebble-core-announced-coming-this-year-and-2017/',
      video: null,
    }
