<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">

  <title>Segment Launches Sources to Unify Siloed Customer Data in</title>



  <!-- Search Engine Friendly Metadata  -->
  <meta name="author" content="Segment">
  <meta name="keywords" content="Segment">
  <meta name="description" content="
  SAN FRANCISCO, CA--(Marketwired - April 06, 2016) - &nbsp;Segment, the customer data platform, today released Sources, giving companies access to new types...">
  <meta name="title" content="Segment Launches Sources to Unify Siloed Customer Data in Minutes">
  <meta name="ticker" content="">
  <meta name="DC.date.issued" content="4/6/2016">
  <!-- Google site verification meta tag -->
  <meta name="google-site-verification" content="TPh-fYpDjXZUz98ciWasVb52qbvctqomC6zZc8vuUPU">
  <!-- Google Syndication source  -->
  <link name="syndication-source" href="https://www.globenewswire.com/news-release/2016/04/06/1300478/0/en/Segment-Launches-Sources-to-Unify-Siloed-Customer-Data-in-Minutes.html">
  <meta name="original-source" content="https://www.globenewswire.com/news-release/2016/04/06/1300478/0/en/Segment-Launches-Sources-to-Unify-Siloed-Customer-Data-in-Minutes.html">
  <!-- Twitter Cards -->
  <meta name="twitter:card" content="summary">
  <meta name="twitter:site" content="globenewswire">
  <meta name="twitter:title" content="Segment Launches Sources to Unify Siloed Customer Data in Minutes">
  <meta name="twitter:description" content="
  SAN FRANCISCO, CA--(Marketwired - April 06, 2016) - &nbsp;Segment, the customer data platform, today released Sources, giving companies access to new types...">
  <!-- <meta name="twitter:creator" content="??????" />  -->
  <!-- Open Graph-->
  <meta property="og:title" content="Segment Launches Sources to Unify Siloed Customer Data in Minutes">
  <meta property="og:type" content="article">

  <meta name="twitter:image" content="/news-release/logo/901814/0/901814.png?lastModified=09%2F16%2F2020%2010%3A00%3A19&amp;v=1300478">
  <meta property="og:image" content="/news-release/logo/901814/0/901814.png?lastModified=09%2F16%2F2020%2010%3A00%3A19&amp;v=1300478">

  <meta property="og:url" content="https://www.globenewswire.com/news-release/2016/04/06/1300478/0/en/Segment-Launches-Sources-to-Unify-Siloed-Customer-Data-in-Minutes.html">
  <meta property="og:description" content="
  SAN FRANCISCO, CA--(Marketwired - April 06, 2016) - &nbsp;Segment, the customer data platform, today released Sources, giving companies access to new types...">
  <meta property="og:article:published_time" content="4/6/2016 4:00:00 PM">
  <meta property="og:article:author " content="Segment">
  <meta property="og:article:tag" content="Segment, ">
  <meta property="og:locale" content="en_US">
  <meta property="og:site_name" content="GlobeNewswire News Room">


  <link rel="shortcut icon" href="/Content/logo/favicon.ico" type="image/x-icon">

  <link rel="preload" href="/content/fonts/font-montserrat/JTUQjIg1_i6t8kCHKm459WxRyS7m.woff2" as="font" type="font/woff2" crossorigin="">

  <link rel="preload" href="/content/fonts/font-montserrat/JTUSjIg1_i6t8kCHKm459Wlhyw.woff2" as="font" type="font/woff2" crossorigin="">
  <link rel="stylesheet" as="style" href="/Content/css/bootstrap.min.css" onload="this.rel='stylesheet'">
  <link rel="stylesheet" as="style" href="/bundles/pnr-global-styles?v=UMJs9YSLMKjYWEIL98dnPh9ySR6ERYAOxkI-Yo_sFoY1" onload="this.rel='stylesheet'">
  <link rel="stylesheet" as="style" href="/bundles/react-styles?v=_GKYAKRAdQLkKxUgiBI4KrCsjj3U6bLRCRCiNNm1Sf81" onload="this.rel='stylesheet'">
  <link href="/bundles/react-styles?v=_GKYAKRAdQLkKxUgiBI4KrCsjj3U6bLRCRCiNNm1Sf81" rel="stylesheet">


  <script src="/Scripts/stickyfill.min.js" defer="" async=""></script>
  <link rel="canonical" href="https://www.globenewswire.com/news-release/2016/04/06/1300478/0/en/Segment-Launches-Sources-to-Unify-Siloed-Customer-Data-in-Minutes.html">
  <script type="text/javascript" defer="" src="/bundles/layout-jquery-scripts?v=-OGLMVWxIPR3zeOU27ZatCn90l4Fd6xEKfsznyki1xw1"></script>
  <link rel="preload" href="/bundles/react-scripts?v=SJ2ohADMpR7urxLR3P8d_6aOG9XEc0AKMcilpo8CPsY1" as="script">

  <script type="text/javascript">
    window.enableInlineImageZoom = false;
    var fsEnableInlineImageZoom = 'True';
    if (fsEnableInlineImageZoom && fsEnableInlineImageZoom.trim().toLowerCase() === 'true') {
      window.enableInlineImageZoom = true;
    }

    window.quoteCarouselSettings = {
      isEnabled: 'false'
    };
  </script>
  <script src="/bundles/article-details-scripts?v=sut4IK0UL1boHbFB1dIFz0Aa19vFkU8ObPQ_jAqEuiM1"></script>


  <script src="/bundles/react-scripts?v=SJ2ohADMpR7urxLR3P8d_6aOG9XEc0AKMcilpo8CPsY1"></script>
  <style data-jss="" data-meta="MuiSnackbar">
    .MuiSnackbar-root {
      left: 8px;
      right: 8px;
      display: flex;
      z-index: 1400;
      position: fixed;
      align-items: center;
      justify-content: center;
    }

    .MuiSnackbar-anchorOriginTopCenter {
      top: 8px;
    }

    @media (min-width:600px) {
      .MuiSnackbar-anchorOriginTopCenter {
        top: 24px;
        left: 50%;
        right: auto;
        transform: translateX(-50%);
      }
    }

    .MuiSnackbar-anchorOriginBottomCenter {
      bottom: 8px;
    }

    @media (min-width:600px) {
      .MuiSnackbar-anchorOriginBottomCenter {
        left: 50%;
        right: auto;
        bottom: 24px;
        transform: translateX(-50%);
      }
    }

    .MuiSnackbar-anchorOriginTopRight {
      top: 8px;
      justify-content: flex-end;
    }

    @media (min-width:600px) {
      .MuiSnackbar-anchorOriginTopRight {
        top: 24px;
        left: auto;
        right: 24px;
      }
    }

    .MuiSnackbar-anchorOriginBottomRight {
      bottom: 8px;
      justify-content: flex-end;
    }

    @media (min-width:600px) {
      .MuiSnackbar-anchorOriginBottomRight {
        left: auto;
        right: 24px;
        bottom: 24px;
      }
    }

    .MuiSnackbar-anchorOriginTopLeft {
      top: 8px;
      justify-content: flex-start;
    }

    @media (min-width:600px) {
      .MuiSnackbar-anchorOriginTopLeft {
        top: 24px;
        left: 24px;
        right: auto;
      }
    }

    .MuiSnackbar-anchorOriginBottomLeft {
      bottom: 8px;
      justify-content: flex-start;
    }

    @media (min-width:600px) {
      .MuiSnackbar-anchorOriginBottomLeft {
        left: 24px;
        right: auto;
        bottom: 24px;
      }
    }
  </style>
  <style data-jss="" data-meta="MuiTouchRipple">
    .MuiTouchRipple-root {
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 0;
      overflow: hidden;
      position: absolute;
      border-radius: inherit;
      pointer-events: none;
    }

    .MuiTouchRipple-ripple {
      opacity: 0;
      position: absolute;
    }

    .MuiTouchRipple-rippleVisible {
      opacity: 0.3;
      animation: MuiTouchRipple-keyframes-enter 550ms cubic-bezier(0.4, 0, 0.2, 1);
      transform: scale(1);
    }

    .MuiTouchRipple-ripplePulsate {
      animation-duration: 200ms;
    }

    .MuiTouchRipple-child {
      width: 100%;
      height: 100%;
      display: block;
      opacity: 1;
      border-radius: 50%;
      background-color: currentColor;
    }

    .MuiTouchRipple-childLeaving {
      opacity: 0;
      animation: MuiTouchRipple-keyframes-exit 550ms cubic-bezier(0.4, 0, 0.2, 1);
    }

    .MuiTouchRipple-childPulsate {
      top: 0;
      left: 0;
      position: absolute;
      animation: MuiTouchRipple-keyframes-pulsate 2500ms cubic-bezier(0.4, 0, 0.2, 1) 200ms infinite;
    }

    @-webkit-keyframes MuiTouchRipple-keyframes-enter {
      0% {
        opacity: 0.1;
        transform: scale(0);
      }

      100% {
        opacity: 0.3;
        transform: scale(1);
      }
    }

    @-webkit-keyframes MuiTouchRipple-keyframes-exit {
      0% {
        opacity: 1;
      }

      100% {
        opacity: 0;
      }
    }

    @-webkit-keyframes MuiTouchRipple-keyframes-pulsate {
      0% {
        transform: scale(1);
      }

      50% {
        transform: scale(0.92);
      }

      100% {
        transform: scale(1);
      }
    }
  </style>
  <style data-jss="" data-meta="MuiButtonBase">
    .MuiButtonBase-root {
      color: inherit;
      border: 0;
      cursor: pointer;
      margin: 0;
      display: inline-flex;
      outline: 0;
      padding: 0;
      position: relative;
      align-items: center;
      user-select: none;
      border-radius: 0;
      vertical-align: middle;
      -moz-appearance: none;
      justify-content: center;
      text-decoration: none;
      background-color: transparent;
      -webkit-appearance: none;
      -webkit-tap-highlight-color: transparent;
    }

    .MuiButtonBase-root::-moz-focus-inner {
      border-style: none;
    }

    .MuiButtonBase-root.Mui-disabled {
      cursor: default;
      pointer-events: none;
    }

    @media print {
      .MuiButtonBase-root {
        -webkit-print-color-adjust: exact;
      }
    }
  </style>
  <style data-jss="" data-meta="MuiPopover">
    .MuiPopover-paper {
      outline: 0;
      position: absolute;
      max-width: calc(100% - 32px);
      min-width: 16px;
      max-height: calc(100% - 32px);
      min-height: 16px;
      overflow-x: hidden;
      overflow-y: auto;
    }
  </style>
  <style data-jss="" data-meta="MuiMenu">
    .MuiMenu-paper {
      max-height: calc(100% - 96px);
      -webkit-overflow-scrolling: touch;
    }

    .MuiMenu-list {
      outline: 0;
    }
  </style>
  <style data-jss="" data-meta="MuiButton">
    .MuiButton-root {
      color: rgba(0, 0, 0, 0.87);
      padding: 6px 16px;
      font-size: 0.875rem;
      min-width: 64px;
      box-sizing: border-box;
      transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, box-shadow 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, border 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
      font-family: "Roboto", "Helvetica", "Arial", sans-serif;
      font-weight: 500;
      line-height: 1.75;
      border-radius: 4px;
      letter-spacing: 0.02857em;
      text-transform: uppercase;
    }

    .MuiButton-root:hover {
      text-decoration: none;
      background-color: rgba(0, 0, 0, 0.04);
    }

    .MuiButton-root.Mui-disabled {
      color: rgba(0, 0, 0, 0.26);
    }

    @media (hover: none) {
      .MuiButton-root:hover {
        background-color: transparent;
      }
    }

    .MuiButton-root:hover.Mui-disabled {
      background-color: transparent;
    }

    .MuiButton-label {
      width: 100%;
      display: inherit;
      align-items: inherit;
      justify-content: inherit;
    }

    .MuiButton-text {
      padding: 6px 8px;
    }

    .MuiButton-textPrimary {
      color: #3f51b5;
    }

    .MuiButton-textPrimary:hover {
      background-color: rgba(63, 81, 181, 0.04);
    }

    @media (hover: none) {
      .MuiButton-textPrimary:hover {
        background-color: transparent;
      }
    }

    .MuiButton-textSecondary {
      color: #f50057;
    }

    .MuiButton-textSecondary:hover {
      background-color: rgba(245, 0, 87, 0.04);
    }

    @media (hover: none) {
      .MuiButton-textSecondary:hover {
        background-color: transparent;
      }
    }

    .MuiButton-outlined {
      border: 1px solid rgba(0, 0, 0, 0.23);
      padding: 5px 15px;
    }

    .MuiButton-outlined.Mui-disabled {
      border: 1px solid rgba(0, 0, 0, 0.12);
    }

    .MuiButton-outlinedPrimary {
      color: #3f51b5;
      border: 1px solid rgba(63, 81, 181, 0.5);
    }

    .MuiButton-outlinedPrimary:hover {
      border: 1px solid #3f51b5;
      background-color: rgba(63, 81, 181, 0.04);
    }

    @media (hover: none) {
      .MuiButton-outlinedPrimary:hover {
        background-color: transparent;
      }
    }

    .MuiButton-outlinedSecondary {
      color: #f50057;
      border: 1px solid rgba(245, 0, 87, 0.5);
    }

    .MuiButton-outlinedSecondary:hover {
      border: 1px solid #f50057;
      background-color: rgba(245, 0, 87, 0.04);
    }

    .MuiButton-outlinedSecondary.Mui-disabled {
      border: 1px solid rgba(0, 0, 0, 0.26);
    }

    @media (hover: none) {
      .MuiButton-outlinedSecondary:hover {
        background-color: transparent;
      }
    }

    .MuiButton-contained {
      color: rgba(0, 0, 0, 0.87);
      box-shadow: 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);
      background-color: #e0e0e0;
    }

    .MuiButton-contained:hover {
      box-shadow: 0px 2px 4px -1px rgba(0, 0, 0, 0.2), 0px 4px 5px 0px rgba(0, 0, 0, 0.14), 0px 1px 10px 0px rgba(0, 0, 0, 0.12);
      background-color: #d5d5d5;
    }

    .MuiButton-contained.Mui-focusVisible {
      box-shadow: 0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12);
    }

    .MuiButton-contained:active {
      box-shadow: 0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12);
    }

    .MuiButton-contained.Mui-disabled {
      color: rgba(0, 0, 0, 0.26);
      box-shadow: none;
      background-color: rgba(0, 0, 0, 0.12);
    }

    @media (hover: none) {
      .MuiButton-contained:hover {
        box-shadow: 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);
        background-color: #e0e0e0;
      }
    }

    .MuiButton-contained:hover.Mui-disabled {
      background-color: rgba(0, 0, 0, 0.12);
    }

    .MuiButton-containedPrimary {
      color: #fff;
      background-color: #3f51b5;
    }

    .MuiButton-containedPrimary:hover {
      background-color: #303f9f;
    }

    @media (hover: none) {
      .MuiButton-containedPrimary:hover {
        background-color: #3f51b5;
      }
    }

    .MuiButton-containedSecondary {
      color: #fff;
      background-color: #f50057;
    }

    .MuiButton-containedSecondary:hover {
      background-color: #c51162;
    }

    @media (hover: none) {
      .MuiButton-containedSecondary:hover {
        background-color: #f50057;
      }
    }

    .MuiButton-disableElevation {
      box-shadow: none;
    }

    .MuiButton-disableElevation:hover {
      box-shadow: none;
    }

    .MuiButton-disableElevation.Mui-focusVisible {
      box-shadow: none;
    }

    .MuiButton-disableElevation:active {
      box-shadow: none;
    }

    .MuiButton-disableElevation.Mui-disabled {
      box-shadow: none;
    }

    .MuiButton-colorInherit {
      color: inherit;
      border-color: currentColor;
    }

    .MuiButton-textSizeSmall {
      padding: 4px 5px;
      font-size: 0.8125rem;
    }

    .MuiButton-textSizeLarge {
      padding: 8px 11px;
      font-size: 0.9375rem;
    }

    .MuiButton-outlinedSizeSmall {
      padding: 3px 9px;
      font-size: 0.8125rem;
    }

    .MuiButton-outlinedSizeLarge {
      padding: 7px 21px;
      font-size: 0.9375rem;
    }

    .MuiButton-containedSizeSmall {
      padding: 4px 10px;
      font-size: 0.8125rem;
    }

    .MuiButton-containedSizeLarge {
      padding: 8px 22px;
      font-size: 0.9375rem;
    }

    .MuiButton-fullWidth {
      width: 100%;
    }

    .MuiButton-startIcon {
      display: inherit;
      margin-left: -4px;
      margin-right: 8px;
    }

    .MuiButton-startIcon.MuiButton-iconSizeSmall {
      margin-left: -2px;
    }

    .MuiButton-endIcon {
      display: inherit;
      margin-left: 8px;
      margin-right: -4px;
    }

    .MuiButton-endIcon.MuiButton-iconSizeSmall {
      margin-right: -2px;
    }

    .MuiButton-iconSizeSmall>*:first-child {
      font-size: 18px;
    }

    .MuiButton-iconSizeMedium>*:first-child {
      font-size: 20px;
    }

    .MuiButton-iconSizeLarge>*:first-child {
      font-size: 22px;
    }
  </style>
  <style data-jss="" data-meta="MuiDialog">
    @media print {
      .MuiDialog-root {
        position: absolute !important;
      }
    }

    .MuiDialog-scrollPaper {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .MuiDialog-scrollBody {
      overflow-x: hidden;
      overflow-y: auto;
      text-align: center;
    }

    .MuiDialog-scrollBody:after {
      width: 0;
      height: 100%;
      content: "";
      display: inline-block;
      vertical-align: middle;
    }

    .MuiDialog-container {
      height: 100%;
      outline: 0;
    }

    @media print {
      .MuiDialog-container {
        height: auto;
      }
    }

    .MuiDialog-paper {
      margin: 32px;
      position: relative;
      overflow-y: auto;
    }

    @media print {
      .MuiDialog-paper {
        box-shadow: none;
        overflow-y: visible;
      }
    }

    .MuiDialog-paperScrollPaper {
      display: flex;
      max-height: calc(100% - 64px);
      flex-direction: column;
    }

    .MuiDialog-paperScrollBody {
      display: inline-block;
      text-align: left;
      vertical-align: middle;
    }

    .MuiDialog-paperWidthFalse {
      max-width: calc(100% - 64px);
    }

    .MuiDialog-paperWidthXs {
      max-width: 444px;
    }

    @media (max-width:507.95px) {
      .MuiDialog-paperWidthXs.MuiDialog-paperScrollBody {
        max-width: calc(100% - 64px);
      }
    }

    .MuiDialog-paperWidthSm {
      max-width: 600px;
    }

    @media (max-width:663.95px) {
      .MuiDialog-paperWidthSm.MuiDialog-paperScrollBody {
        max-width: calc(100% - 64px);
      }
    }

    .MuiDialog-paperWidthMd {
      max-width: 960px;
    }

    @media (max-width:1023.95px) {
      .MuiDialog-paperWidthMd.MuiDialog-paperScrollBody {
        max-width: calc(100% - 64px);
      }
    }

    .MuiDialog-paperWidthLg {
      max-width: 1280px;
    }

    @media (max-width:1343.95px) {
      .MuiDialog-paperWidthLg.MuiDialog-paperScrollBody {
        max-width: calc(100% - 64px);
      }
    }

    .MuiDialog-paperWidthXl {
      max-width: 1920px;
    }

    @media (max-width:1983.95px) {
      .MuiDialog-paperWidthXl.MuiDialog-paperScrollBody {
        max-width: calc(100% - 64px);
      }
    }

    .MuiDialog-paperFullWidth {
      width: calc(100% - 64px);
    }

    .MuiDialog-paperFullScreen {
      width: 100%;
      height: 100%;
      margin: 0;
      max-width: 100%;
      max-height: none;
      border-radius: 0;
    }

    .MuiDialog-paperFullScreen.MuiDialog-paperScrollBody {
      margin: 0;
      max-width: 100%;
    }
  </style>
</head>

<body id="app-body-container"><input name="__RequestVerificationToken" type="hidden" value="r4wCu6Jvbs4QtkMhnmipKaGxg1ZOG09qpluv0kJlND0TYWpLuo-swpEKCFW0jedDeA1LvM1N31YwyhZWY_19JMCguD01">

  <link rel="stylesheet" type="text/css" href="/styles/gnw_nitf.css?v=1234">

  <script type="application/ld+json">
      {"@context":"https://schema.org","@type":"NewsArticle","mainEntityOfPage":{"@type":"WebPage","id":"https://www.globenewswire.com/news-release/2016/04/06/1300478/0/en/Segment-Launches-Sources-to-Unify-Siloed-Customer-Data-in-Minutes.html"},"Headline":"Segment Launches Sources to Unify Siloed Customer Data in Minutes","DatePublished":"4/6/2016","DateModified":"4/6/2016","inLanguage":"en","author":{"@type":"Person","name":"Segment"},"publisher":{"@type":"Organization","name":"Segment"},"Description":"\n    SAN FRANCISCO, CA--(Marketwired - April 06, 2016) -  Segment, the customer data platform, today released Sources, giving companies access to new..."}
  </script>




  <link href="/bundles/article-details-styles?v=aaHVoNVD4QqTEqIfvWPmRu5NnQ3VqLFOilaZaZrrIws1" rel="stylesheet">


  <script type="text/javascript">

    window.initGradientEffect = function () {
    }

    document.addEventListener('DOMContentLoaded', function () {
      const extendedTableZoomElements = document.querySelectorAll('.extended-table-zoom');
      if (extendedTableZoomElements && extendedTableZoomElements.length > 0) {
        window.Stickyfill.add(extendedTableZoomElements);
      }

      const socialMediaSideBarContainerElement = document.querySelectorAll('.social-media-side-bar-container');
      if (socialMediaSideBarContainerElement && socialMediaSideBarContainerElement.length > 0) {
        window.Stickyfill.add(socialMediaSideBarContainerElement);
      }
    }, false);
  </script>


  <script async="" src="https://www.googletagmanager.com/gtag/js?id=G-ERWPGTJ5X8"></script>
  <script type="text/javascript">
    window.dataLayer = window.dataLayer || [];
    function gtag() { dataLayer.push(arguments); }
    gtag('js', new Date());

    gtag('config', 'G-ERWPGTJ5X8');
  </script>




  <div role="main">
    <a href="#maincontainer" class="skip-link btn btn-primary text-uppercase">Accessibility: Skip TopNav</a>
    <!--Start header -->
    <div id="pnr-global-site-header-section">
      <div id="navigation" class="nav-container  nav-container-not-auth ">
        <div id="navigation-logo" class="logo-container" title="Home"><a id="navigation-home-link" href="https://www.globenewswire.com" aria-label="Home"><img src="https://www.globenewswire.com/content/logo/color.svg" id="navigation-company-logo" title="Home" alt="GlobeNewswire" width="100%" height="62"></a></div>
        <div id="navigation-title" class="title-container hide">Segment Launches Sources to Unify Siloed Customer Data in Minutes</div>
        <div id="navigation-hamburger-button" class="hamburger-button material-icons">dehaze</div>
        <div id="navigation-small-devices-search-container" class="search-small-devices-container"><span id="navigation-small-devices-search-button" class="search-button material-icons">search</span></div>
        <div id="navigation-main-nav-bar" class="side-bar side-bar-right">
          <div id="navigation-main-small-devices-container" class="hamburger-button material-icons close-button">close</div>
          <div id="navigation-main-navbar-container" class="navbar-container" style="display: block;">
            <ul id="navigation-main-navbar-nav" class="navbar-nav">
              <li id="navigation-nav-item-newsroom" class="nav-item" aria-label="newsroom"><a id="navigation-newsroom" class="nav-link" href="https://www.globenewswire.com/newsroom" target="_self" aria-label="newsroom" rel="" aria-labelledby="navigation-nav-item-newsroom" role="link">Newsroom</a></li>
              <li id="navigation-nav-item-services" class="nav-item drop-down" aria-label="services"><a id="navigation-services" class="nav-link dropdown-toggle" href="#" target="" aria-label="services" rel="" aria-labelledby="navigation-nav-item-services" role="button">services</a>
                <div id="navigation-services-dropdown-menu" class=" dropdown-menu" aria-label="services"><a id="navigation-press_release_distribution" class="nav-link dropdown-item" href="https://www.notified.com/gnw-pr/press-release-distribution" target="_blank" aria-label="press_release_distribution, opens a new tab" rel="noopener noreferrer" aria-labelledby="navigation-services">press release distribution</a><a id="navigation-global_options" class="nav-link dropdown-item" href="https://www.notified.com/gnw-pr/global-distribution" target="_blank" aria-label="global_options, opens a new tab" rel="noopener noreferrer" aria-labelledby="navigation-services">global options</a><a id="navigation-regulatory_filings" class="nav-link dropdown-item" href="https://www.notified.com/gnw-pr/regulatory-filings" target="_blank" aria-label="regulatory_filings, opens a new tab" rel="noopener noreferrer" aria-labelledby="navigation-services">regulatory filings</a><a id="navigation-media_partners"
                    class="nav-link dropdown-item" href="https://www.notified.com/gnw-pr/media-partners" target="_blank" aria-label="media_partners, opens a new tab" rel="noopener noreferrer" aria-labelledby="navigation-services">media partners</a></div>
              </li>
              <li id="navigation-nav-item-contact" class="nav-item" aria-label="contact"><a id="navigation-contact" class="nav-link" href="https://insight.notified.com/globenewswire-contact-us" target="_self" aria-label="contact" rel="" aria-labelledby="navigation-nav-item-contact" role="link">contact us</a></li>
              <li id="navigation-nav-item-language" class="nav-item" aria-label="language"><a id="navigation-language" class="nav-link" href="https://www.globenewswire.com/fr/news-release/2016/04/06/1300478/0/en/Segment-Launches-Sources-to-Unify-Siloed-Customer-Data-in-Minutes.html" target="_self" aria-label="language" rel="" aria-labelledby="navigation-nav-item-language" role="link">Français</a></li>
            </ul>
          </div>
        </div>
        <div id="navigation-right-navigation-container" class="right-navigation-container">
          <div class="link-button-container"><a href="https://www.globenewswire.com/home/<USER>" id="navigation-desktop-signin" target="_self" class="">sign in</a></div>
          <div class="link-button-container"><a class="MuiButtonBase-root MuiButton-root MuiButton-contained registerButton MuiButton-containedPrimary" tabindex="0" aria-disabled="false" id="navigation-desktop-register-btuuton" href="https://www.globenewswire.com/home/<USER>" target="_self"><span class="MuiButton-label">register</span><span class="MuiTouchRipple-root"></span></a></div>
          <div class="link-button-container"><button type="button" id="global-search-expand" class="search-button material-icons border-0">search</button></div>
        </div>
        <div id="navigation-authentication-container-scroll" class="right-navigation-container authentication  hide">
          <div class="link-button-container avatar-scroll"></div>
        </div>
        <div id="navigation-hamburger-button-scroll" class="hamburger-buton-scroll hide"></div>
        <div id="navigation-small-devices-background-disabled" class="small-devices-navigation-background-disabled"></div>
      </div>
      <div class="search-container"></div>
    </div>
    <!--End header-->
    <!-- Start Body -->
    <div class="pnr-body-container" id="maincontainer" tabindex="-1">
      <script type="text/javascript">

        // used in ui component
        window.pnrApplicationSettings = {
          Application: "pnr",
          SelectedLocale: 'en-US',
          PnrHostUrl: 'https://www.globenewswire.com',
          IsAuthenticated: false,
          ContextUser: '',
          ApplicationUrl: 'https://www.globenewswire.com',
          PageContext: '',
          SubscriptionId: 0,
          SubscriptionName: '',
          ArticleLogoUrl: '',
          ArticleHeadline: '',
          IsMobileVersion: 'False' === "True" ? true : false,
          HideLanguageSelection: false,
          NewsSearchHeading: '',
          ArticleMediaAttachments: [],
          AuthSessionExpirationMinutes: '0',
          AppLogoUrl: 'https://www.globenewswire.com/content/logo/color.svg',
          ReaderForgotPasswordUrl: 'https://pnrlogin.globenewswire.com/en/reset/confirmresetpassword',
          ReaderRegisterUrl: 'https://pnrlogin.globenewswire.com/en/register',
          IsQuickSignInEnabled: true,
          ReaderAccountBaseUrl: ''
        };
      </script>






      <div class="main-container container-overwrite" id="container-article" itemscope="" itemtype="http://schema.org/NewsArticle">

        <meta itemprop="wordCount" content="0">
        <meta itemprop="inLanguage" content="en">
        <meta itemprop="description" name="description" content="
  SAN FRANCISCO, CA--(Marketwired - April 06, 2016) - &nbsp;Segment, the customer data platform, today released Sources, giving companies access to new types...">
        <meta itemprop="dateModified" content="4/6/2016">
        <meta itemscope="" itemprop="mainEntityOfPage" itemtype="https://schema.org/WebPage" itemid="https://www.globenewswire.com/news-release/2016/04/06/1300478/0/en/Segment-Launches-Sources-to-Unify-Siloed-Customer-Data-in-Minutes.html">

        <div class="main-header-container ">





          <meta itemprop="image" content="http://www.marketwire.com/library/MwGo/2016/4/4/11G091766/mw1a0ss3mck1sv91saj19m310kr1t782-124371367694.jpg">

          <div class="carousel-container" id="article-logo-carousel">
            <div style="display: block;">
              <div class="slick-slider align-slider-images article-logo-section slick-initialized">
                <div class="slick-list">
                  <div class="slick-track" style="opacity: 1; transform: translate3d(0px, 0px, 0px); width: 1232px;">
                    <div data-index="0" class="slick-slide slick-active slick-current" tabindex="-1" aria-hidden="false" style="outline: none;">
                      <div>
                        <div class="carousel-image-item" tabindex="-1" style="width: 100%; display: inline-block;">
                          <h1><img src="http://www.marketwire.com/library/MwGo/2016/4/4/11G091766/mw1a0ss3mck1sv91saj19m310kr1t782-124371367694.jpg?size=3" alt="logo"></h1>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <script type="text/javascript">

            window.pnrApplicationSettings.PageContext = 'article-page';
            window.articlesSecondaryLogos = null;
            window.articlesSecondaryLogos = JSON.parse('[{\"Title\":null,\"Caption\":null,\"AlternateText\":null,\"LogoUrl\":\"http://www.marketwire.com/library/MwGo/2016/4/4/11G091766/mw1a0ss3mck1sv91saj19m310kr1t782-124371367694.jpg\",\"SourceAlias\":\"\",\"IsPrimaryLogo\":false}]');


            try {
              window.pnrApplicationSettings.ArticleLogoUrl = 'http://www.marketwire.com/library/MwGo/2016/4/4/11G091766/mw1a0ss3mck1sv91saj19m310kr1t782-124371367694.jpg';
            } catch (ex) {
              console.log(e);
            }
          </script>

          <h1 class="article-headline" itemprop="headline">Segment Launches Sources to Unify Siloed Customer Data in Minutes</h1>

          <h2 class="article-sub-headline" itemprop="alternativeHeadline">
            <p>Segment Sources Combines Data From Cloud Services Like Salesforce, Zendesk and SendGrid With Website and Mobile Data, Building a Complete Picture of the Customer Journey </p>
          </h2>



          <p class="article-published-source">


            <span class="d-flex justify-content-start">
              <span class="article-published" itemprop="datePublished">
                <time datetime="2016-04-06T16:00:00Z">April 06, 2016 12:00 ET</time>
              </span>

              <span class="article-source" itemprop="sourceOrganization" itemscope="" itemtype="http://schema.org/Organization">
                | Source:
                <span>

                  <a href="/en/search/organization/Segment" itemprop="name">Segment</a>
                </span>

              </span>
            </span>

            <span id="pnr-global-follow-button" class="pnr-follow-button-width-height"><button class="MuiButtonBase-root MuiButton-root MuiButton-contained pnr-follow-button MuiButton-containedPrimary" tabindex="0" type="button" id="pnr-follow-button"><span class="MuiButton-label">Follow</span><span class="MuiTouchRipple-root"></span></button></span>

            <span itemprop="author copyrightHolder" style="display: none;">Segment</span>
          </p>
          <p itemprop="dateline contentLocation" style="display: none;">
            San Francisco, California, UNITED STATES
          </p>

          <p></p>
        </div>
        <hr>
        <div class="main-scroll-container">
          <div id="pnr-global-social-media-sidebar-section">
            <div class="social-media-side-bar-container">
              <ul>
                <li><button id="facebook" title="Globenewswire Facebook" aria-label="facebook" class="react-share__ShareButton" style="background-color: transparent; border: none; padding: 0px; font: inherit; color: inherit; cursor: pointer;"><svg viewBox="0 0 64 64" width="64" height="64" class="share-button-icon">
                      <rect width="64" height="64" rx="0" ry="0" fill="#3b5998" style="fill: none;"></rect>
                      <path d="M34.1,47V33.3h4.6l0.7-5.3h-5.3v-3.4c0-1.5,0.4-2.6,2.6-2.6l2.8,0v-4.8c-0.5-0.1-2.2-0.2-4.1-0.2 c-4.1,0-6.9,2.5-6.9,7V28H24v5.3h4.6V47H34.1z" fill="white"></path>
                    </svg></button></li>
                <li><button id="twitter" aria-label="twitter" class="react-share__ShareButton" style="background-color: transparent; border: none; padding: 0px; font: inherit; color: inherit; cursor: pointer;"><svg viewBox="0 0 64 64" width="64" height="64" class="share-button-icon">
                      <rect width="64" height="64" rx="0" ry="0" fill="#00aced" style="fill: none;"></rect>
                      <path d="M48,22.1c-1.2,0.5-2.4,0.9-3.8,1c1.4-0.8,2.4-2.1,2.9-3.6c-1.3,0.8-2.7,1.3-4.2,1.6 C41.7,19.8,40,19,38.2,19c-3.6,0-6.6,2.9-6.6,6.6c0,0.5,0.1,1,0.2,1.5c-5.5-0.3-10.3-2.9-13.5-6.9c-0.6,1-0.9,2.1-0.9,3.3 c0,2.3,1.2,4.3,2.9,5.5c-1.1,0-2.1-0.3-3-0.8c0,0,0,0.1,0,0.1c0,3.2,2.3,5.8,5.3,6.4c-0.6,0.1-1.1,0.2-1.7,0.2c-0.4,0-0.8,0-1.2-0.1 c0.8,2.6,3.3,4.5,6.1,4.6c-2.2,1.8-5.1,2.8-8.2,2.8c-0.5,0-1.1,0-1.6-0.1c2.9,1.9,6.4,2.9,10.1,2.9c12.1,0,18.7-10,18.7-18.7 c0-0.3,0-0.6,0-0.8C46,24.5,47.1,23.4,48,22.1z" fill="white"></path>
                    </svg></button></li>
                <li><button id="linkedin" aria-label="linkedin" class="react-share__ShareButton" style="background-color: transparent; border: none; padding: 0px; font: inherit; color: inherit; cursor: pointer;"><svg viewBox="0 0 64 64" width="64" height="64" class="share-button-icon">
                      <rect width="64" height="64" rx="0" ry="0" fill="#007fb1" style="fill: none;"></rect>
                      <path d="M20.4,44h5.4V26.6h-5.4V44z M23.1,18c-1.7,0-3.1,1.4-3.1,3.1c0,1.7,1.4,3.1,3.1,3.1 c1.7,0,3.1-1.4,3.1-3.1C26.2,19.4,24.8,18,23.1,18z M39.5,26.2c-2.6,0-4.4,1.4-5.1,2.8h-0.1v-2.4h-5.2V44h5.4v-8.6 c0-2.3,0.4-4.5,3.2-4.5c2.8,0,2.8,2.6,2.8,4.6V44H46v-9.5C46,29.8,45,26.2,39.5,26.2z" fill="white"></path>
                    </svg></button></li>
                <li><button id="email" aria-label="email" class="react-share__ShareButton" style="background-color: transparent; border: none; padding: 0px; font: inherit; color: inherit; cursor: pointer;"><svg viewBox="0 0 64 64" width="64" height="64" class="share-button-icon">
                      <rect width="64" height="64" rx="0" ry="0" fill="#7f7f7f" style="fill: none;"></rect>
                      <path d="M17,22v20h30V22H17z M41.1,25L32,32.1L22.9,25H41.1z M20,39V26.6l12,9.3l12-9.3V39H20z" fill="white"></path>
                    </svg></button></li>
                <li class="small-devices-hidden">
                  <hr style="width: 50%;">
                </li>
                <li class=""><button id="follow" class="material-icons-outlined border-0 bg-transparent" type="button">add_box</button></li>
                <li class="small-devices-hidden download-pdf-print-container"><button id="more-actions" class="download-pdf-print-more border-0 bg-transparent" type="button">...</button></li>
              </ul>
            </div>
          </div>


          <div class="main-body-container article-body " id="main-body-container" itemprop="articleBody">
            <div class="mw_release">
              <p>
                <span class="mw_region">SAN FRANCISCO, CA</span><span>--(Marketwired - April 06, 2016) - </span>&nbsp;<a href="https://segment.com" rel="nofollow" title="Segment">Segment</a>, the customer data platform, today released <a href="http://segment.com/sources" rel="nofollow" title="Sources">Sources</a>, giving companies access to new types of cloud services data for a more comprehensive view of their customers. With just a few clicks, businesses can combine crucial data from a variety of customer touch points -- support tickets, payments, email campaigns, sales conversations, and more -- with their own website and mobile data into a single data warehouse for analysis. Segment's customers -- including Instacart, Trunk Club, and Udacity -- are using this new data to learn how each interaction prompts customers to stay engaged, drop off, or buy more.
              </p>
              <p>Segment users can now seamlessly export, sync, and store data from industry leading CRM, marketing, and payments platforms into Postgres or Amazon Redshift for analysis. New sources available for use today include Salesforce, Stripe, Zendesk, SendGrid, Mandrill, Intercom, Hubspot, and Twilio, with more coming over the next several weeks.</p>
              <p>"Most companies only analyze how users move through their website and mobile apps, but that's only a tiny sliver of their interactions with a company," said Peter Reinhardt, CEO and co-founder of Segment. "To build great customer experiences, companies must understand how each customer reacts to each touchpoint, whether that's in an email, sales call, or support ticket. This data has previously been stuck in siloed cloud services, and getting it out required months of engineering time. Segment Sources unlocks your data with just a few clicks."</p>
              <p>Segment Sources pipes customer data from cloud services into a data warehouse, removing engineering complexity and providing a more complete customer data set. This allows engineers to reclaim hundreds of hours spent manually connecting these services and tracking and translating their customer data. </p>
              <p>"With Sources, we're gathering vital new insights about our customer experience," said Che Horder, Director of Analytics at Instacart. "For example, by combining Zendesk with purchase data, we can analyze how interactions with our Customer Happiness team affect conversion, sales, and long-term retention. We have a much clearer understanding of our funnel, what's working, and the impact of our support team on revenue." </p>
              <p>Similarly, Sources also increases the value that each of its cloud connections provides, enabling new use cases for partner data while allowing them to focus on what they do best. "SendGrid customers want to analyze long-term user engagement trends and improve their campaigns with raw access to their email data," said Scott Williamson, Senior Director, Product Management at SendGrid. "Segment Sources allows us to stay focused on building the best email delivery service, while also helping our customers drill deep into email behavior and connect our data with other important customer touch points."</p>
              <p>Sources builds on Segment's considerable momentum over the last year, which includes the launch of new products like <a href="https://segment.com/blog/introducing-segment-warehouses-redshift-postgres/" rel="nofollow" title="Warehouses">Warehouses</a>, a <a href="https://segment.com/blog/segment-raises-27-million-series-b/" rel="nofollow" title="Series B">Series B</a> led by Thrive Capital, and an increase in headcount from 30 to 80. Segment now serves 11,000 engineers and analysts at more than 7,000 businesses, passing 25 billion discrete user interactions per month across 50,000 tool connections.&nbsp;</p>
              <p>
                <strong>About Segment<br></strong>Segment is the customer data platform that developers and analysts love because of its elegant APIs and extensive partner ecosystem. As the single point of data collection and distribution, Segment puts customer data to work across hundreds of marketing and analytics applications. Segment is trusted by thousands of companies including Atlassian, Bonobos, Instacart, Intuit and New Relic. For more information visit <a href="https://www.segment.com" rel="nofollow" title="">https://segment.com</a> and <a href="https://www.segment.com/sources" rel="nofollow" title="">https://segment.com/sources</a>.
              </p>
              <div class="mw_disclaimer"></div>
            </div>
            <p>Contact Information: </p>
            <div class="mw-contact">
              <p><strong>MEDIA CONTACT:</strong><br>Nikki Garcia<br>Bateman Group<br><a href="mailto:<EMAIL>" rel="nofollow" title="<EMAIL>"><EMAIL></a></p>
            </div>
            <p></p>
          </div>




          <!--Begin MW Image Box-->
          <div id="article-resource-container" class="main-images-container">
            <div class="row images-row " id="article_image-box">
            </div>
          </div>


          <script type="text/javascript">


          </script>


          <!-- Contact -->


        </div>

        <meta itemprop="provider" content="“GlobeNewswire”">
        <meta itemprop="isFamilyFriendly" content="true">
        <meta itemprop="copyrightYear" content="2016">
      </div>

      <div class="main-recommended-reading-container article-page-recommended-reading-container" style="display: none;">
        <div id="pnr-global-recommended-reading"></div>
      </div>
      <div class="main-recommended-articles-container article-page-explore-container">
        <div id="pnr-global-card-explore-view" style="display: block; width: 100%; float: left;">
          <div class="card-explore-main-recommended-articles-container ">
            <div class="card-explore-recomended-container card-explore-explore-container-column-wrap" style="display: none;">
              <div class="view-more-releases"><a href="/en/newsroom" class="home-page-more text-uppercase">View More News</a><span class="material-icons rightArrow">east</span></div>
            </div>
            <div class="card-explore-explore-container explore-container-horizontal">
              <div class="explore-main-container">
                <div style="display: block; width: 100%;">
                  <h2>Explore</h2>
                </div>
                <div class="slick-slider explore-carousel slick-initialized" dir="ltr"><button type="button" data-role="none" class="slick-arrow slick-prev" style="display: block;"> Previous</button>
                  <div class="slick-list">
                    <div class="slick-track" style="width: 4788px; opacity: 1; transform: translate3d(-1368px, 0px, 0px);">
                      <div data-index="-4" tabindex="-1" class="slick-slide slick-cloned" aria-hidden="true" style="width: 342px;">
                        <div>
                          <div data-testid="explore-item" class="explore-item"><a aria-label="HII Hosts Australian..." href="https://www.globenewswire.com/news-release/2023/07/14/2705147/0/en/HII-Hosts-Australian-Ambassador-and-Senator-Kaine-at-Newport-News-Shipbuilding.html"><img alt="HII Hosts Australian..." loading="lazy" src="https://ml.globenewswire.com/Resource/Download/8846cb51-07df-4412-a59e-0bcf5a58a791?size=3" style="max-width: 100%;"></a>
                            <div class="explore-title"><a id="exp-2705147" aria-label="HII Hosts Australian..." href="https://www.globenewswire.com/news-release/2023/07/14/2705147/0/en/HII-Hosts-Australian-Ambassador-and-Senator-Kaine-at-Newport-News-Shipbuilding.html" title="HII Hosts Australian...">HII Hosts Australian Ambassador and Senator Kaine ...</a></div>
                            <div class="explore-body">July 14, 2023 18:30 ET</div>
                          </div>
                        </div>
                      </div>
                      <div data-index="-3" tabindex="-1" class="slick-slide slick-cloned" aria-hidden="true" style="width: 342px;">
                        <div>
                          <div data-testid="explore-item" class="explore-item"><a aria-label="Green Builder Media..." href="https://www.globenewswire.com/news-release/2023/07/14/2705087/0/en/Green-Builder-Media-Releases-2023-Homeowner-s-Handbook-of-Green-Building-Remodeling.html"><img alt="Green Builder Media..." loading="lazy" src="https://ml.globenewswire.com/Resource/Download/b199faf3-8995-4976-bb8a-fef0e0a0c5ce?size=3" style="max-width: 100%;"></a>
                            <div class="explore-title"><a id="exp-2705087" aria-label="Green Builder Media..." href="https://www.globenewswire.com/news-release/2023/07/14/2705087/0/en/Green-Builder-Media-Releases-2023-Homeowner-s-Handbook-of-Green-Building-Remodeling.html" title="Green Builder Media...">Green Builder Media Releases 2023 Homeowner’s Hand...</a></div>
                            <div class="explore-body">July 14, 2023 14:03 ET</div>
                          </div>
                        </div>
                      </div>
                      <div data-index="-2" tabindex="-1" class="slick-slide slick-cloned" aria-hidden="true" style="width: 342px;">
                        <div>
                          <div data-testid="explore-item" class="explore-item"><a aria-label="Government of Canada..." href="https://www.globenewswire.com/news-release/2023/07/14/2705083/0/en/Government-of-Canada-Announces-Funding-for-Skills-Compétences-Canada-under-the-Canadian-Apprenticeship-Strategy.html"><img alt="Government of Canada..." loading="lazy" src="https://ml.globenewswire.com/Resource/Download/b39304cb-9ef3-48e6-97c1-d8bcac19e0db?size=3" style="max-width: 100%;"></a>
                            <div class="explore-title"><a id="exp-2705083" aria-label="Government of Canada..." href="https://www.globenewswire.com/news-release/2023/07/14/2705083/0/en/Government-of-Canada-Announces-Funding-for-Skills-Compétences-Canada-under-the-Canadian-Apprenticeship-Strategy.html" title="Government of Canada...">Government of Canada Announces Funding for Skills/...</a></div>
                            <div class="explore-body">July 14, 2023 14:02 ET</div>
                          </div>
                        </div>
                      </div>
                      <div data-index="-1" tabindex="-1" class="slick-slide slick-cloned" aria-hidden="true" style="width: 342px;">
                        <div>
                          <div data-testid="explore-item" class="explore-item"><a aria-label="New Fisher House..." href="https://www.globenewswire.com/news-release/2023/07/14/2705079/0/en/New-Fisher-House-Dedicated-at-the-Columbia-Missouri-Veterans-Affairs-Health-Care-System.html"><img alt="New Fisher House..." loading="lazy" src="https://ml.globenewswire.com/Resource/Download/7844f5a4-5693-4777-960d-99d9ecfd990a?size=3" style="max-width: 100%;"></a>
                            <div class="explore-title"><a id="exp-2705079" aria-label="New Fisher House..." href="https://www.globenewswire.com/news-release/2023/07/14/2705079/0/en/New-Fisher-House-Dedicated-at-the-Columbia-Missouri-Veterans-Affairs-Health-Care-System.html" title="New Fisher House...">New Fisher House Dedicated at the Columbia Missour...</a></div>
                            <div class="explore-body">July 14, 2023 13:50 ET</div>
                          </div>
                        </div>
                      </div>
                      <div data-index="0" class="slick-slide slick-active slick-current" tabindex="-1" aria-hidden="false" style="outline: none; width: 342px;">
                        <div>
                          <div data-testid="explore-item" class="explore-item"><a aria-label="Acumen..." href="https://www.globenewswire.com/news-release/2023/07/16/2705282/0/en/Acumen-Pharmaceuticals-Presents-Positive-Topline-Results-from-First-in-Human-Phase-1-Study-of-ACU193-for-Early-Alzheimer-s-Disease-at-the-Alzheimer-s-Association-International-Conf.html"><img alt="Acumen..." loading="lazy" src="https://ml.globenewswire.com/Resource/Download/d817ba18-a731-4fa2-81fa-d4f40d08596d?size=3" style="max-width: 100%;"></a>
                            <div class="explore-title"><a id="exp-2705282" aria-label="Acumen..." href="https://www.globenewswire.com/news-release/2023/07/16/2705282/0/en/Acumen-Pharmaceuticals-Presents-Positive-Topline-Results-from-First-in-Human-Phase-1-Study-of-ACU193-for-Early-Alzheimer-s-Disease-at-the-Alzheimer-s-Association-International-Conf.html" title="Acumen...">Acumen Pharmaceuticals Presents Positive Topline R...</a></div>
                            <div class="explore-body">July 16, 2023 03:00 ET</div>
                          </div>
                        </div>
                      </div>
                      <div data-index="1" class="slick-slide slick-active" tabindex="-1" aria-hidden="false" style="outline: none; width: 342px;">
                        <div>
                          <div data-testid="explore-item" class="explore-item"><a aria-label="HII Hosts Australian..." href="https://www.globenewswire.com/news-release/2023/07/14/2705147/0/en/HII-Hosts-Australian-Ambassador-and-Senator-Kaine-at-Newport-News-Shipbuilding.html"><img alt="HII Hosts Australian..." loading="lazy" src="https://ml.globenewswire.com/Resource/Download/8846cb51-07df-4412-a59e-0bcf5a58a791?size=3" style="max-width: 100%;"></a>
                            <div class="explore-title"><a id="exp-2705147" aria-label="HII Hosts Australian..." href="https://www.globenewswire.com/news-release/2023/07/14/2705147/0/en/HII-Hosts-Australian-Ambassador-and-Senator-Kaine-at-Newport-News-Shipbuilding.html" title="HII Hosts Australian...">HII Hosts Australian Ambassador and Senator Kaine ...</a></div>
                            <div class="explore-body">July 14, 2023 18:30 ET</div>
                          </div>
                        </div>
                      </div>
                      <div data-index="2" class="slick-slide slick-active" tabindex="-1" aria-hidden="false" style="outline: none; width: 342px;">
                        <div>
                          <div data-testid="explore-item" class="explore-item"><a aria-label="Green Builder Media..." href="https://www.globenewswire.com/news-release/2023/07/14/2705087/0/en/Green-Builder-Media-Releases-2023-Homeowner-s-Handbook-of-Green-Building-Remodeling.html"><img alt="Green Builder Media..." loading="lazy" src="https://ml.globenewswire.com/Resource/Download/b199faf3-8995-4976-bb8a-fef0e0a0c5ce?size=3" style="max-width: 100%;"></a>
                            <div class="explore-title"><a id="exp-2705087" aria-label="Green Builder Media..." href="https://www.globenewswire.com/news-release/2023/07/14/2705087/0/en/Green-Builder-Media-Releases-2023-Homeowner-s-Handbook-of-Green-Building-Remodeling.html" title="Green Builder Media...">Green Builder Media Releases 2023 Homeowner’s Hand...</a></div>
                            <div class="explore-body">July 14, 2023 14:03 ET</div>
                          </div>
                        </div>
                      </div>
                      <div data-index="3" class="slick-slide slick-active" tabindex="-1" aria-hidden="false" style="outline: none; width: 342px;">
                        <div>
                          <div data-testid="explore-item" class="explore-item"><a aria-label="Government of Canada..." href="https://www.globenewswire.com/news-release/2023/07/14/2705083/0/en/Government-of-Canada-Announces-Funding-for-Skills-Compétences-Canada-under-the-Canadian-Apprenticeship-Strategy.html"><img alt="Government of Canada..." loading="lazy" src="https://ml.globenewswire.com/Resource/Download/b39304cb-9ef3-48e6-97c1-d8bcac19e0db?size=3" style="max-width: 100%;"></a>
                            <div class="explore-title"><a id="exp-2705083" aria-label="Government of Canada..." href="https://www.globenewswire.com/news-release/2023/07/14/2705083/0/en/Government-of-Canada-Announces-Funding-for-Skills-Compétences-Canada-under-the-Canadian-Apprenticeship-Strategy.html" title="Government of Canada...">Government of Canada Announces Funding for Skills/...</a></div>
                            <div class="explore-body">July 14, 2023 14:02 ET</div>
                          </div>
                        </div>
                      </div>
                      <div data-index="4" class="slick-slide" tabindex="-1" aria-hidden="true" style="outline: none; width: 342px;">
                        <div>
                          <div data-testid="explore-item" class="explore-item"><a aria-label="New Fisher House..." href="https://www.globenewswire.com/news-release/2023/07/14/2705079/0/en/New-Fisher-House-Dedicated-at-the-Columbia-Missouri-Veterans-Affairs-Health-Care-System.html"><img alt="New Fisher House..." loading="lazy" src="https://ml.globenewswire.com/Resource/Download/7844f5a4-5693-4777-960d-99d9ecfd990a?size=3" style="max-width: 100%;"></a>
                            <div class="explore-title"><a id="exp-2705079" aria-label="New Fisher House..." href="https://www.globenewswire.com/news-release/2023/07/14/2705079/0/en/New-Fisher-House-Dedicated-at-the-Columbia-Missouri-Veterans-Affairs-Health-Care-System.html" title="New Fisher House...">New Fisher House Dedicated at the Columbia Missour...</a></div>
                            <div class="explore-body">July 14, 2023 13:50 ET</div>
                          </div>
                        </div>
                      </div>
                      <div data-index="5" tabindex="-1" class="slick-slide slick-cloned" aria-hidden="true" style="width: 342px;">
                        <div>
                          <div data-testid="explore-item" class="explore-item"><a aria-label="Acumen..." href="https://www.globenewswire.com/news-release/2023/07/16/2705282/0/en/Acumen-Pharmaceuticals-Presents-Positive-Topline-Results-from-First-in-Human-Phase-1-Study-of-ACU193-for-Early-Alzheimer-s-Disease-at-the-Alzheimer-s-Association-International-Conf.html"><img alt="Acumen..." loading="lazy" src="https://ml.globenewswire.com/Resource/Download/d817ba18-a731-4fa2-81fa-d4f40d08596d?size=3" style="max-width: 100%;"></a>
                            <div class="explore-title"><a id="exp-2705282" aria-label="Acumen..." href="https://www.globenewswire.com/news-release/2023/07/16/2705282/0/en/Acumen-Pharmaceuticals-Presents-Positive-Topline-Results-from-First-in-Human-Phase-1-Study-of-ACU193-for-Early-Alzheimer-s-Disease-at-the-Alzheimer-s-Association-International-Conf.html" title="Acumen...">Acumen Pharmaceuticals Presents Positive Topline R...</a></div>
                            <div class="explore-body">July 16, 2023 03:00 ET</div>
                          </div>
                        </div>
                      </div>
                      <div data-index="6" tabindex="-1" class="slick-slide slick-cloned" aria-hidden="true" style="width: 342px;">
                        <div>
                          <div data-testid="explore-item" class="explore-item"><a aria-label="HII Hosts Australian..." href="https://www.globenewswire.com/news-release/2023/07/14/2705147/0/en/HII-Hosts-Australian-Ambassador-and-Senator-Kaine-at-Newport-News-Shipbuilding.html"><img alt="HII Hosts Australian..." loading="lazy" src="https://ml.globenewswire.com/Resource/Download/8846cb51-07df-4412-a59e-0bcf5a58a791?size=3" style="max-width: 100%;"></a>
                            <div class="explore-title"><a id="exp-2705147" aria-label="HII Hosts Australian..." href="https://www.globenewswire.com/news-release/2023/07/14/2705147/0/en/HII-Hosts-Australian-Ambassador-and-Senator-Kaine-at-Newport-News-Shipbuilding.html" title="HII Hosts Australian...">HII Hosts Australian Ambassador and Senator Kaine ...</a></div>
                            <div class="explore-body">July 14, 2023 18:30 ET</div>
                          </div>
                        </div>
                      </div>
                      <div data-index="7" tabindex="-1" class="slick-slide slick-cloned" aria-hidden="true" style="width: 342px;">
                        <div>
                          <div data-testid="explore-item" class="explore-item"><a aria-label="Green Builder Media..." href="https://www.globenewswire.com/news-release/2023/07/14/2705087/0/en/Green-Builder-Media-Releases-2023-Homeowner-s-Handbook-of-Green-Building-Remodeling.html"><img alt="Green Builder Media..." loading="lazy" src="https://ml.globenewswire.com/Resource/Download/b199faf3-8995-4976-bb8a-fef0e0a0c5ce?size=3" style="max-width: 100%;"></a>
                            <div class="explore-title"><a id="exp-2705087" aria-label="Green Builder Media..." href="https://www.globenewswire.com/news-release/2023/07/14/2705087/0/en/Green-Builder-Media-Releases-2023-Homeowner-s-Handbook-of-Green-Building-Remodeling.html" title="Green Builder Media...">Green Builder Media Releases 2023 Homeowner’s Hand...</a></div>
                            <div class="explore-body">July 14, 2023 14:03 ET</div>
                          </div>
                        </div>
                      </div>
                      <div data-index="8" tabindex="-1" class="slick-slide slick-cloned" aria-hidden="true" style="width: 342px;">
                        <div>
                          <div data-testid="explore-item" class="explore-item"><a aria-label="Government of Canada..." href="https://www.globenewswire.com/news-release/2023/07/14/2705083/0/en/Government-of-Canada-Announces-Funding-for-Skills-Compétences-Canada-under-the-Canadian-Apprenticeship-Strategy.html"><img alt="Government of Canada..." loading="lazy" src="https://ml.globenewswire.com/Resource/Download/b39304cb-9ef3-48e6-97c1-d8bcac19e0db?size=3" style="max-width: 100%;"></a>
                            <div class="explore-title"><a id="exp-2705083" aria-label="Government of Canada..." href="https://www.globenewswire.com/news-release/2023/07/14/2705083/0/en/Government-of-Canada-Announces-Funding-for-Skills-Compétences-Canada-under-the-Canadian-Apprenticeship-Strategy.html" title="Government of Canada...">Government of Canada Announces Funding for Skills/...</a></div>
                            <div class="explore-body">July 14, 2023 14:02 ET</div>
                          </div>
                        </div>
                      </div>
                      <div data-index="9" tabindex="-1" class="slick-slide slick-cloned" aria-hidden="true" style="width: 342px;">
                        <div>
                          <div data-testid="explore-item" class="explore-item"><a aria-label="New Fisher House..." href="https://www.globenewswire.com/news-release/2023/07/14/2705079/0/en/New-Fisher-House-Dedicated-at-the-Columbia-Missouri-Veterans-Affairs-Health-Care-System.html"><img alt="New Fisher House..." loading="lazy" src="https://ml.globenewswire.com/Resource/Download/7844f5a4-5693-4777-960d-99d9ecfd990a?size=3" style="max-width: 100%;"></a>
                            <div class="explore-title"><a id="exp-2705079" aria-label="New Fisher House..." href="https://www.globenewswire.com/news-release/2023/07/14/2705079/0/en/New-Fisher-House-Dedicated-at-the-Columbia-Missouri-Veterans-Affairs-Health-Care-System.html" title="New Fisher House...">New Fisher House Dedicated at the Columbia Missour...</a></div>
                            <div class="explore-body">July 14, 2023 13:50 ET</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div><button type="button" data-role="none" class="slick-arrow slick-next" style="display: block;"> Next</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div id="attachment-render-section"></div>
      <div id="large-table-viewer"></div>
      <div id="quote-carousel-container"></div>



      <script type="text/javascript">

        window.pnrApplicationSettings.PageContext = 'article-page';

        try {
          window.pnrApplicationSettings.ArticleHeadline = 'Segment Launches Sources to Unify Siloed Customer Data in Minutes';
        } catch (ex) {
          console.log(ex);
        }
        window.pnrApplicationSettings.IsAuthenticated = false;
        window.analyticsTrackingId = '';

        window.cardListViewHoldersList = [
          {
            id: "pnr-global-recommended-reading",
            props: {
              ApiUrl: "/api/article/recommended/2Vor1unn9jD6R4qzD5jBVw==/1300478/en",
              DefaultView: "card"
            }
          }
        ];

        window.cardExplore = {
          id: "pnr-global-card-explore-view",

          ExploreView: {
            ApiUrl: "/api/article/explore/en/False",
            IsHorizontalView: true
          }
        };



        window.ZoomOutTitle = 'Expand';
        window.combinedMediaPortal = {
          followButtonElementId: "pnr-global-follow-button",
          socialMediaSideBarElementId: "pnr-global-social-media-sidebar-section",
          followFormModel: {
            ApiUrl: '/api/subscribe/follow-organization',
            ContextOrgId: 0,
            OrgName: 'Segment'
          },
          socialShareModel: JSON.parse('{\"SocialItemData\":{\"Url\":\"https://www.globenewswire.com/news-release/2016/04/06/1300478/0/en/Segment-Launches-Sources-to-Unify-Siloed-Customer-Data-in-Minutes.html\",\"Title\":\"Segment Launches Sources to Unify Siloed Customer Data in Minutes\",\"Body\":\"\\n    SAN FRANCISCO, CA--(Marketwired - April 06, 2016) -  Segment, the customer data platform, today released Sources, giving companies access to new types of cloud services data for a more...\"},\"AdditionalItems\":[{\"Key\":\"printedcopy\",\"Label\":\"Print\",\"Url\":\"https://www.globenewswire.com/news-release/2016/04/06/1300478/0/en/Segment-Launches-Sources-to-Unify-Siloed-Customer-Data-in-Minutes.html?print=1\",\"Track\":true},{\"Key\":\"downloadPdf\",\"Label\":\"Download PDF\",\"Url\":\"https://www.globenewswire.com/news-release/2016/04/06/1300478/0/en/Segment-Launches-Sources-to-Unify-Siloed-Customer-Data-in-Minutes.html?pdf=1\",\"Track\":true},{\"Key\":\"rss\",\"Label\":\"Subscribe via RSS\",\"Url\":\"/rssfeed/organization/2Vor1unn9jD6R4qzD5jBVw==\",\"Track\":true},{\"Key\":\"atom\",\"Label\":\"Subscribe via ATOM\",\"Url\":\"/atomfeed/organization/2Vor1unn9jD6R4qzD5jBVw==\",\"Track\":true},{\"Key\":\"js-widget\",\"Label\":\"Javascript\",\"Url\":\"https://www.globenewswire.com/JSWidget/organization/FeX2iK5bUKThWojLrfB1oA%3d%3d\",\"Track\":false}],\"BasicModel\":false,\"ShowPintrest\":false}')
        }

      </script>

    </div>
    <!-- End Body -->
    <!--Start footer -->
    <div id="pnr-global-site-footer-section" class="home-page-footer">
      <div class="footer-container" id="footer-container">
        <div class="header-container">
          <div id="footer-logo" class="logo-container" title="Home"><a id="footer-logo-home-link" aria-label="Home" href="/"><img src="https://www.globenewswire.com/content/logo/footer.svg" id="footer-company-logo" title="Home" alt="GlobeNewswire" loading="lazy"></a></div>
        </div>
        <div class="body-container">
          <div class="content-section" id="footer-left">
            <h2>About Us</h2>
            <p>GlobeNewswire is one of the world's largest newswire distribution networks, specializing in the delivery of corporate press releases, financial disclosures and multimedia content to media, investors, and consumers worldwide.</p>
            <p class="social-links"><span>Follow us on social media: </span><span><a id="GlobeNewswireLinkedinId" href="https://www.linkedin.com/company/1006947" title="GlobeNewswire Linkedin" aria-label="GlobeNewswire Linkedin, opens a new tab" target="_blank" rel="noreferrer noopener"><i><svg viewBox="0 0 64 64" width="64" height="64" aria-labelledby="GlobeNewswireLinkedinId" class="share-button-icon">
                      <rect width="64" height="64" rx="0" ry="0" fill="#007fb1" style="fill: none;"></rect>
                      <path d="M20.4,44h5.4V26.6h-5.4V44z M23.1,18c-1.7,0-3.1,1.4-3.1,3.1c0,1.7,1.4,3.1,3.1,3.1 c1.7,0,3.1-1.4,3.1-3.1C26.2,19.4,24.8,18,23.1,18z M39.5,26.2c-2.6,0-4.4,1.4-5.1,2.8h-0.1v-2.4h-5.2V44h5.4v-8.6 c0-2.3,0.4-4.5,3.2-4.5c2.8,0,2.8,2.6,2.8,4.6V44H46v-9.5C46,29.8,45,26.2,39.5,26.2z" fill="white"></path>
                    </svg>
                    <title id="TTGlobeNewswireLinkedinId">GlobeNewswire Linkedin</title>
                  </i></a><a id="NotifiedDigitalTwitterId" href="https://twitter.com/Notified" class="share-alignment" title="Notified Twitter" aria-label="Notified Twitter, opens a new tab" target="_blank" rel="noreferrer noopener"><i><svg viewBox="0 0 64 64" width="64" height="64" aria-labelledby="NotifiedDigitalTwitterId" class="share-button-icon">
                      <rect width="64" height="64" rx="0" ry="0" fill="#00aced" style="fill: none;"></rect>
                      <path d="M48,22.1c-1.2,0.5-2.4,0.9-3.8,1c1.4-0.8,2.4-2.1,2.9-3.6c-1.3,0.8-2.7,1.3-4.2,1.6 C41.7,19.8,40,19,38.2,19c-3.6,0-6.6,2.9-6.6,6.6c0,0.5,0.1,1,0.2,1.5c-5.5-0.3-10.3-2.9-13.5-6.9c-0.6,1-0.9,2.1-0.9,3.3 c0,2.3,1.2,4.3,2.9,5.5c-1.1,0-2.1-0.3-3-0.8c0,0,0,0.1,0,0.1c0,3.2,2.3,5.8,5.3,6.4c-0.6,0.1-1.1,0.2-1.7,0.2c-0.4,0-0.8,0-1.2-0.1 c0.8,2.6,3.3,4.5,6.1,4.6c-2.2,1.8-5.1,2.8-8.2,2.8c-0.5,0-1.1,0-1.6-0.1c2.9,1.9,6.4,2.9,10.1,2.9c12.1,0,18.7-10,18.7-18.7 c0-0.3,0-0.6,0-0.8C46,24.5,47.1,23.4,48,22.1z" fill="white"></path>
                    </svg>
                    <title id="TTIntradoDigitalTwitterId">Notified Twitter</title>
                  </i></a></span></p>
          </div>
          <div class="content-section" id="footer-right">
            <h2>Newswire Distribution Network &amp; Management</h2>
            <ul>
              <li id="footer-home" class=""><a id="footer-home-link" class="" aria-label="Home" rel="" href="https://www.globenewswire.com/" target="_self">Home</a></li>
              <li id="footer-Newsroom" class=""><a id="footer-Newsroom-link" class="" aria-label="Newsroom" rel="" href="https://www.globenewswire.com/newsroom" target="_self">Newsroom</a></li>
              <li id="footer-rssFeeds" class=""><a id="footer-rssFeeds-link" class="" aria-label="RSS Feeds" rel="" href="https://www.globenewswire.com/rss/list" target="_self">RSS Feeds</a></li>
              <li id="footer-legal" class=""><a id="footer-legal-link" class="" aria-label="Legal, opens a new tab" rel="noopener noreferrer" href="https://www.notified.com/privacy" target="_blank">Legal</a></li>
              <li id="footer-contact" class=""><a id="footer-contact-link" class="" aria-label="Contact us, opens a new tab" rel="noopener noreferrer" href="https://insight.notified.com/globenewswire-contact-us" target="_blank">Contact us</a></li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    <!--End footer-->
    <div id="cookies-consent" style="position: fixed"></div>
    <script>
      var preloadedScript = document.createElement("script");
      preloadedScript.src = "/bundles/react-scripts?v=SJ2ohADMpR7urxLR3P8d_6aOG9XEc0AKMcilpo8CPsY1";
      document.head.appendChild(preloadedScript);
    </script>
    <noscript>
      <script src="/bundles/react-scripts?v=SJ2ohADMpR7urxLR3P8d_6aOG9XEc0AKMcilpo8CPsY1"></script>

    </noscript>
  </div>


  <div style="display:none;" id="pnr-follow-dialog-portal-container">
    <div class="reader-signin-container"></div>;
  </div>
</body>

</html>
