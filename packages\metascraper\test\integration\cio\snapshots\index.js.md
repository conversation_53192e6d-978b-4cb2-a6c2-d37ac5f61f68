# Snapshot report for `test/integration/cio/index.js`

The actual snapshot is saved in `index.js.snap`.

Generated by [AVA](https://avajs.dev).

## cio

> Snapshot 1

    {
      audio: null,
      author: '<PERSON>',
      date: '2015-06-03T14:06:00.000Z',
      description: 'If IT is going to better align themselves with business operations, they must be able to objectively quantify the value of the cloud.',
      image: 'http://images.techhive.com/images/article/2015/06/msftone_cloudperspective4-100588855-primary.idge.png',
      lang: 'en',
      logo: 'https://idge.staticworld.net/cio/CIO_logo_300x300.png',
      publisher: 'CIO',
      title: '20 Ways to Measure the Success of Your Growing Cloud Investment',
      url: 'http://www.cio.com/article/2929788/cloud-computing/20-ways-to-measure-the-success-of-your-growing-cloud-investment.html',
      video: null,
    }
