# Snapshot report for `test/integration/economic-times/index.js`

The actual snapshot is saved in `index.js.snap`.

Generated by [AVA](https://avajs.dev).

## economic-times

> Snapshot 1

    {
      author: '<PERSON>',
      date: '2016-01-12T17:04:00.000Z',
      description: 'HackerRank, a tech recruiting company, has launched HackerRank Jobs, to bridge the gap between the applicant and recruiter.',
      image: 'http://economictimes.indiatimes.com/thumb/msid-50551911,width-600,resizemode-4/hackerrank-launches-job-search-platform-hackerrank-jobs.jpg',
      lang: null,
      logo: 'https://economictimes.indiatimes.com/icons/etfavicon.ico',
      publisher: 'The Economic Times',
      title: 'HackerRank launches job search platform HackerRank Jobs - The Economic Times',
      url: 'http://economictimes.indiatimes.com/jobs/hackerrank-launches-job-search-platform-hackerrank-jobs/articleshow/50551900.cms',
      video: null,
    }
