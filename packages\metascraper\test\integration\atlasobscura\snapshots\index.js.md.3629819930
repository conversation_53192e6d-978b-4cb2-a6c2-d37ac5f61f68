# Snapshot report for `test/integration/atlasobscura/index.js`

The actual snapshot is saved in `index.js.snap`.

Generated by [AVA](https://avajs.dev).

## atlasobscura

> Snapshot 1

    {
      author: '<PERSON>',
      date: '2017-06-22T20:16:06.000Z',
      description: 'The innocent-looking “<PERSON><PERSON><PERSON> blank” is a pyromaniac.',
      image: 'https://assets.atlasobscura.com/media/W1siZiIsInVwbG9hZHMvYXNzZXRzL2Q3ZjA4OTZkYjgwNjI4OGZmN19TY3JlZW4gU2hvdCAyMDE3LTA2LTIxIGF0IDkuMzYuNDQgQU0ucG5nIl0sWyJwIiwidGh1bWIiLCIxMTQ4eDc2NSsxNysxOTkiXSxbInAiLCJjb252ZXJ0IiwiLXF1YWxpdHkgOTEgLWF1dG8tb3JpZW50Il0sWyJwIiwidGh1bWIiLCI2MDB4PiJdXQ/Screen%20Shot%202017-06-21%20at%209.36.44%20AM.png',
      lang: 'en',
      logo: 'http://assets.atlasobscura.com/assets/favicon-cc5216a130077092525c8c0c8564549de1817d1a1885e1d69369fd98aaff504a.ico',
      publisher: 'Atlas Obscura',
      title: 'This Ikea Bowl Has Been Setting Things on Fire',
      url: 'http://www.atlasobscura.com/articles/ikea-bowl-blanda-blank-fire',
      video: null,
    }
