<!DOCTYPE html><html lang="en"><head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title></title>
    <meta name="Description" content="">
    <link href="/media/favicon_io/favicon-32x32.png" rel="icon" sizes="32x32">
    <link rel="stylesheet" href="/css/prism.css">
    <link rel="stylesheet" href="/css/index.css">
    <link rel="alternate" href="/feed/feed.xml" type="application/atom+xml" title="Software for Days">
    <link href="https://fonts.googleapis.com/css2?family=Lora:ital,wght@0,400;0,700;1,400&amp;display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&amp;display=swap" rel="stylesheet">
  <style type="text/css">#Meebo\:AdElement\.Root,
#\5f _mom_ad_12,
#\5f _mom_ad_2,
AD-SLOT,
AD-TRIPLE-BOX,
ADS-RIGHT,
AFS-AD,
DFP-AD,
FBS-AD,
LEADERBOARD-AD,
[ad-id^="googlead"],
[data-ad-module],
[id^="adframe_wrap_"],
[id^="bunyad_ads_"],
[lazy-ad="leftbottom_banner"],
[lazy-ad="leftthin_banner"],
[lazy-ad="lefttop_banner"],
[lazy-ad="top_banner"],
[onclick^="window.open('http://adultfriendfinder.com/search/"],
[onclick^="window.open('https://www.brazzersnetwork.com/landing/"],
[onclick^="window.open('window.open('//delivery.trafficfabrik.com/"],
[src*="//www.dianomi.com/smartads.epl"],
[src^="/Redirect.a2b?"],
[src^="http://api.lanistaads.com/ServeAd?"],
a[data-redirect^="http://click.plista.com/pets"],
a[href$="/vghd.shtml"],
a[onclick*="//m.economictimes.com/etmack/click.htm"],
a[onmousedown^="this.href='/wp-content/embed-ad-content/"],
a[src^="https://www.utherverse.com/net/"],
ad-desktop-sidebar,
amp-ad-custom,
app-advertisement,
aside[id^="adrotate_widgets-"],
aside[id^="advads_ad_widget-"],
aside[id^="div-gpt-ad"],
aside[id^="tn_ads_widget-"],
aside[itemtype="https://schema.org/WPAdBlock"],
bottomadblock,
div[class*="-storyBodyAd-"],
div[class*="_AdInArticle_"],
div[class*="_browserAdOuterContainer_"],
div[class^="AdBannerWrapper-"],
div[class^="AdCard_"],
div[class^="AdEmbeded__AddWrapper"],
div[class^="AdItem-"],
div[class^="Ad__adContainer"],
div[class^="Ad__bigBox"],
div[class^="Ad__container"],
div[class^="AdhesionAd_"],
div[class^="BannerAd_"],
div[class^="BlockAdvert-"],
div[class^="Directory__footerAds"],
div[class^="Display_displayAd"],
div[class^="PreAd_"],
div[class^="ResponsiveAd-"],
div[class^="SponsoredAds"],
div[class^="ad_border_"],
div[class^="ad_position_"],
div[class^="adbanner_"],
div[class^="adpubs-"],
div[class^="ads-partner-"],
div[class^="adsbutt_wrapper_"],
div[class^="advertisement-desktop"],
div[class^="articleAdUnitMPU_"],
div[class^="awpcp-random-ads"],
div[class^="block-openx-"],
div[class^="hp-ad-rect-"],
div[class^="index__adWrapper"],
div[class^="index_adAfterContent_"],
div[class^="index_adBeforeContent_"],
div[class^="index_displayAd_"],
div[class^="kiwi-ad-wrapper"],
div[class^="kiwiad-desktop"],
div[class^="kiwiad-popup"],
div[class^="largeRectangleAd_"],
div[class^="lifeOnwerAd"],
div[class^="local-feed-banner-ads"],
div[class^="pane-google-admanager-"],
div[class^="proadszone-"],
div[data-ad-underplayer],
div[data-adservice-param-tagid="contentad"],
div[data-adunit-path],
div[data-adunit],
div[data-content="Advertisement"],
div[data-id-advertdfpconf],
div[data-mediatype="advertising"],
div[data-role="sidebarAd"],
div[data-spotim-slot],
div[data-subscript="Advertising"],
div[id^="ADV-SLOT-"],
div[id^="YFBMSN"],
div[id^="acm-ad-tag-"],
div[id^="ad-cid-"],
div[id^="ad-div-"],
div[id^="ad-gpt-"],
div[id^="ad-position-"],
div[id^="ad-server-"],
div[id^="ad_bigbox_"],
div[id^="ad_head_celtra_"],
div[id^="ad_position_"],
div[id^="ad_rect_"],
div[id^="ad_script_"],
div[id^="adfox_"],
div[id^="adrotate_widgets-"],
div[id^="ads120_600-widget"],
div[id^="ads250_250-widget"],
div[id^="ads300_100-widget"],
div[id^="ads300_250-widget"],
div[id^="ads300_600-widget"],
div[id^="adspot-"],
div[id^="advads_"],
div[id^="advt-"],
div[id^="amzn-assoc-ad"],
div[id^="block-views-topheader-ad-block-"],
div[id^="cns_ads_"],
div[id^="crt-"][style],
div[id^="dfp-slot-"],
div[id^="div-ads-"],
div[id^="div-adtech-ad-"],
div[id^="div_ad_stack_"],
div[id^="div_openx_ad_"],
div[id^="dmRosAdWrapper"],
div[id^="drudge-column-ads-"],
div[id^="ezoic-pub-ad"],
div[id^="google_dfp_"],
div[id^="gtm-ad-"],
div[id^="lazyad-"],
div[id^="proadszone-"],
div[id^="q1-adset-"],
div[id^="rc-widget-"],
div[id^="sticky_ad_"],
div[id^="tms-ad-dfp-"],
div[id^="traffective-ad-"],
div[id^="yandex_ad"],
div[itemtype="http://www.schema.org/WPAdBlock"],
iframe[src*="mellowads.com"],
img[alt^="Fuckbook"],
p[id^="div-gpt-ad-"],
script[src^="http://free-shoutbox.net/app/webroot/shoutbox/sb.php?shoutbox="] + #freeshoutbox_content,
span[data-component-type="s-ads-metrics"],
topadblock,
[id*="MGWrap"],
[id*="MarketGid"],
#\5f _admvnlb_modal_container,
input[onclick^="window.open('http://www.FriendlyDuck.com/"],
input[onclick^="window.open('http://www.friendlyduck.com/"],
#\5f _nq__hh[style="display:block!important"],
[onclick*="content.ad/"],
div > [class][onclick*=".updateAnalyticsEvents"],
AMP-AD,
div[class$="dealnews"] > .dealnews,
iframe[src^="http://static.mozo.com.au/strips/"],
td[valign="top"] > .mainmenu[style="padding:10px 0 0 0 !important;"],
#mn div[style="position:relative"] > #center_col > ._Ak,
#mn div[style="position:relative"] > #center_col > div > ._dPg,
c-wiz[jsrenderer="YnuqN"] > div > div > .Rn1jbe,
div[data-crl="true"][data-id^="CarouselPLA-"],
div[data-flt-ve="sponsored_search_ads"],
div[data-ismultirow="true"][data-id^="CarouselPLA-"],
div[jscontroller="U835zd"] + c-wiz[jsrenderer="YnuqN"],
div[role="navigation"] + c-wiz > div > .kxhcC,
div[role="navigation"] + c-wiz > script + div > .kxhcC,
a[data-obtrack^="http://paid.outbrain.com/network/redir?"],
a[data-oburl^="http://paid.outbrain.com/network/redir?"],
a[data-oburl^="https://paid.outbrain.com/network/redir?"],
a[data-redirect^="http://paid.outbrain.com/network/redir?"],
a[data-redirect^="https://paid.outbrain.com/network/redir?"],
a[data-redirect^="this.href='http://paid.outbrain.com/network/redir?"],
a[data-url^="http://paid.outbrain.com/network/redir?"],
a[data-url^="http://paid.outbrain.com/network/redir?"] + .author,
a[data-widget-outbrain-redirect^="http://paid.outbrain.com/network/redir?"],
a[onmousedown^="this.href='http://paid.outbrain.com/network/redir?"][target="_blank"],
a[onmousedown^="this.href='http://paid.outbrain.com/network/redir?"][target="_blank"] + .ob_source,
a[onmousedown^="this.href='http://staffpicks.outbrain.com/network/redir?"][target="_blank"],
a[onmousedown^="this.href='http://staffpicks.outbrain.com/network/redir?"][target="_blank"] + .ob_source,
a[onmousedown^="this.href='https://paid.outbrain.com/network/redir?"][target="_blank"] + .ob_source,
a[style="display:block;width:300px;min-height:250px"][href^="http://li.cnet.com/click?"],
a[target="_blank"][onmousedown="this.href^='http://paid.outbrain.com/network/redir?"],
div[class^="zn-sponsored-outbrain-"],
.trc_rbox_div a[target="_blank"][href^="http://tab"],
a[target="_blank"][href^="http://api.taboola.com/"],
div[class^="backfill-taboola-home-slot-"],
div[id^="mainads"],
div[id^="zergnet-widget"],
input[onclick^="window.open('http://www.firstload.de/affiliate/"],
foxfield-xmas,
[onclick*="window.open('http://deloplen.com/"],
[data-uri^="https://s3.amazonaws.com"],
[data-lnguri^="https://s3.amazonaws.com"],
[onclick*="postlnk.com"],
[src*="/dutti/"],
display-ad-component,
[class^="DisplayAd"],
div[class*="displayAdRight"],
iframe[src^="https://smitionsory.co/"],
[data-lnguri*="vipbox"],
[src="/static/img/download-top.png"],
[src="/img/bat-banner.png"],
[src^="https://aff1xstavka.com"],
[id^="p_root_"],
[src*="librateam.net"],
[src^="//dombnrs.com/"],
#\5f CookieText,
#\5f _accept_cookie,
#\5f _ic-notice,
#\5f _ndcc_cookieImpliedConsent,
#\5f cookieConsentWrapper,
#\5f iph_cp_popup,
#___gatsby div[class*="CookieBox"],
#___gatsby div[class*="cookieBox"],
#___gatsby div[class*="cookies-alert"],
#___gatsby div[class*="cookies-module--cookieprompt"],
#__layout div[class*="cookie-popup"],
#__next div[class*="CookieConsent"],
#__next div[class*="CookieNotice"],
#__next div[class*="CookieWidget"],
#__next div[class*="GdprBanner"],
#__next div[class*="GdprParentWrapper"],
#__next div[class*="PrivacyNotification"],
#__next div[class*="PrivacyPolicy"],
#__next div[class*="StyledCookieLayer"],
#__next div[class*="privacy-notification"],
#cookieBanner\.banner,
#ipott div[class*="CookiePolicy"],
#js\.cookie\.banner,
#main-content div[class*="CookieWarning"],
#react-app div[class*="cookieConsentBanner"],
#root [class*="CookiePolicyWidget"],
#root div[aria-label="cookieconsent"],
#root div[class*="cookieMonster-wrapper"],
#root div[class*="cookies-policy"],
#root div[data-dmid*="cookiebar"],
#root div[data-test*="Cookie-Banner"],
#root-header div[class*="GdprPopup"],
.RootContainerClassName div[class*="CookieNotificationPopup"],
.dv-responsive div[data-bind*="CookiesInfo"],
.sÂ·footer-gdpr,
COOKIE-ALERT,
COOKIE-HEADER,
DCINFO_COOKIE_AGB,
GDPR-NOTICE,
[aria-describedby="cookie-information"],
[aria-describedby="cookieprivacy"],
[aria-describedby="cookies-policy-message"],
[aria-describedby="dialog-eu-cookie-law"],
[aria-labelledby="ui-dialog-title-cookiebox"],
[cnn-cookie-policy],
[cookie-unique-name],
[data-at-selector="cookie-banner"],
[data-bind="visible: showCookieWarning"],
[data-cookie-validity-days],
[data-cookie="uso-accept-cookies"],
[data-cookie^="cookie.disclaimer"],
[data-key="cookies-warning"],
[data-module="cookies"],
[data-name="cookieNotice"],
[data-notification="CookiePolicyAgreement"],
[data-qa="txtGdprCookiePopup"],
[data-react-class="EUCookieBanner"],
[data-selector="cookie-policy"],
[data-target="coockie"],
[data-test-id="cookie-consent-banner"],
[data-test="cookie-banner"],
[data-testid="cookienotice-container"],
[data-testid="main-cookies-banner-container"],
[data-token="gdpr_cookie_notice"],
[data-type="cookie-addsense"],
[data-unique-cookie-name],
[data-veci="cookies-policy"],
[name="cookie-info"],
[ng-show^="cookiePolicyCtrl"],
[type="cookie-notification"],
accept-cookie-box,
acidjs-xcookie-consent,
adcookies,
amedia-privacybox,
amp-consent,
amp-user-notification,
app-aviso-cookie,
app-banner[arialabel*="Cookies"],
app-cookie,
app-cookie-consent,
app-cookie-consent-feature,
app-cookie-notification,
app-cookie-policy,
app-cookie-rules,
app-cookie-warning,
app-cookies-consent,
app-cookies-policy-updated,
app-root app-cookie-acceptance,
aside[class^="cookie-banner__Wrapper-"],
awsui-alert[dismiss*="CookieBanner"],
aza-cookie-message,
b2c-cookies-notification,
cbn-cookies,
cg-cookies-banner,
cloudflare-app[app="cookiless"],
cloudflare-app[app="tibrr-cookie-consent"],
cmpviews-cookies-accept,
cn-cookie-agreement,
col-cookie-info-banner,
consent,
consent-notification,
cook-disclaimer,
cookie-agreement-block,
cookie-bar,
cookie-confirmation,
cookie-consent,
cookie-drawer-policy,
cookie-info,
cookie-law-banner,
cookie-notice,
cookie-notification,
cookie-permission,
cookie-policy,
cookie-policy-popin,
cookie-popup,
cookie-warning,
cookie_notice,
cookiebox,
cookies-consent,
cookies-gdpr,
cookies-hint,
cookies-popup,
cookies-requirement,
cookies-warning,
cookies.ng-scope,
cookiless-div,
cru-cookie-policy,
div[class$="BandeauCookies"],
div[class$="UpdatePanelCookie"],
div[class*="-CookiesBar-"],
div[class*="CookieBanner-"],
div[class*="CookieBannerContainer"],
div[class*="CookieBarStyled"],
div[class*="CookieDisclaimer-"],
div[class*="CookieWrapper"],
div[class*="CookiesBanner_"],
div[class*="CookiesFooter"],
div[class*="GDPRConsentForm"],
div[class*="Warnings__cookieBar"],
div[class*="_CookieBanner_"],
div[class*="_cookiesPolicy_"],
div[class*="cookie-consent-modal_"],
div[class*="cookieBarContainer"],
div[class*="cookieDisclaimer"],
div[class*="cookiesBarVisible"],
div[class^="ConsentBar_"],
div[class^="CookieBanner_"],
div[class^="CookieBarWrapper"],
div[class^="CookieBar_"],
div[class^="CookieGdpr"],
div[class^="CookieNotice"],
div[class^="CookiePopup"],
div[class^="CookieWarn__"],
div[class^="CookieWarning_"],
div[class^="CookiesAdvice"],
div[class^="CookiesNotification"],
div[class^="Cookies__Wrapper"],
div[class^="Cookies__cookies"],
div[class^="GDPR-module"],
div[class^="GDPRBanner_"],
div[class^="GdprBanner_"],
div[class^="_CookieNotice_"],
div[class^="__alertCookie"],
div[class^="__cookie-banner"],
div[class^="_cookieBanner-"],
div[class^="app-components-CookieDisclaimer"],
div[class^="app_cmpAppGdpr-"],
div[class^="app_gdpr-"],
div[class^="ccpa-notification-banner"],
div[class^="cookie-container_"],
div[class^="cookie-notification_"],
div[class^="cookie-ui_"],
div[class^="cookieBanner-"],
div[class^="cookieBanner_"],
div[class^="cookieBlock"],
div[class^="cookiesAgreement"],
div[class^="cookiesAlert_"],
div[class^="gdpr_banner_"],
div[class^="index_cookieNotification"],
div[class^="popupConsentBanner"],
div[class^="src-ui-Cookie"],
div[class^="styles__Cookie"],
div[class^="termly-consent-"],
div[data-automation="cookies-banner"],
div[data-automation="privacy-banner-wrapper"],
div[data-banner="cookies"],
div[data-borlabs-cookie-wrap],
div[data-box-name="cookie policy"],
div[data-cmp-no-consent],
div[data-component="cookie-policy"],
div[data-component="cookieContent"],
div[data-components="pdr-ws1lib-header"],
div[data-container-acceptcookies],
div[data-cookie-law-banner-selector],
div[data-cookie-name="header_cookie_policy"],
div[data-cookie-notification],
div[data-cookie-path],
div[data-cookie-policy-modal],
div[data-cookie-warning],
div[data-cookiebanner],
div[data-cookiebar],
div[data-cookielayer-init],
div[data-corgi-component="cookie-banner"],
div[data-etsy-promo-cookie-expires],
div[data-gdpr-consent-prompt],
div[data-id="cookieBar"],
div[data-id="cookie_dialog"],
div[data-is-cookieinfo],
div[data-notificationid="cookie"],
div[data-pnp-mi-id="mi-cookies"],
div[data-pov-accept-cookies-message],
div[data-privacy-consent],
div[data-react-class="CookieNotice"],
div[data-role="cookie-policy-banner"],
div[data-section-type="CookiesBanner"],
div[data-selector="container@cookiebar"],
div[data-selen-group="cookies-bar"],
div[data-selenium="CookieBanner"],
div[data-test-sell-cookie-banner],
div[data-test="cookie-policy-banner"],
div[data-testid="cookie-disclaimer"],
div[data-testid="cookie-policy-banner"],
div[data-veloute="cookie-disclaimer"],
div[data-widget="cookie-bar"],
div[data-wzb="CookieNotification"],
div[element-structure-cookiemonster],
div[id*="CoockieInformation"],
div[id*="cookie_info_bar"],
div[id*="nx_gdpr_modal"],
div[id="cm:cookieWrapper"],
div[id^="CookieMessage_"],
div[id^="ccpa-notification-banner"],
div[id^="mod-privacy-policy"],
div[id^="n-cookies"],
div[js-cookies],
div[ld-scope="cookie-consent"],
div[ng-click="self.setCookieDisclaimer()"],
div[spec="cookie-disclaimer"],
dk-cookie-warning,
dva-m-cookie-flyout,
ea-truste-consent-bar,
edn-cookie,
eo-cookie-bar,
epaas-consent-drawer-shell,
form[name="FormLeggeCookies"],
g-cookie-policy,
gdpr-cookies,
gdpr-policies,
gdprpopupmod,
gfp-cookie-policy-notice,
hma-cookie-notification,
idg-cookie-info-bar,
ir-cookie-consent,
jsa-cookie,
kuma-cookie-bar,
lib-privacy-policy-toast,
lnb-cookie-policy-overlay,
m-cookies-notice,
md-toast.cookies-toast,
mip-cookie-info,
nl-cookie-notice,
o-cookie-law,
oct-cookie-consent,
ods-accept-cookies-message,
of-cookies-notice,
onl-cookie,
outer-wrapper-cookie,
policy-consent,
pp-cookie-dialog,
privacy-policy-toast,
rtk-cookies-notice,
sd-cookies-widget,
section[class*="_CookieNotice"],
section[data-test-id="cookie-notice"],
section[data-view="cookiePolicy"],
sell-cookie-banner,
shb-cookie-accept,
suchen-cookie-privacy-toast,
thread-cookie-notice,
ts-cookie-msg,
tui-cookie-bar,
tui-cookie-consent,
uc-cookie-notification,
vc-site-cookiepolicy-dialog,
vls-cookie-banner,
voc-cookie-warning,
wc-cookies,
web-cookie-consent,
wp-cookie-notification-banner,
xk-cookie-policy-warning,
yg-cookie-confirm,
ytd-app ~ iron-overlay-backdrop,
ytd-consent-bump-renderer,
ytd-mealbar-promo-renderer,
ytg-notification-footer,
body.sliding-popup-processed > #sliding-popup,
header > #notify-bar,
div > #cnsh[style="display: block;"],
div[style="min-width: 750px;"] > .gb_wb { display: none !important; }

div[aria-label="Ads"],
div[class*="margin-Advert"],
div[data-native_ad],
div[id^="advads-"],
div[id^="dfp-ad-"],
div[id^="div-gpt-ad"],
div[id^="google_ads_iframe_"],
div[itemtype="http://schema.org/WPAdBlock"],
iframe[id^="google_ads_frame"],
iframe[id^="google_ads_iframe"],
iframe[name^="google_ads_iframe"],
iframe[src^="http://ad.yieldmanager.com/"],
[id*="ScriptRoot"],
a[data-nvp*="'trafficUrl':'https://paid.outbrain.com/network/redir?"],
a[onmousedown^="this.href='https://paid.outbrain.com/network/redir?"][target="_blank"],
.trc_related_container div[data-item-syndicated="true"],
[aria-label="cookieconsent"],
[data-cookie-id],
div[class^="cookie-banner"],
div[class^="cookie-consent_"],
div[data-cookie],
div[data-module="cookie_banner"],
div[data-nconvert-cookie],
thor-cookies,
div[class*="CookieConsent"] { display: none !important; }</style><style type="text/css">a[href^="http://d2.zedo.com/"],
[href*="cadsecs.com/"],
[href*="prayuserparka.com/"],
[href*="wap4dollar.com/"],
a[href*="ad2upapp.com/"],
a[href*="n47adshostnet.com/"],
[href*="uselnk.com/"],
a[href="https://www.focus.de/deal/focus-online-deal-mit-weltsparen-setzen-sie-jetzt-auf-zinserhoehungen-weltsparen-schenkt-ihnen-dafuer-150-euro_id_11868745.html"] > img,
a[href^="http://web.adblade.com/"],
a[href*="5iclx7wa4q.com"],
a[href*="=Adtracker"],
a[href*="=adscript"],
a[href*="=exoclick"],
a[href*="onclkds."],
a[href*="pussl3.com"],
[href*="postlnk.com"],
[href*="passtechusa.com"],
[href*="librateam.net"],
a[href*="pussl"] { display: none !important; }</style><style type="text/css">*,
  ::before,
  ::after {
    animation-delay: 0s !important;
    transition-delay: 0s !important;
    animation-duration: 0s !important;
    transition-duration: 0s !important;
    transition-property: none !important;
  }</style></head>
  <body>
    <header>
      <h2>
        <span class="badge">
          <a class="title" href="/">Software for Days</a>
        </span>
      </h2>
    </header>
    <main class="post">

      <h1>Resolving the Time Paradox Implied by Functional Programs</h1>

<p class="introductory-caveat">Excerpted from <a href="/post/functional-programming-and-identity-state-and-time/#resolving-the-time-paradox">Functional Programming and the Semantics of Change, State &amp; Time</a>.</p>
<blockquote>
<p>“No man can ever cross the same river twice.” Because what’s a river? I mean, we love this idea of objects; like there’s this thing that changes. Right? There’s no river. Right? There’s water there at one point-in-time. And another point-in-time, there’s other water there. — Rich Hickey, <a href="https://github.com/matthiasn/talk-transcripts/blob/master/Hickey_Rich/AreWeThereYet.md">Are We There Yet</a>, quoting Heraclitus.</p>
</blockquote>
<p>From the perspective of a user, a functional program may appear stateful. Interact with <a href="/post/functional-programming-and-identity-state-and-time/#stateful-functional-programs">the functional ATM program above</a> and notice the program remembering previous encounters. On the one hand, this is not surprising. We included an imperative layer to remember previous states. Instead of decomposing the state of the program into distinct objects, like <code>bankAccount</code> and <code>withdrawalAmount</code>, we created a single global object, the <code>store</code>. On the other hand, focusing on the “object” portion of the program betrays an object-oriented predisposition. The imperative piece of the program is merely a construct used to facilitate a computation based asynchronously on another. One can even imagine a programming language where such a construct is built into the language itself, hiding any imperative implementation from the programmer’s view. In fact, such a language exists that compiles to JavaScript.<sup class="footnote-ref"><a href="#fn1" id="fnref1">[1]</a></sup> In other words, it is syntax, not semantics. The semantics of the program better align with the semantics of a recursive, iterative function, having state <code>S</code> at a discrete step <code>i</code> — run the functional ATM program with the output of the previous run to produce the input for the next.</p>
<p>That a program with a functional, stateless and timeless core can maintain state is surprising, to say the least. Look around the room, bus, park or wherever you find yourself reading this sentence, and you will likely identify “a collection of distinct objects,” such as dogs, people, and trees, “whose behaviors may change over time.” Look around the functional ATM program, on the other hand, and there are no identifiable objects to be found. Yet, the program appears to have state just like any other object in the room.</p>
<p>However, the ostensible “paradox” dissipates when the augmentation of our conception of time extends beyond the functional program to include the rest of our physical reality.</p>
<blockquote>
<p>One way to resolve this paradox is to realize that it is the user’s temporal existence that imposes state on the system. If the user could step back from the interaction and think in terms of streams of balances rather than individual transactions, the system would appear stateless — <a href="https://web.mit.edu/alexmv/6.037/sicp.pdf">SICP</a> Section 3.5.5</p>
</blockquote>
<p>Instead of viewing the <em>world</em> as the sum of its objects, each reflecting its latest state as time elapses, we may also think in terms of discrete state histories. We may interpret the dog at the park as moving in discrete steps <code>S(i)</code> to <code>S(i+1)</code>, just as we interpret the state of our functional program as moving in discrete steps <code>S(i)</code> to <code>S(i+1)</code>.</p>
<p>Consider video media. To movie scenes, we may attribute the same object-oriented semantics. Character and inanimate objects shift, interact and evolve as time elapses.</p>
<video id="metavideo" poster="/media/metavideoposter.gif" preload="none" style="margin:0;padding:0" width="480" controls="">
    <source src="/media/metavideo.mp4" type="video/mp4; codecs=&quot;avc1.42E01E, mp4a.40.2&quot;">
    <img src="/media/metavideo.mp4" title="Your browser does not support the mp4 video codec.">
</video>
<p>While playing the above video, for example, we may conclude that “a cat is dancing.” Yet, videos are comprised of static frames stitched together in a certain sequence at discrete time intervals. Each frame corresponds to a state of the video at a moment in time and the frames, taken together, a time-denominated series of discrete states. The media chosen above is intentionally meta. The video includes a TV animation of a scene mirrored by a flip-book<sup class="footnote-ref"><a href="#fn2" id="fnref2">[2]</a></sup>, showing static frames strung together at discrete time intervals, which itself is mirrored by a flip-book in “real” life, showing static frames strung together at discrete time intervals. Take another step back to notice that the above gif media (or mp4 if your browser supports html5) being played on <em>your</em> computer is comprised of static frames, strung together at discrete time intervals.</p>
<p>There is nothing stopping us from taking another step back and interpreting the real world in which your computer currently sits as static frames, strung together at discrete time intervals. We <em>may</em> attribute normal object-oriented semantics to the above gif, concluding that “a cat is dancing.” However, we may also attribute functional semantics, concluding that “a cat has arms above its head on frame fᵢ.” At a park in the real world, we may conclude that “a dog is chasing a squirrel.” However, we may also conclude that “a dog is in the running motion behind a squirrel in the running motion on frame fᵢ.” In both cases, we may identify a time-series of states instead of objects that change over time. The functional programming paradigm can be coherently applied to world and program alike.</p>
<p>With a model for discrete time in mind, it is less surprising that functional programs can appear stateful. A user of the program may be viewed as a series of states, just like the program itself. A specific series of user states, for example,</p>
<pre class="language-text"><code class="language-text">U₀: "Open up this blog post"<br>U₁: “Select 20 option”<br>U₂: “Click withdraw”<br>...<br>U(i): Uᵢ</code></pre>
<p>directly precipitate a series of program states:</p>
<pre class="language-text"><code class="language-text">S₀: balance:100, amount:10<br>S₁: balance:100, amount:20<br>S₂: balance:80, amount:20<br>...<br>S(i): program(Sᵢ₋₁, Eᵢ)</code></pre>
<p>In both cases, pieces of static information may be listed, one <em>after</em> another. Moreover, both lists can be plotted along the same discrete timeline <code>i</code>. User interactions come in a certain order <code>U(i)</code>, triggering a run of the program function against the result of the previous run <code>S(i-1)</code> and event data <code>E(i)</code>, in order to produce <code>S(i)</code>. Our reality can be viewed as a time-series of states, just as it can be viewed as a collection of objects. Functional programming models a time-series of states, just as as object-oriented programming models objects. When the program and world <em>alike</em> can be viewed as “streams of information that flow in the system,” (<a href="https://web.mit.edu/alexmv/6.037/sicp.pdf">SICP</a> Section 3) the world can flow into the program, and the program back into the world.</p>
<hr class="footnotes-sep">
<section class="footnotes">
<ol class="footnotes-list">
<li id="fn1" class="footnote-item"><p><a href="https://guide.elm-lang.org/">Elm</a> programs are trivially made to be stateful, notwithstanding the exclusive use of pure functions <em>and</em> the asynchrony of user interactions! This counter program</p>
<pre class="language-elm"><code class="language-elm"><span class="token import_statement"><span class="token keyword">import</span> Browser</span><span class="token import_statement"><br><span class="token keyword">import</span> Html <span class="token keyword">exposing</span> </span><span class="token punctuation">(</span><span class="token constant">Html</span><span class="token punctuation">,</span> <span class="token hvariable">button</span><span class="token punctuation">,</span> <span class="token hvariable">div</span><span class="token punctuation">,</span> <span class="token hvariable">text</span><span class="token punctuation">)</span><br><span class="token import_statement"><span class="token keyword">import</span> Html.Events <span class="token keyword">exposing</span> </span><span class="token punctuation">(</span><span class="token hvariable">onClick</span><span class="token punctuation">)</span><br><br><span class="token hvariable">main</span> <span class="token operator">=</span><br><span class="token hvariable">Browser.sandbox</span> <span class="token punctuation">{</span> <span class="token hvariable">init</span> <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">,</span> <span class="token hvariable">update</span> <span class="token operator">=</span> <span class="token hvariable">update</span><span class="token punctuation">,</span> <span class="token hvariable">view</span> <span class="token operator">=</span> <span class="token hvariable">view</span> <span class="token punctuation">}</span><br><br><span class="token keyword">type</span> <span class="token constant">Msg</span> <span class="token operator">=</span> <span class="token constant">Increment</span> <span class="token operator">|</span> <span class="token constant">Decrement</span><br><br><span class="token hvariable">update</span> <span class="token hvariable">msg</span> <span class="token hvariable">model</span> <span class="token operator">=</span><br><span class="token keyword">case</span> <span class="token hvariable">msg</span> <span class="token keyword">of</span><br><span class="token constant">Increment</span> <span class="token operator">-&gt;</span><br>  <span class="token hvariable">model</span> <span class="token operator">+</span> <span class="token number">1</span><br><br><span class="token constant">Decrement</span> <span class="token operator">-&gt;</span><br>  <span class="token hvariable">model</span> <span class="token operator">-</span> <span class="token number">1</span><br><br><span class="token hvariable">view</span> <span class="token hvariable">model</span> <span class="token operator">=</span><br><span class="token hvariable">div</span> <span class="token punctuation">[</span><span class="token punctuation">]</span><br><span class="token punctuation">[</span> <span class="token hvariable">button</span> <span class="token punctuation">[</span> <span class="token hvariable">onClick</span> <span class="token constant">Decrement</span> <span class="token punctuation">]</span> <span class="token punctuation">[</span> <span class="token hvariable">text</span> <span class="token string">"-"</span> <span class="token punctuation">]</span><br><span class="token punctuation">,</span> <span class="token hvariable">div</span> <span class="token punctuation">[</span><span class="token punctuation">]</span> <span class="token punctuation">[</span> <span class="token hvariable">text</span> <span class="token punctuation">(</span><span class="token hvariable">String.fromInt</span> <span class="token hvariable">model</span><span class="token punctuation">)</span> <span class="token punctuation">]</span><br><span class="token punctuation">,</span> <span class="token hvariable">button</span> <span class="token punctuation">[</span> <span class="token hvariable">onClick</span> <span class="token constant">Increment</span> <span class="token punctuation">]</span> <span class="token punctuation">[</span> <span class="token hvariable">text</span> <span class="token string">"+"</span> <span class="token punctuation">]</span><br><span class="token punctuation">]</span></code></pre>
<p>can be seen <a href="https://elm-lang.org/examples/buttons">here</a> tracking counter state, even though a user may of course click the counter buttons asynchronously. Like garbage collection in JavaScript, Elm hides any imperative code dedicated to communication between asynchronous scripts from the programmer’s view. <a href="#fnref1" class="footnote-backref">↩︎</a></p>
</li>
<li id="fn2" class="footnote-item"><p>A flip-book <a href="https://softwareengineering.stackexchange.com/a/245409/369472">has been suggested</a> as a valuable mental model for state in functional programming <a href="#fnref2" class="footnote-backref">↩︎</a></p>
</li>
</ol>
</section>


<p class="footer">
    <span class="badge"><a href="/">← Home</a></span>
</p>

    </main>
    
    <footer>
      <ul>
        <li>
          <a href="/feed/feed.xml">rss</a>
        </li><li>
          <a class="contact" target="_blank" href="https://github.com/jbmilgrom/blogg">source</a>
        </li>
        <li>
          <a href="/post/hello-world">how was I built?</a>
        </li>
        <li>
          <a class="contact" target="_blank" href="https://twitter.com/jbmilgrom">twitter</a>
        </li>
        <li>
          <a class="contact" target="_blank" href="https://stackoverflow.com/users/3645851/jbmilgrom">stackoverflow</a>
        </li>
        <li>
          <a class="contact" target="_blank" href="mailto:<EMAIL>">email</a>
        </li>
      </ul>
    </footer>
  

</body></html>
