# Snapshot report for `test/integration/npr/index.js`

The actual snapshot is saved in `index.js.snap`.

Generated by [AVA](https://avajs.dev).

## npr

> Snapshot 1

    {
      audio: 'https://play.podtrac.com/510289/edge1.pod.npr.org/anon.npr-mp3/npr/pmoney/2020/12/20201223_pmoney_pmpod1054.mp3?orgId=1&topicId=1125&d=1477&p=510289&story=949764249&t=podcast&e=949764249&dl=1&siteplayer=true&size=23584686&awCollectionId=510289&awEpisodeId=949764249&dl=1',
      author: '<PERSON><PERSON>',
      date: '2020-12-24T03:36:00.000Z',
      description: 'A global pandemic might not be the best time to try something new with technology. But Taiwan decided to do it anyway. | Subscribe to our weekly newsletter here.',
      image: 'https://media.npr.org/assets/img/2020/12/23/gettyimages-1199493836_wide-b0f8c2e44d3617f2f5ff7f4dceff064ecad00439.jpg?s=1400',
      lang: 'en',
      logo: 'https://static-assets.npr.org/static/images/favicon/favicon-180x180.png',
      publisher: 'NPR',
      title: 'Fork The Government : Planet Money',
      url: 'https://www.npr.org/2020/12/23/949764249/fork-the-government',
      video: null,
    }
