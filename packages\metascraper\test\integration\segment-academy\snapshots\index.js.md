# Snapshot report for `test/integration/segment-academy/index.js`

The actual snapshot is saved in `index.js.snap`.

Generated by [AVA](https://avajs.dev).

## segment-academy

> Snapshot 1

    {
      audio: null,
      author: null,
      date: null,
      description: 'When deciding whether to track users on the server or on the client, there are many gotchas and factors to consider. Here is a short guide with pros and cons of each.',
      image: 'https://www.segment.com/academy',
      lang: null,
      publisher: 'Segment',
      title: 'When to Track on the Client vs. Server',
      url: 'https://www.segment.com/academy/collecting-data/when-to-track-on-the-client-vs-server/',
      video: null,
    }
