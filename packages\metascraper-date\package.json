{"name": "metascraper-date", "description": "Get date property from HTML markup", "homepage": "https://github.com/microlinkhq/metascraper/packages/metascraper-date", "version": "5.49.1", "types": "src/index.d.ts", "main": "src/index.js", "author": {"email": "<EMAIL>", "name": "microlink.io", "url": "https://microlink.io"}, "repository": {"directory": "packages/metascraper-date", "type": "git", "url": "git+https://github.com/microlinkhq/metascraper.git#master"}, "bugs": {"url": "https://github.com/microlinkhq/metascraper/issues"}, "keywords": ["date", "metascraper"], "dependencies": {"@metascraper/helpers": "workspace:*"}, "engines": {"node": ">= 16"}, "files": ["src"], "scripts": {"test": "exit 0"}, "license": "MIT"}