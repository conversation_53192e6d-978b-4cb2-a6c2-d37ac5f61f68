<!DOCTYPE html>
<html id="atomic" class="desktop bkt ua-chrome ua-94.0 JsEnabled jsenabled"
  lang="en-US">

<head>
  <script async="" src="https://s.yimg.com/aaq/vzm/cs_1.2.0.js"></script>
  <script>
    window.performance.mark('PageStart');
    document.documentElement.className += ' JsEnabled jsenabled';

  </script>
  <title>All GitHub users can keep their code private | Engadget</title>
  <meta http-equiv="content-type" content="text/html; charset=utf-8">
  <meta http-equiv="content-type" content="text/html; charset=utf-8">
  <meta http-equiv="x-dns-prefetch-control" content="on">
  <meta http-equiv="X-UA-Compatible" content="chrome=1">
  <meta name="description"
    content="Historically, if you've wanted to create a private repository on GitHub, you had to be a paying user, but that's about to change.">
  <meta property="og:description"
    content="Historically, if you've wanted to create a private repository on GitHub, you had to be a paying user, but that's about to change.">
  <meta property="og:image"
    content="https://s.yimg.com/uu/api/res/1.2/C.Injrjs8P1U7ExbkYEEcg--~B/aD0xMDY5O3c9MTYwMDthcHBpZD15dGFjaHlvbg--/https://o.aolcdn.com/images/dims?crop=4834%2C3230%2C0%2C0&amp;quality=85&amp;format=jpg&amp;resize=1600%2C1069&amp;image_uri=https://s.yimg.com/os/creatr-images/2019-01/2d1e22f0-12ae-11e9-bae7-60d640081814&amp;client=a1acac3e1b3290917d92&amp;signature=269ae0af3a1a1772ffea6759d126791b9ad25184">
  <meta property="og:url"
    content="https://www.engadget.com/2019-01-07-all-github-users-keep-code-private.html">
  <meta name="twitter:image"
    content="https://s.yimg.com/uu/api/res/1.2/C.Injrjs8P1U7ExbkYEEcg--~B/aD0xMDY5O3c9MTYwMDthcHBpZD15dGFjaHlvbg--/https://o.aolcdn.com/images/dims?crop=4834%2C3230%2C0%2C0&amp;quality=85&amp;format=jpg&amp;resize=1600%2C1069&amp;image_uri=https://s.yimg.com/os/creatr-images/2019-01/2d1e22f0-12ae-11e9-bae7-60d640081814&amp;client=a1acac3e1b3290917d92&amp;signature=269ae0af3a1a1772ffea6759d126791b9ad25184">
  <meta name="twitter:title"
    content="All GitHub users can keep their code private | Engadget">
  <meta property="og:title"
    content="All GitHub users can keep their code private | Engadget">
  <meta name="oath:guce:consent-host" content="guce.engadget.com">
  <meta property="og:type" content="website">
  <script type="text/javascript">
    var AdsServicePosition = {};
    var AdsClientPosition = {};
    var JacCallbacks = {};

  </script>
  <script type="text/javascript">
    var uacCallbacks = {};

  </script>
  <meta name="viewport"
    content="width=device-width,initial-scale=1,minimum-scale=1">
  <meta http-equiv="content-type" content="text/html; charset=utf-8">
  <meta name="google-site-verification"
    content="wLXjbTY6pixubu5bTDip5LFbmV3y14B-1FuGRBA608U">
  <meta name="google-site-verification"
    content="tWejEOjeALosEomCYTYn5Ps9e_bfdSDFep3l5tdwP30">
  <meta name="google-site-verification"
    content="1GwTnKM_I1dlzvY6ZgkdMD7npjzC-zbHHs9BXlA8BrI">
  <meta name="google-site-verification"
    content="2AxZaoCWIVXn4j_PSZpxB_gj8X9Y2rfzSyOtPXO_wEI">
  <meta name="google-site-verification"
    content="WHAdu9KkehGzlorxbvJ2y_5amqxc3cg7tkZPOq5kfHE">
  <meta name="google-site-verification"
    content="xX3g98CIOMeGJbk03PNHNS-NQMGfB0LonZXXvhFfBlc">
  <meta name="google-site-verification"
    content="xd57-NQw8NoYtNTyi0qomvWufyqOYmCYgM2K2Y8hNrE">
  <meta name="bitly-verification" content="f57da7265213">
  <meta name="p:domain_verify" content="fb69533f2acad78bdd89abf093c35a28">
  <meta name="msvalidate.01" content="5ABD8A078F3356F3A6A8C8643C31FB8F">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="application-name" content="Engadget">
  <meta property="fb:pages" content="5738237369">
  <meta property="fb:admins" content="11710688">
  <meta property="fb:app_id" content="464864107046584">
  <meta property="og:locale" content="en_US">
  <meta property="og:site_name" content="Engadget">
  <meta name="twitter:site" content="`Engadget`">
  <meta name="msapplication-TileColor" content="#2B2D32">
  <meta name="msapplication-TileImage"
    content="https://s.yimg.com/kw/assets/eng-e-558.png">
  <meta name="msapplication-square70x70logo"
    content="https://s.yimg.com/kw/assets/eng-e-128.png">
  <meta name="msapplication-square150x150logo"
    content="https://s.yimg.com/kw/assets/eng-e-270.png">
  <meta name="msapplication-wide310x150logo"
    content="https://s.yimg.com/kw/assets/eng-e-558x270.png">
  <meta name="msapplication-square310x310logo"
    content="https://s.yimg.com/kw/assets/images/eng-e-558.png">
  <meta name="msapplication-notification"
    content="frequency=30;polling-uri=https://notifications.buildmypinnedsite.com/?feed=https://www.engadget.com/rss.xml&amp;id=1;polling-uri2=https://notifications.buildmypinnedsite.com/?feed=https://www.engadget.com/rss.xml&amp;id=2;polling-uri3=https://notifications.buildmypinnedsite.com/?feed=https://www.engadget.com/rss.xml&amp;id=3;polling-uri4=https://notifications.buildmypinnedsite.com/?feed=https://www.engadget.com/rss.xml&amp;id=4;polling-uri5=https://notifications.buildmypinnedsite.com/?feed=https://www.engadget.com/rss.xml&amp;id=5; cycle=1">
  <meta name="apple-touch-fullscreen" content="yes">
  <link rel="alternate" type="application/rss+xml" title="Engadget"
    href="//www.engadget.com/rss.xml">
  <link rel="apple-touch-icon" sizes="57x57"
    href="https://s.yimg.com/kw/assets/apple-touch-icon-57x57.png">
  <link rel="apple-touch-icon" sizes="114x114"
    href="https://s.yimg.com/kw/assets/apple-touch-icon-114x114.png">
  <link rel="apple-touch-icon" sizes="72x72"
    href="https://s.yimg.com/kw/assets/apple-touch-icon-72x72.png">
  <link rel="apple-touch-icon" sizes="144x144"
    href="https://s.yimg.com/kw/assets/apple-touch-icon-144x144.png">
  <link rel="apple-touch-icon" sizes="60x60"
    href="https://s.yimg.com/kw/assets/apple-touch-icon-60x60.png">
  <link rel="apple-touch-icon" sizes="120x120"
    href="https://s.yimg.com/kw/assets/apple-touch-icon-120x120.png">
  <link rel="apple-touch-icon" sizes="76x76"
    href="https://s.yimg.com/kw/assets/apple-touch-icon-76x76.png">
  <link rel="apple-touch-icon" sizes="152x152"
    href="https://s.yimg.com/kw/assets/apple-touch-icon-152x152.png">
  <link rel="icon" type="image/png"
    href="https://s.yimg.com/kw/assets/favicon-16x16.png" sizes="16x16">
  <link rel="icon" type="image/png"
    href="https://s.yimg.com/kw/assets/favicon-32x32.png" sizes="32x32">
  <link rel="icon" type="image/png"
    href="https://s.yimg.com/kw/assets/favicon-96x96.png" sizes="96x96">
  <link rel="icon" type="image/png"
    href="https://s.yimg.com/kw/assets/favicon-160x160.png" sizes="160x160">
  <link rel="search" type="application/opensearchdescription+xml"
    href="https://search.engadget.com/opensearch.xml" title="Engadget">
  <link rel="alternate" hreflang="zh-Hant-HK"
    href="https://chinese.engadget.com/">
  <link rel="alternate" hreflang="zh-Hans-CN" href="https://cn.engadget.com/">
  <link rel="alternate" hreflang="ja-JP" href="https://japanese.engadget.com/">
  <link rel="amphtml"
    href="https://www.engadget.com/amp/2019-01-07-all-github-users-keep-code-private.html">
  <link rel="canonical"
    href="https://www.engadget.com/2019-01-07-all-github-users-keep-code-private.html">
  <meta name="post_id" content="23636374">
  <meta property="article:tag" content="gear">
  <meta property="article:tag" content="github">
  <meta property="article:tag" content="internet">
  <meta property="article:tag" content="microsoft">
  <meta property="article:tag" content="private">
  <meta property="article:tag" content="services">
  <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Article",
      "url": "https://www.engadget.com/2019-01-07-all-github-users-keep-code-private.html",
      "author": [{
        "@type": "Person",
        "url": "https://www.engadget.com/about/editors/mallory-locklear",
        "name": "Mallory Locklear"
      }],
      "headline": "All GitHub users can keep their code private",
      "datePublished": "Mon, Jan 7 2019 15:37:00 EST",
      "mainEntityOfPage": "True",
      "thumbnailUrl": "https://s.yimg.com/uu/api/res/1.2/UdtkLCXFNoeaVmiAAtPvTw--~B/Zmk9ZmlsbDtoPTQ1MTt3PTY3NTthcHBpZD15dGFjaHlvbg--/https://s.yimg.com/uu/api/res/1.2/K0jagCHTBRKuVb_rt7LQ_g--~B/aD0xMDY5O3c9MTYwMDthcHBpZD15dGFjaHlvbg--/https://o.aolcdn.com/images/dims?crop=4834%2C3230%2C0%2C0&quality=85&format=jpg&resize=1600%2C1069&image_uri=https%3A%2F%2Fs.yimg.com%2Fos%2Fcreatr-images%2F2019-01%2F2d1e22f0-12ae-11e9-bae7-60d640081814&client=a1acac3e1b3290917d92&signature=269ae0af3a1a1772ffea6759d126791b9ad25184.cf.webp",
      "image": [{
        "@type": "ImageObject",
        "url": "https://s.yimg.com/uu/api/res/1.2/UdtkLCXFNoeaVmiAAtPvTw--~B/Zmk9ZmlsbDtoPTQ1MTt3PTY3NTthcHBpZD15dGFjaHlvbg--/https://s.yimg.com/uu/api/res/1.2/K0jagCHTBRKuVb_rt7LQ_g--~B/aD0xMDY5O3c9MTYwMDthcHBpZD15dGFjaHlvbg--/https://o.aolcdn.com/images/dims?crop=4834%2C3230%2C0%2C0&quality=85&format=jpg&resize=1600%2C1069&image_uri=https%3A%2F%2Fs.yimg.com%2Fos%2Fcreatr-images%2F2019-01%2F2d1e22f0-12ae-11e9-bae7-60d640081814&client=a1acac3e1b3290917d92&signature=269ae0af3a1a1772ffea6759d126791b9ad25184.cf.webp",
        "width": "1600px",
        "height": "1069px"
      }],
      "articleBody": "Historically, if you've wanted to create a private repository on GitHub, you had to be a paying user, but that's about to change. Starting today, free GitHub users will have access to unlimited private projects as long as there are three or fewer collaborators on board. For larger projects, you'll have to join a paid plan or make your code public, as GitHub isn't changing how it manages public repositories.\n\n%BlockedContent-twitter%\n \n\n\nMicrosoft acquired GitHub last year for $7.5 billion. GitHub CEO Nat Friedman said at the time that the team would be focused on making GitHub more accessible to developers around the world and ensuring reliability, security and performance. He also reiterated that GitHub would continue to operate independently. \"We love GitHub because of the deep care and thoughtfulness that goes into every facet of the developer's experience,\" Friedman wrote in a blog post. \"I understand and respect this, and know that we will continue to build tasteful, snappy, polished tools that developers love.\"\n\nFriedman said today that unlimited free private repositories has been the most requested feature from GitHub users. The company also announced GitHub Enterprise, which brings together Enterprise Cloud and Enterprise Server -- formerly GitHub Business Cloud and GitHub Enterprise, respectively -- into one, unified product. The private repositories for free users are rolling out today.",

      "articleSection": "Gear",
      "keywords": "gear,github,internet,microsoft,private,services",
      "publisher": [{
        "@type": "Organization",
        "name": "Engadget",
        "url": "https://www.engadget.com",
        "logo": [{
          "@type": "ImageObject",
          "url": "https://www.engadget.com/assets/images/eng-e-128.png",
          "width": "128px",
          "height": "128px"
        }]
      }],
      "dateModified": "Thu, May 13 2021 14:01:31 EDT"
    }

  </script>
  <meta name="robots" content="max-image-preview:large">
  <meta property="article:author"
    content="https://www.engadget.com/about/editors/mallory-locklear">
  <meta name="twitter:creator" content="@mallorylocklear">
  <link rel="stylesheet" type="text/css"
    href="https://s.yimg.com/kw/engadget/mod/css/app.01a86c2e65b3d09b5924dc05341e8d56.css">
  <link rel="stylesheet" type="text/css"
    href="https://s.yimg.com/kw/engadget/mod/css/moduleCSS.3dd0c6579b896a5308a2c5f2a8a1242f.css">
  <link rel="stylesheet" type="text/css"
    href="https://s.yimg.com/kw/engadget/mod/css/engad-header/custom.21f98fc0.css">
  <link rel="stylesheet" type="text/css"
    href="https://s.yimg.com/kw/engadget/mod/css/engad-nav/custom.a195b184.css">
  <link rel="stylesheet" type="text/css"
    href="https://s.yimg.com/kw/engadget/mod/css/engad-header/atomic.ltr.59c820e2.css">
  <link rel="stylesheet" type="text/css"
    href="https://s.yimg.com/kw/engadget/mod/css/engad-nav/atomic.ltr.15a7ab83.css">
  <link rel="stylesheet" type="text/css"
    href="https://s.yimg.com/kw/engadget/mod/css/engadget-article/atomic.ltr.feed0ee7.css">
  <link rel="stylesheet" type="text/css"
    href="https://s.yimg.com/kw/engadget/mod/css/engad-footer/atomic.ltr.19912ef5.css">
  <script type="text/javascript">
    ! function () {
      var e, t, n, i, r = {
          passive: !0,
          capture: !0
        },
        a = new Date,
        o = function () {
          i = [], t = -1, e = null, f(addEventListener)
        },
        c = function (i, r) {
          e || (e = r, t = i, n = new Date, f(removeEventListener), u())
        },
        u = function () {
          if (t >= 0 && t < n - a) {
            var r = {
              entryType: "first-input",
              name: e.type,
              target: e.target,
              cancelable: e.cancelable,
              startTime: e.timeStamp,
              processingStart: e.timeStamp + t
            };
            i.forEach((function (e) {
              e(r)
            })), i = []
          }
        },
        s = function (e) {
          if (e.cancelable) {
            var t = (e.timeStamp > 1e12 ? new Date : performance.now()) - e
              .timeStamp;
            "pointerdown" == e.type ? function (e, t) {
              var n = function () {
                  c(e, t), a()
                },
                i = function () {
                  a()
                },
                a = function () {
                  removeEventListener("pointerup", n, r), removeEventListener(
                    "pointercancel", i, r)
                };
              addEventListener("pointerup", n, r), addEventListener(
                "pointercancel", i, r)
            }(t, e) : c(t, e)
          }
        },
        f = function (e) {
          ["mousedown", "keydown", "touchstart", "pointerdown"].forEach((
            function (t) {
              return e(t, s, r)
            }))
        },
        p = "hidden" === document.visibilityState ? 0 : 1 / 0;
      addEventListener("visibilitychange", (function e(t) {
        "hidden" === document.visibilityState && (p = t.timeStamp,
          removeEventListener("visibilitychange", e, !0))
      }), !0);
      o(), self.webVitals = {
        firstInputPolyfill: function (e) {
          i.push(e), u()
        },
        resetFirstInputPolyfill: o,
        get firstHiddenTime() {
          return p
        }
      }
    }();

  </script>
  <script type="text/javascript" src="https://s.yimg.com/ss/rapid-3.53.28.js">
  </script>
  <script type="text/javascript"
    src="https://consent.cmp.oath.com/cmpStub.min.js"></script>
  <script type="text/javascript" src="https://consent.cmp.oath.com/cmp.js"
    async=""></script>
  <script type="text/javascript" src="https://s.yimg.com/oa/consent.js"
    async=""></script>
  <style>
    .flipX video::-webkit-media-text-track-display {
      transform: matrix(-1, 0, 0, 1, 0, 0) !important;
    }

    .flipXY video::-webkit-media-text-track-display {
      transform: matrix(-1, 0, 0, -1, 0, 0) !important;
    }

    .flipXYX video::-webkit-media-text-track-display {
      transform: matrix(1, 0, 0, -1, 0, 0) !important;
    }

  </style>
</head>

<body dir="ltr" class="M(0) D(f) wafer-drawer Ovx(h) wafer-drawer-closed"
  cz-shortcut-listen="true" data-new-gr-c-s-check-loaded="14.1034.0"
  data-gr-ext-installed="">
  <script type="text/javascript">
    // From https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/keys
    if (!Object.keys) {
      Object.keys = (function () {
        'use strict';
        var hasOwnProperty = Object.prototype.hasOwnProperty,
          hasDontEnumBug = !({
            toString: null
          }).propertyIsEnumerable('toString'),
          dontEnums = [
            'toString',
            'toLocaleString',
            'valueOf',
            'hasOwnProperty',
            'isPrototypeOf',
            'propertyIsEnumerable',
            'constructor'
          ],
          dontEnumsLength = dontEnums.length;

        return function (obj) {
          if (typeof obj !== 'function' && (typeof obj !== 'object' ||
              obj === null)) {
            throw new TypeError('Object.keys called on non-object');
          }

          var result = [],
            prop, i;

          for (prop in obj) {
            if (hasOwnProperty.call(obj, prop)) {
              result.push(prop);
            }
          }

          if (hasDontEnumBug) {
            for (i = 0; i < dontEnumsLength; i++) {
              if (hasOwnProperty.call(obj, dontEnums[i])) {
                result.push(dontEnums[i]);
              }
            }
          }
          return result;
        };
      }());
    }
    // Object.entries polyfill
    if (!Object.entries) {
      Object.entries = function (obj) {
        var ownProps = Object.keys(obj),
          i = ownProps.length,
          resArray = new Array(i); // preallocate the Array
        while (i--)
          resArray[i] = [ownProps[i], obj[ownProps[i]]];

        return resArray;
      };
    }
    const postIDdata = document.getElementsByName('post_id') && document
      .getElementsByName('post_id')[0];
    const postID = postIDdata && postIDdata.content;
    // cmsid will be undefined unless its an article page.
    window.JAC_CONFIG = {
      service: {
        adServer: {
          '1AS': {
            region: 'US',
            params: {
              pubmarket: 'us',
              publisher: 'engadget',
              category: 'main',
              entryid: 'bsid:' + postID
            }
          }
        },
        host: 'https://jill.fc.yahoo.com/',
        debug: true,
        site: {
          name: 'engadget',
          spaceId: '**********'
        },
        positions: AdsServicePosition
      },
      client: {
        positions: AdsClientPosition,
        onReady: function () {
          try {
            if (JacCallbacks && Object.entries(JacCallbacks).length !== 0) {
              for (let [position, callbacks] of Object.entries(
                JacCallbacks)) {
                JAC.on('AD_SIZE_CHANGED', (event) => {
                  if (event.meta.positionName === position && callbacks[
                      event.meta.height]) {
                    callbacks[event.meta.height]();
                  }
                });
              }
            }
          } catch (e) {
            console.warn(e);
          }
        }
      }
    };

  </script>
  <div class="drawer-swipeable drawer-transition">
    <div
      class="drawer-menu-container Pos(f) T(0) Start(-225px) Trs(all 0.3s ease-out) H(100%) Ovy(s) Ovx(h)">
      <div id="module-drawer-menu" class="wafer-rapid-module">
        <div>
          <div
            class="W(225px) H(100%) Bgc(engadgetSteelGray) D(f) Fld(c) Jc(sb)"
            data-component="DrawerMenu">
            <ul class="M(0) P(0) D(f) Fld(c) Flx(1) Ovx(h) Ovy(s)">
              <li data-component="NavItem" class=""><a data-component="NavLink"
                  class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetFontLightGray)  C(#engadgetWhiteSmoke):h menu-toggle Pos(r) Whs(nw) Mstart(5px) Mstart(0)!--md Bdts(s) Bdtw(1px) Bdtc(engadgetLightGray) Fz(16px)!--md H(65px)!--md Pstart(20px) P(10px)!--lg Lh(20px)--md"
                  href="/" data-wf-toggle-class="false"
                  data-ylk="elm:navcat;itc:0;sec:navrail" data-rapid_p="1"
                  data-v9y="0">Home</a></li>
              <li data-component="NavItem" class=""><a data-component="NavLink"
                  class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) wafer-toggle C(engadgetFontLightGray) C(#engadgetWhiteSmoke):h menu-toggle Pos(r) Whs(nw) Mstart(5px) Mstart(0)!--md Bdts(s) Bdtw(1px) Bdtc(engadgetLightGray) Fz(16px)!--md H(65px)!--md Pstart(20px) P(10px)!--lg Lh(20px)--md has-toggle-click has-wafer-click"
                  href="#"
                  data-wf-toggle-class="click:toggle:active;click:toggle:Bgc(engadgetGray);click:toggle:C(white)"
                  data-ylk="elm:navcat;itc:1;sec:navrail" data-rapid_p="2"
                  data-v9y="0">Products<svg
                    class="Fill(engadgetFontGray) Stk(engadgetFontGray) Px(5px) Pt(3px) Cur(p)"
                    width="16" style="stroke-width:0;vertical-align:bottom"
                    height="16" viewBox="0 0 48 48" data-icon="caret-down">
                    <path
                      d="M24.21 33.173l12.727-12.728c.78-.78.78-2.048 0-2.828-.78-.78-2.047-.78-2.828 0l-9.9 9.9-9.9-9.9c-.78-.78-2.047-.78-2.827 0-.78.78-.78 2.047 0 2.828L24.21 33.173z">
                    </path>
                  </svg></a>
                <ul data-component="SubNav"
                  class="menu-options sub-nav Bxz(bb) D(n) active+D(f) Miw(200px) Fxd(c) Pos(a) T(100%) Bgc(engadgetGray) Px(0) Pt(10px) Pb(20px) Z(5001) Pos(r)!--md T(a)!--md Pt(0)!--md  Pend(8px)">
                  <li data-component="SubNavItem"
                    class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h">
                    <a data-component="NavLink"
                      class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                      href="/products/phones/"
                      data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                      data-rapid_p="3" data-v9y="1">Phones</a></li>
                  <li data-component="SubNavItem"
                    class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h">
                    <a data-component="NavLink"
                      class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                      href="/products/laptops/"
                      data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                      data-rapid_p="4" data-v9y="1">Laptops</a></li>
                  <li data-component="SubNavItem"
                    class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h">
                    <a data-component="NavLink"
                      class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                      href="/products/headphones/"
                      data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                      data-rapid_p="5" data-v9y="1">Headphones</a></li>
                  <li data-component="SubNavItem"
                    class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h">
                    <a data-component="NavLink"
                      class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                      href="/products/gaming-consoles-home/"
                      data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                      data-rapid_p="6" data-v9y="1">Gaming Consoles</a></li>
                  <li data-component="SubNavItem"
                    class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h">
                    <a data-component="NavLink"
                      class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                      href="/products/wearables/"
                      data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                      data-rapid_p="7" data-v9y="1">Smartwatches</a></li>
                  <li data-component="SubNavItem"
                    class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h">
                    <a data-component="NavLink"
                      class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                      href="/products/"
                      data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                      data-rapid_p="8" data-v9y="1">View all</a></li>
                </ul>
              </li>
              <li data-component="NavItem" class=""><a data-component="NavLink"
                  class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) wafer-toggle C(engadgetFontLightGray) C(#engadgetWhiteSmoke):h menu-toggle Pos(r) Whs(nw) Mstart(5px) Mstart(0)!--md Bdts(s) Bdtw(1px) Bdtc(engadgetLightGray) Fz(16px)!--md H(65px)!--md Pstart(20px) P(10px)!--lg Lh(20px)--md has-toggle-click has-wafer-click"
                  href="#"
                  data-wf-toggle-class="click:toggle:active;click:toggle:Bgc(engadgetGray);click:toggle:C(white)"
                  data-ylk="elm:navcat;itc:1;sec:navrail" data-rapid_p="9"
                  data-v9y="0">Reviews<svg
                    class="Fill(engadgetFontGray) Stk(engadgetFontGray) Px(5px) Pt(3px) Cur(p)"
                    width="16" style="stroke-width:0;vertical-align:bottom"
                    height="16" viewBox="0 0 48 48" data-icon="caret-down">
                    <path
                      d="M24.21 33.173l12.727-12.728c.78-.78.78-2.048 0-2.828-.78-.78-2.047-.78-2.828 0l-9.9 9.9-9.9-9.9c-.78-.78-2.047-.78-2.827 0-.78.78-.78 2.047 0 2.828L24.21 33.173z">
                    </path>
                  </svg></a>
                <ul data-component="SubNav"
                  class="menu-options sub-nav Bxz(bb) D(n) active+D(f) Miw(200px) Fxd(c) Pos(a) T(100%) Bgc(engadgetGray) Px(0) Pt(10px) Pb(20px) Z(5001) Pos(r)!--md T(a)!--md Pt(0)!--md  Pend(8px)">
                  <li data-component="SubNavItem"
                    class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h">
                    <a data-component="NavLink"
                      class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                      href="/tag/best%20tech/"
                      data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                      data-rapid_p="10" data-v9y="1">Best in Tech</a></li>
                  <li data-component="SubNavItem"
                    class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h">
                    <a data-component="NavLink"
                      class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                      href="/tag/hands-on/"
                      data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                      data-rapid_p="11" data-v9y="1">Hands-On</a></li>
                  <li data-component="SubNavItem"
                    class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h">
                    <a data-component="NavLink"
                      class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                      href="/reviews/"
                      data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                      data-rapid_p="12" data-v9y="1">View all</a></li>
                </ul>
              </li>
              <li data-component="NavItem" class=""><a data-component="NavLink"
                  class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) wafer-toggle C(engadgetFontLightGray) C(#engadgetWhiteSmoke):h menu-toggle Pos(r) Whs(nw) Mstart(5px) Mstart(0)!--md Bdts(s) Bdtw(1px) Bdtc(engadgetLightGray) Fz(16px)!--md H(65px)!--md Pstart(20px) P(10px)!--lg Lh(20px)--md has-toggle-click has-wafer-click"
                  href="#"
                  data-wf-toggle-class="click:toggle:active;click:toggle:Bgc(engadgetGray);click:toggle:C(white)"
                  data-ylk="elm:navcat;itc:1;sec:navrail" data-rapid_p="13"
                  data-v9y="0">Gaming<svg
                    class="Fill(engadgetFontGray) Stk(engadgetFontGray) Px(5px) Pt(3px) Cur(p)"
                    width="16" style="stroke-width:0;vertical-align:bottom"
                    height="16" viewBox="0 0 48 48" data-icon="caret-down">
                    <path
                      d="M24.21 33.173l12.727-12.728c.78-.78.78-2.048 0-2.828-.78-.78-2.047-.78-2.828 0l-9.9 9.9-9.9-9.9c-.78-.78-2.047-.78-2.827 0-.78.78-.78 2.047 0 2.828L24.21 33.173z">
                    </path>
                  </svg></a>
                <ul data-component="SubNav"
                  class="menu-options sub-nav Bxz(bb) D(n) active+D(f) Miw(200px) Fxd(c) Pos(a) T(100%) Bgc(engadgetGray) Px(0) Pt(10px) Pb(20px) Z(5001) Pos(r)!--md T(a)!--md Pt(0)!--md  Pend(8px)">
                  <li data-component="SubNavItem"
                    class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h">
                    <a data-component="NavLink"
                      class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                      href="/tag/best%20games/"
                      data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                      data-rapid_p="14" data-v9y="1">Best Games</a></li>
                  <li data-component="SubNavItem"
                    class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h">
                    <a data-component="NavLink"
                      class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                      href="/tag/playstation/"
                      data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                      data-rapid_p="15" data-v9y="1">PlayStation</a></li>
                  <li data-component="SubNavItem"
                    class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h">
                    <a data-component="NavLink"
                      class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                      href="/tag/nintendo/"
                      data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                      data-rapid_p="16" data-v9y="1">Nintendo</a></li>
                  <li data-component="SubNavItem"
                    class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h">
                    <a data-component="NavLink"
                      class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                      href="/tag/xbox/"
                      data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                      data-rapid_p="17" data-v9y="1">Xbox</a></li>
                  <li data-component="SubNavItem"
                    class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h">
                    <a data-component="NavLink"
                      class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                      href="/gaming/"
                      data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                      data-rapid_p="18" data-v9y="1">View all</a></li>
                </ul>
              </li>
              <li data-component="NavItem" class=""><a data-component="NavLink"
                  class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) wafer-toggle C(engadgetFontLightGray) C(#engadgetWhiteSmoke):h menu-toggle Pos(r) Whs(nw) Mstart(5px) Mstart(0)!--md Bdts(s) Bdtw(1px) Bdtc(engadgetLightGray) Fz(16px)!--md H(65px)!--md Pstart(20px) P(10px)!--lg Lh(20px)--md has-toggle-click has-wafer-click"
                  href="#"
                  data-wf-toggle-class="click:toggle:active;click:toggle:Bgc(engadgetGray);click:toggle:C(white)"
                  data-ylk="elm:navcat;itc:1;sec:navrail" data-rapid_p="19"
                  data-v9y="0">Gear<svg
                    class="Fill(engadgetFontGray) Stk(engadgetFontGray) Px(5px) Pt(3px) Cur(p)"
                    width="16" style="stroke-width:0;vertical-align:bottom"
                    height="16" viewBox="0 0 48 48" data-icon="caret-down">
                    <path
                      d="M24.21 33.173l12.727-12.728c.78-.78.78-2.048 0-2.828-.78-.78-2.047-.78-2.828 0l-9.9 9.9-9.9-9.9c-.78-.78-2.047-.78-2.827 0-.78.78-.78 2.047 0 2.828L24.21 33.173z">
                    </path>
                  </svg></a>
                <ul data-component="SubNav"
                  class="menu-options sub-nav Bxz(bb) D(n) active+D(f) Miw(200px) Fxd(c) Pos(a) T(100%) Bgc(engadgetGray) Px(0) Pt(10px) Pb(20px) Z(5001) Pos(r)!--md T(a)!--md Pt(0)!--md  Pend(8px)">
                  <li data-component="SubNavItem"
                    class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h">
                    <a data-component="NavLink"
                      class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                      href="/tag/amazon/"
                      data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                      data-rapid_p="20" data-v9y="1">Amazon</a></li>
                  <li data-component="SubNavItem"
                    class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h">
                    <a data-component="NavLink"
                      class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                      href="/tag/apple/"
                      data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                      data-rapid_p="21" data-v9y="1">Apple</a></li>
                  <li data-component="SubNavItem"
                    class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h">
                    <a data-component="NavLink"
                      class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                      href="/tag/google/"
                      data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                      data-rapid_p="22" data-v9y="1">Google</a></li>
                  <li data-component="SubNavItem"
                    class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h">
                    <a data-component="NavLink"
                      class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                      href="/tag/microsoft/"
                      data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                      data-rapid_p="23" data-v9y="1">Microsoft</a></li>
                  <li data-component="SubNavItem"
                    class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h">
                    <a data-component="NavLink"
                      class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                      href="/tag/samsung/"
                      data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                      data-rapid_p="24" data-v9y="1">Samsung</a></li>
                  <li data-component="SubNavItem"
                    class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h">
                    <a data-component="NavLink"
                      class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                      href="/gear/"
                      data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                      data-rapid_p="25" data-v9y="1">View all</a></li>
                </ul>
              </li>
              <li data-component="NavItem" class=""><a data-component="NavLink"
                  class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) wafer-toggle C(engadgetFontLightGray) C(#engadgetWhiteSmoke):h menu-toggle Pos(r) Whs(nw) Mstart(5px) Mstart(0)!--md Bdts(s) Bdtw(1px) Bdtc(engadgetLightGray) Fz(16px)!--md H(65px)!--md Pstart(20px) P(10px)!--lg Lh(20px)--md has-toggle-click has-wafer-click"
                  href="#"
                  data-wf-toggle-class="click:toggle:active;click:toggle:Bgc(engadgetGray);click:toggle:C(white)"
                  data-ylk="elm:navcat;itc:1;sec:navrail" data-rapid_p="26"
                  data-v9y="0">Entertainment<svg
                    class="Fill(engadgetFontGray) Stk(engadgetFontGray) Px(5px) Pt(3px) Cur(p)"
                    width="16" style="stroke-width:0;vertical-align:bottom"
                    height="16" viewBox="0 0 48 48" data-icon="caret-down">
                    <path
                      d="M24.21 33.173l12.727-12.728c.78-.78.78-2.048 0-2.828-.78-.78-2.047-.78-2.828 0l-9.9 9.9-9.9-9.9c-.78-.78-2.047-.78-2.827 0-.78.78-.78 2.047 0 2.828L24.21 33.173z">
                    </path>
                  </svg></a>
                <ul data-component="SubNav"
                  class="menu-options sub-nav Bxz(bb) D(n) active+D(f) Miw(200px) Fxd(c) Pos(a) T(100%) Bgc(engadgetGray) Px(0) Pt(10px) Pb(20px) Z(5001) Pos(r)!--md T(a)!--md Pt(0)!--md  Pend(8px)">
                  <li data-component="SubNavItem"
                    class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h">
                    <a data-component="NavLink"
                      class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                      href="/tag/movies/"
                      data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                      data-rapid_p="27" data-v9y="1">Movies</a></li>
                  <li data-component="SubNavItem"
                    class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h">
                    <a data-component="NavLink"
                      class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                      href="/tag/music/"
                      data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                      data-rapid_p="28" data-v9y="1">Music</a></li>
                  <li data-component="SubNavItem"
                    class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h">
                    <a data-component="NavLink"
                      class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                      href="/tag/television/"
                      data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                      data-rapid_p="29" data-v9y="1">TV</a></li>
                  <li data-component="SubNavItem"
                    class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h">
                    <a data-component="NavLink"
                      class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                      href="/tag/youtube/"
                      data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                      data-rapid_p="30" data-v9y="1">YouTube</a></li>
                  <li data-component="SubNavItem"
                    class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h">
                    <a data-component="NavLink"
                      class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                      href="/entertainment/"
                      data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                      data-rapid_p="31" data-v9y="1">View all</a></li>
                </ul>
              </li>
              <li data-component="NavItem" class=""><a data-component="NavLink"
                  class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) wafer-toggle C(engadgetFontLightGray) C(#engadgetWhiteSmoke):h menu-toggle Pos(r) Whs(nw) Mstart(5px) Mstart(0)!--md Bdts(s) Bdtw(1px) Bdtc(engadgetLightGray) Fz(16px)!--md H(65px)!--md Pstart(20px) P(10px)!--lg Lh(20px)--md has-toggle-click has-wafer-click"
                  href="#"
                  data-wf-toggle-class="click:toggle:active;click:toggle:Bgc(engadgetGray);click:toggle:C(white)"
                  data-ylk="elm:navcat;itc:1;sec:navrail" data-rapid_p="32"
                  data-v9y="0">Tomorrow<svg
                    class="Fill(engadgetFontGray) Stk(engadgetFontGray) Px(5px) Pt(3px) Cur(p)"
                    width="16" style="stroke-width:0;vertical-align:bottom"
                    height="16" viewBox="0 0 48 48" data-icon="caret-down">
                    <path
                      d="M24.21 33.173l12.727-12.728c.78-.78.78-2.048 0-2.828-.78-.78-2.047-.78-2.828 0l-9.9 9.9-9.9-9.9c-.78-.78-2.047-.78-2.827 0-.78.78-.78 2.047 0 2.828L24.21 33.173z">
                    </path>
                  </svg></a>
                <ul data-component="SubNav"
                  class="menu-options sub-nav Bxz(bb) D(n) active+D(f) Miw(200px) Fxd(c) Pos(a) T(100%) Bgc(engadgetGray) Px(0) Pt(10px) Pb(20px) Z(5001) Pos(r)!--md T(a)!--md Pt(0)!--md  Pend(8px)">
                  <li data-component="SubNavItem"
                    class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h">
                    <a data-component="NavLink"
                      class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                      href="/tag/space/"
                      data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                      data-rapid_p="33" data-v9y="1">Space</a></li>
                  <li data-component="SubNavItem"
                    class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h">
                    <a data-component="NavLink"
                      class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                      href="/tag/ai/"
                      data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                      data-rapid_p="34" data-v9y="1">AI</a></li>
                  <li data-component="SubNavItem"
                    class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h">
                    <a data-component="NavLink"
                      class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                      href="/tag/robots/"
                      data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                      data-rapid_p="35" data-v9y="1">Robotics</a></li>
                  <li data-component="SubNavItem"
                    class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h">
                    <a data-component="NavLink"
                      class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                      href="/tag/transportation/"
                      data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                      data-rapid_p="36" data-v9y="1">Transportation</a></li>
                  <li data-component="SubNavItem"
                    class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h">
                    <a data-component="NavLink"
                      class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                      href="/tomorrow/"
                      data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                      data-rapid_p="37" data-v9y="1">View all</a></li>
                </ul>
              </li>
              <li data-component="NavItem" class=""><a data-component="NavLink"
                  class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetFontLightGray)  C(#engadgetWhiteSmoke):h menu-toggle Pos(r) Whs(nw) Mstart(5px) Mstart(0)!--md Bdts(s) Bdtw(1px) Bdtc(engadgetLightGray) Fz(16px)!--md H(65px)!--md Pstart(20px) P(10px)!--lg Lh(20px)--md"
                  href="/buyers-guide/deals/" data-wf-toggle-class="false"
                  data-ylk="elm:navcat;itc:0;sec:navrail" data-rapid_p="38"
                  data-v9y="0">Deals</a></li>
              <li data-component="NavItem" class=""><a data-component="NavLink"
                  class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) wafer-toggle C(engadgetFontLightGray) C(#engadgetWhiteSmoke):h menu-toggle Pos(r) Whs(nw) Mstart(5px) Mstart(0)!--md Bdts(s) Bdtw(1px) Bdtc(engadgetLightGray) Fz(16px)!--md H(65px)!--md Pstart(20px) P(10px)!--lg Lh(20px)--md has-toggle-click has-wafer-click"
                  href="#"
                  data-wf-toggle-class="click:toggle:active;click:toggle:Bgc(engadgetGray);click:toggle:C(white)"
                  data-ylk="elm:navcat;itc:1;sec:navrail" data-rapid_p="39"
                  data-v9y="0">Buying Guides<svg
                    class="Fill(engadgetFontGray) Stk(engadgetFontGray) Px(5px) Pt(3px) Cur(p)"
                    width="16" style="stroke-width:0;vertical-align:bottom"
                    height="16" viewBox="0 0 48 48" data-icon="caret-down">
                    <path
                      d="M24.21 33.173l12.727-12.728c.78-.78.78-2.048 0-2.828-.78-.78-2.047-.78-2.828 0l-9.9 9.9-9.9-9.9c-.78-.78-2.047-.78-2.827 0-.78.78-.78 2.047 0 2.828L24.21 33.173z">
                    </path>
                  </svg></a>
                <ul data-component="SubNav"
                  class="menu-options sub-nav Bxz(bb) D(n) active+D(f) Miw(200px) Fxd(c) Pos(a) T(100%) Bgc(engadgetGray) Px(0) Pt(10px) Pb(20px) Z(5001) Pos(r)!--md T(a)!--md Pt(0)!--md  Pend(8px)">
                  <li data-component="SubNavItem"
                    class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h">
                    <a data-component="NavLink"
                      class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                      href="/buyers-guide/health-tech/"
                      data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                      data-rapid_p="40" data-v9y="1">Health &amp; Fitness</a>
                  </li>
                  <li data-component="SubNavItem"
                    class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h">
                    <a data-component="NavLink"
                      class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                      href="/buyers-guide/parenting-guide"
                      data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                      data-rapid_p="41" data-v9y="1">Parents &amp; Kids</a></li>
                  <li data-component="SubNavItem"
                    class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h">
                    <a data-component="NavLink"
                      class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                      href="/buyers-guide/outdoor-gear-guide/"
                      data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                      data-rapid_p="42" data-v9y="1">Outdoor Gear</a></li>
                  <li data-component="SubNavItem"
                    class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h">
                    <a data-component="NavLink"
                      class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                      href="/buyers-guide/holiday-gift-guide-2020/"
                      data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                      data-rapid_p="43" data-v9y="1">Holiday Gift Guide</a></li>
                  <li data-component="SubNavItem"
                    class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h">
                    <a data-component="NavLink"
                      class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                      href="/buyers-guide/back-to-school-gift-guide-2021/"
                      data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                      data-rapid_p="44" data-v9y="1">Back to School</a></li>
                </ul>
              </li>
              <li data-component="NavItem" class=""><a data-component="NavLink"
                  class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetFontLightGray)  C(#engadgetWhiteSmoke):h menu-toggle Pos(r) Whs(nw) Mstart(5px) Mstart(0)!--md Bdts(s) Bdtw(1px) Bdtc(engadgetLightGray) Fz(16px)!--md H(65px)!--md Pstart(20px) P(10px)!--lg Lh(20px)--md"
                  href="https://www.youtube.com/channel/UC-6OW5aJYBFM33zXQlBKPNA?sub_confirmation=1"
                  data-wf-toggle-class="false"
                  data-ylk="elm:navcat;itc:0;sec:navrail" data-rapid_p="45"
                  data-v9y="0">Video</a></li>
              <li data-component="NavItem" class=""><a data-component="NavLink"
                  class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetFontLightGray)  C(#engadgetWhiteSmoke):h menu-toggle Pos(r) Whs(nw) Mstart(5px) Mstart(0)!--md Bdts(s) Bdtw(1px) Bdtc(engadgetLightGray) Fz(16px)!--md H(65px)!--md Pstart(20px) P(10px)!--lg Lh(20px)--md"
                  href="/2019-08-01-engadget-podcasts.html"
                  data-wf-toggle-class="false"
                  data-ylk="elm:navcat;itc:0;sec:navrail" data-rapid_p="46"
                  data-v9y="0">Podcasts</a></li>
            </ul>
            <div class="D(f) Fld(c) Mih(192px)">
              <div class="D(f) Mih(65px) Bgc(engadgetGray) Ai(c)"><a
                  href="https://oidc.engadget.com/login?dest=https%3A%2F%www.engadget.com&amp;pspid=**********&amp;activity=header"
                  class="D(if) H(100%) W(100%) Px(15px) Ai(c) Ff($ff-primary) Fz(18px) Fz(16px)!--md Fw(700) C(engadgetWhiteGray) C(white):h Td(n) Bxz(bb) H(100%) W(100%) Px(15px) Bxz(bb) P(20px)!--md"
                  data-ylk="slk:Login;elm:link;itc:0" aria-label="Login"
                  data-rapid_p="47" data-v9y="0">Login</a></div>
              <div
                class="D(f) Mih(65px) Bgc(engadgetGray) Ai(c) BdT--md Bdtc(engadgetFontBlack)--md">
                <a href="https://oidc.engadget.com/create?dest=https%3A%2F%www.engadget.com&amp;pspid=&amp;pspid=**********&amp;activity=header"
                  class="D(if) H(100%) W(100%) Px(15px) Ai(c) Ff($ff-primary) Fz(18px) Fz(16px)!--md Fw(700) C(engadgetWhiteGray) C(white):h Td(n) Bxz(bb) H(100%) W(100%) Px(15px) Bxz(bb) P(20px)!--md"
                  data-ylk="slk:Sign up;elm:link;itc:0" aria-label="Sign up"
                  data-rapid_p="48" data-v9y="0">Sign up</a></div>
              <div
                class="social-follow D(f) Ai(c) BdT--md Bdtc(engadgetFontBlack)--md Mih(60px)">
                <div
                  class="Fxg(1) D(f) Ai(c) Jc(c) H(60px) Bgc(engadgetGray) BdEnd Bdendc(engadgetFontBlack)">
                  <a href="https://www.facebook.com/engadget"
                    class="D(b) Td(n) C(engadgetFontGray) socialLink"
                    data-ylk="sec:followus;elm:follow;itc:0"
                    aria-label="Facebook" data-rapid_p="49" data-v9y="0"><svg
                      class="Fill(engadgetFontGray) Stk(engadgetFontGray) socialLink:h_Fill(#fff) socialLink:h_Stk(#fff) Cur(p)"
                      width="24" style="stroke-width:0;vertical-align:bottom"
                      height="24" viewBox="0 0 32 32" data-icon="LogoFacebook">
                      <path
                        d="M12.752 30.4V16.888H9.365V12.02h3.387V7.865c0-3.264 2.002-6.264 6.613-6.264 1.866 0 3.248.19 3.248.19l-.11 4.54s-1.404-.013-2.943-.013c-1.66 0-1.93.81-1.93 2.152v3.553h5.008l-.22 4.867h-4.786V30.4h-4.88z">
                      </path>
                    </svg></a></div>
                <div
                  class="Fxg(1) D(f) Ai(c) Jc(c) H(60px) Bgc(engadgetGray) BdEnd Bdendc(engadgetFontBlack)">
                  <a href="https://twitter.com/engadget"
                    class="D(b) Td(n) C(engadgetFontGray) socialLink"
                    data-ylk="sec:followus;elm:follow;itc:0"
                    aria-label="Twitter" data-rapid_p="50" data-v9y="0"><svg
                      class="Fill(engadgetFontGray) Stk(engadgetFontGray) socialLink:h_Fill(#fff) socialLink:h_Stk(#fff) Cur(p)"
                      width="24" style="stroke-width:0;vertical-align:bottom"
                      height="24" viewBox="0 0 32 32" data-icon="LogoTwitter">
                      <path
                        d="M30.402 7.094c-1.058.47-2.198.782-3.392.928 1.218-.725 2.154-1.885 2.595-3.256-1.134.674-2.405 1.165-3.75 1.43-1.077-1.148-2.612-1.862-4.31-1.862-3.268 0-5.915 2.635-5.915 5.893 0 .464.056.91.155 1.34-4.915-.244-9.266-2.59-12.18-6.158-.51.87-.806 1.885-.806 2.96 0 2.044 1.045 3.847 2.633 4.905-.974-.032-1.883-.3-2.68-.736v.07c0 2.857 2.034 5.236 4.742 5.773-.498.138-1.022.21-1.56.21-.38 0-.75-.034-1.11-.103.75 2.344 2.93 4.042 5.518 4.09-2.024 1.58-4.57 2.523-7.333 2.523-.478 0-.952-.032-1.41-.085 2.613 1.674 5.72 2.65 9.054 2.65 10.872 0 16.814-8.976 16.814-16.765 0-.254-.008-.507-.018-.762 1.155-.83 2.155-1.868 2.95-3.047z">
                      </path>
                    </svg></a></div>
                <div class="Fxg(1) D(f) Ai(c) Jc(c) H(60px) Bgc(engadgetGray)">
                  <a href="https://www.youtube.com/user/engadget"
                    class="D(b) Td(n) C(engadgetFontGray) socialLink"
                    data-ylk="sec:followus;elm:follow;itc:0"
                    aria-label="YouTube" data-rapid_p="51" data-v9y="0"><svg
                      class="Fill(engadgetFontGray) Stk(engadgetFontGray) socialLink:h_Fill(#fff) socialLink:h_Stk(#fff) Cur(p)"
                      width="24" style="stroke-width:0;vertical-align:bottom"
                      height="24" viewBox="0 0 24 24" data-icon="LogoYoutube">
                      <path
                        d="M9.73 15.117l-.002-6.18 5.944 3.1-5.943 3.08zm13.05-7.252s-.215-1.516-.875-2.184c-.836-.875-1.775-.88-2.203-.93-3.08-.223-7.697-.223-7.697-.223h-.01s-4.618 0-7.696.222c-.43.05-1.37.055-2.206.93-.66.67-.874 2.185-.874 2.185S1 9.645 1 11.425v1.67c0 1.78.22 3.56.22 3.56s.215 1.516.874 2.184c.837.875 1.937.847 2.426.94C6.28 19.947 12 20 12 20s4.623-.007 7.702-.23c.43-.05 1.367-.055 2.204-.93.66-.67.875-2.185.875-2.185s.22-1.78.22-3.56v-1.67c0-1.78-.22-3.56-.22-3.56z">
                      </path>
                    </svg></a></div>
              </div>
            </div>
          </div>
        </div>
        <script>
          window.performance.mark('drawer-menu');
          window.performance.measure('drawer-menuDone', 'PageStart',
            'drawer-menu');

        </script>
      </div>
    </div>
    <div class="wrapper W(100vw) T(0) Start(0) Ovs(touch)">
      <header id="Header" class="Pos(r) T(0) Z(3) W(100%)">
        <div id="module-header" class="wafer-rapid-module">
          <div>
            <div
              class="rwd-outer-container W(100vw) D(f) Jc(sa) H(80px) H(60px)!--xs"
              data-component="RWDContainer">
              <div
                class="rwd-inner-container W(1235px) W(980px)--lg W(640px)--md W(100%)--sm Mx(20px)--sm W(100%)!--md H(80px) H(60px)!--xs M(0)! Pstart(20px)--md Bxz(bb) Bgc(#fff) Bgc(engadgetSteelGray)--md Bdbc(engadgetGray) Bdbs(s)--md Bdbw(1px)--md D(f) Ai(c) Jc(sb) Pos(f)--md T(0)--md Z(2)--md">
                <div class="Whs(nw)">
                  <div
                    class="hamburger-nav-btn-container D(n) Mend(20px) D(ib)!--md Va(t)">
                    <button
                      class="P(0) Bgc(t) Bd(n) O(n) W(35px) H(35px) D(f) Ai(c) Jc(sa) Cur(p) has-click"
                      aria-label="Menu for navigation opens a modal overlay"
                      alt="Menu" data-drawer-target="hamburger"
                      data-wf-drawer-state="open-left"
                      data-component="HamburgerNavBtn"
                      data-ylk="sec:header;subsec:menu;slk:hamburger;elm:logo;itc:0"
                      data-rapid_p="1" data-v9y="1">
                      <div class="hamburger Pe(n)">
                        <div class="W(20px) H(2px) Bgc(white) Mb(3px)"></div>
                        <div class="W(20px) H(2px) Bgc(white) Mb(3px)"></div>
                        <div class="W(20px) H(2px) Bgc(white)"></div>
                      </div>
                    </button></div><a href="/" aria-label="Engadget "
                    class="Td(n) D(ib)"
                    data-ylk="sec:header;subsec:logo;slk:Engadget;elm:logo;itc:0"
                    data-rapid_p="2" data-v9y="1"><svg
                      class="Fill(engadgetSteelGray) Stk(engadgetSteelGray) Fill(#fff)--md Stk(#fff)--md W(152px) W(120px)--xs Cur(p)"
                      width="33" style="stroke-width:0;vertical-align:bottom"
                      height="33" viewBox="0 0 152 33"
                      data-icon="logo-engadget">
                      <path
                        d="M0 24.1C0 25.7 1.3 27 3 27h12v-3H3v-3h11.8c1.6 0 3.2-1.3 3.2-2.9v.1-5.9c0-1.6-1.5-3.2-3.2-3.2H3c-1.6 0-3 1.6-3 3.2v11.8zM15 18H3v-6h12v6zm40.1-9H43.2c-1.6 0-3.2 1.6-3.2 3.2v11.9-.1c0 1.6 1.6 3 3.2 3H55v3H43v3h12.1c1.6 0 2.9-1.4 2.9-3V12.2c0-1.6-1.3-3.2-2.9-3.2zM55 24H43V12h12v12zM32 9h-8.9c-1.6 0-3.1 1.6-3.1 3.2V27h3V12h9c1.6 0 3 1.5 3 3.2V27h3V15.2c0-3.3-2.7-6.2-6-6.2zm43 15H63v-8.9c0-1.6 1.6-3.2 3.3-3.2H75v15h3V12.2c0-1.6-1.2-3.2-2.8-3.2h-8.9C63 9 60 11.9 60 15.2v8.9c0 1.6 1.7 2.9 3.3 2.9h5.2c2.7 0 5.5-1.1 6.5-3v3m68-27h-3v27h3V12h9V9h-9zm-42 24.1c0 1.6.9 2.9 2.5 2.9H115v3h-11v3h11.4c1.6 0 2.6-1.4 2.6-3.1v.1-17.8c0-1.6-1-3.2-2.6-3.2h-11.9c-1.6 0-2.5 1.6-2.5 3.2v11.9zm14-.1h-11V12h11v12zM95 9H83.4c-1.6 0-3.4 1.6-3.4 3.2v11.9-.1c0 1.6 1.8 3 3.4 3H98V0h-3v9zm0 15H83V12h12v12zm25 .1c0 1.6 1.7 2.9 3.3 2.9H135v-3h-12v-3h12.2c1.6 0 2.8-1.3 2.8-2.9v.1-5.9c0-1.6-1.2-3.2-2.8-3.2h-11.9c-1.6 0-3.3 1.6-3.3 3.2v11.8zm15-6.1h-12v-6h12v6z">
                      </path>
                    </svg></a>
                </div>
                <div class="D(f)! D(n)!--md"><a
                    href="https://oidc.engadget.com/login?dest=https%3A%2F%www.engadget.com&amp;pspid=**********&amp;activity=header"
                    class="D(if) Ai(c) Ff($ff-primary) Fz(15px) Fz(400) C(engadgetFontDarkGray) C(engadgetFontBlack):h Td(n) Bxz(bb)"
                    data-ylk="sec:header;subsec:account;slk:profile;elm:link;itc:0"
                    data-rapid_p="3" data-v9y="1">Login</a></div><button
                  class="P(0) Cur(p) Bd(n) Bgc(t) C(engadgetFontLightGray) wafer-toggle D(n)! D(b)!--md W(80px) W(60px)!--xs H(100%) Pos(r) Bdstarts(s)! Bdstartw(1px)! Bdstartc(engadgetGray)! srchBtn has-toggle-click has-wafer-click"
                  data-component="IconButton" type="button"
                  data-wf-toggle-class="click:toggle:active"
                  data-wf-toggle-target="#search" aria-label="Search"
                  title="Search" data-rapid_p="4" data-v9y="0"><svg
                    class="Fill(engadgetFontLightGray) Stk(engadgetFontLightGray) srchBtn:h_Fill(#fff) srchBtn:h_Stk(#fff) Cur(p)"
                    width="20" style="stroke-width:0;vertical-align:bottom"
                    height="20" viewBox="0 0 24 24" data-icon="search">
                    <path
                      d="M9 3C5.686 3 3 5.686 3 9c0 3.313 2.686 6 6 6s6-2.687 6-6c0-3.314-2.686-6-6-6m13.713 19.713c-.387.388-1.016.388-1.404 0l-7.404-7.404C12.55 16.364 10.85 17 9 17c-4.418 0-8-3.582-8-8 0-4.42 3.582-8 8-8s8 3.58 8 8c0 1.85-.634 3.55-1.69 4.905l7.403 7.404c.39.386.39 1.015 0 1.403">
                    </path>
                  </svg></button>
              </div>
            </div>
          </div>
          <script>
            window.performance.mark('header');
            window.performance.measure('headerDone', 'PageStart', 'header');

          </script>
        </div>
      </header>
      <div id="nav" class="W(100%)">
        <div id="module-nav" class="wafer-rapid-module">
          <div>
            <nav class="Pos(r)" data-component="Nav">
              <div
                class="rwd-outer-container W(100vw) D(f) Jc(sa) Bgc(engadgetSteelGray) D(n)--md Bdbc(engadgetGray) Bdbs(s) Bdbw(1px)"
                data-component="RWDContainer">
                <div
                  class="rwd-inner-container W(1235px) W(980px)--lg W(640px)--md W(100%)--sm Mx(20px)--sm D(f) Jc(sb) Ai(c)">
                  <ul
                    class="List(n) D(f) P(0) M(0) H(80px) Pos(r) Start(-25px)">
                    <li data-component="NavItem"
                      class="wafer-menu nav-menu Pos(r)"
                      data-wf-should-activate-on-hover="1"
                      data-wf-skip-menuitem-focus="1"><a
                        data-component="NavLink"
                        class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetFontLightGray) Bgc(engadgetGray):h C(#engadgetWhiteSmoke):h menu-toggle Pos(r) Whs(nw) Mstart(5px) Mstart(0)!--md Bdts(s) Bdtw(1px) Bdtc(engadgetLightGray) Fz(16px)!--md H(65px)!--md P(15px) P(10px)!--lg Lh(20px)--md menu-closed"
                        href="/products/" data-wf-toggle-class="false"
                        data-ylk="elm:navcat;itc:1;sec:navrail" data-rapid_p="1"
                        data-v9y="1" aria-expanded="false">Products</a>
                      <ul data-component="SubNav"
                        class="menu-options sub-nav Bxz(bb) D(n) active+D(f) Miw(200px) Fxd(c) Pos(a) T(100%) Bgc(engadgetGray) Px(0) Pt(10px) Pb(20px) Z(5001) Pos(r)!--md T(a)!--md Pt(0)!--md Mstart(5px)"
                        tabindex="-1" role="menu" aria-hidden="true">
                        <li data-component="SubNavItem"
                          class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h"
                          role="menuitem" tabindex="-1"><a
                            data-component="NavLink"
                            class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                            href="/products/phones/"
                            data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                            data-rapid_p="2" data-v9y="1">Phones</a></li>
                        <li data-component="SubNavItem"
                          class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h"
                          role="menuitem" tabindex="-1"><a
                            data-component="NavLink"
                            class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                            href="/products/laptops/"
                            data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                            data-rapid_p="3" data-v9y="1">Laptops</a></li>
                        <li data-component="SubNavItem"
                          class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h"
                          role="menuitem" tabindex="-1"><a
                            data-component="NavLink"
                            class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                            href="/products/headphones/"
                            data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                            data-rapid_p="4" data-v9y="1">Headphones</a></li>
                        <li data-component="SubNavItem"
                          class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h"
                          role="menuitem" tabindex="-1"><a
                            data-component="NavLink"
                            class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                            href="/products/gaming-consoles-home/"
                            data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                            data-rapid_p="5" data-v9y="1">Gaming Consoles</a>
                        </li>
                        <li data-component="SubNavItem"
                          class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h"
                          role="menuitem" tabindex="-1"><a
                            data-component="NavLink"
                            class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                            href="/products/wearables/"
                            data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                            data-rapid_p="6" data-v9y="1">Smartwatches</a></li>
                        <li data-component="SubNavItem"
                          class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h"
                          role="menuitem" tabindex="-1"><a
                            data-component="NavLink"
                            class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                            href="/products/"
                            data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                            data-rapid_p="7" data-v9y="1">View all</a></li>
                      </ul>
                    </li>
                    <li data-component="NavItem"
                      class="wafer-menu nav-menu Pos(r)"
                      data-wf-should-activate-on-hover="1"
                      data-wf-skip-menuitem-focus="1"><a
                        data-component="NavLink"
                        class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetFontLightGray) Bgc(engadgetGray):h C(#engadgetWhiteSmoke):h menu-toggle Pos(r) Whs(nw) Mstart(5px) Mstart(0)!--md Bdts(s) Bdtw(1px) Bdtc(engadgetLightGray) Fz(16px)!--md H(65px)!--md P(15px) P(10px)!--lg Lh(20px)--md menu-closed"
                        href="/reviews/" data-wf-toggle-class="false"
                        data-ylk="elm:navcat;itc:1;sec:navrail" data-rapid_p="8"
                        data-v9y="1" aria-expanded="false">Reviews</a>
                      <ul data-component="SubNav"
                        class="menu-options sub-nav Bxz(bb) D(n) active+D(f) Miw(200px) Fxd(c) Pos(a) T(100%) Bgc(engadgetGray) Px(0) Pt(10px) Pb(20px) Z(5001) Pos(r)!--md T(a)!--md Pt(0)!--md Mstart(5px)"
                        tabindex="-1" role="menu" aria-hidden="true">
                        <li data-component="SubNavItem"
                          class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h"
                          role="menuitem" tabindex="-1"><a
                            data-component="NavLink"
                            class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                            href="/tag/best%20tech/"
                            data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                            data-rapid_p="9" data-v9y="1">Best in Tech</a></li>
                        <li data-component="SubNavItem"
                          class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h"
                          role="menuitem" tabindex="-1"><a
                            data-component="NavLink"
                            class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                            href="/tag/hands-on/"
                            data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                            data-rapid_p="10" data-v9y="1">Hands-On</a></li>
                        <li data-component="SubNavItem"
                          class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h"
                          role="menuitem" tabindex="-1"><a
                            data-component="NavLink"
                            class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                            href="/reviews/"
                            data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                            data-rapid_p="11" data-v9y="1">View all</a></li>
                      </ul>
                    </li>
                    <li data-component="NavItem"
                      class="wafer-menu nav-menu Pos(r)"
                      data-wf-should-activate-on-hover="1"
                      data-wf-skip-menuitem-focus="1"><a
                        data-component="NavLink"
                        class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetFontLightGray) Bgc(engadgetGray):h C(#engadgetWhiteSmoke):h menu-toggle Pos(r) Whs(nw) Mstart(5px) Mstart(0)!--md Bdts(s) Bdtw(1px) Bdtc(engadgetLightGray) Fz(16px)!--md H(65px)!--md P(15px) P(10px)!--lg Lh(20px)--md menu-closed"
                        href="/gaming/" data-wf-toggle-class="false"
                        data-ylk="elm:navcat;itc:1;sec:navrail"
                        data-rapid_p="12" data-v9y="1"
                        aria-expanded="false">Gaming</a>
                      <ul data-component="SubNav"
                        class="menu-options sub-nav Bxz(bb) D(n) active+D(f) Miw(200px) Fxd(c) Pos(a) T(100%) Bgc(engadgetGray) Px(0) Pt(10px) Pb(20px) Z(5001) Pos(r)!--md T(a)!--md Pt(0)!--md Mstart(5px)"
                        tabindex="-1" role="menu" aria-hidden="true">
                        <li data-component="SubNavItem"
                          class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h"
                          role="menuitem" tabindex="-1"><a
                            data-component="NavLink"
                            class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                            href="/tag/best%20games/"
                            data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                            data-rapid_p="13" data-v9y="1">Best Games</a></li>
                        <li data-component="SubNavItem"
                          class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h"
                          role="menuitem" tabindex="-1"><a
                            data-component="NavLink"
                            class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                            href="/tag/playstation/"
                            data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                            data-rapid_p="14" data-v9y="1">PlayStation</a></li>
                        <li data-component="SubNavItem"
                          class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h"
                          role="menuitem" tabindex="-1"><a
                            data-component="NavLink"
                            class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                            href="/tag/nintendo/"
                            data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                            data-rapid_p="15" data-v9y="1">Nintendo</a></li>
                        <li data-component="SubNavItem"
                          class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h"
                          role="menuitem" tabindex="-1"><a
                            data-component="NavLink"
                            class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                            href="/tag/xbox/"
                            data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                            data-rapid_p="16" data-v9y="1">Xbox</a></li>
                        <li data-component="SubNavItem"
                          class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h"
                          role="menuitem" tabindex="-1"><a
                            data-component="NavLink"
                            class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                            href="/gaming/"
                            data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                            data-rapid_p="17" data-v9y="1">View all</a></li>
                      </ul>
                    </li>
                    <li data-component="NavItem"
                      class="wafer-menu nav-menu Pos(r)"
                      data-wf-should-activate-on-hover="1"
                      data-wf-skip-menuitem-focus="1"><a
                        data-component="NavLink"
                        class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetFontLightGray) Bgc(engadgetGray):h C(#engadgetWhiteSmoke):h menu-toggle Pos(r) Whs(nw) Mstart(5px) Mstart(0)!--md Bdts(s) Bdtw(1px) Bdtc(engadgetLightGray) Fz(16px)!--md H(65px)!--md P(15px) P(10px)!--lg Lh(20px)--md menu-closed"
                        href="/gear/" data-wf-toggle-class="false"
                        data-ylk="elm:navcat;itc:1;sec:navrail"
                        data-rapid_p="18" data-v9y="1"
                        aria-expanded="false">Gear</a>
                      <ul data-component="SubNav"
                        class="menu-options sub-nav Bxz(bb) D(n) active+D(f) Miw(200px) Fxd(c) Pos(a) T(100%) Bgc(engadgetGray) Px(0) Pt(10px) Pb(20px) Z(5001) Pos(r)!--md T(a)!--md Pt(0)!--md Mstart(5px)"
                        tabindex="-1" role="menu" aria-hidden="true">
                        <li data-component="SubNavItem"
                          class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h"
                          role="menuitem" tabindex="-1"><a
                            data-component="NavLink"
                            class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                            href="/tag/amazon/"
                            data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                            data-rapid_p="19" data-v9y="1">Amazon</a></li>
                        <li data-component="SubNavItem"
                          class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h"
                          role="menuitem" tabindex="-1"><a
                            data-component="NavLink"
                            class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                            href="/tag/apple/"
                            data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                            data-rapid_p="20" data-v9y="1">Apple</a></li>
                        <li data-component="SubNavItem"
                          class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h"
                          role="menuitem" tabindex="-1"><a
                            data-component="NavLink"
                            class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                            href="/tag/google/"
                            data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                            data-rapid_p="21" data-v9y="1">Google</a></li>
                        <li data-component="SubNavItem"
                          class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h"
                          role="menuitem" tabindex="-1"><a
                            data-component="NavLink"
                            class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                            href="/tag/microsoft/"
                            data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                            data-rapid_p="22" data-v9y="1">Microsoft</a></li>
                        <li data-component="SubNavItem"
                          class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h"
                          role="menuitem" tabindex="-1"><a
                            data-component="NavLink"
                            class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                            href="/tag/samsung/"
                            data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                            data-rapid_p="23" data-v9y="1">Samsung</a></li>
                        <li data-component="SubNavItem"
                          class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h"
                          role="menuitem" tabindex="-1"><a
                            data-component="NavLink"
                            class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                            href="/gear/"
                            data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                            data-rapid_p="24" data-v9y="1">View all</a></li>
                      </ul>
                    </li>
                    <li data-component="NavItem"
                      class="wafer-menu nav-menu Pos(r)"
                      data-wf-should-activate-on-hover="1"
                      data-wf-skip-menuitem-focus="1"><a
                        data-component="NavLink"
                        class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetFontLightGray) Bgc(engadgetGray):h C(#engadgetWhiteSmoke):h menu-toggle Pos(r) Whs(nw) Mstart(5px) Mstart(0)!--md Bdts(s) Bdtw(1px) Bdtc(engadgetLightGray) Fz(16px)!--md H(65px)!--md P(15px) P(10px)!--lg Lh(20px)--md menu-closed"
                        href="/entertainment/" data-wf-toggle-class="false"
                        data-ylk="elm:navcat;itc:1;sec:navrail"
                        data-rapid_p="25" data-v9y="1"
                        aria-expanded="false">Entertainment</a>
                      <ul data-component="SubNav"
                        class="menu-options sub-nav Bxz(bb) D(n) active+D(f) Miw(200px) Fxd(c) Pos(a) T(100%) Bgc(engadgetGray) Px(0) Pt(10px) Pb(20px) Z(5001) Pos(r)!--md T(a)!--md Pt(0)!--md Mstart(5px)"
                        tabindex="-1" role="menu" aria-hidden="true">
                        <li data-component="SubNavItem"
                          class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h"
                          role="menuitem" tabindex="-1"><a
                            data-component="NavLink"
                            class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                            href="/tag/movies/"
                            data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                            data-rapid_p="26" data-v9y="1">Movies</a></li>
                        <li data-component="SubNavItem"
                          class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h"
                          role="menuitem" tabindex="-1"><a
                            data-component="NavLink"
                            class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                            href="/tag/music/"
                            data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                            data-rapid_p="27" data-v9y="1">Music</a></li>
                        <li data-component="SubNavItem"
                          class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h"
                          role="menuitem" tabindex="-1"><a
                            data-component="NavLink"
                            class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                            href="/tag/television/"
                            data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                            data-rapid_p="28" data-v9y="1">TV</a></li>
                        <li data-component="SubNavItem"
                          class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h"
                          role="menuitem" tabindex="-1"><a
                            data-component="NavLink"
                            class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                            href="/tag/youtube/"
                            data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                            data-rapid_p="29" data-v9y="1">YouTube</a></li>
                        <li data-component="SubNavItem"
                          class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h"
                          role="menuitem" tabindex="-1"><a
                            data-component="NavLink"
                            class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                            href="/entertainment/"
                            data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                            data-rapid_p="30" data-v9y="1">View all</a></li>
                      </ul>
                    </li>
                    <li data-component="NavItem"
                      class="wafer-menu nav-menu Pos(r)"
                      data-wf-should-activate-on-hover="1"
                      data-wf-skip-menuitem-focus="1"><a
                        data-component="NavLink"
                        class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetFontLightGray) Bgc(engadgetGray):h C(#engadgetWhiteSmoke):h menu-toggle Pos(r) Whs(nw) Mstart(5px) Mstart(0)!--md Bdts(s) Bdtw(1px) Bdtc(engadgetLightGray) Fz(16px)!--md H(65px)!--md P(15px) P(10px)!--lg Lh(20px)--md menu-closed"
                        href="/tomorrow/" data-wf-toggle-class="false"
                        data-ylk="elm:navcat;itc:1;sec:navrail"
                        data-rapid_p="31" data-v9y="1"
                        aria-expanded="false">Tomorrow</a>
                      <ul data-component="SubNav"
                        class="menu-options sub-nav Bxz(bb) D(n) active+D(f) Miw(200px) Fxd(c) Pos(a) T(100%) Bgc(engadgetGray) Px(0) Pt(10px) Pb(20px) Z(5001) Pos(r)!--md T(a)!--md Pt(0)!--md Mstart(5px)"
                        tabindex="-1" role="menu" aria-hidden="true">
                        <li data-component="SubNavItem"
                          class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h"
                          role="menuitem" tabindex="-1"><a
                            data-component="NavLink"
                            class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                            href="/tag/space/"
                            data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                            data-rapid_p="32" data-v9y="1">Space</a></li>
                        <li data-component="SubNavItem"
                          class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h"
                          role="menuitem" tabindex="-1"><a
                            data-component="NavLink"
                            class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                            href="/tag/ai/"
                            data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                            data-rapid_p="33" data-v9y="1">AI</a></li>
                        <li data-component="SubNavItem"
                          class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h"
                          role="menuitem" tabindex="-1"><a
                            data-component="NavLink"
                            class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                            href="/tag/robots/"
                            data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                            data-rapid_p="34" data-v9y="1">Robotics</a></li>
                        <li data-component="SubNavItem"
                          class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h"
                          role="menuitem" tabindex="-1"><a
                            data-component="NavLink"
                            class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                            href="/tag/transportation/"
                            data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                            data-rapid_p="35" data-v9y="1">Transportation</a>
                        </li>
                        <li data-component="SubNavItem"
                          class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h"
                          role="menuitem" tabindex="-1"><a
                            data-component="NavLink"
                            class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                            href="/tomorrow/"
                            data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                            data-rapid_p="36" data-v9y="1">View all</a></li>
                      </ul>
                    </li>
                    <li data-component="NavItem" class="nav-menu"><a
                        data-component="NavLink"
                        class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetFontLightGray)  C(#engadgetWhiteSmoke):h menu-toggle Pos(r) Whs(nw) Mstart(5px) Mstart(0)!--md Bdts(s) Bdtw(1px) Bdtc(engadgetLightGray) Fz(16px)!--md H(65px)!--md P(15px) P(10px)!--lg Lh(20px)--md"
                        href="/buyers-guide/deals/" data-wf-toggle-class="false"
                        data-ylk="elm:navcat;itc:0;sec:navrail"
                        data-rapid_p="37" data-v9y="1">Deals</a></li>
                    <li data-component="NavItem"
                      class="wafer-menu nav-menu Pos(r)"
                      data-wf-should-activate-on-hover="1"
                      data-wf-skip-menuitem-focus="1"><a
                        data-component="NavLink"
                        class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetFontLightGray) Bgc(engadgetGray):h C(#engadgetWhiteSmoke):h menu-toggle Pos(r) Whs(nw) Mstart(5px) Mstart(0)!--md Bdts(s) Bdtw(1px) Bdtc(engadgetLightGray) Fz(16px)!--md H(65px)!--md P(15px) P(10px)!--lg Lh(20px)--md menu-closed"
                        href="/products/" data-wf-toggle-class="false"
                        data-ylk="elm:navcat;itc:1;sec:navrail"
                        data-rapid_p="38" data-v9y="1"
                        aria-expanded="false">Buying Guides</a>
                      <ul data-component="SubNav"
                        class="menu-options sub-nav Bxz(bb) D(n) active+D(f) Miw(200px) Fxd(c) Pos(a) T(100%) Bgc(engadgetGray) Px(0) Pt(10px) Pb(20px) Z(5001) Pos(r)!--md T(a)!--md Pt(0)!--md Mstart(5px)"
                        tabindex="-1" role="menu" aria-hidden="true">
                        <li data-component="SubNavItem"
                          class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h"
                          tabindex="-1" role="menuitem"><a
                            data-component="NavLink"
                            class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                            href="/buyers-guide/health-tech/"
                            data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                            data-rapid_p="39" data-v9y="1">Health &amp;
                            Fitness</a></li>
                        <li data-component="SubNavItem"
                          class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h"
                          tabindex="-1" role="menuitem"><a
                            data-component="NavLink"
                            class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                            href="/buyers-guide/parenting-guide"
                            data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                            data-rapid_p="40" data-v9y="1">Parents &amp;
                            Kids</a></li>
                        <li data-component="SubNavItem"
                          class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h"
                          tabindex="-1" role="menuitem"><a
                            data-component="NavLink"
                            class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                            href="/buyers-guide/outdoor-gear-guide/"
                            data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                            data-rapid_p="41" data-v9y="1">Outdoor Gear</a></li>
                        <li data-component="SubNavItem"
                          class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h"
                          tabindex="-1" role="menuitem"><a
                            data-component="NavLink"
                            class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                            href="/buyers-guide/holiday-gift-guide-2020/"
                            data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                            data-rapid_p="42" data-v9y="1">Holiday Gift
                            Guide</a></li>
                        <li data-component="SubNavItem"
                          class="menu-item D(b) Lh(40px) Whs(nw) Bgc(engadgetFontBlack):h"
                          tabindex="-1" role="menuitem"><a
                            data-component="NavLink"
                            class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetWhiteGray) C(#fff):h Px(15px) Fw(400)!"
                            href="/buyers-guide/back-to-school-gift-guide-2021/"
                            data-ylk="elm:navcat;itc:0;sec:navrail;subsec:sub"
                            data-rapid_p="43" data-v9y="1">Back to School</a>
                        </li>
                      </ul>
                    </li>
                    <li data-component="NavItem" class="nav-menu"><a
                        data-component="NavLink"
                        class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetFontLightGray)  C(#engadgetWhiteSmoke):h menu-toggle Pos(r) Whs(nw) Mstart(5px) Mstart(0)!--md Bdts(s) Bdtw(1px) Bdtc(engadgetLightGray) Fz(16px)!--md H(65px)!--md P(15px) P(10px)!--lg Lh(20px)--md"
                        href="https://www.youtube.com/channel/UC-6OW5aJYBFM33zXQlBKPNA?sub_confirmation=1"
                        data-wf-toggle-class="false"
                        data-ylk="elm:navcat;itc:0;sec:navrail"
                        data-rapid_p="44" data-v9y="1">Video</a></li>
                    <li data-component="NavItem" class="nav-menu"><a
                        data-component="NavLink"
                        class="D(if) H(100%) W(100%) Ai(c) Fz(18px) Lh(30px) Fw(700) Td(n) Bxz(bb) C(engadgetFontLightGray)  C(#engadgetWhiteSmoke):h menu-toggle Pos(r) Whs(nw) Mstart(5px) Mstart(0)!--md Bdts(s) Bdtw(1px) Bdtc(engadgetLightGray) Fz(16px)!--md H(65px)!--md P(15px) P(10px)!--lg Lh(20px)--md"
                        href="/2019-08-01-engadget-podcasts.html"
                        data-wf-toggle-class="false"
                        data-ylk="elm:navcat;itc:0;sec:navrail"
                        data-rapid_p="45" data-v9y="1">Podcasts</a></li>
                  </ul>
                  <div class="D(f) Ai(c)">
                    <ul
                      class="D(f) D(n)!--lg Ai(c) List(n) Pstart(0) Pend(25px) M(0)">
                      <li data-component="SocialItem"
                        class="Mstart(25px) socialLink"><a
                          href="https://www.facebook.com/engadget"
                          alt="Facebook" target="_blank"
                          rel="noopener noreferrer" class="C(white)"
                          data-ylk="sec:hl-viewer;slk:Facebook;elm:share;itc:0"
                          data-rapid_p="46" data-v9y="1"><span
                            class="D(n)">Facebook</span><svg
                            class="Fill(engadgetFontGray) Stk(engadgetFontGray) socialLink:h_Fill(#fff) socialLink:h_Stk(#fff) Cur(p)"
                            width="24"
                            style="stroke-width:0;vertical-align:bottom"
                            height="24" viewBox="0 0 32 32"
                            data-icon="LogoFacebook">
                            <path
                              d="M12.752 30.4V16.888H9.365V12.02h3.387V7.865c0-3.264 2.002-6.264 6.613-6.264 1.866 0 3.248.19 3.248.19l-.11 4.54s-1.404-.013-2.943-.013c-1.66 0-1.93.81-1.93 2.152v3.553h5.008l-.22 4.867h-4.786V30.4h-4.88z">
                            </path>
                          </svg></a></li>
                      <li data-component="SocialItem"
                        class="Mstart(25px) socialLink"><a
                          href="https://twitter.com/engadget" alt="Twitter"
                          target="_blank" rel="noopener noreferrer"
                          class="C(white)"
                          data-ylk="sec:hl-viewer;slk:Twitter;elm:share;itc:0"
                          data-rapid_p="47" data-v9y="1"><span
                            class="D(n)">Twitter</span><svg
                            class="Fill(engadgetFontGray) Stk(engadgetFontGray) socialLink:h_Fill(#fff) socialLink:h_Stk(#fff) Cur(p)"
                            width="24"
                            style="stroke-width:0;vertical-align:bottom"
                            height="24" viewBox="0 0 32 32"
                            data-icon="LogoTwitter">
                            <path
                              d="M30.402 7.094c-1.058.47-2.198.782-3.392.928 1.218-.725 2.154-1.885 2.595-3.256-1.134.674-2.405 1.165-3.75 1.43-1.077-1.148-2.612-1.862-4.31-1.862-3.268 0-5.915 2.635-5.915 5.893 0 .464.056.91.155 1.34-4.915-.244-9.266-2.59-12.18-6.158-.51.87-.806 1.885-.806 2.96 0 2.044 1.045 3.847 2.633 4.905-.974-.032-1.883-.3-2.68-.736v.07c0 2.857 2.034 5.236 4.742 5.773-.498.138-1.022.21-1.56.21-.38 0-.75-.034-1.11-.103.75 2.344 2.93 4.042 5.518 4.09-2.024 1.58-4.57 2.523-7.333 2.523-.478 0-.952-.032-1.41-.085 2.613 1.674 5.72 2.65 9.054 2.65 10.872 0 16.814-8.976 16.814-16.765 0-.254-.008-.507-.018-.762 1.155-.83 2.155-1.868 2.95-3.047z">
                            </path>
                          </svg></a></li>
                      <li data-component="SocialItem"
                        class="Mstart(25px) socialLink"><a
                          href="https://www.youtube.com/user/engadget"
                          alt="YouTube" target="_blank"
                          rel="noopener noreferrer" class="C(white)"
                          data-ylk="sec:hl-viewer;slk:YouTube;elm:share;itc:0"
                          data-rapid_p="48" data-v9y="1"><span
                            class="D(n)">YouTube</span><svg
                            class="Fill(engadgetFontGray) Stk(engadgetFontGray) socialLink:h_Fill(#fff) socialLink:h_Stk(#fff) Cur(p)"
                            width="24"
                            style="stroke-width:0;vertical-align:bottom"
                            height="24" viewBox="0 0 24 24"
                            data-icon="LogoYoutube">
                            <path
                              d="M9.73 15.117l-.002-6.18 5.944 3.1-5.943 3.08zm13.05-7.252s-.215-1.516-.875-2.184c-.836-.875-1.775-.88-2.203-.93-3.08-.223-7.697-.223-7.697-.223h-.01s-4.618 0-7.696.222c-.43.05-1.37.055-2.206.93-.66.67-.874 2.185-.874 2.185S1 9.645 1 11.425v1.67c0 1.78.22 3.56.22 3.56s.215 1.516.874 2.184c.837.875 1.937.847 2.426.94C6.28 19.947 12 20 12 20s4.623-.007 7.702-.23c.43-.05 1.367-.055 2.204-.93.66-.67.875-2.185.875-2.185s.22-1.78.22-3.56v-1.67c0-1.78-.22-3.56-.22-3.56z">
                            </path>
                          </svg></a></li>
                    </ul><button
                      class="P(0) Cur(p) Bd(n) Bgc(t) C(engadgetFontLightGray) srchBtn"
                      data-component="IconButton" type="button" id="searchBtn"
                      aria-label="Search" title="Search" data-rapid_p="49"
                      data-v9y="1"><svg
                        class="Fill(engadgetFontLightGray) Stk(engadgetFontLightGray) srchBtn:h_Fill(white) srchBtn:h_Stk(white) Cur(p)"
                        width="20" style="stroke-width:0;vertical-align:bottom"
                        height="20" viewBox="0 0 24 24" data-icon="search">
                        <path
                          d="M9 3C5.686 3 3 5.686 3 9c0 3.313 2.686 6 6 6s6-2.687 6-6c0-3.314-2.686-6-6-6m13.713 19.713c-.387.388-1.016.388-1.404 0l-7.404-7.404C12.55 16.364 10.85 17 9 17c-4.418 0-8-3.582-8-8 0-4.42 3.582-8 8-8s8 3.58 8 8c0 1.85-.634 3.55-1.69 4.905l7.403 7.404c.39.386.39 1.015 0 1.403">
                        </path>
                      </svg></button>
                  </div>
                </div>
              </div>
              <nav id="fixed-nav" aria-hidden="true" data-component="FixedNav"
                class="Pos(f) T(0) Start(0) W(100%) Z(5000) TranslateY(-100%) Trsp(transform) Trsdu(0.5s) Trstf(eo)"
                style="display: none;">
                <div
                  class="rwd-outer-container W(100vw) D(f) Jc(sa) H(80px) Bgc(engadgetSteelGray) D(n)--md Bdbc(engadgetGray) Bdbs(s) Bdbw(1px)"
                  data-component="RWDContainer">
                  <div
                    class="rwd-inner-container W(1235px) W(980px)--lg W(640px)--md W(100%)--sm Mx(20px)--sm Maw(1235px) Maw(980px)--lg D(f) Ai(c) Jc(sb)">
                    <div class="D(f) Ai(c) H(100%)"><a href="/"
                        alt="Engadget US" class="Td(n) Pend(50px)"
                        data-ylk="sec:lnav;elm:logo;itc:0"
                        aria-label="Engadget US" data-rapid_p="50"
                        data-v9y="0"><svg class="Fill(#fff) Stk(#fff) Cur(p)"
                          width="152"
                          style="stroke-width:0;vertical-align:bottom"
                          height="33" viewBox="0 0 152 33"
                          data-icon="logo-engadget">
                          <path
                            d="M0 24.1C0 25.7 1.3 27 3 27h12v-3H3v-3h11.8c1.6 0 3.2-1.3 3.2-2.9v.1-5.9c0-1.6-1.5-3.2-3.2-3.2H3c-1.6 0-3 1.6-3 3.2v11.8zM15 18H3v-6h12v6zm40.1-9H43.2c-1.6 0-3.2 1.6-3.2 3.2v11.9-.1c0 1.6 1.6 3 3.2 3H55v3H43v3h12.1c1.6 0 2.9-1.4 2.9-3V12.2c0-1.6-1.3-3.2-2.9-3.2zM55 24H43V12h12v12zM32 9h-8.9c-1.6 0-3.1 1.6-3.1 3.2V27h3V12h9c1.6 0 3 1.5 3 3.2V27h3V15.2c0-3.3-2.7-6.2-6-6.2zm43 15H63v-8.9c0-1.6 1.6-3.2 3.3-3.2H75v15h3V12.2c0-1.6-1.2-3.2-2.8-3.2h-8.9C63 9 60 11.9 60 15.2v8.9c0 1.6 1.7 2.9 3.3 2.9h5.2c2.7 0 5.5-1.1 6.5-3v3m68-27h-3v27h3V12h9V9h-9zm-42 24.1c0 1.6.9 2.9 2.5 2.9H115v3h-11v3h11.4c1.6 0 2.6-1.4 2.6-3.1v.1-17.8c0-1.6-1-3.2-2.6-3.2h-11.9c-1.6 0-2.5 1.6-2.5 3.2v11.9zm14-.1h-11V12h11v12zM95 9H83.4c-1.6 0-3.4 1.6-3.4 3.2v11.9-.1c0 1.6 1.8 3 3.4 3H98V0h-3v9zm0 15H83V12h12v12zm25 .1c0 1.6 1.7 2.9 3.3 2.9H135v-3h-12v-3h12.2c1.6 0 2.8-1.3 2.8-2.9v.1-5.9c0-1.6-1.2-3.2-2.8-3.2h-11.9c-1.6 0-3.3 1.6-3.3 3.2v11.8zm15-6.1h-12v-6h12v6z">
                          </path>
                        </svg></a>
                      <div class="Pos(r) H(100%)"><button
                          class="drawer-toggle wafer-toggle H(100%) Px(60px) BdX Bdc(engadgetGray) D(f) Ai(c) Bgc(t) C(engadgetWhiteGray) C(#fff):h Fz(16px) Cur(p) has-toggle-click has-wafer-click"
                          type="button"
                          data-wf-toggle-class="click:toggle:active"
                          aria-label="Sections"
                          data-ylk="itc:0;sec:lnav;slk:Sections;elm:link"
                          data-rapid_p="51" data-v9y="0">Sections<svg
                            class="Fill(engadgetFontGray) Stk(engadgetFontGray) drawer-toggle:h_Fill(#fff) drawer-toggle:h_Stk(#fff) Mstart(10px) Cur(p)"
                            width="20"
                            style="stroke-width:0;vertical-align:bottom"
                            height="20" viewBox="0 0 48 48"
                            data-icon="caret-down">
                            <path
                              d="M24.21 33.173l12.727-12.728c.78-.78.78-2.048 0-2.828-.78-.78-2.047-.78-2.828 0l-9.9 9.9-9.9-9.9c-.78-.78-2.047-.78-2.827 0-.78.78-.78 2.047 0 2.828L24.21 33.173z">
                            </path>
                          </svg></button>
                        <ul id="nav-drawer" data-component="Drawer"
                          class="D(n) List(n) Fld(c) Ai(fs) Pos(a) W(100%) T(100%) Start(0) M(0) P(0) Pt(10px)! Pb(20px)! Bgc(engadgetGray) C(white) BdT Bdtc(engadgetLightGray)">
                          <li class="Py(5px) Px(25px)"><a href="/products/"
                              class="Fz(16px) C(engadgetWhiteGray) C(white):h Td(n)"
                              data-ylk="sec:lnav;elm:link;itc:0"
                              data-rapid_p="52" data-v9y="1">Products</a></li>
                          <li class="Py(5px) Px(25px)"><a href="/reviews/"
                              class="Fz(16px) C(engadgetWhiteGray) C(white):h Td(n)"
                              data-ylk="sec:lnav;elm:link;itc:0"
                              data-rapid_p="53" data-v9y="1">Reviews</a></li>
                          <li class="Py(5px) Px(25px)"><a href="/gaming/"
                              class="Fz(16px) C(engadgetWhiteGray) C(white):h Td(n)"
                              data-ylk="sec:lnav;elm:link;itc:0"
                              data-rapid_p="54" data-v9y="1">Gaming</a></li>
                          <li class="Py(5px) Px(25px)"><a href="/gear/"
                              class="Fz(16px) C(engadgetWhiteGray) C(white):h Td(n)"
                              data-ylk="sec:lnav;elm:link;itc:0"
                              data-rapid_p="55" data-v9y="1">Gear</a></li>
                          <li class="Py(5px) Px(25px)"><a href="/entertainment/"
                              class="Fz(16px) C(engadgetWhiteGray) C(white):h Td(n)"
                              data-ylk="sec:lnav;elm:link;itc:0"
                              data-rapid_p="56" data-v9y="1">Entertainment</a>
                          </li>
                          <li class="Py(5px) Px(25px)"><a href="/tomorrow/"
                              class="Fz(16px) C(engadgetWhiteGray) C(white):h Td(n)"
                              data-ylk="sec:lnav;elm:link;itc:0"
                              data-rapid_p="57" data-v9y="1">Tomorrow</a></li>
                          <li class="Py(5px) Px(25px)"><a
                              href="/buyers-guide/deals/"
                              class="Fz(16px) C(engadgetWhiteGray) C(white):h Td(n)"
                              data-ylk="sec:lnav;elm:link;itc:0"
                              data-rapid_p="58" data-v9y="1">Deals</a></li>
                          <li class="Py(5px) Px(25px)"><a href="/products/"
                              class="Fz(16px) C(engadgetWhiteGray) C(white):h Td(n)"
                              data-ylk="sec:lnav;elm:link;itc:0"
                              data-rapid_p="59" data-v9y="1">Buying Guides</a>
                          </li>
                          <li class="Py(5px) Px(25px)"><a
                              href="https://www.youtube.com/channel/UC-6OW5aJYBFM33zXQlBKPNA?sub_confirmation=1"
                              class="Fz(16px) C(engadgetWhiteGray) C(white):h Td(n)"
                              data-ylk="sec:lnav;elm:link;itc:0"
                              data-rapid_p="60" data-v9y="1">Video</a></li>
                          <li class="Py(5px) Px(25px)"><a
                              href="/2019-08-01-engadget-podcasts.html"
                              class="Fz(16px) C(engadgetWhiteGray) C(white):h Td(n)"
                              data-ylk="sec:lnav;elm:link;itc:0"
                              data-rapid_p="61" data-v9y="1">Podcasts</a></li>
                        </ul>
                      </div>
                    </div>
                    <div class="D(f) Ai(c) H(100%)">
                      <div class="D(ib) Pend(20px)"><a
                          href="https://oidc.engadget.com/login?dest=https%3A%2F%www.engadget.com&amp;pspid=**********&amp;activity=header"
                          class="D(if) Ai(c) Fz(15px) Fz(400) C(engadgetFontGray) C(#fff):h Td(n) Bxz(bb)"
                          data-ylk="sec:header;subsec:account;slk:login;elm:link;itc:0"
                          aria-label="login" data-rapid_p="62"
                          data-v9y="0">Login</a></div><button
                        class="P(0) Cur(p) Bd(n) Bgc(t) C(engadgetFontLightGray) wafer-toggle BdStart! Bdc(engadgetGray)! W(80px) H(100%) srchBtn has-toggle-click has-wafer-click"
                        data-component="IconButton" type="button"
                        data-wf-toggle-class="click:toggle:active"
                        data-wf-toggle-target="#search" aria-label="Search"
                        title="Search" data-rapid_p="63" data-v9y="0"><svg
                          class="Fill(engadgetFontGray) Stk(engadgetFontGray) srchBtn:h_Fill(#fff) srchBtn:h_Stk(#fff) Cur(p)"
                          width="20"
                          style="stroke-width:0;vertical-align:bottom"
                          height="20" viewBox="0 0 24 24" data-icon="search">
                          <path
                            d="M9 3C5.686 3 3 5.686 3 9c0 3.313 2.686 6 6 6s6-2.687 6-6c0-3.314-2.686-6-6-6m13.713 19.713c-.387.388-1.016.388-1.404 0l-7.404-7.404C12.55 16.364 10.85 17 9 17c-4.418 0-8-3.582-8-8 0-4.42 3.582-8 8-8s8 3.58 8 8c0 1.85-.634 3.55-1.69 4.905l7.403 7.404c.39.386.39 1.015 0 1.403">
                          </path>
                        </svg></button>
                    </div>
                  </div>
                </div>
              </nav>
              <form id="search" data-component="Search"
                class="V(h) Pe(n) Op(0) Trsdu(0.2s) Trstf(eo) Pos(a) T(0) Start(0) W(100%) Pos(f)!--md H(80px) H(60px)!--xs Z(5001) Bgc(engadgetGray)"
                style="transition-property:opacity"
                action="https://search.engadget.com/search"
                aria-label="Search form">
                <div
                  class="rwd-outer-container W(100vw) D(f) Jc(sa) H(100%) BdB Bdbc(engadgetFontGray)"
                  data-component="RWDContainer">
                  <div
                    class="rwd-inner-container W(1235px) W(980px)--lg W(640px)--md W(100%)--sm Mx(20px)--sm D(f) Ai(c)">
                    <button
                      class="P(0) Cur(p) Bd(n) Bgc(t) C(engadgetFontLightGray) P(0) Pend(16px)! Bd(n) Bgc(t) C(engadgetFontLightGray)"
                      data-component="IconButton" type="submit"
                      aria-label="Search" title="Search"
                      data-ylk="sec:nav;slk:vert-srch;elm:search;elmt:icon;itc:1"
                      data-rapid_p="64" data-v9y="0"><svg
                        class="Fill(engadgetFontLightGray) Stk(engadgetFontLightGray) W(24px) H(24px) W(20px)--xs H(20px)--xs Cur(p)"
                        width="24" style="stroke-width:0;vertical-align:bottom"
                        height="24" viewBox="0 0 24 24" data-icon="search">
                        <path
                          d="M9 3C5.686 3 3 5.686 3 9c0 3.313 2.686 6 6 6s6-2.687 6-6c0-3.314-2.686-6-6-6m13.713 19.713c-.387.388-1.016.388-1.404 0l-7.404-7.404C12.55 16.364 10.85 17 9 17c-4.418 0-8-3.582-8-8 0-4.42 3.582-8 8-8s8 3.58 8 8c0 1.85-.634 3.55-1.69 4.905l7.403 7.404c.39.386.39 1.015 0 1.403">
                        </path>
                      </svg></button><input type="text" id="search-terms"
                      name="p"
                      class="Fz(22px) Fz(18px)--xs C(engadgetFontLightGray) C(engadgetFontLightGray)::ph Bgc(t) O(n) Bd(n) Fxg(1)"
                      placeholder="What are you searching for?" autofocus=""
                      required="" aria-autocomplete="both"
                      aria-roledescription="combobox" aria-expanded="false"
                      aria-label="Search" data-rapid_p="65" data-v9y="0"
                      role="combobox"><input type="hidden" name="fr"
                      value="engadget" data-rapid_p="66" data-v9y="0"><button
                      class="P(0) Cur(p) Bd(n) Bgc(t) C(engadgetFontLightGray) wafer-toggle Mend(-4px) clsBtn has-toggle-click has-wafer-click"
                      data-component="IconButton" type="button"
                      aria-label="Cancel" title="Cancel"
                      data-wf-toggle-class="click:toggle:active"
                      data-wf-toggle-target="#search" data-rapid_p="67"
                      data-v9y="0"><svg
                        class="Fill(engadgetFontLightGray) Stk(engadgetFontLightGray) clsBtn:h_Fill(#fff) clsBtn:h_Stk(#fff) W(30px) H(30px) W(24px)!--xs H(24px)!--xs Cur(p)"
                        width="48" style="stroke-width:0;vertical-align:bottom"
                        height="48" viewBox="0 0 48 48" data-icon="close">
                        <path
                          d="M37.98 34.827l-9.9-9.9 9.9-9.898c.78-.782.78-2.05 0-2.83-.78-.78-2.047-.78-2.828 0l-9.9 9.9-9.898-9.9c-.78-.78-2.048-.78-2.828 0-.78.78-.78 2.047 0 2.828l9.9 9.9-9.9 9.898c-.78.78-.78 2.047 0 2.828.78.78 2.047.78 2.828 0l9.9-9.9 9.898 9.9c.78.78 2.048.78 2.828 0 .782-.78.782-2.047 0-2.827z">
                        </path>
                      </svg></button></div>
                </div>
                <div
                  class="W(100%) Bgc(engadgetGray) C(engadgetFontLightGray) Bdtc(engadgetBlack) Bdts(s) Bdtw(1px) D(f) Jc(sa)"
                  style="transition:opacity .3s ease">
                  <div class="rwd-outer-container W(100vw) D(f) Jc(sa)"
                    data-component="RWDContainer">
                    <div
                      class="rwd-inner-container W(1235px) W(980px)--lg W(640px)--md W(100%)--sm Mx(20px)--sm D(f)">
                      <div id="search-results" class="W(100%) Ov(a) Mah(330px)">
                        <div class="sa-tray-ctn">
                          <div class="sa-tray"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </form>
            </nav>
          </div>
          <script>
            window.performance.mark('nav');
            window.performance.measure('navDone', 'PageStart', 'nav');

          </script>
        </div>
      </div>
      <div id="Masterwrap"
        class="W(100%) Pos(r) Op(1) Reader-open_Op(0) Trs($pageTransition)">
        <div id="Page"
          class="D(f)! Fld(r) Fxw(w) Jc(sb) Ai(fs) Z(1) Pos(r) Mx(a)">
          <div
            class="Bgc(engadgetSteelGray) W(100vw) D(f) Ai(st) Pos(r) Z(1) Jc(c) M(MarginCenter)">
            <div id="LB-MULTI_ATF" class="Pb(25px) Pt(25px)"></div>
            <script type="text/javascript">
              AdsServicePosition['LB-MULTI_ATF'] = {
                alias: '93484774',
                sizes: ["728x90", "970x250"]
              };
              AdsClientPosition['LB-MULTI_ATF'] = {
                targetElement: 'LB-MULTI_ATF',
                features: {
                  rotation: {
                    enabled: true
                  }
                }
              };

            </script>
          </div>
          <main class="W(100%)" role="main">
            <div id="module-article-container" class="wafer-rapid-module">
              <div>
                <section data-component="ArticleContainer">
                  <div class="rwd-outer-container W(100vw) D(f) Jc(sa)"
                    data-component="RWDContainer">
                    <div
                      class="rwd-inner-container W(1235px) W(980px)--lg W(640px)--md W(100%)--sm Mx(20px)--sm Mx(a)!--sm Bxz(bb) Px(20px)--sm">
                      <div class="Mb(35px)" data-component="ArticleHeader">
                        <div class="Mb(20px) ">
                          <h1
                            class="Fz(56px) Lh(60px) Fz(24px)!--sm Lh(28px)!--sm C(engadgetBlack) Mb(20px) Mt(20px)">
                            All GitHub users can keep their code private</h1>
                          <span
                            class="Fz(24px) Lh(32px) Fz(20px)!--sm Lh(28px)!--sm C(engadgetBlack) serif">Previously
                            only paid users could have private projects.</span>
                        </div>
                        <div
                          class="W(1/2) W(100%)!--sm D(b)!--md D(n)--lg D(n)--xl"
                          data-component="ArticleAuthorInfo">
                          <div class="Va(m)">
                            <div class="D(f) Jc(fs)">
                              <div><img alt="Mallory Locklear"
                                  src="https://s.yimg.com/uu/api/res/1.2/j3PQ1zsi0oliqo.Q0_AERw--~B/Zmk9ZmlsbDtoPTk2O3B5b2ZmPTA7dz05NjthcHBpZD15dGFjaHlvbg--/https://s.yimg.com/uu/api/res/1.2/OJG8v_fkJXP_zkCjns7h1A--~B/aD01NDc7dz01NDc7YXBwaWQ9eXRhY2h5b24-/https://s.blogcdn.com/www.engadget.com/media/2017/05/img1284-ed.jpg.cf.webp"
                                  class="W(48px) H(48px) Bdrs(50%) hoverZoomLink">
                              </div>
                              <div class="Fz(18px) Pstart(10px) Mt(-2px)"><span
                                  class="C(engadgetPurple) Lh(20px) Fw(500)"><a
                                    alt="Mallory Locklear"
                                    href="/about/editors/mallory-locklear"
                                    class="D(ib) Ai(c) Td(n) Cur(p) C(engadgetPurple) C(engadgetFontDarkPurple):h"
                                    data-ylk="slk:Mallory Locklear;pos:1;cpos:1;elm:author"
                                    data-rapid_p="1" data-v9y="1">M.
                                    Locklear</a></span><span
                                  class="C(engadgetFontDarkGray) Lh(28px) Mstart(8px)"><span
                                    class="Fz(18px) D(n)--sm">|</span><span
                                    class="Mstart(8px) Fz(18px) Lh(24px)--sm Mstart(0px)--sm D(b)--sm ">01.07.19</span></span>
                                <div class="Lh(20px) D(n)--sm"><a
                                    alt="@mallorylocklear"
                                    class="Td(n) C(engadgetPurple)"
                                    href="https://www.twitter.com/mallorylocklear"
                                    target="_blank" rel="noreferrer noopener"
                                    data-ylk="slk:mallorylocklear;pos:1;cpos:1;elm:hdln;subsec:author"
                                    data-rapid_p="2"
                                    data-v9y="1">@mallorylocklear</a></div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="ads-cont">
                        <div class="Cl(end)"></div>
                        <div
                          class="Fl(end) W(300px) Miw(300px) Mstart(30px) Maw(300px) Ov(h) D(n)--lg">
                          <div class=" Bgc(white)" data-component="JacAd">
                            <div id="RR-MULTI_ATF"></div>
                            <script type="text/javascript">
                              AdsServicePosition["RR-MULTI_ATF"] = {
                                alias: '93484775',
                                sizes: ["300x250", "300x600"]
                              };
                              AdsClientPosition["RR-MULTI_ATF"] = {
                                targetElement: 'RR-MULTI_ATF',
                                features: {
                                  rotation: {
                                    enabled: true
                                  }
                                }
                              };
                              JacCallbacks["RR-MULTI_ATF"] = {};

                            </script>
                          </div>
                          <div data-component="GeminiRightRailAd">
                            <div id="gemini-right-rail-thumbnails"
                              class="Pos(r) Mt(10px)">
                              <h4
                                class="Fz(20px) C(engadgetSteelGray) Pb(5px) Mt(0px)! Mb(15px) Lh(30px) Fw(700)">
                                Sponsored Links</h4><button
                                class="gemini-disclaimer Pos(a) T(5px) End(0) Bd(n) Cur(p)"
                                data-ylk="sec:gemini-right;subsec:account;slk:privacy;elm:link;itc:0"
                                data-rapid_p="3" data-v9y="1"><img
                                  src="https://s.yimg.com/cv/apiv2/adbuilder/gemini.png"
                                  alt="Relevant ads info" width="15"
                                  height="15"></button>
                              <div class="sponsored-links">
                                <div></div>
                              </div>
                            </div>
                            <script type="text/javascript">
                              (function (w, d) {
                                w.nativeSectionCodes = w.nativeSectionCodes ||
                                  [];
                                w.nativeSectionCodes = w.nativeSectionCodes
                                  .concat([
                                    "d9cb7420-8614-47e1-88fa-3383be601dc0",
                                    "fd1fee89-d620-41fe-b5d0-8b2ec3f5fea3"
                                  ]);
                                w.apiKey = "HJRFTXTT38GD283N672X";
                                w.imageType = "thumbnail";

                                var script = d.createElement("script");
                                script.async = true;

                                script.src =
                                  "https://s.yimg.com/dy/ads/native.js";
                                d.body.appendChild(script);

                                w.nativePassbackHandler = w
                                  .nativeErrorHandler = function (data) {
                                    if (data && data.code ===
                                      'd9cb7420-8614-47e1-88fa-3383be601dc0'
                                      ) {
                                      var container = document.getElementById(
                                        'gemini-right-rail-thumbnails');
                                      if (container) {
                                        container.parentNode.removeChild(
                                          container);
                                      }
                                    }
                                  };
                              })(window.top, window.top.document);

                            </script>
                          </div>
                          <div class="Mt(40px) Bgc(white)"
                            data-component="JacAd">
                            <div id="RR-MULTI_BTF"></div>
                            <script type="text/javascript">
                              AdsServicePosition["RR-MULTI_BTF"] = {
                                alias: '93484776',
                                sizes: ["300x250", "300x600"]
                              };
                              AdsClientPosition["RR-MULTI_BTF"] = {
                                targetElement: 'RR-MULTI_BTF',
                                render: {
                                  requireViewable: true
                                },
                                features: {
                                  rotation: {
                                    enabled: true
                                  }
                                }
                              };
                              JacCallbacks["RR-MULTI_BTF"] = {};

                            </script>
                          </div>
                          <div data-conversation-spotlight="true"
                            data-spotlight-sidebar="true"></div>
                        </div>
                      </div>
                      <div class="Maw(905px)">
                        <div class="D(f)" data-component="ArticleBody">
                          <div class="W(200px) Miw(200px) Mend(30px) D(n)--md">
                            <div class="W(100%) Mb(20px)"
                              data-component="VerticalAuthorInfo">
                              <div class="Va(m)">
                                <div class="Ta(c)">
                                  <div class="W(100%)"><img
                                      alt="Mallory Locklear"
                                      src="https://s.yimg.com/uu/api/res/1.2/j3PQ1zsi0oliqo.Q0_AERw--~B/Zmk9ZmlsbDtoPTk2O3B5b2ZmPTA7dz05NjthcHBpZD15dGFjaHlvbg--/https://s.yimg.com/uu/api/res/1.2/OJG8v_fkJXP_zkCjns7h1A--~B/aD01NDc7dz01NDc7YXBwaWQ9eXRhY2h5b24-/https://s.blogcdn.com/www.engadget.com/media/2017/05/img1284-ed.jpg.cf.webp"
                                      class="W(96px) H(96px) Bdrs(50%) Mx(a) My(0) hoverZoomLink">
                                  </div>
                                  <div class="Fz(18px) Lh(20px) Mt(10px)">
                                    <div class="Fw(700) Pb(5px)"><a
                                        alt="Mallory Locklear"
                                        href="/about/editors/mallory-locklear"
                                        class="Td(n) C(engadgetSteelGray)"
                                        data-ylk="slk:Mallory Locklear;pos:1;cpos:1;elm:author"
                                        data-rapid_p="4" data-v9y="1">M.
                                        Locklear</a></div>
                                    <div class="Pb(5px)"><a
                                        alt="@mallorylocklear"
                                        href="https://www.twitter.com/mallorylocklear"
                                        class="Td(n) C(engadgetSteelGray)"
                                        target="_blank"
                                        rel="noreferrer noopener"
                                        data-ylk="slk:mallorylocklear;pos:1;cpos:1;elm:hdln;subsec:author"
                                        data-rapid_p="5"
                                        data-v9y="1">@mallorylocklear</a></div>
                                    <div class="C(engadgetSteelGray)"><span
                                        class="Fz(18px) Lh(28px)">January 7th,
                                        2019</span></div>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div class="Bdbs(s) Bdbw(1px) Bdbc(engadgetGray)">
                            </div>
                            <div data-component="SocialShare"
                              class="D(f) Jc(sa) Ai(c)">
                              <div class="Ta(c) My(20px)"><a href="#comments"
                                  title="comments" aria-label="comments"
                                  data-ylk="sec:article;slk:show comments;itc:1;_p:0;elm:cmmt_open"
                                  data-rapid_p="6" data-v9y="1"><svg
                                    class="Fill(#000) W(26px) H(26px) Cur(p)"
                                    width="48"
                                    style="stroke-width:0;vertical-align:bottom"
                                    height="48" viewBox="0 0 48 48"
                                    data-icon="message">
                                    <path
                                      d="M40.8 28.9c0 2-1.7 3.7-3.8 3.7H13.4l-5.6 5.2V10.6c0-2 1.7-3.7 3.8-3.7H37c2.1 0 3.8 1.6 3.8 3.7v18.3zM11.7 3.3c-4.1 0-7.5 3.3-7.5 7.3v31.1c0 .7.4 1.4 1.1 1.7.2.1.5.2.7.2.5 0 .9-.2 1.2-.5l7.6-6.8H37c4.1 0 7.5-3.3 7.5-7.3V10.6c0-4-3.4-7.3-7.5-7.3H11.7zm21.8 11H15.2c-1 0-1.8.8-1.8 1.8s.8 1.8 1.8 1.8h18.3c1 0 1.8-.8 1.8-1.8.1-1-.8-1.8-1.8-1.8m-18.3 7.3c-1 0-1.8.8-1.8 1.8s.8 1.8 1.8 1.8h18.3c1 0 1.8-.8 1.8-1.8s-.8-1.8-1.8-1.8H15.2z">
                                    </path>
                                  </svg></a></div>
                              <div class="Ta(c) My(20px)"><button
                                  data-share="https://www.facebook.com/dialog/share?app_id=464864107046584&amp;display=popup&amp;href=https://www.engadget.com/2019-01-07-all-github-users-keep-code-private.html"
                                  data-ylk="sec:article;slk:Facebook;itc:0;_p:0;elm:share;g:23636374"
                                  title="Facebook share"
                                  aria-label="Facebook share"
                                  class="engadget-share C(engadgetFontLightGray) C(#fff):h  W(100%) H(26px) M(0) Bd(n) Bg(n)"
                                  data-rapid_p="7" data-v9y="1"><svg
                                    class="Fill(#1877f2) W(26px) H(26px) Cur(p)"
                                    width="32"
                                    style="stroke-width:0;vertical-align:bottom"
                                    height="32" viewBox="0 0 32 32"
                                    data-icon="LogoFacebook">
                                    <path
                                      d="M12.752 30.4V16.888H9.365V12.02h3.387V7.865c0-3.264 2.002-6.264 6.613-6.264 1.866 0 3.248.19 3.248.19l-.11 4.54s-1.404-.013-2.943-.013c-1.66 0-1.93.81-1.93 2.152v3.553h5.008l-.22 4.867h-4.786V30.4h-4.88z">
                                    </path>
                                  </svg></button></div>
                              <div class="Ta(c) My(20px)"><button
                                  data-share="https://twitter.com/share?url=https://www.engadget.com/2019-01-07-all-github-users-keep-code-private.html&amp;text=All GitHub users can keep their code private&amp;via=engadget"
                                  data-ylk="sec:article;slk:Twitter;itc:0;_p:1;elm:share;g:23636374"
                                  title="Twitter feed" aria-label="Twitter feed"
                                  class="engadget-share C(engadgetFontLightGray) C(#fff):h  W(100%) H(26px) M(0) Bd(n) Bg(n)"
                                  data-rapid_p="8" data-v9y="1"><svg
                                    class="Fill(#1da1f2) W(26px) H(26px) Cur(p)"
                                    width="32"
                                    style="stroke-width:0;vertical-align:bottom"
                                    height="32" viewBox="0 0 32 32"
                                    data-icon="LogoTwitter">
                                    <path
                                      d="M30.402 7.094c-1.058.47-2.198.782-3.392.928 1.218-.725 2.154-1.885 2.595-3.256-1.134.674-2.405 1.165-3.75 1.43-1.077-1.148-2.612-1.862-4.31-1.862-3.268 0-5.915 2.635-5.915 5.893 0 .464.056.91.155 1.34-4.915-.244-9.266-2.59-12.18-6.158-.51.87-.806 1.885-.806 2.96 0 2.044 1.045 3.847 2.633 4.905-.974-.032-1.883-.3-2.68-.736v.07c0 2.857 2.034 5.236 4.742 5.773-.498.138-1.022.21-1.56.21-.38 0-.75-.034-1.11-.103.75 2.344 2.93 4.042 5.518 4.09-2.024 1.58-4.57 2.523-7.333 2.523-.478 0-.952-.032-1.41-.085 2.613 1.674 5.72 2.65 9.054 2.65 10.872 0 16.814-8.976 16.814-16.765 0-.254-.008-.507-.018-.762 1.155-.83 2.155-1.868 2.95-3.047z">
                                    </path>
                                  </svg></button></div>
                            </div>
                            <div class="Bdbs(s) Bdbw(1px) Bdbc(engadgetGray)">
                            </div>
                            <div
                              class="Mt(20px) Fz(16px) Lh(24px) C(engadgetGray)"
                              data-component="TagCloud"><span class="Fw(500)">In
                                this article: </span><a data-ylk="itc:0;elm:tag"
                                href="/tag/gear"
                                class="C(engadgetFontDarkGray) C(engadgetFontBlack):h Td(n) Fz(16px) Lh(24px)"
                                data-rapid_p="9" data-v9y="1">gear</a><span>,
                              </span><a data-ylk="itc:0;elm:tag"
                                href="/tag/github"
                                class="C(engadgetFontDarkGray) C(engadgetFontBlack):h Td(n) Fz(16px) Lh(24px)"
                                data-rapid_p="10" data-v9y="1">github</a><span>,
                              </span><a data-ylk="itc:0;elm:tag"
                                href="/tag/internet"
                                class="C(engadgetFontDarkGray) C(engadgetFontBlack):h Td(n) Fz(16px) Lh(24px)"
                                data-rapid_p="11"
                                data-v9y="1">internet</a><span>, </span><a
                                data-ylk="itc:0;elm:tag" href="/tag/microsoft"
                                class="C(engadgetFontDarkGray) C(engadgetFontBlack):h Td(n) Fz(16px) Lh(24px)"
                                data-rapid_p="12"
                                data-v9y="1">microsoft</a><span>, </span><a
                                data-ylk="itc:0;elm:tag" href="/tag/private"
                                class="C(engadgetFontDarkGray) C(engadgetFontBlack):h Td(n) Fz(16px) Lh(24px)"
                                data-rapid_p="13"
                                data-v9y="1">private</a><span>, </span><a
                                data-ylk="itc:0;elm:tag" href="/tag/services"
                                class="C(engadgetFontDarkGray) C(engadgetFontBlack):h Td(n) Fz(16px) Lh(24px)"
                                data-rapid_p="14" data-v9y="1">services</a>
                            </div>
                          </div>
                          <div class="Maw(675px) W(100%)">
                            <figure class="M(0) " data-component="DefaultLede">
                              <img
                                src="https://s.yimg.com/uu/api/res/1.2/UdtkLCXFNoeaVmiAAtPvTw--~B/Zmk9ZmlsbDtoPTQ1MTt3PTY3NTthcHBpZD15dGFjaHlvbg--/https://s.yimg.com/uu/api/res/1.2/K0jagCHTBRKuVb_rt7LQ_g--~B/aD0xMDY5O3c9MTYwMDthcHBpZD15dGFjaHlvbg--/https://o.aolcdn.com/images/dims?crop=4834%2C3230%2C0%2C0&amp;quality=85&amp;format=jpg&amp;resize=1600%2C1069&amp;image_uri=https%3A%2F%2Fs.yimg.com%2Fos%2Fcreatr-images%2F2019-01%2F2d1e22f0-12ae-11e9-bae7-60d640081814&amp;client=a1acac3e1b3290917d92&amp;signature=269ae0af3a1a1772ffea6759d126791b9ad25184.cf.webp"
                                class="W(100%) H(a) hoverZoomLink"
                                alt="Chesnot via Getty Images" width="1600"
                                height="1069">
                              <figcaption
                                class="Mt(7px) Fz(15px) Lh(20px) C(engadgetGray) Pb(25px)">
                                Chesnot via Getty Images<span
                                  class="C(engadgetFontDarkGray)"></span>
                              </figcaption>
                            </figure>
                            <div class="article-text">
                              <p>Historically, if you've wanted to create a
                                private repository on <a
                                  href="https://www.engadget.com/2018-06-19-github-education-is-a-free-software-development-package-for-scho.html"
                                  data-ylk="elm:context_link;itc:0"
                                  data-rapid_p="15" data-v9y="0">GitHub</a>, you
                                had to be a paying user, but that's about to
                                change. Starting today, free GitHub users will
                                have access to unlimited private projects as
                                long as there are three or fewer collaborators
                                on board. For larger projects, you'll have to
                                join a paid plan or make your code public, as
                                GitHub isn't changing how it manages public
                                repositories.</p>
                              <!-- end-legacy-contents -->
                              <center>
                                <div
                                  class="D(f) Jc(c) Ai(c) W(100%) H(72px) H(120px)!--sm W(320px)!--sm Bgc(engadgetGhostWhite) C(engadgetFontBlack) Bdw(1px) Bdts(s) Bdc(#eaeaeb) Bds(s) Ov(h) Mt(20px) Pstart(10px)--md"
                                  data-reactroot="">
                                  <div
                                    class="Bgc(engadgetPurple) Bdrs(50%) H(18px) W(18px) Miw(18px) Mih(18px) Lh(18px) Fz(12px) Ta(c) C(#fff) Mx(10px)">
                                    i</div>
                                  <div
                                    class="Pend(10px) Fz(14px) Lh(24px) Va(m) Wow(bw)">
                                    This content is not available due to your
                                    privacy preferences. <a
                                      data-ylk="sec:red-bar;subsec:privacy;slk:This content is not available due to your privacy preferences. <link>Update your settings here, then reload the page to see it.</link>;elm:link;itc:0"
                                      href="https://engadget.mydashboard.oath.com/guc-redirect?cardType=group&amp;app=thirdPartyContentEmbed&amp;bucket=pd_2&amp;lang=en-US"
                                      style="color:inherit" target="_blank"
                                      rel="noopener noreferrer"
                                      class="Bgc(t) C(engadgetPurple)! Op(0.75) Td(n) Op(1):h"
                                      data-reactroot="" data-rapid_p="16"
                                      data-v9y="0">Update your settings here,
                                      then reload the page to see it.</a></div>
                                </div>

                              </center>

                              <p>Microsoft <a
                                  href="https://www.engadget.com/2018-10-26-microsoft-github-acquisition-complete.html"
                                  data-ylk="elm:context_link;itc:0"
                                  data-rapid_p="17" data-v9y="0">acquired
                                  GitHub</a> last year for $7.5 billion. GitHub
                                CEO Nat Friedman said at the time that the team
                                would be focused on making GitHub more
                                accessible to developers around the world and
                                ensuring reliability, security and performance.
                                He also reiterated that GitHub would continue to
                                operate independently. "We love GitHub because
                                of the deep care and thoughtfulness that goes
                                into every facet of the developer's experience,"
                                Friedman wrote in a <a
                                  href="https://blog.github.com/2018-10-26-github-and-microsoft/"
                                  target="_blank"
                                  data-ylk="elm:context_link;itc:0"
                                  data-rapid_p="18" data-v9y="0">blog post</a>.
                                "I understand and respect this, and know that we
                                will continue to build tasteful, snappy,
                                polished tools that developers love."</p>

                              <p>Friedman <a
                                  href="https://twitter.com/natfriedman/status/1082345112653332480"
                                  target="_blank"
                                  data-ylk="elm:context_link;itc:0"
                                  data-rapid_p="19" data-v9y="0">said</a> today
                                that unlimited free private repositories has
                                been the most requested feature from GitHub
                                users. The company also announced GitHub
                                Enterprise, which brings together Enterprise
                                Cloud and Enterprise Server -- formerly GitHub
                                Business Cloud and GitHub Enterprise,
                                respectively -- into one, unified product. The
                                private repositories for free users are rolling
                                out today.</p>

                              <div
                                class="vdb_player vdb_5c2e27c40822e85f0f059ebb564f3144ff690c0a7c285e51"
                                id="5c2e27c40822e85f0f059ebb">
                                <img
                                  src="https://o.aolcdn.com/images/dar/5845cadfecd996e0372f/c85ce7e289fa2e59e7778398601d66554e58bcd1/aHR0cHM6Ly9jZG4udmlkaWJsZS50di9wcm9kLzIwMTgtMTEvMDcvNWJlMzBhM2QzZGU2YWM1OGNkMjU5NzUzX3YxLmpwZw=="
                                  style="display:none;">
                              </div>
                            </div>
                            <div data-component="TagBoilerplate"></div>
                            <div
                              class="W(100%) Bdts(s) Bdtw(1px) Bdtc(engadgetWhiteGray)">
                            </div>
                            <div
                              class="Fz(16px) sans C(engadgetFontBlack) Mt(20px) Mb(20px) Lh(24px)">
                              <em>All products recommended by Engadget are
                                selected by our editorial team, independent of
                                our parent company. Some of our stories include
                                affiliate links. If you buy something through
                                one of these links, we may earn an affiliate
                                commission.</em></div>
                          </div>
                        </div>
                        <div
                          class="W(100%) Bdbs(s) Bdbw(1px) Bdbc(engadgetGutter) Mb(40px)">
                        </div>
                        <section data-component="PopularStreamHorizontal">
                          <header
                            class="Fz(20px) Lh(20px) Fw(b) Mb(10px) Mt(10px)">
                            Popular on Engadget</header>
                          <ul
                            class="D(f) Jc(sb) Flw(w) M(0) P(0) List(n) Mt(20px)">
                            <li class="W(22%) W(47%)--sm Mb(30px)--sm Bxz(bb)">
                              <article data-component="PostCard">
                                <div data-component="VerticalCard"><a
                                    title="How to pre-order Apple’s new AirPods and MacBook Pros"
                                    href="https://www.engadget.com/how-to-pre-order-apple-airpods-third-generation-macbook-pro-14-inch-16-inch-m1-pro-m1-max-182308261.html"
                                    class="D(b) Mb(15px)"
                                    data-ylk="sec:PopularOnEngadget;slk:How to pre-order Apple’s new AirPods and MacBook Pros;elm:img;ct:story;itc:0"
                                    data-rapid_p="20" data-v9y="0"><img
                                      class="D(b) W(100%) H(a) hoverZoomLink"
                                      loading="eager"
                                      alt="How to pre-order Apple’s new AirPods and MacBook Pros"
                                      src="https://o.aolcdn.com/images/dims?image_uri=https%3A%2F%2Fs.yimg.com%2Fep%2Fcx%2Fblendr%2Fv2%2Fimage-macbook-recirc-jpg_1634738156230.jpeg&amp;thumbnail=250%2C171&amp;client=49kdj93ncb8s938hkdo&amp;signature=87876fe9e7835a975c86fa1f91b0f42976448d7f"
                                      width="640" height="421"></a>
                                  <div class="D(f) Fld(c) Ai(fs)"
                                    data-component="PostInfo">
                                    <h2 class="Lh(24px) My(0)"><a
                                        href="https://www.engadget.com/how-to-pre-order-apple-airpods-third-generation-macbook-pro-14-inch-16-inch-m1-pro-m1-max-182308261.html"
                                        class="C(engadgetSteelGray)  Fw(400) Td(n) Bdbw(1px):h Bdbc(lightgrey):h Bdbs(s):h Fz(16px) Lh(24px) Fw(500)! "
                                        data-ylk="sec:PopularOnEngadget;slk:How to pre-order Apple’s new AirPods and MacBook Pros;elm:hdln;ct:story;aid:recirc-module;itc:0"
                                        data-rapid_p="21" data-v9y="0">How to
                                        pre-order Apple’s new AirPods and
                                        MacBook Pros</a></h2>
                                  </div>
                                </div>
                              </article>
                            </li>
                            <li class="W(22%) W(47%)--sm Mb(30px)--sm Bxz(bb)">
                              <article data-component="PostCard">
                                <div data-component="VerticalCard"><a
                                    title="How to pre-order the Google Pixel 6 and Pixel 6 Pro"
                                    href="/how-to-pre-order-google-pixel-6-pixel-6-pro-smartphones-173051481.html"
                                    class="D(b) Mb(15px)"
                                    data-ylk="sec:PopularOnEngadget;slk:How to pre-order the Google Pixel 6 and Pixel 6 Pro;elm:img;ct:story;itc:0"
                                    data-rapid_p="22" data-v9y="0"><img
                                      class="D(b) W(100%) H(a) hoverZoomLink"
                                      loading="eager"
                                      alt="How to pre-order the Google Pixel 6 and Pixel 6 Pro"
                                      src="https://s.yimg.com/uu/api/res/1.2/m8a12ZuY1t84aX5u3N0oiw--~B/Zmk9ZmlsbDtoPTE3MTtweW9mZj0wO3c9MjUwO2FwcGlkPXl0YWNoeW9u/https://s.yimg.com/os/creatr-uploaded-images/2021-10/547861b0-305d-11ec-bddf-c85f95fd1b12.cf.webp"
                                      width="640" height="421"></a>
                                  <div class="D(f) Fld(c) Ai(fs)"
                                    data-component="PostInfo">
                                    <h2 class="Lh(24px) My(0)"><a
                                        href="/how-to-pre-order-google-pixel-6-pixel-6-pro-smartphones-173051481.html"
                                        class="C(engadgetSteelGray)  Fw(400) Td(n) Bdbw(1px):h Bdbc(lightgrey):h Bdbs(s):h Fz(16px) Lh(24px) Fw(500)! "
                                        data-ylk="sec:PopularOnEngadget;slk:How to pre-order the Google Pixel 6 and Pixel 6 Pro;elm:hdln;ct:story;aid:4c543f59-43ed-4719-afd3-ffaa6370b737;itc:0"
                                        data-rapid_p="23" data-v9y="0">How to
                                        pre-order the Google Pixel 6 and Pixel 6
                                        Pro</a></h2>
                                  </div>
                                </div>
                              </article>
                            </li>
                            <li class="W(22%) W(47%)--sm Mb(30px)--sm Bxz(bb)">
                              <article data-component="PostCard">
                                <div data-component="VerticalCard"><a
                                    title="The new Assassin's Creed educational tour lets you explore the Viking Age"
                                    href="/assassins-creed-valhalla-discovery-tour-viking-age-131501270.html"
                                    class="D(b) Mb(15px)"
                                    data-ylk="sec:PopularOnEngadget;slk:The new Assassin's Creed educational tour lets you explore the Viking Age;elm:img;ct:story;itc:0"
                                    data-rapid_p="24" data-v9y="0"><img
                                      class="D(b) W(100%) H(a) hoverZoomLink"
                                      loading="eager"
                                      alt="The new Assassin's Creed educational tour lets you explore the Viking Age"
                                      src="https://s.yimg.com/uu/api/res/1.2/TmYfddKA9rxXvBmcfl.XMg--~B/Zmk9ZmlsbDtoPTE3MTtweW9mZj0wO3c9MjUwO2FwcGlkPXl0YWNoeW9u/https://s.yimg.com/os/creatr-uploaded-images/2021-10/4b831eb0-31a2-11ec-be77-d3736c542785.cf.webp"
                                      width="640" height="421"></a>
                                  <div class="D(f) Fld(c) Ai(fs)"
                                    data-component="PostInfo">
                                    <h2 class="Lh(24px) My(0)"><a
                                        href="/assassins-creed-valhalla-discovery-tour-viking-age-131501270.html"
                                        class="C(engadgetSteelGray)  Fw(400) Td(n) Bdbw(1px):h Bdbc(lightgrey):h Bdbs(s):h Fz(16px) Lh(24px) Fw(500)! "
                                        data-ylk="sec:PopularOnEngadget;slk:The new Assassin's Creed educational tour lets you explore the Viking Age;elm:hdln;ct:story;aid:16f91c41-ed10-4c98-8930-c35ad45b56f5;itc:0"
                                        data-rapid_p="25" data-v9y="0">The new
                                        Assassin's Creed educational tour lets
                                        you explore the Viking Age</a></h2>
                                  </div>
                                </div>
                              </article>
                            </li>
                            <li class="W(22%) W(47%)--sm Mb(30px)--sm Bxz(bb)">
                              <article data-component="PostCard">
                                <div data-component="VerticalCard"><a
                                    title="Surface Duo 2 review: Microsoft's second dual-screen hybrid is a letdown"
                                    href="/microsoft-surface-duo-2-review-camera-sample-specs-glance-bar-130004036.html"
                                    class="D(b) Mb(15px)"
                                    data-ylk="sec:PopularOnEngadget;slk:Surface Duo 2 review: Microsoft's second dual-screen hybrid is a letdown;elm:img;ct:story;itc:0"
                                    data-rapid_p="26" data-v9y="0"><img
                                      class="D(b) W(100%) H(a) hoverZoomLink"
                                      loading="eager"
                                      alt="Surface Duo 2 review: Microsoft's second dual-screen hybrid is a letdown"
                                      src="https://s.yimg.com/uu/api/res/1.2/xJtcMSITDdo5DSHA9oT_tw--~B/Zmk9ZmlsbDtoPTE3MTtweW9mZj0wO3c9MjUwO2FwcGlkPXl0YWNoeW9u/https://s.yimg.com/os/creatr-uploaded-images/2021-10/a03fcff0-3203-11ec-bfd7-6f98d553a93e.cf.webp"
                                      width="640" height="421"></a>
                                  <div class="D(f) Fld(c) Ai(fs)"
                                    data-component="PostInfo">
                                    <h2 class="Lh(24px) My(0)"><a
                                        href="/microsoft-surface-duo-2-review-camera-sample-specs-glance-bar-130004036.html"
                                        class="C(engadgetSteelGray)  Fw(400) Td(n) Bdbw(1px):h Bdbc(lightgrey):h Bdbs(s):h Fz(16px) Lh(24px) Fw(500)! "
                                        data-ylk="sec:PopularOnEngadget;slk:Surface Duo 2 review: Microsoft's second dual-screen hybrid is a letdown;elm:hdln;ct:story;aid:59213511-d4f6-47b1-91f2-c82eee36c9af;itc:0"
                                        data-rapid_p="27" data-v9y="0">Surface
                                        Duo 2 review: Microsoft's second
                                        dual-screen hybrid is a letdown</a></h2>
                                  </div>
                                </div>
                              </article>
                            </li>
                          </ul>
                        </section>
                        <div
                          class="D(f) Jc(c) Ai(c) W(100%) H(72px) H(120px)!--sm W(320px)!--sm Bgc(engadgetGhostWhite) C(engadgetFontBlack) Bdw(1px) Bdts(s) Bdc(#eaeaeb) Bds(s) Ov(h) Mt(20px) Pstart(10px)--md">
                          <div
                            class="Bgc(engadgetPurple) Bdrs(50%) H(18px) W(18px) Miw(18px) Mih(18px) Lh(18px) Fz(12px) Ta(c) C(#fff) Mx(10px)">
                            i</div>
                          <div
                            class="Pend(10px) Fz(14px) Lh(24px) Va(m) Wow(bw)">
                            This content is not available due to your privacy
                            preferences. <a
                              data-ylk="sec:red-bar;subsec:privacy;slk:This content is not available due to your privacy preferences. <link>Update your settings here, then reload the page to see it.</link>;elm:link;itc:0"
                              href="https://engadget.mydashboard.oath.com/guc-redirect?cardType=group&amp;app=thirdPartyContentEmbed&amp;bucket=pd_2&amp;lang=undefined"
                              style="color:inherit" target="_blank"
                              rel="noopener noreferrer"
                              class="Bgc(t) C(engadgetPurple)! Op(0.75) Td(n) Op(1):h"
                              data-reactroot="" data-rapid_p="28"
                              data-v9y="0">Update your settings here, then
                              reload the page to see it.</a></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </section>
              </div>
              <script>
                window.performance.mark('article-container');
                window.performance.measure('article-containerDone', 'PageStart',
                  'article-container');

              </script>
            </div>
          </main>
          <div class="W(100vw) D(f) Jc(sa) Py(15px)!--sm Py(20px)">
            <div id="readmo-below-article-content"
              class="W(1235px) W(980px)--lg W(640px)--md W(100%)--sm Mx(20px)--sm">
            </div>
          </div>
          <script type="text/javascript">
            (window.readmo = window.readmo || []).push({
              section: '10a6a120170149e6925601d0f1337f93',
              container: '#readmo-below-article-content',
              infinite: false,
              params: {}
            });
            !(function (d) {
              var script = d.createElement("script");
              script.async = true;
              script.src = 'https://s.yimg.com/dy/ads/readmo.js';
              d.body.appendChild(script);
            })(document);

          </script>
          <div
            class="Bgc(white) W(100vw) D(f) Ai(st) Pos(r) Z(1) Jc(c) M(MarginCenter)">
            <div id="LB-MULTI_BTF" class="Pb(25px) Pt(25px)"></div>
            <script type="text/javascript">
              AdsServicePosition['LB-MULTI_BTF'] = {
                alias: '963875579',
                sizes: ["728x90", "970x250"]
              };
              AdsClientPosition['LB-MULTI_BTF'] = {
                targetElement: 'LB-MULTI_BTF',
                render: {
                  requireViewable: true
                },
                features: {
                  rotation: {
                    enabled: true
                  }
                }
              };

            </script>
          </div>
          <script type="text/javascript"
            src="https://cdn.vidible.tv/prod/max-suite/v0/engadget-outstream.com.js">
          </script>
        </div>
      </div>
      <footer id="Footer" class="Pos(r) T(0) Z(3) W(100%)">
        <div id="module-footer" class="wafer-rapid-module">
          <div>
            <div class="Fz(15px)" data-component="Footer">
              <div
                class="rwd-outer-container W(100vw) D(f) Jc(sa) Bgc(engadgetSteelGray) D(n)--md"
                data-component="RWDContainer">
                <div
                  class="rwd-inner-container W(1235px) W(980px)--lg W(640px)--md W(100%)--sm Mx(20px)--sm D(f) Ai(st) Mt(40px) Mb(60px)">
                  <div class="D(f) Fld(c)"
                    style="width:calc((100% - 40px * 3) / 4)">
                    <div class="W(100%) " data-component="SectionHeader">
                      <div class="W(30px) H(3px) Mb(5px) Bgc(engadgetPurple)">
                      </div>
                      <h4 class="M(0) C(#fff) Fw(700) Fz(18px) Mt(10px) ">About
                      </h4>
                    </div>
                    <div class="D(f) Fld(c) Mt(20px)"><a
                        href="https://www.engadget.com/about/"
                        alt="About Engadget"
                        class="C(#959698) C(#fff):h Td(n) Fw(15px) Lh(28px)"
                        data-ylk="sec:engadget-global-footer;slk:About Engadget;elm:category;itc:0"
                        data-rapid_p="1" data-v9y="0">About Engadget</a><a
                        href="https://www.engadget.com/about-our-advertising/"
                        alt="About our Ads"
                        class="C(#959698) C(#fff):h Td(n) Fw(15px) Lh(28px)"
                        data-ylk="sec:engadget-global-footer;slk:About our Ads;elm:category;itc:0"
                        data-rapid_p="2" data-v9y="0">About our Ads</a><a
                        href="https://www.engadget.com/about/advertise/"
                        alt="Advertise"
                        class="C(#959698) C(#fff):h Td(n) Fw(15px) Lh(28px)"
                        data-ylk="sec:engadget-global-footer;slk:Advertise;elm:category;itc:0"
                        data-rapid_p="3" data-v9y="0">Advertise</a><a
                        href="https://o.aolcdn.com/engadget/brand-kit/eng-logo-guidelines.pdf"
                        alt="Brand Kit"
                        class="C(#959698) C(#fff):h Td(n) Fw(15px) Lh(28px)"
                        data-ylk="sec:engadget-global-footer;slk:Brand Kit;elm:category;itc:0"
                        data-rapid_p="4" data-v9y="0">Brand Kit</a><a
                        href="https://www.engadget.com/about-faq-194611880.html"
                        alt="FAQ"
                        class="C(#959698) C(#fff):h Td(n) Fw(15px) Lh(28px)"
                        data-ylk="sec:engadget-global-footer;slk:FAQ;elm:category;itc:0"
                        data-rapid_p="5" data-v9y="0">FAQ</a><a
                        href="https://www.engadget.com/rss.xml" alt="RSS Feed"
                        class="C(#959698) C(#fff):h Td(n) Fw(15px) Lh(28px)"
                        data-ylk="sec:engadget-global-footer;slk:RSS Feed;elm:category;itc:0"
                        data-rapid_p="6" data-v9y="0">RSS Feed</a></div>
                  </div>
                  <div class="W(1px) H(100%) Bgc(#414246) Mx(20px)"></div>
                  <div class="D(f) Fld(c)"
                    style="width:calc((100% - 40px * 3) / 4)">
                    <div class="W(100%) " data-component="SectionHeader">
                      <div class="W(30px) H(3px) Mb(5px) Bgc(engadgetPurple)">
                      </div>
                      <h4 class="M(0) C(#fff) Fw(700) Fz(18px) Mt(10px) ">
                        Sections</h4>
                    </div>
                    <div class="D(f) Fld(c) Mt(20px)"><a
                        href="https://www.engadget.com/reviews/" alt="Reviews"
                        class="C(#959698) C(#fff):h Td(n) Fw(15px) Lh(28px)"
                        data-ylk="sec:engadget-global-footer;slk:Reviews;elm:category;itc:0"
                        data-rapid_p="7" data-v9y="0">Reviews</a><a
                        href="https://www.engadget.com/gear/" alt="Gear"
                        class="C(#959698) C(#fff):h Td(n) Fw(15px) Lh(28px)"
                        data-ylk="sec:engadget-global-footer;slk:Gear;elm:category;itc:0"
                        data-rapid_p="8" data-v9y="0">Gear</a><a
                        href="https://www.engadget.com/gaming/" alt="Gaming"
                        class="C(#959698) C(#fff):h Td(n) Fw(15px) Lh(28px)"
                        data-ylk="sec:engadget-global-footer;slk:Gaming;elm:category;itc:0"
                        data-rapid_p="9" data-v9y="0">Gaming</a><a
                        href="https://www.engadget.com/entertainment/"
                        alt="Entertainment"
                        class="C(#959698) C(#fff):h Td(n) Fw(15px) Lh(28px)"
                        data-ylk="sec:engadget-global-footer;slk:Entertainment;elm:category;itc:0"
                        data-rapid_p="10" data-v9y="0">Entertainment</a><a
                        href="https://www.engadget.com/tomorrow/" alt="Tomorrow"
                        class="C(#959698) C(#fff):h Td(n) Fw(15px) Lh(28px)"
                        data-ylk="sec:engadget-global-footer;slk:Tomorrow;elm:category;itc:0"
                        data-rapid_p="11" data-v9y="0">Tomorrow</a><a
                        href="https://www.engadget.com/buyers-guide/"
                        alt="The Buyers Guide"
                        class="C(#959698) C(#fff):h Td(n) Fw(15px) Lh(28px)"
                        data-ylk="sec:engadget-global-footer;slk:The Buyers Guide;elm:category;itc:0"
                        data-rapid_p="12" data-v9y="0">The Buyers Guide</a><a
                        href="https://www.engadget.com/videos/" alt="Video"
                        class="C(#959698) C(#fff):h Td(n) Fw(15px) Lh(28px)"
                        data-ylk="sec:engadget-global-footer;slk:Video;elm:category;itc:0"
                        data-rapid_p="13" data-v9y="0">Video</a><a
                        href="https://www.engadget.com/2019-08-01-engadget-podcasts.html"
                        alt="Podcasts"
                        class="C(#959698) C(#fff):h Td(n) Fw(15px) Lh(28px)"
                        data-ylk="sec:engadget-global-footer;slk:Podcasts;elm:category;itc:0"
                        data-rapid_p="14" data-v9y="0">Podcasts</a><a
                        href="https://deals.gdgt.com/" alt="Deals"
                        class="C(#959698) C(#fff):h Td(n) Fw(15px) Lh(28px)"
                        data-ylk="sec:engadget-global-footer;slk:Deals;elm:category;itc:0"
                        data-rapid_p="15" data-v9y="0">Deals</a></div>
                  </div>
                  <div class="W(1px) H(100%) Bgc(#414246) Mx(20px)"></div>
                  <div class="D(f) Fld(c)"
                    style="width:calc((100% - 40px * 3) / 4)">
                    <div class="W(100%) " data-component="SectionHeader">
                      <div class="W(30px) H(3px) Mb(5px) Bgc(engadgetPurple)">
                      </div>
                      <h4 class="M(0) C(#fff) Fw(700) Fz(18px) Mt(10px) ">
                        Contribute</h4>
                    </div>
                    <div class="D(f) Fld(c) Mt(20px)"><a
                        href="https://www.engadget.com/2017-05-01-engadget-commenting-policy.html"
                        alt="Comment Guidelines"
                        class="C(#959698) C(#fff):h Td(n) Fw(15px) Lh(28px)"
                        data-ylk="sec:engadget-global-footer;slk:Comment Guidelines;elm:category;itc:0"
                        data-rapid_p="16" data-v9y="0">Comment Guidelines</a><a
                        href="https://www.engadget.com/support/" alt="Support"
                        class="C(#959698) C(#fff):h Td(n) Fw(15px) Lh(28px)"
                        data-ylk="sec:engadget-global-footer;slk:Support;elm:category;itc:0"
                        data-rapid_p="17" data-v9y="0">Support</a></div>
                  </div>
                  <div class="W(1px) H(100%) Bgc(#414246) Mx(20px)"></div>
                  <div class="D(f) Fld(c)"
                    style="width:calc((100% - 40px * 3) / 4)">
                    <div class="W(100%) " data-component="SectionHeader">
                      <div class="W(30px) H(3px) Mb(5px) Bgc(engadgetPurple)">
                      </div>
                      <h4 class="M(0) C(#fff) Fw(700) Fz(18px) Mt(10px) ">
                        International</h4>
                    </div>
                    <div class="D(f) Fld(c) Mt(20px)"><a
                        href="https://chinese.engadget.com/" alt="繁體中文"
                        class="C(#959698) C(#fff):h Td(n) Fw(15px) Lh(28px)"
                        data-ylk="sec:engadget-global-footer;slk:繁體中文;elm:category;itc:0"
                        lang="zh-Hant-HK" data-rapid_p="18"
                        data-v9y="0">繁體中文</a><a href="https://cn.engadget.com/"
                        alt="简体中文"
                        class="C(#959698) C(#fff):h Td(n) Fw(15px) Lh(28px)"
                        data-ylk="sec:engadget-global-footer;slk:简体中文;elm:category;itc:0"
                        lang="zh-Hans-CN" data-rapid_p="19"
                        data-v9y="0">简体中文</a><a
                        href="https://japanese.engadget.com/" alt="日本版"
                        class="C(#959698) C(#fff):h Td(n) Fw(15px) Lh(28px)"
                        data-ylk="sec:engadget-global-footer;slk:日本版;elm:category;itc:0"
                        lang="ja-JP" data-rapid_p="20" data-v9y="0">日本版</a>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="rwd-outer-container W(100vw) D(f) Jc(sa) Bgc(engadgetGray) Bgc(engadgetSteelGray)--md"
                data-component="RWDContainer">
                <div
                  class="rwd-inner-container W(1235px) W(980px)--lg W(640px)--md W(100%)--sm Mx(20px)--sm D(f) Ai(c) C(#fff) My(40px) H(100%)--md Ai(fs)--md Fld(c)--md Mt(20px)--md Mb(25px)--md Jc(sb)">
                  <div class="W(40%) D(n)--md" data-component="EmailSignupForm">
                    <form class="newsletter-form Pos(r) Pend(190px) P(0)--xs"
                      novalidate=""><input type="email"
                        class="Px(15px) W(100%) H(50px) Bxz(bb) Lh(50px) Bdrs(2px) Bd(n) Fz(15px) Fw(400)"
                        name="email" placeholder="<EMAIL>"
                        aria-label="Email" aria-required="true" required=""
                        data-rapid_p="21" data-v9y="0"><button
                        class="Pos(a) End(0) W(170px) P(0) Bds(s) Bdw(1px) Bdrs(2px) Cur(p) Va(t) C(engadgetWhiteSmoke) Bdc(engadgetFontLightGray) Bgc(t) Bgc(#fff):h C(engadgetSteelGray):h Bdc(#fff):h"
                        type="submit"
                        data-ylk="sec:nwslttr;slk:subscribe;elm:btn;itc:1;outcm:sbmt"
                        aria-label="Subscribe" data-rapid_p="22"
                        data-v9y="0"><span
                          class="D(b) Fz(15px) Lh(18px) Py(15px) Fw(400)">Subscribe</span></button>
                      <p class="Pos(a) M(0) T(60px) Start(6px) D(n) C(errorColor)"
                        role="alert">Please enter a valid email address</p>
                    </form>
                  </div>
                  <div class="W(60%) D(f) Jc(fe) Ai(c) W(100%)--md D(b)--md">
                    <span class="C(#fff)--md Fw(700)--md">Follow Us</span>
                    <ul
                      class="D(if) Ai(c) W(100%)--md M(0) Mt(20px)--md P(0) List(n)">
                      <li
                        class="socialLink Mstart(30px) Mstart(0)--md Fxg(1)--md">
                        <a href="https://www.facebook.com/engadget"
                          aria-label="Facebook" alt="Facebook" target="_blank"
                          rel="noopener noreferrer" class="C(engadgetFontGray)"
                          data-ylk="sec:followus;slk:Facebook;elm:follow;itc:0"
                          data-rapid_p="23" data-v9y="0"><svg
                            class="Fill(engadgetFontGray) Stk(engadgetFontGray) socialLink:h_Fill(#fff) socialLink:h_Stk(#fff) Cur(p)"
                            width="24"
                            style="stroke-width:0;vertical-align:bottom"
                            height="24" viewBox="0 0 32 32"
                            data-icon="LogoFacebook">
                            <path
                              d="M12.752 30.4V16.888H9.365V12.02h3.387V7.865c0-3.264 2.002-6.264 6.613-6.264 1.866 0 3.248.19 3.248.19l-.11 4.54s-1.404-.013-2.943-.013c-1.66 0-1.93.81-1.93 2.152v3.553h5.008l-.22 4.867h-4.786V30.4h-4.88z">
                            </path>
                          </svg></a></li>
                      <li
                        class="socialLink Mstart(30px) Mstart(0)--md Fxg(1)--md">
                        <a href="https://twitter.com/engadget"
                          aria-label="Twitter" alt="Twitter" target="_blank"
                          rel="noopener noreferrer" class="C(engadgetFontGray)"
                          data-ylk="sec:followus;slk:Twitter;elm:follow;itc:0"
                          data-rapid_p="24" data-v9y="0"><svg
                            class="Fill(engadgetFontGray) Stk(engadgetFontGray) socialLink:h_Fill(#fff) socialLink:h_Stk(#fff) Cur(p)"
                            width="24"
                            style="stroke-width:0;vertical-align:bottom"
                            height="24" viewBox="0 0 32 32"
                            data-icon="LogoTwitter">
                            <path
                              d="M30.402 7.094c-1.058.47-2.198.782-3.392.928 1.218-.725 2.154-1.885 2.595-3.256-1.134.674-2.405 1.165-3.75 1.43-1.077-1.148-2.612-1.862-4.31-1.862-3.268 0-5.915 2.635-5.915 5.893 0 .464.056.91.155 1.34-4.915-.244-9.266-2.59-12.18-6.158-.51.87-.806 1.885-.806 2.96 0 2.044 1.045 3.847 2.633 4.905-.974-.032-1.883-.3-2.68-.736v.07c0 2.857 2.034 5.236 4.742 5.773-.498.138-1.022.21-1.56.21-.38 0-.75-.034-1.11-.103.75 2.344 2.93 4.042 5.518 4.09-2.024 1.58-4.57 2.523-7.333 2.523-.478 0-.952-.032-1.41-.085 2.613 1.674 5.72 2.65 9.054 2.65 10.872 0 16.814-8.976 16.814-16.765 0-.254-.008-.507-.018-.762 1.155-.83 2.155-1.868 2.95-3.047z">
                            </path>
                          </svg></a></li>
                      <li
                        class="socialLink Mstart(30px) Mstart(0)--md Fxg(1)--md">
                        <a href="https://www.youtube.com/user/engadget"
                          aria-label="YouTube" alt="YouTube" target="_blank"
                          rel="noopener noreferrer" class="C(engadgetFontGray)"
                          data-ylk="sec:followus;slk:YouTube;elm:follow;itc:0"
                          data-rapid_p="25" data-v9y="0"><svg
                            class="Fill(engadgetFontGray) Stk(engadgetFontGray) socialLink:h_Fill(#fff) socialLink:h_Stk(#fff) Cur(p)"
                            width="24"
                            style="stroke-width:0;vertical-align:bottom"
                            height="24" viewBox="0 0 24 24"
                            data-icon="LogoYoutube">
                            <path
                              d="M9.73 15.117l-.002-6.18 5.944 3.1-5.943 3.08zm13.05-7.252s-.215-1.516-.875-2.184c-.836-.875-1.775-.88-2.203-.93-3.08-.223-7.697-.223-7.697-.223h-.01s-4.618 0-7.696.222c-.43.05-1.37.055-2.206.93-.66.67-.874 2.185-.874 2.185S1 9.645 1 11.425v1.67c0 1.78.22 3.56.22 3.56s.215 1.516.874 2.184c.837.875 1.937.847 2.426.94C6.28 19.947 12 20 12 20s4.623-.007 7.702-.23c.43-.05 1.367-.055 2.204-.93.66-.67.875-2.185.875-2.185s.22-1.78.22-3.56v-1.67c0-1.78-.22-3.56-.22-3.56z">
                            </path>
                          </svg></a></li>
                      <li
                        class="socialLink Mstart(30px) Mstart(0)--md Fxg(1)--md">
                        <a href="https://www.instagram.com/engadget/"
                          aria-label="Instagram" alt="Instagram" target="_blank"
                          rel="noopener noreferrer" class="C(engadgetFontGray)"
                          data-ylk="sec:followus;slk:Instagram;elm:follow;itc:0"
                          data-rapid_p="26" data-v9y="0"><svg
                            class="Fill(engadgetFontGray) Stk(engadgetFontGray) socialLink:h_Fill(#fff) socialLink:h_Stk(#fff) Cur(p)"
                            width="24"
                            style="stroke-width:0;vertical-align:bottom"
                            height="24" viewBox="0 0 24 24"
                            data-icon="LogoInstagramEmblem1">
                            <path
                              d="M20.134 16.036c-.044.975-.207 1.504-.344 1.856-.182.467-.398.8-.748 1.15-.35.35-.683.566-1.15.748-.352.136-.88.3-1.856.344-1.054.048-1.37.058-4.04.058s-2.985-.01-4.04-.058c-.974-.045-1.503-.208-1.855-.344a3.104 3.104 0 0 1-1.15-.748 3.102 3.102 0 0 1-.746-1.15c-.137-.352-.3-.88-.345-1.856-.05-1.054-.06-1.37-.06-4.04s.01-2.985.06-4.04c.044-.974.207-1.503.344-1.855.18-.466.398-.8.747-1.15.35-.35.684-.565 1.15-.747.353-.137.882-.3 1.857-.344 1.054-.05 1.37-.06 4.04-.06s2.985.01 4.04.06c.974.044 1.503.206 1.856.343.466.182.8.398 1.15.748s.565.684.747 1.15c.137.353.3.882.344 1.857.048 1.054.058 1.37.058 4.04s-.01 2.985-.058 4.04m1.8-8.162c-.05-1.064-.218-1.79-.465-2.427a4.923 4.923 0 0 0-1.154-1.77 4.923 4.923 0 0 0-1.77-1.154c-.637-.247-1.364-.416-2.428-.464-1.066-.05-1.406-.06-4.12-.06-2.716 0-3.057.01-4.123.06-1.064.048-1.79.217-2.427.464a4.91 4.91 0 0 0-1.77 1.153 4.897 4.897 0 0 0-1.153 1.77c-.248.637-.416 1.364-.465 2.428C2.01 8.94 2 9.28 2 11.995c0 2.716.01 3.057.06 4.123.05 1.064.217 1.79.465 2.427a4.885 4.885 0 0 0 1.153 1.77 4.885 4.885 0 0 0 1.77 1.153c.636.248 1.363.417 2.427.465 1.066.05 1.407.06 4.122.06s3.055-.01 4.12-.06c1.065-.048 1.792-.217 2.428-.465a4.897 4.897 0 0 0 1.77-1.153 4.91 4.91 0 0 0 1.154-1.77c.246-.636.415-1.363.463-2.427.05-1.066.06-1.407.06-4.122s-.01-3.055-.06-4.12m-9.936-1.013a5.135 5.135 0 1 1-.002 10.27 5.135 5.135 0 0 1 .002-10.27zm0 1.8a3.332 3.332 0 1 0 0 6.666 3.332 3.332 0 0 0 0-6.666zm5.336-3.203a1.2 1.2 0 1 1 0 2.4 1.2 1.2 0 0 1 0-2.4z">
                            </path>
                          </svg></a></li>
                      <li
                        class="socialLink Mstart(30px) Mstart(0)--md Fxg(1)--md">
                        <a href="https://www.linkedin.com/company/101421"
                          aria-label="LinkedIn" alt="LinkedIn" target="_blank"
                          rel="noopener noreferrer" class="C(engadgetFontGray)"
                          data-ylk="sec:followus;slk:LinkedIn;elm:follow;itc:0"
                          data-rapid_p="27" data-v9y="0"><svg
                            class="Fill(engadgetFontGray) Stk(engadgetFontGray) socialLink:h_Fill(#fff) socialLink:h_Stk(#fff) Cur(p)"
                            width="24"
                            style="stroke-width:0;vertical-align:bottom"
                            height="24" viewBox="0 0 24 24"
                            data-icon="LogoLinkedIn">
                            <path
                              d="M6.913 20.217H3V8.477h3.913v11.74zM4.957 6.913a1.957 1.957 0 1 1 0-3.914 1.957 1.957 0 0 1 0 3.913zM21 20.217h-3.762v-5.713c0-1.362-.026-3.114-1.956-3.114-1.96 0-2.26 1.483-2.26 3.016v5.81h-3.76V8.47h3.61v1.605h.05c.503-.924 1.73-1.897 3.562-1.897 3.812 0 4.516 2.433 4.516 5.596v6.443z">
                            </path>
                          </svg></a></li>
                    </ul>
                  </div>
                </div>
              </div>
              <div class="rwd-outer-container W(100vw) D(f) Jc(sa) Bgc(#fff)"
                data-component="RWDContainer">
                <div
                  class="rwd-inner-container W(1235px) W(980px)--lg W(640px)--md W(100%)--sm Mx(20px)--sm D(f) Ai(c) Mt(25px)--md Mb(20px)--md">
                  <div class="W(100%) Lh(22px)">
                    <div
                      class="C(engadgetFontDarkGray) Fw(400) Fz(14px)--md Py(30px) P(0)--md Mb(20px)--md">
                      © 2021 Verizon Media Inc.</div>
                    <ul
                      class="D(f) Ai(c) Fxw(w) Mt(0) Mb(25px) Pstart(0) List(n) Pos(r) Start(-24px)">
                      <li class="Pstart(24px)"><a
                          href="https://policies.oath.com/us/en/oath/privacy/guce/faq/index.html"
                          alt="About Verizon Media"
                          class="C(engadgetFontDarkGray) C(engadgetBlack):h Fw(400) Td(n) Cur(p) Fz(12px)"
                          target="_blank" rel="noopener noreferrer"
                          data-ylk="sec:footerlinks;slk:About Verizon Media;elm:category;itc:0"
                          data-rapid_p="28" data-v9y="0">About Verizon Media</a>
                      </li>
                      <li class="Pstart(24px)"><a
                          href="https://www.parsintl.com/publication/engadget/"
                          alt="Reprints and Permissions"
                          class="C(engadgetFontDarkGray) C(engadgetBlack):h Fw(400) Td(n) Cur(p) Fz(12px)"
                          target="_blank" rel="noopener noreferrer"
                          data-ylk="sec:footerlinks;slk:Reprints and Permissions;elm:category;itc:0"
                          data-rapid_p="29" data-v9y="0">Reprints and
                          Permissions</a></li>
                      <li class="Pstart(24px)"><a
                          href="https://aol.uservoice.com/forums/917323"
                          alt="Suggestions"
                          class="C(engadgetFontDarkGray) C(engadgetBlack):h Fw(400) Td(n) Cur(p) Fz(12px)"
                          target="_blank" rel="noopener noreferrer"
                          data-ylk="sec:footerlinks;slk:Suggestions;elm:category;itc:0"
                          data-rapid_p="30" data-v9y="0">Suggestions</a></li>
                      <li class="Pstart(24px)"><a
                          href="https://www.verizonmedia.com/policies/us/en/verizonmedia/privacy/index.html"
                          alt="Privacy Policy (Updated)"
                          class="C(engadgetFontDarkGray) C(engadgetBlack):h Fw(400) Td(n) Cur(p) Fz(12px)"
                          target="_blank" rel="noopener noreferrer"
                          data-ylk="sec:footerlinks;slk:Privacy Policy (Updated);elm:category;itc:0"
                          data-rapid_p="31" data-v9y="0">Privacy Policy
                          (Updated)</a></li>
                      <li class="Pstart(24px)"><a
                          href="https://www.verizonmedia.com/policies/us/en/verizonmedia/terms/otos/index.html"
                          alt="Terms of Service (Updated)"
                          class="C(engadgetFontDarkGray) C(engadgetBlack):h Fw(400) Td(n) Cur(p) Fz(12px)"
                          target="_blank" rel="noopener noreferrer"
                          data-ylk="sec:footerlinks;slk:Terms of Service (Updated);elm:category;itc:0"
                          data-rapid_p="32" data-v9y="0">Terms of Service
                          (Updated)</a></li>
                      <li class="Pstart(24px)"><a
                          href="https://legal.aol.com/trademarks/"
                          alt="Trademarks"
                          class="C(engadgetFontDarkGray) C(engadgetBlack):h Fw(400) Td(n) Cur(p) Fz(12px)"
                          target="_blank" rel="noopener noreferrer"
                          data-ylk="sec:footerlinks;slk:Trademarks;elm:category;itc:0"
                          data-rapid_p="33" data-v9y="0">Trademarks</a></li>
                      <li class="Pstart(24px)"><a
                          href="https://www.engadget.com/about/advertise/"
                          alt="Advertise"
                          class="C(engadgetFontDarkGray) C(engadgetBlack):h Fw(400) Td(n) Cur(p) Fz(12px)"
                          target="_blank" rel="noopener noreferrer"
                          data-ylk="sec:footerlinks;slk:Advertise;elm:category;itc:0"
                          data-rapid_p="34" data-v9y="0">Advertise</a></li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <script>
            window.performance.mark('footer');
            window.performance.measure('footerDone', 'PageStart', 'footer');

          </script>
        </div>
      </footer>
      <div class="drawer-nav-overlay-container Pos(f) T(0) Z(10) D(n) Op(0)">
        <div id="module-drawer-menu-overlay" class="wafer-rapid-module">
          <div>
            <div class="W(100vw) H(100vh) Bgc(leftNavOverlayBgc) D(f) has-click"
              data-drawer-target="hamburger" data-wf-drawer-state="closed"
              data-component="DrawerMenuOverlay">
              <div class="H(80px)--md H(60px)--sm W(100vw) D(f) Ai(c) Mx(20px)">
                <button
                  class="P(0) Pos(r) Bgc(t) Bd(n) O(n) W(35px) H(35px) D(f) Ai(c) Jc(sa) has-click"
                  data-drawer-target="hamburger" data-wf-drawer-state="closed"
                  data-ylk="slk:close;elm:btn;sec:drawer-menu-overlay;itc:1"
                  data-component="DrawerCloseBtn" aria-label="Close button"
                  data-rapid_p="1" data-v9y="1">
                  <div class="Mstart(-20px) Mt(3px) Pe(n)">
                    <div
                      class="Pos(a) W(20px) H(2px) Bgc(engadgetGray) Rotate(45deg)">
                    </div>
                    <div
                      class="Pos(a) W(20px) H(2px) Bgc(engadgetGray) Rotate(-45deg)">
                    </div>
                  </div>
                </button></div>
            </div>
          </div>
          <script>
            window.performance.mark('drawer-menu-overlay');
            window.performance.measure('drawer-menu-overlayDone', 'PageStart',
              'drawer-menu-overlay');

          </script>
        </div>
      </div>
    </div>
  </div><iframe name="__uspapiLocator" style="display: none;"></iframe><iframe
    name="__tcfapiLocator" style="display: none;"></iframe>
  <script async="" src="https://s.yimg.com/dy/ads/native.js"></script>
  <script async="" src="https://s.yimg.com/dy/ads/readmo.js"></script>
  <script>
    (function (d) {
      var sdk = document.createElement('script');
      sdk.src = 'https://s.yimg.com/pv/static/assistjs/desktop-v1.0.70.js';
      sdk.id = "assistjs";
      sdk.onload = function () {
        window.YAHOO.SA.init({
          elems: {
            sbForm: "search",
            sbInput: "search-terms",
            sbClear: 'sb-clr'
          },
          plugins: {
            saTray: {
              parent: "search-results"
            },
            saPCActn: {
              css: "search-result"
            },
            saFr: {
              def: 'engadget',
              fr: 'engadget-s'
            },
            anykey: {
              disable: true
            }
          },
          highlight: {
            boldTag: ""
          },
          saBE: {
            params: {
              appid: 'carbon-production.engadget-k8s.omega.yahoo.com'
            },
            host: '//search.engadget.com',
            base: '/sugg/gossip/gossip-us-engadget/'
          }
        });
      };
      d.body.appendChild(sdk);
    })(document);

  </script>
  <script src="https://s.yimg.com/pv/static/assistjs/desktop-v1.0.70.js"
    id="assistjs"></script>
  <script type="text/javascript">
    (function (win) {
      if (win) {
        win.YAHOO = win.YAHOO || {};
        win.YAHOO.comscore = {
          c14: -1,
          enableTracking: true
        };
        win.YAHOO.context = win.YAHOO.context || {};
        win.YAHOO.context.apptype = 'default';
        win.YAHOO.context.authed = '0';
        win.YAHOO.context.browserName = 'chrome';
        win.YAHOO.context.browserVersion = '94.0';
        win.YAHOO.context.bucket = '';
        win.YAHOO.context.crumb = 'R0m5/q2eaXG';
        win.YAHOO.context.device = 'desktop';
        win.YAHOO.context.feature = 'jac,ncp,readmo,outstream,newnav';
        win.YAHOO.context.guid = '';
        win.YAHOO.context.intl = 'us';
        win.YAHOO.context.lang = 'en-US';
        win.YAHOO.context.meta = {};
        win.YAHOO.context.osName = 'mac os x';
        win.YAHOO.context.osVersion = '10.15';
        win.YAHOO.context.region = 'US';
        win.YAHOO.context.rid = '3van48lgn33t1';
        win.YAHOO.context.site = 'engadget';
        win.YAHOO.context.tpConsent = false;
        win.YAHOO.errBeaconConfig = {
          beaconPath: '/_td/beacon/error',
          site: 'engadget'
        };

      }
    })(window);

  </script>
  <script type="text/javascript"
    src="https://s.yimg.com/os/yc/js/iframe-1.0.15.js" defer=""></script>
  <script type="text/javascript"
    src="https://s.yimg.com/aaq/fp/jsc/tdv2-wafer-utils.d7792bb1.js" defer="">
  </script>
  <script type="text/javascript">
    (function (win) {
      win.addEventListener && win.addEventListener("DOMContentLoaded",
        function () {
          var s = win.document.createElement("script");
          s.async = true;
          s.src = "https://s.yimg.com/aaq/pv/perf-vitals_2.0.1.js";
          win.document.body.appendChild(s);
        });
    })(window);

  </script>
  <script type="text/javascript"
    src="https://s.yimg.com/os/yaft/yaft-0.3.28.min.js" async="" defer="">
  </script>
  <script type="text/javascript" src="https://jill.fc.yahoo.com/v1/client/js"
    async=""></script>
  <script type="text/javascript"
    src="https://s.yimg.com/kw/engadget/mod/js/spotimLogout.cd0232b5ed6f3f8b55fd4f9105f72e6d.js">
  </script>
  <script type="text/javascript"
    src="https://s.yimg.com/aaq/wf/wf-core-1.48.0-modern.js"></script>
  <script type="text/javascript"
    src="https://s.yimg.com/aaq/wf/wf-drawer-1.0.10-modern.js" defer="">
  </script>
  <script type="text/javascript"
    src="https://s.yimg.com/aaq/wf/wf-toggle-1.15.3-modern.js" defer="">
  </script>
  <script type="text/javascript"
    src="https://s.yimg.com/aaq/wf/wf-image-1.2.1-modern.js" defer=""></script>
  <script type="text/javascript"
    src="https://s.yimg.com/aaq/wf/wf-scrollview-2.15.1-modern.js" defer="">
  </script>
  <script type="text/javascript"
    src="https://s.yimg.com/aaq/wf/wf-menu-1.1.3-modern.js" defer=""></script>
  <script type="text/javascript"
    src="https://s.yimg.com/kw/engadget/mod/jsc/engad-nav.de742d7d.js"></script>
  <script type="text/javascript"
    src="https://s.yimg.com/aaq/wf/wf-lightbox-1.5.2-modern.js" defer="">
  </script>
  <script type="text/javascript"
    src="https://s.yimg.com/aaq/wf/wf-tabs-1.11.7-modern.js" defer=""></script>
  <script type="text/javascript"
    src="https://s.yimg.com/aaq/wf/wf-fetch-1.17.8-modern.js" defer=""></script>
  <script type="text/javascript"
    src="https://s.yimg.com/kw/engadget/mod/jsc/engad-footer.d1d5c966.js">
  </script>
  <meta name="twitter:description"
    content="Historically, if you've wanted to create a private repository on GitHub, you had to be a paying user, but that's about to change.">
  <script>
    (function (w) {
      var comsCoreParams = {
        c1: '2',
        c2: '7241469',
        c5: '**********',
        c7: 'http://www.engadget.com/2019-01-07-all-github-users-keep-code-private.html',
        c14: '-1'
      };
      w._comscore = w._comscore || [];
      w._comscore.push(comsCoreParams);
      (function () {
        var s = document.createElement('script');
        var el = document.getElementsByTagName('script')[0];
        s.async = true;
        s.src = 'https://s.yimg.com/aaq/vzm/cs_1.2.0.js';
        el.parentNode.insertBefore(s, el);
      })();
    }(window));

  </script><noscript><img
      src="https://sb.scorecardresearch.com/p?c1=2&amp;c2=7241469&amp;c7=http://www.engadget.com/2019-01-07-all-github-users-keep-code-private.html&amp;c5=**********&amp;cv=2.0&amp;cj=1&amp;c14=-1"></noscript>
  <script type="text/javascript">
    (function (w) {
      if (w.YAHOO && w.YAHOO.i13n && w.YAHOO.i13n.Rapid) {
        YAHOO.i13n.SPACEID = '**********';
        var enableApvBeacon = true;
        var clientOnly = undefined;
        var rapidConfig = {
          click_timeout: 200,
          keys: {
            "_rid": "3van48lgn33t1",
            "abk": "",
            "colo": "ir2",
            "mrkt": "us",
            "p_sec": "default",
            "partner": "none",
            "site": "engadget",
            "uh_vw": 0,
            "navtype": "server",
            "ver": "carbon",
            "pt": "content",
            "pct": "story",
            "paid": "engadget_479=bsd:23636374",
            "pstaid": "",
            "cbe": "contributing writer"
          },
          perf_navigationtime: 2,
          spaceid: YAHOO.i13n.SPACEID,
          test_id: '',
          tracked_mods_viewability: {
            "module-header": "hd",
            "module-drawer-menu": "drawer-menu",
            "module-drawer-menu-overlay": "drawer-menu-overlay",
            "module-nav": "nav",
            "module-article-container": "content",
            "module-footer": "footer"
          },
          viewability: true,
          yql_host: 'udc.yahoo.com',
          yql_path: '/v2/public/yql'
        };

        if (typeof clientOnly !== 'undefined') {
          rapidConfig.client_only = clientOnly;
        }
        // beacon apv for mobile web
        if (enableApvBeacon) {
          rapidConfig.apv_callback = function (apvObj) {
            try {
              if ('**********' === YAHOO.i13n.SPACEID) {
                var img = new Image();
                img.src = '/_td/beacon?beaconType=apv&sp=' + YAHOO.i13n
                  .SPACEID +
                  '&device=desktop&intl=US&pixel_pos=' +
                  apvObj.pixel_pos + '&scroll_dir=' +
                  apvObj.scroll_dir;
              }
            } catch (e) {}
          };
        }

        // ensure webworker is loaded from yaho.com cdn path
        YAHOO.i13n.WEBWORKER_FILE = '/__rapid-worker-1.2.js';

        // setup rapid instance
        YAHOO.i13n.rapidInstance = w.rapidInstance = w.YAHOO.i13n.Rapid(
          rapidConfig);

        // expose rapid config for homepage-viewer client
        if (!w.rapidPageConfig) {
          w.rapidPageConfig = {
            rapidConfig: rapidConfig
          }
        }
      }
    }(window));

  </script>
  <script async="" src="https://s.yimg.com/aaq/pv/perf-vitals_2.0.1.js">
  </script>
  <div class="sa-aria-live-region" aria-live="polite"
    style="position: absolute; left: -9999px;"></div><iframe id="apid-iframe"
    src="https://cmp.advertising.com/cmp-apid.html" width="0" height="0"
    style="display: none;" cbljp9vwm=""></iframe>
</body>
</html>
