# Snapshot report for `test/integration/nbcnews/index.js`

The actual snapshot is saved in `index.js.snap`.

Generated by [AVA](https://avajs.dev).

## nbcnews

> Snapshot 1

    {
      audio: null,
      author: '<PERSON> and <PERSON><PERSON>',
      date: '2021-01-06T16:50:01.000Z',
      description: 'Even before announcing his findings, the Kenosha County district attorney pleaded for peace.',
      image: 'https://media4.s-nbcnews.com/j/newscms/2020_45/3407342/200825-jacob-blake-2x1-se-152p_19527f3bde0e0abf3b82395738909a41.nbcnews-fp-1200-630.jpg',
      lang: 'en',
      logo: 'https://nodeassets.nbcnews.com/cdnassets/projects/ramen/favicon/nbcnews/all-other-sizes-PNG.ico/android-icon-192x192.png',
      publisher: 'NBC News',
      title: 'No charges filed against Kenosha officers in <PERSON> shooting',
      url: 'https://www.nbcnews.com/news/us-news/no-charges-filed-against-kenosha-officers-jaco<PERSON>-blake-shooting-n1252739',
      video: null,
    }
