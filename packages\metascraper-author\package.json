{"name": "metascraper-author", "description": "Get author property from HTML markup", "homepage": "https://metascraper.js.org", "version": "5.49.1", "types": "src/index.d.ts", "main": "src/index.js", "author": {"email": "<EMAIL>", "name": "microlink.io", "url": "https://microlink.io"}, "repository": {"type": "git", "url": "https://github.com/microlinkhq/metascraper/packages/metascraper-author"}, "bugs": {"url": "https://github.com/microlinkhq/metascraper/issues"}, "keywords": ["author", "metascraper"], "dependencies": {"@metascraper/helpers": "workspace:*"}, "engines": {"node": ">= 16"}, "files": ["src"], "scripts": {"test": "exit 0"}, "license": "MIT"}