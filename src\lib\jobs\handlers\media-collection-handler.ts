/**
 * Media Collection Job Handler
 * 
 * Handles media collection jobs using metascraper for efficient favicon and OG image extraction.
 * Runs as a separate background job after content generation completes.
 */

import { Job, JobHandler } from '../types';
import { MediaCollectionJob } from '../media-collection-job';

export class MediaCollectionHandler implements JobHandler {
  private mediaCollectionJob: MediaCollectionJob;

  constructor() {
    this.mediaCollectionJob = new MediaCollectionJob();
  }

  /**
   * Handle media collection job
   */
  async handle(job: Job): Promise<any> {
    console.log(`📸 Processing media collection job ${job.id}`);
    
    try {
      const result = await this.mediaCollectionJob.handle(job);
      
      console.log(`✅ Media collection job ${job.id} completed successfully`);
      return result;
      
    } catch (error) {
      console.error(`❌ Media collection job ${job.id} failed:`, error);
      throw error;
    }
  }
}

export default MediaCollectionHandler;
