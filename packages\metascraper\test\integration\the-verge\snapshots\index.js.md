# Snapshot report for `test/integration/the-verge/index.js`

The actual snapshot is saved in `index.js.snap`.

Generated by [AVA](https://avajs.dev).

## the-verge

> Snapshot 1

    {
      audio: null,
      author: '<PERSON>',
      date: '2016-05-24T20:49:03.000Z',
      description: 'Apple could open Siri to third-party apps very soon',
      image: 'https://img.connatix.com/pid-13bd7676-705c-4894-8449-b8e67fcddc1f/8ffb59d5-b093-4d68-aa90-1dd263c5d84d/1_th.jpg?crop=600:338,smart&width=600&height=338&quality=60&fit=crop',
      lang: 'en',
      logo: 'https://cdn.vox-cdn.com/verge/favicon.ico',
      publisher: 'The Verge',
      title: 'Apple is reportedly building a Siri speaker to rival Amazon’s Echo',
      url: 'https://www.theverge.com/2016/5/24/11763836/apple-siri-speaker-amazon-echo-alexa-google-home-ai',
      video: 'https://volume-assets.voxmedia.com/production/41b133781946b66740bed7920b68bdf3/VRG_VUP_546_Alexa_VS_Siri_V4.mp4',
    }
