# Snapshot report for `test/integration/the-register/index.js`

The actual snapshot is saved in `index.js.snap`.

Generated by [AVA](https://avajs.dev).

## the-register

> Snapshot 1

    {
      audio: null,
      author: '<PERSON>',
      date: '2016-05-04T14:14:38.000Z',
      description: 'Announcement overload? Oh, you’ll love it just as much as Big Mickey Dell',
      image: 'https://regmedia.co.uk/2016/05/04/raincloud_teaser.jpg',
      lang: 'en',
      logo: 'https://www.theregister.com/design_picker/13249a2e80709c7ff2e57dd3d49801cd534f2094/graphics/favicons/apple-touch-icon.png',
      publisher: 'The Register',
      title: 'EMC makes a LEAP forward with Virtustream and more',
      url: 'https://www.theregister.com/2016/05/03/emc_world_virtustream_announcement/',
      video: null,
    }
