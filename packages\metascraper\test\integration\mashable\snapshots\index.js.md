# Snapshot report for `test/integration/mashable/index.js`

The actual snapshot is saved in `index.js.snap`.

Generated by [AVA](https://avajs.dev).

## mashable

> Snapshot 1

    {
      audio: null,
      author: '<PERSON>',
      date: '2021-10-29T15:36:15.000Z',
      description: 'The movie, now on HBO Max, is brilliantly faithful to the <PERSON> original. That reveals some flaws.',
      image: 'https://helios-i.mashable.com/imagery/articles/03kMbX4uhsnOpnHQkfhoNzO/hero-image.fill.size_1200x675.v1634938507.jpg',
      lang: 'en',
      logo: 'https://mashable.com/images/mashable-logomark.png',
      publisher: 'Mashable',
      title: 'The new ‘Dune’ is a great start — but can’t outrun the book’s biggest problem',
      url: 'https://mashable.com/article/dune-movie-hbo-review',
      video: null,
    }
