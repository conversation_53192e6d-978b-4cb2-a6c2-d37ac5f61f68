# Snapshot report for `test/integration/engadget/index.js`

The actual snapshot is saved in `index.js.snap`.

Generated by [AVA](https://avajs.dev).

## engadget

> Snapshot 1

    {
      author: '<PERSON> Locklear',
      date: '2021-05-13T18:01:31.000Z',
      description: 'Historically, if you’ve wanted to create a private repository on GitHub, you had to be a paying user, but that’s about to change.',
      image: 'https://s.yimg.com/uu/api/res/1.2/C.Injrjs8P1U7ExbkYEEcg--~B/aD0xMDY5O3c9MTYwMDthcHBpZD15dGFjaHlvbg--/https://o.aolcdn.com/images/dims?crop=4834%2C3230%2C0%2C0&quality=85&format=jpg&resize=1600%2C1069&image_uri=https://s.yimg.com/os/creatr-images/2019-01/2d1e22f0-12ae-11e9-bae7-60d640081814&client=a1acac3e1b3290917d92&signature=269ae0af3a1a1772ffea6759d126791b9ad25184',
      lang: 'en',
      logo: 'https://s.yimg.com/kw/assets/favicon-160x160.png',
      publisher: 'Engadget',
      title: 'All GitHub users can keep their code private | Engadget',
      url: 'https://www.engadget.com/2019-01-07-all-github-users-keep-code-private.html',
      video: null,
    }
