# Snapshot report for `test/integration/zdnet/index.js`

The actual snapshot is saved in `index.js.snap`.

Generated by [AVA](https://avajs.dev).

## zdnet

> Snapshot 1

    {
      audio: null,
      author: '<PERSON>',
      date: '2016-05-24T13:30:03.000Z',
      description: 'Founded in 2009, <PERSON><PERSON><PERSON> perviously partnered with PayPal, Yahoo and Google to create the DMARC authentication protocol.',
      image: 'http://zdnet4.cbsistatic.com/hub/i/r/2016/05/09/292bfdbf-c37f-4cd2-a5b6-2d9ee9920aef/thumbnail/770x578/053a9fc8f93b4bafd36dcc90c3debd12/istock000074135653medium.jpg',
      lang: 'en',
      logo: 'https://www.zdnet.com/favicon.ico',
      publisher: 'ZDNet',
      title: 'Email security startup <PERSON><PERSON><PERSON> raises $22 million to help enterprises fight phishing attacks | ZDNet',
      url: 'http://www.zdnet.com/article/email-security-startup-agari-raises-22-million-to-help-enterprises-fight-phishing-attacks/',
      video: null,
    }
