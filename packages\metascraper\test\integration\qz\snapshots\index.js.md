# Snapshot report for `test/integration/qz/index.js`

The actual snapshot is saved in `index.js.snap`.

Generated by [AVA](https://avajs.dev).

## qz

> Snapshot 1

    {
      audio: null,
      author: null,
      date: null,
      description: 'As tech giants in the US transition from disruptor to incumbent, a new generation of companies around the world are challenging their dominance–and changing the startup playbook along the way. From China’s internet giants to startups in Nigeria to policy debates in the EU, the next big battles in tech are happening outside the Bay Area.',
      image: 'https://cms.qz.com/wp-content/uploads/2020/12/beyond-s.jpg?quality=75&strip=all&w=1200&h=630&crop=1',
      lang: 'en',
      logo: 'https://cms.qz.com/wp-content/uploads/2020/04/qz-icon.jpg?quality=75&strip=all&w=180&h=180&crop=1',
      publisher: 'Quartz',
      title: 'Beyond Silicon Valley',
      url: 'https://qz.com/on/beyond-silicon-valley/',
      video: null,
    }
