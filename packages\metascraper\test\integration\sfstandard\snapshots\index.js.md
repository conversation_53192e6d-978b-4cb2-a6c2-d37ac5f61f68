# Snapshot report for `test/integration/sfstandard/index.js`

The actual snapshot is saved in `index.js.snap`.

Generated by [AVA](https://avajs.dev).

## sfstandard

> Snapshot 1

    {
      audio: null,
      author: 'lsariasfstandard.com',
      date: '2024-06-24T18:59:54.000Z',
      description: 'Diners hate them. Owners say they need them. But in the raging battle over restaurant service fees, it’s servers and cooks who are on the front lines.',
      image: 'https://content.sfstandard.com/wp-content/uploads/2024/06/featured_20240621-junkfeeban.jpg?resize=1200%2C630',
      lang: 'en',
      logo: 'https://content.sfstandard.com/wp-content/uploads/2024/02/coloryellow-cropdefault-transparentfalse2x.png',
      publisher: 'The San Francisco Standard',
      title: 'Restaurant workers fear they could pay highest cost in junk-fee battle',
      url: 'https://sfstandard.com/2024/06/24/service-fee-restaurants-san-francisco/',
      video: null,
    }
