# Snapshot report for `test/integration/fortune/index.js`

The actual snapshot is saved in `index.js.snap`.

Generated by [AVA](https://avajs.dev).

## fortune

> Snapshot 1

    {
      audio: null,
      author: '<PERSON><PERSON>',
      date: '2021-04-24T09:18:20.000Z',
      description: '<PERSON><PERSON><PERSON><PERSON><PERSON>, a four-year-old startup, is changing the way companies find and evaluate job applicants.',
      image: 'https://content.fortune.com/wp-content/uploads/2015/09/gettyimages-1852881881.jpg?resize=1200,600',
      lang: 'en',
      logo: 'https://fortune.com/icons/favicons/favicon.ico',
      publisher: 'Fortune',
      title: 'Why your next job search may involve solving online puzzles',
      url: 'https://fortune.com/2015/10/05/hackerrank-recruiting-tool/',
      video: null,
    }
