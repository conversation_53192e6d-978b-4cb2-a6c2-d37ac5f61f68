# Snapshot report for `test/integration/learnnode/index.js`

The actual snapshot is saved in `index.js.snap`.

Generated by [AVA](https://avajs.dev).

## learnnode

> Snapshot 1

    {
      audio: null,
      author: 'HackerYou',
      date: null,
      description: 'A premium training course to learn to build apps with Node.js, Express, MongoDB, and friends.',
      image: 'https://learnnode.com/images/NODE/poster.jpg',
      lang: null,
      logo: 'https://learnnode.com/images/NODE/favicon.png',
      publisher: 'Learn Node',
      title: 'Learn Node',
      url: 'https://learnnode.com/',
      video: 'https://player.vimeo.com/external/216213305.sd.mp4?s=815e208b400abe120e9b860dad68762bcf4b828a&profile_id=164',
    }
