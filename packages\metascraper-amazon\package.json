{"name": "metascraper-amazon", "description": "Metascraper integration with Amazon", "homepage": "https://github.com/microlinkhq/metascraper/packages/metascraper-amazon", "version": "5.49.1", "types": "src/index.d.ts", "main": "src/index.js", "author": {"email": "<EMAIL>", "name": "microlink.io", "url": "https://microlink.io"}, "repository": {"directory": "packages/metascraper-amazon", "type": "git", "url": "git+https://github.com/microlinkhq/metascraper.git"}, "bugs": {"url": "https://github.com/microlinkhq/metascraper/issues"}, "keywords": ["amazon", "metascraper"], "dependencies": {"@metascraper/helpers": "workspace:*"}, "devDependencies": {"ava": "5"}, "engines": {"node": ">= 16"}, "files": ["src"], "scripts": {"test": "NODE_PATH=.. TZ=UTC ava --timeout 15s"}, "license": "MIT"}