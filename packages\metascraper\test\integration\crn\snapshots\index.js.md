# Snapshot report for `test/integration/crn/index.js`

The actual snapshot is saved in `index.js.snap`.

Generated by [AVA](https://avajs.dev).

## crn

> Snapshot 1

    {
      audio: null,
      author: '<PERSON>',
      date: '2016-04-29T23:31:03.000Z',
      description: 'The new technology is meant to provide EMC storage customers with an easy cloud solution with simplified billing using Virtustream, according to sources.',
      image: 'http://i.crn.com/images/CRN_fb.jpg',
      lang: 'en',
      logo: 'https://www.crn.com/favicon.ico',
      publisher: 'CRN',
      title: 'EMC World: Sources Say EMC Will Target AWS S3 With New Easy Storage Connectivity To Virtustream',
      url: 'http://www.crn.com/news/cloud/300080531/emc-world-sources-say-emc-will-target-aws-s3-with-new-easy-storage-connectivity-to-virtustream.htm',
      video: null,
    }
