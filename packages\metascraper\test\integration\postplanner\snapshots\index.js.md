# Snapshot report for `test/integration/postplanner/index.js`

The actual snapshot is saved in `index.js.snap`.

Generated by [AVA](https://avajs.dev).

## postplanner

> Snapshot 1

    {
      audio: null,
      author: '<PERSON>',
      date: '2020-12-30T02:13:22.000Z',
      description: 'Want to get more Likes on Facebook in 2021? No problem. To increase Likes on your FB page, you need to boost Likes on your FB posts. Here’s how to do that.',
      image: 'https://www.postplanner.com/hubfs/blog/11_Ways_to_Get_Lots_of_Likes_on_Facebook_(the_right_way)/11%20Ways%20to%20Get%20Lots%20of%20Likes%20on%20Facebook%20(the%20right%20way!!)%20hero.png#keepProtocol',
      lang: null,
      logo: 'https://cdn2.hubspot.net/hubfs/513577/v2/global/favicon.ico',
      publisher: 'Post Planner',
      title: 'Here’s How to Get More Likes on Facebook in 2021 (and Reach Millions)',
      url: 'https://www.postplanner.com/get-more-likes-fans-facebook-page/',
      video: null,
    }
